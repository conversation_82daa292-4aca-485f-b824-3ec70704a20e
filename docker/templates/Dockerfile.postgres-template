# PostgreSQL Services Template
# Optimized for: auth, user, team, calendar, notification services
# Uses postgres-base with pre-cached GORM dependencies

FROM swork/postgres-base:latest AS builder

# Copy source code (PostgreSQL dependencies already cached)
COPY . .

# Build the service with BuildKit cache mounts (memory optimized)
ARG SERVICE_PATH
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux go build \
    -ldflags="-w -s" \
    -p 1 \
    -o main ${SERVICE_PATH}

# Final stage - Uses optimized runtime base
FROM swork/runtime-base:latest

# Copy the binary from builder stage
COPY --from=builder /app/main .
RUN chown appuser:appuser main && chmod +x main

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the binary
CMD ["./main"]