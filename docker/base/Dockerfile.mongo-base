# MongoDB Services Base Image  
# Optimized for: social services
# Contains MongoDB driver + common web dependencies

FROM swork/go-base:latest AS mongo-base

# Pre-download MongoDB service dependencies
# These services handle social interactions
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go mod download \
    go.mongodb.org/mongo-driver \
    github.com/gin-gonic/gin \
    github.com/golang-jwt/jwt/v5 \
    github.com/redis/go-redis/v9 \
    github.com/joho/godotenv \
    github.com/sirupsen/logrus \
    github.com/google/uuid

# This base is optimized for MongoDB-based microservices
# Build time reduction: ~65% for social services with pre-cached MongoDB drivers