# Storage Services Base Image
# Optimized for: drive service (and social service with file uploads)
# Contains storage, imaging, and file processing dependencies

FROM swork/go-base:latest AS storage-base

# Install additional build dependencies for file processing
RUN apk add --no-cache \
    imagemagick-dev \
    ffmpeg-dev

# Pre-download storage and file processing dependencies
# These are heavy packages that take significant time to compile
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go mod download \
    github.com/minio/minio-go/v7 \
    github.com/Backblaze/blazer \
    github.com/disintegration/imaging \
    gorm.io/gorm \
    gorm.io/driver/postgres \
    github.com/gin-gonic/gin \
    github.com/golang-jwt/jwt/v5 \
    github.com/redis/go-redis/v9 \
    github.com/joho/godotenv \
    github.com/sirupsen/logrus \
    github.com/google/uuid

# This base is optimized for storage-intensive microservices
# Build time reduction: ~80% for drive service with pre-cached storage and imaging dependencies