# Shared Go Base Image - Foundation for all microservices
# This image contains Go toolchain + downloaded dependencies
# Significantly reduces build time by caching dependencies

FROM golang:1.23-alpine AS go-base

# Set Go environment for optimal caching
ENV GOCACHE=/root/.cache/go-build
ENV GOMODCACHE=/go/pkg/mod

# Install build dependencies once for all services
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    gcc \
    musl-dev \
    pkgconfig

WORKDIR /app

# Copy go.mod and go.sum for dependency caching
COPY go.mod go.sum ./

# Download and cache all dependencies with BuildKit cache mount
# This layer will be cached and reused across all services
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go mod download

# Verify dependencies are properly cached
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod verify

# This becomes the base for all Go service builds
# Services will inherit this layer with all dependencies pre-cached