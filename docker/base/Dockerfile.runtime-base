# Optimized Runtime Base Image
# Pre-installed runtime dependencies for all Go services
# Eliminates redundant package installations across services

FROM alpine:3.19 AS runtime-base

# Install common runtime dependencies once
# These packages are needed by most/all services
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    curl \
    wget \
    && rm -rf /var/cache/apk/*

# Create non-root user for security (reused by all services)
RUN adduser -D -s /bin/sh appuser

# Set working directory
WORKDIR /app

# Create common directories with proper permissions
RUN mkdir -p /app/logs /app/tmp \
    && chown -R appuser:appuser /app

# This base will be extended by service-specific runtime stages
# Services only need to COPY their binary and set CMD