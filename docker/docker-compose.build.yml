# Optimized Build Configuration with Cache Strategies
# Use this for development builds with maximum performance
# Usage: docker-compose -f docker-compose.yml -f docker/docker-compose.build.yml build

services:
  # Build base images first for maximum cache efficiency
  go-base:
    build:
      context: ..
      dockerfile: docker/base/Dockerfile.go-base
      cache_from:
        - swork/go-base:latest
      cache_to:
        - swork/go-base:latest
    image: swork/go-base:latest

  postgres-base:
    build:
      context: ..
      dockerfile: docker/base/Dockerfile.postgres-base
      cache_from:
        - swork/postgres-base:latest
        - swork/go-base:latest
      cache_to:
        - swork/postgres-base:latest
    image: swork/postgres-base:latest
    depends_on:
      - go-base

  mongo-base:
    build:
      context: ..
      dockerfile: docker/base/Dockerfile.mongo-base
      cache_from:
        - swork/mongo-base:latest
        - swork/go-base:latest
      cache_to:
        - swork/mongo-base:latest
    image: swork/mongo-base:latest
    depends_on:
      - go-base

  storage-base:
    build:
      context: ..
      dockerfile: docker/base/Dockerfile.storage-base
      cache_from:
        - swork/storage-base:latest
        - swork/go-base:latest
      cache_to:
        - swork/storage-base:latest
    image: swork/storage-base:latest
    depends_on:
      - go-base

  runtime-base:
    build:
      context: ..
      dockerfile: docker/base/Dockerfile.runtime-base
      cache_from:
        - swork/runtime-base:latest
      cache_to:
        - swork/runtime-base:latest
    image: swork/runtime-base:latest

  # PostgreSQL services use postgres-base for faster builds
  auth-service:
    build:
      context: ..
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/auth/cmd/main.go
      cache_from:
        - swork/auth-service:latest
        - swork/postgres-base:latest
        - swork/runtime-base:latest
    depends_on:
      - postgres-base
      - runtime-base

  user-service:
    build:
      context: ..
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/user/cmd/main.go
      cache_from:
        - swork/user-service:latest
        - swork/postgres-base:latest
        - swork/runtime-base:latest
    depends_on:
      - postgres-base
      - runtime-base

  team-service:
    build:
      context: ..
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/team/cmd/main.go
      cache_from:
        - swork/team-service:latest
        - swork/postgres-base:latest
        - swork/runtime-base:latest
    depends_on:
      - postgres-base
      - runtime-base

  calendar-service:
    build:
      context: ..
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/calendar/cmd/main.go
      cache_from:
        - swork/calendar-service:latest
        - swork/postgres-base:latest
        - swork/runtime-base:latest
    depends_on:
      - postgres-base
      - runtime-base

  notification-service:
    build:
      context: ..
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/notification/cmd/main.go
      cache_from:
        - swork/notification-service:latest
        - swork/postgres-base:latest
        - swork/runtime-base:latest
    depends_on:
      - postgres-base
      - runtime-base

  # MongoDB services use mongo-base for faster builds
  social-service:
    build:
      context: ..
      dockerfile: docker/templates/Dockerfile.mongo-template
      args:
        SERVICE_PATH: ./services/social/cmd/main.go
      cache_from:
        - swork/social-service:latest
        - swork/mongo-base:latest
        - swork/runtime-base:latest
    depends_on:
      - mongo-base
      - runtime-base


  # Drive service uses optimized storage-base
  drive-service:
    build:
      context: ..
      dockerfile: ./services/drive/Dockerfile
      cache_from:
        - swork/drive-service:latest
        - swork/storage-base:latest
    depends_on:
      - storage-base

  # API Gateway uses the standard template
  api-gateway:
    build:
      context: ..
      dockerfile: docker/templates/Dockerfile.template
      args:
        SERVICE_PATH: ./cmd/gateway/main.go
      cache_from:
        - swork/api-gateway:latest
        - swork/go-base:latest
        - swork/runtime-base:latest
    depends_on:
      - go-base
      - runtime-base