services:
  # Environment validation service - ensures secure configuration
  env-validator:
    image: alpine:3.19
    container_name: swork-env-validator
    volumes:
      - ./scripts:/scripts:ro
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - MONGO_PASSWORD=${MONGO_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - SERVICE_TOKEN=${SERVICE_TOKEN}
      - MINIO_ROOT_USER=${MINIO_ROOT_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
      - APP_ENV=${APP_ENV:-development}
      - GIN_MODE=${GIN_MODE:-debug}
    command: |
      sh -c "
        apk add --no-cache bash && 
        cp /scripts/validate-env.sh /tmp/validate-env.sh && 
        chmod +x /tmp/validate-env.sh && 
        /tmp/validate-env.sh && 
        echo 'Environment validation passed - safe to start services'
      "
    profiles:
      - validation

  # Database Services
  postgres:
    image: postgres:14-alpine
    container_name: swork-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-swork}
      POSTGRES_USER: ${POSTGRES_USER:-swork}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_MULTIPLE_DATABASES: swork_auth,swork_user,swork_team,swork_drive,swork_calendar,swork_notification,swork_messaging,swork_search
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sh:/docker-entrypoint-initdb.d/init-postgres.sh
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U swork"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  mongodb:
    image: mongo:6-jammy
    container_name: swork-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME:-swork}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-swork}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  redis:
    image: redis:7-alpine
    container_name: swork-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --save 60 1000
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  minio:
    image: minio/minio:latest
    container_name: swork-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.75'
        reservations:
          memory: 256M
          cpus: '0.25'

  qdrant:
    image: qdrant/qdrant:latest
    container_name: swork-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'


  # Core Services
  auth-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/auth/cmd/main.go
    container_name: swork-auth
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${POSTGRES_USER:-swork}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - AUTH_DB_DATABASE=swork_auth
      - DB_SSL_MODE=disable
      - REDIS_URL=redis://redis:6379
      - USER_SERVICE_URL=http://user-service:8080
      - JWT_SECRET=${JWT_SECRET}
      - JWT_ACCESS_DURATION=15m
      - JWT_REFRESH_DURATION=7d
      - EMAIL_ENABLED=false
      - BCRYPT_COST=12
      - SERVICE_TOKEN=${SERVICE_TOKEN}
    ports:
      - "8001:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  user-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/user/cmd/main.go
    container_name: swork-user
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${POSTGRES_USER:-swork}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - USER_DB_DATABASE=swork_user
      - DB_SSL_MODE=disable
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8080
      - JWT_SECRET=${JWT_SECRET}
      - SERVICE_TOKEN=${SERVICE_TOKEN}
      # Performance optimization settings
      - USER_DB_MAX_OPEN_CONNS=${USER_DB_MAX_OPEN_CONNS:-25}
      - USER_DB_MAX_IDLE_CONNS=${USER_DB_MAX_IDLE_CONNS:-5}
      - USER_DB_CONN_MAX_LIFETIME=${USER_DB_CONN_MAX_LIFETIME:-300s}
      - USER_DB_CONN_MAX_IDLE_TIME=${USER_DB_CONN_MAX_IDLE_TIME:-60s}
      - USER_DB_SLOW_QUERY_THRESHOLD=${USER_DB_SLOW_QUERY_THRESHOLD:-500ms}
      - USER_DB_ENABLE_QUERY_LOGGING=${USER_DB_ENABLE_QUERY_LOGGING:-false}
      - USER_DB_READ_REPLICA_URL=${USER_DB_READ_REPLICA_URL:-}
      - USER_DB_USE_READ_REPLICA=${USER_DB_USE_READ_REPLICA:-false}
      - USER_SEARCH_CACHE_TTL=${USER_SEARCH_CACHE_TTL:-5m}
      - USER_RELATIONSHIP_CACHE_TTL=${USER_RELATIONSHIP_CACHE_TTL:-10m}
      - USER_SUGGESTIONS_CACHE_TTL=${USER_SUGGESTIONS_CACHE_TTL:-30m}
      - USER_PROFILE_CACHE_TTL=${USER_PROFILE_CACHE_TTL:-15m}
      - USER_ENABLE_SEARCH_CACHE=${USER_ENABLE_SEARCH_CACHE:-true}
      - USER_ENABLE_RELATIONSHIP_CACHE=${USER_ENABLE_RELATIONSHIP_CACHE:-true}
      - USER_ENABLE_SUGGESTIONS_CACHE=${USER_ENABLE_SUGGESTIONS_CACHE:-true}
      - USER_WARM_CACHE_ON_STARTUP=${USER_WARM_CACHE_ON_STARTUP:-false}
    ports:
      - "8002:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      auth-service:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  social-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.mongo-template
      args:
        SERVICE_PATH: ./services/social/cmd/main.go
    container_name: swork-social
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - MONGODB_URI=mongodb://${MONGO_USERNAME:-swork}:${MONGO_PASSWORD}@mongodb:27017/swork_social?authSource=admin
      - MONGO_DATABASE=swork_social
      - REDIS_URL=redis://redis:6379
      - REDIS_DB=3
      - AUTH_SERVICE_URL=http://auth-service:8080
      - USER_SERVICE_URL=http://user-service:8080
      - TEAM_SERVICE_URL=http://team-service:8080
      - NOTIFICATION_SERVICE_URL=http://notification-service:8080
      - JWT_SECRET=${JWT_SECRET}
      - SERVICE_TOKEN=${SERVICE_TOKEN}
      - MINIO_ENDPOINT=minio:9000
      - MINIO_EXTERNAL_ENDPOINT=localhost:9000
      - MINIO_PUBLIC_URL=http://localhost:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_USE_SSL=false
      - MINIO_BUCKET=social-attachments
    ports:
      - "8003:8080"
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      user-service:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.75'
        reservations:
          memory: 256M
          cpus: '0.5'

  team-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/team/cmd/main.go
    container_name: swork-team
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${POSTGRES_USER:-swork}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - TEAM_DB_DATABASE=swork_team
      - DB_SSL_MODE=disable
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8080
      - USER_SERVICE_URL=http://user-service:8080
      - JWT_SECRET=${JWT_SECRET}
      - SERVICE_TOKEN=${SERVICE_TOKEN}
    ports:
      - "8004:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      user-service:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'


  drive-service:
    build:
      context: .
      dockerfile: ./services/drive/Dockerfile
    container_name: swork-drive
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${POSTGRES_USER:-swork}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DRIVE_DB_DATABASE=swork_drive
      - DB_SSL_MODE=disable
      - REDIS_URL=redis://redis:6379
      - STORAGE_PRIMARY_TYPE=${STORAGE_PRIMARY_TYPE}
      - STORAGE_PRIMARY_BUCKET=${STORAGE_PRIMARY_BUCKET}
      - B2_APPLICATION_KEY_ID=${B2_APPLICATION_KEY_ID}
      - B2_APPLICATION_KEY=${B2_APPLICATION_KEY}
      - B2_BUCKET_ID=${B2_BUCKET_ID}
      - STORAGE_REPLICA_TYPE=${STORAGE_REPLICA_TYPE:-minio}
      - STORAGE_REPLICA_BUCKET=${STORAGE_REPLICA_BUCKET:-swork-drive-replica}
      - STORAGE_REPLICA_REGION=${STORAGE_REPLICA_REGION:-us-east-1}
      - STORAGE_REPLICA_ACCESS_KEY=${B2_APPLICATION_KEY_ID}
      - STORAGE_REPLICA_SECRET_KEY=${B2_APPLICATION_KEY}
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ROOT_USER}
      - MINIO_SECRET_KEY=${MINIO_ROOT_PASSWORD}
      - MINIO_USE_SSL=false
      - AUTH_SERVICE_URL=http://auth-service:8080
      - USER_SERVICE_URL=http://user-service:8080
      - TEAM_SERVICE_URL=http://team-service:8080
      - JWT_SECRET=${JWT_SECRET}
      - THUMBNAIL_STORAGE_PATH=/tmp/thumbnails
    ports:
      - "8006:8080"
    volumes:
      - drive_data:/data/uploads
      - thumbnails_data:/app/thumbnails
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      team-service:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.75'

  calendar-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/calendar/cmd/main.go
    container_name: swork-calendar
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${POSTGRES_USER:-swork}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - CALENDAR_DB_DATABASE=swork_calendar
      - DB_SSL_MODE=disable
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8080
      - USER_SERVICE_URL=http://user-service:8080
      - TEAM_SERVICE_URL=http://team-service:8080
    ports:
      - "8007:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      team-service:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  notification-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/notification/cmd/main.go
    container_name: swork-notification
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${POSTGRES_USER:-swork}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - NOTIFICATION_DB_DATABASE=swork_notification
      - DB_SSL_MODE=disable
      - REDIS_URL=redis://redis:6379
      - EMAIL_ENABLED=false
      - PUSH_ENABLED=false
      - SMS_ENABLED=false
      - WEBHOOK_ENABLED=false
      - AUTH_SERVICE_URL=http://auth-service:8080
      - USER_SERVICE_URL=http://user-service:8080
    ports:
      - "8008:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      user-service:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  messaging-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.messaging-template
    container_name: swork-messaging
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      # PostgreSQL for conversations (using centralized config)
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${POSTGRES_USER:-swork}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - MESSAGING_DB_DATABASE=swork_messaging
      - DB_SSL_MODE=disable
      # MongoDB for messages (using centralized config)
      - MONGODB_URI=mongodb://${MONGO_USERNAME:-swork}:${MONGO_PASSWORD}@mongodb:27017/swork_messaging?authSource=admin
      - MESSAGING_MONGO_DATABASE=${MESSAGING_MONGO_DATABASE:-swork_messaging}
      # Redis for cache and real-time (using centralized config)
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=
      - MESSAGING_REDIS_DB=${MESSAGING_REDIS_DB:-2}
      # Security (using centralized config)
      - JWT_SECRET=${JWT_SECRET}
      - SERVICE_TOKEN=${SERVICE_TOKEN}
      # External service URLs (using centralized config)
      - AUTH_SERVICE_URL=http://auth-service:8080
      - USER_SERVICE_URL=http://user-service:8080
      - TEAM_SERVICE_URL=http://team-service:8080
      - NOTIFICATION_SERVICE_URL=http://notification-service:8080
      - DRIVE_SERVICE_URL=http://drive-service:8080
      # WebSocket Configuration
      - WS_MAX_CONNECTIONS=${WS_MAX_CONNECTIONS:-100000}
      - WS_READ_TIMEOUT=${WS_READ_TIMEOUT:-60s}
      - WS_WRITE_TIMEOUT=${WS_WRITE_TIMEOUT:-10s}
      - WS_PING_PERIOD=${WS_PING_PERIOD:-25s}
      - WS_PONG_TIMEOUT=${WS_PONG_TIMEOUT:-30s}
      - WS_MAX_MESSAGE_SIZE=${WS_MAX_MESSAGE_SIZE:-8192}
      - WS_ENABLE_COMPRESSION=${WS_ENABLE_COMPRESSION:-true}
      - WS_BUFFER_SIZE=${WS_BUFFER_SIZE:-1024}
      - WS_CHANNEL_BUFFER_SIZE=${WS_CHANNEL_BUFFER_SIZE:-256}
      # Messaging Configuration
      - MESSAGES_PER_DOCUMENT=${MESSAGES_PER_DOCUMENT:-100}
      - SEQUENCE_BATCH_SIZE=${SEQUENCE_BATCH_SIZE:-50}
      - GROUP_SEQUENCE_BATCH=${GROUP_SEQUENCE_BATCH:-100}
      - MESSAGE_CACHE_TTL=${MESSAGE_CACHE_TTL:-24h}
      - SEQUENCE_CACHE_TTL=${SEQUENCE_CACHE_TTL:-8760h}
      - CONVERSATION_CACHE_TTL=${CONVERSATION_CACHE_TTL:-12h}
      - READ_STATUS_TTL=${READ_STATUS_TTL:-168h}
      - MESSAGE_MAX_WORKERS=${MESSAGE_MAX_WORKERS:-50}
      - MESSAGE_BATCH_SIZE=${MESSAGE_BATCH_SIZE:-500}
      - MESSAGE_BATCH_INTERVAL=${MESSAGE_BATCH_INTERVAL:-100ms}
      # Queue Configuration
      - QUEUE_WORKER_COUNT=${QUEUE_WORKER_COUNT:-10}
      - QUEUE_MAX_WORKER_COUNT=${QUEUE_MAX_WORKER_COUNT:-50}
      - QUEUE_MIN_WORKER_COUNT=${QUEUE_MIN_WORKER_COUNT:-2}
      - QUEUE_PRIORITY_QUEUES=${QUEUE_PRIORITY_QUEUES:-3}
      - QUEUE_MAX_SIZE=${QUEUE_MAX_SIZE:-1000}
      - QUEUE_PROCESSING_TIMEOUT=${QUEUE_PROCESSING_TIMEOUT:-300}
      - QUEUE_RETRY_ATTEMPTS=${QUEUE_RETRY_ATTEMPTS:-3}
      - QUEUE_BACKOFF_MULTIPLIER=${QUEUE_BACKOFF_MULTIPLIER:-2.0}
      - QUEUE_MAX_BACKOFF_DURATION=${QUEUE_MAX_BACKOFF_DURATION:-5m}
      - QUEUE_BATCH_PROCESSING=${QUEUE_BATCH_PROCESSING:-true}
      - QUEUE_BATCH_SIZE=${QUEUE_BATCH_SIZE:-10}
      - QUEUE_BATCH_TIMEOUT=${QUEUE_BATCH_TIMEOUT:-100ms}
      - QUEUE_ENABLE_METRICS=${QUEUE_ENABLE_METRICS:-true}
      - QUEUE_METRICS_INTERVAL=${QUEUE_METRICS_INTERVAL:-30s}
      - QUEUE_HEALTH_CHECK_INTERVAL=${QUEUE_HEALTH_CHECK_INTERVAL:-1m}
      # Connection Management
      - CONNECTION_TTL=${CONNECTION_TTL:-30m}
      - CONNECTION_PRESENCE_TTL=${CONNECTION_PRESENCE_TTL:-5m}
      - CONNECTION_METADATA_TTL=${CONNECTION_METADATA_TTL:-1h}
      - CONNECTION_CLEANUP_INTERVAL=${CONNECTION_CLEANUP_INTERVAL:-5m}
      - CONNECTION_STALE_TIMEOUT=${CONNECTION_STALE_TIMEOUT:-10m}
      - CONNECTION_BATCH_SIZE=${CONNECTION_BATCH_SIZE:-100}
      - CONNECTION_MAX_CONCURRENT_OPS=${CONNECTION_MAX_CONCURRENT_OPS:-10}
      - CONNECTION_HEALTH_CHECK_INTERVAL=${CONNECTION_HEALTH_CHECK_INTERVAL:-1m}
      - CONNECTION_HEARTBEAT_INTERVAL=${CONNECTION_HEARTBEAT_INTERVAL:-30s}
      # Event Processing
      - EVENTS_REALTIME_ENABLED=${EVENTS_REALTIME_ENABLED:-true}
      - EVENTS_REALTIME_TIMEOUT=${EVENTS_REALTIME_TIMEOUT:-5s}
      - EVENTS_ASYNC_ENABLED=${EVENTS_ASYNC_ENABLED:-true}
      - EVENTS_ASYNC_RETRIES=${EVENTS_ASYNC_RETRIES:-3}
      - EVENTS_NOTIFICATIONS_ENABLED=${EVENTS_NOTIFICATIONS_ENABLED:-true}
      - EVENTS_OFFLINE_NOTIFICATION_DELAY=${EVENTS_OFFLINE_NOTIFICATION_DELAY:-30s}
      - EVENTS_FILTER_SYSTEM_MESSAGES=${EVENTS_FILTER_SYSTEM_MESSAGES:-false}
      - EVENTS_FILTER_DELETED_MESSAGES=${EVENTS_FILTER_DELETED_MESSAGES:-true}
      - EVENTS_BUFFER_SIZE=${EVENTS_BUFFER_SIZE:-1000}
      - EVENTS_BATCH_SIZE=${EVENTS_BATCH_SIZE:-10}
      - EVENTS_FLUSH_INTERVAL=${EVENTS_FLUSH_INTERVAL:-100ms}
    ports:
      - "8005:8080"
    volumes:
      - messaging_uploads:/tmp/messaging-uploads
      - ./services/messaging/migrations:/app/migrations:ro
    depends_on:
      postgres:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
      team-service:
        condition: service_started
      notification-service:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  search-service:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.postgres-template
      args:
        SERVICE_PATH: ./services/search/cmd/main.go
    container_name: swork-search
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SEARCH_HOST=0.0.0.0
      - SEARCH_PORT=8009
      - SEARCH_DATABASE_URL=postgres://${POSTGRES_USER:-swork}:${POSTGRES_PASSWORD}@postgres:5432/swork_search?sslmode=disable
      - SEARCH_DATABASE_MAX_OPEN_CONNS=25
      - SEARCH_DATABASE_MAX_IDLE_CONNS=5
      - SEARCH_DATABASE_CONN_MAX_LIFETIME=1h
      - SEARCH_ENGINE_PROVIDER=qdrant
      - SEARCH_ENGINE_HOST=qdrant
      - SEARCH_ENGINE_PORT=6333
      - SEARCH_ENGINE_API_KEY=
      - SEARCH_INDEX_NAME=swork_search
      - SEARCH_VECTOR_SIZE=1536
      - SEARCH_BATCH_SIZE=100
      - SEARCH_MAX_RESULTS=100
      - AI_PROVIDER=openai
      - AI_API_KEY=${AI_API_KEY}
      - AI_MODEL=text-embedding-3-small
      - AI_MAX_TOKENS=8192
      - AI_TEMPERATURE=0.0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET=${JWT_SECRET}
      - SERVICE_TOKEN=${SERVICE_TOKEN}
    ports:
      - "8009:8009"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: docker/templates/Dockerfile.template
      args:
        SERVICE_PATH: ./cmd/gateway/main.go
    container_name: swork-gateway
    environment:
      - APP_ENV=development
      - GO_ENV=development
      - GIN_MODE=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://swork-auth:8080
      - USER_SERVICE_URL=http://swork-user:8080
      - SOCIAL_SERVICE_URL=http://swork-social:8080
      - TEAM_SERVICE_URL=http://swork-team:8080
      - DRIVE_SERVICE_URL=http://swork-drive:8080
      - CALENDAR_SERVICE_URL=http://swork-calendar:8080
      - NOTIFICATION_SERVICE_URL=http://swork-notification:8080
      - MESSAGING_SERVICE_URL=http://swork-messaging:8080
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_REQUESTS=100
      - RATE_LIMIT_WINDOW=60s
      - JWT_SECRET=${JWT_SECRET}
    ports:
      - "8000:8080"
    depends_on:
      - auth-service
      - user-service
      - social-service
      - team-service
      - drive-service
      - calendar-service
      - notification-service
      - messaging-service
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.75'

volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  qdrant_data:
    driver: local
  drive_data:
    driver: local
  thumbnails_data:
    driver: local
  messaging_uploads:
    driver: local

networks:
  default:
    name: swork-network