package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"

	"github.com/swork-team/platform/pkg/async"
	"github.com/swork-team/platform/pkg/cache"
	"github.com/swork-team/platform/pkg/database"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/webhook"
	redisutil "github.com/swork-team/platform/pkg/redis"
)

// ServiceConfig holds configuration for service initialization
type ServiceConfig interface {
	GetDatabaseConfig() DatabaseConfig
	GetRedisConfig() RedisConfig
	GetServerConfig() ServerConfig
}

// DatabaseConfig represents database configuration
type DatabaseConfig interface {
	GetHost() string
	GetPort() string
	GetUsername() string
	GetPassword() string
	GetDatabase() string
	GetSSLMode() string
	GetURI() string // For MongoDB
}

// RedisConfig represents Redis configuration
type RedisConfig interface {
	GetURL() string
}

// ServerConfig represents server configuration
type ServerConfig interface {
	GetHost() string
	GetPort() string
	GetReadTimeout() time.Duration
	GetWriteTimeout() time.Duration
}

// ServiceComponents holds all initialized service components
type ServiceComponents struct {
	Database       *gorm.DB             // For PostgreSQL services
	MongoDB        *mongo.Database      // For MongoDB services  
	MongoClient    *mongo.Client        // MongoDB client for closing connections
	RedisClient    *redis.Client
	CacheManager   *cache.DirectCacheManager
	AsyncJobQueue  *async.AsyncJobQueue
	WebhookService *webhook.WebhookClient
}

// RouterSetupFunction defines the function signature for setting up service-specific routes
type RouterSetupFunction func(components *ServiceComponents) *gin.Engine

// BackgroundTaskFunc defines the function signature for background tasks
type BackgroundTaskFunc func(components *ServiceComponents)

// ConnectionPoolConfig holds database connection pool configuration
type ConnectionPoolConfig struct {
	MaxIdleConns    int
	MaxOpenConns    int
	ConnMaxLifetime time.Duration
}

// DatabaseType represents the type of database
type DatabaseType int

const (
	PostgreSQL DatabaseType = iota
	MongoDB
)

// ServiceOptions holds service initialization options
type ServiceOptions struct {
	ServiceName        string
	Config             ServiceConfig
	Models             []interface{}
	RouterSetup        RouterSetupFunction
	EnableExtensions   bool
	ServiceCleanupFunc func() error
	DatabaseType       DatabaseType // Specify which database to use
	ConnectionPooling  *ConnectionPoolConfig // Optional connection pooling config
	BackgroundTasks    []BackgroundTaskFunc // Optional background tasks to start
}

// BootstrapService initializes and runs a service with common patterns
func BootstrapService(opts ServiceOptions) {
	log.Printf("Starting %s service...", opts.ServiceName)

	// Initialize database connection
	components, err := initializeComponents(opts)
	if err != nil {
		log.Fatalf("Failed to initialize service components: %v", err)
	}

	// Setup router
	router := opts.RouterSetup(components)

	// Start background tasks
	for _, task := range opts.BackgroundTasks {
		go task(components)
	}

	// Create HTTP server
	serverConfig := opts.Config.GetServerConfig()
	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%s", serverConfig.GetHost(), serverConfig.GetPort()),
		Handler:      router,
		ReadTimeout:  serverConfig.GetReadTimeout(),
		WriteTimeout: serverConfig.GetWriteTimeout(),
	}

	// Start server
	go func() {
		log.Printf("%s service starting on %s", opts.ServiceName, srv.Addr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for shutdown signal
	gracefulShutdown(srv, opts.ServiceCleanupFunc)
}

// initializeComponents sets up all common service components
func initializeComponents(opts ServiceOptions) (*ServiceComponents, error) {
	var db *gorm.DB
	var mongoDB *mongo.Database
	var mongoClient *mongo.Client
	
	dbConfig := opts.Config.GetDatabaseConfig()
	
	// Initialize database based on type
	switch opts.DatabaseType {
	case PostgreSQL:
		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
			dbConfig.GetHost(), dbConfig.GetPort(), dbConfig.GetUsername(), 
			dbConfig.GetPassword(), dbConfig.GetDatabase(), dbConfig.GetSSLMode())

		var err error
		db, err = database.NewPostgresConnection(dsn)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
		}

		// Enable PostgreSQL extensions if requested
		if opts.EnableExtensions {
			if err := enablePostgresExtensions(db); err != nil {
				log.Printf("Warning: Failed to enable PostgreSQL extensions: %v", err)
			}
		}

		// Configure connection pool if specified
		if opts.ConnectionPooling != nil {
			sqlDB, err := db.DB()
			if err != nil {
				return nil, fmt.Errorf("failed to get underlying database: %w", err)
			}
			
			sqlDB.SetMaxIdleConns(opts.ConnectionPooling.MaxIdleConns)
			sqlDB.SetMaxOpenConns(opts.ConnectionPooling.MaxOpenConns)
			sqlDB.SetConnMaxLifetime(opts.ConnectionPooling.ConnMaxLifetime)
		}

		// Auto-migrate models for GORM
		if len(opts.Models) > 0 {
			if err := db.AutoMigrate(opts.Models...); err != nil {
				return nil, fmt.Errorf("failed to migrate database: %w", err)
			}
		}
		
	case MongoDB:
		uri := dbConfig.GetURI()
		if uri == "" {
			return nil, fmt.Errorf("MongoDB URI is required but not provided")
		}
		
		var err error
		mongoClient, err = database.NewMongoConnection(uri)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
		}
		
		mongoDB = database.GetDatabase(mongoClient, dbConfig.GetDatabase())
		
	default:
		return nil, fmt.Errorf("unsupported database type: %v", opts.DatabaseType)
	}

	// Redis connection
	redisConfig := opts.Config.GetRedisConfig()
	redisClient, err := redisutil.NewRedisClient(redisConfig.GetURL())
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	// Initialize infrastructure components
	cacheManager := cache.NewDirectCacheManager(redisClient)
	asyncJobQueue := async.NewAsyncJobQueue(redisClient, opts.ServiceName, 5)
	webhookService := webhook.NewWebhookClient(&webhook.WebhookConfig{
		Enabled: false, // Disabled by default
	})

	return &ServiceComponents{
		Database:       db,
		MongoDB:        mongoDB,
		MongoClient:    mongoClient,
		RedisClient:    redisClient,
		CacheManager:   cacheManager,
		AsyncJobQueue:  asyncJobQueue,
		WebhookService: webhookService,
	}, nil
}

// enablePostgresExtensions enables common PostgreSQL extensions
func enablePostgresExtensions(db *gorm.DB) error {
	extensions := []string{
		"CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"",
		"CREATE EXTENSION IF NOT EXISTS \"pgcrypto\"",
	}

	for _, ext := range extensions {
		if err := db.Exec(ext).Error; err != nil {
			return fmt.Errorf("failed to create extension: %w", err)
		}
	}

	return nil
}

// gracefulShutdown handles graceful server shutdown
func gracefulShutdown(srv *http.Server, cleanupFunc func() error) {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// Run service-specific cleanup
	if cleanupFunc != nil {
		if err := cleanupFunc(); err != nil {
			log.Printf("Error during service cleanup: %v", err)
		}
	}

	log.Println("Server exited")
}

// SetupStandardRouter creates a standard Gin router with common middleware
func SetupStandardRouter() *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	return router
}


// AddStandardHealthCheck adds a standard health check endpoint to the router
func AddStandardHealthCheck(router *gin.Engine, serviceName string) {
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": fmt.Sprintf("%s is healthy", serviceName),
			"data": gin.H{
				"service": serviceName,
				"status":  "healthy",
			},
		})
	})
}