package server

import (
	"time"

	"github.com/swork-team/platform/pkg/config"
)

// ConfigAdapter wraps the existing config structures to implement our interfaces
type ConfigAdapter struct {
	Server   config.ServerConfig
	Database DatabaseConfig  // Use our interface instead of concrete type
	Redis    config.RedisConfig
}

// GetDatabaseConfig implements ServiceConfig
func (c *ConfigAdapter) GetDatabaseConfig() DatabaseConfig {
	return c.Database
}

// GetRedisConfig implements ServiceConfig
func (c *ConfigAdapter) GetRedisConfig() RedisConfig {
	return &RedisConfigAdapter{c.Redis}
}

// GetServerConfig implements ServiceConfig
func (c *ConfigAdapter) GetServerConfig() ServerConfig {
	return &ServerConfigAdapter{c.Server}
}

// DatabaseConfigAdapter adapts config.DatabaseConfig to our interface
type DatabaseConfigAdapter struct {
	config.DatabaseConfig
}

func (d *DatabaseConfigAdapter) GetHost() string     { return d.Host }
func (d *DatabaseConfigAdapter) GetPort() string     { return d.Port }
func (d *DatabaseConfigAdapter) GetUsername() string { return d.Username }
func (d *DatabaseConfigAdapter) GetPassword() string { return d.Password }
func (d *DatabaseConfigAdapter) GetDatabase() string { return d.Database }
func (d *DatabaseConfigAdapter) GetSSLMode() string  { return d.SSLMode }
func (d *DatabaseConfigAdapter) GetURI() string      { return "" } // Not applicable for PostgreSQL

// RedisConfigAdapter adapts config.RedisConfig to our interface
type RedisConfigAdapter struct {
	config.RedisConfig
}

func (r *RedisConfigAdapter) GetURL() string { return r.URL }

// ServerConfigAdapter adapts config.ServerConfig to our interface
type ServerConfigAdapter struct {
	config.ServerConfig
}

func (s *ServerConfigAdapter) GetHost() string           { return s.Host }
func (s *ServerConfigAdapter) GetPort() string           { return s.Port }
func (s *ServerConfigAdapter) GetReadTimeout() time.Duration  { return s.ReadTimeout }
func (s *ServerConfigAdapter) GetWriteTimeout() time.Duration { return s.WriteTimeout }

// MongoConfigAdapter adapts config.MongoConfig to our interface
type MongoConfigAdapter struct {
	config.MongoConfig
}

func (m *MongoConfigAdapter) GetHost() string     { return "" } // Not applicable for MongoDB
func (m *MongoConfigAdapter) GetPort() string     { return "" } // Not applicable for MongoDB  
func (m *MongoConfigAdapter) GetUsername() string { return "" } // Not applicable for MongoDB
func (m *MongoConfigAdapter) GetPassword() string { return "" } // Not applicable for MongoDB
func (m *MongoConfigAdapter) GetDatabase() string { return m.Database }
func (m *MongoConfigAdapter) GetSSLMode() string  { return "" } // Not applicable for MongoDB
func (m *MongoConfigAdapter) GetURI() string      { return m.URI }

// NewConfigAdapter creates a new config adapter from standard config components
func NewConfigAdapter(server config.ServerConfig, database config.DatabaseConfig, redis config.RedisConfig) *ConfigAdapter {
	return &ConfigAdapter{
		Server:   server,
		Database: &DatabaseConfigAdapter{database},
		Redis:    redis,
	}
}

// NewMongoConfigAdapter creates a new config adapter for MongoDB services
func NewMongoConfigAdapter(server config.ServerConfig, database config.MongoConfig, redis config.RedisConfig) *ConfigAdapter {
	return &ConfigAdapter{
		Server:   server,
		Database: &MongoConfigAdapter{database},
		Redis:    redis,
	}
}