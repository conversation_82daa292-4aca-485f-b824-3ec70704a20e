package server

import (
	"os"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
)

// ServiceBootstrapConfig holds configuration for service bootstrapping
type ServiceBootstrapConfig struct {
	ServiceName      string
	ConfigAdapter    ServiceConfig        // Pre-created config adapter
	ModelsProvider   func() []interface{} // Function to provide models for migration
	RouterSetup      RouterSetupFunction
	EnableExtensions bool
	DatabaseType     DatabaseType
	CustomStartupLog func(logger.ServiceLogger) // Optional custom startup logging
}

// ServiceBootstrapper provides a standardized way to bootstrap services
type ServiceBootstrapper struct {
	config ServiceBootstrapConfig
}

// NewServiceBootstrapper creates a new service bootstrapper
func NewServiceBootstrapper(config ServiceBootstrapConfig) *ServiceBootstrapper {
	return &ServiceBootstrapper{
		config: config,
	}
}

// Bootstrap starts the service with standardized initialization
func (sb *ServiceBootstrapper) Bootstrap() {
	// Initialize standardized logger
	loggerManager := logger.NewServiceLoggerManager(sb.config.ServiceName)
	serviceLogger := loggerManager.GetLogger()

	// Log service startup with custom or default logging
	if sb.config.CustomStartupLog != nil {
		sb.config.CustomStartupLog(serviceLogger)
	} else {
		sb.defaultStartupLog(serviceLogger)
	}

	// Get models for migration
	var models []interface{}
	if sb.config.ModelsProvider != nil {
		models = sb.config.ModelsProvider()
	}

	// Create cleanup function
	cleanupFunc := func() error {
		serviceLogger.Info(sb.config.ServiceName + " service shutting down")
		return nil
	}

	// Bootstrap the service
	BootstrapService(ServiceOptions{
		ServiceName:        sb.config.ServiceName,
		Config:             sb.config.ConfigAdapter,
		Models:             models,
		RouterSetup:        sb.config.RouterSetup,
		EnableExtensions:   sb.config.EnableExtensions,
		ServiceCleanupFunc: cleanupFunc,
		DatabaseType:       sb.config.DatabaseType,
	})
}

// defaultStartupLog provides default startup logging
func (sb *ServiceBootstrapper) defaultStartupLog(serviceLogger logger.ServiceLogger) {
	// Basic startup logging with available information
	fields := []logger.Field{
		logger.F("environment", os.Getenv("APP_ENV")),
		logger.F("database_type", sb.getDatabaseTypeString()),
	}

	serviceLogger.Info(sb.config.ServiceName+" service starting up", fields...)
}

// getDatabaseTypeString returns database type as string
func (sb *ServiceBootstrapper) getDatabaseTypeString() string {
	if sb.config.DatabaseType == MongoDB {
		return "mongodb"
	}
	return "postgresql"
}

// StandardRouterSetup provides a standardized router setup function
func StandardRouterSetup(serviceName string, setupFunc func(*gin.Engine, *ServiceComponents, logger.ServiceLogger)) RouterSetupFunction {
	return func(components *ServiceComponents) *gin.Engine {
		// Set Gin mode based on environment
		if os.Getenv("APP_ENV") == "production" {
			gin.SetMode(gin.ReleaseMode)
		}

		router := gin.New()

		// Get logger from context or create new one
		loggerManager := logger.NewServiceLoggerManager(serviceName)
		serviceLogger := loggerManager.GetLogger()

		// Add standardized middleware
		router.Use(logger.RequestLoggingMiddleware(serviceLogger))
		router.Use(logger.UserContextMiddleware())
		router.Use(logger.ErrorLoggingMiddleware())
		router.Use(gin.Recovery())

		// Call the service-specific setup function
		setupFunc(router, components, serviceLogger)

		return router
	}
}

// HealthCheckHandler provides a standardized health check handler
func HealthCheckHandler(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(200, gin.H{
			"service": serviceName,
			"status":  "healthy",
		})
	}
}
