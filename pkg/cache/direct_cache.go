package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
)

// DirectCacheManager provides high-performance cache operations without Redis KEYS commands
type DirectCacheManager struct {
	redis      *redis.Client
	tagManager *CacheTagManager
}

// CacheTagManager handles cache key tagging for efficient invalidation
type CacheTagManager struct {
	redis *redis.Client
}

// NewDirectCacheManager creates a new direct cache manager
func NewDirectCacheManager(redisClient *redis.Client) *DirectCacheManager {
	return &DirectCacheManager{
		redis:      redisClient,
		tagManager: &CacheTagManager{redis: redisClient},
	}
}

// SetWithTags stores a cache value with associated tags for efficient invalidation
func (ctm *CacheTagManager) SetWithTags(ctx context.Context, key string, value interface{}, ttl time.Duration, tags []string) error {
	pipe := ctm.redis.Pipeline()
	
	// Set the cache value
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal cache value: %w", err)
	}
	
	pipe.Set(ctx, key, data, ttl)
	
	// Add key to relevant tag sets (O(1) operations)
	for _, tag := range tags {
		tagKey := fmt.Sprintf("tag:%s", tag)
		pipe.SAdd(ctx, tagKey, key)
		// Tag expires after cache to ensure cleanup
		pipe.Expire(ctx, tagKey, ttl+time.Hour)
	}
	
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute cache pipeline: %w", err)
	}
	
	log.Printf("Cache SET: key=%s, tags=%v, ttl=%v", key, tags, ttl)
	return nil
}

// Get retrieves a cache value by key
func (dcm *DirectCacheManager) Get(ctx context.Context, key string, result interface{}) error {
	cached := dcm.redis.Get(ctx, key).Val()
	if cached == "" {
		return fmt.Errorf("cache miss for key: %s", key)
	}
	
	if err := json.Unmarshal([]byte(cached), result); err != nil {
		return fmt.Errorf("failed to unmarshal cached value: %w", err)
	}
	
	log.Printf("Cache HIT: key=%s", key)
	return nil
}

// Set stores a cache value with TTL
func (dcm *DirectCacheManager) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal cache value: %w", err)
	}
	
	err = dcm.redis.Set(ctx, key, data, ttl).Err()
	if err != nil {
		return fmt.Errorf("failed to set cache value: %w", err)
	}
	
	log.Printf("Cache SET: key=%s, ttl=%v", key, ttl)
	return nil
}

// SetWithTags stores a cache value with associated tags
func (dcm *DirectCacheManager) SetWithTags(ctx context.Context, key string, value interface{}, ttl time.Duration, tags []string) error {
	return dcm.tagManager.SetWithTags(ctx, key, value, ttl, tags)
}

// InvalidateByTag removes all cache entries associated with a tag
func (ctm *CacheTagManager) InvalidateByTag(ctx context.Context, tag string) error {
	tagKey := fmt.Sprintf("tag:%s", tag)
	
	// Get all keys for this tag (controlled O(N) - N is bounded by tag usage)
	keys, err := ctm.redis.SMembers(ctx, tagKey).Result()
	if err != nil {
		if err == redis.Nil {
			log.Printf("Cache tag not found: %s", tag)
			return nil
		}
		return fmt.Errorf("failed to get keys for tag %s: %w", tag, err)
	}
	
	if len(keys) == 0 {
		log.Printf("No cache keys found for tag: %s", tag)
		return nil
	}
	
	// Delete cache entries and tag in single pipeline
	pipe := ctm.redis.Pipeline()
	
	// Delete all keys associated with this tag
	if len(keys) > 0 {
		pipe.Del(ctx, keys...)
	}
	
	// Delete the tag set itself
	pipe.Del(ctx, tagKey)
	
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to invalidate tag %s: %w", tag, err)
	}
	
	log.Printf("Cache INVALIDATED: tag=%s, keys_deleted=%d", tag, len(keys))
	return nil
}

// InvalidateByTag invalidates all cache entries for a tag
func (dcm *DirectCacheManager) InvalidateByTag(ctx context.Context, tag string) error {
	return dcm.tagManager.InvalidateByTag(ctx, tag)
}

// InvalidateUser removes all cache entries for a specific user
func (dcm *DirectCacheManager) InvalidateUser(ctx context.Context, userID string) error {
	return dcm.InvalidateByTag(ctx, fmt.Sprintf("user:%s", userID))
}

// InvalidateTeam removes all cache entries for a specific team
func (dcm *DirectCacheManager) InvalidateTeam(ctx context.Context, teamID string) error {
	// First, get team members to invalidate their caches too
	members, err := dcm.getTeamMembers(ctx, teamID)
	if err != nil {
		log.Printf("Failed to get team members for cache invalidation: %v", err)
		// Continue with team cache invalidation even if member lookup fails
	}
	
	// Invalidate team caches
	if err := dcm.InvalidateByTag(ctx, fmt.Sprintf("team:%s", teamID)); err != nil {
		return fmt.Errorf("failed to invalidate team cache: %w", err)
	}
	
	// Invalidate affected user caches
	for _, memberID := range members {
		if err := dcm.InvalidateByTag(ctx, fmt.Sprintf("user:%s", memberID)); err != nil {
			log.Printf("Failed to invalidate user cache for member %s: %v", memberID, err)
			// Continue with other members
		}
	}
	
	return nil
}

// InvalidatePost removes all cache entries for a specific post
func (dcm *DirectCacheManager) InvalidatePost(ctx context.Context, postID string, authorID string) error {
	// Invalidate post-specific caches
	if err := dcm.InvalidateByTag(ctx, fmt.Sprintf("post:%s", postID)); err != nil {
		return fmt.Errorf("failed to invalidate post cache: %w", err)
	}
	
	// Invalidate author's cached posts if provided
	if authorID != "" {
		if err := dcm.InvalidateByTag(ctx, fmt.Sprintf("user:%s", authorID)); err != nil {
			log.Printf("Failed to invalidate author cache: %v", err)
		}
	}
	
	return nil
}

// getTeamMembers retrieves team member IDs from cache or database
func (dcm *DirectCacheManager) getTeamMembers(ctx context.Context, teamID string) ([]string, error) {
	// Try to get team members from cache first
	var teamData map[string]interface{}
	teamCacheKey := fmt.Sprintf("team:members:%s", teamID)
	
	err := dcm.Get(ctx, teamCacheKey, &teamData)
	if err == nil {
		// Found in cache, extract member IDs
		if members, ok := teamData["member_ids"].([]interface{}); ok {
			memberIDs := make([]string, len(members))
			for i, member := range members {
				if memberID, ok := member.(string); ok {
					memberIDs[i] = memberID
				}
			}
			return memberIDs, nil
		}
	}
	
	// Cache miss or invalid data - for now return empty slice
	// In a real implementation, this would call the team service
	// to get the current member list
	log.Printf("Team members not found in cache for team %s, skipping user cache invalidation", teamID)
	return []string{}, nil
}

// Delete removes a specific cache key
func (dcm *DirectCacheManager) Delete(ctx context.Context, key string) error {
	err := dcm.redis.Del(ctx, key).Err()
	if err != nil {
		return fmt.Errorf("failed to delete cache key %s: %w", key, err)
	}
	
	log.Printf("Cache DELETE: key=%s", key)
	return nil
}

// Exists checks if a cache key exists
func (dcm *DirectCacheManager) Exists(ctx context.Context, key string) bool {
	count := dcm.redis.Exists(ctx, key).Val()
	return count > 0
}

// GetCacheStats returns cache performance metrics
func (dcm *DirectCacheManager) GetCacheStats(ctx context.Context) (*CacheStats, error) {
	pipe := dcm.redis.Pipeline()
	
	// Get Redis info
	infoCmd := pipe.Info(ctx, "memory")
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get cache stats: %w", err)
	}
	
	info := infoCmd.Val()
	
	return &CacheStats{
		RedisInfo: info,
		Timestamp: time.Now(),
	}, nil
}

// CacheStats represents cache performance metrics
type CacheStats struct {
	RedisInfo string    `json:"redis_info"`
	Timestamp time.Time `json:"timestamp"`
}

// HealthCheck verifies the cache manager is working
func (dcm *DirectCacheManager) HealthCheck(ctx context.Context) error {
	// Test Redis connection
	if err := dcm.redis.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("redis connection failed: %w", err)
	}
	
	// Test basic operations
	testKey := "health_check_test"
	testValue := "ok"
	
	if err := dcm.Set(ctx, testKey, testValue, time.Minute); err != nil {
		return fmt.Errorf("cache set operation failed: %w", err)
	}
	
	var result string
	if err := dcm.Get(ctx, testKey, &result); err != nil {
		return fmt.Errorf("cache get operation failed: %w", err)
	}
	
	if result != testValue {
		return fmt.Errorf("cache value mismatch: expected %s, got %s", testValue, result)
	}
	
	// Clean up test key
	dcm.Delete(ctx, testKey)
	
	return nil
}