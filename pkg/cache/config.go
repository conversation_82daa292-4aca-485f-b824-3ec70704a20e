package cache

import (
	"time"
)

// CacheConfig contains cache-related configuration
type CacheConfig struct {
	DefaultTTL      time.Duration `mapstructure:"default_ttl" default:"15m"`
	TagExpiryBuffer time.Duration `mapstructure:"tag_expiry_buffer" default:"1h"`
	Enabled         bool          `mapstructure:"enabled" default:"true"`
}

// DefaultCacheConfig returns sensible defaults for cache configuration
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		DefaultTTL:      15 * time.Minute,
		TagExpiryBuffer: time.Hour,
		Enabled:         true,
	}
}

