package logger

import (
	"context"
	"time"
)

// PerformanceLogger provides performance monitoring capabilities
type PerformanceLogger struct {
	logger ServiceLogger
}

// NewPerformanceLogger creates a new performance logger
func NewPerformanceLogger(logger ServiceLogger) *PerformanceLogger {
	return &PerformanceLogger{
		logger: logger,
	}
}

// Timer represents a performance timer
type Timer struct {
	logger    ServiceLogger
	operation string
	startTime time.Time
	fields    []Field
}

// StartTimer begins timing an operation
func (p *PerformanceLogger) StartTimer(operation string, fields ...Field) *Timer {
	return &Timer{
		logger:    p.logger.WithOperation(operation),
		operation: operation,
		startTime: time.Now(),
		fields:    fields,
	}
}

// Stop ends the timer and logs the duration
func (t *Timer) Stop() {
	duration := time.Since(t.startTime)
	
	allFields := append(t.fields,
		F("duration_ms", duration.Milliseconds()),
		F("duration_ns", duration.Nanoseconds()),
	)
	
	if duration > time.Second {
		t.logger.Warn("Slow operation detected", allFields...)
	} else if duration > 500*time.Millisecond {
		t.logger.Warn("Operation took longer than expected", allFields...)
	} else {
		t.logger.Debug("Operation completed", allFields...)
	}
}

// StopWithContext ends the timer and logs with additional context
func (t *Timer) StopWithContext(ctx context.Context, additionalFields ...Field) {
	duration := time.Since(t.startTime)
	
	allFields := append(t.fields,
		F("duration_ms", duration.Milliseconds()),
		F("duration_ns", duration.Nanoseconds()),
	)
	allFields = append(allFields, additionalFields...)
	
	contextLogger := t.logger.WithContext(ctx)
	
	if duration > time.Second {
		contextLogger.Warn("Slow operation detected", allFields...)
	} else if duration > 500*time.Millisecond {
		contextLogger.Warn("Operation took longer than expected", allFields...)
	} else {
		contextLogger.Debug("Operation completed", allFields...)
	}
}

// Database operation logging helpers

// LogDatabaseQuery logs database query performance
func (p *PerformanceLogger) LogDatabaseQuery(query string, duration time.Duration, recordCount int, err error) {
	fields := []Field{
		F("query_type", "database"),
		F("duration_ms", duration.Milliseconds()),
		F("record_count", recordCount),
	}
	
	logger := p.logger.WithFields(fields...)
	
	if err != nil {
		logger.WithError(err).Error("Database query failed")
		return
	}
	
	if duration > 5*time.Second {
		logger.Error("Very slow database query detected")
	} else if duration > time.Second {
		logger.Warn("Slow database query detected")
	} else {
		logger.Debug("Database query completed")
	}
}

// LogCacheOperation logs cache operation performance
func (p *PerformanceLogger) LogCacheOperation(operation, key string, duration time.Duration, hit bool, err error) {
	fields := []Field{
		F("cache_operation", operation),
		F("cache_key", key),
		F("duration_ms", duration.Milliseconds()),
		F("cache_hit", hit),
	}
	
	logger := p.logger.WithFields(fields...)
	
	if err != nil {
		logger.WithError(err).Error("Cache operation failed")
		return
	}
	
	if duration > 100*time.Millisecond {
		logger.Warn("Slow cache operation detected")
	} else {
		logger.Debug("Cache operation completed")
	}
}

// LogExternalAPICall logs external API call performance
func (p *PerformanceLogger) LogExternalAPICall(service, endpoint string, duration time.Duration, statusCode int, err error) {
	fields := []Field{
		F("external_service", service),
		F("endpoint", endpoint),
		F("duration_ms", duration.Milliseconds()),
		F("status_code", statusCode),
	}
	
	logger := p.logger.WithFields(fields...)
	
	if err != nil {
		logger.WithError(err).Error("External API call failed")
		return
	}
	
	if statusCode >= 500 {
		logger.Error("External API returned server error")
	} else if statusCode >= 400 {
		logger.Warn("External API returned client error")
	} else if duration > 10*time.Second {
		logger.Warn("Very slow external API call")
	} else if duration > 3*time.Second {
		logger.Warn("Slow external API call")
	} else {
		logger.Debug("External API call completed")
	}
}

// Business operation logging helpers

// LogBusinessOperation logs business operation metrics
func (p *PerformanceLogger) LogBusinessOperation(operation string, duration time.Duration, success bool, metadata map[string]interface{}) {
	fields := []Field{
		F("business_operation", operation),
		F("duration_ms", duration.Milliseconds()),
		F("success", success),
	}
	
	// Add metadata as fields
	for key, value := range metadata {
		fields = append(fields, F(key, value))
	}
	
	logger := p.logger.WithFields(fields...)
	
	if !success {
		logger.Error("Business operation failed")
	} else if duration > 30*time.Second {
		logger.Warn("Very slow business operation")
	} else if duration > 10*time.Second {
		logger.Warn("Slow business operation")
	} else {
		logger.Info("Business operation completed")
	}
}

// Measurement helps track metrics over time
type Measurement struct {
	logger ServiceLogger
	name   string
	tags   []Field
}

// NewMeasurement creates a new measurement tracker
func (p *PerformanceLogger) NewMeasurement(name string, tags ...Field) *Measurement {
	return &Measurement{
		logger: p.logger,
		name:   name,
		tags:   tags,
	}
}

// Record records a measurement value
func (m *Measurement) Record(value float64, additionalTags ...Field) {
	allTags := append(m.tags, additionalTags...)
	allTags = append(allTags,
		F("measurement", m.name),
		F("value", value),
		F("timestamp", time.Now().Unix()),
	)
	
	m.logger.Info("Measurement recorded", allTags...)
}

// RecordCount records a count measurement
func (m *Measurement) RecordCount(count int64, additionalTags ...Field) {
	m.Record(float64(count), additionalTags...)
}

// RecordDuration records a duration measurement
func (m *Measurement) RecordDuration(duration time.Duration, additionalTags ...Field) {
	m.Record(float64(duration.Milliseconds()), append(additionalTags, F("unit", "milliseconds"))...)
}