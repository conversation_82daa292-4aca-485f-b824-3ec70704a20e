package logger

import (
	"context"
	"os"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// Logger wraps logrus.Entry with additional context and service information
type Logger struct {
	entry       *logrus.Entry
	serviceName string
	version     string
	environment string
}

// ServiceLogger interface defines the logging contract for all services
type ServiceLogger interface {
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)
	
	WithContext(ctx context.Context) ServiceLogger
	WithFields(fields ...Field) ServiceLogger
	WithError(err error) ServiceLogger
	WithRequestID(requestID string) ServiceLogger
	WithUserID(userID string) ServiceLogger
	WithOperation(operation string) ServiceLogger
}

// Field represents a log field with key-value pair
type Field struct {
	Key   string
	Value interface{}
}

// Fields helper function to create multiple fields
func Fields(fields ...Field) []Field {
	return fields
}

// F is a shorthand for creating a Field
func F(key string, value interface{}) Field {
	return Field{Key: key, Value: value}
}

// Config holds logging configuration
type Config struct {
	Level       string `json:"level" env:"LOG_LEVEL" default:"info"`
	Format      string `json:"format" env:"LOG_FORMAT" default:"json"`
	Output      string `json:"output" env:"LOG_OUTPUT" default:"stdout"`
	ServiceName string `json:"service_name" env:"SERVICE_NAME" default:"unknown"`
	Version     string `json:"version" env:"SERVICE_VERSION" default:"unknown"`
	Environment string `json:"environment" env:"APP_ENV" default:"development"`
}

// NewLogger creates a new structured logger instance
func NewLogger(config Config) ServiceLogger {
	logger := logrus.New()
	
	// Configure log level
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)
	
	// Configure formatter
	switch strings.ToLower(config.Format) {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "function",
				logrus.FieldKeyFile:  "file",
			},
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: time.RFC3339,
			FullTimestamp:   true,
		})
	default:
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	}
	
	// Configure output
	switch strings.ToLower(config.Output) {
	case "stdout":
		logger.SetOutput(os.Stdout)
	case "stderr":
		logger.SetOutput(os.Stderr)
	default:
		logger.SetOutput(os.Stdout)
	}
	
	// Create base entry with service-level fields
	entry := logger.WithFields(logrus.Fields{
		"service":     config.ServiceName,
		"version":     config.Version,
		"environment": config.Environment,
	})
	
	return &Logger{
		entry:       entry,
		serviceName: config.ServiceName,
		version:     config.Version,
		environment: config.Environment,
	}
}

// NewDefaultLogger creates a logger with default configuration
func NewDefaultLogger(serviceName string) ServiceLogger {
	return NewLogger(Config{
		Level:       getEnv("LOG_LEVEL", "info"),
		Format:      getEnv("LOG_FORMAT", "json"),
		Output:      getEnv("LOG_OUTPUT", "stdout"),
		ServiceName: serviceName,
		Version:     getEnv("SERVICE_VERSION", "unknown"),
		Environment: getEnv("APP_ENV", "development"),
	})
}

// Debug logs a debug message
func (l *Logger) Debug(msg string, fields ...Field) {
	l.entry.WithFields(convertFields(fields)).Debug(msg)
}

// Info logs an info message
func (l *Logger) Info(msg string, fields ...Field) {
	l.entry.WithFields(convertFields(fields)).Info(msg)
}

// Warn logs a warning message
func (l *Logger) Warn(msg string, fields ...Field) {
	l.entry.WithFields(convertFields(fields)).Warn(msg)
}

// Error logs an error message
func (l *Logger) Error(msg string, fields ...Field) {
	l.entry.WithFields(convertFields(fields)).Error(msg)
}

// Fatal logs a fatal message and exits
func (l *Logger) Fatal(msg string, fields ...Field) {
	l.entry.WithFields(convertFields(fields)).Fatal(msg)
}

// WithContext adds context information to the logger
func (l *Logger) WithContext(ctx context.Context) ServiceLogger {
	newEntry := l.entry
	
	// Extract request ID from context if available
	if requestID := GetRequestIDFromContext(ctx); requestID != "" {
		newEntry = newEntry.WithField("request_id", requestID)
	}
	
	// Extract user ID from context if available
	if userID := GetUserIDFromContext(ctx); userID != "" {
		newEntry = newEntry.WithField("user_id", userID)
	}
	
	// Extract trace ID from context if available
	if traceID := GetTraceIDFromContext(ctx); traceID != "" {
		newEntry = newEntry.WithField("trace_id", traceID)
	}
	
	return &Logger{
		entry:       newEntry,
		serviceName: l.serviceName,
		version:     l.version,
		environment: l.environment,
	}
}

// WithFields adds multiple fields to the logger
func (l *Logger) WithFields(fields ...Field) ServiceLogger {
	logrusFields := convertFields(fields)
	
	return &Logger{
		entry:       l.entry.WithFields(logrusFields),
		serviceName: l.serviceName,
		version:     l.version,
		environment: l.environment,
	}
}

// WithError adds error information to the logger
func (l *Logger) WithError(err error) ServiceLogger {
	return &Logger{
		entry:       l.entry.WithError(err),
		serviceName: l.serviceName,
		version:     l.version,
		environment: l.environment,
	}
}

// WithRequestID adds request ID to the logger
func (l *Logger) WithRequestID(requestID string) ServiceLogger {
	return &Logger{
		entry:       l.entry.WithField("request_id", requestID),
		serviceName: l.serviceName,
		version:     l.version,
		environment: l.environment,
	}
}

// WithUserID adds user ID to the logger
func (l *Logger) WithUserID(userID string) ServiceLogger {
	return &Logger{
		entry:       l.entry.WithField("user_id", userID),
		serviceName: l.serviceName,
		version:     l.version,
		environment: l.environment,
	}
}

// WithOperation adds operation context to the logger
func (l *Logger) WithOperation(operation string) ServiceLogger {
	return &Logger{
		entry:       l.entry.WithField("operation", operation),
		serviceName: l.serviceName,
		version:     l.version,
		environment: l.environment,
	}
}

// Helper functions

// convertFields converts our Field slice to logrus Fields
func convertFields(fields []Field) logrus.Fields {
	logrusFields := make(logrus.Fields)
	for _, field := range fields {
		logrusFields[field.Key] = field.Value
	}
	return logrusFields
}

// getEnv gets environment variable with default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Context keys for request tracking
type contextKey string

const (
	RequestIDKey contextKey = "request_id"
	UserIDKey    contextKey = "user_id"
	TraceIDKey   contextKey = "trace_id"
	OperationKey contextKey = "operation"
)

// GetRequestIDFromContext extracts request ID from context
func GetRequestIDFromContext(ctx context.Context) string {
	if requestID, ok := ctx.Value(RequestIDKey).(string); ok {
		return requestID
	}
	return ""
}

// GetUserIDFromContext extracts user ID from context
func GetUserIDFromContext(ctx context.Context) string {
	if userID, ok := ctx.Value(UserIDKey).(string); ok {
		return userID
	}
	return ""
}

// GetTraceIDFromContext extracts trace ID from context
func GetTraceIDFromContext(ctx context.Context) string {
	if traceID, ok := ctx.Value(TraceIDKey).(string); ok {
		return traceID
	}
	return ""
}

// AddRequestIDToContext adds request ID to context
func AddRequestIDToContext(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, RequestIDKey, requestID)
}

// AddUserIDToContext adds user ID to context
func AddUserIDToContext(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, UserIDKey, userID)
}

// AddTraceIDToContext adds trace ID to context
func AddTraceIDToContext(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, TraceIDKey, traceID)
}