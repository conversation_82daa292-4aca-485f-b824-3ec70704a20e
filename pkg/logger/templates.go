package logger

import (
	"fmt"
	"strings"
)

// ServiceTemplate contains template code for service logging integration
type ServiceTemplate struct {
	ServiceName string
	Database    string // "postgresql" or "mongodb"
}

// GenerateMainGoTemplate generates the main.go template with logging integration
func GenerateMainGoTemplate(template ServiceTemplate) string {
	var configAdapterCall string
	var databaseType string
	
	if template.Database == "mongodb" {
		configAdapterCall = "server.NewMongoConfigAdapter(cfg.Server, cfg.Database, cfg.Redis)"
		databaseType = "server.MongoDB"
	} else {
		configAdapterCall = "server.NewConfigAdapter(cfg.Server, cfg.Database, cfg.Redis)"
		databaseType = "server.PostgreSQL"
	}
	
	return fmt.Sprintf(`package main

import (
	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	"github.com/swork-team/platform/services/%s/internal/config"
	"github.com/swork-team/platform/services/%s/internal/handlers"
	"github.com/swork-team/platform/services/%s/internal/repositories"
	"github.com/swork-team/platform/services/%s/internal/services"
)

func main() {
	cfg := config.LoadConfig()

	// Initialize standardized logger
	loggerManager := logger.NewServiceLoggerManager("%s")
	serviceLogger := loggerManager.GetLogger()
	
	serviceLogger.Info("%s service starting up",
		logger.F("version", cfg.Server.Version),
		logger.F("environment", cfg.Server.Environment),
		logger.F("port", cfg.Server.Port),
		logger.F("database_type", "%s"),
	)

	// Create config adapter for bootstrap
	configAdapter := %s

	// Setup router function
	routerSetup := func(components *server.ServiceComponents) *gin.Engine {
		// Initialize repositories and services here
		return setupRouter(components, cfg, loggerManager)
	}

	// Cleanup function
	cleanupFunc := func() error {
		serviceLogger.Info("%s service shutting down")
		return nil
	}

	// Bootstrap the service
	server.BootstrapService(server.ServiceOptions{
		ServiceName:        "%s",
		Config:             configAdapter,
		Models:             models, // Define your models slice
		RouterSetup:        routerSetup,
		EnableExtensions:   false,
		ServiceCleanupFunc: cleanupFunc,
		DatabaseType:       %s,
	})
}

func setupRouter(components *server.ServiceComponents, cfg *config.Config, loggerManager *logger.ServiceLoggerManager) *gin.Engine {
	router := server.SetupStandardRouter()
	
	serviceLogger := loggerManager.GetLogger()
	
	// Add standardized logging middleware
	router.Use(logger.RequestLoggingMiddleware(serviceLogger))
	router.Use(logger.UserContextMiddleware())
	router.Use(logger.ErrorLoggingMiddleware())

	// Health check
	server.AddStandardHealthCheck(router, "%s-service")

	// Add your routes here

	return router
}`,
		template.ServiceName, template.ServiceName, template.ServiceName, template.ServiceName,
		template.ServiceName, strings.Title(template.ServiceName), strings.ToLower(template.Database),
		configAdapterCall, strings.Title(template.ServiceName), template.ServiceName,
		databaseType, template.ServiceName)
}

// GenerateHandlerTemplate generates handler template with logging
func GenerateHandlerTemplate(serviceName string) string {
	return fmt.Sprintf(`package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/utils"
)

type %sHandler struct {
	service %sService
}

func New%sHandler(service %sService) *%sHandler {
	return &%sHandler{
		service: service,
	}
}

// Example handler method with logging
func (h *%sHandler) ExampleMethod(c *gin.Context) {
	// Get logger from context (injected by middleware)
	requestLogger := logger.GetLoggerFromContext(c)
	
	requestLogger.Info("Processing example request",
		logger.F("handler", "%s"),
		logger.F("method", "ExampleMethod"),
	)
	
	// Your business logic here
	
	utils.SuccessResponse(c, http.StatusOK, "Success", nil)
}`, strings.Title(serviceName), strings.Title(serviceName), 
		strings.Title(serviceName), strings.Title(serviceName), 
		strings.Title(serviceName), strings.Title(serviceName),
		strings.Title(serviceName), serviceName)
}

// GenerateServiceTemplate generates service template with logging
func GenerateServiceTemplate(serviceName string) string {
	return fmt.Sprintf(`package services

import (
	"context"
	"github.com/swork-team/platform/pkg/logger"
)

type %sService struct {
	logger logger.ServiceLogger
	// Add your dependencies here
}

func New%sService(logger logger.ServiceLogger) *%sService {
	return &%sService{
		logger: logger,
	}
}

// Example service method with logging
func (s *%sService) ExampleMethod(ctx context.Context, param string) error {
	operationLogger := s.logger.WithContext(ctx).WithOperation("example_method")
	
	operationLogger.Info("Starting example operation",
		logger.F("parameter", param),
	)
	
	// Your business logic here
	
	operationLogger.Info("Example operation completed successfully")
	return nil
}`, strings.Title(serviceName), strings.Title(serviceName), 
		strings.Title(serviceName), strings.Title(serviceName),
		strings.Title(serviceName))
}

// LoggingBestPractices contains logging best practices documentation
const LoggingBestPractices = `
# Logging Best Practices for Swork Team Platform

## 1. Service Initialization

Always initialize the logger manager at the start of your main() function:

'''go
loggerManager := logger.NewServiceLoggerManager("service-name")
serviceLogger := loggerManager.GetLogger()
'''

## 2. Middleware Setup

Add logging middleware to your router in this order:

'''go
router.Use(logger.RequestLoggingMiddleware(serviceLogger))
router.Use(logger.UserContextMiddleware())
router.Use(logger.ErrorLoggingMiddleware())
'''

## 3. Handler Logging

In handlers, get the logger from context and add relevant fields:

'''go
func (h *Handler) Method(c *gin.Context) {
	requestLogger := logger.GetLoggerFromContext(c)
	
	requestLogger.Info("Processing request",
		logger.F("handler", "handler_name"),
		logger.F("operation", "operation_name"),
	)
}
'''

## 4. Service Layer Logging

Pass logger to services and use operation-specific loggers:

'''go
operationLogger := s.logger.WithContext(ctx).WithOperation("operation_name")

operationLogger.Info("Operation started",
	logger.F("parameter", value),
)
'''

## 5. Error Logging

Always log errors with context:

'''go
if err != nil {
	operationLogger.WithError(err).Error("Operation failed",
		logger.F("step", "database_query"),
		logger.F("user_id", userID),
	)
	return err
}
'''

## 6. Performance Logging

Use performance logger for timing operations:

'''go
perfLogger := loggerManager.GetPerformanceLogger()
timer := perfLogger.StartTimer("database_query",
	logger.F("table", "users"),
	logger.F("operation", "select"),
)
defer timer.Stop()
'''

## 7. Security Logging

Log security events using the security helper:

'''go
loggerManager.LogSecurityEvent("auth_attempt", userID, clientIP, success, details)
'''

## 8. Business Events

Log important business events:

'''go
loggerManager.LogUserAction(userID, "post_created", true, metadata)
'''

## 9. Log Levels

Use appropriate log levels:
- DEBUG: Detailed information for debugging
- INFO: General operational information
- WARN: Potentially harmful situations
- ERROR: Error events that might still allow the application to continue
- FATAL: Very severe error events that will presumably lead the application to abort

## 10. Field Naming

Use consistent field names:
- user_id (not userID or userId)
- request_id (not requestID)
- operation (not op or action)
- duration_ms (not duration or time)

## 11. Context Propagation

Always propagate context with user and request information:

'''go
ctx := logger.AddUserIDToContext(ctx, userID)
ctx = logger.AddRequestIDToContext(ctx, requestID)
'''
`

// GenerateDocumentation generates best practices documentation
func GenerateDocumentation() string {
	return LoggingBestPractices
}