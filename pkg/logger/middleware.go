package logger

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestLoggingMiddleware creates a middleware for request/response logging
func RequestLoggingMiddleware(logger ServiceLogger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// Generate request ID if not present
		requestID := c.Get<PERSON>eader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		
		// Add request ID to context
		ctx := AddRequestIDToContext(c.Request.Context(), requestID)
		c.Request = c.Request.WithContext(ctx)
		
		// Add request ID to response headers
		c.<PERSON><PERSON>("X-Request-ID", requestID)
		
		// Create request logger
		requestLogger := logger.WithRequestID(requestID).WithFields(
			F("method", c.Request.Method),
			F("path", c.Request.URL.Path),
			F("query", c.Request.URL.RawQuery),
			F("ip", c.ClientIP()),
			F("user_agent", c.Request.UserAgent()),
		)
		
		// Log request start
		requestLogger.Info("Request started")
		
		// Store logger in context for use in handlers
		c.Set("logger", requestLogger)
		
		// Process request
		c.Next()
		
		// Calculate duration
		duration := time.Since(start)
		
		// Log request completion
		responseLogger := requestLogger.WithFields(
			F("status", c.Writer.Status()),
			F("duration_ms", duration.Milliseconds()),
			F("response_size", c.Writer.Size()),
		)
		
		// Add error information if present
		if len(c.Errors) > 0 {
			responseLogger = responseLogger.WithFields(
				F("errors", c.Errors.String()),
			)
		}
		
		// Log based on status code
		if c.Writer.Status() >= 500 {
			responseLogger.Error("Request completed with server error")
		} else if c.Writer.Status() >= 400 {
			responseLogger.Warn("Request completed with client error")
		} else {
			responseLogger.Info("Request completed successfully")
		}
	}
}

// GetLoggerFromContext extracts the logger from gin context
func GetLoggerFromContext(c *gin.Context) ServiceLogger {
	if logger, exists := c.Get("logger"); exists {
		if serviceLogger, ok := logger.(ServiceLogger); ok {
			return serviceLogger
		}
	}
	
	// Fallback to default logger
	return NewDefaultLogger("unknown")
}

// UserContextMiddleware adds user information to the logging context
func UserContextMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract user ID from authentication middleware
		if userID, exists := c.Get("user_id"); exists {
			if userIDStr, ok := userID.(string); ok {
				// Add user ID to request context
				ctx := AddUserIDToContext(c.Request.Context(), userIDStr)
				c.Request = c.Request.WithContext(ctx)
				
				// Update logger with user context
				if logger := GetLoggerFromContext(c); logger != nil {
					updatedLogger := logger.WithUserID(userIDStr)
					c.Set("logger", updatedLogger)
				}
			}
		}
		
		c.Next()
	}
}

// OperationMiddleware adds operation context to logging
func OperationMiddleware(operation string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Update logger with operation context
		if logger := GetLoggerFromContext(c); logger != nil {
			updatedLogger := logger.WithOperation(operation)
			c.Set("logger", updatedLogger)
		}
		
		c.Next()
	}
}

// ErrorLoggingMiddleware logs detailed error information
func ErrorLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		
		// Log any errors that occurred during request processing
		if len(c.Errors) > 0 {
			logger := GetLoggerFromContext(c)
			
			for _, err := range c.Errors {
				errorLogger := logger.WithError(err.Err).WithFields(
					F("error_type", err.Type),
					F("error_meta", err.Meta),
				)
				
				switch err.Type {
				case gin.ErrorTypeBind:
					errorLogger.Warn("Request binding error")
				case gin.ErrorTypePublic:
					errorLogger.Info("Public error")
				case gin.ErrorTypeRender:
					errorLogger.Error("Response rendering error")
				default:
					errorLogger.Error("Request processing error")
				}
			}
		}
	}
}