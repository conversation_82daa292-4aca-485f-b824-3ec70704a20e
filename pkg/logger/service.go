package logger

import (
	"fmt"
	"os"
	"strconv"
	"sync"
)

// ServiceLoggerConfig holds service-specific logging configuration
type ServiceLoggerConfig struct {
	ServiceName   string
	Version       string
	Environment   string
	LogLevel      string
	LogFormat     string
	LogOutput     string
	EnableMetrics bool
	EnableTracing bool
}

// LoadServiceConfig loads logging configuration for a specific service
func LoadServiceConfig(serviceName string) ServiceLoggerConfig {
	return ServiceLoggerConfig{
		ServiceName:   serviceName,
		Version:       getEnvWithDefault("SERVICE_VERSION", "unknown"),
		Environment:   getEnvWithDefault("APP_ENV", "development"),
		LogLevel:      getEnvWithDefault("LOG_LEVEL", getDefaultLogLevel()),
		LogFormat:     getEnvWithDefault("LOG_FORMAT", getDefaultLogFormat()),
		LogOutput:     getEnvWithDefault("LOG_OUTPUT", "stdout"),
		EnableMetrics: getEnvBool("LOG_ENABLE_METRICS", true),
		EnableTracing: getEnvBool("LOG_ENABLE_TRACING", true),
	}
}

// NewServiceLogger creates a logger configured for a specific service
func NewServiceLogger(serviceName string) ServiceLogger {
	config := LoadServiceConfig(serviceName)
	
	loggerConfig := Config{
		Level:       config.LogLevel,
		Format:      config.LogFormat,
		Output:      config.LogOutput,
		ServiceName: config.ServiceName,
		Version:     config.Version,
		Environment: config.Environment,
	}
	
	return NewLogger(loggerConfig)
}

// getDefaultLogLevel returns the appropriate default log level based on environment
func getDefaultLogLevel() string {
	env := getEnvWithDefault("APP_ENV", "development")
	switch env {
	case "production":
		return "info"
	case "staging":
		return "info"
	case "development":
		return "debug"
	case "test":
		return "warn"
	default:
		return "info"
	}
}

// getDefaultLogFormat returns the appropriate default log format based on environment
func getDefaultLogFormat() string {
	env := getEnvWithDefault("APP_ENV", "development")
	if env == "development" {
		return "text"
	}
	return "json"
}

// getEnvWithDefault gets environment variable with fallback to default
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvBool gets environment variable as boolean
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// ServiceLoggerManager manages loggers for different operations within a service
type ServiceLoggerManager struct {
	baseLogger     ServiceLogger
	perfLogger     *PerformanceLogger
	operationCache sync.Map // Thread-safe map for operation loggers
}

// NewServiceLoggerManager creates a new logger manager for a service
func NewServiceLoggerManager(serviceName string) *ServiceLoggerManager {
	baseLogger := NewServiceLogger(serviceName)
	
	return &ServiceLoggerManager{
		baseLogger:     baseLogger,
		perfLogger:     NewPerformanceLogger(baseLogger),
		operationCache: sync.Map{},
	}
}

// GetLogger returns the base service logger
func (m *ServiceLoggerManager) GetLogger() ServiceLogger {
	return m.baseLogger
}

// GetPerformanceLogger returns the performance logger
func (m *ServiceLoggerManager) GetPerformanceLogger() *PerformanceLogger {
	return m.perfLogger
}

// GetOperationLogger returns a logger for a specific operation (cached)
func (m *ServiceLoggerManager) GetOperationLogger(operation string) ServiceLogger {
	if value, exists := m.operationCache.Load(operation); exists {
		return value.(ServiceLogger)
	}
	
	logger := m.baseLogger.WithOperation(operation)
	m.operationCache.Store(operation, logger)
	return logger
}

// Database logging helpers for common operations

// LogDBConnection logs database connection events
func (m *ServiceLoggerManager) LogDBConnection(dbType, host string, success bool, err error) {
	logger := m.GetOperationLogger("db_connection").WithFields(
		F("db_type", dbType),
		F("db_host", host),
		F("success", success),
	)
	
	if err != nil {
		logger.WithError(err).Error("Database connection failed")
	} else if success {
		logger.Info("Database connection established")
	} else {
		logger.Warn("Database connection attempt failed")
	}
}

// LogDBMigration logs database migration events
func (m *ServiceLoggerManager) LogDBMigration(version string, direction string, success bool, err error) {
	logger := m.GetOperationLogger("db_migration").WithFields(
		F("migration_version", version),
		F("migration_direction", direction),
		F("success", success),
	)
	
	if err != nil {
		logger.WithError(err).Error("Database migration failed")
	} else if success {
		logger.Info("Database migration completed")
	} else {
		logger.Warn("Database migration unsuccessful")
	}
}

// Service startup/shutdown logging helpers

// LogServiceStartup logs service startup events
func (m *ServiceLoggerManager) LogServiceStartup(port int, additionalInfo map[string]interface{}) {
	fields := []Field{
		F("event", "service_startup"),
		F("port", port),
	}
	
	for key, value := range additionalInfo {
		fields = append(fields, F(key, value))
	}
	
	m.baseLogger.Info("Service starting up", fields...)
}

// LogServiceReady logs when service is ready to accept requests
func (m *ServiceLoggerManager) LogServiceReady(port int) {
	m.baseLogger.Info("Service ready to accept requests",
		F("event", "service_ready"),
		F("port", port),
	)
}

// LogServiceShutdown logs service shutdown events
func (m *ServiceLoggerManager) LogServiceShutdown(reason string) {
	m.baseLogger.Info("Service shutting down",
		F("event", "service_shutdown"),
		F("reason", reason),
	)
}

// LogServiceError logs critical service errors
func (m *ServiceLoggerManager) LogServiceError(component string, err error, fatal bool) {
	logger := m.baseLogger.WithFields(
		F("event", "service_error"),
		F("component", component),
		F("fatal", fatal),
	).WithError(err)
	
	if fatal {
		logger.Fatal("Critical service error")
	} else {
		logger.Error("Service error occurred")
	}
}

// Business logic logging helpers

// LogUserAction logs user-initiated actions
func (m *ServiceLoggerManager) LogUserAction(userID, action string, success bool, metadata map[string]interface{}) {
	fields := []Field{
		F("event", "user_action"),
		F("user_id", userID),
		F("action", action),
		F("success", success),
	}
	
	for key, value := range metadata {
		fields = append(fields, F(key, value))
	}
	
	logger := m.GetOperationLogger("user_action")
	
	if success {
		logger.Info("User action completed", fields...)
	} else {
		logger.Warn("User action failed", fields...)
	}
}

// LogSystemEvent logs system-level events
func (m *ServiceLoggerManager) LogSystemEvent(event string, severity string, metadata map[string]interface{}) {
	fields := []Field{
		F("event", "system_event"),
		F("event_type", event),
		F("severity", severity),
	}
	
	for key, value := range metadata {
		fields = append(fields, F(key, value))
	}
	
	logger := m.GetOperationLogger("system_event")
	
	switch severity {
	case "critical":
		logger.Error("Critical system event", fields...)
	case "warning":
		logger.Warn("System warning", fields...)
	case "info":
		logger.Info("System event", fields...)
	default:
		logger.Debug("System event", fields...)
	}
}

// Security logging helpers

// LogSecurityEvent logs security-related events
func (m *ServiceLoggerManager) LogSecurityEvent(eventType, userID, source string, success bool, details map[string]interface{}) {
	fields := []Field{
		F("event", "security_event"),
		F("security_event_type", eventType),
		F("user_id", userID),
		F("source", source),
		F("success", success),
	}
	
	for key, value := range details {
		fields = append(fields, F(key, value))
	}
	
	logger := m.GetOperationLogger("security")
	
	if !success {
		logger.Warn("Security event failed", fields...)
	} else {
		logger.Info("Security event", fields...)
	}
}

// LogAuthAttempt logs authentication attempts
func (m *ServiceLoggerManager) LogAuthAttempt(userID, method, source string, success bool, reason string) {
	logger := m.GetOperationLogger("authentication").WithFields(
		F("event", "auth_attempt"),
		F("user_id", userID),
		F("auth_method", method),
		F("source", source),
		F("success", success),
		F("reason", reason),
	)
	
	if success {
		logger.Info("Authentication successful")
	} else {
		logger.Warn("Authentication failed")
	}
}

// Configuration validation

// ValidateLogConfig validates logging configuration
func ValidateLogConfig(config ServiceLoggerConfig) error {
	validLevels := map[string]bool{
		"trace": true, "debug": true, "info": true, 
		"warn": true, "warning": true, "error": true, 
		"fatal": true, "panic": true,
	}
	
	if !validLevels[config.LogLevel] {
		return fmt.Errorf("invalid log level: %s", config.LogLevel)
	}
	
	validFormats := map[string]bool{
		"json": true, "text": true,
	}
	
	if !validFormats[config.LogFormat] {
		return fmt.Errorf("invalid log format: %s", config.LogFormat)
	}
	
	validOutputs := map[string]bool{
		"stdout": true, "stderr": true,
	}
	
	if !validOutputs[config.LogOutput] {
		return fmt.Errorf("invalid log output: %s", config.LogOutput)
	}
	
	return nil
}