package redis

import (
	"context"
	"fmt"
	"log"

	"github.com/redis/go-redis/v9"
)

// RedisConfig interface for Redis configuration
type RedisConfig interface {
	GetURL() string
	GetPassword() string
	GetDB() int
}

// NewRedisClient creates a Redis client from URL string
func NewRedisClient(redisURL string) (*redis.Client, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse redis URL: %w", err)
	}

	client := redis.NewClient(opt)

	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to ping redis: %w", err)
	}

	log.Println("Successfully connected to Redis")
	return client, nil
}

// NewRedisClientFromConfig creates a Redis client from configuration object
func NewRedisClientFromConfig(cfg RedisConfig) (*redis.Client, error) {
	opt, err := redis.ParseURL(cfg.GetURL())
	if err != nil {
		return nil, fmt.Errorf("failed to parse redis URL: %w", err)
	}

	// Override password if provided in config
	if cfg.GetPassword() != "" {
		opt.Password = cfg.GetPassword()
	}

	// Override DB if provided in config
	opt.DB = cfg.GetDB()

	client := redis.NewClient(opt)

	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to ping redis: %w", err)
	}

	log.Println("Successfully connected to Redis")
	return client, nil
}