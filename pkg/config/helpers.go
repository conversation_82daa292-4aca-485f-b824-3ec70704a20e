package config

import (
	"strconv"
	"strings"
)

// GetIntSliceEnv parses a comma-separated list of integers from the environment variable
func GetIntSliceEnv(key string, defaultValue []int) []int {
	if value := GetEnv(key, ""); value != "" {
		strs := strings.Split(value, ",")
		ints := make([]int, 0, len(strs))
		for _, str := range strs {
			if i, err := strconv.Atoi(strings.TrimSpace(str)); err == nil {
				ints = append(ints, i)
			}
		}
		if len(ints) > 0 {
			return ints
		}
	}
	return defaultValue
}
