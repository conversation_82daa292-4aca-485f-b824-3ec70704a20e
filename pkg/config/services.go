package config

import "time"

// ServiceRegistry provides centralized service URL configuration
type ServiceRegistry struct {
	Auth              string `json:"auth_service_url"`
	User              string `json:"user_service_url"`
	Team              string `json:"team_service_url"`
	Social            string `json:"social_service_url"`
	Drive             string `json:"drive_service_url"`
	Calendar          string `json:"calendar_service_url"`
	Notification      string `json:"notification_service_url"`
}

// NewServiceRegistry creates a standardized service registry
func NewServiceRegistry() ServiceRegistry {
	return ServiceRegistry{
		Auth:             GetEnv("AUTH_SERVICE_URL", "http://auth-service:8080"),
		User:             GetEnv("USER_SERVICE_URL", "http://user-service:8080"),
		Team:             GetEnv("TEAM_SERVICE_URL", "http://team-service:8080"),
		Social:           GetEnv("SOCIAL_SERVICE_URL", "http://social-service:8080"),
		Drive:            GetEnv("DRIVE_SERVICE_URL", "http://drive-service:8080"),
		Calendar:         GetEnv("CALENDAR_SERVICE_URL", "http://calendar-service:8080"),
		Notification:     GetEnv("NOTIFICATION_SERVICE_URL", "http://notification-service:8080"),
	}
}

// TimeoutConfig provides standardized timeout configurations
type TimeoutConfig struct {
	HTTP        time.Duration `json:"http_timeout"`
	Database    time.Duration `json:"database_timeout"`
	Cache       time.Duration `json:"cache_timeout"`
	Processing  time.Duration `json:"processing_timeout"`
	Shutdown    time.Duration `json:"shutdown_timeout"`
}

// NewTimeoutConfig creates standardized timeout configuration
func NewTimeoutConfig() TimeoutConfig {
	return TimeoutConfig{
		HTTP:       GetDurationEnv("HTTP_TIMEOUT", 30*time.Second),
		Database:   GetDurationEnv("DATABASE_TIMEOUT", 10*time.Second),
		Cache:      GetDurationEnv("CACHE_TIMEOUT", 5*time.Second),
		Processing: GetDurationEnv("PROCESSING_TIMEOUT", 5*time.Minute),
		Shutdown:   GetDurationEnv("SHUTDOWN_TIMEOUT", 30*time.Second),
	}
}

// CacheTTLConfig provides standardized cache TTL configurations  
type CacheTTLConfig struct {
	UserProfile    time.Duration `json:"user_profile_ttl"`
	TeamInfo       time.Duration `json:"team_info_ttl"`
	Suggestions    time.Duration `json:"suggestions_ttl"`
	Search         time.Duration `json:"search_ttl"`
}

// NewCacheTTLConfig creates standardized cache configuration
func NewCacheTTLConfig() CacheTTLConfig {
	return CacheTTLConfig{
		UserProfile: GetDurationEnv("CACHE_USER_PROFILE_TTL", 30*time.Minute),
		TeamInfo:    GetDurationEnv("CACHE_TEAM_INFO_TTL", 15*time.Minute),
		Suggestions: GetDurationEnv("CACHE_SUGGESTIONS_TTL", 10*time.Minute),
		Search:      GetDurationEnv("CACHE_SEARCH_TTL", 5*time.Minute),
	}
}

// MessagingServiceConfig provides centralized messaging service configuration
type MessagingServiceConfig struct {
	*BaseServiceConfig
	MongoDB     MongoConfig
	WebSocket   WebSocketConfig
	Messaging   MessagingConfig
	Queue       QueueConfig
	Connection  ConnectionConfig
	Events      EventConfig
}

// WebSocketConfig provides WebSocket connection configuration
type WebSocketConfig struct {
	MaxConnections     int64         `json:"max_connections"`
	ReadTimeout        time.Duration `json:"read_timeout"`
	WriteTimeout       time.Duration `json:"write_timeout"`
	PongTimeout        time.Duration `json:"pong_timeout"`
	PingPeriod         time.Duration `json:"ping_period"`
	MaxMessageSize     int64         `json:"max_message_size"`
	EnableCompression  bool          `json:"enable_compression"`
	BufferSize         int           `json:"buffer_size"`
	ChannelBufferSize  int           `json:"channel_buffer_size"`
}

// MessagingConfig provides messaging-specific configuration
type MessagingConfig struct {
	MessagesPerDocument  int           `json:"messages_per_document"`
	SequenceBatchSize    int64         `json:"sequence_batch_size"`
	GroupSequenceBatch   int64         `json:"group_sequence_batch"`
	MessageCacheTTL      time.Duration `json:"message_cache_ttl"`
	SequenceCacheTTL     time.Duration `json:"sequence_cache_ttl"`
	ConversationCacheTTL time.Duration `json:"conversation_cache_ttl"`
	ReadStatusTTL        time.Duration `json:"read_status_ttl"`
	MaxWorkers           int           `json:"max_workers"`
	BatchSize            int           `json:"batch_size"`
	BatchInterval        time.Duration `json:"batch_interval"`
}

// QueueConfig provides queue processing configuration
type QueueConfig struct {
	WorkerCount          int           `json:"worker_count"`
	MaxWorkerCount       int           `json:"max_worker_count"`
	MinWorkerCount       int           `json:"min_worker_count"`
	PriorityQueues       int           `json:"priority_queues"`
	MaxQueueSize         int           `json:"max_queue_size"`
	ProcessingTimeout    int           `json:"processing_timeout"`
	RetryAttempts        int           `json:"retry_attempts"`
	BackoffMultiplier    float64       `json:"backoff_multiplier"`
	MaxBackoffDuration   time.Duration `json:"max_backoff_duration"`
	BatchProcessing      bool          `json:"batch_processing"`
	BatchSize           int           `json:"batch_size"`
	BatchTimeout        time.Duration `json:"batch_timeout"`
	EnableMetrics       bool          `json:"enable_metrics"`
	MetricsInterval     time.Duration `json:"metrics_interval"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
}

// ConnectionConfig provides connection management configuration
type ConnectionConfig struct {
	ConnectionTTL       time.Duration `json:"connection_ttl"`
	PresenceTTL         time.Duration `json:"presence_ttl"`
	MetadataTTL         time.Duration `json:"metadata_ttl"`
	CleanupInterval     time.Duration `json:"cleanup_interval"`
	StaleTimeout        time.Duration `json:"stale_timeout"`
	BatchSize          int           `json:"batch_size"`
	MaxConcurrentOps   int           `json:"max_concurrent_ops"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HeartbeatInterval   time.Duration `json:"heartbeat_interval"`
}

// EventConfig provides event processing configuration
type EventConfig struct {
	RealTimeEnabled        bool          `json:"realtime_enabled"`
	RealTimeTimeout        time.Duration `json:"realtime_timeout"`
	AsyncEnabled           bool          `json:"async_enabled"`
	AsyncRetries           int           `json:"async_retries"`
	NotificationsEnabled   bool          `json:"notifications_enabled"`
	OfflineNotificationDelay time.Duration `json:"offline_notification_delay"`
	FilterSystemMessages   bool          `json:"filter_system_messages"`
	FilterDeletedMessages  bool          `json:"filter_deleted_messages"`
	BufferSize            int           `json:"buffer_size"`
	BatchSize            int           `json:"batch_size"`
	FlushInterval        time.Duration `json:"flush_interval"`
}

// NewMessagingServiceConfig creates a centralized messaging service configuration
func NewMessagingServiceConfig() *MessagingServiceConfig {
	LoadEnvFile()
	
	baseConfig := LoadBaseConfig("MESSAGING")
	mongoConfig := NewMongoConfig()
	
	// Override specific configuration for messaging service
	mongoConfig.Database = GetEnv("MESSAGING_MONGO_DATABASE", "swork_messaging")
	
	// Use messaging-specific Redis database
	redisConfig := NewRedisConfig()
	redisConfig.DB = GetEnvAsInt("MESSAGING_REDIS_DB", 2)
	
	// Override base config with messaging-specific settings
	baseConfig.Redis = redisConfig
	
	return &MessagingServiceConfig{
		BaseServiceConfig: baseConfig,
		MongoDB:          mongoConfig,
		WebSocket: WebSocketConfig{
			MaxConnections:     GetEnvAsInt64("WS_MAX_CONNECTIONS", 100000),
			ReadTimeout:        GetEnvAsDuration("WS_READ_TIMEOUT", 60*time.Second),
			WriteTimeout:       GetEnvAsDuration("WS_WRITE_TIMEOUT", 10*time.Second),
			PongTimeout:        GetEnvAsDuration("WS_PONG_TIMEOUT", 30*time.Second),
			PingPeriod:         GetEnvAsDuration("WS_PING_PERIOD", 25*time.Second),
			MaxMessageSize:     GetEnvAsInt64("WS_MAX_MESSAGE_SIZE", 8192),
			EnableCompression:  GetEnvAsBool("WS_ENABLE_COMPRESSION", true),
			BufferSize:         GetEnvAsInt("WS_BUFFER_SIZE", 1024),
			ChannelBufferSize:  GetEnvAsInt("WS_CHANNEL_BUFFER_SIZE", 256),
		},
		Messaging: MessagingConfig{
			MessagesPerDocument:  GetEnvAsInt("MESSAGES_PER_DOCUMENT", 100),
			SequenceBatchSize:    GetEnvAsInt64("SEQUENCE_BATCH_SIZE", 50),
			GroupSequenceBatch:   GetEnvAsInt64("GROUP_SEQUENCE_BATCH", 100),
			MessageCacheTTL:      GetEnvAsDuration("MESSAGE_CACHE_TTL", 24*time.Hour),
			SequenceCacheTTL:     GetEnvAsDuration("SEQUENCE_CACHE_TTL", 8760*time.Hour),
			ConversationCacheTTL: GetEnvAsDuration("CONVERSATION_CACHE_TTL", 12*time.Hour),
			ReadStatusTTL:        GetEnvAsDuration("READ_STATUS_TTL", 168*time.Hour),
			MaxWorkers:           GetEnvAsInt("MESSAGE_MAX_WORKERS", 50),
			BatchSize:            GetEnvAsInt("MESSAGE_BATCH_SIZE", 500),
			BatchInterval:        GetEnvAsDuration("MESSAGE_BATCH_INTERVAL", 100*time.Millisecond),
		},
		Queue: QueueConfig{
			WorkerCount:          GetEnvAsInt("QUEUE_WORKER_COUNT", 10),
			MaxWorkerCount:       GetEnvAsInt("QUEUE_MAX_WORKER_COUNT", 50),
			MinWorkerCount:       GetEnvAsInt("QUEUE_MIN_WORKER_COUNT", 2),
			PriorityQueues:       GetEnvAsInt("QUEUE_PRIORITY_QUEUES", 3),
			MaxQueueSize:         GetEnvAsInt("QUEUE_MAX_SIZE", 1000),
			ProcessingTimeout:    GetEnvAsInt("QUEUE_PROCESSING_TIMEOUT", 300),
			RetryAttempts:        GetEnvAsInt("QUEUE_RETRY_ATTEMPTS", 3),
			BackoffMultiplier:    GetEnvAsFloat64("QUEUE_BACKOFF_MULTIPLIER", 2.0),
			MaxBackoffDuration:   GetEnvAsDuration("QUEUE_MAX_BACKOFF_DURATION", 5*time.Minute),
			BatchProcessing:      GetEnvAsBool("QUEUE_BATCH_PROCESSING", true),
			BatchSize:           GetEnvAsInt("QUEUE_BATCH_SIZE", 10),
			BatchTimeout:        GetEnvAsDuration("QUEUE_BATCH_TIMEOUT", 100*time.Millisecond),
			EnableMetrics:       GetEnvAsBool("QUEUE_ENABLE_METRICS", true),
			MetricsInterval:     GetEnvAsDuration("QUEUE_METRICS_INTERVAL", 30*time.Second),
			HealthCheckInterval: GetEnvAsDuration("QUEUE_HEALTH_CHECK_INTERVAL", 1*time.Minute),
		},
		Connection: ConnectionConfig{
			ConnectionTTL:       GetEnvAsDuration("CONNECTION_TTL", 30*time.Minute),
			PresenceTTL:         GetEnvAsDuration("CONNECTION_PRESENCE_TTL", 5*time.Minute),
			MetadataTTL:         GetEnvAsDuration("CONNECTION_METADATA_TTL", 1*time.Hour),
			CleanupInterval:     GetEnvAsDuration("CONNECTION_CLEANUP_INTERVAL", 5*time.Minute),
			StaleTimeout:        GetEnvAsDuration("CONNECTION_STALE_TIMEOUT", 10*time.Minute),
			BatchSize:          GetEnvAsInt("CONNECTION_BATCH_SIZE", 100),
			MaxConcurrentOps:   GetEnvAsInt("CONNECTION_MAX_CONCURRENT_OPS", 10),
			HealthCheckInterval: GetEnvAsDuration("CONNECTION_HEALTH_CHECK_INTERVAL", 1*time.Minute),
			HeartbeatInterval:   GetEnvAsDuration("CONNECTION_HEARTBEAT_INTERVAL", 30*time.Second),
		},
		Events: EventConfig{
			RealTimeEnabled:        GetEnvAsBool("EVENTS_REALTIME_ENABLED", true),
			RealTimeTimeout:        GetEnvAsDuration("EVENTS_REALTIME_TIMEOUT", 5*time.Second),
			AsyncEnabled:           GetEnvAsBool("EVENTS_ASYNC_ENABLED", true),
			AsyncRetries:           GetEnvAsInt("EVENTS_ASYNC_RETRIES", 3),
			NotificationsEnabled:   GetEnvAsBool("EVENTS_NOTIFICATIONS_ENABLED", true),
			OfflineNotificationDelay: GetEnvAsDuration("EVENTS_OFFLINE_NOTIFICATION_DELAY", 30*time.Second),
			FilterSystemMessages:   GetEnvAsBool("EVENTS_FILTER_SYSTEM_MESSAGES", false),
			FilterDeletedMessages:  GetEnvAsBool("EVENTS_FILTER_DELETED_MESSAGES", true),
			BufferSize:            GetEnvAsInt("EVENTS_BUFFER_SIZE", 1000),
			BatchSize:            GetEnvAsInt("EVENTS_BATCH_SIZE", 10),
			FlushInterval:        GetEnvAsDuration("EVENTS_FLUSH_INTERVAL", 100*time.Millisecond),
		},
	}
}