package config

// BaseServiceConfig provides a standard configuration structure for simple services
// that only need the core infrastructure components (Server, Database, Redis, Services)
type BaseServiceConfig struct {
	Server   ServerConfig
	Database DatabaseConfig
	Redis    RedisConfig
	Services ServiceRegistry
}

// MongoBaseServiceConfig provides a standard configuration structure for services using MongoDB
type MongoBaseServiceConfig struct {
	Server   ServerConfig
	Database MongoConfig
	Redis    RedisConfig
	Services ServiceRegistry
}

// ExtendedServerBaseConfig provides base configuration with server BaseURL extension
type ExtendedServerBaseConfig struct {
	BaseServiceConfig
	BaseURL string
}

// ExtendedMongoServerBaseConfig provides MongoDB base configuration with server BaseURL extension
type ExtendedMongoServerBaseConfig struct {
	MongoBaseServiceConfig
	BaseURL string
}

// NewExtendedServerConfig creates an extended server configuration with BaseURL
func NewExtendedServerConfig(serviceName, baseURL string) *ExtendedServerBaseConfig {
	baseConfig := LoadBaseConfig(serviceName)
	return &ExtendedServerBaseConfig{
		BaseServiceConfig: *baseConfig,
		BaseURL:          baseURL,
	}
}

// NewExtendedMongoServerConfig creates an extended MongoDB server configuration with BaseURL
func NewExtendedMongoServerConfig(serviceName, baseURL string) *ExtendedMongoServerBaseConfig {
	baseConfig := LoadMongoBaseConfig(serviceName)
	return &ExtendedMongoServerBaseConfig{
		MongoBaseServiceConfig: *baseConfig,
		BaseURL:               baseURL,
	}
}

// NewBaseServiceConfig creates a standardized base configuration for simple services
// serviceName: The service name used for database prefix (e.g., "TEAM", "USER")
// portEnv: The environment variable name for port (e.g., "PORT", "SERVER_PORT")
// hostEnv: The environment variable name for host (e.g., "HOST", "SERVER_HOST")
func NewBaseServiceConfig(serviceName, portEnv, hostEnv string) *BaseServiceConfig {
	return &BaseServiceConfig{
		Server:   NewServerConfig(portEnv, hostEnv, "8080", "0.0.0.0"),
		Database: NewDatabaseConfig(serviceName),
		Redis:    NewRedisConfig(),
		Services: NewServiceRegistry(),
	}
}

// NewStandardServiceConfig creates a base configuration using standardized environment variable names
// This uses "SERVER_PORT" and "SERVER_HOST" as the standard port/host environment variables
func NewStandardServiceConfig(serviceName string) *BaseServiceConfig {
	return NewBaseServiceConfig(serviceName, "SERVER_PORT", "SERVER_HOST")
}

// NewLegacyServiceConfig creates a base configuration using legacy environment variable names
// This uses "PORT" and "HOST" for backward compatibility
func NewLegacyServiceConfig(serviceName string) *BaseServiceConfig {
	return NewBaseServiceConfig(serviceName, "PORT", "HOST")
}

// NewMongoServiceConfig creates a standardized MongoDB service configuration
func NewMongoServiceConfig(serviceName, portEnv, hostEnv string) *MongoBaseServiceConfig {
	return &MongoBaseServiceConfig{
		Server:   NewServerConfig(portEnv, hostEnv, "8080", "0.0.0.0"),
		Database: NewMongoConfig(),
		Redis:    NewRedisConfig(),
		Services: NewServiceRegistry(),
	}
}

// NewStandardMongoServiceConfig creates a MongoDB configuration using standardized environment variables
func NewStandardMongoServiceConfig(serviceName string) *MongoBaseServiceConfig {
	return NewMongoServiceConfig(serviceName, "SERVER_PORT", "SERVER_HOST")
}

// LoadBaseConfig provides a standard LoadConfig implementation for simple services
// This can be used directly or as a base for more complex configurations
// Uses the standardized SERVER_PORT and SERVER_HOST environment variables
func LoadBaseConfig(serviceName string) *BaseServiceConfig {
	LoadEnvFile()
	return NewStandardServiceConfig(serviceName)
}

// LoadMongoBaseConfig provides a standard LoadConfig implementation for MongoDB services
func LoadMongoBaseConfig(serviceName string) *MongoBaseServiceConfig {
	LoadEnvFile()
	return NewStandardMongoServiceConfig(serviceName)
}