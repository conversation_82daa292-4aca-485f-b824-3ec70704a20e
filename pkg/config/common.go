package config

import (
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Common configuration structures used across all services

type ServerConfig struct {
	Port         string
	Host         string
	Version      string
	Environment  string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

type DatabaseConfig struct {
	Host            string
	Port            string
	Username        string
	Password        string
	Database        string
	SSLMode         string
	MaxIdleConns    int
	MaxOpenConns    int
	ConnMaxLifetime time.Duration
}

type RedisConfig struct {
	URL      string
	Password string
	DB       int
}


type MongoConfig struct {
	URI        string
	Database   string
	MaxPool    uint64
	MinPool    uint64
	MaxConnIdle time.Duration
}


// Environment helper functions

func LoadEnvFile() {
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}
}

func GetEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func GetIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.Atoi(value); err == nil {
			return intVal
		}
	}
	return defaultValue
}

func GetInt64Env(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intVal
		}
	}
	return defaultValue
}

func GetUint64Env(key string, defaultValue uint64) uint64 {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.ParseUint(value, 10, 64); err == nil {
			return intVal
		}
	}
	return defaultValue
}

func GetDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func GetBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolVal, err := strconv.ParseBool(value); err == nil {
			return boolVal
		}
	}
	return defaultValue
}

func GetEnvAsInt(key string, defaultValue int) int {
	return GetIntEnv(key, defaultValue)
}

func GetEnvAsInt64(key string, defaultValue int64) int64 {
	return GetInt64Env(key, defaultValue)
}

func GetEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	return GetDurationEnv(key, defaultValue)
}

func GetEnvAsBool(key string, defaultValue bool) bool {
	return GetBoolEnv(key, defaultValue)
}

func GetEnvAsFloat64(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
			return floatVal
		}
	}
	return defaultValue
}

func GetBrokersEnv(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// Common configuration builders

func NewServerConfig(portEnv, hostEnv string, defaultPort, defaultHost string) ServerConfig {
	return ServerConfig{
		Port:         GetEnv(portEnv, defaultPort),
		Host:         GetEnv(hostEnv, defaultHost),
		Version:      GetEnv("APP_VERSION", "1.0.0"),
		Environment:  GetEnv("APP_ENV", "development"),
		ReadTimeout:  GetDurationEnv("READ_TIMEOUT", 15*time.Second),
		WriteTimeout: GetDurationEnv("WRITE_TIMEOUT", 15*time.Second),
	}
}

func NewDatabaseConfig(dbPrefix string) DatabaseConfig {
	return DatabaseConfig{
		Host:            GetEnv("DB_HOST", "localhost"),
		Port:            GetEnv("DB_PORT", "5432"),
		Username:        GetEnv("DB_USERNAME", ""),
		Password:        GetEnv("DB_PASSWORD", ""),
		Database:        GetEnv(dbPrefix+"_DB_DATABASE", ""),
		SSLMode:         GetEnv("DB_SSL_MODE", "disable"),
		MaxIdleConns:    GetIntEnv("DB_MAX_IDLE_CONNS", 10),
		MaxOpenConns:    GetIntEnv("DB_MAX_OPEN_CONNS", 100),
		ConnMaxLifetime: GetDurationEnv("DB_CONN_MAX_LIFETIME", time.Hour),
	}
}

func NewRedisConfig() RedisConfig {
	return RedisConfig{
		URL:      GetEnv("REDIS_URL", "redis://localhost:6379"),
		Password: GetEnv("REDIS_PASSWORD", ""),
		DB:       GetIntEnv("REDIS_DB", 0),
	}
}


func NewMongoConfig() MongoConfig {
	return MongoConfig{
		URI:         GetEnv("MONGODB_URI", "mongodb://localhost:27017"),
		Database:    GetEnv("MONGO_DATABASE", ""),
		MaxPool:     GetUint64Env("MONGO_MAX_POOL", 100),
		MinPool:     GetUint64Env("MONGO_MIN_POOL", 10),
		MaxConnIdle: GetDurationEnv("MONGO_MAX_CONN_IDLE", 30*time.Second),
	}
}

