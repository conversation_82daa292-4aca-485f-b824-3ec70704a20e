package clients

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

type BaseServiceClient struct {
	httpClient *http.Client
	baseURL    string
	cache      *redis.Client
	timeout    time.Duration
}

type CacheConfig struct {
	TTL     time.Duration
	Enabled bool
}

type ServiceError struct {
	StatusCode int    `json:"status_code"`
	Message    string `json:"message"`
	Service    string `json:"service"`
	Body       string `json:"body,omitempty"`
}

func (e *ServiceError) Error() string {
	if e.Service != "" {
		return fmt.Sprintf("%s service error %d: %s", e.Service, e.StatusCode, e.Message)
	}
	return fmt.Sprintf("service error %d: %s", e.StatusCode, e.Message)
}

// ServiceResponse represents a standard API response
type ServiceResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

func NewBaseServiceClient(baseURL string, cache *redis.Client) *BaseServiceClient {
	return &BaseServiceClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		baseURL: strings.TrimSuffix(baseURL, "/"),
		cache:   cache,
		timeout: 30 * time.Second,
	}
}

func (c *BaseServiceClient) Get(ctx context.Context, path string, result interface{}) error {
	url := fmt.Sprintf("%s%s", c.baseURL, path)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return &ServiceError{
			StatusCode: resp.StatusCode,
			Message:    fmt.Sprintf("HTTP %d", resp.StatusCode),
			Service:    extractServiceFromURL(url),
		}
	}

	if err := json.NewDecoder(resp.Body).Decode(result); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	return nil
}

func (c *BaseServiceClient) GetFromCacheOrService(ctx context.Context, cacheKey, path string, result interface{}, config CacheConfig) error {
	if config.Enabled && c.cache != nil {
		cached := c.cache.Get(ctx, cacheKey).Val()
		if cached != "" {
			if err := json.Unmarshal([]byte(cached), result); err == nil {
				return nil
			}
		}
	}

	if err := c.Get(ctx, path, result); err != nil {
		return err
	}

	if config.Enabled && c.cache != nil {
		data, _ := json.Marshal(result)
		c.cache.Set(ctx, cacheKey, data, config.TTL)
	}

	return nil
}

func (c *BaseServiceClient) Post(ctx context.Context, path string, body, result interface{}) error {
	return c.PostWithHeaders(ctx, path, body, result, nil)
}

func (c *BaseServiceClient) GetWithHeaders(ctx context.Context, path string, result interface{}, headers map[string]string, config CacheConfig) error {
	// For requests with headers, we don't use cache (usually auth-specific)
	url := fmt.Sprintf("%s%s", c.baseURL, path)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Add custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return &ServiceError{
			StatusCode: resp.StatusCode,
			Message:    fmt.Sprintf("HTTP %d", resp.StatusCode),
			Service:    extractServiceFromURL(url),
		}
	}

	if err := json.NewDecoder(resp.Body).Decode(result); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	return nil
}

func (c *BaseServiceClient) PostWithHeaders(ctx context.Context, path string, body, result interface{}, headers map[string]string) error {
	url := fmt.Sprintf("%s%s", c.baseURL, path)

	var bodyReader *strings.Reader
	if body != nil {
		data, err := json.Marshal(body)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = strings.NewReader(string(data))
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bodyReader)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Add custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return &ServiceError{
			StatusCode: resp.StatusCode,
			Message:    fmt.Sprintf("HTTP %d", resp.StatusCode),
			Service:    extractServiceFromURL(url),
		}
	}

	if result != nil {
		if err := json.NewDecoder(resp.Body).Decode(result); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

// extractServiceFromURL extracts service name from URL for better error reporting
func extractServiceFromURL(url string) string {
	if strings.Contains(url, "auth-service") || strings.Contains(url, ":8001") {
		return "auth"
	}
	if strings.Contains(url, "user-service") || strings.Contains(url, ":8002") {
		return "user"
	}
	if strings.Contains(url, "social-service") || strings.Contains(url, ":8003") {
		return "social"
	}
	if strings.Contains(url, "team-service") || strings.Contains(url, ":8004") {
		return "team"
	}
	if strings.Contains(url, "drive-service") || strings.Contains(url, ":8006") {
		return "drive"
	}
	if strings.Contains(url, "calendar-service") || strings.Contains(url, ":8007") {
		return "calendar"
	}
	if strings.Contains(url, "notification-service") || strings.Contains(url, ":8008") {
		return "notification"
	}
	return "unknown"
}
