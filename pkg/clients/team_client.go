package clients

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

type TeamServiceClient interface {
	GetTeam(ctx context.Context, teamID string) (*Team, error)
	GetTeams(ctx context.Context, teamIDs []string) ([]*Team, error)
	GetTeamMembers(ctx context.Context, teamID string) ([]*TeamMember, error)
	GetUserTeams(ctx context.Context, userID string) ([]*Team, error)
}

type CachedTeamServiceClient struct {
	*BaseServiceClient
	config CacheConfig
}

func NewTeamServiceClient(baseURL string, cache *redis.Client) TeamServiceClient {
	return &CachedTeamServiceClient{
		BaseServiceClient: NewBaseServiceClient(baseURL, cache),
		config: CacheConfig{
			TTL:     15 * time.Minute, // Teams change less frequently than users
			Enabled: true,
		},
	}
}

func (c *CachedTeamServiceClient) GetTeam(ctx context.Context, teamID string) (*Team, error) {
	cacheKey := fmt.Sprintf("team:%s", teamID)
	path := fmt.Sprintf("/teams/%s", teamID)
	
	var response struct {
		Success bool `json:"success"`
		Message string `json:"message"`
		Data    struct {
			Team *Team `json:"team"`
		} `json:"data"`
	}
	err := c.GetFromCacheOrService(ctx, cacheKey, path, &response, c.config)
	if err != nil {
		return nil, fmt.Errorf("failed to get team %s: %w", teamID, err)
	}
	
	if !response.Success {
		return nil, fmt.Errorf("team service error: %s", response.Message)
	}
	
	return response.Data.Team, nil
}

func (c *CachedTeamServiceClient) GetTeams(ctx context.Context, teamIDs []string) ([]*Team, error) {
	if len(teamIDs) == 0 {
		return []*Team{}, nil
	}
	
	if len(teamIDs) == 1 {
		team, err := c.GetTeam(ctx, teamIDs[0])
		if err != nil {
			return nil, err
		}
		return []*Team{team}, nil
	}
	
	// For multiple teams, use batch endpoint
	path := fmt.Sprintf("/teams/batch?ids=%s", strings.Join(teamIDs, ","))
	
	var response struct {
		Success bool `json:"success"`
		Message string `json:"message"`
		Data    struct {
			Teams []Team `json:"teams"`
			Count int    `json:"count"`
		} `json:"data"`
	}
	if err := c.Get(ctx, path, &response); err != nil {
		return nil, fmt.Errorf("failed to get teams: %w", err)
	}
	
	if !response.Success {
		return nil, fmt.Errorf("team service error: %s", response.Message)
	}
	
	// Convert to pointer slice
	teams := make([]*Team, len(response.Data.Teams))
	for i, team := range response.Data.Teams {
		teamCopy := team
		teams[i] = &teamCopy
		
		// Cache individual teams with proper error handling
		if c.config.Enabled && c.cache != nil {
			cacheKey := fmt.Sprintf("team:%s", team.ID.String())
			teamResp := TeamResponse{
				Success: true,
				Message: "Team retrieved successfully",
				Data: struct {
					Team *Team `json:"team"`
				}{
					Team: &teamCopy,
				},
			}
			
			// Cache asynchronously with timeout context to prevent leaks
			go func(key string, data TeamResponse) {
				defer func() {
					if r := recover(); r != nil {
						// Log panic but don't crash the application
						fmt.Printf("Cache operation panic: %v\n", r)
					}
				}()
				
				// Create timeout context for cache operation
				timeoutCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()
				
				c.cacheTeamResponseWithContext(timeoutCtx, key, data)
			}(cacheKey, teamResp)
		}
	}
	
	return teams, nil
}

func (c *CachedTeamServiceClient) GetTeamMembers(ctx context.Context, teamID string) ([]*TeamMember, error) {
	cacheKey := fmt.Sprintf("team_members:%s", teamID)
	path := fmt.Sprintf("/teams/%s/members", teamID)
	
	var response struct {
		Success bool `json:"success"`
		Message string `json:"message"`
		Data    struct {
			Members []TeamMember `json:"members"`
			Count   int          `json:"count"`
		} `json:"data"`
	}
	err := c.GetFromCacheOrService(ctx, cacheKey, path, &response, c.config)
	if err != nil {
		return nil, fmt.Errorf("failed to get team members for %s: %w", teamID, err)
	}
	
	if !response.Success {
		return nil, fmt.Errorf("team service error: %s", response.Message)
	}
	
	// Convert to pointer slice
	members := make([]*TeamMember, len(response.Data.Members))
	for i, member := range response.Data.Members {
		memberCopy := member
		members[i] = &memberCopy
	}
	
	return members, nil
}

func (c *CachedTeamServiceClient) GetUserTeams(ctx context.Context, userID string) ([]*Team, error) {
	cacheKey := fmt.Sprintf("user_teams:%s", userID)
	path := "/user/teams"
	
	var response struct {
		Success bool `json:"success"`
		Message string `json:"message"`
		Data    struct {
			Memberships []struct {
				TeamID string `json:"team_id"`
			} `json:"memberships"`
			PrimaryTeam *Team `json:"primary_team"`
		} `json:"data"`
	}
	
	err := c.GetFromCacheOrService(ctx, cacheKey, path, &response, c.config)
	if err != nil {
		return nil, fmt.Errorf("failed to get teams for user %s: %w", userID, err)
	}
	
	if !response.Success {
		return nil, fmt.Errorf("team service error: %s", response.Message)
	}
	
	// For now, return empty slice if no primary team (user has no teams)
	// TODO: In the future, we might want to fetch all teams from memberships
	var teams []*Team
	if response.Data.PrimaryTeam != nil {
		teamCopy := *response.Data.PrimaryTeam
		teams = []*Team{&teamCopy}
		
		// Cache individual teams with proper error handling
		if c.config.Enabled && c.cache != nil {
			teamCacheKey := fmt.Sprintf("team:%s", teamCopy.ID.String())
			teamResp := TeamResponse{
				Success: true,
				Message: "Team retrieved successfully",
				Data: struct {
					Team *Team `json:"team"`
				}{
					Team: &teamCopy,
				},
			}
			
			// Cache asynchronously with timeout context to prevent leaks
			go func(key string, data TeamResponse) {
				defer func() {
					if r := recover(); r != nil {
						// Log panic but don't crash the application
						fmt.Printf("Cache operation panic: %v\n", r)
					}
				}()
				
				// Create timeout context for cache operation
				timeoutCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()
				
				c.cacheTeamResponseWithContext(timeoutCtx, key, data)
			}(teamCacheKey, teamResp)
		}
	}
	
	return teams, nil
}

func (c *CachedTeamServiceClient) cacheTeamResponse(ctx context.Context, cacheKey string, response TeamResponse) {
	if data, err := c.serializeForCache(response); err == nil {
		c.cache.Set(ctx, cacheKey, data, c.config.TTL)
	}
}

func (c *CachedTeamServiceClient) cacheTeamResponseWithContext(ctx context.Context, cacheKey string, response TeamResponse) {
	if data, err := c.serializeForCache(response); err == nil {
		// Use the provided context which has timeout
		c.cache.Set(ctx, cacheKey, data, c.config.TTL)
	}
}

func (c *CachedTeamServiceClient) serializeForCache(data interface{}) (string, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

type TeamResponse struct {
	Success bool `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Team *Team `json:"team"`
	} `json:"data"`
}