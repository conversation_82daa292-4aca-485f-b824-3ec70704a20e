package clients

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	pkgconfig "github.com/swork-team/platform/pkg/config"
)

// UserServiceClient provides clean interface for user service communication
type UserServiceClient interface {
	// Registration flow
	CreateUserProfile(ctx context.Context, req CreateUserProfileRequest) error
	
	// User retrieval
	GetUser(ctx context.Context, userID string) (*User, error)
	GetUsers(ctx context.Context, userIDs []string) ([]*User, error)
	
	// Validation
	ValidateUsername(ctx context.Context, username string) error
	ValidateEmail(ctx context.Context, email string) error
	
	// Health
	HealthCheck(ctx context.Context) error
}

// StandardizedUserClient implements UserServiceClient using BaseServiceClient
type StandardizedUserClient struct {
	*BaseServiceClient
	timeouts pkgconfig.TimeoutConfig
	cacheTTL pkgconfig.CacheTTLConfig
}

// CreateUserProfileRequest represents the payload for creating a user profile
type CreateUserProfileRequest struct {
	UserID        uuid.UUID `json:"user_id" binding:"required"`
	Email         string    `json:"email,omitempty"`
	PhoneNumber   string    `json:"phone_number,omitempty"`
	FirstName     string    `json:"first_name" binding:"required"`
	LastName      string    `json:"last_name" binding:"required"`
	Username      string    `json:"username" binding:"required"`
	EmailVerified bool      `json:"email_verified"`
	PhoneVerified bool      `json:"phone_verified"`
}

// BatchUsersRequest represents a batch user request
type BatchUsersRequest struct {
	UserIDs []string `json:"user_ids" binding:"required"`
}

// ValidationRequest for username/email validation
type ValidationRequest struct {
	Value string `json:"value" binding:"required"`
}

// NewUserServiceClient creates a standardized user client
func NewUserServiceClient(baseURL string, cache *redis.Client) UserServiceClient {
	timeouts := pkgconfig.NewTimeoutConfig()
	cacheTTL := pkgconfig.NewCacheTTLConfig()
	
	client := &StandardizedUserClient{
		BaseServiceClient: NewBaseServiceClient(baseURL, cache),
		timeouts:         timeouts,
		cacheTTL:         cacheTTL,
	}
	
	// Configure HTTP timeout
	client.BaseServiceClient.timeout = timeouts.HTTP
	
	return client
}

// CreateUserProfile creates a new user profile in the user service
func (c *StandardizedUserClient) CreateUserProfile(ctx context.Context, req CreateUserProfileRequest) error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, c.timeouts.HTTP)
	defer cancel()
	
	// Prepare headers with service authentication
	headers := map[string]string{
		"Authorization": "Service " + os.Getenv("SERVICE_TOKEN"),
		"Content-Type":  "application/json",
	}
	
	var response ServiceResponse
	
	start := time.Now()
	err := c.PostWithHeaders(ctx, "/internal/users", req, &response, headers)
	duration := time.Since(start)
	
	// Log metrics for monitoring
	if err != nil {
		fmt.Printf("METRIC: user_profile_creation_failed user_id=%s duration=%v error=%v\n", 
			req.UserID, duration, err)
		
		// Check for specific error types
		if serviceErr, ok := err.(*ServiceError); ok {
			switch serviceErr.StatusCode {
			case 409:
				return fmt.Errorf("user profile creation conflict: %s", serviceErr.Message)
			case 503, 504:
				return fmt.Errorf("user service temporarily unavailable")
			default:
				return fmt.Errorf("user service error: %s", serviceErr.Message)
			}
		}
		
		return fmt.Errorf("failed to create user profile: %w", err)
	}
	
	if !response.Success {
		fmt.Printf("METRIC: user_profile_creation_rejected user_id=%s duration=%v message=%s\n", 
			req.UserID, duration, response.Message)
		return fmt.Errorf("user profile creation rejected: %s", response.Message)
	}
	
	fmt.Printf("METRIC: user_profile_creation_success user_id=%s duration=%v\n", 
		req.UserID, duration)
	
	return nil
}

// GetUser retrieves a single user by ID
func (c *StandardizedUserClient) GetUser(ctx context.Context, userID string) (*User, error) {
	headers := map[string]string{
		"Authorization": "Service " + os.Getenv("SERVICE_TOKEN"),
	}
	
	var response struct {
		Success bool  `json:"success"`
		Message string `json:"message"`
		Data    *User `json:"data"`
	}
	
	cacheConfig := CacheConfig{
		Enabled: true,
		TTL:     c.cacheTTL.UserProfile,
	}
	
	path := fmt.Sprintf("/internal/users/%s", userID)
	err := c.GetWithHeaders(ctx, path, &response, headers, cacheConfig)
	if err != nil {
		if serviceErr, ok := err.(*ServiceError); ok && serviceErr.StatusCode == 404 {
			return nil, nil // User not found
		}
		return nil, fmt.Errorf("failed to get user %s: %w", userID, err)
	}
	
	if !response.Success {
		return nil, fmt.Errorf("user service error: %s", response.Message)
	}
	
	return response.Data, nil
}

// GetUsers retrieves multiple users by IDs (batch operation)
func (c *StandardizedUserClient) GetUsers(ctx context.Context, userIDs []string) ([]*User, error) {
	if len(userIDs) == 0 {
		return []*User{}, nil
	}
	
	if len(userIDs) > 50 {
		return nil, fmt.Errorf("too many user IDs requested, maximum 50 allowed")
	}
	
	headers := map[string]string{
		"Authorization": "Service " + os.Getenv("SERVICE_TOKEN"),
		"Content-Type":  "application/json",
	}
	
	requestBody := BatchUsersRequest{
		UserIDs: userIDs,
	}
	
	var response struct {
		Success bool    `json:"success"`
		Message string  `json:"message"`
		Data    []*User `json:"data"`
	}
	
	err := c.PostWithHeaders(ctx, "/internal/users/batch", requestBody, &response, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}
	
	if !response.Success {
		return nil, fmt.Errorf("user service error: %s", response.Message)
	}
	
	return response.Data, nil
}

// ValidateUsername checks if a username is available
func (c *StandardizedUserClient) ValidateUsername(ctx context.Context, username string) error {
	headers := map[string]string{
		"Authorization": "Service " + os.Getenv("SERVICE_TOKEN"),
		"Content-Type":  "application/json",
	}
	
	requestBody := ValidationRequest{
		Value: username,
	}
	
	var response ServiceResponse
	
	err := c.PostWithHeaders(ctx, "/internal/users/validate/username", requestBody, &response, headers)
	if err != nil {
		return fmt.Errorf("failed to validate username: %w", err)
	}
	
	if !response.Success {
		return fmt.Errorf("username validation failed: %s", response.Message)
	}
	
	return nil
}

// ValidateEmail checks if an email is available
func (c *StandardizedUserClient) ValidateEmail(ctx context.Context, email string) error {
	if email == "" {
		return nil // Empty email is valid (optional field)
	}
	
	headers := map[string]string{
		"Authorization": "Service " + os.Getenv("SERVICE_TOKEN"),
		"Content-Type":  "application/json",
	}
	
	requestBody := ValidationRequest{
		Value: email,
	}
	
	var response ServiceResponse
	
	err := c.PostWithHeaders(ctx, "/internal/users/validate/email", requestBody, &response, headers)
	if err != nil {
		return fmt.Errorf("failed to validate email: %w", err)
	}
	
	if !response.Success {
		return fmt.Errorf("email validation failed: %s", response.Message)
	}
	
	return nil
}

// HealthCheck verifies if the user service is healthy
func (c *StandardizedUserClient) HealthCheck(ctx context.Context) error {
	var response struct {
		Status string `json:"status"`
	}
	
	err := c.Get(ctx, "/health", &response)
	if err != nil {
		return fmt.Errorf("user service health check failed: %w", err)
	}
	
	return nil
}