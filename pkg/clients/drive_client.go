package clients

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	sharedModels "github.com/swork-team/platform/pkg/models"
)

type DriveServiceClient struct {
	*BaseServiceClient
}

func NewDriveServiceClient(baseURL string, cache *redis.Client) DriveServiceClient {
	return DriveServiceClient{
		BaseServiceClient: NewBaseServiceClient(baseURL, cache),
	}
}

func (c *DriveServiceClient) GetAttachments(ctx context.Context, attachmentIDs []uuid.UUID) ([]sharedModels.AttachmentBasic, error) {
	if len(attachmentIDs) == 0 {
		return []sharedModels.AttachmentBasic{}, nil
	}

	stringIDs := make([]string, len(attachmentIDs))
	for i, id := range attachmentIDs {
		stringIDs[i] = id.String()
	}

	var response ServiceResponse
	path := fmt.Sprintf("/drive/attachments/batch?ids=%s", strings.Join(stringIDs, ","))

	err := c.Get(ctx, path, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get attachments: %w", err)
	}

	var attachments []sharedModels.AttachmentBasic
	if data, ok := response.Data.([]interface{}); ok {
		for _, item := range data {
			if attachmentData, ok := item.(map[string]interface{}); ok {
				var attachment sharedModels.AttachmentBasic
				
				if id, ok := attachmentData["id"].(string); ok {
					if uuid, err := uuid.Parse(id); err == nil {
						attachment.ID = uuid
					}
				}
				if attachmentType, ok := attachmentData["type"].(string); ok {
					attachment.Type = sharedModels.AttachmentType(attachmentType)
				}
				if name, ok := attachmentData["name"].(string); ok {
					attachment.Name = name
				}
				if url, ok := attachmentData["url"].(string); ok {
					attachment.URL = url
				}
				if thumbnailURL, ok := attachmentData["thumbnail_url"].(string); ok {
					attachment.ThumbnailURL = thumbnailURL
				}
				if size, ok := attachmentData["size"].(float64); ok {
					attachment.Size = int64(size)
				}
				if mimeType, ok := attachmentData["mime_type"].(string); ok {
					attachment.MimeType = mimeType
				}
				
				attachments = append(attachments, attachment)
			}
		}
	}

	return attachments, nil
}

func (c *DriveServiceClient) GetAttachment(ctx context.Context, attachmentID uuid.UUID) (*sharedModels.AttachmentBasic, error) {
	var response ServiceResponse
	path := fmt.Sprintf("/drive/attachments/%s", attachmentID.String())

	err := c.Get(ctx, path, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get attachment: %w", err)
	}

	if data, ok := response.Data.(map[string]interface{}); ok {
		var attachment sharedModels.AttachmentBasic
		
		if id, ok := data["id"].(string); ok {
			if uuid, err := uuid.Parse(id); err == nil {
				attachment.ID = uuid
			}
		}
		if attachmentType, ok := data["type"].(string); ok {
			attachment.Type = sharedModels.AttachmentType(attachmentType)
		}
		if name, ok := data["name"].(string); ok {
			attachment.Name = name
		}
		if url, ok := data["url"].(string); ok {
			attachment.URL = url
		}
		if thumbnailURL, ok := data["thumbnail_url"].(string); ok {
			attachment.ThumbnailURL = thumbnailURL
		}
		if size, ok := data["size"].(float64); ok {
			attachment.Size = int64(size)
		}
		if mimeType, ok := data["mime_type"].(string); ok {
			attachment.MimeType = mimeType
		}

		return &attachment, nil
	}

	return nil, fmt.Errorf("invalid attachment data format")
}

func (c *DriveServiceClient) ValidateAttachments(ctx context.Context, attachmentIDs []uuid.UUID, userID string) error {
	if len(attachmentIDs) == 0 {
		return nil
	}

	stringIDs := make([]string, len(attachmentIDs))
	for i, id := range attachmentIDs {
		stringIDs[i] = id.String()
	}

	var response ServiceResponse
	path := fmt.Sprintf("/drive/attachments/validate?ids=%s&user_id=%s", strings.Join(stringIDs, ","), userID)

	err := c.Get(ctx, path, &response)
	if err != nil {
		return fmt.Errorf("failed to validate attachments: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("attachment validation failed: %s", response.Error)
	}

	return nil
}

// Root folder types
type RootFolderFile struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Size      int64  `json:"size"`
	MimeType  string `json:"mimeType"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

type RootFolderFolder struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Path        string `json:"path"`
	Visibility  string `json:"visibility"`
	Description string `json:"description"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

type RootFolderResponse struct {
	Files        []RootFolderFile   `json:"files"`
	Folders      []RootFolderFolder `json:"folders"`
	TotalFiles   int64              `json:"totalFiles"`
	TotalFolders int64              `json:"totalFolders"`
	Offset       int                `json:"offset"`
	Limit        int                `json:"limit"`
}

type GetRootFolderOptions struct {
	TeamID *uuid.UUID
	Offset int
	Limit  int
}

// GetRootFolder retrieves root-level files and folders for the user
func (c *DriveServiceClient) GetRootFolder(ctx context.Context, options *GetRootFolderOptions) (*RootFolderResponse, error) {
	var response ServiceResponse
	
	// Build query parameters
	params := []string{}
	
	if options != nil {
		if options.TeamID != nil {
			params = append(params, "teamId="+options.TeamID.String())
		}
		if options.Offset > 0 {
			params = append(params, "offset="+strconv.Itoa(options.Offset))
		}
		if options.Limit > 0 {
			params = append(params, "limit="+strconv.Itoa(options.Limit))
		}
	}
	
	path := "/api/v1/drive/root"
	if len(params) > 0 {
		path += "?" + strings.Join(params, "&")
	}

	err := c.Get(ctx, path, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get root folder: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("get root folder failed: %s", response.Error)
	}

	// Parse the response data
	data, ok := response.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid root folder response format")
	}

	result := &RootFolderResponse{}

	// Parse files
	if filesData, ok := data["files"].([]interface{}); ok {
		for _, fileItem := range filesData {
			if fileData, ok := fileItem.(map[string]interface{}); ok {
				file := RootFolderFile{}
				if id, ok := fileData["id"].(string); ok {
					file.ID = id
				}
				if name, ok := fileData["name"].(string); ok {
					file.Name = name
				}
				if size, ok := fileData["size"].(float64); ok {
					file.Size = int64(size)
				}
				if mimeType, ok := fileData["mimeType"].(string); ok {
					file.MimeType = mimeType
				}
				if createdAt, ok := fileData["createdAt"].(string); ok {
					file.CreatedAt = createdAt
				}
				if updatedAt, ok := fileData["updatedAt"].(string); ok {
					file.UpdatedAt = updatedAt
				}
				result.Files = append(result.Files, file)
			}
		}
	}

	// Parse folders
	if foldersData, ok := data["folders"].([]interface{}); ok {
		for _, folderItem := range foldersData {
			if folderData, ok := folderItem.(map[string]interface{}); ok {
				folder := RootFolderFolder{}
				if id, ok := folderData["id"].(string); ok {
					folder.ID = id
				}
				if name, ok := folderData["name"].(string); ok {
					folder.Name = name
				}
				if path, ok := folderData["path"].(string); ok {
					folder.Path = path
				}
				if visibility, ok := folderData["visibility"].(string); ok {
					folder.Visibility = visibility
				}
				if description, ok := folderData["description"].(string); ok {
					folder.Description = description
				}
				if createdAt, ok := folderData["createdAt"].(string); ok {
					folder.CreatedAt = createdAt
				}
				if updatedAt, ok := folderData["updatedAt"].(string); ok {
					folder.UpdatedAt = updatedAt
				}
				result.Folders = append(result.Folders, folder)
			}
		}
	}

	// Parse metadata
	if totalFiles, ok := data["totalFiles"].(float64); ok {
		result.TotalFiles = int64(totalFiles)
	}
	if totalFolders, ok := data["totalFolders"].(float64); ok {
		result.TotalFolders = int64(totalFolders)
	}
	if offset, ok := data["offset"].(float64); ok {
		result.Offset = int(offset)
	}
	if limit, ok := data["limit"].(float64); ok {
		result.Limit = int(limit)
	}

	return result, nil
}