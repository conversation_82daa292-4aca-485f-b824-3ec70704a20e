package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
)

type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type PaginatedResponse struct {
	APIResponse
	Meta PaginationMeta `json:"meta"`
}

type PaginationMeta struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

func SuccessResponse(c *gin.Context, status int, message string, data interface{}) {
	c.JSON(status, APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	})
}

func ErrorResponse(c *gin.Context, status int, message string, err error) {
	response := APIResponse{
		Success: false,
		Message: message,
	}

	if err != nil {
		response.Error = err.Error()
	}

	c.<PERSON>(status, response)
}

func PaginatedSuccessResponse(c *gin.Context, data interface{}, meta PaginationMeta) {
	c.JSON(http.StatusOK, PaginatedResponse{
		APIResponse: APIResponse{
			Success: true,
			Data:    data,
		},
		Meta: meta,
	})
}

// Enhanced response functions with logging

// SuccessResponseWithLog logs the operation success and sends response
func SuccessResponseWithLog(c *gin.Context, status int, message string, data interface{}, operation string, fields ...logger.Field) {
	// Log success
	loggerInstance := logger.GetLoggerFromContext(c)
	allFields := append([]logger.Field{logger.F("operation", operation)}, fields...)
	loggerInstance.Info("Operation completed successfully", allFields...)
	
	// Send response
	SuccessResponse(c, status, message, data)
}

// ErrorResponseWithLog logs the operation error and sends response
func ErrorResponseWithLog(c *gin.Context, status int, message string, err error, operation string, fields ...logger.Field) {
	// Log error
	loggerInstance := logger.GetLoggerFromContext(c)
	allFields := append([]logger.Field{logger.F("operation", operation)}, fields...)
	loggerInstance.WithError(err).Error("Operation failed", allFields...)
	
	// Send response
	ErrorResponse(c, status, message, err)
}

// LogOperation logs the start of an operation (for operations that need start logging)
func LogOperation(c *gin.Context, operation string, fields ...logger.Field) {
	loggerInstance := logger.GetLoggerFromContext(c)
	allFields := append([]logger.Field{logger.F("operation", operation)}, fields...)
	loggerInstance.Info("Operation started", allFields...)
}

// LogSecurityEvent logs security-related events
func LogSecurityEvent(c *gin.Context, event string, success bool, fields ...logger.Field) {
	loggerInstance := logger.GetLoggerFromContext(c)
	allFields := append([]logger.Field{
		logger.F("security_event", event),
		logger.F("success", success),
		logger.F("client_ip", c.ClientIP()),
		logger.F("user_agent", c.Request.UserAgent()),
	}, fields...)
	
	if success {
		loggerInstance.Info("Security event", allFields...)
	} else {
		loggerInstance.Warn("Security event failed", allFields...)
	}
}