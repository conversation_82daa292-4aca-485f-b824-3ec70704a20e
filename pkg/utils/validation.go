package utils

import (
	"crypto/rand"
	"fmt"
	"html"
	"math/big"
	"net/url"
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"
)

var (
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	phoneRegex = regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
)

func IsValidEmail(email string) bool {
	return emailRegex.MatchString(email)
}

func IsValidPassword(password string) bool {
	if len(password) < 8 {
		return false
	}

	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	return hasUpper && hasLower && hasNumber && hasSpecial
}

func IsValidUsername(username string) bool {
	// Allow 9-digit numeric usernames (auto-generated)
	if len(username) == 9 {
		numericRegex := regexp.MustCompile(`^\d{9}$`)
		if numericRegex.MatchString(username) {
			return true
		}
	}

	// Traditional username validation (3-30 characters, alphanumeric + underscores)
	if len(username) < 3 || len(username) > 30 {
		return false
	}

	usernameRegex := regexp.MustCompile(`^[a-zA-Z0-9_]+$`)
	return usernameRegex.MatchString(username)
}

func IsValidPhoneNumber(phone string) bool {
	return phoneRegex.MatchString(phone)
}

func IsEmailOrPhone(contact string) string {
	if IsValidEmail(contact) {
		return "email"
	}
	if IsValidPhoneNumber(contact) {
		return "phone"
	}
	return ""
}

// Input sanitization functions

// SanitizeString removes dangerous characters and normalizes input
func SanitizeString(input string) string {
	// Trim whitespace
	sanitized := strings.TrimSpace(input)

	// Escape HTML to prevent XSS
	sanitized = html.EscapeString(sanitized)

	return sanitized
}

// SanitizeHTML removes script tags and dangerous HTML
func SanitizeHTML(input string) string {
	// Remove script tags
	scriptRegex := regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	sanitized := scriptRegex.ReplaceAllString(input, "")

	// Remove on* event handlers
	eventRegex := regexp.MustCompile(`(?i)\s*on\w+\s*=\s*["'][^"']*["']`)
	sanitized = eventRegex.ReplaceAllString(sanitized, "")

	// Remove javascript: URLs
	jsRegex := regexp.MustCompile(`(?i)javascript:`)
	sanitized = jsRegex.ReplaceAllString(sanitized, "")

	return strings.TrimSpace(sanitized)
}

// ValidateURL checks if URL is valid and safe
func ValidateURL(urlStr string) bool {
	if urlStr == "" {
		return true // Empty URL is allowed
	}

	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return false
	}

	// Only allow http and https schemes
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return false
	}

	return true
}

// ValidateStringLength checks if string length is within bounds
func ValidateStringLength(str string, min, max int) bool {
	length := utf8.RuneCountInString(str)
	return length >= min && length <= max
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Error implements the error interface
func (v ValidationError) Error() string {
	return v.Message
}

// ValidationErrors is a slice of ValidationError that implements error interface
type ValidationErrors []ValidationError

// Error implements the error interface for ValidationErrors
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return "validation failed"
	}
	if len(ve) == 1 {
		return ve[0].Error()
	}
	return "multiple validation errors occurred"
}

// ValidationResult holds validation results
type ValidationResult struct {
	IsValid bool             `json:"is_valid"`
	Errors  ValidationErrors `json:"errors,omitempty"`
}

// ValidateRequiredString validates that a string field is not empty
func ValidateRequiredString(value, fieldName string) *ValidationError {
	if strings.TrimSpace(value) == "" {
		return &ValidationError{
			Field:   fieldName,
			Message: fieldName + " is required",
		}
	}
	return nil
}

// ValidateStringWithLength validates string length and returns validation error if invalid
func ValidateStringWithLength(value, fieldName string, min, max int, required bool) *ValidationError {
	if value == "" && !required {
		return nil
	}

	if value == "" && required {
		return &ValidationError{
			Field:   fieldName,
			Message: fieldName + " is required",
		}
	}

	if !ValidateStringLength(value, min, max) {
		return &ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("%s must be between %d and %d characters", fieldName, min, max),
		}
	}

	return nil
}

// ValidateOptionalURL validates URL if provided
func ValidateOptionalURL(url, fieldName string) *ValidationError {
	if url != "" && !ValidateURL(url) {
		return &ValidationError{
			Field:   fieldName,
			Message: "Invalid " + fieldName + " URL",
		}
	}
	return nil
}

// ValidateWithRegex validates string against regex pattern
func ValidateWithRegex(value, fieldName, pattern, errorMessage string) *ValidationError {
	if value == "" {
		return nil
	}

	regex := regexp.MustCompile(pattern)
	if !regex.MatchString(value) {
		return &ValidationError{
			Field:   fieldName,
			Message: errorMessage,
		}
	}

	return nil
}

// ValidateEnum validates that value is in allowed enum values
func ValidateEnum(value, fieldName string, allowedValues []string) *ValidationError {
	if value == "" {
		return nil
	}

	for _, allowed := range allowedValues {
		if value == allowed {
			return nil
		}
	}

	return &ValidationError{
		Field:   fieldName,
		Message: fmt.Sprintf("Invalid %s. Allowed values: %s", fieldName, strings.Join(allowedValues, ", ")),
	}
}

// ValidateNumericRange validates that a number is within specified range
func ValidateNumericRange(value int, fieldName string, min, max int) *ValidationError {
	if value < min || value > max {
		return &ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("%s must be between %d and %d", fieldName, min, max),
		}
	}
	return nil
}

// GenerateUniqueUsername generates a unique 9-digit numeric username
// This function generates a cryptographically secure random 9-digit number
// that can be used as a username. The number range is 100000000-999999999.
func GenerateUniqueUsername() (string, error) {
	// Generate a random number between 100000000 and 999999999 (9 digits)
	min := big.NewInt(100000000) // 100,000,000
	max := big.NewInt(999999999) // 999,999,999

	// Calculate the range
	rangeSize := new(big.Int).Sub(max, min)
	rangeSize.Add(rangeSize, big.NewInt(1)) // Add 1 to make it inclusive

	// Generate a random number in the range
	randomNum, err := rand.Int(rand.Reader, rangeSize)
	if err != nil {
		return "", fmt.Errorf("failed to generate random number: %w", err)
	}

	// Add the minimum value to get the final number
	result := new(big.Int).Add(randomNum, min)

	return result.String(), nil
}

// ValidationBuilder provides a fluent interface for building validation rules
type ValidationBuilder struct {
	errors ValidationErrors
}

// NewValidationBuilder creates a new validation builder
func NewValidationBuilder() *ValidationBuilder {
	return &ValidationBuilder{
		errors: make(ValidationErrors, 0),
	}
}

// ValidateRequired adds required field validation
func (vb *ValidationBuilder) ValidateRequired(value, fieldName string) *ValidationBuilder {
	if err := ValidateRequiredString(value, fieldName); err != nil {
		vb.errors = append(vb.errors, *err)
	}
	return vb
}

// ValidateLength adds string length validation
func (vb *ValidationBuilder) ValidateLength(value, fieldName string, min, max int, required bool) *ValidationBuilder {
	if err := ValidateStringWithLength(value, fieldName, min, max, required); err != nil {
		vb.errors = append(vb.errors, *err)
	}
	return vb
}

// ValidateURL adds URL validation
func (vb *ValidationBuilder) ValidateURL(url, fieldName string) *ValidationBuilder {
	if err := ValidateOptionalURL(url, fieldName); err != nil {
		vb.errors = append(vb.errors, *err)
	}
	return vb
}

// ValidateRegex adds regex validation
func (vb *ValidationBuilder) ValidateRegex(value, fieldName, pattern, errorMessage string) *ValidationBuilder {
	if err := ValidateWithRegex(value, fieldName, pattern, errorMessage); err != nil {
		vb.errors = append(vb.errors, *err)
	}
	return vb
}

// ValidateEnum adds enum validation
func (vb *ValidationBuilder) ValidateEnum(value, fieldName string, allowedValues []string) *ValidationBuilder {
	if err := ValidateEnum(value, fieldName, allowedValues); err != nil {
		vb.errors = append(vb.errors, *err)
	}
	return vb
}

// ValidateRange adds numeric range validation
func (vb *ValidationBuilder) ValidateRange(value int, fieldName string, min, max int) *ValidationBuilder {
	if err := ValidateNumericRange(value, fieldName, min, max); err != nil {
		vb.errors = append(vb.errors, *err)
	}
	return vb
}

// Build returns the validation result
func (vb *ValidationBuilder) Build() ValidationResult {
	return ValidationResult{
		IsValid: len(vb.errors) == 0,
		Errors:  vb.errors,
	}
}

// GetErrors returns the validation errors
func (vb *ValidationBuilder) GetErrors() ValidationErrors {
	return vb.errors
}
