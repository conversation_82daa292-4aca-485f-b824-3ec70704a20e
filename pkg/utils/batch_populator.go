package utils

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/clients"
)

// RequestCacheKey is the context key for request cache
type RequestCache<PERSON>ey struct{}

// BatchPopulator provides efficient batch data population utilities
type Batch<PERSON>opulator struct {
	userClient clients.UserServiceClient
	teamClient clients.TeamServiceClient
}

// NewBatchPopulator creates a new batch populator
func NewBatchPopulator(userClient clients.UserServiceClient, teamClient clients.TeamServiceClient) *BatchPopulator {
	return &BatchPopulator{
		userClient: userClient,
		teamClient: teamClient,
	}
}

// UserMap represents a map of user ID to user data
type UserMap map[string]*clients.User

// TeamMap represents a map of team ID to team data
type TeamMap map[string]*clients.Team

// PopulateUsers fetches users in batch and returns a map
func (bp *BatchPopulator) PopulateUsers(ctx context.Context, userIDs []string) (UserMap, error) {
	if len(userIDs) == 0 {
		return make(UserMap), nil
	}

	// Remove duplicates
	uniqueIDs := ExtractUniqueStrings(userIDs)
	
	// Check request cache first
	userMap := make(UserMap)
	var uncachedIDs []string
	
	// Try to get request cache from context
	if cache := getRequestCacheFromContext(ctx); cache != nil {
		for _, id := range uniqueIDs {
			if user := cache.GetCachedUser(id); user != nil {
				userMap[id] = user
			} else {
				uncachedIDs = append(uncachedIDs, id)
			}
		}
	} else {
		uncachedIDs = uniqueIDs
	}

	// Fetch uncached users in batch
	if len(uncachedIDs) > 0 {
		users, err := bp.userClient.GetUsers(ctx, uncachedIDs)
		if err != nil {
			// If user service fails (e.g., authentication issues), create fallback users for all uncached IDs
			fmt.Printf("Warning: Failed to fetch users from user service: %v. Using fallback users.\n", err)
			for _, id := range uncachedIDs {
				userUUID, _ := uuid.Parse(id)
				fallbackUser := &clients.User{
					ID:           userUUID,
					FirstName:    "Unknown",
					LastName:     "User",
					Username:     "unknown",
					ProfileImage: "",
				}
				userMap[id] = fallbackUser
			}
		} else {
			// Add fetched users to map and cache
			for _, user := range users {
				userMap[user.ID.String()] = user
			}

			// Add fallback users for missing ones
			for _, id := range uncachedIDs {
				if _, exists := userMap[id]; !exists {
					userUUID, _ := uuid.Parse(id)
					fallbackUser := &clients.User{
						ID:           userUUID,
						FirstName:    "Unknown",
						LastName:     "User",
						Username:     "unknown",
						ProfileImage: "",
					}
					userMap[id] = fallbackUser
				}
			}
		}

		// Cache the results for this request
		if cache := getRequestCacheFromContext(ctx); cache != nil {
			cache.CacheUsers(userMap)
		}
	}

	return userMap, nil
}

// PopulateTeams fetches teams in batch and returns a map
func (bp *BatchPopulator) PopulateTeams(ctx context.Context, teamIDs []string) (TeamMap, error) {
	if len(teamIDs) == 0 {
		return make(TeamMap), nil
	}

	// Remove duplicates
	uniqueIDs := ExtractUniqueStrings(teamIDs)
	
	// Check request cache first
	teamMap := make(TeamMap)
	var uncachedIDs []string
	
	// Try to get request cache from context
	if cache := getRequestCacheFromContext(ctx); cache != nil {
		for _, id := range uniqueIDs {
			if team := cache.GetCachedTeam(id); team != nil {
				teamMap[id] = team
			} else {
				uncachedIDs = append(uncachedIDs, id)
			}
		}
	} else {
		uncachedIDs = uniqueIDs
	}

	// Fetch uncached teams in batch
	if len(uncachedIDs) > 0 {
		teams, err := bp.teamClient.GetTeams(ctx, uncachedIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch teams: %w", err)
		}

		// Add fetched teams to map
		for _, team := range teams {
			teamMap[team.ID.String()] = team
		}

		// Cache the results for this request
		if cache := getRequestCacheFromContext(ctx); cache != nil {
			cache.CacheTeams(teamMap)
		}
	}

	return teamMap, nil
}


// RequestCache provides request-scoped caching to prevent duplicate API calls
type RequestCache struct {
	users map[string]*clients.User
	teams map[string]*clients.Team
}

// NewRequestCache creates a new request-scoped cache
func NewRequestCache() *RequestCache {
	return &RequestCache{
		users: make(map[string]*clients.User),
		teams: make(map[string]*clients.Team),
	}
}

// GetUser returns cached user or fetches if not cached
func (rc *RequestCache) GetUser(ctx context.Context, userClient clients.UserServiceClient, userID string) *clients.User {
	if user, exists := rc.users[userID]; exists {
		return user
	}
	
	user, err := userClient.GetUser(ctx, userID)
	if err != nil {
		// Return fallback user
		userUUID, _ := uuid.Parse(userID)
		user = &clients.User{
			ID:           userUUID,
			FirstName:    "Unknown",
			LastName:     "User",
			Username:     "unknown",
			ProfileImage: "",
		}
	}
	
	rc.users[userID] = user
	return user
}

// GetTeam returns cached team or fetches if not cached
func (rc *RequestCache) GetTeam(ctx context.Context, teamClient clients.TeamServiceClient, teamID string) *clients.Team {
	if team, exists := rc.teams[teamID]; exists {
		return team
	}
	
	team, err := teamClient.GetTeam(ctx, teamID)
	if err != nil {
		return nil
	}
	
	rc.teams[teamID] = team
	return team
}

// CacheUsers adds users to the cache
func (rc *RequestCache) CacheUsers(users UserMap) {
	for id, user := range users {
		rc.users[id] = user
	}
}

// CacheTeams adds teams to the cache
func (rc *RequestCache) CacheTeams(teams TeamMap) {
	for id, team := range teams {
		rc.teams[id] = team
	}
}

// GetCachedUser returns a cached user by ID
func (rc *RequestCache) GetCachedUser(userID string) *clients.User {
	return rc.users[userID]
}

// GetCachedTeam returns a cached team by ID
func (rc *RequestCache) GetCachedTeam(teamID string) *clients.Team {
	return rc.teams[teamID]
}

// Helper function to get request cache from context
func getRequestCacheFromContext(ctx context.Context) *RequestCache {
	// Use the RequestCacheKey to get request cache
	if cache, ok := ctx.Value(RequestCacheKey{}).(*RequestCache); ok {
		return cache
	}
	return nil
}