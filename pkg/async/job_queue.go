package async

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

// AsyncJobQueue provides Redis-based job queue functionality to replace Kafka events
type AsyncJobQueue struct {
	redis      *redis.Client
	queueName  string
	workers    int
	retryDelay time.Duration
	maxRetries int
}

// AsyncJob represents a job to be processed asynchronously
type AsyncJob struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	UserID    string                 `json:"user_id"`
	Data      map[string]interface{} `json:"data"`
	Attempts  int                    `json:"attempts"`
	MaxRetries int                   `json:"max_retries"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// JobProcessor defines the interface for processing different job types
type JobProcessor interface {
	ProcessJob(ctx context.Context, job *AsyncJob) error
}

// NewAsyncJobQueue creates a new async job queue
func NewAsyncJobQueue(redisClient *redis.Client, queueName string, workers int) *AsyncJobQueue {
	return &AsyncJobQueue{
		redis:      redisClient,
		queueName:  fmt.Sprintf("async_jobs:%s", queueName),
		workers:    workers,
		retryDelay: 30 * time.Second,
		maxRetries: 3,
	}
}

// EnqueueJob adds a job to the queue
func (aq *AsyncJobQueue) EnqueueJob(ctx context.Context, job *AsyncJob) error {
	// Set job metadata
	if job.ID == "" {
		job.ID = uuid.New().String()
	}
	if job.CreatedAt.IsZero() {
		job.CreatedAt = time.Now()
	}
	if job.MaxRetries == 0 {
		job.MaxRetries = aq.maxRetries
	}
	job.UpdatedAt = time.Now()
	
	data, err := json.Marshal(job)
	if err != nil {
		return fmt.Errorf("failed to marshal job: %w", err)
	}
	
	// Add to Redis list (FIFO queue)
	err = aq.redis.LPush(ctx, aq.queueName, data).Err()
	if err != nil {
		return fmt.Errorf("failed to enqueue job: %w", err)
	}
	
	log.Printf("Job ENQUEUED: id=%s, type=%s, user_id=%s", job.ID, job.Type, job.UserID)
	return nil
}

// StartWorkers starts the specified number of worker goroutines
func (aq *AsyncJobQueue) StartWorkers(ctx context.Context, processor JobProcessor) error {
	log.Printf("Starting %d async job workers for queue: %s", aq.workers, aq.queueName)
	
	for i := 0; i < aq.workers; i++ {
		go aq.worker(ctx, processor, i)
	}
	
	return nil
}

// worker processes jobs from the queue
func (aq *AsyncJobQueue) worker(ctx context.Context, processor JobProcessor, workerID int) {
	log.Printf("Worker %d started for queue: %s", workerID, aq.queueName)
	
	for {
		select {
		case <-ctx.Done():
			log.Printf("Worker %d stopping: %v", workerID, ctx.Err())
			return
		default:
			// Blocking pop from queue with timeout
			result, err := aq.redis.BRPop(ctx, 5*time.Second, aq.queueName).Result()
			if err != nil {
				if err == redis.Nil {
					// Timeout, no jobs available
					continue
				}
				log.Printf("Worker %d error: %v", workerID, err)
				continue
			}
			
			// Parse job
			var job AsyncJob
			if err := json.Unmarshal([]byte(result[1]), &job); err != nil {
				log.Printf("Worker %d failed to unmarshal job: %v", workerID, err)
				continue
			}
			
			// Process job
			if err := aq.processJob(ctx, processor, &job); err != nil {
				log.Printf("Worker %d job %s failed: %v", workerID, job.ID, err)
			}
		}
	}
}

// processJob handles individual job processing with retry logic
func (aq *AsyncJobQueue) processJob(ctx context.Context, processor JobProcessor, job *AsyncJob) error {
	log.Printf("Processing job: id=%s, type=%s, attempt=%d", job.ID, job.Type, job.Attempts+1)
	
	job.Attempts++
	job.UpdatedAt = time.Now()
	
	// Process the job
	err := processor.ProcessJob(ctx, job)
	if err != nil {
		log.Printf("Job %s failed (attempt %d/%d): %v", job.ID, job.Attempts, job.MaxRetries, err)
		
		// Retry if attempts remain
		if job.Attempts < job.MaxRetries {
			log.Printf("Retrying job %s after %v", job.ID, aq.retryDelay)
			
			// Wait before retry
			time.Sleep(aq.retryDelay)
			
			// Re-enqueue for retry
			if retryErr := aq.EnqueueJob(ctx, job); retryErr != nil {
				log.Printf("Failed to re-enqueue job %s for retry: %v", job.ID, retryErr)
			}
		} else {
			// Max retries exceeded, move to dead letter queue
			if dlqErr := aq.moveToDeadLetterQueue(ctx, job, err); dlqErr != nil {
				log.Printf("Failed to move job %s to dead letter queue: %v", job.ID, dlqErr)
			}
		}
		
		return err
	}
	
	log.Printf("Job completed successfully: id=%s, type=%s", job.ID, job.Type)
	return nil
}

// moveToDeadLetterQueue moves failed jobs to a dead letter queue for manual inspection
func (aq *AsyncJobQueue) moveToDeadLetterQueue(ctx context.Context, job *AsyncJob, processingError error) error {
	dlqName := fmt.Sprintf("%s:dlq", aq.queueName)
	
	// Add error information to job
	job.Data["processing_error"] = processingError.Error()
	job.Data["failed_at"] = time.Now().Format(time.RFC3339)
	
	data, err := json.Marshal(job)
	if err != nil {
		return fmt.Errorf("failed to marshal dead letter job: %w", err)
	}
	
	err = aq.redis.LPush(ctx, dlqName, data).Err()
	if err != nil {
		return fmt.Errorf("failed to add job to dead letter queue: %w", err)
	}
	
	log.Printf("Job moved to dead letter queue: id=%s, error=%v", job.ID, processingError)
	return nil
}

// GetQueueStats returns queue statistics
func (aq *AsyncJobQueue) GetQueueStats(ctx context.Context) (*QueueStats, error) {
	pipe := aq.redis.Pipeline()
	
	queueLenCmd := pipe.LLen(ctx, aq.queueName)
	dlqLenCmd := pipe.LLen(ctx, fmt.Sprintf("%s:dlq", aq.queueName))
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get queue stats: %w", err)
	}
	
	return &QueueStats{
		QueueName:       aq.queueName,
		PendingJobs:     queueLenCmd.Val(),
		DeadLetterJobs:  dlqLenCmd.Val(),
		Workers:         aq.workers,
		Timestamp:       time.Now(),
	}, nil
}

// QueueStats represents queue performance metrics
type QueueStats struct {
	QueueName      string    `json:"queue_name"`
	PendingJobs    int64     `json:"pending_jobs"`
	DeadLetterJobs int64     `json:"dead_letter_jobs"`
	Workers        int       `json:"workers"`
	Timestamp      time.Time `json:"timestamp"`
}

// PurgeQueue removes all jobs from the queue (use with caution)
func (aq *AsyncJobQueue) PurgeQueue(ctx context.Context) error {
	err := aq.redis.Del(ctx, aq.queueName).Err()
	if err != nil {
		return fmt.Errorf("failed to purge queue: %w", err)
	}
	
	log.Printf("Queue purged: %s", aq.queueName)
	return nil
}

// PurgeDeadLetterQueue removes all dead letter jobs
func (aq *AsyncJobQueue) PurgeDeadLetterQueue(ctx context.Context) error {
	dlqName := fmt.Sprintf("%s:dlq", aq.queueName)
	err := aq.redis.Del(ctx, dlqName).Err()
	if err != nil {
		return fmt.Errorf("failed to purge dead letter queue: %w", err)
	}
	
	log.Printf("Dead letter queue purged: %s", dlqName)
	return nil
}

// HealthCheck verifies the job queue is working
func (aq *AsyncJobQueue) HealthCheck(ctx context.Context) error {
	// Test Redis connection
	if err := aq.redis.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("redis connection failed: %w", err)
	}
	
	// Test queue operations
	testJob := &AsyncJob{
		Type:   "health_check",
		UserID: "test",
		Data:   map[string]interface{}{"test": true},
	}
	
	if err := aq.EnqueueJob(ctx, testJob); err != nil {
		return fmt.Errorf("failed to enqueue test job: %w", err)
	}
	
	// Clean up test job
	aq.redis.LPop(ctx, aq.queueName)
	
	return nil
}