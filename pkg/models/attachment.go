package models

import (
	"time"

	"github.com/google/uuid"
)

// Attachment represents the canonical attachment model shared across all services
// This is the single source of truth for attachment data structure
type Attachment struct {
	ID               uuid.UUID          `json:"id"`
	Type             AttachmentType     `json:"type"`
	Name             string             `json:"name"`
	OriginalName     string             `json:"original_name"`
	URL              string             `json:"url"`
	ThumbnailURL     string             `json:"thumbnail_url,omitempty"`
	Size             int64              `json:"size"`
	MimeType         string             `json:"mime_type"`
	Width            int                `json:"width,omitempty"`
	Height           int                `json:"height,omitempty"`
	Duration         int                `json:"duration,omitempty"`
	Checksum         string             `json:"checksum,omitempty"`
	ProcessingStatus ProcessingStatus   `json:"processing_status"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt        time.Time          `json:"created_at"`
	UpdatedAt        time.Time          `json:"updated_at"`
}

// AttachmentBasic represents a lightweight version of Attachment for service-to-service communication
type AttachmentBasic struct {
	ID           uuid.UUID      `json:"id"`
	Type         AttachmentType `json:"type"`
	Name         string         `json:"name"`
	URL          string         `json:"url"`
	ThumbnailURL string         `json:"thumbnail_url,omitempty"`
	Size         int64          `json:"size"`
	MimeType     string         `json:"mime_type"`
}

// ToBasic converts a full Attachment to AttachmentBasic
func (a *Attachment) ToBasic() *AttachmentBasic {
	return &AttachmentBasic{
		ID:           a.ID,
		Type:         a.Type,
		Name:         a.Name,
		URL:          a.URL,
		ThumbnailURL: a.ThumbnailURL,
		Size:         a.Size,
		MimeType:     a.MimeType,
	}
}

// AttachmentType enum for different types of attachments
type AttachmentType string

const (
	AttachmentTypeImage    AttachmentType = "image"
	AttachmentTypeVideo    AttachmentType = "video"
	AttachmentTypeAudio    AttachmentType = "audio"
	AttachmentTypeDocument AttachmentType = "document"
	AttachmentTypeFile     AttachmentType = "file"
	AttachmentTypeLink     AttachmentType = "link"
	AttachmentTypeLocation AttachmentType = "location"
	AttachmentTypeArchive  AttachmentType = "archive"
	AttachmentTypeOther    AttachmentType = "other"
)

// ProcessingStatus enum for attachment processing status
type ProcessingStatus string

const (
	ProcessingStatusPending    ProcessingStatus = "pending"
	ProcessingStatusProcessing ProcessingStatus = "processing"
	ProcessingStatusProcessed  ProcessingStatus = "processed"
	ProcessingStatusCompleted  ProcessingStatus = "completed"
	ProcessingStatusFailed     ProcessingStatus = "failed"
	ProcessingStatusSkipped    ProcessingStatus = "skipped"
)

// IsImage checks if the attachment is an image
func (a *Attachment) IsImage() bool {
	return a.Type == AttachmentTypeImage
}

// IsVideo checks if the attachment is a video
func (a *Attachment) IsVideo() bool {
	return a.Type == AttachmentTypeVideo
}

// IsAudio checks if the attachment is audio
func (a *Attachment) IsAudio() bool {
	return a.Type == AttachmentTypeAudio
}

// IsMedia checks if the attachment is media (image, video, or audio)
func (a *Attachment) IsMedia() bool {
	return a.IsImage() || a.IsVideo() || a.IsAudio()
}

// HasDimensions checks if the attachment has width/height dimensions
func (a *Attachment) HasDimensions() bool {
	return a.Width > 0 && a.Height > 0
}