package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents the canonical user model shared across all services
// This is the single source of truth for user data structure
type User struct {
	ID            uuid.UUID      `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	Email         string         `json:"email" gorm:"uniqueIndex;not null"`
	FirstName     string         `json:"first_name" gorm:"size:100;not null"`
	LastName      string         `json:"last_name" gorm:"size:100;not null"`
	Username      string         `json:"username" gorm:"uniqueIndex;size:50;not null"`
	ProfileImage  string         `json:"profile_image"`
	CoverImage    string         `json:"cover_image"`
	Bio           string         `json:"bio" gorm:"type:text"`
	Location      string         `json:"location" gorm:"size:100"`
	Website       string         `json:"website"`
	DateOfBirth   *time.Time     `json:"date_of_birth"`
	PhoneNumber   string         `json:"phone_number" gorm:"size:20"`
	PhoneVerified bool           `json:"phone_verified" gorm:"default:false"`
	Gender        string         `json:"gender" gorm:"size:20"`
	Timezone      string         `json:"timezone" gorm:"default:'UTC'"`
	Language      string         `json:"language" gorm:"default:'en'"`
	EmailVerified bool           `json:"email_verified" gorm:"default:false"`
	IsActive      bool           `json:"is_active" gorm:"default:true"`
	IsVerified    bool           `json:"is_verified" gorm:"default:false"`
	LastSeen      *time.Time     `json:"last_seen"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserBasic represents a lightweight version of User for service-to-service communication
// Used when full user details aren't needed
type UserBasic struct {
	ID           uuid.UUID `json:"id"`
	Username     string    `json:"username"`
	Email        string    `json:"email"`
	FirstName    string    `json:"first_name"`
	LastName     string    `json:"last_name"`
	ProfileImage string    `json:"profile_image"`
	IsActive     bool      `json:"is_active"`
	IsVerified   bool      `json:"is_verified"`
}

// ToBasic converts a full User to UserBasic
func (u *User) ToBasic() *UserBasic {
	return &UserBasic{
		ID:           u.ID,
		Username:     u.Username,
		Email:        u.Email,
		FirstName:    u.FirstName,
		LastName:     u.LastName,
		ProfileImage: u.ProfileImage,
		IsActive:     u.IsActive,
		IsVerified:   u.IsVerified,
	}
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	return u.FirstName + " " + u.LastName
}

// GetDisplayName returns username or full name for display
func (u *User) GetDisplayName() string {
	if u.Username != "" {
		return u.Username
	}
	return u.GetFullName()
}

// BeforeCreate hook for UUID generation
func (u *User) BeforeCreate(tx *gorm.DB) (err error) {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return
}

// TableName specifies the table name for GORM
func (User) TableName() string {
	return "users"
}