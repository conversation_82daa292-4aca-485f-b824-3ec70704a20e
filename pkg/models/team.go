package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Permissions represents a JSON array of permission strings
type Permissions []string

// Value implements driver.Valuer for database storage
func (p Permissions) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return json.Marshal(p)
}

// <PERSON>an implements sql.Scanner for database retrieval
func (p *Permissions) Scan(value interface{}) error {
	if value == nil {
		*p = nil
		return nil
	}
	
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into Permissions", value)
	}
	
	return json.Unmarshal(bytes, p)
}

// Team represents the canonical team model shared across all services
// This unifies the team structure and resolves IsPrivate vs Visibility conflicts
type Team struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primaryKey"`
	Name        string         `json:"name" gorm:"not null;size:100"`
	Slug        string         `json:"slug" gorm:"uniqueIndex;not null;size:100"`
	Description string         `json:"description" gorm:"size:500"`
	Avatar      string         `json:"avatar" gorm:"size:255"`
	Cover       string         `json:"cover" gorm:"size:255"`
	Website     string         `json:"website" gorm:"size:255"`
	Location    string         `json:"location" gorm:"size:100"`
	Category    string         `json:"category" gorm:"size:100;index"`
	
	// Settings - using enum instead of boolean for better extensibility
	Visibility  TeamVisibility `json:"visibility" gorm:"default:'private'"`
	JoinPolicy  JoinPolicy     `json:"join_policy" gorm:"default:'invite_only'"`
	MaxMembers  int            `json:"max_members" gorm:"default:1000"`
	
	// Metadata
	OwnerID     uuid.UUID      `json:"owner_id" gorm:"type:uuid;not null;index"`
	MemberCount int            `json:"member_count" gorm:"default:1"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	IsVerified  bool           `json:"is_verified" gorm:"default:false"`
	
	// Timestamps
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships (populated by GORM when needed)
	Members  []TeamMember  `json:"members,omitempty" gorm:"foreignKey:TeamID"`
	Settings *TeamSettings `json:"settings,omitempty" gorm:"foreignKey:TeamID"`
}

// TeamBasic represents a lightweight version of Team for service-to-service communication
type TeamBasic struct {
	ID          uuid.UUID      `json:"id"`
	Name        string         `json:"name"`
	Slug        string         `json:"slug"`
	Description string         `json:"description"`
	Avatar      string         `json:"avatar"`
	Visibility  TeamVisibility `json:"visibility"`
	OwnerID     uuid.UUID      `json:"owner_id"`
	MemberCount int            `json:"member_count"`
	IsActive    bool           `json:"is_active"`
	CreatedAt   time.Time      `json:"created_at"`
}

// TeamMember represents team membership
type TeamMember struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primaryKey"`
	TeamID      uuid.UUID      `json:"team_id" gorm:"type:uuid;not null;index"`
	UserID      uuid.UUID      `json:"user_id" gorm:"type:uuid;not null;index"`
	Role        TeamRole       `json:"role" gorm:"not null"`
	Permissions Permissions    `json:"permissions" gorm:"type:json"`
	Title       string         `json:"title" gorm:"size:100"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	IsPrimary   bool           `json:"is_primary" gorm:"default:false"`
	JoinedAt    time.Time      `json:"joined_at" gorm:"default:CURRENT_TIMESTAMP"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

// TeamSettings represents team configuration settings
type TeamSettings struct {
	ID                   uuid.UUID `json:"id" gorm:"type:uuid;primaryKey"`
	TeamID               uuid.UUID `json:"team_id" gorm:"type:uuid;not null;index"`
	AllowMemberInvites   bool      `json:"allow_member_invites" gorm:"default:true"`
	RequireApproval      bool      `json:"require_approval" gorm:"default:false"`
	ShowMemberList       bool      `json:"show_member_list" gorm:"default:true"`
	ShowMemberProfiles   bool      `json:"show_member_profiles" gorm:"default:true"`
	AllowDirectMessages  bool      `json:"allow_direct_messages" gorm:"default:true"`
	AllowPublicChannels  bool      `json:"allow_public_channels" gorm:"default:true"`
	AllowPrivateChannels bool      `json:"allow_private_channels" gorm:"default:true"`
	AllowFileSharing     bool      `json:"allow_file_sharing" gorm:"default:true"`
	AllowExternalLinks   bool      `json:"allow_external_links" gorm:"default:true"`
	ModerateContent      bool      `json:"moderate_content" gorm:"default:false"`
	EnableCalendar       bool      `json:"enable_calendar" gorm:"default:true"`
	EnableDrive          bool      `json:"enable_drive" gorm:"default:true"`
	EnableNotifications  bool      `json:"enable_notifications" gorm:"default:true"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

// BeforeCreate hook for UUID generation
func (ts *TeamSettings) BeforeCreate(tx *gorm.DB) (err error) {
	if ts.ID == uuid.Nil {
		ts.ID = uuid.New()
	}
	return
}

// TableName specifies the table name for GORM
func (TeamSettings) TableName() string {
	return "team_settings"
}

// Enums for team configuration
type TeamVisibility string

const (
	TeamVisibilityPublic     TeamVisibility = "public"
	TeamVisibilityPrivate    TeamVisibility = "private"
	TeamVisibilityRestricted TeamVisibility = "restricted"
)

type JoinPolicy string

const (
	JoinPolicyOpen       JoinPolicy = "open"
	JoinPolicyApproval   JoinPolicy = "approval"
	JoinPolicyInviteOnly JoinPolicy = "invite_only"
)

type TeamRole string

const (
	TeamRoleOwner  TeamRole = "owner"
	TeamRoleAdmin  TeamRole = "admin"
	TeamRoleMember TeamRole = "member"
)

// ToBasic converts a full Team to TeamBasic
func (t *Team) ToBasic() *TeamBasic {
	return &TeamBasic{
		ID:          t.ID,
		Name:        t.Name,
		Slug:        t.Slug,
		Description: t.Description,
		Avatar:      t.Avatar,
		Visibility:  t.Visibility,
		OwnerID:     t.OwnerID,
		MemberCount: t.MemberCount,
		IsActive:    t.IsActive,
		CreatedAt:   t.CreatedAt,
	}
}

// IsPublic returns true if team is publicly visible
func (t *Team) IsPublic() bool {
	return t.Visibility == TeamVisibilityPublic
}

// IsPrivate returns true if team is private (backward compatibility)
func (t *Team) IsPrivate() bool {
	return t.Visibility == TeamVisibilityPrivate
}

// CanUserView checks if a user can view this team
func (t *Team) CanUserView(userID uuid.UUID) bool {
	if t.Visibility == TeamVisibilityPublic {
		return true
	}
	return t.OwnerID == userID
}

// BeforeCreate hook for UUID generation
func (t *Team) BeforeCreate(tx *gorm.DB) (err error) {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
	return
}

func (tm *TeamMember) BeforeCreate(tx *gorm.DB) (err error) {
	if tm.ID == uuid.Nil {
		tm.ID = uuid.New()
	}
	return
}

// TableName specifies the table names for GORM
func (Team) TableName() string {
	return "teams"
}

func (TeamMember) TableName() string {
	return "team_members"
}