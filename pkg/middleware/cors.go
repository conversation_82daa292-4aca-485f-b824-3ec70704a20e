package middleware

import (
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

func CORSMiddleware() gin.HandlerFunc {
	// Check if we're in development mode
	isDevelopment := os.Getenv("GO_ENV") == "development" || 
					 os.Getenv("GIN_MODE") == "debug" || 
					 os.Getenv("NODE_ENV") == "development" || 
					 os.Getenv("APP_ENV") == "development"
	
	return func(c *gin.Context) {
		// In development mode, disable CORS completely for easier development
		if isDevelopment {
			origin := c.Request.Header.Get("Origin")
			if origin != "" {
				// Use the actual origin instead of wildcard when credentials are needed
				c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
			} else {
				// Fallback to wildcard only when no origin is present
				c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
			}
			c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
			c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-User-ID, X-Device-ID, X-Platform-ID, X-Request-ID, X-Timestamp, X-Client-Version, X-Client-Name")
			c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")
			c.Header("Access-Control-Max-Age", "86400")
			
			if c.Request.Method == "OPTIONS" {
				c.AbortWithStatus(http.StatusNoContent)
				return
			}
			
			c.Next()
			return
		}
		
		// Production mode: Use secure CORS configuration
		allowedOrigins := os.Getenv("CORS_ALLOWED_ORIGINS")
		if allowedOrigins == "" {
			// Default to secure localhost origins for development fallback
			allowedOrigins = "http://localhost:3000,http://localhost:3001,https://localhost:3000,https://localhost:3001"
		}
		
		origins := strings.Split(allowedOrigins, ",")
		for i, origin := range origins {
			origins[i] = strings.TrimSpace(origin)
		}

		origin := c.Request.Header.Get("Origin")
		
		// Check if the origin is in the allowed list
		allowed := false
		for _, allowedOrigin := range origins {
			if allowedOrigin == "*" || origin == allowedOrigin {
				allowed = true
				break
			}
		}
		
		// Set CORS headers securely
		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
			c.Header("Access-Control-Allow-Credentials", "true")
		} else {
			// For non-credentialed requests, we can be more permissive for public endpoints
			if isPublicEndpoint(c.Request.URL.Path) {
				c.Header("Access-Control-Allow-Origin", "*")
				// NOTE: Do not set Allow-Credentials for public endpoints
			} else {
				// Reject requests from disallowed origins for protected endpoints
				c.Header("Access-Control-Allow-Origin", "")
			}
		}
		
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-User-ID, X-Device-ID, X-Platform-ID, X-Request-ID, X-Timestamp, X-Client-Version, X-Client-Name")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")
		c.Header("Access-Control-Max-Age", "86400") // Cache preflight for 24 hours

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// isPublicEndpoint checks if the endpoint is public and doesn't require credentials
func isPublicEndpoint(path string) bool {
	publicPrefixes := []string{
		"/health",
		"/status",
		"/swagger",
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/verify",
		"/api/v1/auth/reset-password",
		"/api/v1/social/feed/public",
		"/api/v1/social/feed/trending",
	}
	
	for _, prefix := range publicPrefixes {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}
	
	return false
}