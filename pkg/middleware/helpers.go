package middleware

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/utils"
)

// ===== HELPER MIDDLEWARE (OPTIONAL 5TH) =====

// RequireUserMiddleware ensures user is authenticated and creates UserContext
// This is the 5th middleware for services that need user context validation
func RequireUserMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get(UserIDKey)
		if !exists {
			utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", nil)
			c.Abort()
			return
		}

		// Validate user ID is not empty
		if userID == "" {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid user context", nil)
			c.Abort()
			return
		}

		// Check if this is service authentication
		if userID == "service" {
			if serviceAuth, exists := c.Get(ServiceAuthKey); exists && serviceAuth.(bool) {
				userContext := &UserContext{
					UserID: "service",
					Email:  "service@internal",
					Roles:  []string{"service"},
					Teams:  []string{},
				}
				c.Set(UserContextKey, userContext)
				c.Next()
				return
			} else {
				utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid service context", nil)
				c.Abort()
				return
			}
		}

		// Create user context for easy access
		userIDStr, ok := userID.(string)
		if !ok {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid user ID type", nil)
			c.Abort()
			return
		}

		userContext := &UserContext{
			UserID: userIDStr,
		}

		if email, exists := c.Get(EmailKey); exists {
			if emailStr, ok := email.(string); ok {
				userContext.Email = emailStr
			}
		}
		if roles, exists := c.Get(RolesKey); exists {
			if rolesSlice, ok := roles.([]string); ok {
				userContext.Roles = rolesSlice
			}
		}
		if teams, exists := c.Get(TeamsKey); exists {
			if teamsSlice, ok := teams.([]string); ok {
				userContext.Teams = teamsSlice
			}
		}

		c.Set(UserContextKey, userContext)
		c.Next()
	}
}

// ===== CONTEXT HELPERS =====

// UserContext represents the authenticated user information
type UserContext struct {
	UserID string
	Email  string
	Roles  []string
	Teams  []string
}

// GetUserID extracts user ID from context with validation
func GetUserID(c *gin.Context) (string, error) {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return "", fmt.Errorf("user not authenticated")
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		return "", fmt.Errorf("invalid user ID format")
	}

	return userIDStr, nil
}

// MustGetUserID extracts user ID from context and sends error response on failure
func MustGetUserID(c *gin.Context) string {
	userID, err := GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, err.Error(), nil)
		c.Abort()
		return ""
	}
	return userID
}

// GetUserContext retrieves the complete user context
func GetUserContext(c *gin.Context) (*UserContext, error) {
	userCtx, exists := c.Get(UserContextKey)
	if !exists {
		return nil, fmt.Errorf("user context not found")
	}

	userContext, ok := userCtx.(*UserContext)
	if !ok {
		return nil, fmt.Errorf("invalid user context format")
	}

	return userContext, nil
}

// GetUserIDFromContext extracts the user ID from the gin context as UUID
func GetUserIDFromContext(c *gin.Context) (uuid.UUID, error) {
	userIDStr, exists := c.Get(UserIDKey)
	if !exists {
		return uuid.Nil, fmt.Errorf("user ID not found in context")
	}

	userIDValue, ok := userIDStr.(string)
	if !ok {
		return uuid.Nil, fmt.Errorf("user ID is not a string")
	}

	userID, err := uuid.Parse(userIDValue)
	if err != nil {
		return uuid.Nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	return userID, nil
}

// MustGetUserIDFromContext extracts user ID from context and responds with error if invalid
func MustGetUserIDFromContext(c *gin.Context) uuid.UUID {
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, err.Error(), nil)
		c.Abort()
		return uuid.Nil
	}
	return userID
}

// GetOptionalUser returns user ID if authenticated, empty string if not (no error response)
func GetOptionalUser(c *gin.Context) string {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return ""
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return ""
	}

	return userIDStr
}

// GetUserTeams extracts user teams from context
func GetUserTeams(c *gin.Context) []string {
	userTeams, exists := c.Get(TeamsKey)
	if !exists {
		return []string{}
	}

	teams, ok := userTeams.([]string)
	if !ok {
		return []string{}
	}

	return teams
}

// ExtractBearerToken extracts bearer token from Authorization header
func ExtractBearerToken(c *gin.Context) (string, error) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return "", fmt.Errorf("authorization header required")
	}

	bearerToken := strings.Split(authHeader, " ")
	if len(bearerToken) != 2 {
		return "", fmt.Errorf("invalid authorization format")
	}

	if bearerToken[0] != "Bearer" {
		return "", fmt.Errorf("invalid token type, expected Bearer")
	}

	return bearerToken[1], nil
}

// ForwardUserToken creates context with user token for service-to-service calls
func ForwardUserToken(c *gin.Context) string {
	// First check Authorization header (for API clients)
	if authHeader := c.GetHeader("Authorization"); authHeader != "" {
		return authHeader
	}

	// Then check access_token cookie (for web frontend)
	if cookieToken, err := c.Cookie("access_token"); err == nil && cookieToken != "" {
		return "Bearer " + cookieToken
	}

	return ""
}

// ValidateServiceToken checks if request has valid service token
func ValidateServiceToken(c *gin.Context) bool {
	authHeader := c.GetHeader("Authorization")
	if !strings.HasPrefix(authHeader, "Service ") {
		return false
	}

	token := strings.TrimPrefix(authHeader, "Service ")
	expectedToken := os.Getenv("SERVICE_TOKEN")
	return token != "" && token == expectedToken
}

// ===== CONVENIENCE FUNCTIONS =====

// MustGetAuthenticatedUser extracts user ID with validation and returns empty string on error
func MustGetAuthenticatedUser(c *gin.Context) string {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", nil)
		return ""
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid user context", nil)
		return ""
	}

	return userIDStr
}

// ParseUUIDParam extracts and validates UUID parameter from URL
func ParseUUIDParam(c *gin.Context, paramName string) (uuid.UUID, error) {
	paramStr := c.Param(paramName)
	if paramStr == "" {
		return uuid.Nil, fmt.Errorf("%s parameter is required", paramName)
	}

	parsedUUID, err := uuid.Parse(paramStr)
	if err != nil {
		return uuid.Nil, fmt.Errorf("invalid %s format: %w", paramName, err)
	}

	return parsedUUID, nil
}

// MustParseUUIDParam extracts UUID parameter and responds with error if invalid
func MustParseUUIDParam(c *gin.Context, paramName string) uuid.UUID {
	parsedUUID, err := ParseUUIDParam(c, paramName)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		c.Abort()
		return uuid.Nil
	}
	return parsedUUID
}

// BindJSONRequest binds JSON request and handles errors
func BindJSONRequest(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err)
		return false
	}
	return true
}

// ===== PAGINATION MIDDLEWARE =====

// PaginationRequest represents pagination parameters from middleware
type PaginationRequest struct {
	Cursor    string `json:"cursor,omitempty" form:"cursor"`
	Limit     int    `json:"limit,omitempty" form:"limit"`
	Direction string `json:"direction,omitempty" form:"direction"`
}

// ValidateCursorPagination validates and normalizes cursor pagination parameters
func ValidateCursorPagination(c *gin.Context) *PaginationRequest {
	// Set default values
	request := &PaginationRequest{
		Cursor:    c.Query("cursor"),
		Limit:     20,
		Direction: "forward",
	}

	// Parse limit parameter
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			request.Limit = limit
		}
	}

	// Parse direction parameter
	if direction := c.Query("direction"); direction != "" {
		request.Direction = direction
	}

	// Validate and normalize limit
	if request.Limit <= 0 {
		request.Limit = 20
	}
	if request.Limit > 100 {
		request.Limit = 100
	}

	// Validate direction
	if request.Direction != "forward" && request.Direction != "backward" {
		request.Direction = "forward"
	}

	return request
}

// ValidatePagination validates standard pagination parameters (for backwards compatibility)
func ValidatePagination(c *gin.Context) *PaginationRequest {
	return ValidateCursorPagination(c)
}

// HandleStandardError provides consistent error handling for common service errors
func HandleStandardError(c *gin.Context, err error, entityName string) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()
	switch {
	case errorMsg == "not found" || strings.Contains(errorMsg, "not found"):
		utils.ErrorResponse(c, http.StatusNotFound, fmt.Sprintf("%s not found", entityName), nil)
		return true
	case errorMsg == "access denied" || strings.Contains(errorMsg, "access denied"):
		utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
		return true
	case errorMsg == "already exists" || strings.Contains(errorMsg, "already exists"):
		utils.ErrorResponse(c, http.StatusConflict, fmt.Sprintf("%s already exists", entityName), nil)
		return true
	default:
		return false
	}
}