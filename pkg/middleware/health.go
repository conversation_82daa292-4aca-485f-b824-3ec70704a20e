package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/utils"
)

// HealthCheckMiddleware creates a standardized health check endpoint
func HealthCheckMiddleware(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		utils.SuccessResponse(c, http.StatusOK, serviceName+" is healthy", map[string]string{
			"service": serviceName,
			"status":  "healthy",
		})
	}
}

