package middleware

import (
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

// SecurityConfig holds configuration for security headers
type SecurityConfig struct {
	Environment       string
	ContentTypeNoSniff bool
	FrameOptions      string
	XSSProtection     string
	ContentSecurity   string
	ReferrerPolicy    string
	HSTSMaxAge        string
	HSTSSubdomains    bool
	ServerHeader      string
}

// NewSecurityConfig creates default security configuration
func NewSecurityConfig() SecurityConfig {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "development"
	}

	return SecurityConfig{
		Environment:       env,
		ContentTypeNoSniff: true,
		FrameOptions:      "DENY",
		XSSProtection:     "1; mode=block",
		ReferrerPolicy:    "strict-origin-when-cross-origin",
		HSTSMaxAge:        "31536000", // 1 year
		HSTSSubdomains:    true,
		ServerHeader:      "", // Remove server identification
	}
}

// SecurityHeadersMiddleware applies comprehensive security headers
func SecurityHeadersMiddleware(config ...SecurityConfig) gin.HandlerFunc {
	var cfg SecurityConfig
	if len(config) > 0 {
		cfg = config[0]
	} else {
		cfg = NewSecurityConfig()
	}

	return func(c *gin.Context) {
		// Content-Type Options
		if cfg.ContentTypeNoSniff {
			c.Header("X-Content-Type-Options", "nosniff")
		}

		// Frame Options
		if cfg.FrameOptions != "" {
			c.Header("X-Frame-Options", cfg.FrameOptions)
		}

		// XSS Protection
		if cfg.XSSProtection != "" {
			c.Header("X-XSS-Protection", cfg.XSSProtection)
		}

		// Content Security Policy
		csp := cfg.getContentSecurityPolicy()
		if csp != "" {
			c.Header("Content-Security-Policy", csp)
		}

		// Referrer Policy
		if cfg.ReferrerPolicy != "" {
			c.Header("Referrer-Policy", cfg.ReferrerPolicy)
		}

		// HSTS (only for HTTPS)
		if cfg.Environment == "production" && c.Request.TLS != nil {
			hstsValue := "max-age=" + cfg.HSTSMaxAge
			if cfg.HSTSSubdomains {
				hstsValue += "; includeSubDomains"
			}
			hstsValue += "; preload"
			c.Header("Strict-Transport-Security", hstsValue)
		}

		// Permission Policy (Feature Policy replacement)
		c.Header("Permissions-Policy", cfg.getPermissionsPolicy())

		// Remove server identification
		if cfg.ServerHeader == "" {
			c.Header("Server", "")
		} else {
			c.Header("Server", cfg.ServerHeader)
		}

		// Cache control for sensitive endpoints
		if cfg.isSensitiveEndpoint(c.Request.URL.Path) {
			c.Header("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}

		// Additional security headers
		c.Header("X-Robots-Tag", "noindex, nofollow, nosnippet, noarchive")
		c.Header("X-Download-Options", "noopen")
		c.Header("X-Permitted-Cross-Domain-Policies", "none")

		c.Next()
	}
}

// getContentSecurityPolicy returns appropriate CSP based on environment
func (cfg SecurityConfig) getContentSecurityPolicy() string {
	if cfg.ContentSecurity != "" {
		return cfg.ContentSecurity
	}

	// Default CSP for drive service
	baseCSP := []string{
		"default-src 'self'",
		"script-src 'self' 'unsafe-inline'", // Might need adjustment based on frontend
		"style-src 'self' 'unsafe-inline'",
		"img-src 'self' data: blob:",
		"media-src 'self' blob:",
		"object-src 'none'",
		"base-uri 'self'",
		"form-action 'self'",
		"frame-ancestors 'none'",
		"upgrade-insecure-requests",
	}

	if cfg.Environment == "development" {
		// More relaxed CSP for development
		baseCSP = append(baseCSP, "connect-src 'self' ws: wss:")
	} else {
		// Stricter CSP for production
		baseCSP = append(baseCSP, "connect-src 'self'")
		baseCSP = append(baseCSP, "require-trusted-types-for 'script'")
	}

	return strings.Join(baseCSP, "; ")
}

// getPermissionsPolicy returns appropriate permissions policy
func (cfg SecurityConfig) getPermissionsPolicy() string {
	// Restrict potentially dangerous browser features
	policies := []string{
		"accelerometer=()",
		"camera=()",
		"geolocation=()",
		"gyroscope=()",
		"magnetometer=()",
		"microphone=()",
		"payment=()",
		"usb=()",
		"interest-cohort=()", // Disable FLoC
	}

	// Allow fullscreen for file preview functionality
	policies = append(policies, "fullscreen=(self)")

	return strings.Join(policies, ", ")
}

// isSensitiveEndpoint checks if the endpoint handles sensitive data
func (cfg SecurityConfig) isSensitiveEndpoint(path string) bool {
	sensitivePatterns := []string{
		"/api/v1/auth/",
		"/api/v1/drive/",
		"/login",
		"/register",
		"/upload",
		"/download",
		"/share",
		"/admin",
	}

	for _, pattern := range sensitivePatterns {
		if strings.Contains(path, pattern) {
			return true
		}
	}

	return false
}

// CSRFProtectionMiddleware provides CSRF protection for state-changing operations
func CSRFProtectionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip CSRF for safe methods
		if c.Request.Method == "GET" || c.Request.Method == "HEAD" || c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// Check for CSRF token in header
		csrfToken := c.GetHeader("X-CSRF-Token")
		
		// For now, just validate that a token is present
		// In production, you'd validate against a stored token
		if csrfToken == "" {
			// Check if this is an API request (has Authorization header)
			authHeader := c.GetHeader("Authorization")
			if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
				// API requests with valid JWT tokens are exempt from CSRF
				c.Next()
				return
			}

			c.Header("Content-Type", "application/json")
			c.AbortWithStatusJSON(403, gin.H{
				"error": "CSRF token required",
				"code":  "CSRF_TOKEN_REQUIRED",
			})
			return
		}

		c.Next()
	}
}

// RequestSizeLimitMiddleware limits request body size
func RequestSizeLimitMiddleware(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.Header("Content-Type", "application/json")
			c.AbortWithStatusJSON(413, gin.H{
				"error": "Request entity too large",
				"code":  "REQUEST_TOO_LARGE",
				"max_size": maxSize,
			})
			return
		}

		// Also set a reader limit to prevent memory exhaustion
		c.Request.Body = &limitedReader{
			reader: c.Request.Body,
			limit:  maxSize,
		}

		c.Next()
	}
}

// limitedReader implements io.ReadCloser with size limiting
type limitedReader struct {
	reader io.ReadCloser
	limit  int64
	read   int64
}

func (lr *limitedReader) Read(p []byte) (n int, err error) {
	if lr.read >= lr.limit {
		return 0, fmt.Errorf("request body too large")
	}

	remaining := lr.limit - lr.read
	if int64(len(p)) > remaining {
		p = p[:remaining]
	}

	n, err = lr.reader.Read(p)
	lr.read += int64(n)
	return n, err
}

func (lr *limitedReader) Close() error {
	return lr.reader.Close()
}