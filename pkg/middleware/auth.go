package middleware

import (
	"crypto/subtle"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/utils"
)

// AuthSource represents different authentication sources
type AuthSource int

const (
	AuthSourceJWT AuthSource = iota
	AuthSourceCookie
	AuthSourceService
	AuthSourceHeader
	AuthSourceWebSocket
)

// AuthConfig defines authentication configuration
type AuthConfig struct {
	Required    bool
	Sources     []AuthSource
	WebSocket   bool
	FailOnError bool
}

// AuthResult represents authentication result
type AuthResult struct {
	Authenticated bool
	UserID        string
	Email         string
	Roles         []string
	Teams         []string
	ServiceAuth   bool
	Source        AuthSource
}

// Cache JWT secret to avoid repeated env lookups
var (
	jwtSecretCache []byte
	jwtSecretOnce  sync.Once
)

// Context keys - must match helpers.go
const (
	UserIDKey      = "user_id"
	EmailKey       = "email"
	RolesKey       = "roles"
	TeamsKey       = "teams"
	ServiceAuthKey = "service_auth"
	UserContextKey = "user_context"
)

// Claims represents JWT claims
type Claims struct {
	UserID string   `json:"user_id"`
	Email  string   `json:"email"`
	Roles  []string `json:"roles"`
	Teams  []string `json:"teams"`
	jwt.RegisteredClaims
}

// ===== CORE MIDDLEWARE (4 MAIN ONES) =====

// 1. AuthMiddleware - Required authentication with all sources
func AuthMiddleware() gin.HandlerFunc {
	config := AuthConfig{
		Required:    true,
		Sources:     []AuthSource{AuthSourceService, AuthSourceHeader, AuthSourceJWT, AuthSourceCookie},
		FailOnError: true,
	}
	return unifiedAuthMiddleware(config)
}

// 2. OptionalAuthMiddleware - Optional authentication with all sources
func OptionalAuthMiddleware() gin.HandlerFunc {
	config := AuthConfig{
		Required:    false,
		Sources:     []AuthSource{AuthSourceService, AuthSourceHeader, AuthSourceJWT, AuthSourceCookie},
		FailOnError: false,
	}
	return unifiedAuthMiddleware(config)
}

// 3. ServiceAuthMiddleware - Service-to-service authentication only
func ServiceAuthMiddleware() gin.HandlerFunc {
	config := AuthConfig{
		Required:    true,
		Sources:     []AuthSource{AuthSourceService},
		FailOnError: true,
	}
	return unifiedAuthMiddleware(config)
}

// 4. WebSocketAuthMiddleware - WebSocket specific authentication
func WebSocketAuthMiddleware() gin.HandlerFunc {
	config := AuthConfig{
		Required:    true,
		Sources:     []AuthSource{AuthSourceWebSocket, AuthSourceJWT},
		WebSocket:   true,
		FailOnError: true,
	}
	return unifiedAuthMiddleware(config)
}

// ===== UNIFIED IMPLEMENTATION =====

// unifiedAuthMiddleware handles all authentication logic
func unifiedAuthMiddleware(config AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		result := tryAuthenticate(c, config)

		if result.Authenticated {
			// Set context values
			c.Set(UserIDKey, result.UserID)
			if result.Email != "" {
				c.Set(EmailKey, result.Email)
			}
			if len(result.Roles) > 0 {
				c.Set(RolesKey, result.Roles)
			}
			if len(result.Teams) > 0 {
				c.Set(TeamsKey, result.Teams)
			}
			if result.ServiceAuth {
				c.Set(ServiceAuthKey, true)
			}
			c.Next()
			return
		}

		// Handle authentication failure
		if config.Required && config.FailOnError {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Authorization required", nil)
			c.Abort()
			return
		}

		// Continue without authentication (optional auth)
		c.Next()
	}
}

// tryAuthenticate attempts authentication with configured sources
func tryAuthenticate(c *gin.Context, config AuthConfig) AuthResult {
	for _, source := range config.Sources {
		switch source {
		case AuthSourceService:
			if result := tryServiceAuth(c); result.Authenticated {
				return result
			}
		case AuthSourceHeader:
			if result := tryHeaderAuth(c); result.Authenticated {
				return result
			}
		case AuthSourceWebSocket:
			if result := tryWebSocketAuth(c); result.Authenticated {
				return result
			}
		case AuthSourceJWT:
			if result := tryJWTAuth(c); result.Authenticated {
				return result
			}
		case AuthSourceCookie:
			if result := tryCookieAuth(c); result.Authenticated {
				return result
			}
		}
	}
	return AuthResult{Authenticated: false}
}

// ===== AUTHENTICATION STRATEGIES =====

// tryServiceAuth attempts service token authentication
func tryServiceAuth(c *gin.Context) AuthResult {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return AuthResult{Authenticated: false}
	}

	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Service" {
		return AuthResult{Authenticated: false}
	}

	serviceToken := parts[1]
	expectedServiceToken := os.Getenv("SERVICE_TOKEN")
	if expectedServiceToken == "" || len(expectedServiceToken) < 32 {
		return AuthResult{Authenticated: false}
	}

	if subtle.ConstantTimeCompare([]byte(serviceToken), []byte(expectedServiceToken)) == 1 {
		return AuthResult{
			Authenticated: true,
			UserID:        "service",
			Email:         "service@internal",
			Roles:         []string{"service"},
			Teams:         []string{},
			ServiceAuth:   true,
			Source:        AuthSourceService,
		}
	}

	return AuthResult{Authenticated: false}
}

// tryHeaderAuth attempts header-based authentication (from gateway)
func tryHeaderAuth(c *gin.Context) AuthResult {
	userID := c.GetHeader("X-User-ID")
	if userID == "" {
		return AuthResult{Authenticated: false}
	}

	result := AuthResult{
		Authenticated: true,
		UserID:        userID,
		Source:        AuthSourceHeader,
	}

	if email := c.GetHeader("X-User-Email"); email != "" {
		result.Email = email
	}

	if rolesHeader := c.GetHeader("X-User-Roles"); rolesHeader != "" {
		result.Roles = strings.Split(rolesHeader, ",")
	}

	if teamsHeader := c.GetHeader("X-User-Teams"); teamsHeader != "" {
		result.Teams = strings.Split(teamsHeader, ",")
	}

	return result
}

// tryWebSocketAuth attempts WebSocket authentication (query param or header)
func tryWebSocketAuth(c *gin.Context) AuthResult {
	var tokenString string

	// Check query parameter first (WebSocket standard)
	if token := c.Query("token"); token != "" {
		tokenString = token
	} else {
		// Fallback to Authorization header
		if authHeader := c.GetHeader("Authorization"); authHeader != "" {
			if strings.HasPrefix(authHeader, "Bearer ") {
				tokenString = strings.TrimPrefix(authHeader, "Bearer ")
			}
		}
	}

	if tokenString == "" {
		return AuthResult{Authenticated: false}
	}

	return validateJWTToken(tokenString, AuthSourceWebSocket)
}

// tryJWTAuth attempts JWT Bearer token authentication
func tryJWTAuth(c *gin.Context) AuthResult {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return AuthResult{Authenticated: false}
	}

	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		return AuthResult{Authenticated: false}
	}

	return validateJWTToken(parts[1], AuthSourceJWT)
}

// tryCookieAuth attempts cookie-based authentication
func tryCookieAuth(c *gin.Context) AuthResult {
	cookieToken, err := c.Cookie("access_token")
	if err != nil || cookieToken == "" {
		return AuthResult{Authenticated: false}
	}

	return validateJWTToken(cookieToken, AuthSourceCookie)
}

// validateJWTToken validates JWT token and extracts claims
func validateJWTToken(tokenString string, source AuthSource) AuthResult {
	claims := &Claims{}

	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return getJWTSecret()
	})

	if err != nil || !token.Valid {
		return AuthResult{Authenticated: false}
	}

	return AuthResult{
		Authenticated: true,
		UserID:        claims.UserID,
		Email:         claims.Email,
		Roles:         claims.Roles,
		Teams:         claims.Teams,
		ServiceAuth:   false,
		Source:        source,
	}
}

// getJWTSecret safely retrieves and validates JWT secret
func getJWTSecret() ([]byte, error) {
	jwtSecretOnce.Do(func() {
		jwtSecret := os.Getenv("JWT_SECRET")
		if jwtSecret == "" {
			log := logger.NewDefaultLogger("middleware")
			log.Error("JWT_SECRET environment variable not set")
			return
		}
		if len(jwtSecret) < 32 {
			log := logger.NewDefaultLogger("middleware")
			log.Error("JWT_SECRET is too short, must be at least 32 characters")
			return
		}
		jwtSecretCache = []byte(jwtSecret)
	})

	if jwtSecretCache == nil {
		return nil, fmt.Errorf("JWT secret not properly configured")
	}

	return jwtSecretCache, nil
}