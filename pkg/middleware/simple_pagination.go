package middleware

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// SimplePaginationRequest represents simple offset-based pagination
type SimplePaginationRequest struct {
	Page   int `json:"page"`
	Limit  int `json:"limit"`
	Offset int `json:"offset"`
}

// SimplePaginationResponse represents pagination metadata for responses
type SimplePaginationResponse struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// ValidateSimplePagination validates and normalizes simple pagination parameters
func ValidateSimplePagination(c *gin.Context) *SimplePaginationRequest {
	// Default values
	page := 1
	limit := 20

	// Parse page parameter
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// Parse limit parameter
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// Validate and normalize limit (1-100)
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	// Calculate offset
	offset := (page - 1) * limit

	return &SimplePaginationRequest{
		Page:   page,
		Limit:  limit,
		Offset: offset,
	}
}

// CreatePaginationResponse creates pagination metadata for API responses
func CreatePaginationResponse(page, limit int, total int64) *SimplePaginationResponse {
	totalPages := int((total + int64(limit) - 1) / int64(limit)) // Ceiling division
	if totalPages < 1 {
		totalPages = 1
	}

	return &SimplePaginationResponse{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// Helper function to extract simple pagination from context
func GetSimplePagination(c *gin.Context) (int, int) {
	pagination := ValidateSimplePagination(c)
	return pagination.Limit, pagination.Offset
}