package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/utils"
)

type RateLimiter struct {
	redis *redis.Client
}

func NewRateLimiter(redisClient *redis.Client) *RateLimiter {
	return &RateLimiter{
		redis: redisClient,
	}
}

func (rl *RateLimiter) LimitByIP(requests int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := c.ClientIP()
		key := fmt.Sprintf("rate_limit:ip:%s", ip)

		if !rl.allow(c, key, requests, window) {
			return
		}

		c.Next()
	}
}

func (rl *RateLimiter) LimitByUser(requests int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.Next()
			return
		}

		key := fmt.Sprintf("rate_limit:user:%s", userID)

		if !rl.allow(c, key, requests, window) {
			return
		}

		c.Next()
	}
}

func (rl *RateLimiter) allow(c *gin.Context, key string, requests int, window time.Duration) bool {
	ctx := c.Request.Context()

	current, err := rl.redis.Get(ctx, key).Int()
	if err != nil && err != redis.Nil {
		// If Redis fails, allow the request but log error
		fmt.Printf("Rate limiter Redis error: %v\n", err)
		return true
	}

	if current >= requests {
		// Get TTL for rate limit reset info
		ttl, _ := rl.redis.TTL(ctx, key).Result()
		resetTime := time.Now().Add(ttl)
		if ttl <= 0 {
			resetTime = time.Now().Add(window)
		}
		
		c.Header("X-RateLimit-Limit", strconv.Itoa(requests))
		c.Header("X-RateLimit-Remaining", "0")
		c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime.Unix(), 10))
		c.Header("Retry-After", strconv.FormatInt(int64(ttl.Seconds()), 10))

		utils.ErrorResponse(c, http.StatusTooManyRequests, "Rate limit exceeded. Please try again later.", nil)
		c.Abort()
		return false
	}

	// Increment counter atomically
	pipe := rl.redis.Pipeline()
	pipe.Incr(ctx, key)
	if current == 0 {
		// Only set expiration for new keys to avoid resetting TTL
		pipe.Expire(ctx, key, window)
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		fmt.Printf("Rate limiter pipeline error: %v\n", err)
		// Still allow the request if Redis fails
	}

	// Set rate limit headers
	remaining := requests - current - 1
	if remaining < 0 {
		remaining = 0
	}
	
	c.Header("X-RateLimit-Limit", strconv.Itoa(requests))
	c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
	c.Header("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(window).Unix(), 10))

	return true
}

// LimitByEndpoint applies different rate limits based on endpoint
func (rl *RateLimiter) LimitByEndpoint() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		method := c.Request.Method
		
		var requests int
		var window time.Duration
		
		// Define rate limits for different endpoints
		switch {
		case strings.Contains(path, "/teams") && method == "POST":
			// Team creation - more restrictive
			requests, window = 5, time.Hour
		case strings.Contains(path, "/teams") && strings.Contains(path, "/invite"):
			// Invitations - moderate limits
			requests, window = 10, time.Minute*10
		case strings.Contains(path, "/search"):
			// Search operations - prevent abuse
			requests, window = 30, time.Minute
		case strings.Contains(path, "/teams") && strings.Contains(path, "/members"):
			// Member operations
			requests, window = 20, time.Minute
		default:
			// Default rate limit for other operations
			requests, window = 100, time.Minute
		}
		
		userID, exists := c.Get("user_id")
		var key string
		if exists && userID != "" {
			key = fmt.Sprintf("rate_limit:endpoint:%s:%s:%s", userID, method, path)
		} else {
			key = fmt.Sprintf("rate_limit:endpoint:ip:%s:%s:%s", c.ClientIP(), method, path)
		}
		
		if !rl.allowWithLogging(c, key, requests, window, fmt.Sprintf("%s %s", method, path)) {
			return
		}
		
		c.Next()
	}
}

// LimitTeamOperations provides specific limits for team operations
func (rl *RateLimiter) LimitTeamOperations() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.Next()
			return
		}
		
		path := c.Request.URL.Path
		method := c.Request.Method
		
		var requests int
		var window time.Duration
		var operation string
		
		// More granular limits for team operations
		switch {
		case method == "POST" && strings.HasSuffix(path, "/teams"):
			requests, window, operation = 3, time.Hour, "team_creation"
		case strings.Contains(path, "/invite") && method == "POST":
			requests, window, operation = 20, time.Hour, "invitation_send"
		case strings.Contains(path, "/members") && method == "POST":
			requests, window, operation = 10, time.Minute*10, "member_add"
		case strings.Contains(path, "/search"):
			requests, window, operation = 50, time.Minute, "search"
		default:
			c.Next()
			return
		}
		
		key := fmt.Sprintf("rate_limit:team_ops:%s:%s", userID, operation)
		
		if !rl.allowWithLogging(c, key, requests, window, operation) {
			return
		}
		
		c.Next()
	}
}

// allowWithLogging includes security logging for rate limit violations
func (rl *RateLimiter) allowWithLogging(c *gin.Context, key string, requests int, window time.Duration, operation string) bool {
	ctx := c.Request.Context()
	
	current, err := rl.redis.Get(ctx, key).Int()
	if err != nil && err != redis.Nil {
		log := logger.NewDefaultLogger("middleware")
		log.Error("Rate limiter Redis error",
			logger.F("error", err),
			logger.F("key", key),
			logger.F("operation", operation))
		return true
	}
	
	if current >= requests {
		// Log rate limit violation for security monitoring
		userID, _ := c.Get("user_id")
		log := logger.NewDefaultLogger("middleware")
		log.Warn("Rate limit exceeded",
			logger.F("user_id", userID),
			logger.F("ip", c.ClientIP()),
			logger.F("operation", operation),
			logger.F("current_count", current),
			logger.F("limit", requests),
			logger.F("window", window.String()),
			logger.F("user_agent", c.GetHeader("User-Agent")))
		
		// Get TTL for rate limit reset info
		ttl, _ := rl.redis.TTL(ctx, key).Result()
		resetTime := time.Now().Add(ttl)
		if ttl <= 0 {
			resetTime = time.Now().Add(window)
		}
		
		c.Header("X-RateLimit-Limit", strconv.Itoa(requests))
		c.Header("X-RateLimit-Remaining", "0")
		c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime.Unix(), 10))
		c.Header("Retry-After", strconv.FormatInt(int64(ttl.Seconds()), 10))
		
		utils.ErrorResponse(c, http.StatusTooManyRequests, "Rate limit exceeded. Please try again later.", nil)
		c.Abort()
		return false
	}
	
	// Increment counter atomically
	pipe := rl.redis.Pipeline()
	pipe.Incr(ctx, key)
	if current == 0 {
		pipe.Expire(ctx, key, window)
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		log := logger.NewDefaultLogger("middleware")
		log.Error("Rate limiter pipeline error",
			logger.F("error", err),
			logger.F("key", key))
	}
	
	// Set rate limit headers
	remaining := requests - current - 1
	if remaining < 0 {
		remaining = 0
	}
	
	c.Header("X-RateLimit-Limit", strconv.Itoa(requests))
	c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
	c.Header("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(window).Unix(), 10))
	
	return true
}

