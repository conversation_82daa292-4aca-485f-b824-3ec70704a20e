package webhook

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// WebhookClient handles HTTP webhook delivery to replace Kafka external events
type WebhookClient struct {
	httpClient *http.Client
	queue      chan *WebhookRequest
	workers    int
	config     *WebhookConfig
}

// WebhookConfig contains webhook client configuration
type WebhookConfig struct {
	Enabled     bool          `mapstructure:"enabled" default:"false"`
	Timeout     time.Duration `mapstructure:"timeout" default:"30s"`
	RetryCount  int           `mapstructure:"retry_count" default:"3"`
	VerifySSL   bool          `mapstructure:"verify_ssl" default:"true"`
	Workers     int           `mapstructure:"workers" default:"5"`
	QueueSize   int           `mapstructure:"queue_size" default:"1000"`
}

// WebhookRequest represents a webhook to be sent
type WebhookRequest struct {
	URL     string                 `json:"url"`
	Event   string                 `json:"event"`
	UserID  string                 `json:"user_id"`
	Data    map[string]interface{} `json:"data"`
	Headers map[string]string      `json:"headers,omitempty"`
	Retries int                    `json:"retries"`
}

// WebhookResponse represents the response from a webhook delivery
type WebhookResponse struct {
	StatusCode int           `json:"status_code"`
	Body       string        `json:"body"`
	Duration   time.Duration `json:"duration"`
	Error      error         `json:"error,omitempty"`
}

// NewWebhookClient creates a new webhook client
func NewWebhookClient(config *WebhookConfig) *WebhookClient {
	if config == nil {
		config = &WebhookConfig{
			Enabled:    false,
			Timeout:    30 * time.Second,
			RetryCount: 3,
			VerifySSL:  true,
			Workers:    5,
			QueueSize:  1000,
		}
	}

	// Create HTTP client with timeout and SSL configuration
	transport := &http.Transport{
		MaxIdleConns:       100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:    90 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: !config.VerifySSL,
		},
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   config.Timeout,
	}

	wc := &WebhookClient{
		httpClient: client,
		queue:      make(chan *WebhookRequest, config.QueueSize),
		workers:    config.Workers,
		config:     config,
	}

	// Start workers if webhooks are enabled
	if config.Enabled {
		wc.startWorkers()
	}

	return wc
}

// startWorkers starts webhook delivery workers
func (wc *WebhookClient) startWorkers() {
	log.Printf("Starting %d webhook workers", wc.workers)
	
	for i := 0; i < wc.workers; i++ {
		go wc.worker(i)
	}
}

// worker processes webhook requests from the queue
func (wc *WebhookClient) worker(workerID int) {
	log.Printf("Webhook worker %d started", workerID)
	
	for req := range wc.queue {
		if err := wc.deliverWebhook(req); err != nil {
			log.Printf("Webhook worker %d delivery failed: %v", workerID, err)
		}
	}
	
	log.Printf("Webhook worker %d stopped", workerID)
}

// SendWebhook queues a webhook for delivery
func (wc *WebhookClient) SendWebhook(req *WebhookRequest) error {
	if !wc.config.Enabled {
		log.Printf("Webhooks disabled, skipping: %s", req.Event)
		return nil
	}

	select {
	case wc.queue <- req:
		log.Printf("Webhook queued: event=%s, user_id=%s, url=%s", req.Event, req.UserID, req.URL)
		return nil
	default:
		return fmt.Errorf("webhook queue full, dropping event: %s", req.Event)
	}
}

// SendWebhookSync sends a webhook synchronously (blocking)
func (wc *WebhookClient) SendWebhookSync(req *WebhookRequest) (*WebhookResponse, error) {
	if !wc.config.Enabled {
		return &WebhookResponse{}, fmt.Errorf("webhooks are disabled")
	}

	return wc.deliverWebhookWithResponse(req)
}

// deliverWebhook handles webhook delivery with retry logic
func (wc *WebhookClient) deliverWebhook(req *WebhookRequest) error {
	response, err := wc.deliverWebhookWithResponse(req)
	if err != nil {
		return err
	}

	if response.StatusCode >= 400 {
		return fmt.Errorf("webhook delivery failed with status %d", response.StatusCode)
	}

	return nil
}

// deliverWebhookWithResponse delivers webhook and returns full response
func (wc *WebhookClient) deliverWebhookWithResponse(req *WebhookRequest) (*WebhookResponse, error) {
	start := time.Now()
	
	// Prepare payload
	payload := map[string]interface{}{
		"event":   req.Event,
		"user_id": req.UserID,
		"data":    req.Data,
		"timestamp": time.Now().Format(time.RFC3339),
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal webhook payload: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", req.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create webhook request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("User-Agent", "Swork-Team-Webhook/1.0")
	
	// Add custom headers
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// Deliver webhook with retries
	var lastErr error
	var response *http.Response
	
	for attempt := 0; attempt <= wc.config.RetryCount; attempt++ {
		if attempt > 0 {
			// Exponential backoff
			delay := time.Duration(attempt*attempt) * time.Second
			log.Printf("Retrying webhook delivery (attempt %d) after %v: %s", attempt+1, delay, req.URL)
			time.Sleep(delay)
		}

		response, lastErr = wc.httpClient.Do(httpReq)
		if lastErr == nil && response.StatusCode < 400 {
			break // Success
		}

		if response != nil {
			response.Body.Close()
		}
	}

	duration := time.Since(start)

	if lastErr != nil {
		return &WebhookResponse{
			Duration: duration,
			Error:    lastErr,
		}, fmt.Errorf("webhook delivery failed after %d attempts: %w", wc.config.RetryCount+1, lastErr)
	}

	defer response.Body.Close()

	// Read response body
	body, err := io.ReadAll(response.Body)
	if err != nil {
		log.Printf("Failed to read webhook response body: %v", err)
		body = []byte{}
	}

	webhookResponse := &WebhookResponse{
		StatusCode: response.StatusCode,
		Body:       string(body),
		Duration:   duration,
	}

	log.Printf("Webhook delivered: event=%s, status=%d, duration=%v, url=%s", 
		req.Event, response.StatusCode, duration, req.URL)

	return webhookResponse, nil
}

// GetQueueStats returns webhook queue statistics
func (wc *WebhookClient) GetQueueStats() *WebhookStats {
	return &WebhookStats{
		QueueLength: len(wc.queue),
		QueueSize:   cap(wc.queue),
		Workers:     wc.workers,
		Enabled:     wc.config.Enabled,
		Timestamp:   time.Now(),
	}
}

// WebhookStats represents webhook performance metrics
type WebhookStats struct {
	QueueLength int       `json:"queue_length"`
	QueueSize   int       `json:"queue_size"`
	Workers     int       `json:"workers"`
	Enabled     bool      `json:"enabled"`
	Timestamp   time.Time `json:"timestamp"`
}

// Close gracefully shuts down the webhook client
func (wc *WebhookClient) Close() error {
	log.Println("Shutting down webhook client...")
	
	// Close the queue to stop workers
	close(wc.queue)
	
	// Close HTTP client
	wc.httpClient.CloseIdleConnections()
	
	log.Println("Webhook client shut down complete")
	return nil
}

// HealthCheck verifies the webhook client is working
func (wc *WebhookClient) HealthCheck() error {
	stats := wc.GetQueueStats()
	
	if wc.config.Enabled {
		// Check if queue is not full
		if stats.QueueLength >= stats.QueueSize {
			return fmt.Errorf("webhook queue is full (%d/%d)", stats.QueueLength, stats.QueueSize)
		}
	}
	
	return nil
}

// WebhookService interface for dependency injection
type WebhookService interface {
	SendWebhook(req *WebhookRequest) error
	SendWebhookSync(req *WebhookRequest) (*WebhookResponse, error)
	GetQueueStats() *WebhookStats
	HealthCheck() error
	Close() error
}

// Ensure WebhookClient implements WebhookService
var _ WebhookService = (*WebhookClient)(nil)