package database

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/config"
)

// NewRedisClient creates a new Redis client with the provided configuration
func NewRedisClient(cfg *config.RedisConfig) *redis.Client {
	if cfg == nil {
		log.Println("Warning: Redis config is nil, returning nil client")
		return nil
	}

	// Parse Redis URL or use individual components
	var client *redis.Client

	if cfg.URL != "" {
		// Parse Redis URL
		opt, err := redis.ParseURL(cfg.URL)
		if err != nil {
			log.Printf("Warning: Failed to parse Redis URL %s: %v", cfg.URL, err)
			return nil
		}

		// Override password and DB if provided in config
		if cfg.Password != "" {
			opt.Password = cfg.Password
		}
		if cfg.DB != 0 {
			opt.DB = cfg.DB
		}

		// Set connection pool settings
		opt.PoolSize = 10
		opt.MinIdleConns = 5
		opt.ConnMaxIdleTime = 5 * time.Minute
		opt.PoolTimeout = 30 * time.Second

		client = redis.NewClient(opt)
	} else {
		// Fallback to default localhost configuration
		client = redis.NewClient(&redis.Options{
			Addr:            "localhost:6379",
			Password:        cfg.Password,
			DB:              cfg.DB,
			PoolSize:        10,
			MinIdleConns:    5,
			ConnMaxIdleTime: 5 * time.Minute,
			PoolTimeout:     30 * time.Second,
		})
	}

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		log.Printf("Warning: Failed to connect to Redis: %v", err)
		// Don't return nil here - let the application decide how to handle Redis unavailability
		// This allows the gateway to start even if Redis is temporarily unavailable
		return client
	}

	log.Println("Successfully connected to Redis")
	return client
}

// NewRedisCluster creates a new Redis cluster client
func NewRedisCluster(addrs []string, password string) *redis.ClusterClient {
	if len(addrs) == 0 {
		log.Println("Warning: No Redis cluster addresses provided")
		return nil
	}

	client := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        addrs,
		Password:     password,
		PoolSize:     10,
		MinIdleConns: 5,
		PoolTimeout:  30 * time.Second,
	})

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		log.Printf("Warning: Failed to connect to Redis cluster: %v", err)
		return client
	}

	log.Println("Successfully connected to Redis cluster")
	return client
}

// RedisHealthCheck checks if Redis is available and responsive
func RedisHealthCheck(client *redis.Client) error {
	if client == nil {
		return fmt.Errorf("redis client is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	return client.Ping(ctx).Err()
}

// RedisClusterHealthCheck checks if Redis cluster is available and responsive
func RedisClusterHealthCheck(client *redis.ClusterClient) error {
	if client == nil {
		return fmt.Errorf("redis cluster client is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	return client.Ping(ctx).Err()
}
