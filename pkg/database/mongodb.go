package database

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
)

func NewMongoConnection(mongoURL string) (*mongo.Client, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Configure client options with connection pooling and reliability settings
	clientOptions := options.Client().
		ApplyURI(mongoURL).
		SetMaxPoolSize(100).                                                               // Maximum connections in the pool
		SetMinPoolSize(10).                                                                // Minimum connections to maintain
		SetMaxConnIdleTime(30 * time.Second).                                              // Max idle time before closing connection
		SetServerSelectionTimeout(5 * time.Second).                                        // Timeout for server selection
		SetSocketTimeout(10 * time.Second).                                                // Socket timeout
		SetConnectTimeout(10 * time.Second).                                               // Connection timeout
		SetHeartbeatInterval(10 * time.Second).                                            // Heartbeat frequency
		SetLocalThreshold(15 * time.Millisecond).                                          // Latency threshold for server selection
		SetRetryWrites(true).                                                              // Enable retryable writes
		SetRetryReads(true).                                                               // Enable retryable reads
		SetWriteConcern(writeconcern.New(writeconcern.WMajority(), writeconcern.J(true))). // Majority write concern with a journal
		SetReadPreference(readpref.Primary())                                              // Read from primary for consistency

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to mongodb: %w", err)
	}

	// Test connection
	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		return nil, fmt.Errorf("failed to ping mongodb: %w", err)
	}

	log.Println("Successfully connected to MongoDB with optimized connection pool")
	return client, nil
}

func GetDatabase(client *mongo.Client, dbName string) *mongo.Database {
	return client.Database(dbName)
}

// WithTransaction Transaction helper function for atomic operations
func WithTransaction(ctx context.Context, client *mongo.Client, fn func(mongo.SessionContext) error) error {
	session, err := client.StartSession()
	if err != nil {
		return fmt.Errorf("failed to start session: %w", err)
	}
	defer session.EndSession(ctx)

	// Transaction options with writing concern
	txnOpts := options.Transaction().
		SetWriteConcern(writeconcern.New(writeconcern.WMajority(), writeconcern.J(true)))

	// Wrapper function to match the expected signature
	transactionFn := func(sc mongo.SessionContext) (interface{}, error) {
		err := fn(sc)
		return nil, err
	}

	return mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
		_, err := session.WithTransaction(sc, transactionFn, txnOpts)
		return err
	})
}

// AtomicIncrement Atomic increment helper for engagement counts
func AtomicIncrement(ctx context.Context, collection *mongo.Collection, filter bson.M, field string, increment int64) error {
	update := bson.M{"$inc": bson.M{field: increment}}
	_, err := collection.UpdateOne(ctx, filter, update)
	return err
}

// AtomicDecrement Atomic decrement helper for engagement counts
func AtomicDecrement(ctx context.Context, collection *mongo.Collection, filter bson.M, field string, decrement int64) error {
	update := bson.M{"$inc": bson.M{field: -decrement}}
	_, err := collection.UpdateOne(ctx, filter, update)
	return err
}

// IsTransactionUnsupportedError checks if an error indicates transactions are not supported
func IsTransactionUnsupportedError(err error) bool {
	if err == nil {
		return false
	}

	if mongo.IsTimeout(err) {
		return true
	}

	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "transaction numbers are only allowed") ||
		strings.Contains(errMsg, "not supported") ||
		strings.Contains(errMsg, "replica set") ||
		strings.Contains(errMsg, "sessions are not supported")
}
