package main

import (
	"flag"
	"log"
	"os"

	"github.com/joho/godotenv"
	"github.com/swork-team/platform/pkg/database"
	"github.com/swork-team/platform/pkg/models"
)

func main() {
	var service = flag.String("service", "all", "Service to migrate (all, auth, user, social, team, drive, calendar, notification, messaging)")
	flag.Parse()

	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	switch *service {
	case "all":
		migrateAll()
	case "auth":
		migrateAuth()
	case "user":
		migrateUser()
	case "social":
		migrateSocial()
	case "team":
		migrateTeam()
	case "drive":
		migrateDrive()
	case "calendar":
		migrateCalendar()
	case "notification":
		migrateNotification()
	case "messaging":
		migrateMessaging()
	default:
		log.Fatalf("Unknown service: %s", *service)
	}

	log.Println("Migration completed successfully")
}

func migrateAll() {
	log.Println("Running all migrations...")
	migrateAuth()
	migrateUser()
	migrateSocial()
	migrateTeam()
	migrateDrive()
	migrateCalendar()
	migrateNotification()
	migrateMessaging()
}

func migrateAuth() {
	log.Println("Migrating auth service...")

	dbURL := getEnv("AUTH_DATABASE_URL", "")
	db, err := database.NewPostgresConnection(dbURL)
	if err != nil {
		log.Fatalf("Failed to connect to auth database: %v", err)
	}

	// Use shared user model for auth service
	if err := db.AutoMigrate(&models.User{}); err != nil {
		log.Fatalf("Failed to migrate auth database: %v", err)
	}

	log.Println("Auth service migration completed")
}

func migrateUser() {
	log.Println("Migrating user service...")

	dbURL := getEnv("USER_DATABASE_URL", "")
	db, err := database.NewPostgresConnection(dbURL)
	if err != nil {
		log.Fatalf("Failed to connect to user database: %v", err)
	}

	// Use shared user model
	if err := db.AutoMigrate(&models.User{}); err != nil {
		log.Fatalf("Failed to migrate user database: %v", err)
	}

	log.Println("User service migration completed")
}

func migrateSocial() {
	log.Println("Migrating social service...")

	// Social service uses MongoDB, so no migrations needed
	// MongoDB collections are created automatically
	log.Println("Social service migration completed (MongoDB collections auto-created)")
}

func migrateTeam() {
	log.Println("Migrating team service...")

	dbURL := getEnv("TEAM_DATABASE_URL", "")
	db, err := database.NewPostgresConnection(dbURL)
	if err != nil {
		log.Fatalf("Failed to connect to team database: %v", err)
	}

	// Use shared team models
	if err := db.AutoMigrate(
		&models.Team{},
		&models.TeamMember{},
	); err != nil {
		log.Fatalf("Failed to migrate team database: %v", err)
	}

	log.Println("Team service migration completed")
}

func migrateDrive() {
	log.Println("Migrating drive service...")

	// Drive service migrations are handled by individual service
	// This avoids importing internal models
	log.Println("Drive service migration skipped (handled by service)")
}

func migrateCalendar() {
	log.Println("Migrating calendar service...")

	// Calendar service migrations are handled by individual service
	log.Println("Calendar service migration skipped (handled by service)")
}

func migrateNotification() {
	log.Println("Migrating notification service...")

	// Notification service migrations are handled by individual service
	log.Println("Notification service migration skipped (handled by service)")
}

func migrateMessaging() {
	log.Println("Migrating messaging service...")

	// Messaging service uses SQL migrations for PostgreSQL and MongoDB auto-creation
	// Run SQL migrations using the init-db.sh script
	log.Println("Messaging service migration handled by init-db.sh script")
	log.Println("PostgreSQL: Run ./services/messaging/scripts/init-db.sh")
	log.Println("MongoDB: Collections auto-created with proper indexes")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
