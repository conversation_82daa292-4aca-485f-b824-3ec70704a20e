// @title SWork Team Platform API
// @version 1.0
// @description This is a comprehensive API for the SWork team collaboration platform.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8000
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	// _ "github.com/swork-team/platform/docs" // Temporarily disabled for initial build
	"github.com/swork-team/platform/pkg/config"
	"github.com/swork-team/platform/pkg/database"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
)

type GatewayConfig struct {
	Server                 config.ServerConfig
	Redis                  config.RedisConfig
	AuthServiceURL         string
	UserServiceURL         string
	SocialServiceURL       string
	TeamServiceURL         string
	DriveServiceURL        string
	CalendarServiceURL     string
	NotificationServiceURL string
	MessagingServiceURL    string
	SearchServiceURL       string
}

func loadConfig() *GatewayConfig {
	config.LoadEnvFile()

	return &GatewayConfig{
		Server:                 config.NewServerConfig("PORT", "HOST", "8080", "0.0.0.0"),
		Redis:                  config.NewRedisConfig(),
		AuthServiceURL:         config.GetEnv("AUTH_SERVICE_URL", "http://localhost:8001"),
		UserServiceURL:         config.GetEnv("USER_SERVICE_URL", "http://localhost:8002"),
		SocialServiceURL:       config.GetEnv("SOCIAL_SERVICE_URL", "http://localhost:8003"),
		TeamServiceURL:         config.GetEnv("TEAM_SERVICE_URL", "http://localhost:8004"),
		DriveServiceURL:        config.GetEnv("DRIVE_SERVICE_URL", "http://localhost:8006"),
		CalendarServiceURL:     config.GetEnv("CALENDAR_SERVICE_URL", "http://localhost:8007"),
		NotificationServiceURL: config.GetEnv("NOTIFICATION_SERVICE_URL", "http://localhost:8008"),
		MessagingServiceURL:    config.GetEnv("MESSAGING_SERVICE_URL", "http://localhost:8005"),
		SearchServiceURL:       config.GetEnv("SEARCH_SERVICE_URL", "http://localhost:8009"),
	}
}

func main() {
	cfg := loadConfig()

	redisClient := database.NewRedisClient(&cfg.Redis)

	router := setupRouter(cfg, redisClient)

	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	go func() {
		log.Printf("API Gateway starting on %s:%s", cfg.Server.Host, cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down API Gateway...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("API Gateway exited")
}

func setupRouter(cfg *GatewayConfig, redisClient interface{}) *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// Disable automatic redirect for trailing slashes
	router.RedirectTrailingSlash = false

	// Production-ready middleware chain
	router.Use(gin.LoggerWithConfig(gin.LoggerConfig{
		Formatter: func(param gin.LogFormatterParams) string {
			return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
				param.ClientIP,
				param.TimeStamp.Format(time.RFC3339),
				param.Method,
				param.Path,
				param.Request.Proto,
				param.StatusCode,
				param.Latency,
				param.Request.UserAgent(),
				param.ErrorMessage,
			)
		},
		Output:    gin.DefaultWriter,
		SkipPaths: []string{"/health", "/swagger"},
	}))

	router.Use(gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		log.Printf("Panic recovered: %v", recovered)
		c.Header("Content-Type", "application/json")
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"error": "Internal server error",
			"code":  "INTERNAL_ERROR",
		})
	}))

	// Security middleware
	router.Use(func(c *gin.Context) {
		// Security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("X-Gateway-Version", "1.0.0")
		c.Next()
	})

	router.Use(middleware.CORSMiddleware())

	// Swagger documentation with custom handler for root path
	router.GET("/swagger/*any", func(c *gin.Context) {
		path := c.Param("any")
		// If accessing /swagger/ (empty path), redirect to index.html
		if path == "/" || path == "" {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
			return
		}
		// Otherwise, use the standard swagger handler
		ginSwagger.WrapHandler(swaggerFiles.Handler)(c)
	})

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		utils.SuccessResponse(c, http.StatusOK, "API Gateway is healthy", map[string]string{
			"service": "api-gateway",
			"status":  "healthy",
		})
	})

	// Service status endpoint
	router.GET("/status", func(c *gin.Context) {
		status := checkServicesHealth(cfg)
		utils.SuccessResponse(c, http.StatusOK, "Service status", status)
	})

	// Request size limiting middleware (32MB max)
	router.Use(func(c *gin.Context) {
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 32<<20)
		c.Next()
	})

	// Setup rate limiting if Redis is available
	if redisClient != nil {
		// TODO: Implement rate limiting with Redis
		// rateLimiter := middleware.NewRateLimiter(redisClient.(*redis.Client))
		// router.Use(rateLimiter.LimitByIP(1000, time.Hour))
		log.Println("Rate limiting available - Redis connected")
	} else {
		log.Println("Warning: Rate limiting disabled - Redis not available")
	}

	// Auth service routes (no auth required)
	authGroup := router.Group("/api/v1/auth")
	{
		authGroup.Any("/*path", createProxy(cfg.AuthServiceURL, "/auth"))
	}

	// User service routes
	userGroup := router.Group("/api/v1/users")
	userGroup.Use(middleware.OptionalAuthMiddleware()) // Some endpoints don't require auth
	{
		userGroup.Any("/*path", createProxy(cfg.UserServiceURL, "/users"))
	}

	// Social service routes with conditional authentication
	socialGroup := router.Group("/api/v1/social")
	socialGroup.Use(createConditionalAuthMiddleware())
	{
		socialGroup.Any("/*path", createProxy(cfg.SocialServiceURL, "/social"))
	}

	// Team service routes - explicit registration to avoid trailing slash redirect
	router.GET("/api/v1/teams", middleware.AuthMiddleware(), createProxy(cfg.TeamServiceURL, "/teams"))
	router.POST("/api/v1/teams", middleware.AuthMiddleware(), createProxy(cfg.TeamServiceURL, "/teams"))
	router.PUT("/api/v1/teams", middleware.AuthMiddleware(), createProxy(cfg.TeamServiceURL, "/teams"))
	router.DELETE("/api/v1/teams", middleware.AuthMiddleware(), createProxy(cfg.TeamServiceURL, "/teams"))
	router.Any("/api/v1/teams/*path", middleware.AuthMiddleware(), createProxy(cfg.TeamServiceURL, "/teams"))

	// Drive service routes
	driveGroup := router.Group("/api/v1/drive")
	driveGroup.Use(middleware.AuthMiddleware())
	{
		driveGroup.Any("/*path", createProxy(cfg.DriveServiceURL, "/api/v1/drive"))
	}

	// Drive service public routes (no auth required)
	drivePublicGroup := router.Group("/api/v1/public")
	{
		drivePublicGroup.Any("/*path", createProxy(cfg.DriveServiceURL, "/api/v1/public"))
	}

	// Drive service admin routes (requires auth + admin role)
	driveAdminGroup := router.Group("/api/v1/admin")
	driveAdminGroup.Use(middleware.AuthMiddleware())
	{
		driveAdminGroup.Any("/*path", createProxy(cfg.DriveServiceURL, "/api/v1/admin"))
	}

	// Calendar service routes
	calendarGroup := router.Group("/api/v1/calendar")
	calendarGroup.Use(middleware.AuthMiddleware())
	{
		calendarGroup.Any("/*path", createProxy(cfg.CalendarServiceURL, "/calendar"))
	}

	// Notification service routes
	notificationGroup := router.Group("/api/v1/notifications")
	notificationGroup.Use(middleware.AuthMiddleware())
	{
		notificationGroup.Any("/*path", createProxy(cfg.NotificationServiceURL, "/notifications"))
	}

	// Messaging service routes with conditional authentication
	messagingGroup := router.Group("/api/v1/messaging")
	messagingGroup.Use(createMessagingConditionalAuthMiddleware())
	{
		messagingGroup.Any("/*path", createProxy(cfg.MessagingServiceURL, "/api/v1"))
	}

	// Search service routes
	searchGroup := router.Group("/api/v1/search")
	searchGroup.Use(middleware.AuthMiddleware())
	{
		searchGroup.Any("/*path", createProxy(cfg.SearchServiceURL, "/search"))
	}

	return router
}

// createConditionalAuthMiddleware checks if the route requires authentication
func createConditionalAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// Define public endpoints that don't require authentication
		publicEndpoints := []string{
			"/api/v1/social/feed/public",
			"/api/v1/social/feed/trending",
		}

		// Check if path starts with any public endpoint patterns
		publicPatterns := []string{
			"/api/v1/social/posts/",    // GET /posts/:id and related endpoints
			"/api/v1/social/users/",    // GET /users/:user_id/posts
			"/api/v1/social/comments/", // GET /comments/:id/replies and likes
		}

		isPublic := false

		// Check exact matches first
		for _, endpoint := range publicEndpoints {
			if path == endpoint {
				isPublic = true
				break
			}
		}

		// Check pattern matches for GET requests only with strict validation
		if !isPublic && c.Request.Method == "GET" {
			// Define allowed public GET endpoints with exact patterns
			allowedPublicGETPaths := map[string]bool{
				// Posts and their public sub-resources
				"/api/v1/social/posts/comments":        true, // GET /posts/:id/comments
				"/api/v1/social/posts/likes":           true, // GET /posts/:id/likes (deprecated)
				"/api/v1/social/posts/reactions":       true, // GET /posts/:id/reactions
				"/api/v1/social/posts/reactions/users": true, // GET /posts/:id/reactions/users

				// Comments and their public sub-resources
				"/api/v1/social/comments/replies":         true, // GET /comments/:id/replies
				"/api/v1/social/comments/likes":           true, // GET /comments/:id/likes (deprecated)
				"/api/v1/social/comments/reactions":       true, // GET /comments/:id/reactions
				"/api/v1/social/comments/reactions/users": true, // GET /comments/:id/reactions/users
			}

			// Check for individual post access: /posts/:id
			if strings.HasPrefix(path, "/api/v1/social/posts/") {
				pathParts := strings.Split(strings.TrimPrefix(path, "/api/v1/social/posts/"), "/")
				// Allow only /posts/:id (single path segment that is not empty)
				if len(pathParts) == 1 && pathParts[0] != "" && !strings.Contains(pathParts[0], "/") {
					isPublic = true
				}
			}

			// Check for user posts: /users/:user_id/posts
			if strings.HasPrefix(path, "/api/v1/social/users/") && strings.HasSuffix(path, "/posts") {
				pathParts := strings.Split(strings.TrimPrefix(path, "/api/v1/social/users/"), "/")
				// Ensure it's exactly /users/:user_id/posts (2 segments: user_id and "posts")
				if len(pathParts) == 2 && pathParts[0] != "" && pathParts[1] == "posts" {
					isPublic = true
				}
			}

			// Check for allowed comment/post sub-resources with ID validation
			if !isPublic {
				for _, pattern := range publicPatterns {
					if strings.HasPrefix(path, pattern) {
						// Extract the base path without ID for validation
						var basePath string
						var pathParts []string

						if strings.HasPrefix(path, "/api/v1/social/posts/") {
							pathParts = strings.Split(strings.TrimPrefix(path, "/api/v1/social/posts/"), "/")
							if len(pathParts) >= 2 && pathParts[0] != "" {
								basePath = "/api/v1/social/posts/" + strings.Join(pathParts[1:], "/")
							}
						} else if strings.HasPrefix(path, "/api/v1/social/comments/") {
							pathParts = strings.Split(strings.TrimPrefix(path, "/api/v1/social/comments/"), "/")
							if len(pathParts) >= 2 && pathParts[0] != "" {
								basePath = "/api/v1/social/comments/" + strings.Join(pathParts[1:], "/")
							}
						}

						// Check if the base path (without ID) is in the allowed list
						if basePath != "" && allowedPublicGETPaths[basePath] {
							isPublic = true
							break
						}
					}
				}
			}
		}

		// Apply authentication middleware only if endpoint is not public
		if !isPublic {
			log.Printf("Gateway: Applying auth middleware for %s %s", c.Request.Method, path)
			middleware.AuthMiddleware()(c)
		} else {
			log.Printf("Gateway: Allowing public access with optional auth for %s %s", c.Request.Method, path)
			// Apply optional auth middleware for public endpoints so authenticated users get user-specific data
			middleware.OptionalAuthMiddleware()(c)
		}
	}
}

// createMessagingConditionalAuthMiddleware checks if the messaging route requires authentication
func createMessagingConditionalAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		log.Printf("Gateway: *** CONDITIONAL MIDDLEWARE CALLED FOR PATH: %s", path)

		// TEMPORARY: Skip all authentication for debugging
		log.Printf("Gateway: Skipping authentication for debugging")
		c.Next()
	}
}

func createProxy(targetURL, servicePath string) gin.HandlerFunc {
	target, err := url.Parse(targetURL)
	if err != nil {
		log.Fatalf("Failed to parse target URL %s: %v", targetURL, err)
	}

	proxy := httputil.NewSingleHostReverseProxy(target)

	// Remove CORS headers coming from downstream service to prevent duplicates
	proxy.ModifyResponse = func(resp *http.Response) error {
		corsHeaders := []string{
			"Access-Control-Allow-Origin",
			"Access-Control-Allow-Credentials",
			"Access-Control-Allow-Headers",
			"Access-Control-Allow-Methods",
			"Access-Control-Max-Age",
		}
		for _, h := range corsHeaders {
			resp.Header.Del(h)
		}
		return nil
	}

	// Configure proxy with production-ready settings
	proxy.Transport = &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
		DisableKeepAlives:   false,
	}

	// Custom error handler
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		log.Printf("Proxy error for %s: %v", r.URL.Path, err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadGateway)
		w.Write([]byte(`{"error":"Service temporarily unavailable","code":"PROXY_ERROR"}`))
	}

	return func(c *gin.Context) {
		// Create a timeout context for the request
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
		defer cancel()
		c.Request = c.Request.WithContext(ctx)

		// Build the target path
		originalPath := c.Request.URL.Path
		path := c.Param("path")
		
		// Special handling for empty path to avoid double slashes
		var newPath string
		if path == "" || path == "/" {
			newPath = servicePath
		} else {
			newPath = servicePath + path
		}
		
		// Clean up double slashes
		newPath = strings.ReplaceAll(newPath, "//", "/")

		log.Printf("Gateway proxy: %s -> %s%s", originalPath, targetURL, newPath)
		c.Request.URL.Path = newPath

		// Set forwarding headers
		c.Request.Header.Set("X-Forwarded-Host", c.Request.Host)
		c.Request.Header.Set("X-Forwarded-Proto", getScheme(c.Request.TLS != nil))
		c.Request.Header.Set("X-Forwarded-For", c.ClientIP())
		c.Request.Header.Set("X-Real-IP", c.ClientIP())

		// Forward authentication context if available
		if userID, exists := c.Get("user_id"); exists {
			c.Request.Header.Set("X-User-ID", userID.(string))
		}
		if email, exists := c.Get("email"); exists {
			c.Request.Header.Set("X-User-Email", email.(string))
		}
		if roles, exists := c.Get("roles"); exists {
			if roleSlice, ok := roles.([]string); ok {
				c.Request.Header.Set("X-User-Roles", strings.Join(roleSlice, ","))
			}
		}
		if teams, exists := c.Get("teams"); exists {
			if teamSlice, ok := teams.([]string); ok {
				c.Request.Header.Set("X-User-Teams", strings.Join(teamSlice, ","))
			}
		}

		// Add request tracing
		c.Request.Header.Set("X-Request-ID", generateRequestID())
		c.Request.Header.Set("X-Gateway-Timestamp", time.Now().UTC().Format(time.RFC3339))

		proxy.ServeHTTP(c.Writer, c.Request)

		// Restore original path
		c.Request.URL.Path = originalPath
	}
}

func getScheme(isTLS bool) string {
	if isTLS {
		return "https"
	}
	return "http"
}

func generateRequestID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Nanosecond())
}

func checkServicesHealth(cfg *GatewayConfig) map[string]interface{} {
	services := map[string]string{
		"auth":         cfg.AuthServiceURL + "/health",
		"user":         cfg.UserServiceURL + "/health",
		"social":       cfg.SocialServiceURL + "/health",
		"team":         cfg.TeamServiceURL + "/health",
		"drive":        cfg.DriveServiceURL + "/health",
		"calendar":     cfg.CalendarServiceURL + "/health",
		"notification": cfg.NotificationServiceURL + "/health",
		"messaging":    cfg.MessagingServiceURL + "/health",
		"search":       cfg.SearchServiceURL + "/health",
	}

	status := make(map[string]interface{})
	client := &http.Client{
		Timeout: 5 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:      10,
			IdleConnTimeout:   30 * time.Second,
			DisableKeepAlives: true,
		},
	}

	type serviceHealth struct {
		Status     string    `json:"status"`
		URL        string    `json:"url"`
		Latency    string    `json:"latency"`
		LastCheck  time.Time `json:"last_check"`
		StatusCode int       `json:"status_code,omitempty"`
		Error      string    `json:"error,omitempty"`
	}

	for name, healthURL := range services {
		start := time.Now()
		health := serviceHealth{
			URL:       healthURL,
			LastCheck: start,
		}

		resp, err := client.Get(healthURL)
		latency := time.Since(start)
		health.Latency = latency.String()

		if err != nil {
			health.Status = "unhealthy"
			health.Error = err.Error()
		} else {
			health.StatusCode = resp.StatusCode
			resp.Body.Close()

			if resp.StatusCode == http.StatusOK {
				health.Status = "healthy"
			} else {
				health.Status = "unhealthy"
				health.Error = fmt.Sprintf("HTTP %d", resp.StatusCode)
			}
		}

		status[name] = health
	}

	return status
}
