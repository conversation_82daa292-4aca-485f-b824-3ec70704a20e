#!/bin/bash
# Environment Variable Validation Script
# This script validates that all required security-sensitive environment variables are set
# and do not contain insecure default values

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔒 Validating environment variables for security...${NC}"

# Track validation status
VALIDATION_FAILED=false

# Function to validate a required environment variable
validate_required_var() {
    local var_name=$1
    local var_value=${!var_name:-}
    local forbidden_values=("${@:2}")
    
    if [[ -z "$var_value" ]]; then
        echo -e "${RED}❌ ERROR: $var_name is required but not set${NC}"
        VALIDATION_FAILED=true
        return 1
    fi
    
    # Check against forbidden values
    for forbidden in "${forbidden_values[@]}"; do
        if [[ "$var_value" == "$forbidden" ]]; then
            echo -e "${RED}❌ ERROR: $var_name contains insecure default value: $forbidden${NC}"
            VALIDATION_FAILED=true
            return 1
        fi
    done
    
    # Check for placeholder values
    if [[ "$var_value" == *"CHANGE_ME"* ]]; then
        echo -e "${RED}❌ ERROR: $var_name contains placeholder value. Please generate a secure value.${NC}"
        VALIDATION_FAILED=true
        return 1
    fi
    
    echo -e "${GREEN}✅ $var_name is properly configured${NC}"
    return 0
}

# Function to validate minimum length
validate_min_length() {
    local var_name=$1
    local var_value=${!var_name:-}
    local min_length=$2
    
    if [[ ${#var_value} -lt $min_length ]]; then
        echo -e "${RED}❌ ERROR: $var_name must be at least $min_length characters long${NC}"
        VALIDATION_FAILED=true
        return 1
    fi
    
    return 0
}

echo -e "${YELLOW}Validating database credentials...${NC}"
validate_required_var "POSTGRES_PASSWORD" ""
validate_min_length "POSTGRES_PASSWORD" 16

validate_required_var "MONGO_PASSWORD" ""
validate_min_length "MONGO_PASSWORD" 16

echo -e "${YELLOW}Validating JWT and service tokens...${NC}"
validate_required_var "JWT_SECRET" ""
validate_min_length "JWT_SECRET" 32

validate_required_var "SERVICE_TOKEN" ""
validate_min_length "SERVICE_TOKEN" 24

echo -e "${YELLOW}Validating MinIO credentials...${NC}"
validate_required_var "MINIO_ROOT_USER" "minioadmin" "admin" "root"
validate_required_var "MINIO_ROOT_PASSWORD" "minioadmin" "password" "admin"
validate_min_length "MINIO_ROOT_PASSWORD" 16

# Additional security checks
echo -e "${YELLOW}Performing additional security checks...${NC}"

# Check if running in development mode
if [[ "${APP_ENV:-}" == "development" ]]; then
    echo -e "${YELLOW}⚠️  WARNING: Running in development mode. Ensure production environment variables are set for production deployment.${NC}"
fi

# Check if debug mode is enabled
if [[ "${GIN_MODE:-}" == "debug" ]]; then
    echo -e "${YELLOW}⚠️  WARNING: Debug mode enabled. Disable for production deployment.${NC}"
fi

# Final validation result
if [[ "$VALIDATION_FAILED" == true ]]; then
    echo -e "${RED}❌ Environment validation FAILED. Please fix the issues above before starting services.${NC}"
    echo -e "${YELLOW}💡 Use these commands to generate secure values:${NC}"
    echo -e "   ${GREEN}# For passwords and secrets:${NC}"
    echo -e "   openssl rand -base64 32"
    echo -e "   ${GREEN}# For JWT secrets (longer):${NC}"
    echo -e "   openssl rand -base64 64"
    echo -e "   ${GREEN}# For MinIO username:${NC}"
    echo -e "   echo \"swork-admin-\$(openssl rand -hex 4)\""
    exit 1
else
    echo -e "${GREEN}✅ All environment variables are properly configured for security!${NC}"
    echo -e "${GREEN}🚀 Safe to start services.${NC}"
fi