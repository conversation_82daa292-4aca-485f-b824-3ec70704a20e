#!/bin/bash
#
# Sequential Build and Start Script
# Builds and starts services sequentially to avoid memory issues,
# with an option to skip specified services.
#
# Usage:
#   SKIP_SERVICES="service1,service2" ./scripts/sequential-build.sh

set -e

# --- Configuration ---
# List of all services in their build order.
ALL_SERVICES=(
    "auth"
    "user"
    "team"
    "drive"
    "calendar"
    "notification"
    "social"
    "messaging"
    "search"
    "gateway"
)

# List of infrastructure services managed by docker-compose but not built here.
INFRA_SERVICES=("postgres" "mongodb" "redis" "minio" "qdrant")

# --- Logic ---

SERVICES_TO_START=("${INFRA_SERVICES[@]}")

echo "🚀 Starting sequential build..."
if [ -n "$SKIP_SERVICES" ]; then
    echo "🟡 Skipping services: ${SKIP_SERVICES}"
fi

for service in "${ALL_SERVICES[@]}"; do
    # Check if the current service is in the comma-separated SKIP_SERVICES list
    if [[ ",$SKIP_SERVICES," == *",$service,"* ]] || [[ ",$SKIP_SERVICES," == *",${service}-service,"* ]]; then
        echo "  -> Skipping build for ${service}-service."
        continue
    fi

    # Build the service
    echo "  -> 🔨 Building ${service}-service..."
    if ! ./scripts/build-optimized.sh "$service"; then
        echo "❌ Failed to build ${service}-service"
        exit 1
    fi

    # Add to the list of services to start with docker-compose
    compose_service_name=$( [[ "$service" == "gateway" ]] && echo "api-gateway" || echo "${service}-service" )
    SERVICES_TO_START+=("$compose_service_name")
done

echo "✅ All selected services built successfully."
echo "🚀 Starting services with docker-compose..."

docker-compose up -d "${SERVICES_TO_START[@]}"

echo "🎉 System started with specified services."