// MongoDB initialization script
db = db.getSiblingDB('admin');

// Create databases for services that use MongoDB
const databases = ['swork_social', 'swork_notification', 'swork_messaging'];

databases.forEach(function(dbName) {
    print('Creating database: ' + dbName);
    db = db.getSiblingDB(dbName);
    
    // Create a dummy collection to initialize the database
    db.createCollection('_init');
    
    print('Database ' + dbName + ' created successfully');
});

print('All MongoDB databases initialized successfully!');