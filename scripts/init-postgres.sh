#!/bin/bash
set -e

# Create multiple databases and enable UUID extension
for db in swork_auth swork_user swork_team swork_drive swork_calendar swork_notification swork_messaging; do
    echo "Creating database: $db"
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        CREATE DATABASE $db;
        GRANT ALL PRIVILEGES ON DATABASE $db TO $POSTGRES_USER;
EOSQL

    echo "Enabling UUID extension for database: $db"
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$db" <<-EOSQL
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
EOSQL
done

echo "All databases created with UUID extension enabled successfully!"