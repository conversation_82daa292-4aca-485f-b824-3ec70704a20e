-- Initialize multiple databases for different services
CREATE DATABASE swork_auth;
CREATE DATABASE swork_user;  
CREATE DATABASE swork_team;
CREATE DATABASE swork_drive;
CREATE DATABASE swork_calendar;
CREATE DATABASE swork_notification;

-- Enable UUID extension in all databases
\c swork_auth;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c swork_user;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c swork_team;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c swork_drive;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c swork_calendar;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c swork_notification;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE swork TO swork_user;
GRANT ALL PRIVILEGES ON DATABASE swork_auth TO swork_user;
GRANT ALL PRIVILEGES ON DATABASE swork_user TO swork_user;
GRANT ALL PRIVILEGES ON DATABASE swork_team TO swork_user;
GRANT ALL PRIVILEGES ON DATABASE swork_drive TO swork_user;
GRANT ALL PRIVILEGES ON DATABASE swork_calendar TO swork_user;
GRANT ALL PRIVILEGES ON DATABASE swork_notification TO swork_user;