# Swork Team Platform - Development Makefile

.PHONY: help dev dev-build dev-down dev-clean dev-logs dev-status dev-infra
.PHONY: fast-build rebuild restart logs health test build clean install docs

# Default target
help: ## Show this help message
	@echo "Swork Team Platform - Development Commands"
	@echo "==========================================="
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  %-15s %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

# Development commands
dev: ## Start all services in development mode
	@echo "🚀 Starting Swork Team Platform in development mode..."
	@chmod +x scripts/init-postgres.sh
	docker-compose up -d

dev-build: ## Build and start all services (memory-safe sequential build)
	@echo "🚀 Building with memory-safe sequential approach..."
	@chmod +x scripts/build-optimized.sh scripts/init-postgres.sh
	./scripts/build-optimized.sh bases
	@echo "🔨 Building services sequentially to prevent memory issues..."
	@echo "⚠️  Calendar service is currently disabled due to compilation error"
	@./scripts/sequential-build.sh skip-calendar

dev-down: ## Stop all services
	@echo "🛑 Stopping Swork Team Platform..."
	docker-compose down

dev-clean: ## Stop services and remove volumes
	@echo "🧹 Cleaning up Swork Team Platform..."
	docker-compose down -v --remove-orphans
	docker system prune -f

dev-logs: ## Show logs from all services
	docker-compose logs -f

dev-status: ## Show status of all services
	@echo "📊 Swork Team Platform Service Status:"
	@echo "======================================"
	docker-compose ps

dev-infra: ## Start infrastructure services only (postgres, mongodb, redis, api-gateway) for local development
	@echo "🏗️ Starting infrastructure services for local development..."
	@echo "Services: PostgreSQL, MongoDB, Redis, Minio, API Gateway"
	@chmod +x scripts/init-postgres.sh
	docker-compose up -d postgres mongodb redis minio api-gateway
	@echo "✅ Infrastructure services started!"
	@echo ""
	@echo "📋 Available services:"
	@echo "API Gateway:        http://localhost:8000"
	@echo "PostgreSQL:         localhost:5432 (swork/swork123)"
	@echo "MongoDB:            localhost:27017 (swork/swork123)"
	@echo "Redis:              localhost:6379"
	@echo "Minio:              http://localhost:9001 (minioadmin/minioadmin)"
	@echo ""
	@echo "💡 Now you can run Go services locally:"
	@echo "cd services/auth && go run cmd/main.go"
	@echo "cd services/user && go run cmd/main.go"
	@echo "# etc..."

# Build commands
fast-build: ## Fast service build (usage: make fast-build SERVICE=auth)
	@if [ -z "$(SERVICE)" ]; then \
		echo "❌ Specify service: make fast-build SERVICE=auth"; \
		echo "Services: auth, user, social, team, drive, notification, gateway"; \
		exit 1; \
	fi
	@if [ "$(SERVICE)" = "calendar" ]; then \
		echo "⚠️ Calendar service is currently disabled due to compilation error"; \
		exit 0; \
	fi
	@chmod +x scripts/build-optimized.sh
	./scripts/build-optimized.sh $(SERVICE)

# Service commands
rebuild: ## Rebuild service (usage: make rebuild SERVICE=auth-service)
	@if [ -z "$(SERVICE)" ]; then echo "❌ Specify: make rebuild SERVICE=auth-service"; exit 1; fi
	@if [ "$(SERVICE)" = "calendar-service" ]; then \
		echo "⚠️ Calendar service is currently disabled due to compilation error"; \
		exit 0; \
	fi
	@make fast-build SERVICE=$(shell echo $(SERVICE) | sed 's/-service//')
	docker-compose restart $(SERVICE)

restart: ## Restart service (usage: make restart SERVICE=auth-service)
	@if [ -z "$(SERVICE)" ]; then echo "❌ Specify: make restart SERVICE=auth-service"; exit 1; fi
	docker-compose restart $(SERVICE)

logs: ## Follow service logs (usage: make logs SERVICE=auth-service)
	@if [ -z "$(SERVICE)" ]; then echo "❌ Specify: make logs SERVICE=auth-service"; exit 1; fi
	docker-compose logs -f $(SERVICE)

# Quick service rebuilds
rebuild-auth: ## Rebuild auth service
	@make fast-build SERVICE=auth && docker-compose restart auth-service

rebuild-user: ## Rebuild user service
	@make fast-build SERVICE=user && docker-compose restart user-service

rebuild-social: ## Rebuild social service
	@make fast-build SERVICE=social && docker-compose restart social-service

rebuild-team: ## Rebuild team service
	@make fast-build SERVICE=team && docker-compose restart team-service

rebuild-drive: ## Rebuild drive service
	@make fast-build SERVICE=drive && docker-compose restart drive-service

# Calendar service is currently disabled
# rebuild-calendar: ## Rebuild calendar service
# 	@make fast-build SERVICE=calendar && docker-compose restart calendar-service

rebuild-notification: ## Rebuild notification service
	@make fast-build SERVICE=notification && docker-compose restart notification-service

rebuild-gateway: ## Rebuild API gateway
	@make fast-build SERVICE=gateway && docker-compose restart api-gateway

# Database commands
db: ## Connect to database (usage: make db SERVICE=postgres/mongo/redis)
	@case "$(SERVICE)" in \
		postgres) docker-compose exec postgres psql -U swork -d swork ;; \
		mongo) docker-compose exec mongodb mongosh -u swork -p swork123 --authenticationDatabase admin ;; \
		redis) docker-compose exec redis redis-cli ;; \
		*) echo "❌ Specify: make db SERVICE=postgres/mongo/redis" ;; \
	esac

# Health checks
health: ## Check health of all services
	@echo "🏥 Checking service health..."
	@echo "API Gateway: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health || echo "Failed")"
	@echo "Auth Service: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/health || echo "Failed")"
	@echo "User Service: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8002/health || echo "Failed")"
	@echo "Social Service: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8003/health || echo "Failed")"
	@echo "Team Service: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8004/health || echo "Failed")"
	@echo "Drive Service: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8006/health || echo "Failed")"
	@echo "Calendar Service: SKIPPED (disabled due to compilation error)"
	@echo "Notification Service: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8008/health || echo "Failed")"

docs: ## Generate Swagger docs and rebuild gateway
	@which swag > /dev/null || go install github.com/swaggo/swag/cmd/swag@latest
	swag init -g cmd/gateway/main.go -o docs --exclude "services/team,services/user,services/notification,services/drive"
	@if docker-compose ps api-gateway | grep -q "Up"; then \
		docker-compose build api-gateway && docker-compose up -d api-gateway; \
	fi

# Go commands
install: ## Install Go dependencies
	go mod download
	go mod tidy

build: ## Build all services locally
	@echo "🔨 Building all services..."
	@for service in auth user social team drive notification; do \
		echo "Building $$service service..."; \
		cd services/$$service && go build -o ../../bin/$$service-service ./cmd/main.go && cd ../..; \
	done
	@echo "Building API gateway..."
	@cd cmd/gateway && go build -o ../../bin/api-gateway ./main.go

test: ## Run tests for all services
	@echo "🧪 Running tests..."
	go test ./...

clean: ## Clean build artifacts
	@echo "🧹 Cleaning build artifacts..."
	rm -rf bin/
	go clean ./...

# Setup
setup: ## Setup optimized build system
	@chmod +x scripts/build-optimized.sh
	./scripts/build-optimized.sh bases

quick-start: setup dev-build ## Quick start for new developers
	@echo "🎉 Platform running at http://localhost:8000"