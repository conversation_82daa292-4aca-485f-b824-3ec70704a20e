# 🚀 Swork Team Platform

<div align="center">

![Platform Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Go Version](https://img.shields.io/badge/go-1.23+-00ADD8.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)
![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)
![Docker](https://img.shields.io/badge/docker-ready-blue.svg)
![Kubernetes](https://img.shields.io/badge/k8s-ready-326CE5.svg)

**🌟 Enterprise-Grade Social Collaboration & Team Management Platform**

*Built with Go microservices architecture, designed for modern teams*

[🚀 Quick Start](#-quick-start) •
[📖 Documentation](#-documentation) •
[🏗️ Architecture](#%EF%B8%8F-architecture) •
[🌐 API Reference](#-api-reference) •
[⚡ Features](#-features)

</div>

---

## 📋 Table of Contents

1. [🌟 Platform Overview](#-platform-overview)
2. [⚡ Features](#-features)
3. [🏗️ Architecture](#%EF%B8%8F-architecture)
4. [🚀 Quick Start](#-quick-start)
5. [🔧 Development](#-development)
6. [🌐 API Reference](#-api-reference)
7. [🗃️ Database Design](#%EF%B8%8F-database-design)
8. [🔐 Security](#-security)
9. [🚀 Deployment](#-deployment)
10. [🧪 Testing](#-testing)
11. [📊 Monitoring](#-monitoring)
12. [🤝 Contributing](#-contributing)

---

## 🌟 Platform Overview

**Swork Team Platform** is a comprehensive, enterprise-grade social collaboration and team management system built with **modern microservices architecture**. Designed for high-performance, scalability, and developer experience.

### 🎯 **Mission**
Empower teams to collaborate seamlessly through integrated social features, file management, and productivity tools.

### ✨ **Key Highlights**

| Feature | Description | Technology |
|---------|-------------|------------|
| **🏗️ Microservices** | 7 independent services with clean boundaries | Go, Docker, Kubernetes |
| **🗄️ Polyglot Persistence** | Right database for each use case | PostgreSQL, MongoDB, Redis |
| **🔐 Modern Auth** | OTP-based registration, JWT tokens, MFA | HMAC-256, Bcrypt |
| **📱 Real-time Features** | Live notifications, presence | WebSocket, Redis Pub/Sub |
| **🌐 Production Ready** | Health checks, monitoring, graceful shutdown | Structured logging, Metrics |
| **📊 Enterprise Features** | File versioning, audit logs, permissions | RBAC, Content moderation |

---

## ⚡ Features

<table>
<tr>
<td width="50%">

### 👥 **Core Social Features**
- ✅ **Rich Posts** - Text, images, videos, polls, events
- ✅ **Engagement System** - 6 reaction types (like, love, haha, wow, sad, angry)
- ✅ **Comments & Replies** - Hierarchical discussion threads
- ✅ **Content Sharing** - Cross-team and public sharing
- ✅ **Activity Feeds** - Personalized, public, trending algorithms
- ✅ **Search & Discovery** - Full-text search with filters

### 🔐 **Authentication & Security**
- ✅ **OTP Registration** - Email/SMS verification
- ✅ **JWT Token Management** - Access + refresh token pattern
- ✅ **Multi-Factor Auth** - Additional security layers
- ✅ **Rate Limiting** - API abuse prevention
- ✅ **Input Validation** - XSS and injection protection
- ✅ **Audit Logging** - Comprehensive security trails

</td>
<td width="50%">

### 🏢 **Team Collaboration**
- ✅ **Team Management** - Hierarchical roles and permissions
- ✅ **Workspace Switching** - Multi-team context management
- ✅ **Invitation System** - Token-based team invitations
- ✅ **Permission Inheritance** - Granular access control
- ✅ **Team Analytics** - Activity and engagement metrics

### 📁 **Enterprise Drive**
- ✅ **File Versioning** - Complete version history
- ✅ **Content Deduplication** - Storage optimization
- ✅ **Chunked Uploads** - Large file support with resume
- ✅ **Thumbnail Generation** - Async image/video processing
- ✅ **Storage Redundancy** - Multi-provider with failover
- ✅ **Access Control** - Folder-level permissions

</td>
</tr>
<tr>
<td width="50%">

### 💬 **Real-time Messaging**
- ✅ **Multi-party Conversations** - Direct, group, channels
- ✅ **Message Threading** - Organized discussions
- ✅ **Typing Indicators** - Live user activity
- ✅ **Read Receipts** - Message status tracking
- ✅ **File Attachments** - Rich media support
- ✅ **Message Search** - Full-text conversation search

### 📅 **Calendar & Events**
- ✅ **Multi-calendar Support** - Personal and team calendars
- ✅ **Event Management** - Recurring events with attendees
- ✅ **Availability Tracking** - Scheduling optimization
- ✅ **Smart Reminders** - Multi-channel notifications
- ✅ **Conflict Detection** - Automatic scheduling conflicts

</td>
<td width="50%">

### 🔔 **Notification System**
- ✅ **Multi-channel Delivery** - Email, push, SMS, webhooks
- ✅ **User Preferences** - Granular notification controls
- ✅ **Template System** - Customizable notification templates
- ✅ **Delivery Tracking** - Retry logic and analytics
- ✅ **Quiet Hours** - Smart notification scheduling

### 🛠️ **Developer Experience**
- ✅ **Comprehensive APIs** - RESTful with OpenAPI docs
- ✅ **Health Monitoring** - Built-in health checks
- ✅ **Hot Reloading** - Fast development iteration
- ✅ **Docker Support** - One-command setup
- ✅ **K8s Ready** - Production deployment manifests

</td>
</tr>
</table>

---

## 🏗️ Architecture

### 🎨 **Microservices Design**

```mermaid
graph TB
    subgraph "🌐 Client Layer"
        WEB[🖥️ Web App]
        MOBILE[📱 Mobile App]
        API_CLIENT[🔧 API Clients]
    end
    
    subgraph "🚪 Gateway Layer"
        GATEWAY[🚪 API Gateway<br/>Port: 8000]
    end
    
    subgraph "🔐 Authentication Layer"
        AUTH[🔐 Auth Service<br/>Port: 8001]
    end
    
    subgraph "🏢 Core Services"
        USER[👥 User Service<br/>Port: 8002]
        SOCIAL[📱 Social Service<br/>Port: 8003]
        TEAM[🏢 Team Service<br/>Port: 8004]
    end
    
    subgraph "💬 Communication Services"
        NOTIFICATION[🔔 Notification<br/>Port: 8008]
    end
    
    subgraph "📁 Content Services"
        DRIVE[📁 Drive Service<br/>Port: 8006]
        CALENDAR[📅 Calendar<br/>Port: 8007]
    end
    
    subgraph "🗄️ Data Layer"
        PG[(🐘 PostgreSQL<br/>Port: 5432)]
        MONGO[(🍃 MongoDB<br/>Port: 27017)]
        REDIS[(⚡ Redis<br/>Port: 6379)]
        MINIO[(📦 MinIO<br/>Port: 9000)]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    API_CLIENT --> GATEWAY
    
    GATEWAY --> AUTH
    GATEWAY --> USER
    GATEWAY --> SOCIAL
    GATEWAY --> TEAM
    GATEWAY --> DRIVE
    GATEWAY --> CALENDAR
    GATEWAY --> NOTIFICATION
    
    AUTH --> PG
    USER --> PG
    TEAM --> PG
    DRIVE --> PG
    CALENDAR --> PG
    NOTIFICATION --> PG
    
    SOCIAL --> MONGO
    
    AUTH --> REDIS
    USER --> REDIS
    SOCIAL --> REDIS
    
    DRIVE --> MINIO
    SOCIAL --> MINIO
```

### 🎯 **Service Responsibilities**

<details>
<summary><strong>🔐 Authentication Service</strong> - Modern OTP-based authentication</summary>

**Database:** PostgreSQL  
**Features:**
- 📧 OTP-based registration (email/phone verification)
- 🔑 JWT token lifecycle management (access + refresh)
- 🔒 Password reset with time-limited tokens
- 🧹 Background cleanup jobs for expired records
- 🛡️ Security logging and audit trails

**Models:** `AuthUser`, `Session`, `EmailVerification`, `PasswordReset`, `OTPVerification`
</details>

<details>
<summary><strong>👥 User Service</strong> - User profiles and social relationships</summary>

**Database:** PostgreSQL  
**Features:**
- 👤 Complete user profile management (CRUD)
- 👥 Friend request system (pending/accepted states)
- 👥 Follow/unfollow functionality  
- 🚫 User blocking system
- 🔍 Advanced search (username/email lookup)
- ⚙️ User settings and preferences
- 🚦 Rate limiting (100 requests/minute per user)

**Models:** `User`, `FriendRequest`, `Follow`, `Block`, `UserSettings`
</details>

<details>
<summary><strong>📱 Social Service</strong> - Rich social media functionality</summary>

**Database:** MongoDB (document-oriented for scalability)  
**Features:**
- 📝 Multi-type posts (text, image, video, link, poll, event)
- 💬 Hierarchical comment system with replies
- ❤️ Rich reaction system (6 types: like, love, haha, wow, sad, angry)
- 📤 Content sharing with visibility controls
- 📰 Advanced feed algorithms (public, trending, personalized)
- 📍 Location-based posts with geospatial indexing
- 🛡️ Content moderation and reporting system
- 📎 File attachment integration with MinIO storage

**Models:** `Post`, `Comment`, `Like`, `Share`, `Report`, `Feed`
</details>

<details>
<summary><strong>🏢 Team Service</strong> - Workspace collaboration management</summary>

**Database:** PostgreSQL  
**Features:**
- 🏗️ Team creation with slug-based URLs
- 👑 Role-based permissions (member, admin, owner)
- 📧 Token-based invitation system
- 🗂️ Team categorization and discovery
- 🔄 User context switching between teams
- 📊 Team activity tracking and analytics
- ⚙️ Team settings and configuration

**Models:** `Team`, `TeamMember`, `TeamInvitation`, `TeamJoinRequest`, `TeamSettings`, `TeamActivity`, `UserContext`
</details>


<details>
<summary><strong>📁 Drive Service</strong> - Enterprise file storage</summary>

**Database:** PostgreSQL  
**Features:**
- ⬆️ Chunked file uploads for large files
- 🔄 Content deduplication with reference counting
- 📈 File versioning and rollback capabilities
- 🔐 Advanced permission system (read, write, admin)
- 📁 Folder hierarchy with permission inheritance
- 🤝 File sharing with expiration dates
- 🖼️ Thumbnail generation service
- 🌐 Multi-provider storage with failover (primary/replica)
- 🗑️ Garbage collection and storage optimization

**Models:** `File`, `Folder`, `FileShare`, `FolderShare`, `UploadSession`, `ContentStore`
</details>

<details>
<summary><strong>📅 Calendar Service</strong> - Event and schedule management</summary>

**Database:** PostgreSQL  
**Features:**
- 📅 Multiple calendar support per user
- 📝 Rich event management with attendees
- ⏰ Smart reminders with notification integration
- 🕐 Availability tracking and scheduling
- 📎 Event attachments and sharing
- 🔄 Background reminder processing
- 🔍 Conflict detection and resolution

**Models:** `Calendar`, `Event`, `EventAttendee`, `EventReminder`, `EventAttachment`, `CalendarShare`, `AvailabilitySlot`
</details>

<details>
<summary><strong>🔔 Notification Service</strong> - Multi-channel delivery</summary>

**Database:** PostgreSQL  
**Features:**
- 📧 Multi-channel delivery (in-app, email, push, SMS, webhook)
- ⚙️ Granular user preferences per type/channel
- 🌙 Quiet hours and priority filtering
- 📱 Device management for push notifications
- 📋 Notification templates and personalization
- 📊 Delivery tracking and retry logic
- 📈 Analytics and performance metrics

**Models:** `Notification`, `NotificationDelivery`, `UserNotificationPreference`, `Device`, `NotificationTemplate`, `NotificationStats`
</details>

### 🔄 **Data Flow Architecture**

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant Auth
    participant Social
    participant User
    participant Redis
    participant MongoDB
    
    Client->>Gateway: POST /api/v1/social/posts
    Gateway->>Auth: Validate JWT Token
    Auth-->>Gateway: User Context
    Gateway->>Social: Create Post Request
    Social->>User: Get User Details
    User-->>Social: User Profile
    Social->>Redis: Cache Post
    Social->>MongoDB: Save Post
    MongoDB-->>Social: Post Created
    Social-->>Gateway: Success Response
    Gateway-->>Client: Created Post
```

---

## 🚀 Quick Start

### 📋 **Prerequisites**

| Requirement | Version | Purpose |
|------------|---------|---------|
| **Docker** | 20.10+ | Container orchestration |
| **Docker Compose** | 2.0+ | Multi-service management |
| **Go** | 1.23+ | Local development (optional) |
| **Make** | Any | Convenience commands (optional) |

### ⚡ **One-Command Setup**

```bash
# 🚀 Start everything with health checks
make quick-start
```

This command will:
1. ✅ Check all system dependencies
2. 🔨 Build and start all services
3. 🏥 Run comprehensive health checks
4. 📋 Display service information and URLs

### 🛠️ **Manual Setup**

<details>
<summary><strong>📖 Step-by-step instructions</strong></summary>

```bash
# 1️⃣ Clone the repository
git clone <repository-url>
cd swork-team

# 2️⃣ Build and start all services
make dev-build

# 3️⃣ Verify everything is running
make health
make info

# 4️⃣ View logs (optional)
make dev-logs
```
</details>

### 🌐 **Service URLs**

After successful startup, services will be available at:

| Service | URL | Purpose |
|---------|-----|---------|
| **🚪 API Gateway** | http://localhost:8000 | Main API entry point |
| **📚 Swagger Docs** | http://localhost:8000/swagger/ | Interactive API documentation |
| **🔐 Auth Service** | http://localhost:8001 | Authentication endpoints |
| **👥 User Service** | http://localhost:8002 | User management |
| **📱 Social Service** | http://localhost:8003 | Social features |
| **🏢 Team Service** | http://localhost:8004 | Team collaboration |
| **📁 Drive Service** | http://localhost:8006 | File management |
| **📅 Calendar** | http://localhost:8007 | Event scheduling |
| **🔔 Notifications** | http://localhost:8008 | Notification delivery |

### 🗄️ **Database Access**

```bash
# 🐘 PostgreSQL
make db-postgres

# 🍃 MongoDB  
make db-mongo

# ⚡ Redis
make db-redis
```

---

## 🔧 Development

### 🏃‍♂️ **Development Workflow**

<table>
<tr>
<td width="50%">

#### 🚀 **Service Management**
```bash
# Start all services
make dev

# Build and start (after code changes)  
make dev-build

# Stop all services
make dev-down

# Clean up (removes volumes)
make dev-clean

# View logs
make dev-logs
```

#### 🔄 **Fast Iteration**
```bash
# Rebuild specific service
make rebuild SERVICE=auth-service

# Restart service
make restart SERVICE=user-service

# Follow service logs
make logs SERVICE=social-service
```

</td>
<td width="50%">

#### 🏗️ **Infrastructure Only**
```bash
# Start infrastructure for local dev
make dev-infra
# Starts: PostgreSQL, MongoDB, Redis, MinIO, Gateway

# Then run services locally:
cd services/auth && go run cmd/main.go
cd services/user && go run cmd/main.go
# etc...
```

#### 🧪 **Testing & Quality**
```bash
# Run all tests
make test

# Build all services locally
make build

# API testing
make test-api

# Health checks
make health
```

</td>
</tr>
</table>

### 🎯 **Service-Specific Commands**

Each service has dedicated rebuild commands for fast development:

```bash
make rebuild-auth        # Auth service
make rebuild-user        # User service  
make rebuild-social      # Social service
make rebuild-team        # Team service
make rebuild-drive       # Drive service
make rebuild-calendar    # Calendar service
make rebuild-notification # Notification service
make rebuild-gateway     # API Gateway
```

### 📝 **Code Style & Standards**

- **🔧 Language**: Go 1.23+ with modules
- **📐 Architecture**: Clean Architecture with DDD principles
- **🗄️ Database**: GORM for PostgreSQL, Official driver for MongoDB
- **🔍 Linting**: Built-in Go formatting with `gofmt`
- **📋 Documentation**: Comprehensive inline documentation
- **🧪 Testing**: Unit tests with table-driven patterns

---

## 🌐 API Reference

### 🔐 **Authentication**

All API requests require authentication via Bearer token (except public endpoints):

```bash
curl -H "Authorization: Bearer your-jwt-token" \
     -H "Content-Type: application/json" \
     http://localhost:8000/api/v1/endpoint
```

### 📊 **Response Format**

All APIs return consistent JSON responses:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  },
  "error": null
}
```

### 🚪 **API Gateway Routes**

All services are accessible through the API Gateway at `http://localhost:8000/api/v1/`:

<details>
<summary><strong>🔐 Authentication APIs</strong></summary>

```bash
# User Registration with OTP
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "secure123",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890"
}

# Verify OTP
POST /api/v1/auth/verify-otp
{
  "email": "<EMAIL>",
  "otp": "123456"
}

# User Login
POST /api/v1/auth/login
{
  "email": "<EMAIL>", 
  "password": "secure123"
}

# Refresh Token
POST /api/v1/auth/refresh
{
  "refresh_token": "your-refresh-token"
}

# Logout
POST /api/v1/auth/logout
```
</details>

<details>
<summary><strong>👥 User Management APIs</strong></summary>

```bash
# Get User Profile
GET /api/v1/users/profile

# Update Profile
PUT /api/v1/users/profile
{
  "first_name": "John",
  "last_name": "Doe Updated",
  "bio": "Software engineer",
  "location": "San Francisco"
}

# Search Users
GET /api/v1/users/search?q=john&limit=10

# Send Friend Request
POST /api/v1/users/friend-request
{
  "receiver_id": "user-uuid"
}

# Accept Friend Request  
PUT /api/v1/users/friend-request/accept
{
  "request_id": "request-uuid"
}

# Get Friends List
GET /api/v1/users/friends?limit=20&offset=0

# Follow User
POST /api/v1/users/follow
{
  "user_id": "user-uuid"
}

# Block User
POST /api/v1/users/block  
{
  "user_id": "user-uuid"
}
```
</details>

<details>
<summary><strong>📱 Social Media APIs</strong></summary>

```bash
# Create Post
POST /api/v1/social/posts
{
  "content": "Hello world!",
  "type": "text",
  "visibility": "public",
  "tags": ["hello", "world"],
  "mentions": ["user123"],
  "team_id": "team-uuid"
}

# Get Personal Feed
GET /api/v1/social/feed?limit=20&skip=0

# Get Public Feed (no auth required)
GET /api/v1/social/feed/public?limit=20

# Get Trending Posts
GET /api/v1/social/feed/trending?time_window=24h&limit=10

# Like Post
POST /api/v1/social/posts/{post-id}/reactions
{
  "type": "like"
}

# Comment on Post
POST /api/v1/social/comments
{
  "post_id": "post-id",
  "content": "Great post!",
  "parent_id": "comment-id" // Optional for replies
}

# Share Post
POST /api/v1/social/shares
{
  "post_id": "post-id",
  "comment": "Check this out!",
  "visibility": "public"
}

# Search Posts
GET /api/v1/social/posts/search?q=golang&limit=20
```
</details>

<details>
<summary><strong>🏢 Team Management APIs</strong></summary>

```bash
# Create Team
POST /api/v1/teams
{
  "name": "Engineering Team",
  "description": "Software development team",
  "category": "engineering",
  "visibility": "private"
}

# Get Team Details
GET /api/v1/teams/{team-id}

# Invite Member
POST /api/v1/teams/{team-id}/invite
{
  "email": "<EMAIL>",
  "role": "member"
}

# Accept Team Invitation
POST /api/v1/teams/invitations/{invitation-id}/accept

# Update Member Role
PUT /api/v1/teams/{team-id}/members/{user-id}/role
{
  "role": "admin"
}

# Get Team Members
GET /api/v1/teams/{team-id}/members?limit=20

# Switch Team Context
POST /api/v1/teams/{team-id}/context

# Get Team Posts
GET /api/v1/social/teams/{team-id}/posts?limit=20
```
</details>

<details>
<summary><strong>💬 Messaging APIs</strong></summary>

```bash
# Create Conversation
POST /api/v1/messages/conversations
{
  "type": "direct", // or "group"
  "participants": ["user1-id", "user2-id"],
  "name": "Project Discussion" // Optional for group
}

# Send Message
POST /api/v1/messages
{
  "conversation_id": "conv-id",
  "content": "Hello everyone!",
  "type": "text",
  "attachment_ids": ["file-id"] // Optional
}

# Get Conversations
GET /api/v1/messages/conversations?limit=20

# Get Messages
GET /api/v1/messages?conversation_id=conv-id&limit=50

# Search Messages
GET /api/v1/messages/search?q=project&conversation_id=conv-id

# Mark as Read
PUT /api/v1/messages/{message-id}/read
```
</details>

<details>
<summary><strong>📁 Drive APIs</strong></summary>

```bash
# Upload File
POST /api/v1/drive/upload
Content-Type: multipart/form-data
{
  "file": <file-data>,
  "folder_id": "folder-uuid", // Optional
  "visibility": "private"
}

# Create Folder
POST /api/v1/drive/folders
{
  "name": "Documents",
  "parent_id": "parent-folder-id", // Optional
  "visibility": "private"
}

# Get Files
GET /api/v1/drive/files?folder_id=folder-id&limit=20

# Share File
POST /api/v1/drive/files/{file-id}/share
{
  "shared_with": "user-id",
  "permission": "read", // or "write"
  "expires_at": "2024-12-31T23:59:59Z" // Optional
}

# Download File
GET /api/v1/drive/files/{file-id}/download

# Get File Versions
GET /api/v1/drive/files/{file-id}/versions
```
</details>

<details>
<summary><strong>📅 Calendar APIs</strong></summary>

```bash
# Create Calendar
POST /api/v1/calendar/calendars
{
  "name": "Work Calendar",
  "description": "My work schedule",
  "color": "#FF5733",
  "is_public": false
}

# Create Event
POST /api/v1/calendar/events
{
  "calendar_id": "calendar-id",
  "title": "Team Meeting",
  "description": "Weekly sync",
  "start_time": "2024-01-15T10:00:00Z",
  "end_time": "2024-01-15T11:00:00Z",
  "attendees": ["user1-id", "user2-id"]
}

# Get Events
GET /api/v1/calendar/events?start=2024-01-01&end=2024-01-31

# Set Availability
POST /api/v1/calendar/availability
{
  "day_of_week": 1, // Monday = 1
  "start_time": "09:00",
  "end_time": "17:00"
}

# Check Availability
GET /api/v1/calendar/availability/check?users=user1,user2&start=2024-01-15T10:00:00Z&end=2024-01-15T11:00:00Z
```
</details>

<details>
<summary><strong>🔔 Notification APIs</strong></summary>

```bash
# Send Notification
POST /api/v1/notifications
{
  "recipient_id": "user-id",
  "type": "message",
  "title": "New Message",
  "body": "You have a new message from John",
  "channels": ["in_app", "email", "push"]
}

# Get Notifications
GET /api/v1/notifications?unread_only=true&limit=20

# Mark as Read
PUT /api/v1/notifications/{notification-id}/read

# Update Preferences
PUT /api/v1/notifications/preferences
{
  "message": {
    "in_app": true,
    "email": false,
    "push": true
  },
  "friend_request": {
    "in_app": true,
    "email": true,
    "push": false
  }
}

# Register Device
POST /api/v1/notifications/devices
{
  "device_token": "firebase-token",
  "platform": "ios", // or "android"
  "app_version": "1.0.0"
}
```
</details>

### 📚 **Interactive Documentation**

**Swagger UI**: http://localhost:8000/swagger/  
Complete interactive API documentation with request/response examples.

---

## 🗃️ Database Design

### 🏗️ **Database Architecture**

The platform uses **polyglot persistence** - choosing the right database for each use case:

| Database | Services | Use Case | Rationale |
|----------|----------|----------|-----------|
| **🐘 PostgreSQL** | Auth, User, Team, Drive, Calendar, Notification | Structured data with ACID requirements | Strong consistency, complex queries, referential integrity |
| **🍃 MongoDB** | Social, Messaging | Document-oriented, high-volume data | Flexible schema, horizontal scaling, JSON-like documents |
| **⚡ Redis** | All services | Caching, sessions, real-time features | In-memory performance, pub/sub, atomic operations |
| **📦 MinIO** | Drive, Social | File storage | S3-compatible object storage, high throughput |

### 📊 **Database Schema Overview**

<details>
<summary><strong>🐘 PostgreSQL Schema</strong></summary>

#### Authentication Service
```sql
-- Users table with authentication data
CREATE TABLE auth_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- OTP verification table
CREATE TABLE otp_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth_users(id),
    otp VARCHAR(10) NOT NULL,
    type VARCHAR(20) NOT NULL, -- 'email' or 'phone'
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Sessions table for JWT management
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth_users(id),
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### User Service
```sql
-- Extended user profile information
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    bio TEXT,
    avatar_url VARCHAR(255),
    location VARCHAR(100),
    website VARCHAR(255),
    date_of_birth DATE,
    privacy_settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_seen_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Friend relationships
CREATE TABLE friend_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID REFERENCES users(id),
    receiver_id UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'rejected'
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(requester_id, receiver_id)
);

-- Follow relationships
CREATE TABLE follows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    follower_id UUID REFERENCES users(id),
    followed_id UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(follower_id, followed_id)
);

-- User blocking
CREATE TABLE blocks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    blocker_id UUID REFERENCES users(id),
    blocked_id UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(blocker_id, blocked_id)
);
```

#### Team Service
```sql
-- Teams table
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    visibility VARCHAR(20) DEFAULT 'private', -- 'public', 'private'
    settings JSONB DEFAULT '{}',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Team membership with roles
CREATE TABLE team_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_id UUID REFERENCES teams(id),
    user_id UUID REFERENCES users(id),
    role VARCHAR(20) DEFAULT 'member', -- 'owner', 'admin', 'member'
    permissions JSONB DEFAULT '{}',
    joined_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(team_id, user_id)
);

-- Team invitations
CREATE TABLE team_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_id UUID REFERENCES teams(id),
    inviter_id UUID REFERENCES users(id),
    email VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'member',
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'rejected', 'expired'
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### Drive Service
```sql
-- Files table with versioning support
CREATE TABLE files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    path VARCHAR(500) NOT NULL,
    size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    checksum VARCHAR(64),
    owner_id UUID NOT NULL,
    team_id UUID,
    folder_id UUID REFERENCES folders(id),
    parent_id UUID REFERENCES files(id), -- For versioning
    version INTEGER DEFAULT 1,
    is_latest BOOLEAN DEFAULT true,
    visibility VARCHAR(20) DEFAULT 'private',
    status VARCHAR(20) DEFAULT 'active',
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Folders hierarchy
CREATE TABLE folders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    path VARCHAR(500) NOT NULL,
    owner_id UUID NOT NULL,
    team_id UUID,
    parent_id UUID REFERENCES folders(id),
    visibility VARCHAR(20) DEFAULT 'private',
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- File sharing permissions
CREATE TABLE file_shares (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID REFERENCES files(id),
    shared_by UUID NOT NULL,
    shared_with UUID NOT NULL,
    permission VARCHAR(20) DEFAULT 'read', -- 'read', 'write'
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Content deduplication
CREATE TABLE content_stores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    checksum VARCHAR(64) UNIQUE NOT NULL,
    size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    storage_path VARCHAR(500) NOT NULL,
    reference_count INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW()
);
```

</details>

<details>
<summary><strong>🍃 MongoDB Schema</strong></summary>

#### Social Service Collections
```javascript
// Posts collection
{
  _id: ObjectId,
  author_id: "uuid",
  team_id: "uuid", // Optional
  content: "Post content",
  type: "text|image|video|link|poll|event",
  visibility: "public|friends|team|private",
  tags: ["tag1", "tag2"],
  mentions: ["user1", "user2"],
  location: {
    lat: 37.7749,
    lng: -122.4194,
    name: "San Francisco"
  },
  attachment_ids: ["uuid1", "uuid2"],
  metrics: {
    like_count: 10,
    comment_count: 5,
    share_count: 2,
    view_count: 100
  },
  is_deleted: false,
  is_flagged: false,
  created_at: ISODate,
  updated_at: ISODate
}

// Comments collection
{
  _id: ObjectId,
  post_id: ObjectId,
  author_id: "uuid",
  parent_id: ObjectId, // For replies
  content: "Comment content",
  attachment_ids: ["uuid1"],
  like_count: 3,
  is_deleted: false,
  is_flagged: false,
  created_at: ISODate,
  updated_at: ISODate
}

// Likes collection  
{
  _id: ObjectId,
  user_id: "uuid",
  post_id: ObjectId, // Optional
  comment_id: ObjectId, // Optional
  type: "like|love|haha|wow|sad|angry",
  created_at: ISODate
}

// MongoDB Indexes for Social Service
db.posts.createIndex({ "author_id": 1, "created_at": -1 })
db.posts.createIndex({ "visibility": 1, "created_at": -1 })
db.posts.createIndex({ "team_id": 1, "created_at": -1 })
db.posts.createIndex({ "$text": { "content": "text", "tags": "text" } })
db.posts.createIndex({ "location": "2dsphere" }) // Geospatial
db.comments.createIndex({ "post_id": 1, "parent_id": 1, "created_at": -1 })
db.likes.createIndex({ "post_id": 1, "type": 1 })
db.likes.createIndex({ "user_id": 1, "post_id": 1 }, { unique: true })
```

#### Messaging Service Collections
```javascript
// Conversations collection
{
  _id: ObjectId,
  type: "direct|group|channel",
  name: "Conversation name", // Optional for direct
  participants: [
    {
      user_id: "uuid",
      role: "admin|member",
      joined_at: ISODate,
      last_read_at: ISODate
    }
  ],
  last_message: {
    content: "Last message preview",
    author_id: "uuid",
    created_at: ISODate
  },
  is_archived: false,
  created_at: ISODate,
  updated_at: ISODate
}

// Messages collection
{
  _id: ObjectId,
  conversation_id: ObjectId,
  author_id: "uuid",
  content: "Message content",
  type: "text|file|image|system",
  attachment_ids: ["uuid1", "uuid2"],
  reactions: [
    {
      user_id: "uuid",
      type: "like|love|haha",
      created_at: ISODate
    }
  ],
  thread_id: ObjectId, // For threaded replies
  reply_count: 3,
  is_edited: false,
  edited_at: ISODate,
  is_deleted: false,
  created_at: ISODate
}

// MongoDB Indexes for Messaging
db.conversations.createIndex({ "participants.user_id": 1 })
db.conversations.createIndex({ "updated_at": -1 })
db.messages.createIndex({ "conversation_id": 1, "created_at": -1 })
db.messages.createIndex({ "$text": { "content": "text" } })
db.messages.createIndex({ "author_id": 1, "created_at": -1 })
```
</details>

### 🔍 **Performance Optimizations**

The platform includes comprehensive database optimizations:

#### PostgreSQL Optimizations
- **Composite Indexes**: Multi-column indexes for common query patterns
- **Partial Indexes**: Filtered indexes for better performance
- **GIN Indexes**: Full-text search capabilities
- **Connection Pooling**: 10 idle, 100 max connections per service
- **Query Optimization**: EXPLAIN-analyzed query patterns

#### MongoDB Optimizations  
- **Compound Indexes**: Multi-field indexes for complex queries
- **Text Indexes**: Full-text search across content
- **Geospatial Indexes**: Location-based queries
- **TTL Indexes**: Automatic data expiration
- **Read Preferences**: Primary reads for consistency

#### Redis Optimizations
- **Key Expiration**: Automatic cleanup of stale data
- **Memory Optimization**: Efficient data structures
- **Pipelining**: Batched operations for performance
- **Pub/Sub**: Real-time notifications and updates

---

## 🔐 Security

### 🛡️ **Security Architecture**

<table>
<tr>
<td width="50%">

#### 🔐 **Authentication & Authorization**
- ✅ **Modern Auth Flow**: OTP-based registration with email/SMS
- ✅ **JWT Tokens**: HMAC-256 with access + refresh pattern
- ✅ **Multi-Factor Auth**: Additional security layers
- ✅ **Service-to-Service**: Dedicated internal service tokens
- ✅ **Token Rotation**: Automatic refresh token management
- ✅ **Session Management**: Secure session lifecycle

#### 🔒 **Data Protection** 
- ✅ **Input Validation**: Comprehensive request validation
- ✅ **SQL Injection Prevention**: ORM with prepared statements
- ✅ **XSS Protection**: Content sanitization and escaping
- ✅ **CORS Configuration**: Proper cross-origin policies
- ✅ **Rate Limiting**: Per-user API abuse prevention
- ✅ **Content Security**: File upload validation and scanning

</td>
<td width="50%">

#### 🛡️ **Infrastructure Security**
- ✅ **HTTPS Enforcement**: TLS 1.3 encryption in transit
- ✅ **Database Encryption**: Encryption at rest
- ✅ **Secret Management**: Environment-based configuration
- ✅ **Container Security**: Non-root user execution
- ✅ **Network Isolation**: Service mesh communication
- ✅ **Health Monitoring**: Continuous security scanning

#### 📊 **Audit & Compliance**
- ✅ **Audit Trails**: Comprehensive activity logging
- ✅ **Access Logging**: Request/response tracking
- ✅ **Privacy Controls**: GDPR-compliant data handling
- ✅ **Data Retention**: Configurable retention policies
- ✅ **Breach Detection**: Anomaly monitoring
- ✅ **Compliance Ready**: SOC 2 Type II preparation

</td>
</tr>
</table>

### 🔑 **Authentication Flow**

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Gateway
    participant Auth
    participant User_Service
    participant Redis
    
    User->>Frontend: Register with email/password
    Frontend->>Gateway: POST /auth/register
    Gateway->>Auth: Registration request
    Auth->>Auth: Hash password (bcrypt)
    Auth->>Auth: Generate OTP
    Auth->>Auth: Save to database
    Auth->>User: Send OTP via email/SMS
    Auth-->>Gateway: Registration pending
    Gateway-->>Frontend: OTP sent
    
    User->>Frontend: Enter OTP
    Frontend->>Gateway: POST /auth/verify-otp
    Gateway->>Auth: OTP verification
    Auth->>Auth: Validate OTP & expiry
    Auth->>User_Service: Create user profile
    Auth->>Auth: Generate JWT tokens
    Auth->>Redis: Cache session
    Auth-->>Gateway: Success + tokens
    Gateway-->>Frontend: Authentication success
```

### 🔐 **JWT Token Structure**

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "user-uuid",
    "email": "<EMAIL>",
    "roles": ["user"],
    "team_context": "team-uuid",
    "iat": 1640995200,
    "exp": 1640998800,
    "iss": "swork-auth-service"
  }
}
```

### 🛡️ **Permission System**

```javascript
// Role-based permissions
const permissions = {
  "owner": {
    "team": ["create", "read", "update", "delete", "invite", "remove"],
    "post": ["create", "read", "update", "delete", "moderate"],
    "file": ["upload", "download", "share", "delete"]
  },
  "admin": {
    "team": ["read", "update", "invite"],
    "post": ["create", "read", "update", "moderate"],
    "file": ["upload", "download", "share"]
  },
  "member": {
    "team": ["read"],
    "post": ["create", "read", "update_own"],
    "file": ["upload", "download", "share_own"]
  }
}
```

---

## 🚀 Deployment

### 🐳 **Docker Deployment**

<details>
<summary><strong>📋 Production Docker Setup</strong></summary>

```bash
# 1️⃣ Build production images
docker-compose -f docker-compose.prod.yml build

# 2️⃣ Start production stack
docker-compose -f docker-compose.prod.yml up -d

# 3️⃣ Verify deployment
docker-compose -f docker-compose.prod.yml ps
```

**Production Docker Compose**:
```yaml
version: '3.8'
services:
  # Database services with persistence
  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: swork
      POSTGRES_USER: swork
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
  
  # Application services with health checks
  auth-service:
    build: .
    environment:
      - APP_ENV=production
      - JWT_SECRET=${JWT_SECRET}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```
</details>

### ☸️ **Kubernetes Deployment**

<details>
<summary><strong>🚀 Kubernetes Manifests</strong></summary>

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: swork-platform

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: swork-config
  namespace: swork-platform
data:
  POSTGRES_HOST: "postgres-service"
  REDIS_HOST: "redis-service"
  MONGODB_HOST: "mongodb-service"

---
# k8s/auth-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: swork-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: swork/auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: swork-secrets
              key: jwt-secret
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"

---
# k8s/auth-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: swork-platform
spec:
  selector:
    app: auth-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

**Deploy to Kubernetes**:
```bash
# Apply all manifests
kubectl apply -f k8s/

# Verify deployment
kubectl get pods -n swork-platform
kubectl get services -n swork-platform

# Scale services
kubectl scale deployment auth-service --replicas=5 -n swork-platform
```
</details>

### ☁️ **Cloud Deployment Options**

<table>
<tr>
<td width="33%">

#### **🔷 AWS Deployment**
```bash
# EKS Cluster
eksctl create cluster \
  --name swork-platform \
  --version 1.21 \
  --nodegroup-name workers \
  --nodes 3 \
  --node-type t3.medium

# RDS PostgreSQL
aws rds create-db-instance \
  --db-instance-identifier swork-postgres \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username swork \
  --allocated-storage 20

# ElastiCache Redis
aws elasticache create-cache-cluster \
  --cache-cluster-id swork-redis \
  --cache-node-type cache.t3.micro \
  --engine redis
```

</td>
<td width="33%">

#### **🔷 Google Cloud**
```bash
# GKE Cluster
gcloud container clusters create swork-platform \
  --num-nodes=3 \
  --zone=us-central1-a \
  --machine-type=e2-medium

# Cloud SQL
gcloud sql instances create swork-postgres \
  --database-version=POSTGRES_13 \
  --tier=db-f1-micro \
  --region=us-central1

# Memorystore Redis
gcloud redis instances create swork-redis \
  --size=1 \
  --region=us-central1
```

</td>
<td width="33%">

#### **🔷 Azure Deployment**
```bash
# AKS Cluster
az aks create \
  --resource-group swork-rg \
  --name swork-platform \
  --node-count 3 \
  --node-vm-size Standard_B2s \
  --enable-addons monitoring

# Azure Database
az postgres server create \
  --resource-group swork-rg \
  --name swork-postgres \
  --admin-user swork \
  --sku-name B_Gen5_1

# Azure Cache
az redis create \
  --resource-group swork-rg \
  --name swork-redis \
  --location eastus \
  --sku Basic \
  --vm-size c0
```

</td>
</tr>
</table>

### 🔧 **Environment Configuration**

<details>
<summary><strong>⚙️ Production Environment Variables</strong></summary>

```bash
# Security
JWT_SECRET=your-super-secure-jwt-secret-32-chars-minimum
SERVICE_TOKEN=internal-service-communication-token
BCRYPT_COST=12

# Database Configuration
DB_HOST=postgres-server
DB_PORT=5432
DB_USERNAME=swork
DB_PASSWORD=secure-db-password
DB_SSL_MODE=require

# MongoDB Configuration  
MONGODB_URI=****************************************************
MONGO_DATABASE=swork_production

# Redis Configuration
REDIS_URL=redis://redis-server:6379
REDIS_PASSWORD=redis-secure-password

# Storage Configuration
MINIO_ENDPOINT=storage-server:9000
MINIO_ACCESS_KEY=storage-access-key
MINIO_SECRET_KEY=storage-secret-key
MINIO_USE_SSL=true

# Email Configuration (for notifications)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=email-password
SMTP_FROM=Swork Platform <<EMAIL>>

# Monitoring & Logging
LOG_LEVEL=info
ENABLE_METRICS=true
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600s
```
</details>

---

## 🧪 Testing

### 🔬 **Testing Strategy**

<table>
<tr>
<td width="50%">

#### **🧪 Unit Testing**
```bash
# Run all unit tests
make test

# Run tests with coverage
go test -v -cover ./...

# Run specific service tests
cd services/auth && go test ./...

# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

#### **🔧 Integration Testing**
```bash
# Start test environment
make dev-build

# Run API integration tests
make test-api

# Test specific endpoints
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}'
```

</td>
<td width="50%">

#### **🚀 Load Testing**
```bash
# Install Apache Benchmark
sudo apt install apache2-utils

# Test API Gateway
ab -n 1000 -c 10 http://localhost:8000/health

# Test authentication endpoint
ab -n 500 -c 5 -T application/json \
  -p auth-test.json \
  http://localhost:8000/api/v1/auth/login
```

#### **🔍 Health Testing**
```bash
# Check all service health
make health

# Individual service health
curl http://localhost:8001/health # Auth
curl http://localhost:8002/health # User
curl http://localhost:8003/health # Social
# ... etc for all services
```

</td>
</tr>
</table>

### 📊 **Test Coverage Goals**

| Component | Target Coverage | Current Status |
|-----------|----------------|----------------|
| **Authentication** | 95%+ | ✅ Implemented |
| **User Management** | 90%+ | ✅ Implemented |
| **Social Features** | 85%+ | ✅ Implemented |
| **Team Management** | 90%+ | ✅ Implemented |
| **Messaging** | 85%+ | ✅ Implemented |
| **Drive System** | 90%+ | ✅ Implemented |
| **Calendar** | 85%+ | ✅ Implemented |
| **Notifications** | 80%+ | ✅ Implemented |

### 🧪 **Example Test Suite**

<details>
<summary><strong>📝 Authentication Service Tests</strong></summary>

```go
package handlers_test

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    
    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
)

func TestAuthHandler_Register(t *testing.T) {
    tests := []struct {
        name           string
        requestBody    map[string]interface{}
        expectedStatus int
        expectedError  string
    }{
        {
            name: "Valid registration",
            requestBody: map[string]interface{}{
                "email":      "<EMAIL>",
                "password":   "securepassword123",
                "first_name": "John",
                "last_name":  "Doe",
            },
            expectedStatus: http.StatusCreated,
        },
        {
            name: "Invalid email format",
            requestBody: map[string]interface{}{
                "email":    "invalid-email",
                "password": "securepassword123",
            },
            expectedStatus: http.StatusBadRequest,
            expectedError:  "Invalid email format",
        },
        {
            name: "Weak password",
            requestBody: map[string]interface{}{
                "email":    "<EMAIL>",
                "password": "weak",
            },
            expectedStatus: http.StatusBadRequest,
            expectedError:  "Password must be at least 8 characters",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup
            gin.SetMode(gin.TestMode)
            router := setupTestRouter()
            
            body, _ := json.Marshal(tt.requestBody)
            req := httptest.NewRequest("POST", "/auth/register", bytes.NewBuffer(body))
            req.Header.Set("Content-Type", "application/json")
            
            recorder := httptest.NewRecorder()
            router.ServeHTTP(recorder, req)
            
            // Assertions
            assert.Equal(t, tt.expectedStatus, recorder.Code)
            
            var response map[string]interface{}
            json.Unmarshal(recorder.Body.Bytes(), &response)
            
            if tt.expectedError != "" {
                assert.Contains(t, response["error"].(string), tt.expectedError)
            } else {
                assert.True(t, response["success"].(bool))
                assert.NotEmpty(t, response["data"])
            }
        })
    }
}

func TestAuthHandler_Login(t *testing.T) {
    // Test user login flow
    // Including OTP verification if enabled
}

func TestAuthHandler_RefreshToken(t *testing.T) {
    // Test JWT token refresh mechanism
}
```
</details>

---

## 📊 Monitoring

### 📈 **Observability Stack**

<table>
<tr>
<td width="50%">

#### **📊 Metrics & Monitoring**
- ✅ **Prometheus**: Time-series metrics collection
- ✅ **Grafana**: Visualization and dashboards
- ✅ **AlertManager**: Intelligent alerting rules
- ✅ **Health Checks**: Built-in service health endpoints
- ✅ **Custom Metrics**: Business-specific KPIs
- ✅ **SLA Monitoring**: 99.9% uptime tracking

#### **📋 Logging & Tracing**
- ✅ **Structured Logging**: JSON-formatted logs with context
- ✅ **Centralized Logs**: ELK Stack (Elasticsearch, Logstash, Kibana)
- ✅ **Distributed Tracing**: Jaeger for request tracking
- ✅ **Error Tracking**: Sentry for error aggregation
- ✅ **Audit Logs**: Security and compliance tracking

</td>
<td width="50%">

#### **🚨 Alerting & Incident Response**
- ✅ **Smart Alerts**: Context-aware notifications
- ✅ **Escalation Policies**: Multi-tier alert routing
- ✅ **PagerDuty Integration**: On-call management
- ✅ **Slack Notifications**: Team collaboration alerts
- ✅ **Performance Thresholds**: Proactive monitoring
- ✅ **Recovery Automation**: Self-healing capabilities

#### **📈 Performance Metrics**
- ✅ **Response Times**: P50, P95, P99 latencies
- ✅ **Throughput**: Requests per second tracking
- ✅ **Error Rates**: 4xx and 5xx error monitoring
- ✅ **Database Performance**: Query optimization metrics
- ✅ **Resource Utilization**: CPU, memory, storage usage

</td>
</tr>
</table>

### 📊 **Key Performance Indicators**

| Metric | Target | Critical Threshold | Current Status |
|--------|--------|-------------------|----------------|
| **API Response Time** | < 200ms P95 | > 1s P95 | ✅ Optimized |
| **System Uptime** | 99.9% | < 99.5% | ✅ Monitored |
| **Database Query Time** | < 50ms P95 | > 200ms P95 | ✅ Indexed |
| **Error Rate** | < 0.1% | > 1% | ✅ Handled |
| **CPU Usage** | < 70% avg | > 90% avg | ✅ Scaled |
| **Memory Usage** | < 80% avg | > 95% avg | ✅ Optimized |

### 🔍 **Monitoring Setup**

<details>
<summary><strong>📊 Prometheus Configuration</strong></summary>

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert-rules.yml"

scrape_configs:
  - job_name: 'swork-services'
    static_configs:
      - targets: 
        - 'auth-service:8080'
        - 'user-service:8080'
        - 'social-service:8080'
        - 'team-service:8080'
        - 'drive-service:8080'
        - 'calendar-service:8080'
        - 'notification-service:8080'
        - 'api-gateway:8080'
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'infrastructure'
    static_configs:
      - targets:
        - 'postgres:5432'
        - 'mongodb:27017'
        - 'redis:6379'
        - 'minio:9000'
```

**Alert Rules**:
```yaml
# alert-rules.yml
groups:
  - name: swork-platform
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.instance }} has been down for more than 1 minute"
```
</details>

<details>
<summary><strong>📊 Grafana Dashboards</strong></summary>

**Main Platform Dashboard**:
- 🚪 API Gateway metrics (request rate, response time, error rate)
- 🔐 Authentication metrics (login success rate, token generation)
- 👥 User activity metrics (active users, registrations)
- 💬 Social engagement metrics (posts, comments, likes)
- 📁 Storage metrics (file uploads, storage usage)
- 🗄️ Database performance (connection pool, query time)

**Infrastructure Dashboard**:
- 💻 System resources (CPU, memory, disk usage)
- 🌐 Network traffic and latency
- 🐳 Container health and resource usage
- 🗄️ Database connections and performance
- ⚡ Redis cache hit rates and memory usage
- 📦 Storage system health and capacity
</details>

### 🔍 **Health Check Endpoints**

Each service provides comprehensive health information:

```bash
# API Gateway health check
curl http://localhost:8000/health
{
  "success": true,
  "message": "API Gateway is healthy",
  "data": {
    "service": "api-gateway",
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "24h30m15s",
    "checks": {
      "database": "healthy",
      "redis": "healthy",
      "services": {
        "auth": "healthy",
        "user": "healthy",
        "social": "healthy"
      }
    }
  }
}

# Service status overview
curl http://localhost:8000/status
```

---

## 🤝 Contributing

### 🌟 **Getting Started**

We welcome contributions from the community! Here's how to get started:

#### **🚀 Quick Setup**
```bash
# 1️⃣ Fork the repository
gh repo fork swork-team/platform

# 2️⃣ Clone your fork
git clone https://github.com/your-username/swork-platform.git
cd swork-platform

# 3️⃣ Set up development environment
make dev-build

# 4️⃣ Create a feature branch
git checkout -b feature/amazing-feature

# 5️⃣ Make your changes and test
make test

# 6️⃣ Submit a pull request
gh pr create --title "Add amazing feature" --body "Description of changes"
```

### 📋 **Development Guidelines**

<table>
<tr>
<td width="50%">

#### **📝 Code Standards**
- ✅ **Go Code Style**: Follow `gofmt` and `golint`
- ✅ **Error Handling**: Comprehensive error handling
- ✅ **Documentation**: Inline comments and README updates
- ✅ **Testing**: Unit tests for all new features
- ✅ **Performance**: Consider performance implications
- ✅ **Security**: Follow security best practices

#### **🔄 Git Workflow**
- ✅ **Branch Naming**: `feature/`, `bugfix/`, `hotfix/`
- ✅ **Commit Messages**: Clear, descriptive messages
- ✅ **Small Changes**: Keep PRs focused and small
- ✅ **Reviews**: All changes require code review
- ✅ **CI/CD**: All checks must pass
- ✅ **Documentation**: Update relevant documentation

</td>
<td width="50%">

#### **🧪 Testing Requirements**
- ✅ **Unit Tests**: 80%+ coverage for new code
- ✅ **Integration Tests**: Test API endpoints
- ✅ **Documentation**: Update API documentation
- ✅ **Manual Testing**: Verify changes work as expected
- ✅ **Performance**: No performance regressions
- ✅ **Security**: Security review for sensitive changes

#### **📖 Documentation**
- ✅ **API Changes**: Update OpenAPI specifications
- ✅ **README Updates**: Keep documentation current
- ✅ **Code Comments**: Document complex logic
- ✅ **Migration Guides**: For breaking changes
- ✅ **Changelog**: Update CHANGELOG.md
- ✅ **Examples**: Provide usage examples

</td>
</tr>
</table>

### 🏗️ **Architecture Guidelines**

When contributing, please follow these architectural principles:

#### **🎯 Microservices Principles**
- **Single Responsibility**: Each service has one clear purpose
- **Database Per Service**: No shared databases between services
- **API-First Design**: Well-defined service interfaces
- **Stateless Services**: Services should be stateless
- **Fault Tolerance**: Handle failures gracefully
- **Independent Deployment**: Services deploy independently

#### **🔄 Development Process**
1. **📋 Issue First**: Create/find an issue before coding
2. **🗣️ Discussion**: Discuss approach in the issue
3. **🔄 Implementation**: Follow coding standards
4. **🧪 Testing**: Comprehensive test coverage
5. **📝 Documentation**: Update all relevant docs
6. **👀 Review**: Submit PR for code review
7. **🚀 Deployment**: Merge after approval

### 🛠️ **Common Development Tasks**

<details>
<summary><strong>➕ Adding a New Service</strong></summary>

```bash
# 1️⃣ Create service directory
mkdir -p services/new-service/{cmd,internal/{config,handlers,models,repositories,services}}

# 2️⃣ Copy service template
cp services/auth/cmd/main.go services/new-service/cmd/main.go

# 3️⃣ Update docker-compose.yml
# Add new service configuration

# 4️⃣ Update API Gateway
# Add routing rules in cmd/gateway/main.go

# 5️⃣ Create database migration
# Add SQL files or MongoDB collections

# 6️⃣ Add to Makefile
# Include rebuild and logs targets

# 7️⃣ Update documentation
# Add service description to README
```
</details>

<details>
<summary><strong>🔧 Adding a New API Endpoint</strong></summary>

```go
// 1️⃣ Define the request/response models
type CreatePostRequest struct {
    Content    string   `json:"content" binding:"required"`
    Type       string   `json:"type" binding:"required"`
    Visibility string   `json:"visibility" binding:"required"`
    Tags       []string `json:"tags"`
}

// 2️⃣ Add the handler method
func (h *SocialHandler) CreatePost(c *gin.Context) {
    var req CreatePostRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err)
        return
    }
    
    userID := middleware.MustGetAuthenticatedUser(c)
    
    post, err := h.socialService.CreatePost(c.Request.Context(), userID, &req)
    if err != nil {
        utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create post", err)
        return
    }
    
    utils.SuccessResponse(c, http.StatusCreated, "Post created successfully", post)
}

// 3️⃣ Add route registration
router.POST("/posts", h.CreatePost)

// 4️⃣ Add Swagger documentation
// @Summary Create a new post
// @Description Create a new social media post
// @Tags Social
// @Accept json
// @Produce json
// @Param post body CreatePostRequest true "Post data"
// @Success 201 {object} Post
// @Failure 400 {object} ErrorResponse
// @Router /social/posts [post]
```
</details>

### 📊 **Contribution Statistics**

| Contribution Type | Guidelines | Review Process |
|-------------------|------------|----------------|
| **🐛 Bug Fixes** | Minimal scope, include test | 1 reviewer, fast-track |
| **✨ New Features** | Design doc recommended | 2 reviewers, thorough testing |
| **📝 Documentation** | Clear and comprehensive | 1 reviewer, quick approval |
| **🔒 Security** | Security review required | Security team + 2 reviewers |
| **🏗️ Architecture** | RFC process required | Architecture review board |
| **🚀 Performance** | Benchmarks required | Performance team review |

---

## 📚 Additional Resources

### 🔗 **Quick Links**

| Resource | Description | URL |
|----------|-------------|-----|
| **📚 API Documentation** | Interactive Swagger UI | http://localhost:8000/swagger/ |
| **📊 Grafana Dashboards** | Monitoring and metrics | http://localhost:3000 |
| **🔍 Prometheus** | Metrics collection | http://localhost:9090 |
| **📋 Jaeger Tracing** | Distributed tracing | http://localhost:16686 |
| **📁 MinIO Console** | Object storage management | http://localhost:9001 |
| **🏥 Health Checks** | Service health status | http://localhost:8000/health |

### 📖 **Documentation**

- **🏗️ [Architecture Decision Records](docs/adr/)**: Design decisions and rationale
- **🚀 [Deployment Guide](docs/deployment.md)**: Production deployment instructions  
- **🔐 [Security Guide](docs/security.md)**: Security best practices and configuration
- **🧪 [Testing Guide](docs/testing.md)**: Testing strategies and examples
- **🛠️ [Development Setup](docs/development.md)**: Local development environment setup
- **📊 [Monitoring Guide](docs/monitoring.md)**: Observability and alerting setup

### 🆘 **Support & Community**

- **🐛 [Issues](https://github.com/swork-team/platform/issues)**: Bug reports and feature requests
- **💬 [Discussions](https://github.com/swork-team/platform/discussions)**: Community discussions
- **📧 [Mailing List](mailto:<EMAIL>)**: Developer mailing list
- **💬 [Discord](https://discord.gg/swork-team)**: Real-time community chat
- **📚 [Wiki](https://github.com/swork-team/platform/wiki)**: Extended documentation

---

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

```
MIT License

Copyright (c) 2024 Swork Team Platform

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

<div align="center">

## 🎉 **Ready to Get Started?**

**[🚀 Quick Start](#-quick-start)** • **[📖 Documentation](#-documentation)** • **[🤝 Contributing](#-contributing)**

---

### **Built with ❤️ by the Swork Team**

*Empowering teams to collaborate seamlessly through modern technology*

**⭐ Star us on GitHub** • **🔔 Watch for updates** • **🍴 Fork and contribute**

---

</div>