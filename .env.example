# Server Configuration
PORT=8080
HOST=0.0.0.0
APP_ENV=development

# Database Configuration - GENERATE SECURE PASSWORDS FOR PRODUCTION
# Use: openssl rand -base64 32
POSTGRES_USER=swork
POSTGRES_PASSWORD=CHANGE_ME_USE_OPENSSL_RAND_BASE64_32
POSTGRES_DB=swork

# Service-specific database names (required for local development)
AUTH_DB_DATABASE=swork_auth
USER_DB_DATABASE=swork_user
TEAM_DB_DATABASE=swork_team
DRIVE_DB_DATABASE=swork_drive
CALENDAR_DB_DATABASE=swork_calendar
NOTIFICATION_DB_DATABASE=swork_notification

# MongoDB Configuration - GENERATE SECURE PASSWORDS FOR PRODUCTION  
# Use: openssl rand -base64 32
MONGO_USERNAME=swork
MONGO_PASSWORD=CHANGE_ME_USE_OPENSSL_RAND_BASE64_32
MONGO_DATABASE=swork

# Service-specific MongoDB database names
SOCIAL_MONGO_DATABASE=swork_social
NOTIFICATION_MONGO_DATABASE=swork_notification

# Database URLs (auto-constructed from above credentials)
DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/${POSTGRES_DB}?sslmode=disable
AUTH_DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/swork_auth?sslmode=disable
USER_DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/swork_user?sslmode=disable
TEAM_DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/swork_team?sslmode=disable
DRIVE_DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/swork_drive?sslmode=disable
CALENDAR_DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/swork_calendar?sslmode=disable

# MongoDB URI (auto-constructed from above credentials)
MONGODB_URI=mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@localhost:27017/${MONGO_DATABASE}
MONGO_TIMEOUT=10s

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration - CRITICAL: Use cryptographically secure random values
# Generate with: openssl rand -base64 64
JWT_SECRET=CHANGE_ME_USE_OPENSSL_RAND_BASE64_64
JWT_ACCESS_DURATION=15m
JWT_REFRESH_DURATION=168h
JWT_EMAIL_VERIFICATION_DURATION=24h
JWT_PASSWORD_RESET_DURATION=1h

# Service Authentication Token - CRITICAL: Use cryptographically secure random value
# Generate with: openssl rand -base64 32
SERVICE_TOKEN=CHANGE_ME_USE_OPENSSL_RAND_BASE64_32

# Service URLs (for development)
AUTH_SERVICE_URL=http://localhost:8001
USER_SERVICE_URL=http://localhost:8002
SOCIAL_SERVICE_URL=http://localhost:8003
TEAM_SERVICE_URL=http://localhost:8004
DRIVE_SERVICE_URL=http://localhost:8006
CALENDAR_SERVICE_URL=http://localhost:8007
NOTIFICATION_SERVICE_URL=http://localhost:8008

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Swork Team

# MinIO Storage Configuration - CRITICAL: Change default credentials
# Generate secure credentials with: openssl rand -base64 24
MINIO_ROOT_USER=CHANGE_ME_USE_SECURE_USERNAME
MINIO_ROOT_PASSWORD=CHANGE_ME_USE_OPENSSL_RAND_BASE64_24
MINIO_ACCESS_KEY=${MINIO_ROOT_USER}
MINIO_SECRET_KEY=${MINIO_ROOT_PASSWORD}

# Primary Storage Configuration (always Backblaze B2)
STORAGE_PRIMARY_TYPE=backblaze-b2
STORAGE_PRIMARY_BUCKET=swork-drive-primary

# Replica Storage Configuration  
STORAGE_REPLICA_TYPE=minio
STORAGE_REPLICA_BUCKET=swork-drive-replica
STORAGE_REPLICA_REGION=us-east-1

# Backblaze B2 Configuration (required for primary storage)
# Get these from your Backblaze B2 account at https://secure.backblaze.com/
B2_APPLICATION_KEY_ID=your_b2_application_key_id
B2_APPLICATION_KEY=your_b2_application_key
B2_BUCKET_ID=your_b2_bucket_id

# File Upload Limits
MAX_FILE_SIZE=*********

# Additional Storage Configuration
MINIO_ENDPOINT=minio:9000
MINIO_USE_SSL=false

# Push Notifications (Firebase - optional, for mobile push notifications)
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=