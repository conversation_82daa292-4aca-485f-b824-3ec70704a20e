package services

import (
	"context"
	"fmt"

	"github.com/swork-team/ai-service/internal/ai"
	"github.com/swork-team/ai-service/internal/config"
	"github.com/swork-team/ai-service/internal/models"
	"github.com/swork-team/ai-service/internal/search"
	"github.com/swork-team/ai-service/internal/utils"
)

// AIService coordinates all AI-related functionality
type AIService struct {
	config            *config.Config
	geminiService     *ai.GeminiService
	semanticSearch    *ai.SemanticSearchService
	summarization     *ai.SummarizationService
	ragQA             *ai.RAGQAService
	fastSearch        *search.FastSearchService
	serviceLogger     utils.ServiceLogger
}

// NewAIService creates a new AI service with all sub-services
func NewAIService(cfg *config.Config) *AIService {
	ctx := context.Background()
	serviceLogger := utils.NewServiceLogger()

	service := &AIService{
		config:        cfg,
		serviceLogger: serviceLogger,
	}

	// Initialize AI services
	service.initializeAIServices(ctx, cfg)
	service.initializeSearchServices(cfg)

	return service
}

// initializeAIServices initializes AI-related services
func (s *AIService) initializeAIServices(ctx context.Context, cfg *config.Config) {
	// Initialize Gemini service
	if cfg.IsGeminiConfigured() {
		geminiService, err := ai.NewGeminiService(ctx, cfg.AI.GeminiAPIKey)
		if err != nil {
			s.serviceLogger.Warn("Failed to initialize Gemini service", utils.F("error", err))
		} else {
			s.geminiService = geminiService
		}
	}

	// Initialize other AI services
	s.semanticSearch = ai.NewSemanticSearchService(
		cfg.Features.FAISSIndexPath,
		cfg.Features.FAISSMetaPath,
		cfg.AI.EmbedModel,
	)
	s.summarization = ai.NewSummarizationService(cfg.AI.OpenAIAPIKey)
	s.ragQA = ai.NewRAGQAService(cfg.AI.OpenAIAPIKey)
}

// initializeSearchServices initializes search-related services
func (s *AIService) initializeSearchServices(cfg *config.Config) {
	if cfg.IsTypesenseConfigured() {
		tsConfig := &search.TypesenseConfig{
			Addresses: []string{cfg.Typesense.URL},
			APIKey:    cfg.Typesense.APIKey,
			EnableSSL: cfg.Typesense.SSL,
		}

		fastSearchService, err := search.NewFastSearchService(tsConfig, "swork_documents")
		if err != nil {
			s.serviceLogger.Warn("Failed to initialize fast search service", utils.F("error", err))
		} else {
			s.fastSearch = fastSearchService
		}
	}
}

// RAGQuery handles RAG query requests
func (s *AIService) RAGQuery(ctx context.Context, req *models.RAGQueryRequest) (*models.RAGQueryResponseData, error) {
	s.serviceLogger.Info("Processing RAG query", utils.F("question", req.Question))

	// TODO: In a real implementation, this would:
	// 1. Call drive-service to get document contents based on team_id, conversation_id, source_document_ids
	// 2. Use semantic search to find relevant documents
	// 3. Use RAG Q&A to generate an answer based on the retrieved documents

	// For now, return mock data
	mockAnswer := fmt.Sprintf("AI-generated answer for question: '%s'", req.Question)
	mockSources := []models.SourceInfo{
		{DocumentID: "doc_abc_123", Name: "Project_Plan.pdf", Score: 0.92},
		{DocumentID: "doc_def_456", Name: "Meeting_Notes_Q4.docx", Score: 0.88},
	}

	return &models.RAGQueryResponseData{
		Answer:  mockAnswer,
		Sources: mockSources,
	}, nil
}

// GenerateEmail generates an email using AI
func (s *AIService) GenerateEmail(ctx context.Context, req *models.EmailGenerationRequest) (*models.EmailGenerationResponseData, error) {
	if s.geminiService == nil {
		return nil, fmt.Errorf("Gemini service not configured (missing API key)")
	}

	// Set default language if not provided
	if req.Language == "" {
		req.Language = "Vietnamese"
	}

	s.serviceLogger.Info("Generating email",
		utils.F("purpose", req.Purpose),
		utils.F("tone", req.Tone),
		utils.F("language", req.Language))

	return s.geminiService.GenerateEmail(ctx, req)
}

// SemanticSearch performs semantic search on documents
func (s *AIService) SemanticSearch(ctx context.Context, req *models.SemanticSearchRequest) (*models.SemanticSearchResponseData, error) {
	if s.semanticSearch == nil {
		return nil, fmt.Errorf("semantic search service not initialized")
	}

	// Set default topK if not provided
	topK := req.TopK
	if topK <= 0 {
		topK = 5
	}

	s.serviceLogger.Info("Performing semantic search",
		utils.F("query", req.Query),
		utils.F("top_k", topK))

	results, err := s.semanticSearch.Search(req.Query, req.TeamID, topK)
	if err != nil {
		return nil, fmt.Errorf("semantic search failed: %w", err)
	}

	return &models.SemanticSearchResponseData{
		Results: results,
	}, nil
}

// SummarizeDocument summarizes a document
func (s *AIService) SummarizeDocument(ctx context.Context, req *models.SummarizationRequest) (*models.SummarizationResponseData, error) {
	if s.summarization == nil {
		return nil, fmt.Errorf("summarization service not configured")
	}

	// Set default language if not provided
	language := req.Language
	if language == "" {
		language = "Vietnamese"
	}

	s.serviceLogger.Info("Summarizing document",
		utils.F("content_length", len(req.Content)),
		utils.F("language", language))

	return s.summarization.SummarizeDocument(ctx, req.Content, language)
}

// RAGQA performs RAG-based question answering
func (s *AIService) RAGQA(ctx context.Context, req *models.RAGQARequest) (*models.RAGQAResponseData, error) {
	if s.ragQA == nil {
		return nil, fmt.Errorf("RAG Q&A service not configured")
	}

	// Set default language if not provided
	language := req.Language
	if language == "" {
		language = "Vietnamese"
	}

	s.serviceLogger.Info("Processing RAG Q&A",
		utils.F("question", req.Question),
		utils.F("document_count", len(req.DocumentContents)),
		utils.F("language", language))

	return s.ragQA.AnswerQuestion(ctx, req.Question, req.DocumentContents, language)
}

// FastSearch performs fast search using Elasticsearch
func (s *AIService) FastSearch(ctx context.Context, req *models.FastSearchRequest) (*models.FastSearchResponseData, error) {
	s.serviceLogger.Info("Performing fast search",
		utils.F("query", req.Query),
		utils.F("size", req.Size))

	if s.fastSearch == nil {
		return s.fallbackToSemanticSearch(ctx, req)
	}

	return s.fastSearch.Search(ctx, req)
}

// fallbackToSemanticSearch provides fallback when Elasticsearch is not available
func (s *AIService) fallbackToSemanticSearch(ctx context.Context, req *models.FastSearchRequest) (*models.FastSearchResponseData, error) {
	s.serviceLogger.Info("Falling back to semantic search")

	semanticReq := &models.SemanticSearchRequest{
		Query:  req.Query,
		TeamID: req.TeamID,
		TopK:   req.Size,
	}

	semanticResult, err := s.SemanticSearch(ctx, semanticReq)
	if err != nil {
		return nil, err
	}

	// Convert semantic search results to fast search format
	var hits []models.SearchHit
	for _, result := range semanticResult.Results {
		hit := models.SearchHit{
			Document: models.Document{
				ID:           result.DocumentID,
				Title:        result.Name,
				Content:      result.Snippet,
				DocumentType: "document",
			},
			Score: result.Score,
		}
		hits = append(hits, hit)
	}

	return &models.FastSearchResponseData{
		Hits:     hits,
		Total:    int64(len(hits)),
		MaxScore: 1.0,
		Took:     10,
		From:     req.From,
		Size:     req.Size,
	}, nil
}

// AutoComplete provides auto-completion suggestions
func (s *AIService) AutoComplete(ctx context.Context, req *models.AutoCompleteRequest) (*models.AutoCompleteResponseData, error) {
	s.serviceLogger.Info("Providing auto-complete suggestions", utils.F("query", req.Query))

	if s.fastSearch == nil {
		return s.fallbackAutoComplete(req), nil
	}

	return s.fastSearch.AutoComplete(ctx, req)
}

// fallbackAutoComplete provides basic auto-complete when Elasticsearch is not available
func (s *AIService) fallbackAutoComplete(req *models.AutoCompleteRequest) *models.AutoCompleteResponseData {
	suggestions := []models.AutoCompleteSuggestion{
		{Text: req.Query + " document", Score: 0.9},
		{Text: req.Query + " guide", Score: 0.8},
		{Text: req.Query + " report", Score: 0.7},
	}

	if req.Size > 0 && len(suggestions) > req.Size {
		suggestions = suggestions[:req.Size]
	}

	return &models.AutoCompleteResponseData{
		Suggestions: suggestions,
	}
}

// IndexDocument indexes a document for search
func (s *AIService) IndexDocument(ctx context.Context, req *models.IndexDocumentRequest) error {
	if s.fastSearch == nil {
		return fmt.Errorf("search indexing service is not available")
	}

	s.serviceLogger.Info("Indexing document", utils.F("document_id", req.Document.ID))
	return s.fastSearch.IndexDocument(ctx, req)
}

// BulkIndexDocuments indexes multiple documents for search
func (s *AIService) BulkIndexDocuments(ctx context.Context, req *models.BulkIndexRequest) error {
	if s.fastSearch == nil {
		return fmt.Errorf("search indexing service is not available")
	}

	s.serviceLogger.Info("Bulk indexing documents", utils.F("document_count", len(req.Documents)))
	return s.fastSearch.BulkIndex(ctx, req)
}

// DeleteDocument deletes a document from the search index
func (s *AIService) DeleteDocument(ctx context.Context, req *models.DeleteDocumentRequest) error {
	if s.fastSearch == nil {
		return fmt.Errorf("search indexing service is not available")
	}

	s.serviceLogger.Info("Deleting document", utils.F("document_id", req.DocumentID))
	return s.fastSearch.DeleteDocument(ctx, req)
}

// GetSearchStats returns search service statistics
func (s *AIService) GetSearchStats(ctx context.Context) map[string]interface{} {
	if s.fastSearch == nil {
		return map[string]interface{}{
			"available": false,
			"message":   "Fast search service not configured",
		}
	}

	return s.fastSearch.GetStats(ctx)
}

// Close closes all AI services
func (s *AIService) Close() error {
	if s.geminiService != nil {
		return s.geminiService.Close()
	}
	return nil
}