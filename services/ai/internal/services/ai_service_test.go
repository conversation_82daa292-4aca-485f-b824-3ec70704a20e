package services

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/swork-team/ai-service/internal/config"
	"github.com/swork-team/ai-service/internal/models"
)

func TestNewAIService(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "",
		OpenAIAPIKey: "",
		EmbedModel:   "test-model",
		FAISSIndexPath: "test.index",
		FAISSMetaPath:  "test.meta",
		Port:         "8080",
		Environment:  "test",
	}

	service := NewAIService(cfg)
	assert.NotNil(t, service)
	assert.NotNil(t, service.config)
	assert.NotNil(t, service.semanticSearchService)
	assert.NotNil(t, service.summarizationService)
	assert.NotNil(t, service.ragQAService)
}

func TestRAGQuery(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "",
		OpenAIAPIKey: "",
		EmbedModel:   "test-model",
		FAISSIndexPath: "test.index",
		FAISSMetaPath:  "test.meta",
		Port:         "8080",
		Environment:  "test",
	}

	service := NewAIService(cfg)
	ctx := context.Background()

	req := &models.RAGQueryRequest{
		Question: "What is artificial intelligence?",
		TeamID:   nil,
	}

	result, err := service.RAGQuery(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Contains(t, result.Answer, "What is artificial intelligence?")
	assert.NotEmpty(t, result.Sources)
}

func TestSemanticSearch(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "",
		OpenAIAPIKey: "",
		EmbedModel:   "test-model",
		FAISSIndexPath: "test.index",
		FAISSMetaPath:  "test.meta",
		Port:         "8080",
		Environment:  "test",
	}

	service := NewAIService(cfg)
	ctx := context.Background()

	req := &models.SemanticSearchRequest{
		Query: "test query",
		TopK:  3,
	}

	result, err := service.SemanticSearch(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotEmpty(t, result.Results)
	assert.LessOrEqual(t, len(result.Results), 3)
}

func TestSummarizeDocument(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "",
		OpenAIAPIKey: "",
		EmbedModel:   "test-model",
		FAISSIndexPath: "test.index",
		FAISSMetaPath:  "test.meta",
		Port:         "8080",
		Environment:  "test",
	}

	service := NewAIService(cfg)
	ctx := context.Background()

	req := &models.SummarizationRequest{
		Content:  "This is a long document that needs to be summarized. It contains important information about various topics.",
		Language: "Vietnamese",
	}

	result, err := service.SummarizeDocument(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotEmpty(t, result.Summary)
	assert.Equal(t, "Vietnamese", result.Language)
}

func TestRAGQA(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "",
		OpenAIAPIKey: "",
		EmbedModel:   "test-model",
		FAISSIndexPath: "test.index",
		FAISSMetaPath:  "test.meta",
		Port:         "8080",
		Environment:  "test",
	}

	service := NewAIService(cfg)
	ctx := context.Background()

	req := &models.RAGQARequest{
		Question: "What is the main topic?",
		DocumentContents: []string{
			"This document discusses artificial intelligence and machine learning.",
			"AI is transforming various industries including healthcare and finance.",
		},
		Language: "Vietnamese",
	}

	result, err := service.RAGQA(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotEmpty(t, result.Answer)
	assert.Equal(t, len(req.DocumentContents), len(result.Sources))
}
