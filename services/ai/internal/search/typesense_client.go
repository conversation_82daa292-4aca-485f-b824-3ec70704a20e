package search

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/typesense/typesense-go/typesense"
	"github.com/typesense/typesense-go/typesense/api"
)

// TypesenseConfig holds Typesense configuration
type TypesenseConfig struct {
	Addresses    []string
	APIKey       string
	Timeout      time.Duration
	MaxRetries   int
	EnableSSL    bool
	ConnectionTimeoutSeconds int
	HealthcheckIntervalSeconds int
}

// TypesenseClient wraps the official Typesense client with additional functionality
type TypesenseClient struct {
	client *typesense.Client
	config *TypesenseConfig
}

// NewTypesenseClient creates a new Typesense client with the given configuration
func NewTypesenseClient(config *TypesenseConfig) (*TypesenseClient, error) {
	// Set default values
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.ConnectionTimeoutSeconds == 0 {
		config.ConnectionTimeoutSeconds = 10
	}
	if config.HealthcheckIntervalSeconds == 0 {
		config.HealthcheckIntervalSeconds = 15
	}
	if len(config.Addresses) == 0 {
		config.Addresses = []string{"http://localhost:8108"}
	}

	// Build Typesense nodes configuration
	var nodes []typesense.Node
	for _, addr := range config.Addresses {
		protocol := "http"
		if config.EnableSSL {
			protocol = "https"
		}

		// Parse address to extract host and port
		host := addr
		port := "8108"
		if strings.Contains(addr, "://") {
			parts := strings.Split(addr, "://")
			if len(parts) > 1 {
				protocol = parts[0]
				host = parts[1]
			}
		}
		if strings.Contains(host, ":") {
			hostParts := strings.Split(host, ":")
			if len(hostParts) == 2 {
				host = hostParts[0]
				port = hostParts[1]
			}
		}

		nodes = append(nodes, typesense.Node{
			Host:     host,
			Port:     port,
			Protocol: protocol,
		})
	}

	// Create Typesense client configuration
	clientConfig := &typesense.Config{
		APIKey:                     config.APIKey,
		Nodes:                      nodes,
		ConnectionTimeoutSeconds:   config.ConnectionTimeoutSeconds,
		HealthcheckIntervalSeconds: config.HealthcheckIntervalSeconds,
	}

	// Create the client
	client := typesense.NewClient(clientConfig)

	tsClient := &TypesenseClient{
		client: client,
		config: config,
	}

	// Test the connection
	if err := tsClient.Ping(context.Background()); err != nil {
		log.Printf("Warning: Typesense connection test failed: %v", err)
	}

	return tsClient, nil
}

// Ping tests the connection to Typesense
func (c *TypesenseClient) Ping(ctx context.Context) error {
	// Typesense doesn't have a dedicated ping endpoint, so we'll use health check
	_, err := c.client.Health(ctx)
	if err != nil {
		return fmt.Errorf("ping request failed: %w", err)
	}

	return nil
}

// Health checks the health of the Typesense cluster
func (c *TypesenseClient) Health(ctx context.Context) (*ClusterHealth, error) {
	healthResponse, err := c.client.Health(ctx)
	if err != nil {
		return nil, fmt.Errorf("health request failed: %w", err)
	}

	// Convert Typesense health response to our ClusterHealth format
	health := &ClusterHealth{
		Status: "green", // Typesense is either healthy or not
		ClusterName: "typesense-cluster",
		NumberOfNodes: 1, // Default for single node
	}

	// If we got a response, the cluster is healthy
	if healthResponse != nil && healthResponse.Ok {
		health.Status = "green"
	} else {
		health.Status = "red"
	}

	return health, nil
}

// CreateCollection creates a Typesense collection with the given schema
func (c *TypesenseClient) CreateCollection(ctx context.Context, collectionName string, schema map[string]interface{}) error {
	// Convert Elasticsearch mapping to Typesense schema
	typesenseSchema := c.convertMappingToSchema(collectionName, schema)

	// Create the collection
	_, err := c.client.Collections().Create(ctx, typesenseSchema)
	if err != nil {
		// Collection already exists is not an error
		if strings.Contains(err.Error(), "already exists") {
			return nil
		}
		return fmt.Errorf("create collection failed: %w", err)
	}

	return nil
}

// convertMappingToSchema converts Elasticsearch mapping to Typesense schema
func (c *TypesenseClient) convertMappingToSchema(name string, mapping map[string]interface{}) *api.CollectionSchema {
	schema := &api.CollectionSchema{
		Name: name,
		Fields: []api.Field{
			{Name: "id", Type: "string"},
			{Name: "title", Type: "string"},
			{Name: "content", Type: "string"},
			{Name: "summary", Type: "string", Optional: true},
			{Name: "document_type", Type: "string", Facet: true},
			{Name: "user_id", Type: "string", Facet: true},
			{Name: "team_id", Type: "string", Facet: true, Optional: true},
			{Name: "tags", Type: "string[]", Facet: true, Optional: true},
			{Name: "language", Type: "string", Facet: true, Optional: true},
			{Name: "created_at", Type: "int64"},
			{Name: "updated_at", Type: "int64"},
		},
		DefaultSortingField: "created_at",
	}

	return schema
}

// CollectionExists checks if a collection exists
func (c *TypesenseClient) CollectionExists(ctx context.Context, collectionName string) (bool, error) {
	_, err := c.client.Collection(collectionName).Retrieve(ctx)
	if err != nil {
		// If collection doesn't exist, Typesense returns an error
		if strings.Contains(err.Error(), "Not Found") || strings.Contains(err.Error(), "not found") {
			return false, nil
		}
		return false, fmt.Errorf("collection exists request failed: %w", err)
	}

	return true, nil
}

// DeleteCollection deletes a collection
func (c *TypesenseClient) DeleteCollection(ctx context.Context, collectionName string) error {
	_, err := c.client.Collection(collectionName).Delete(ctx)
	if err != nil {
		// Collection not found is not an error
		if strings.Contains(err.Error(), "Not Found") || strings.Contains(err.Error(), "not found") {
			return nil
		}
		return fmt.Errorf("delete collection failed: %w", err)
	}

	return nil
}

// GetClient returns the underlying Typesense client
func (c *TypesenseClient) GetClient() *typesense.Client {
	return c.client
}

// IsHealthy returns true if the Typesense cluster is healthy
func (c *TypesenseClient) IsHealthy(ctx context.Context) bool {
	health, err := c.Health(ctx)
	if err != nil {
		return false
	}
	return health.Status == "green"
}

// ClusterHealth represents cluster health information (compatible with both Elasticsearch and Typesense)
type ClusterHealth struct {
	ClusterName         string `json:"cluster_name"`
	Status              string `json:"status"`
	TimedOut            bool   `json:"timed_out"`
	NumberOfNodes       int    `json:"number_of_nodes"`
	NumberOfDataNodes   int    `json:"number_of_data_nodes"`
	ActivePrimaryShards int    `json:"active_primary_shards"`
	ActiveShards        int    `json:"active_shards"`
	RelocatingShards    int    `json:"relocating_shards"`
	InitializingShards  int    `json:"initializing_shards"`
	UnassignedShards    int    `json:"unassigned_shards"`
}
