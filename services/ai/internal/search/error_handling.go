package search

import (
	"context"
	"fmt"
	"log"
	"time"
)

// ErrorHandler provides robust error handling and recovery mechanisms
type ErrorHandler struct {
	maxRetries    int
	retryDelay    time.Duration
	circuitBreaker *CircuitBreaker
}

// NewErrorHandler creates a new error handler
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{
		maxRetries:     3,
		retryDelay:     time.Second,
		circuitBreaker: NewCircuitBreaker(5, time.Minute*5),
	}
}

// ExecuteWithRetry executes a function with retry logic
func (eh *ErrorHandler) ExecuteWithRetry(ctx context.Context, operation func() error) error {
	var lastErr error
	
	for attempt := 0; attempt <= eh.maxRetries; attempt++ {
		if attempt > 0 {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(eh.retryDelay * time.Duration(attempt)):
				// Continue with retry
			}
		}
		
		err := operation()
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		// Don't retry on certain types of errors
		if !eh.isRetryableError(err) {
			break
		}
		
		log.Printf("Operation failed (attempt %d/%d): %v", attempt+1, eh.maxRetries+1, err)
	}
	
	return fmt.Errorf("operation failed after %d attempts: %w", eh.maxRetries+1, lastErr)
}

// ExecuteWithCircuitBreaker executes a function with circuit breaker pattern
func (eh *ErrorHandler) ExecuteWithCircuitBreaker(operation func() error) error {
	return eh.circuitBreaker.Execute(operation)
}

// isRetryableError determines if an error is retryable
func (eh *ErrorHandler) isRetryableError(err error) bool {
	if err == nil {
		return false
	}
	
	errStr := err.Error()
	
	// Network-related errors are retryable
	retryableErrors := []string{
		"connection refused",
		"timeout",
		"temporary failure",
		"service unavailable",
		"too many requests",
	}
	
	for _, retryable := range retryableErrors {
		if contains(errStr, retryable) {
			return true
		}
	}
	
	return false
}

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	maxFailures   int
	resetTimeout  time.Duration
	failures      int
	lastFailTime  time.Time
	state         CircuitState
}

// CircuitState represents the state of the circuit breaker
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        CircuitClosed,
	}
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(operation func() error) error {
	switch cb.state {
	case CircuitOpen:
		if time.Since(cb.lastFailTime) > cb.resetTimeout {
			cb.state = CircuitHalfOpen
			cb.failures = 0
		} else {
			return fmt.Errorf("circuit breaker is open")
		}
	case CircuitHalfOpen:
		// Allow one request to test if service is back
	case CircuitClosed:
		// Normal operation
	}
	
	err := operation()
	
	if err != nil {
		cb.failures++
		cb.lastFailTime = time.Now()
		
		if cb.failures >= cb.maxFailures {
			cb.state = CircuitOpen
		} else if cb.state == CircuitHalfOpen {
			cb.state = CircuitOpen
		}
		
		return err
	}
	
	// Success - reset circuit breaker
	cb.failures = 0
	cb.state = CircuitClosed
	
	return nil
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() CircuitState {
	return cb.state
}

// HealthChecker provides health checking functionality
type HealthChecker struct {
	client *ElasticsearchClient
}

// NewHealthChecker creates a new health checker
func NewHealthChecker(client *ElasticsearchClient) *HealthChecker {
	return &HealthChecker{
		client: client,
	}
}

// CheckHealth performs a comprehensive health check
func (hc *HealthChecker) CheckHealth(ctx context.Context) *HealthStatus {
	status := &HealthStatus{
		Timestamp: time.Now(),
		Checks:    make(map[string]CheckResult),
	}
	
	// Check Elasticsearch connectivity
	status.Checks["elasticsearch_ping"] = hc.checkElasticsearchPing(ctx)
	
	// Check cluster health
	status.Checks["cluster_health"] = hc.checkClusterHealth(ctx)
	
	// Check index existence
	status.Checks["index_exists"] = hc.checkIndexExists(ctx)
	
	// Determine overall status
	status.Overall = hc.determineOverallStatus(status.Checks)
	
	return status
}

// checkElasticsearchPing checks if Elasticsearch is responding
func (hc *HealthChecker) checkElasticsearchPing(ctx context.Context) CheckResult {
	start := time.Now()
	err := hc.client.Ping(ctx)
	duration := time.Since(start)
	
	if err != nil {
		return CheckResult{
			Status:   "unhealthy",
			Message:  fmt.Sprintf("Elasticsearch ping failed: %v", err),
			Duration: duration,
		}
	}
	
	return CheckResult{
		Status:   "healthy",
		Message:  "Elasticsearch is responding",
		Duration: duration,
	}
}

// checkClusterHealth checks the Elasticsearch cluster health
func (hc *HealthChecker) checkClusterHealth(ctx context.Context) CheckResult {
	start := time.Now()
	health, err := hc.client.Health(ctx)
	duration := time.Since(start)
	
	if err != nil {
		return CheckResult{
			Status:   "unhealthy",
			Message:  fmt.Sprintf("Failed to get cluster health: %v", err),
			Duration: duration,
		}
	}
	
	status := "healthy"
	if health.Status == "red" {
		status = "unhealthy"
	} else if health.Status == "yellow" {
		status = "degraded"
	}
	
	return CheckResult{
		Status:   status,
		Message:  fmt.Sprintf("Cluster status: %s", health.Status),
		Duration: duration,
		Metadata: map[string]interface{}{
			"cluster_name":    health.ClusterName,
			"number_of_nodes": health.NumberOfNodes,
			"active_shards":   health.ActiveShards,
		},
	}
}

// checkIndexExists checks if the search index exists
func (hc *HealthChecker) checkIndexExists(ctx context.Context) CheckResult {
	start := time.Now()
	exists, err := hc.client.IndexExists(ctx, "swork_documents")
	duration := time.Since(start)
	
	if err != nil {
		return CheckResult{
			Status:   "unhealthy",
			Message:  fmt.Sprintf("Failed to check index existence: %v", err),
			Duration: duration,
		}
	}
	
	if !exists {
		return CheckResult{
			Status:   "degraded",
			Message:  "Search index does not exist",
			Duration: duration,
		}
	}
	
	return CheckResult{
		Status:   "healthy",
		Message:  "Search index exists",
		Duration: duration,
	}
}

// determineOverallStatus determines the overall health status
func (hc *HealthChecker) determineOverallStatus(checks map[string]CheckResult) string {
	hasUnhealthy := false
	hasDegraded := false
	
	for _, check := range checks {
		switch check.Status {
		case "unhealthy":
			hasUnhealthy = true
		case "degraded":
			hasDegraded = true
		}
	}
	
	if hasUnhealthy {
		return "unhealthy"
	}
	if hasDegraded {
		return "degraded"
	}
	return "healthy"
}

// HealthStatus represents the overall health status
type HealthStatus struct {
	Overall   string                 `json:"overall"`
	Timestamp time.Time              `json:"timestamp"`
	Checks    map[string]CheckResult `json:"checks"`
}

// CheckResult represents the result of a single health check
type CheckResult struct {
	Status   string                 `json:"status"`
	Message  string                 `json:"message"`
	Duration time.Duration          `json:"duration"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Utility function
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 || 
		(len(s) > len(substr) && s[:len(substr)] == substr) ||
		(len(s) > len(substr) && s[len(s)-len(substr):] == substr))
}
