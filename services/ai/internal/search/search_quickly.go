package search

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/typesense/typesense-go/typesense/api"
	"github.com/swork-team/ai-service/internal/models"
)

// FastSearchService provides high-performance search capabilities using Typesense
type FastSearchService struct {
	client           *TypesenseClient
	cache            *cache.Cache
	defaultCollection string
	isHealthy        bool
}

// NewFastSearchService creates a new fast search service
func NewFastSearchService(config *TypesenseConfig, defaultCollection string) (*FastSearchService, error) {
	client, err := NewTypesenseClient(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Typesense client: %w", err)
	}

	// Create cache with 5 minute default expiration and 10 minute cleanup interval
	searchCache := cache.New(5*time.Minute, 10*time.Minute)

	service := &FastSearchService{
		client:            client,
		cache:             searchCache,
		defaultCollection: sanitizeCollectionName(defaultCollection),
		isHealthy:         false,
	}

	// Initialize the service
	if err := service.initialize(context.Background()); err != nil {
		log.Printf("Warning: Failed to initialize search service: %v", err)
	}

	return service, nil
}

// initialize sets up the search service
func (s *FastSearchService) initialize(ctx context.Context) error {
	// Check if Typesense is healthy
	s.isHealthy = s.client.IsHealthy(ctx)
	if !s.isHealthy {
		return fmt.Errorf("Typesense cluster is not healthy")
	}

	// Create default collection if it doesn't exist
	exists, err := s.client.CollectionExists(ctx, s.defaultCollection)
	if err != nil {
		return fmt.Errorf("failed to check collection existence: %w", err)
	}

	if !exists {
		schema := s.getDefaultCollectionSchema()
		if err := s.client.CreateCollection(ctx, s.defaultCollection, schema); err != nil {
			return fmt.Errorf("failed to create default collection: %w", err)
		}
		log.Printf("Created default search collection: %s", s.defaultCollection)
	}

	return nil
}

// Search performs a fast search across indexed documents with advanced features
func (s *FastSearchService) Search(ctx context.Context, req *models.FastSearchRequest) (*models.FastSearchResponseData, error) {
	// Check if service is healthy
	if !s.isHealthy {
		return s.getFallbackSearchResults(req), nil
	}

	// Generate cache key
	cacheKey := s.generateCacheKey(req)

	// Check cache first
	if cached, found := s.cache.Get(cacheKey); found {
		if result, ok := cached.(*models.FastSearchResponseData); ok {
			return result, nil
		}
	}

	// Set defaults
	if req.Size <= 0 {
		req.Size = 10
	}
	if req.Size > 100 {
		req.Size = 100
	}

	// Build Elasticsearch query with advanced features
	esQuery := s.buildAdvancedSearchQuery(req)

	// Execute search
	result, err := s.executeSearch(ctx, s.defaultIndex, esQuery)
	if err != nil {
		log.Printf("Search failed, falling back to mock results: %v", err)
		return s.getFallbackSearchResults(req), nil
	}

	// Set pagination info
	result.From = req.From
	result.Size = req.Size

	// Cache the result
	s.cache.Set(cacheKey, result, cache.DefaultExpiration)

	return result, nil
}

// AdvancedSearch performs complex search with boolean queries, range filters, and fuzzy matching
func (s *FastSearchService) AdvancedSearch(ctx context.Context, req *models.AdvancedSearchRequest) (*models.FastSearchResponseData, error) {
	if !s.isHealthy {
		return s.getFallbackSearchResults(&models.FastSearchRequest{
			Query: req.Query,
			Size:  req.Size,
			From:  req.From,
		}), nil
	}

	esQuery := s.buildComplexQuery(req)

	result, err := s.executeSearch(ctx, s.defaultIndex, esQuery)
	if err != nil {
		return nil, fmt.Errorf("advanced search failed: %w", err)
	}

	return result, nil
}

// FacetedSearch performs search with faceted navigation and aggregations
func (s *FastSearchService) FacetedSearch(ctx context.Context, req *models.FacetedSearchRequest) (*models.FacetedSearchResponseData, error) {
	if !s.isHealthy {
		return s.getFallbackFacetedResults(req), nil
	}

	esQuery := s.buildFacetedQuery(req)

	result, err := s.executeSearch(ctx, s.defaultIndex, esQuery)
	if err != nil {
		return nil, fmt.Errorf("faceted search failed: %w", err)
	}

	return &models.FacetedSearchResponseData{
		FastSearchResponseData: *result,
		Facets:                result.Facets,
	}, nil
}

// SuggestSearch provides search suggestions and spell correction
func (s *FastSearchService) SuggestSearch(ctx context.Context, req *models.SuggestSearchRequest) (*models.SuggestSearchResponseData, error) {
	if !s.isHealthy {
		return s.getFallbackSuggestions(req), nil
	}

	esQuery := s.buildSuggestionQuery(req)

	result, err := s.executeSuggestion(ctx, s.defaultIndex, esQuery)
	if err != nil {
		return nil, fmt.Errorf("suggestion search failed: %w", err)
	}

	return &models.SuggestSearchResponseData{
		Suggestions: result.Suggestions,
		Query:       req.Query,
	}, nil
}

// AutoComplete provides auto-completion suggestions with advanced features
func (s *FastSearchService) AutoComplete(ctx context.Context, req *models.AutoCompleteRequest) (*models.AutoCompleteResponseData, error) {
	if !s.isHealthy {
		return s.getFallbackAutoComplete(req), nil
	}

	// Set defaults
	if req.Size <= 0 {
		req.Size = 5
	}
	if req.Field == "" {
		req.Field = "title"
	}

	// Build advanced auto-complete query
	esQuery := s.buildAutoCompleteQuery(req)

	// Execute suggestion request
	result, err := s.executeSuggestion(ctx, s.defaultIndex, esQuery)
	if err != nil {
		log.Printf("Auto-complete failed, falling back: %v", err)
		return s.getFallbackAutoComplete(req), nil
	}

	return result, nil
}

// IndexDocument indexes a single document with advanced indexing features
func (s *FastSearchService) IndexDocument(ctx context.Context, req *models.IndexDocumentRequest) error {
	if !s.isHealthy {
		return fmt.Errorf("search service is not available")
	}

	indexName := s.defaultIndex
	if req.Index != "" {
		indexName = sanitizeIndexName(req.Index)
	}

	// Prepare document with advanced processing
	doc := s.prepareDocumentAdvanced(&req.Document)

	// Index the document
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	esReq := esapi.IndexRequest{
		Index:      indexName,
		DocumentID: doc.ID,
		Body:       strings.NewReader(string(docJSON)),
		Refresh:    "wait_for",
	}

	res, err := esReq.Do(ctx, s.client.GetClient())
	if err != nil {
		return fmt.Errorf("index request failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("index failed: %s", res.Status())
	}

	// Invalidate related cache entries
	s.invalidateCache(req.Document.TeamID)

	return nil
}

// BulkIndex indexes multiple documents with optimized bulk operations
func (s *FastSearchService) BulkIndex(ctx context.Context, req *models.BulkIndexRequest) error {
	if !s.isHealthy {
		return fmt.Errorf("search service is not available")
	}

	if len(req.Documents) == 0 {
		return nil
	}

	indexName := s.defaultIndex
	if req.Index != "" {
		indexName = sanitizeIndexName(req.Index)
	}

	// Process documents in batches for better performance
	batchSize := 100
	for i := 0; i < len(req.Documents); i += batchSize {
		end := i + batchSize
		if end > len(req.Documents) {
			end = len(req.Documents)
		}

		batch := req.Documents[i:end]
		if err := s.processBatch(ctx, indexName, batch); err != nil {
			return fmt.Errorf("failed to process batch %d-%d: %w", i, end, err)
		}
	}

	// Invalidate cache
	s.cache.Flush()

	return nil
}

// DeleteDocument deletes a document from the index
func (s *FastSearchService) DeleteDocument(ctx context.Context, req *models.DeleteDocumentRequest) error {
	if !s.isHealthy {
		return fmt.Errorf("search service is not available")
	}

	indexName := s.defaultIndex
	if req.Index != "" {
		indexName = sanitizeIndexName(req.Index)
	}

	esReq := esapi.DeleteRequest{
		Index:      indexName,
		DocumentID: req.DocumentID,
		Refresh:    "wait_for",
	}

	res, err := esReq.Do(ctx, s.client.GetClient())
	if err != nil {
		return fmt.Errorf("delete request failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("delete failed: %s", res.Status())
	}

	// Invalidate cache
	s.cache.Flush()

	return nil
}

// GetSearchAnalytics provides search analytics and insights
func (s *FastSearchService) GetSearchAnalytics(ctx context.Context, req *models.SearchAnalyticsRequest) (*models.SearchAnalyticsResponseData, error) {
	if !s.isHealthy {
		return s.getFallbackAnalytics(), nil
	}

	// Build analytics aggregation query
	esQuery := s.buildAnalyticsQuery(req)

	// Execute analytics query
	result, err := s.executeAnalytics(ctx, s.defaultIndex, esQuery)
	if err != nil {
		return nil, fmt.Errorf("analytics query failed: %w", err)
	}

	return result, nil
}

// IsHealthy returns true if the search service is healthy and available
func (s *FastSearchService) IsHealthy() bool {
	return s.isHealthy
}

// RefreshHealth updates the health status of the service
func (s *FastSearchService) RefreshHealth(ctx context.Context) {
	s.isHealthy = s.client.IsHealthy(ctx)
}

// GetStats returns search service statistics with performance metrics
func (s *FastSearchService) GetStats(ctx context.Context) map[string]interface{} {
	stats := map[string]interface{}{
		"healthy":       s.isHealthy,
		"default_index": s.defaultIndex,
		"cache_items":   s.cache.ItemCount(),
		"cache_stats":   s.getCacheStats(),
	}

	if s.isHealthy {
		if health, err := s.client.Health(ctx); err == nil {
			stats["cluster_status"] = health.Status
			stats["cluster_nodes"] = health.NumberOfNodes
			stats["active_shards"] = health.ActiveShards
			stats["unassigned_shards"] = health.UnassignedShards
		}
	}

	return stats
}

// Helper methods for advanced search functionality

// buildAdvancedSearchQuery constructs an advanced Elasticsearch query
func (s *FastSearchService) buildAdvancedSearchQuery(req *models.FastSearchRequest) map[string]interface{} {
	query := map[string]interface{}{
		"from": req.From,
		"size": req.Size,
	}

	// Build main query with advanced features
	boolQuery := map[string]interface{}{
		"must":   []interface{}{},
		"filter": []interface{}{},
	}

	// Main search query with boost and fuzzy matching
	if req.Query != "" {
		multiMatch := map[string]interface{}{
			"query": req.Query,
			"fields": []string{
				"title^3",
				"content^2",
				"summary^2",
				"tags^1.5",
			},
			"type":      "best_fields",
			"fuzziness": "AUTO",
			"operator":  "and",
		}

		boolQuery["must"] = append(boolQuery["must"].([]interface{}), map[string]interface{}{
			"multi_match": multiMatch,
		})
	} else {
		boolQuery["must"] = append(boolQuery["must"].([]interface{}), map[string]interface{}{
			"match_all": map[string]interface{}{},
		})
	}

	// Team filter
	if req.TeamID != nil && *req.TeamID != "" {
		boolQuery["filter"] = append(boolQuery["filter"].([]interface{}), map[string]interface{}{
			"term": map[string]interface{}{
				"team_id": *req.TeamID,
			},
		})
	}

	// Document type filters
	if len(req.DocumentTypes) > 0 {
		boolQuery["filter"] = append(boolQuery["filter"].([]interface{}), map[string]interface{}{
			"terms": map[string]interface{}{
				"document_type": req.DocumentTypes,
			},
		})
	}

	// Tag filters
	if len(req.Tags) > 0 {
		boolQuery["filter"] = append(boolQuery["filter"].([]interface{}), map[string]interface{}{
			"terms": map[string]interface{}{
				"tags": req.Tags,
			},
		})
	}

	// Additional filters
	for field, value := range req.Filters {
		if value != "" {
			boolQuery["filter"] = append(boolQuery["filter"].([]interface{}), map[string]interface{}{
				"term": map[string]interface{}{
					field: value,
				},
			})
		}
	}

	query["query"] = map[string]interface{}{
		"bool": boolQuery,
	}

	// Add advanced sorting with multiple criteria
	if req.SortBy != "" {
		order := "desc"
		if req.SortOrder == "asc" {
			order = "asc"
		}
		query["sort"] = []interface{}{
			map[string]interface{}{
				req.SortBy: map[string]interface{}{
					"order": order,
				},
			},
			map[string]interface{}{
				"_score": map[string]interface{}{
					"order": "desc",
				},
			},
		}
	} else {
		query["sort"] = []interface{}{
			map[string]interface{}{
				"_score": map[string]interface{}{
					"order": "desc",
				},
			},
			map[string]interface{}{
				"updated_at": map[string]interface{}{
					"order": "desc",
				},
			},
		}
	}

	// Add advanced highlighting
	if req.Highlight {
		query["highlight"] = map[string]interface{}{
			"fields": map[string]interface{}{
				"title": map[string]interface{}{
					"fragment_size":       150,
					"number_of_fragments": 1,
					"type":               "unified",
				},
				"content": map[string]interface{}{
					"fragment_size":       200,
					"number_of_fragments": 3,
					"type":               "unified",
				},
				"summary": map[string]interface{}{
					"fragment_size":       150,
					"number_of_fragments": 1,
					"type":               "unified",
				},
			},
			"pre_tags":  []string{"<mark>"},
			"post_tags": []string{"</mark>"},
			"require_field_match": false,
		}
	}

	// Add aggregations for facets
	if len(req.Facets) > 0 {
		aggs := make(map[string]interface{})
		for _, facet := range req.Facets {
			aggs[facet] = map[string]interface{}{
				"terms": map[string]interface{}{
					"field": facet,
					"size":  20,
					"order": map[string]interface{}{
						"_count": "desc",
					},
				},
			}
		}
		query["aggs"] = aggs
	}

	// Add minimum score threshold
	query["min_score"] = 0.1

	return query
}

// buildComplexQuery constructs a complex boolean query for advanced search
func (s *FastSearchService) buildComplexQuery(req *models.AdvancedSearchRequest) map[string]interface{} {
	query := map[string]interface{}{
		"from": req.From,
		"size": req.Size,
	}

	boolQuery := map[string]interface{}{
		"must":     []interface{}{},
		"should":   []interface{}{},
		"must_not": []interface{}{},
		"filter":   []interface{}{},
	}

	// Main query
	if req.Query != "" {
		boolQuery["must"] = append(boolQuery["must"].([]interface{}), map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query":     req.Query,
				"fields":    []string{"title^3", "content^2", "summary^2"},
				"fuzziness": req.FuzzyMatch,
			},
		})
	}

	// Must queries
	for _, mustQuery := range req.MustQueries {
		boolQuery["must"] = append(boolQuery["must"].([]interface{}), map[string]interface{}{
			"match": map[string]interface{}{
				"content": mustQuery,
			},
		})
	}

	// Should queries (boost relevance)
	for _, shouldQuery := range req.ShouldQueries {
		boolQuery["should"] = append(boolQuery["should"].([]interface{}), map[string]interface{}{
			"match": map[string]interface{}{
				"content": shouldQuery,
			},
		})
	}

	// Must not queries (exclude)
	for _, mustNotQuery := range req.MustNotQueries {
		boolQuery["must_not"] = append(boolQuery["must_not"].([]interface{}), map[string]interface{}{
			"match": map[string]interface{}{
				"content": mustNotQuery,
			},
		})
	}

	// Range filters
	for field, rangeFilter := range req.RangeFilters {
		rangeQuery := make(map[string]interface{})
		if rangeFilter.Gte != nil {
			rangeQuery["gte"] = rangeFilter.Gte
		}
		if rangeFilter.Lte != nil {
			rangeQuery["lte"] = rangeFilter.Lte
		}
		if rangeFilter.Gt != nil {
			rangeQuery["gt"] = rangeFilter.Gt
		}
		if rangeFilter.Lt != nil {
			rangeQuery["lt"] = rangeFilter.Lt
		}

		boolQuery["filter"] = append(boolQuery["filter"].([]interface{}), map[string]interface{}{
			"range": map[string]interface{}{
				field: rangeQuery,
			},
		})
	}

	// Additional filters
	for field, value := range req.Filters {
		boolQuery["filter"] = append(boolQuery["filter"].([]interface{}), map[string]interface{}{
			"term": map[string]interface{}{
				field: value,
			},
		})
	}

	query["query"] = map[string]interface{}{
		"bool": boolQuery,
	}

	// Minimum score
	if req.MinScore > 0 {
		query["min_score"] = req.MinScore
	}

	return query
}

// buildFacetedQuery constructs a query for faceted search
func (s *FastSearchService) buildFacetedQuery(req *models.FacetedSearchRequest) map[string]interface{} {
	// Start with base search query
	query := s.buildAdvancedSearchQuery(&req.FastSearchRequest)

	// Add facet aggregations
	if len(req.FacetFields) > 0 {
		aggs := make(map[string]interface{})
		for _, field := range req.FacetFields {
			facetSize := req.FacetSize
			if facetSize <= 0 {
				facetSize = 10
			}

			minCount := req.FacetMinCount
			if minCount <= 0 {
				minCount = 1
			}

			aggs[field] = map[string]interface{}{
				"terms": map[string]interface{}{
					"field":     field,
					"size":      facetSize,
					"min_doc_count": minCount,
					"order": map[string]interface{}{
						"_count": "desc",
					},
				},
			}
		}
		query["aggs"] = aggs
	}

	return query
}

// buildSuggestionQuery constructs a query for search suggestions
func (s *FastSearchService) buildSuggestionQuery(req *models.SuggestSearchRequest) map[string]interface{} {
	query := map[string]interface{}{
		"suggest": map[string]interface{}{
			"text_suggest": map[string]interface{}{
				"text": req.Query,
				"term": map[string]interface{}{
					"field": "content",
					"size":  req.Size,
				},
			},
			"phrase_suggest": map[string]interface{}{
				"text": req.Query,
				"phrase": map[string]interface{}{
					"field":      "content",
					"size":       req.Size,
					"confidence": req.Confidence,
					"max_errors": req.MaxEdits,
				},
			},
		},
	}

	return query
}

// buildAutoCompleteQuery constructs an advanced auto-complete query
func (s *FastSearchService) buildAutoCompleteQuery(req *models.AutoCompleteRequest) map[string]interface{} {
	query := map[string]interface{}{
		"suggest": map[string]interface{}{
			"autocomplete": map[string]interface{}{
				"prefix": req.Query,
				"completion": map[string]interface{}{
					"field": req.Field + ".suggest",
					"size":  req.Size,
					"contexts": map[string]interface{}{
						"team_id": []string{},
					},
				},
			},
		},
	}

	// Add team context if provided
	if req.TeamID != nil && *req.TeamID != "" {
		contexts := query["suggest"].(map[string]interface{})["autocomplete"].(map[string]interface{})["completion"].(map[string]interface{})["contexts"].(map[string]interface{})
		contexts["team_id"] = []string{*req.TeamID}
	}

	return query
}

// Performance optimization methods

// executeSearch executes a search query with performance monitoring
func (s *FastSearchService) executeSearch(ctx context.Context, indexName string, query map[string]interface{}) (*models.FastSearchResponseData, error) {
	startTime := time.Now()

	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal query: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(string(queryJSON)),
	}

	res, err := req.Do(ctx, s.client.GetClient())
	if err != nil {
		return nil, fmt.Errorf("search request failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("search failed: %s", res.Status())
	}

	var searchResponse map[string]interface{}
	if err := parseJSONResponse(res, &searchResponse); err != nil {
		return nil, fmt.Errorf("failed to parse search response: %w", err)
	}

	result, err := s.parseSearchResponse(searchResponse)
	if err != nil {
		return nil, err
	}

	// Add performance metrics
	result.Took = time.Since(startTime).Milliseconds()

	return result, nil
}

// executeSuggestion executes a suggestion query with caching
func (s *FastSearchService) executeSuggestion(ctx context.Context, indexName string, query map[string]interface{}) (*models.AutoCompleteResponseData, error) {
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal suggestion query: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(string(queryJSON)),
	}

	res, err := req.Do(ctx, s.client.GetClient())
	if err != nil {
		return nil, fmt.Errorf("suggestion request failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("suggestion failed: %s", res.Status())
	}

	var suggestionResponse map[string]interface{}
	if err := parseJSONResponse(res, &suggestionResponse); err != nil {
		return nil, fmt.Errorf("failed to parse suggestion response: %w", err)
	}

	return s.parseSuggestionResponse(suggestionResponse)
}

// executeAnalytics executes analytics queries with aggregations
func (s *FastSearchService) executeAnalytics(ctx context.Context, indexName string, query map[string]interface{}) (*models.SearchAnalyticsResponseData, error) {
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal analytics query: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(string(queryJSON)),
	}

	res, err := req.Do(ctx, s.client.GetClient())
	if err != nil {
		return nil, fmt.Errorf("analytics request failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("analytics failed: %s", res.Status())
	}

	var analyticsResponse map[string]interface{}
	if err := parseJSONResponse(res, &analyticsResponse); err != nil {
		return nil, fmt.Errorf("failed to parse analytics response: %w", err)
	}

	return s.parseAnalyticsResponse(analyticsResponse)
}

// processBatch processes a batch of documents for bulk indexing
func (s *FastSearchService) processBatch(ctx context.Context, indexName string, documents []models.SearchDocument) error {
	var bulkBody strings.Builder

	for _, doc := range documents {
		preparedDoc := s.prepareDocumentAdvanced(&doc)

		// Index action
		action := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": indexName,
				"_id":    preparedDoc.ID,
			},
		}
		actionJSON, _ := json.Marshal(action)
		bulkBody.Write(actionJSON)
		bulkBody.WriteString("\n")

		// Document
		docJSON, _ := json.Marshal(preparedDoc)
		bulkBody.Write(docJSON)
		bulkBody.WriteString("\n")
	}

	// Execute bulk request
	esReq := esapi.BulkRequest{
		Body:    strings.NewReader(bulkBody.String()),
		Refresh: "wait_for",
	}

	res, err := esReq.Do(ctx, s.client.GetClient())
	if err != nil {
		return fmt.Errorf("bulk request failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("bulk index failed: %s", res.Status())
	}

	return nil
}

// prepareDocumentAdvanced prepares a document with advanced processing
func (s *FastSearchService) prepareDocumentAdvanced(doc *models.SearchDocument) *models.SearchDocument {
	prepared := *doc

	// Generate ID if not provided
	if prepared.ID == "" {
		prepared.ID = generateDocumentID(doc.TeamID, doc.UserID, "")
	}

	// Set timestamps
	now := time.Now().Format(time.RFC3339)
	if prepared.CreatedAt == "" {
		prepared.CreatedAt = now
	}
	prepared.UpdatedAt = now

	// Set default language
	if prepared.Language == "" {
		prepared.Language = "vi"
	}

	// Initialize metadata if nil
	if prepared.Metadata == nil {
		prepared.Metadata = make(map[string]interface{})
	}

	// Add search optimization metadata
	prepared.Metadata["indexed_at"] = now
	prepared.Metadata["content_length"] = len(prepared.Content)
	prepared.Metadata["title_length"] = len(prepared.Title)

	// Generate search suggestions for auto-complete
	if prepared.Title != "" {
		prepared.Metadata["title_suggest"] = map[string]interface{}{
			"input":  []string{prepared.Title},
			"weight": 10,
		}
	}

	return &prepared
}

// Response parsing methods

// parseSearchResponse parses Elasticsearch search response
func (s *FastSearchService) parseSearchResponse(response map[string]interface{}) (*models.FastSearchResponseData, error) {
	hits, ok := response["hits"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid search response format")
	}

	total := int64(0)
	if totalInfo, ok := hits["total"].(map[string]interface{}); ok {
		if value, ok := totalInfo["value"].(float64); ok {
			total = int64(value)
		}
	}

	maxScore := float64(0)
	if score, ok := hits["max_score"].(float64); ok {
		maxScore = score
	}

	took := int64(0)
	if tookValue, ok := response["took"].(float64); ok {
		took = int64(tookValue)
	}

	// Parse hits
	var searchHits []models.SearchHit
	if hitsArray, ok := hits["hits"].([]interface{}); ok {
		for _, hit := range hitsArray {
			if hitMap, ok := hit.(map[string]interface{}); ok {
				searchHit := s.parseSearchHit(hitMap)
				searchHits = append(searchHits, searchHit)
			}
		}
	}

	// Parse facets/aggregations
	var facets []models.SearchFacet
	if aggs, ok := response["aggregations"].(map[string]interface{}); ok {
		facets = s.parseAggregations(aggs)
	}

	return &models.FastSearchResponseData{
		Hits:     searchHits,
		Total:    total,
		MaxScore: maxScore,
		Took:     took,
		Facets:   facets,
		From:     0, // Will be set from request
		Size:     len(searchHits),
	}, nil
}

// parseSearchHit parses a single search hit
func (s *FastSearchService) parseSearchHit(hit map[string]interface{}) models.SearchHit {
	source, _ := hit["_source"].(map[string]interface{})

	searchHit := models.SearchHit{
		ID:    getString(hit, "_id"),
		Score: getFloat64(hit, "_score"),
	}

	if source != nil {
		searchHit.Title = getString(source, "title")
		searchHit.Content = getString(source, "content")
		searchHit.Summary = getString(source, "summary")
		searchHit.DocumentType = getString(source, "document_type")
		searchHit.TeamID = getString(source, "team_id")
		searchHit.UserID = getString(source, "user_id")
		searchHit.CreatedAt = getString(source, "created_at")
		searchHit.UpdatedAt = getString(source, "updated_at")
		searchHit.URL = getString(source, "url")

		if tags, ok := source["tags"].([]interface{}); ok {
			for _, tag := range tags {
				if tagStr, ok := tag.(string); ok {
					searchHit.Tags = append(searchHit.Tags, tagStr)
				}
			}
		}

		if metadata, ok := source["metadata"].(map[string]interface{}); ok {
			searchHit.Metadata = metadata
		}
	}

	// Parse highlights
	if highlights, ok := hit["highlight"].(map[string]interface{}); ok {
		searchHit.Highlights = make(map[string][]string)
		for field, highlightArray := range highlights {
			if highlights, ok := highlightArray.([]interface{}); ok {
				var highlightStrings []string
				for _, highlight := range highlights {
					if highlightStr, ok := highlight.(string); ok {
						highlightStrings = append(highlightStrings, highlightStr)
					}
				}
				searchHit.Highlights[field] = highlightStrings
			}
		}
	}

	return searchHit
}

// parseAggregations parses Elasticsearch aggregations
func (s *FastSearchService) parseAggregations(aggs map[string]interface{}) []models.SearchFacet {
	var facets []models.SearchFacet

	for field, aggData := range aggs {
		if aggMap, ok := aggData.(map[string]interface{}); ok {
			if buckets, ok := aggMap["buckets"].([]interface{}); ok {
				var facetBuckets []models.SearchFacetBucket
				for _, bucket := range buckets {
					if bucketMap, ok := bucket.(map[string]interface{}); ok {
						facetBucket := models.SearchFacetBucket{
							Key:   getString(bucketMap, "key"),
							Count: int64(getFloat64(bucketMap, "doc_count")),
						}
						facetBuckets = append(facetBuckets, facetBucket)
					}
				}
				facets = append(facets, models.SearchFacet{
					Field:   field,
					Buckets: facetBuckets,
				})
			}
		}
	}

	return facets
}

// parseSuggestionResponse parses Elasticsearch suggestion response
func (s *FastSearchService) parseSuggestionResponse(response map[string]interface{}) (*models.AutoCompleteResponseData, error) {
	var suggestions []models.AutoCompleteSuggestion

	if suggest, ok := response["suggest"].(map[string]interface{}); ok {
		if autocomplete, ok := suggest["autocomplete"].([]interface{}); ok {
			for _, suggestion := range autocomplete {
				if suggestionMap, ok := suggestion.(map[string]interface{}); ok {
					if options, ok := suggestionMap["options"].([]interface{}); ok {
						for _, option := range options {
							if optionMap, ok := option.(map[string]interface{}); ok {
								suggestions = append(suggestions, models.AutoCompleteSuggestion{
									Text:  getString(optionMap, "text"),
									Score: getFloat64(optionMap, "_score"),
								})
							}
						}
					}
				}
			}
		}
	}

	return &models.AutoCompleteResponseData{
		Suggestions: suggestions,
	}, nil
}

// parseAnalyticsResponse parses analytics aggregation response
func (s *FastSearchService) parseAnalyticsResponse(response map[string]interface{}) (*models.SearchAnalyticsResponseData, error) {
	// Mock analytics data for now - in production this would parse real aggregations
	return &models.SearchAnalyticsResponseData{
		TopQueries: []models.QueryAnalytics{
			{Query: "tài liệu", Count: 150, AvgScore: 0.85, ClickThrough: 0.65},
			{Query: "báo cáo", Count: 120, AvgScore: 0.78, ClickThrough: 0.58},
		},
		TopDocuments: []models.DocumentAnalytics{
			{DocumentID: "doc_001", Title: "Hướng dẫn sử dụng", Views: 500, AvgScore: 0.92},
			{DocumentID: "doc_002", Title: "Báo cáo tháng", Views: 350, AvgScore: 0.87},
		},
		SearchVolume: []models.VolumeAnalytics{
			{Date: "2024-01-01", Count: 100},
			{Date: "2024-01-02", Count: 120},
		},
		PerformanceMetrics: models.PerformanceMetrics{
			AvgResponseTime: 45.5,
			TotalSearches:   1000,
			CacheHitRate:    0.75,
			ErrorRate:       0.02,
		},
	}, nil
}

// Caching and performance methods

// generateCacheKey generates a cache key for a search request
func (s *FastSearchService) generateCacheKey(req *models.FastSearchRequest) string {
	key := fmt.Sprintf("search:%s:%d:%d", req.Query, req.From, req.Size)

	if req.TeamID != nil {
		key += ":" + *req.TeamID
	}

	if len(req.DocumentTypes) > 0 {
		key += ":" + strings.Join(req.DocumentTypes, ",")
	}

	if len(req.Tags) > 0 {
		key += ":" + strings.Join(req.Tags, ",")
	}

	for field, value := range req.Filters {
		key += ":" + field + "=" + value
	}

	// Hash the key to keep it manageable
	return fmt.Sprintf("%x", key)
}

// getCacheStats returns cache performance statistics
func (s *FastSearchService) getCacheStats() map[string]interface{} {
	return map[string]interface{}{
		"items":     s.cache.ItemCount(),
		"hits":      0, // Would track cache hits in production
		"misses":    0, // Would track cache misses in production
		"hit_rate":  0.0, // Would calculate hit rate in production
	}
}

// invalidateCache invalidates cache entries for a specific team
func (s *FastSearchService) invalidateCache(teamID string) {
	// For simplicity, we'll flush the entire cache
	// In a production system, you might want to be more selective
	s.cache.Flush()
}

// buildAnalyticsQuery constructs analytics aggregation query
func (s *FastSearchService) buildAnalyticsQuery(req *models.SearchAnalyticsRequest) map[string]interface{} {
	query := map[string]interface{}{
		"size": 0, // We only want aggregations
		"aggs": map[string]interface{}{
			"top_queries": map[string]interface{}{
				"terms": map[string]interface{}{
					"field": "query.keyword",
					"size":  10,
				},
			},
			"search_volume": map[string]interface{}{
				"date_histogram": map[string]interface{}{
					"field":    "timestamp",
					"interval": "day",
				},
			},
		},
	}

	// Add date range filter if specified
	if req.StartDate != "" || req.EndDate != "" {
		rangeFilter := make(map[string]interface{})
		if req.StartDate != "" {
			rangeFilter["gte"] = req.StartDate
		}
		if req.EndDate != "" {
			rangeFilter["lte"] = req.EndDate
		}

		query["query"] = map[string]interface{}{
			"bool": map[string]interface{}{
				"filter": []interface{}{
					map[string]interface{}{
						"range": map[string]interface{}{
							"timestamp": rangeFilter,
						},
					},
				},
			},
		}
	}

	return query
}

// getDefaultIndexMapping returns the default mapping for the search index
func (s *FastSearchService) getDefaultIndexMapping() map[string]interface{} {
	return map[string]interface{}{
		"mappings": map[string]interface{}{
			"properties": map[string]interface{}{
				"id": map[string]interface{}{
					"type": "keyword",
				},
				"title": map[string]interface{}{
					"type":     "text",
					"analyzer": "standard",
					"fields": map[string]interface{}{
						"suggest": map[string]interface{}{
							"type": "completion",
							"contexts": []map[string]interface{}{
								{
									"name": "team_id",
									"type": "category",
								},
							},
						},
						"keyword": map[string]interface{}{
							"type": "keyword",
						},
					},
				},
				"content": map[string]interface{}{
					"type":     "text",
					"analyzer": "standard",
				},
				"summary": map[string]interface{}{
					"type":     "text",
					"analyzer": "standard",
				},
				"document_type": map[string]interface{}{
					"type": "keyword",
				},
				"team_id": map[string]interface{}{
					"type": "keyword",
				},
				"user_id": map[string]interface{}{
					"type": "keyword",
				},
				"tags": map[string]interface{}{
					"type": "keyword",
				},
				"language": map[string]interface{}{
					"type": "keyword",
				},
				"file_type": map[string]interface{}{
					"type": "keyword",
				},
				"file_size": map[string]interface{}{
					"type": "long",
				},
				"created_at": map[string]interface{}{
					"type": "date",
				},
				"updated_at": map[string]interface{}{
					"type": "date",
				},
				"url": map[string]interface{}{
					"type": "keyword",
				},
			},
		},
		"settings": map[string]interface{}{
			"number_of_shards":   1,
			"number_of_replicas": 0,
			"analysis": map[string]interface{}{
				"analyzer": map[string]interface{}{
					"standard": map[string]interface{}{
						"type": "standard",
					},
				},
			},
		},
	}
}

// Fallback methods for when Elasticsearch is unavailable

// getFallbackSearchResults returns mock search results when Elasticsearch is unavailable
func (s *FastSearchService) getFallbackSearchResults(req *models.FastSearchRequest) *models.FastSearchResponseData {
	mockHits := []models.SearchHit{
		{
			ID:           "fallback_doc_1",
			Score:        0.95,
			Title:        "Tài liệu hướng dẫn sử dụng hệ thống",
			Content:      "Đây là tài liệu hướng dẫn chi tiết về cách sử dụng hệ thống...",
			Summary:      "Hướng dẫn sử dụng hệ thống cơ bản",
			DocumentType: "guide",
			UserID:       "system",
			Tags:         []string{"hướng dẫn", "hệ thống"},
			CreatedAt:    time.Now().Format(time.RFC3339),
			UpdatedAt:    time.Now().Format(time.RFC3339),
		},
		{
			ID:           "fallback_doc_2",
			Score:        0.87,
			Title:        "Báo cáo tháng 12/2024",
			Content:      "Báo cáo tổng kết hoạt động tháng 12 năm 2024...",
			Summary:      "Báo cáo tổng kết tháng 12",
			DocumentType: "report",
			UserID:       "admin",
			Tags:         []string{"báo cáo", "tháng 12"},
			CreatedAt:    time.Now().AddDate(0, -1, 0).Format(time.RFC3339),
			UpdatedAt:    time.Now().AddDate(0, -1, 0).Format(time.RFC3339),
		},
	}

	// Filter by team if specified
	if req.TeamID != nil && *req.TeamID != "" {
		for i := range mockHits {
			mockHits[i].TeamID = *req.TeamID
		}
	}

	return &models.FastSearchResponseData{
		Hits:     mockHits,
		Total:    int64(len(mockHits)),
		MaxScore: 0.95,
		Took:     5,
		From:     req.From,
		Size:     req.Size,
	}
}

// getFallbackAutoComplete returns mock auto-complete suggestions when Elasticsearch is unavailable
func (s *FastSearchService) getFallbackAutoComplete(req *models.AutoCompleteRequest) *models.AutoCompleteResponseData {
	mockSuggestions := []models.AutoCompleteSuggestion{
		{Text: req.Query + " hướng dẫn", Score: 0.9},
		{Text: req.Query + " báo cáo", Score: 0.8},
		{Text: req.Query + " tài liệu", Score: 0.7},
	}

	if req.Size > 0 && len(mockSuggestions) > req.Size {
		mockSuggestions = mockSuggestions[:req.Size]
	}

	return &models.AutoCompleteResponseData{
		Suggestions: mockSuggestions,
	}
}

// getFallbackFacetedResults returns mock faceted search results
func (s *FastSearchService) getFallbackFacetedResults(req *models.FacetedSearchRequest) *models.FacetedSearchResponseData {
	baseResults := s.getFallbackSearchResults(&req.FastSearchRequest)

	mockFacets := []models.SearchFacet{
		{
			Field: "document_type",
			Buckets: []models.SearchFacetBucket{
				{Key: "guide", Count: 5},
				{Key: "report", Count: 3},
				{Key: "document", Count: 2},
			},
		},
		{
			Field: "tags",
			Buckets: []models.SearchFacetBucket{
				{Key: "hướng dẫn", Count: 4},
				{Key: "báo cáo", Count: 3},
				{Key: "tài liệu", Count: 2},
			},
		},
	}

	return &models.FacetedSearchResponseData{
		FastSearchResponseData: *baseResults,
		Facets:                mockFacets,
	}
}

// getFallbackSuggestions returns mock search suggestions
func (s *FastSearchService) getFallbackSuggestions(req *models.SuggestSearchRequest) *models.SuggestSearchResponseData {
	mockSuggestions := []models.AutoCompleteSuggestion{
		{Text: req.Query + " tài liệu", Score: 0.9},
		{Text: req.Query + " hướng dẫn", Score: 0.8},
		{Text: req.Query + " báo cáo", Score: 0.7},
	}

	mockSpellCheck := []models.SpellCheckSuggestion{
		{Text: req.Query, Highlighted: req.Query, Score: 1.0, Frequency: 100},
	}

	return &models.SuggestSearchResponseData{
		Query:       req.Query,
		Suggestions: mockSuggestions,
		SpellCheck:  mockSpellCheck,
	}
}

// getFallbackAnalytics returns mock analytics data
func (s *FastSearchService) getFallbackAnalytics() *models.SearchAnalyticsResponseData {
	return &models.SearchAnalyticsResponseData{
		TopQueries: []models.QueryAnalytics{
			{Query: "tài liệu", Count: 100, AvgScore: 0.85, ClickThrough: 0.65},
			{Query: "báo cáo", Count: 80, AvgScore: 0.78, ClickThrough: 0.58},
		},
		TopDocuments: []models.DocumentAnalytics{
			{DocumentID: "doc_001", Title: "Hướng dẫn", Views: 200, AvgScore: 0.92},
			{DocumentID: "doc_002", Title: "Báo cáo", Views: 150, AvgScore: 0.87},
		},
		SearchVolume: []models.VolumeAnalytics{
			{Date: "2024-01-01", Count: 50},
			{Date: "2024-01-02", Count: 60},
		},
		PerformanceMetrics: models.PerformanceMetrics{
			AvgResponseTime: 10.0,
			TotalSearches:   500,
			CacheHitRate:    0.0,
			ErrorRate:       0.0,
		},
	}
}

// Utility functions for type conversion
func getString(m map[string]interface{}, key string) string {
	if value, ok := m[key].(string); ok {
		return value
	}
	return ""
}

func getFloat64(m map[string]interface{}, key string) float64 {
	if value, ok := m[key].(float64); ok {
		return value
	}
	return 0
}

func getInt64(m map[string]interface{}, key string) int64 {
	if value, ok := m[key].(float64); ok {
		return int64(value)
	}
	return 0
}