package search

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"
)

// sanitizeCollectionName ensures the collection name is valid for Typesense
func sanitizeCollectionName(name string) string {
	// Convert to lowercase and replace invalid characters
	name = strings.ToLower(name)
	name = strings.ReplaceAll(name, " ", "_")
	name = strings.ReplaceAll(name, "-", "_")

	// Remove any characters that aren't alphanumeric or underscore
	var result strings.Builder
	for _, r := range name {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '_' {
			result.WriteRune(r)
		}
	}

	sanitized := result.String()
	if sanitized == "" {
		return "default_collection"
	}
	return sanitized
}

// For backward compatibility
func sanitizeIndexName(name string) string {
	return sanitizeCollectionName(name)
}

// buildJSONReader creates a JSON reader from a map
func buildJSONReader(data map[string]interface{}) (io.Reader, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %w", err)
	}
	
	return bytes.NewReader(jsonData), nil
}

// parseJSONResponse parses JSON response from any source
func parseJSONResponse(body []byte, target interface{}) error {
	if len(body) == 0 {
		return fmt.Errorf("empty response body")
	}

	if err := json.Unmarshal(body, target); err != nil {
		return fmt.Errorf("failed to parse JSON response: %w", err)
	}

	return nil
}

// generateDocumentID generates a unique document ID
func generateDocumentID(teamID, userID, originalID string) string {
	if originalID != "" {
		return originalID
	}
	
	parts := []string{}
	if teamID != "" {
		parts = append(parts, teamID)
	}
	if userID != "" {
		parts = append(parts, userID)
	}
	
	// Add timestamp for uniqueness
	parts = append(parts, fmt.Sprintf("%d", getCurrentTimestamp()))
	
	return strings.Join(parts, "_")
}

// getCurrentTimestamp returns current Unix timestamp in milliseconds
func getCurrentTimestamp() int64 {
	return time.Now().UnixMilli()
}
