package ai

import (
	"context"
	"fmt"

	"github.com/sasha<PERSON>nov/go-openai"
	"github.com/swork-team/ai-service/internal/models"
)

// SummarizationService handles document summarization
type SummarizationService struct {
	openaiClient *openai.Client
	openaiAPIKey string
}

// NewSummarizationService creates a new summarization service
func NewSummarizationService(openaiAPIKey string) *SummarizationService {
	var client *openai.Client
	if openaiAPIKey != "" {
		client = openai.NewClient(openaiAPIKey)
	}

	return &SummarizationService{
		openaiClient: client,
		openaiAPIKey: openaiAPIKey,
	}
}

// SummarizeDocument summarizes the given content
func (s *SummarizationService) SummarizeDocument(ctx context.Context, content, language string) (*models.SummarizationResponseData, error) {
	// Try OpenAI first if configured
	if s.openaiClient != nil {
		result, err := s.summarizeWithOpenAI(ctx, content, language)
		if err == nil {
			return result, nil
		}
		// Log error but continue to fallback
		fmt.Printf("[Summarization] OpenAI error: %v\n", err)
	}

	// Fallback to mock response
	return s.getMockSummary(content, language), nil
}

// summarizeWithOpenAI uses OpenAI to summarize the document
func (s *SummarizationService) summarizeWithOpenAI(ctx context.Context, content, language string) (*models.SummarizationResponseData, error) {
	prompt := fmt.Sprintf("Tóm tắt ngắn gọn tài liệu sau bằng %s:\n%s", language, content)

	req := openai.ChatCompletionRequest{
		Model: openai.GPT4,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleUser,
				Content: prompt,
			},
		},
		MaxTokens:   300,
		Temperature: 0.3,
	}

	resp, err := s.openaiClient.CreateChatCompletion(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response choices returned")
	}

	summary := resp.Choices[0].Message.Content

	return &models.SummarizationResponseData{
		Summary:  summary,
		Language: language,
	}, nil
}

// getMockSummary returns a mock summary when no AI service is available
func (s *SummarizationService) getMockSummary(content, language string) *models.SummarizationResponseData {
	contentPreview := content
	if len(content) > 50 {
		contentPreview = content[:50] + "..."
	}

	return &models.SummarizationResponseData{
		Summary:  fmt.Sprintf("Đây là bản tóm tắt tự động cho tài liệu: %s", contentPreview),
		Language: language,
	}
}

// IsConfigured returns true if the summarization service is properly configured
func (s *SummarizationService) IsConfigured() bool {
	return s.openaiAPIKey != ""
}
