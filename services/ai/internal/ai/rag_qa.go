package ai

import (
	"context"
	"fmt"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
	"github.com/swork-team/ai-service/internal/models"
)

// RAGQAService handles RAG-based question answering
type RAGQAService struct {
	openaiClient *openai.Client
	openaiAPIKey string
}

// NewRAGQAService creates a new RAG Q&A service
func NewRAGQAService(openaiAPIKey string) *RAGQAService {
	var client *openai.Client
	if openaiAPIKey != "" {
		client = openai.NewClient(openaiAPIKey)
	}

	return &RAGQAService{
		openaiClient: client,
		openaiAPIKey: openaiAPIKey,
	}
}

// AnswerQuestion answers a question based on the provided document contents
func (s *RAGQAService) AnswerQuestion(ctx context.Context, question string, documentContents []string, language string) (*models.RAGQAResponseData, error) {
	// Try OpenAI with LangChain-like approach if configured
	if s.openaiClient != nil {
		result, err := s.answerWithOpenAI(ctx, question, documentContents, language)
		if err == nil {
			return result, nil
		}
		// Log error but continue to fallback
		fmt.Printf("[RAG] OpenAI/LangChain error: %v\n", err)
	}

	// Fallback to mock response
	return s.getMockAnswer(question, documentContents), nil
}

// answerWithOpenAI uses OpenAI to answer the question based on document contents
func (s *RAGQAService) answerWithOpenAI(ctx context.Context, question string, documentContents []string, language string) (*models.RAGQAResponseData, error) {
	// Combine document contents into context
	context := ""
	for i, doc := range documentContents {
		context += fmt.Sprintf("Document %d:\n%s\n\n", i+1, doc)
	}

	prompt := fmt.Sprintf(`Based on the following documents, please answer the question in %s.

Documents:
%s

Question: %s

Please provide a comprehensive answer based on the information in the documents. If the answer cannot be found in the documents, please state that clearly.`, language, context, question)

	req := openai.ChatCompletionRequest{
		Model: openai.GPT4,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleUser,
				Content: prompt,
			},
		},
		MaxTokens:   500,
		Temperature: 0.3,
	}

	resp, err := s.openaiClient.CreateChatCompletion(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no response choices returned")
	}

	answer := resp.Choices[0].Message.Content

	// Create sources from document contents
	sources := make([]models.RAGQAResponseSource, len(documentContents))
	for i, doc := range documentContents {
		snippet := doc
		if len(doc) > 50 {
			snippet = doc[:50]
		}
		sources[i] = models.RAGQAResponseSource{
			DocumentID: fmt.Sprintf("doc_%d", i+1),
			Snippet:    snippet,
		}
	}

	return &models.RAGQAResponseData{
		Answer:  answer,
		Sources: sources,
	}, nil
}

// getMockAnswer returns a mock answer when no AI service is available
func (s *RAGQAService) getMockAnswer(question string, documentContents []string) *models.RAGQAResponseData {
	answer := fmt.Sprintf("Đây là câu trả lời cho câu hỏi: '%s' dựa trên tài liệu cung cấp.", question)

	sources := make([]models.RAGQAResponseSource, len(documentContents))
	for i, doc := range documentContents {
		snippet := doc
		if len(doc) > 50 {
			snippet = doc[:50]
		}
		sources[i] = models.RAGQAResponseSource{
			DocumentID: fmt.Sprintf("doc_%d", i+1),
			Snippet:    snippet,
		}
	}

	return &models.RAGQAResponseData{
		Answer:  answer,
		Sources: sources,
	}
}

// IsConfigured returns true if the RAG Q&A service is properly configured
func (s *RAGQAService) IsConfigured() bool {
	return s.openaiAPIKey != ""
}
