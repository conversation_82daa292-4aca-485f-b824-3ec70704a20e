package ai

import (
	"fmt"
	"log"
	"os"

	"github.com/swork-team/ai-service/internal/models"
)

// SemanticSearchService handles semantic search functionality
type SemanticSearchService struct {
	indexPath string
	metaPath  string
	modelName string
	// TODO: Add actual embedding model and FAISS index when implementing
	// model SentenceTransformer
	// index faiss.Index
	// metadata []map[string]interface{}
}

// NewSemanticSearchService creates a new semantic search service
func NewSemanticSearchService(indexPath, metaPath, modelName string) *SemanticSearchService {
	service := &SemanticSearchService{
		indexPath: indexPath,
		metaPath:  metaPath,
		modelName: modelName,
	}

	// TODO: Initialize actual model and index
	// This is where you would load the sentence transformer model and FAISS index
	// For now, we'll use mock data similar to the Python implementation
	if err := service.loadModelAndIndex(); err != nil {
		log.Printf("[SemanticSearch] Could not load model/index: %v", err)
	}

	return service
}

// loadModelAndIndex loads the embedding model and FAISS index
func (s *SemanticSearchService) loadModelAndIndex() error {
	// Check if index files exist
	if _, err := os.Stat(s.indexPath); os.IsNotExist(err) {
		return fmt.Errorf("FAISS index file not found: %s", s.indexPath)
	}
	if _, err := os.Stat(s.metaPath); os.IsNotExist(err) {
		return fmt.Errorf("FAISS metadata file not found: %s", s.metaPath)
	}

	// TODO: Implement actual model and index loading
	// This would involve:
	// 1. Loading a sentence transformer model (possibly via Python binding or Go implementation)
	// 2. Loading the FAISS index
	// 3. Loading the metadata

	return nil
}

// Search performs semantic search on the indexed documents
func (s *SemanticSearchService) Search(query string, teamID *string, topK int) ([]models.SemanticSearchResult, error) {
	// TODO: Implement actual semantic search
	// This would involve:
	// 1. Encoding the query using the sentence transformer model
	// 2. Searching the FAISS index
	// 3. Retrieving metadata for the results
	// 4. Returning formatted results

	// For now, return mock data similar to the Python implementation
	mockResults := []models.SemanticSearchResult{
		{
			DocumentID: "doc_001",
			Name:       "Hop_dong_doi_tac_2023.pdf",
			Score:      0.93,
			Snippet:    "Hợp đồng đã ký với đối tác nước ngoài năm 2023...",
		},
		{
			DocumentID: "doc_002",
			Name:       "Bao_cao_tai_chinh_2023.xlsx",
			Score:      0.89,
			Snippet:    "Báo cáo tài chính năm 2023 cho thấy...",
		},
	}

	// Limit results to topK
	if len(mockResults) > topK {
		mockResults = mockResults[:topK]
	}

	return mockResults, nil
}

// IsConfigured returns true if the semantic search service is properly configured
func (s *SemanticSearchService) IsConfigured() bool {
	// Check if index files exist
	_, indexExists := os.Stat(s.indexPath)
	_, metaExists := os.Stat(s.metaPath)
	return indexExists == nil && metaExists == nil
}
