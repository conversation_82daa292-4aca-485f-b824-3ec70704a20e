package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/generative-ai-go/genai"
	"github.com/swork-team/ai-service/internal/models"
	"google.golang.org/api/option"
)

// GeminiService handles Google Gemini AI functionality
type GeminiService struct {
	client *genai.Client
	model  *genai.GenerativeModel
	apiKey string
}

// NewGeminiService creates a new Gemini service
func NewGeminiService(ctx context.Context, apiKey string) (*GeminiService, error) {
	if apiKey == "" {
		return &GeminiService{apiKey: apiKey}, nil
	}

	client, err := genai.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	model := client.GenerativeModel("gemini-1.5-flash")

	return &GeminiService{
		client: client,
		model:  model,
		apiKey: apiKey,
	}, nil
}

// GenerateEmail generates an email using Gemini AI
func (g *GeminiService) GenerateEmail(ctx context.Context, req *models.EmailGenerationRequest) (*models.EmailGenerationResponseData, error) {
	if !g.IsConfigured() {
		return nil, fmt.Errorf("Gemini service not configured (missing API key)")
	}

	// Set default language if not provided
	language := req.Language
	if language == "" {
		language = "Vietnamese"
	}

	// Build detailed prompt requesting JSON output
	prompt := fmt.Sprintf(`
Hãy viết một email chuyên nghiệp dựa trên các chi tiết sau.
ĐẦU RA BẮT BUỘC phải là một đối tượng JSON hợp lệ chỉ chứa hai khóa: "subject" và "body".
Không thêm bất kỳ văn bản, giải thích, hay ký tự markdown nào khác ngoài đối tượng JSON.

Chi tiết yêu cầu:
- Ngôn ngữ: %s
- Tên người nhận: %s
- Mục đích email: %s
- Giọng văn (tone): %s
- Bối cảnh bổ sung: %s
- Tên người gửi: %s

Ví dụ về JSON đầu ra:
{
  "subject": "Chủ đề email mẫu",
  "body": "Kính gửi %s,\n\nĐây là nội dung của email.\n\nTrân trọng,\n%s"
}
`, language, req.RecipientName, req.Purpose, req.Tone, req.Context, req.SenderName, req.RecipientName, req.SenderName)

	resp, err := g.model.GenerateContent(ctx, genai.Text(prompt))
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("no content generated")
	}

	// Extract text from response
	var responseText string
	for _, part := range resp.Candidates[0].Content.Parts {
		if textPart, ok := part.(genai.Text); ok {
			responseText += string(textPart)
		}
	}

	// Clean up the response text to ensure it's valid JSON
	cleanedText := strings.TrimSpace(responseText)
	cleanedText = strings.ReplaceAll(cleanedText, "```json", "")
	cleanedText = strings.ReplaceAll(cleanedText, "```", "")
	cleanedText = strings.TrimSpace(cleanedText)

	// Parse JSON response
	var emailData struct {
		Subject string `json:"subject"`
		Body    string `json:"body"`
	}

	if err := json.Unmarshal([]byte(cleanedText), &emailData); err != nil {
		return nil, fmt.Errorf("failed to parse AI response as JSON: %w", err)
	}

	if emailData.Subject == "" || emailData.Body == "" {
		return nil, fmt.Errorf("AI response missing 'subject' or 'body' field")
	}

	return &models.EmailGenerationResponseData{
		Subject: emailData.Subject,
		Body:    emailData.Body,
	}, nil
}

// Close closes the Gemini client
func (g *GeminiService) Close() error {
	if g.client != nil {
		return g.client.Close()
	}
	return nil
}

// IsConfigured returns true if the Gemini service is properly configured
func (g *GeminiService) IsConfigured() bool {
	return g.apiKey != "" && g.client != nil
}
