package config

import (
	"errors"
	"log"
	"os"
	"strconv"
)

// Config holds all configuration for the AI service
type Config struct {
	// Service Configuration
	Port        string
	Environment string

	// AI Configuration
	AI           AIConfig
	Typesense    TypesenseConfig
	Features     FeatureConfig
}

// AIConfig holds AI service specific configuration
type AIConfig struct {
	GeminiAPIKey string
	OpenAIAPIKey string
	EmbedModel   string
}

// TypesenseConfig holds Typesense configuration
type TypesenseConfig struct {
	URL    string
	APIKey string
	SSL    bool
}

// FeatureConfig holds feature flags and paths
type FeatureConfig struct {
	FAISSIndexPath   string
	FAISSMetaPath    string
	MockDriveService bool
}

// LoadConfig loads configuration from environment variables
func LoadConfig() *Config {
	cfg := &Config{
		Port:        getEnvWithDefault("PORT", "8080"),
		Environment: getEnvWithDefault("ENVIRONMENT", "development"),
		AI: AIConfig{
			GeminiAPIKey: os.Getenv("GEMINI_API_KEY"),
			OpenAIAPIKey: os.Getenv("OPENAI_API_KEY"),
			EmbedModel:   getEnvWithDefault("EMBED_MODEL", "sentence-transformers/multi-qa-mpnet-base-dot-v1"),
		},
		Typesense: TypesenseConfig{
			URL:    getEnvWithDefault("TYPESENSE_URL", "http://localhost:8108"),
			APIKey: os.Getenv("TYPESENSE_API_KEY"),
			SSL:    getBoolEnv("TYPESENSE_SSL", false),
		},
		Features: FeatureConfig{
			FAISSIndexPath:   getEnvWithDefault("FAISS_INDEX_PATH", "faiss_docs.index"),
			FAISSMetaPath:    getEnvWithDefault("FAISS_META_PATH", "faiss_docs_meta.npy"),
			MockDriveService: getBoolEnv("MOCK_DRIVE_SERVICE", false),
		},
	}

	// Validate required configuration
	if err := validateConfig(cfg); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}

	return cfg
}

// getEnvWithDefault returns the value of an environment variable or a default value
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getBoolEnv returns the boolean value of an environment variable or a default value
func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// validateConfig checks that all required configuration is present
func validateConfig(cfg *Config) error {
	if cfg.AI.GeminiAPIKey == "" && cfg.AI.OpenAIAPIKey == "" {
		return errors.New("at least one AI API key (GEMINI_API_KEY or OPENAI_API_KEY) is required")
	}
	return nil
}

// IsGeminiConfigured returns true if Gemini API key is configured
func (c *Config) IsGeminiConfigured() bool {
	return c.AI.GeminiAPIKey != ""
}

// IsOpenAIConfigured returns true if OpenAI API key is configured
func (c *Config) IsOpenAIConfigured() bool {
	return c.AI.OpenAIAPIKey != ""
}

// IsTypesenseConfigured returns true if Typesense is configured
func (c *Config) IsTypesenseConfigured() bool {
	return c.Typesense.URL != "" && c.Typesense.APIKey != ""
}

// IsElasticsearchConfigured returns true if Typesense is configured (for backward compatibility)
func (c *Config) IsElasticsearchConfigured() bool {
	return c.IsTypesenseConfigured()
}