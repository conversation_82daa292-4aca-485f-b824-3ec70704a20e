package handlers

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/ai-service/internal/models"
	"github.com/swork-team/ai-service/internal/services"
	"github.com/swork-team/ai-service/internal/utils"
)

// <PERSON><PERSON> handles HTTP requests for the AI service
type Handler struct {
	aiService *services.AIService
}

// NewHandler creates a new handler
func NewHandler(aiService *services.AIService) *Handler {
	return &Handler{
		aiService: aiService,
	}
}

// HealthCheck handles health check requests
func (h *Handler) HealthCheck(c *gin.Context) {
	response := models.HealthResponse{
		Status:  "healthy",
		Service: "ai-service",
	}
	utils.SuccessResponse(c, http.StatusOK, "Service is healthy", response)
}

// validateUserID checks for required X-User-ID header
func (h *Handler) validateUserID(c *gin.Context) (string, bool) {
	userID := c.GetHeader("X-User-ID")
	if userID == "" {
		utils.ErrorResponse(c, http.StatusUnauthorized, "X-User-ID header is required", nil)
		return "", false
	}
	return userID, true
}

// RAGQuery handles RAG query requests
func (h *Handler) RAGQuery(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.RAGQueryRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	result, err := h.aiService.RAGQuery(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to process RAG query", err,
			"rag_query", utils.F("user_id", userID), utils.F("question", req.Question))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "RAG query processed successfully", result,
		"rag_query", utils.F("user_id", userID), utils.F("question", req.Question))
}

// GenerateEmail handles email generation requests
func (h *Handler) GenerateEmail(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.EmailGenerationRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	result, err := h.aiService.GenerateEmail(c.Request.Context(), &req)
	if err != nil {
		if strings.Contains(err.Error(), "not configured") || strings.Contains(err.Error(), "missing API key") {
			utils.ErrorResponseWithLog(c, http.StatusServiceUnavailable, "AI service not configured", err,
				"generate_email", utils.F("user_id", userID))
			return
		}
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to generate email", err,
			"generate_email", utils.F("user_id", userID), utils.F("purpose", req.Purpose))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Email generated successfully", result,
		"generate_email", utils.F("user_id", userID), utils.F("purpose", req.Purpose))
}

// SemanticSearch handles semantic search requests
func (h *Handler) SemanticSearch(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.SemanticSearchRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	result, err := h.aiService.SemanticSearch(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to perform semantic search", err,
			"semantic_search", utils.F("user_id", userID), utils.F("query", req.Query))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Semantic search completed successfully", result,
		"semantic_search", utils.F("user_id", userID), utils.F("query", req.Query))
}

// SummarizeDocument handles document summarization requests
func (h *Handler) SummarizeDocument(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.SummarizationRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	result, err := h.aiService.SummarizeDocument(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to summarize document", err,
			"summarize_document", utils.F("user_id", userID), utils.F("content_length", len(req.Content)))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Document summarized successfully", result,
		"summarize_document", utils.F("user_id", userID), utils.F("content_length", len(req.Content)))
}

// RAGQA handles RAG Q&A requests
func (h *Handler) RAGQA(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.RAGQARequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	result, err := h.aiService.RAGQA(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to process RAG Q&A", err,
			"rag_qa", utils.F("user_id", userID), utils.F("question", req.Question))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "RAG Q&A processed successfully", result,
		"rag_qa", utils.F("user_id", userID), utils.F("question", req.Question))
}

// FastSearch handles fast search requests using Elasticsearch
func (h *Handler) FastSearch(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.FastSearchRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	result, err := h.aiService.FastSearch(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to perform fast search", err,
			"fast_search", utils.F("user_id", userID), utils.F("query", req.Query))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Fast search completed successfully", result,
		"fast_search", utils.F("user_id", userID), utils.F("query", req.Query))
}

// AutoComplete handles auto-completion requests
func (h *Handler) AutoComplete(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.AutoCompleteRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	result, err := h.aiService.AutoComplete(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to get auto-complete suggestions", err,
			"auto_complete", utils.F("user_id", userID), utils.F("query", req.Query))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Auto-complete suggestions retrieved successfully", result,
		"auto_complete", utils.F("user_id", userID), utils.F("query", req.Query))
}

// IndexDocument handles document indexing requests
func (h *Handler) IndexDocument(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.IndexDocumentRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Set user ID if not provided in document
	if req.Document.UserID == "" {
		req.Document.UserID = userID
	}

	err := h.aiService.IndexDocument(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to index document", err,
			"index_document", utils.F("user_id", userID), utils.F("document_id", req.Document.ID))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Document indexed successfully", nil,
		"index_document", utils.F("user_id", userID), utils.F("document_id", req.Document.ID))
}

// BulkIndexDocuments handles bulk document indexing requests
func (h *Handler) BulkIndexDocuments(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	var req models.BulkIndexRequest
	if !utils.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Set user ID for documents that don't have it
	for i := range req.Documents {
		if req.Documents[i].UserID == "" {
			req.Documents[i].UserID = userID
		}
	}

	err := h.aiService.BulkIndexDocuments(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to bulk index documents", err,
			"bulk_index_documents", utils.F("user_id", userID), utils.F("document_count", len(req.Documents)))
		return
	}

	message := "Successfully indexed documents"
	utils.SuccessResponseWithLog(c, http.StatusOK, message, nil,
		"bulk_index_documents", utils.F("user_id", userID), utils.F("document_count", len(req.Documents)))
}

// GetSearchStats handles search statistics requests
func (h *Handler) GetSearchStats(c *gin.Context) {
	userID, ok := h.validateUserID(c)
	if !ok {
		return
	}

	stats := h.aiService.GetSearchStats(c.Request.Context())

	utils.SuccessResponseWithLog(c, http.StatusOK, "Search statistics retrieved successfully", stats,
		"get_search_stats", utils.F("user_id", userID))
}