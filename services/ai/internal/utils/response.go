package utils

import (
	"github.com/gin-gonic/gin"
)

// APIResponse represents a generic API response structure
type APIResponse[T any] struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    *T     `json:"data,omitempty"`
}

// SuccessResponse sends a successful response
func SuccessResponse[T any](c *gin.Context, statusCode int, message string, data T) {
	response := APIResponse[T]{
		Success: true,
		Message: message,
		Data:    &data,
	}
	c.JSO<PERSON>(statusCode, response)
}

// SuccessResponseWithLog sends a successful response with logging
func SuccessResponseWithLog[T any](c *gin.Context, statusCode int, message string, data T, operation string, fields ...interface{}) {
	// For now, just call SuccessResponse - logging can be added later
	SuccessResponse(c, statusCode, message, data)
}

// ErrorResponse sends an error response
func ErrorResponse(c *gin.Context, statusCode int, message string, err error) {
	response := APIResponse[any]{
		Success: false,
		Message: message,
	}
	c.<PERSON>(statusCode, response)
}

// ErrorResponseWithLog sends an error response with logging
func ErrorResponseWithLog(c *gin.Context, statusCode int, message string, err error, operation string, fields ...interface{}) {
	// For now, just call ErrorResponse - logging can be added later
	ErrorResponse(c, statusCode, message, err)
}
