package utils

import (
	"log"
)

// Simple logger interface for now
type ServiceLogger interface {
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
}

type simpleLogger struct{}

func (l *simpleLogger) Info(msg string, fields ...interface{}) {
	log.Printf("[INFO] %s %v", msg, fields)
}

func (l *simpleLogger) Warn(msg string, fields ...interface{}) {
	log.Printf("[WARN] %s %v", msg, fields)
}

func (l *simpleLogger) Error(msg string, fields ...interface{}) {
	log.Printf("[ERROR] %s %v", msg, fields)
}

// NewServiceLogger creates a simple logger
func NewServiceLogger() ServiceLogger {
	return &simpleLogger{}
}

// F creates a field for logging
func F(key string, value interface{}) interface{} {
	return map[string]interface{}{key: value}
}
