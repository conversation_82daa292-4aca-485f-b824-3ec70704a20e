package models

// HealthResponse represents health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
}

// === Core AI Models ===

// RAGQueryRequest represents a request for RAG query
type RAGQueryRequest struct {
	Question          string   `json:"question" binding:"required"`
	TeamID            *string  `json:"team_id,omitempty"`
	ConversationID    *string  `json:"conversation_id,omitempty"`
	SourceDocumentIDs []string `json:"source_document_ids,omitempty"`
}

// RAGQueryResponseData represents the data part of RAG query response
type RAGQueryResponseData struct {
	Answer  string       `json:"answer"`
	Sources []SourceInfo `json:"sources"`
}

// EmailGenerationRequest represents a request for email generation
type EmailGenerationRequest struct {
	RecipientName string `json:"recipient_name" binding:"required"`
	Purpose       string `json:"purpose" binding:"required"`
	Tone          string `json:"tone" binding:"required"`
	Context       string `json:"context" binding:"required"`
	SenderName    string `json:"sender_name" binding:"required"`
	Language      string `json:"language,omitempty"`
}

// EmailGenerationResponseData represents the data part of email generation response
type EmailGenerationResponseData struct {
	Subject string `json:"subject"`
	Body    string `json:"body"`
}

// SemanticSearchRequest represents a request for semantic search
type SemanticSearchRequest struct {
	Query  string  `json:"query" binding:"required"`
	TeamID *string `json:"team_id,omitempty"`
	TopK   int     `json:"top_k,omitempty"`
}

// SemanticSearchResponseData represents the data part of semantic search response
type SemanticSearchResponseData struct {
	Results []SearchResult `json:"results"`
}

// SummarizationRequest represents a request for document summarization
type SummarizationRequest struct {
	Content  string `json:"content" binding:"required"`
	Language string `json:"language,omitempty"`
}

// SummarizationResponseData represents the data part of summarization response
type SummarizationResponseData struct {
	Summary  string `json:"summary"`
	Language string `json:"language"`
}

// RAGQARequest represents a request for RAG Q&A
type RAGQARequest struct {
	Question         string   `json:"question" binding:"required"`
	DocumentContents []string `json:"document_contents" binding:"required"`
	Language         string   `json:"language,omitempty"`
}

// RAGQAResponseData represents the data part of RAG Q&A response
type RAGQAResponseData struct {
	Answer  string       `json:"answer"`
	Sources []SourceInfo `json:"sources"`
}

// === Common Models ===

// SourceInfo represents a source document reference
type SourceInfo struct {
	DocumentID string  `json:"document_id"`
	Name       string  `json:"name,omitempty"`
	Score      float64 `json:"score,omitempty"`
	Snippet    string  `json:"snippet,omitempty"`
}

// SearchResult represents a single search result
type SearchResult struct {
	DocumentID string  `json:"document_id"`
	Name       string  `json:"name"`
	Score      float64 `json:"score"`
	Snippet    string  `json:"snippet"`
}

// SemanticSearchResult represents a single semantic search result (alias for SearchResult)
type SemanticSearchResult = SearchResult

// RAGQAResponseSource represents a source in RAG Q&A response (alias for SourceInfo)
type RAGQAResponseSource = SourceInfo

// === Search Models ===

// Document represents a document in the search index
type Document struct {
	ID           string                 `json:"id"`
	Title        string                 `json:"title"`
	Content      string                 `json:"content"`
	Summary      string                 `json:"summary,omitempty"`
	DocumentType string                 `json:"document_type"`
	TeamID       string                 `json:"team_id,omitempty"`
	UserID       string                 `json:"user_id"`
	Tags         []string               `json:"tags,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt    string                 `json:"created_at"`
	UpdatedAt    string                 `json:"updated_at"`
	Language     string                 `json:"language,omitempty"`
	FileType     string                 `json:"file_type,omitempty"`
	FileSize     int64                  `json:"file_size,omitempty"`
	URL          string                 `json:"url,omitempty"`
}

// FastSearchRequest represents a fast search request
type FastSearchRequest struct {
	Query         string            `json:"query" binding:"required"`
	Filters       map[string]string `json:"filters,omitempty"`
	TeamID        *string           `json:"team_id,omitempty"`
	DocumentTypes []string          `json:"document_types,omitempty"`
	Tags          []string          `json:"tags,omitempty"`
	From          int               `json:"from,omitempty"`
	Size          int               `json:"size,omitempty"`
	SortBy        string            `json:"sort_by,omitempty"`
	SortOrder     string            `json:"sort_order,omitempty"`
	Highlight     bool              `json:"highlight,omitempty"`
	Facets        []string          `json:"facets,omitempty"`
}

// SearchHit represents a single search result with score and highlights
type SearchHit struct {
	Document
	Score      float64                 `json:"score"`
	Highlights map[string][]string     `json:"highlights,omitempty"`
}

// SearchFacet represents aggregation results
type SearchFacet struct {
	Field   string                    `json:"field"`
	Buckets []SearchFacetBucket       `json:"buckets"`
}

// SearchFacetBucket represents a facet bucket
type SearchFacetBucket struct {
	Key   string `json:"key"`
	Count int64  `json:"count"`
}

// FastSearchResponseData represents the data part of fast search response
type FastSearchResponseData struct {
	Hits       []SearchHit    `json:"hits"`
	Total      int64          `json:"total"`
	MaxScore   float64        `json:"max_score"`
	Took       int64          `json:"took"`
	Facets     []SearchFacet  `json:"facets,omitempty"`
	From       int            `json:"from"`
	Size       int            `json:"size"`
}

// AutoCompleteRequest represents an auto-complete request
type AutoCompleteRequest struct {
	Query  string  `json:"query" binding:"required"`
	TeamID *string `json:"team_id,omitempty"`
	Field  string  `json:"field,omitempty"`
	Size   int     `json:"size,omitempty"`
}

// AutoCompleteSuggestion represents a single suggestion
type AutoCompleteSuggestion struct {
	Text  string  `json:"text"`
	Score float64 `json:"score"`
}

// AutoCompleteResponseData represents auto-complete response data
type AutoCompleteResponseData struct {
	Suggestions []AutoCompleteSuggestion `json:"suggestions"`
}

// IndexDocumentRequest represents a request to index a document
type IndexDocumentRequest struct {
	Document Document `json:"document" binding:"required"`
	Index    string   `json:"index,omitempty"`
}

// BulkIndexRequest represents a bulk indexing request
type BulkIndexRequest struct {
	Documents []Document `json:"documents" binding:"required"`
	Index     string     `json:"index,omitempty"`
}

// DeleteDocumentRequest represents a request to delete a document
type DeleteDocumentRequest struct {
	DocumentID string `json:"document_id" binding:"required"`
	Index      string `json:"index,omitempty"`
}

// === Advanced Search Models (for compatibility) ===

// AdvancedSearchRequest represents a complex search request with boolean queries
type AdvancedSearchRequest struct {
	Query          string                 `json:"query"`
	MustQueries    []string               `json:"must_queries,omitempty"`
	ShouldQueries  []string               `json:"should_queries,omitempty"`
	MustNotQueries []string               `json:"must_not_queries,omitempty"`
	Filters        map[string]interface{} `json:"filters,omitempty"`
	RangeFilters   map[string]RangeFilter `json:"range_filters,omitempty"`
	TeamID         *string                `json:"team_id,omitempty"`
	From           int                    `json:"from"`
	Size           int                    `json:"size"`
	SortBy         string                 `json:"sort_by,omitempty"`
	SortOrder      string                 `json:"sort_order,omitempty"`
	Highlight      bool                   `json:"highlight"`
	FuzzyMatch     bool                   `json:"fuzzy_match"`
	MinScore       float64                `json:"min_score,omitempty"`
}

// RangeFilter represents a range filter for numeric or date fields
type RangeFilter struct {
	Gte interface{} `json:"gte,omitempty"` // Greater than or equal
	Lte interface{} `json:"lte,omitempty"` // Less than or equal
	Gt  interface{} `json:"gt,omitempty"`  // Greater than
	Lt  interface{} `json:"lt,omitempty"`  // Less than
}

// FacetedSearchRequest represents a search request with faceted navigation
type FacetedSearchRequest struct {
	FastSearchRequest
	FacetFields   []string `json:"facet_fields"`
	FacetSize     int      `json:"facet_size"`
	FacetMinCount int      `json:"facet_min_count"`
}

// FacetedSearchResponseData represents faceted search response
type FacetedSearchResponseData struct {
	FastSearchResponseData
	Facets []SearchFacet `json:"facets"`
}

// SearchFacetValue represents a facet value (alias for SearchFacetBucket)
type SearchFacetValue = SearchFacetBucket

// SuggestSearchRequest represents a search suggestion request
type SuggestSearchRequest struct {
	Query        string  `json:"query" binding:"required"`
	TeamID       *string `json:"team_id,omitempty"`
	Size         int     `json:"size"`
	Confidence   float64 `json:"confidence,omitempty"`
	MaxEdits     int     `json:"max_edits,omitempty"`
	PrefixLength int     `json:"prefix_length,omitempty"`
}

// SearchDocument is an alias for Document (for backward compatibility)
type SearchDocument = Document

// SuggestSearchResponseData represents search suggestion response
type SuggestSearchResponseData struct {
	Query       string                   `json:"query"`
	Suggestions []AutoCompleteSuggestion `json:"suggestions"`
}

// SearchAnalyticsRequest represents a request for search analytics
type SearchAnalyticsRequest struct {
	TeamID    *string `json:"team_id,omitempty"`
	StartDate string  `json:"start_date,omitempty"`
	EndDate   string  `json:"end_date,omitempty"`
	Metrics   []string `json:"metrics,omitempty"`
}

// SearchAnalyticsResponseData represents search analytics response
type SearchAnalyticsResponseData struct {
	TopQueries   []QueryAnalytics    `json:"top_queries"`
	TopDocuments []DocumentAnalytics `json:"top_documents"`
}

// QueryAnalytics represents analytics for a search query
type QueryAnalytics struct {
	Query        string  `json:"query"`
	Count        int64   `json:"count"`
	AvgScore     float64 `json:"avg_score"`
	ClickThrough float64 `json:"click_through_rate"`
}

// DocumentAnalytics represents analytics for a document
type DocumentAnalytics struct {
	DocumentID string  `json:"document_id"`
	Title      string  `json:"title"`
	Views      int64   `json:"views"`
	AvgScore   float64 `json:"avg_score"`
}

// VolumeAnalytics represents search volume over time
type VolumeAnalytics struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// PerformanceMetrics represents search performance metrics
type PerformanceMetrics struct {
	AvgResponseTime float64 `json:"avg_response_time_ms"`
	TotalSearches   int64   `json:"total_searches"`
	CacheHitRate    float64 `json:"cache_hit_rate"`
	ErrorRate       float64 `json:"error_rate"`
}
