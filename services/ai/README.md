# Swork AI Service (Go)

A comprehensive AI service built with Go that provides various AI-powered features including RAG (Retrieval-Augmented Generation), semantic search, document summarization, email generation, and **high-performance Elasticsearch-based search**.

## Features

### Core AI Features
- **RAG Query**: Retrieval-Augmented Generation for question answering
- **Email Generation**: AI-powered email generation using Google Gemini
- **Semantic Search**: Document search using embeddings and FAISS
- **Document Summarization**: Automatic document summarization using OpenAI
- **RAG Q&A**: Question answering based on provided document contents

### Fast Search System (NEW)
- **Real-time Search**: High-performance search across large volumes of unstructured data
- **Elasticsearch Integration**: Full-text search with inverted indexing for optimal performance
- **Advanced Query Features**: Boolean queries, range filters, fuzzy matching, and faceted search
- **Auto-completion**: Intelligent search suggestions and spell correction
- **Analytics**: Search analytics and performance insights
- **Scalability**: Designed for horizontal scaling and distributed search
- **Fallback Mechanisms**: Graceful degradation when Elasticsearch is unavailable

## API Endpoints

### Health Check
- `GET /health` - Service health status

### AI Operations
- `POST /ai/rag/query` - Process RAG queries
- `POST /ai/generate-email` - Generate emails using AI
- `POST /ai/semantic-search` - Perform semantic document search
- `POST /ai/summarize-document` - Summarize documents
- `POST /ai/rag/qa` - Question-answering with document context

### Fast Search Operations (NEW)
- `POST /ai/search` - Fast search across indexed documents
- `POST /ai/autocomplete` - Auto-completion suggestions
- `POST /ai/index` - Index a single document
- `POST /ai/bulk-index` - Bulk index multiple documents
- `GET /ai/search/stats` - Search service statistics

## Configuration

Copy `.env.example` to `.env` and configure the following environment variables:

### Required for Email Generation
- `GEMINI_API_KEY`: Google Gemini API key

### Required for Summarization and RAG Q&A
- `OPENAI_API_KEY`: OpenAI API key

### Optional Configuration
- `EMBED_MODEL`: Embedding model name (default: sentence-transformers/multi-qa-mpnet-base-dot-v1)
- `FAISS_INDEX_PATH`: Path to FAISS index file (default: faiss_docs.index)
- `FAISS_META_PATH`: Path to FAISS metadata file (default: faiss_docs_meta.npy)
- `PORT`: Service port (default: 8080)
- `GIN_MODE`: Gin mode (debug/release)
- `ENVIRONMENT`: Environment (development/production)
- `MOCK_DRIVE_SERVICE`: Enable mock mode for development (true/false)

### Elasticsearch Configuration (NEW)
- `ELASTICSEARCH_URL`: Elasticsearch cluster URL (default: http://localhost:9200)
- `ELASTICSEARCH_USERNAME`: Elasticsearch username (optional)
- `ELASTICSEARCH_PASSWORD`: Elasticsearch password (optional)
- `ELASTICSEARCH_SSL`: Enable SSL for Elasticsearch (true/false)

## Development

### Prerequisites
- Go 1.21 or later
- Docker (optional)

### Running Locally

1. Install dependencies:
```bash
go mod download
```

2. Copy and configure environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. Run the service:
```bash
go run cmd/main.go
```

The service will start on port 8080 (or the port specified in the PORT environment variable).

### Running with Docker

1. Build the Docker image:
```bash
docker build -t swork-ai-service .
```

2. Run the container:
```bash
docker run -p 8080:8080 --env-file .env swork-ai-service
```

## Testing

Run tests with:
```bash
go test ./...
```

## Project Structure

```
services/ai-go/
├── cmd/
│   └── main.go              # Application entry point
├── internal/
│   ├── ai/                  # AI service implementations
│   │   ├── gemini.go        # Google Gemini integration
│   │   ├── rag_qa.go        # RAG Q&A functionality
│   │   ├── semantic_search.go # Semantic search functionality
│   │   └── summarization.go # Document summarization
│   ├── config/              # Configuration management
│   │   └── config.go
│   ├── handlers/            # HTTP handlers
│   │   └── handlers.go
│   ├── models/              # Data models
│   │   └── models.go
│   └── services/            # Business logic services
│       └── ai_service.go
├── .env.example             # Environment variables example
├── Dockerfile               # Docker configuration
├── go.mod                   # Go module definition
├── go.sum                   # Go module checksums
└── README.md               # This file
```

## Migration from Python

This Go implementation maintains the same API interface as the original Python FastAPI service, ensuring compatibility with existing clients. Key differences:

1. **Framework**: Migrated from FastAPI to Gin (Go HTTP framework)
2. **AI Libraries**: 
   - Google Gemini: Using `google/generative-ai-go`
   - OpenAI: Using `sashabaranov/go-openai`
   - Semantic Search: Placeholder for Go-based embedding solution
3. **Configuration**: Using `godotenv` for environment variable management
4. **Error Handling**: Go-style error handling with proper error propagation
5. **Concurrency**: Leveraging Go's built-in concurrency features

## Notes

- The semantic search functionality currently returns mock data. For production use, you'll need to implement actual embedding generation and FAISS integration.
- All AI services gracefully degrade to mock responses when API keys are not configured.
- The service follows the same logging and error handling patterns as other services in the Swork platform.
