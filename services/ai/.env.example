# AI API Keys
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Embedding and Search Configuration
EMBED_MODEL=sentence-transformers/multi-qa-mpnet-base-dot-v1
FAISS_INDEX_PATH=faiss_docs.index
FAISS_META_PATH=faiss_docs_meta.npy

# Service Configuration
PORT=8080
GIN_MODE=debug
ENVIRONMENT=development

# Mock Configuration (for development)
MOCK_DRIVE_SERVICE=true

# Typesense Configuration
TYPESENSE_URL=http://localhost:8108
TYPESENSE_API_KEY=your_typesense_api_key_here
TYPESENSE_SSL=false