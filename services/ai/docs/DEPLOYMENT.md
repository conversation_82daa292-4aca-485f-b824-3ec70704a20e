# Fast Search Service Deployment Guide

This guide covers deploying the Swork AI Service with Elasticsearch integration for production use.

## Prerequisites

### System Requirements
- Go 1.21 or later
- Elasticsearch 8.x cluster
- Docker (optional)
- Minimum 2GB RAM
- Minimum 1GB disk space

### Elasticsearch Setup

#### Option 1: Elasticsearch Cloud (Recommended)
1. Sign up for Elasticsearch Service on Elastic Cloud
2. Create a deployment with appropriate sizing
3. Note the endpoint URL, username, and password
4. Configure security settings and API keys

#### Option 2: Self-Hosted Elasticsearch
```bash
# Using Docker
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  docker.elastic.co/elasticsearch/elasticsearch:8.11.0

# Verify installation
curl http://localhost:9200
```

#### Option 3: Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
spec:
  replicas: 3
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
        ports:
        - containerPort: 9200
        - containerPort: 9300
        env:
        - name: cluster.name
          value: "swork-search"
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: discovery.seed_hosts
          value: "elasticsearch-0.elasticsearch,elasticsearch-1.elasticsearch,elasticsearch-2.elasticsearch"
        - name: cluster.initial_master_nodes
          value: "elasticsearch-0,elasticsearch-1,elasticsearch-2"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
```

## Configuration

### Environment Variables

Create a `.env` file with the following configuration:

```bash
# AI API Keys
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Embedding and Search Configuration
EMBED_MODEL=sentence-transformers/multi-qa-mpnet-base-dot-v1
FAISS_INDEX_PATH=faiss_docs.index
FAISS_META_PATH=faiss_docs_meta.npy

# Service Configuration
PORT=8080
GIN_MODE=release
ENVIRONMENT=production

# Elasticsearch Configuration
ELASTICSEARCH_URL=https://your-cluster.es.region.aws.found.io:9243
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=your_password_here
ELASTICSEARCH_SSL=true

# Mock Configuration (disable in production)
MOCK_DRIVE_SERVICE=false
```

### Production Configuration Best Practices

1. **Security**:
   - Use HTTPS for Elasticsearch connections
   - Store API keys in secure secret management systems
   - Enable authentication and authorization
   - Use network security groups/firewalls

2. **Performance**:
   - Set `GIN_MODE=release` for production
   - Configure appropriate Elasticsearch cluster sizing
   - Enable connection pooling
   - Set up monitoring and alerting

3. **Reliability**:
   - Configure multiple Elasticsearch nodes
   - Set up backup and disaster recovery
   - Implement health checks and circuit breakers
   - Use load balancers for high availability

## Deployment Options

### Option 1: Direct Deployment

```bash
# Build the application
go build -o ai-service ./cmd/main.go

# Run with environment variables
export ELASTICSEARCH_URL="https://your-cluster.es.region.aws.found.io:9243"
export ELASTICSEARCH_USERNAME="elastic"
export ELASTICSEARCH_PASSWORD="your_password"
export GEMINI_API_KEY="your_gemini_key"
export OPENAI_API_KEY="your_openai_key"

./ai-service
```

### Option 2: Docker Deployment

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o ai-service ./cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/ai-service .

EXPOSE 8080
CMD ["./ai-service"]
```

```bash
# Build and run
docker build -t swork-ai-service .
docker run -p 8080:8080 --env-file .env swork-ai-service
```

### Option 3: Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: swork-ai-service
  labels:
    app: swork-ai-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: swork-ai-service
  template:
    metadata:
      labels:
        app: swork-ai-service
    spec:
      containers:
      - name: ai-service
        image: swork-ai-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: ELASTICSEARCH_URL
          valueFrom:
            secretKeyRef:
              name: ai-service-secrets
              key: elasticsearch-url
        - name: ELASTICSEARCH_USERNAME
          valueFrom:
            secretKeyRef:
              name: ai-service-secrets
              key: elasticsearch-username
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-service-secrets
              key: elasticsearch-password
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-service-secrets
              key: gemini-api-key
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-service-secrets
              key: openai-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: swork-ai-service
spec:
  selector:
    app: swork-ai-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

## Monitoring and Observability

### Health Checks

The service provides a health check endpoint:
```bash
curl http://localhost:8080/health
```

### Metrics and Monitoring

1. **Application Metrics**:
   - Request latency and throughput
   - Error rates
   - Cache hit rates
   - Search performance metrics

2. **Elasticsearch Metrics**:
   - Cluster health
   - Index size and document count
   - Query performance
   - Resource utilization

3. **Recommended Monitoring Stack**:
   - Prometheus for metrics collection
   - Grafana for visualization
   - Elasticsearch monitoring tools
   - Log aggregation (ELK stack)

### Logging

Configure structured logging for production:
```bash
export GIN_MODE=release
export LOG_LEVEL=info
export LOG_FORMAT=json
```

## Scaling Considerations

### Horizontal Scaling
- Deploy multiple service instances behind a load balancer
- Use stateless design for easy scaling
- Configure session affinity if needed

### Elasticsearch Scaling
- Add more nodes to the Elasticsearch cluster
- Use index sharding for large datasets
- Implement index lifecycle management
- Consider hot/warm/cold architecture

### Performance Optimization
- Enable response caching
- Use connection pooling
- Optimize Elasticsearch mappings
- Implement query optimization

## Security

### Network Security
- Use VPC/private networks
- Configure security groups
- Enable TLS/SSL encryption
- Implement API rate limiting

### Authentication & Authorization
- Integrate with existing auth systems
- Use API keys for service-to-service communication
- Implement role-based access control
- Audit logging for security events

### Data Protection
- Encrypt data at rest and in transit
- Implement data retention policies
- Regular security updates
- Vulnerability scanning

## Backup and Disaster Recovery

### Elasticsearch Backups
```bash
# Create snapshot repository
curl -X PUT "localhost:9200/_snapshot/backup_repository" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/backup/elasticsearch"
  }
}'

# Create snapshot
curl -X PUT "localhost:9200/_snapshot/backup_repository/snapshot_1"
```

### Application Backups
- Configuration files
- Custom mappings and settings
- Application logs
- Monitoring data

## Troubleshooting

### Common Issues

1. **Elasticsearch Connection Issues**:
   - Check network connectivity
   - Verify credentials
   - Check SSL/TLS configuration
   - Review firewall rules

2. **Performance Issues**:
   - Monitor resource utilization
   - Check Elasticsearch cluster health
   - Review query performance
   - Analyze cache hit rates

3. **Search Quality Issues**:
   - Review index mappings
   - Check analyzer configuration
   - Validate document structure
   - Test query relevance

### Debug Commands

```bash
# Check service health
curl http://localhost:8080/health

# Check search statistics
curl -H "X-User-ID: admin" http://localhost:8080/ai/search/stats

# Test Elasticsearch connection
curl http://elasticsearch:9200/_cluster/health

# View service logs
docker logs swork-ai-service
```
