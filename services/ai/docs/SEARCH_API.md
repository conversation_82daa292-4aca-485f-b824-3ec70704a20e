# Fast Search API Documentation

This document describes the Fast Search API endpoints provided by the Swork AI Service. The Fast Search system uses Elasticsearch to provide high-performance search capabilities with advanced features like faceted search, auto-completion, and analytics.

## Base URL

```
http://localhost:8080/ai
```

## Authentication

All endpoints require the following headers:
- `X-User-ID`: Required. The ID of the user making the request
- `X-Request-ID`: Optional. A unique identifier for request tracing

## Endpoints

### 1. Fast Search

Performs a fast search across indexed documents with advanced features.

**Endpoint:** `POST /search`

**Request Body:**
```json
{
  "query": "search terms",
  "filters": {
    "document_type": "report",
    "language": "vi"
  },
  "team_id": "team123",
  "document_types": ["report", "document"],
  "tags": ["important", "quarterly"],
  "from": 0,
  "size": 10,
  "sort_by": "updated_at",
  "sort_order": "desc",
  "highlight": true,
  "facets": ["document_type", "tags"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "T<PERSON><PERSON> kiếm nhanh thành công",
  "data": {
    "hits": [
      {
        "id": "doc_123",
        "score": 0.95,
        "title": "Quarterly Report Q4 2024",
        "content": "This is the quarterly report...",
        "summary": "Summary of Q4 performance",
        "document_type": "report",
        "team_id": "team123",
        "user_id": "user456",
        "tags": ["quarterly", "report"],
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T15:45:00Z",
        "highlights": {
          "title": ["<mark>Quarterly</mark> Report Q4 2024"],
          "content": ["This is the <mark>quarterly</mark> report..."]
        },
        "url": "https://example.com/doc/123"
      }
    ],
    "total": 25,
    "max_score": 0.95,
    "took": 45,
    "facets": [
      {
        "field": "document_type",
        "buckets": [
          {"key": "report", "count": 15},
          {"key": "document", "count": 10}
        ]
      }
    ],
    "from": 0,
    "size": 10
  }
}
```

### 2. Auto-Complete

Provides auto-completion suggestions for search queries.

**Endpoint:** `POST /autocomplete`

**Request Body:**
```json
{
  "query": "quar",
  "team_id": "team123",
  "field": "title",
  "size": 5
}
```

**Response:**
```json
{
  "success": true,
  "message": "Gợi ý tự động thành công",
  "data": {
    "suggestions": [
      {"text": "quarterly report", "score": 0.9},
      {"text": "quarterly review", "score": 0.8},
      {"text": "quarterly meeting", "score": 0.7}
    ]
  }
}
```

### 3. Index Document

Indexes a single document for search.

**Endpoint:** `POST /index`

**Request Body:**
```json
{
  "document": {
    "id": "doc_123",
    "title": "Important Document",
    "content": "This is the content of the document...",
    "summary": "Document summary",
    "document_type": "report",
    "team_id": "team123",
    "tags": ["important", "quarterly"],
    "metadata": {
      "author": "John Doe",
      "department": "Finance"
    },
    "language": "vi",
    "file_type": "pdf",
    "file_size": 1024000,
    "url": "https://example.com/doc/123"
  },
  "index": "custom_index"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tài liệu đã được lập chỉ mục thành công",
  "data": null
}
```

### 4. Bulk Index Documents

Indexes multiple documents in a single request for better performance.

**Endpoint:** `POST /bulk-index`

**Request Body:**
```json
{
  "documents": [
    {
      "title": "Document 1",
      "content": "Content of document 1...",
      "document_type": "report",
      "team_id": "team123"
    },
    {
      "title": "Document 2",
      "content": "Content of document 2...",
      "document_type": "document",
      "team_id": "team123"
    }
  ],
  "index": "custom_index"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đã lập chỉ mục thành công 2 tài liệu",
  "data": null
}
```

### 5. Search Statistics

Returns search service statistics and performance metrics.

**Endpoint:** `GET /search/stats`

**Response:**
```json
{
  "success": true,
  "message": "Thống kê tìm kiếm",
  "data": {
    "healthy": true,
    "default_index": "swork_documents",
    "cache_items": 150,
    "cache_stats": {
      "items": 150,
      "hits": 1200,
      "misses": 300,
      "hit_rate": 0.8
    },
    "cluster_status": "green",
    "cluster_nodes": 3,
    "active_shards": 15,
    "unassigned_shards": 0
  }
}
```

## Advanced Features

### Faceted Search

Use the `facets` parameter in the search request to get aggregated counts for specific fields:

```json
{
  "query": "report",
  "facets": ["document_type", "tags", "language"]
}
```

### Highlighting

Set `highlight: true` to get highlighted snippets showing where search terms appear in the content.

### Sorting

Use `sort_by` and `sort_order` to control result ordering:
- `sort_by`: Field to sort by (e.g., "updated_at", "created_at", "title")
- `sort_order`: "asc" or "desc"

### Filtering

Use the `filters` object to apply exact-match filters:
```json
{
  "filters": {
    "document_type": "report",
    "language": "vi",
    "user_id": "user123"
  }
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "success": false,
  "message": "Error description in Vietnamese",
  "data": null
}
```

Common HTTP status codes:
- `400`: Bad Request - Invalid request format
- `401`: Unauthorized - Missing or invalid X-User-ID header
- `500`: Internal Server Error - Service error or Elasticsearch unavailable

## Performance Considerations

1. **Pagination**: Use `from` and `size` parameters for pagination. Maximum `size` is 100.
2. **Caching**: Results are cached for 5 minutes to improve performance.
3. **Bulk Operations**: Use bulk indexing for better performance when indexing multiple documents.
4. **Facets**: Limit the number of facet fields to avoid performance impact.

## Fallback Behavior

When Elasticsearch is unavailable, the service provides fallback responses:
- Search requests return mock results based on the query
- Auto-complete returns basic suggestions
- Indexing operations return appropriate error messages
- Statistics show service unavailability

This ensures the API remains responsive even when the search backend is down.
