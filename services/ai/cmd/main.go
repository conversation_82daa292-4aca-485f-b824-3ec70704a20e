package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/ai-service/internal/config"
	"github.com/swork-team/ai-service/internal/handlers"
	"github.com/swork-team/ai-service/internal/services"
)

func main() {
	// Initialize configuration
	cfg := config.LoadConfig()

	// Initialize services
	aiService := services.NewAIService(cfg)

	// Initialize handlers
	handler := handlers.NewHandler(aiService)

	// Setup router
	router := setupRouter(handler)

	// Create HTTP server
	srv := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Println("Starting Swork AI Service on port 8080...")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}

func setupRouter(handler *handlers.Handler) *gin.Engine {
	// Set Gin mode based on environment
	if os.Getenv("GIN_MODE") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// Add basic middleware
	router.Use(corsMiddleware())
	router.Use(gin.Recovery())

	// Health check endpoint
	router.GET("/health", handler.HealthCheck)

	// AI endpoints
	ai := router.Group("/ai")
	{
		// Core AI features
		ai.POST("/rag/query", handler.RAGQuery)
		ai.POST("/generate-email", handler.GenerateEmail)
		ai.POST("/semantic-search", handler.SemanticSearch)
		ai.POST("/summarize-document", handler.SummarizeDocument)
		ai.POST("/rag/qa", handler.RAGQA)

		// Search features
		ai.POST("/search", handler.FastSearch)
		ai.POST("/autocomplete", handler.AutoComplete)
		ai.POST("/index", handler.IndexDocument)
		ai.POST("/bulk-index", handler.BulkIndexDocuments)
		ai.GET("/search/stats", handler.GetSearchStats)
	}

	return router
}

// corsMiddleware provides basic CORS support
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-User-ID, X-Request-ID")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}