package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/calendar/internal/models"
	"gorm.io/gorm"
)

type CalendarRepository struct {
	db *gorm.DB
}

func NewCalendarRepository(db *gorm.DB) *CalendarRepository {
	return &CalendarRepository{
		db: db,
	}
}

// Calendar operations
func (r *CalendarRepository) CreateCalendar(ctx context.Context, calendar *models.Calendar) error {
	result := r.db.WithContext(ctx).Create(calendar)
	if result.Error != nil {
		return fmt.Errorf("failed to create calendar: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) GetCalendarByID(ctx context.Context, id uuid.UUID) (*models.Calendar, error) {
	var calendar models.Calendar
	result := r.db.WithContext(ctx).
		Preload("Events", "deleted_at IS NULL").
		Preload("Shares").
		Where("id = ?", id).
		First(&calendar)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get calendar: %w", result.Error)
	}
	
	return &calendar, nil
}

func (r *CalendarRepository) GetUserCalendars(ctx context.Context, userID string) ([]models.Calendar, error) {
	var calendars []models.Calendar
	
	// Get owned calendars and shared calendars
	result := r.db.WithContext(ctx).
		Where("owner_id = ? AND owner_type = ? AND is_active = ?", userID, models.CalendarOwnerTypeUser, true).
		Or("id IN (SELECT calendar_id FROM calendar_shares WHERE shared_with = ? AND (expires_at IS NULL OR expires_at > ?))", userID, time.Now()).
		Order("is_default DESC, created_at ASC").
		Find(&calendars)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user calendars: %w", result.Error)
	}
	
	return calendars, nil
}

func (r *CalendarRepository) GetTeamCalendars(ctx context.Context, teamID string) ([]models.Calendar, error) {
	var calendars []models.Calendar
	result := r.db.WithContext(ctx).
		Where("team_id = ? AND owner_type = ? AND is_active = ?", teamID, models.CalendarOwnerTypeTeam, true).
		Order("created_at ASC").
		Find(&calendars)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get team calendars: %w", result.Error)
	}
	
	return calendars, nil
}

func (r *CalendarRepository) UpdateCalendar(ctx context.Context, calendar *models.Calendar) error {
	result := r.db.WithContext(ctx).Save(calendar)
	if result.Error != nil {
		return fmt.Errorf("failed to update calendar: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) DeleteCalendar(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Delete(&models.Calendar{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete calendar: %w", result.Error)
	}
	return nil
}

// Event operations
func (r *CalendarRepository) CreateEvent(ctx context.Context, event *models.Event) error {
	tx := r.db.WithContext(ctx).Begin()
	
	// Create event
	if err := tx.Create(event).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create event: %w", err)
	}
	
	// Create attendees if provided
	if len(event.Attendees) > 0 {
		for i := range event.Attendees {
			event.Attendees[i].EventID = event.ID
		}
		if err := tx.Create(&event.Attendees).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create attendees: %w", err)
		}
	}
	
	// Create reminders if provided
	if len(event.Reminders) > 0 {
		for i := range event.Reminders {
			event.Reminders[i].EventID = event.ID
		}
		if err := tx.Create(&event.Reminders).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create reminders: %w", err)
		}
	}
	
	return tx.Commit().Error
}

func (r *CalendarRepository) GetEventByID(ctx context.Context, id uuid.UUID) (*models.Event, error) {
	var event models.Event
	result := r.db.WithContext(ctx).
		Preload("Calendar").
		Preload("Attendees").
		Preload("Reminders").
		Preload("Attachments").
		Where("id = ?", id).
		First(&event)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get event: %w", result.Error)
	}
	
	return &event, nil
}

func (r *CalendarRepository) GetEventsByCalendar(ctx context.Context, calendarID uuid.UUID, startTime, endTime time.Time) ([]models.Event, error) {
	var events []models.Event
	query := r.db.WithContext(ctx).
		Preload("Attendees").
		Preload("Reminders").
		Where("calendar_id = ?", calendarID)
	
	if !startTime.IsZero() && !endTime.IsZero() {
		query = query.Where("(start_time BETWEEN ? AND ?) OR (end_time BETWEEN ? AND ?) OR (start_time <= ? AND end_time >= ?)",
			startTime, endTime, startTime, endTime, startTime, endTime)
	}
	
	result := query.Order("start_time ASC").Find(&events)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get events: %w", result.Error)
	}
	
	return events, nil
}

func (r *CalendarRepository) GetUserEvents(ctx context.Context, userID string, startTime, endTime time.Time) ([]models.Event, error) {
	var events []models.Event
	
	// Get events from user's calendars and events where user is an attendee
	query := r.db.WithContext(ctx).
		Preload("Calendar").
		Preload("Attendees").
		Preload("Reminders").
		Joins("LEFT JOIN calendars ON events.calendar_id = calendars.id").
		Joins("LEFT JOIN event_attendees ON events.id = event_attendees.event_id").
		Where("(calendars.owner_id = ? AND calendars.owner_type = ?) OR event_attendees.user_id = ?", 
			userID, models.CalendarOwnerTypeUser, userID)
	
	if !startTime.IsZero() && !endTime.IsZero() {
		query = query.Where("(events.start_time BETWEEN ? AND ?) OR (events.end_time BETWEEN ? AND ?) OR (events.start_time <= ? AND events.end_time >= ?)",
			startTime, endTime, startTime, endTime, startTime, endTime)
	}
	
	result := query.Distinct().Order("events.start_time ASC").Find(&events)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user events: %w", result.Error)
	}
	
	return events, nil
}

func (r *CalendarRepository) SearchEvents(ctx context.Context, userID, query string, startTime, endTime time.Time, limit, offset int) ([]models.Event, error) {
	var events []models.Event
	
	searchQuery := r.db.WithContext(ctx).
		Preload("Calendar").
		Preload("Attendees").
		Joins("LEFT JOIN calendars ON events.calendar_id = calendars.id").
		Joins("LEFT JOIN event_attendees ON events.id = event_attendees.event_id").
		Where("(calendars.owner_id = ? AND calendars.owner_type = ?) OR event_attendees.user_id = ?", 
			userID, models.CalendarOwnerTypeUser, userID)
	
	if query != "" {
		searchPattern := "%" + query + "%"
		searchQuery = searchQuery.Where("events.title ILIKE ? OR events.description ILIKE ? OR events.location ILIKE ?", 
			searchPattern, searchPattern, searchPattern)
	}
	
	if !startTime.IsZero() && !endTime.IsZero() {
		searchQuery = searchQuery.Where("(events.start_time BETWEEN ? AND ?) OR (events.end_time BETWEEN ? AND ?) OR (events.start_time <= ? AND events.end_time >= ?)",
			startTime, endTime, startTime, endTime, startTime, endTime)
	}
	
	result := searchQuery.Distinct().
		Order("events.start_time ASC").
		Limit(limit).Offset(offset).
		Find(&events)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to search events: %w", result.Error)
	}
	
	return events, nil
}

func (r *CalendarRepository) UpdateEvent(ctx context.Context, event *models.Event) error {
	result := r.db.WithContext(ctx).Save(event)
	if result.Error != nil {
		return fmt.Errorf("failed to update event: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) DeleteEvent(ctx context.Context, id uuid.UUID) error {
	tx := r.db.WithContext(ctx).Begin()
	
	// Delete related records first
	if err := tx.Where("event_id = ?", id).Delete(&models.EventAttendee{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete attendees: %w", err)
	}
	
	if err := tx.Where("event_id = ?", id).Delete(&models.EventReminder{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete reminders: %w", err)
	}
	
	if err := tx.Where("event_id = ?", id).Delete(&models.EventAttachment{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete attachments: %w", err)
	}
	
	// Delete the event
	if err := tx.Delete(&models.Event{}, id).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete event: %w", err)
	}
	
	return tx.Commit().Error
}

// Attendee operations
func (r *CalendarRepository) AddAttendee(ctx context.Context, attendee *models.EventAttendee) error {
	result := r.db.WithContext(ctx).Create(attendee)
	if result.Error != nil {
		return fmt.Errorf("failed to add attendee: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) GetAttendee(ctx context.Context, eventID uuid.UUID, userID string) (*models.EventAttendee, error) {
	var attendee models.EventAttendee
	result := r.db.WithContext(ctx).Where("event_id = ? AND user_id = ?", eventID, userID).First(&attendee)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get attendee: %w", result.Error)
	}
	
	return &attendee, nil
}

func (r *CalendarRepository) UpdateAttendee(ctx context.Context, attendee *models.EventAttendee) error {
	result := r.db.WithContext(ctx).Save(attendee)
	if result.Error != nil {
		return fmt.Errorf("failed to update attendee: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) RemoveAttendee(ctx context.Context, eventID uuid.UUID, userID string) error {
	result := r.db.WithContext(ctx).Where("event_id = ? AND user_id = ?", eventID, userID).Delete(&models.EventAttendee{})
	if result.Error != nil {
		return fmt.Errorf("failed to remove attendee: %w", result.Error)
	}
	return nil
}

// Reminder operations
func (r *CalendarRepository) CreateReminder(ctx context.Context, reminder *models.EventReminder) error {
	result := r.db.WithContext(ctx).Create(reminder)
	if result.Error != nil {
		return fmt.Errorf("failed to create reminder: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) GetPendingReminders(ctx context.Context, beforeTime time.Time) ([]models.EventReminder, error) {
	var reminders []models.EventReminder
	result := r.db.WithContext(ctx).
		Preload("Event").
		Joins("JOIN events ON event_reminders.event_id = events.id").
		Where("event_reminders.is_sent = ? AND events.start_time - INTERVAL '1 minute' * event_reminders.minutes_before <= ?", 
			false, beforeTime).
		Find(&reminders)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get pending reminders: %w", result.Error)
	}
	
	return reminders, nil
}

func (r *CalendarRepository) MarkReminderSent(ctx context.Context, id uuid.UUID) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.EventReminder{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_sent": true,
			"sent_at": &now,
		})
	
	if result.Error != nil {
		return fmt.Errorf("failed to mark reminder as sent: %w", result.Error)
	}
	return nil
}

// Availability operations
func (r *CalendarRepository) CreateAvailabilitySlot(ctx context.Context, slot *models.AvailabilitySlot) error {
	result := r.db.WithContext(ctx).Create(slot)
	if result.Error != nil {
		return fmt.Errorf("failed to create availability slot: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) GetUserAvailability(ctx context.Context, userID string) ([]models.AvailabilitySlot, error) {
	var slots []models.AvailabilitySlot
	result := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("day_of_week ASC, start_time ASC").
		Find(&slots)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user availability: %w", result.Error)
	}
	
	return slots, nil
}

func (r *CalendarRepository) UpdateAvailabilitySlot(ctx context.Context, slot *models.AvailabilitySlot) error {
	result := r.db.WithContext(ctx).Save(slot)
	if result.Error != nil {
		return fmt.Errorf("failed to update availability slot: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) DeleteAvailabilitySlot(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Delete(&models.AvailabilitySlot{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete availability slot: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) CreateAvailabilityException(ctx context.Context, exception *models.AvailabilityException) error {
	result := r.db.WithContext(ctx).Create(exception)
	if result.Error != nil {
		return fmt.Errorf("failed to create availability exception: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) GetAvailabilityExceptions(ctx context.Context, userID string, startDate, endDate time.Time) ([]models.AvailabilityException, error) {
	var exceptions []models.AvailabilityException
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND date BETWEEN ? AND ?", userID, startDate, endDate).
		Order("date ASC").
		Find(&exceptions)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get availability exceptions: %w", result.Error)
	}
	
	return exceptions, nil
}

// Calendar sharing operations
func (r *CalendarRepository) ShareCalendar(ctx context.Context, share *models.CalendarShare) error {
	result := r.db.WithContext(ctx).Create(share)
	if result.Error != nil {
		return fmt.Errorf("failed to share calendar: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) GetCalendarShare(ctx context.Context, calendarID uuid.UUID, userID string) (*models.CalendarShare, error) {
	var share models.CalendarShare
	result := r.db.WithContext(ctx).
		Where("calendar_id = ? AND shared_with = ?", calendarID, userID).
		First(&share)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get calendar share: %w", result.Error)
	}
	
	return &share, nil
}

func (r *CalendarRepository) UpdateCalendarShare(ctx context.Context, share *models.CalendarShare) error {
	result := r.db.WithContext(ctx).Save(share)
	if result.Error != nil {
		return fmt.Errorf("failed to update calendar share: %w", result.Error)
	}
	return nil
}

func (r *CalendarRepository) RemoveCalendarShare(ctx context.Context, calendarID uuid.UUID, userID string) error {
	result := r.db.WithContext(ctx).
		Where("calendar_id = ? AND shared_with = ?", calendarID, userID).
		Delete(&models.CalendarShare{})
	
	if result.Error != nil {
		return fmt.Errorf("failed to remove calendar share: %w", result.Error)
	}
	return nil
}

// Statistics and utility operations
func (r *CalendarRepository) GetCalendarStats(ctx context.Context, calendarID uuid.UUID) (*CalendarStats, error) {
	var stats CalendarStats
	
	// Get total event count
	if err := r.db.WithContext(ctx).Model(&models.Event{}).
		Where("calendar_id = ?", calendarID).
		Count(&stats.TotalEvents).Error; err != nil {
		return nil, fmt.Errorf("failed to get event count: %w", err)
	}
	
	// Get upcoming events count
	if err := r.db.WithContext(ctx).Model(&models.Event{}).
		Where("calendar_id = ? AND start_time > ?", calendarID, time.Now()).
		Count(&stats.UpcomingEvents).Error; err != nil {
		return nil, fmt.Errorf("failed to get upcoming events count: %w", err)
	}
	
	// Get events by type
	var eventsByType []struct {
		EventType string
		Count     int64
	}
	
	if err := r.db.WithContext(ctx).Model(&models.Event{}).
		Select("event_type, COUNT(*) as count").
		Where("calendar_id = ?", calendarID).
		Group("event_type").
		Scan(&eventsByType).Error; err != nil {
		return nil, fmt.Errorf("failed to get events by type: %w", err)
	}
	
	stats.EventsByType = make(map[string]int64)
	for _, et := range eventsByType {
		stats.EventsByType[et.EventType] = et.Count
	}
	
	return &stats, nil
}

func (r *CalendarRepository) CheckConflicts(ctx context.Context, userID string, startTime, endTime time.Time, excludeEventID *uuid.UUID) ([]models.Event, error) {
	var conflicts []models.Event
	
	query := r.db.WithContext(ctx).
		Preload("Calendar").
		Joins("JOIN event_attendees ON events.id = event_attendees.event_id").
		Where("event_attendees.user_id = ? AND event_attendees.status = ?", userID, models.AttendeeStatusAccepted).
		Where("(events.start_time < ? AND events.end_time > ?) OR (events.start_time < ? AND events.end_time > ?) OR (events.start_time >= ? AND events.end_time <= ?)",
			endTime, startTime, startTime, endTime, startTime, endTime)
	
	if excludeEventID != nil {
		query = query.Where("events.id != ?", *excludeEventID)
	}
	
	result := query.Find(&conflicts)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to check conflicts: %w", result.Error)
	}
	
	return conflicts, nil
}

type CalendarStats struct {
	TotalEvents    int64            `json:"total_events"`
	UpcomingEvents int64            `json:"upcoming_events"`
	EventsByType   map[string]int64 `json:"events_by_type"`
}