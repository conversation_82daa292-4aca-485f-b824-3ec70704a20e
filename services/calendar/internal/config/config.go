package config

import (
	"time"

	"github.com/swork-team/platform/pkg/config"
)

// Config extends the base configuration with Calendar-specific settings
type Config struct {
	config.BaseServiceConfig
	Calendar CalendarConfig `json:"calendar"`
}

type CalendarConfig struct {
	DefaultTimeZone       string        `json:"default_timezone"`
	MaxEventsPerDay       int           `json:"max_events_per_day"`
	MaxAttendeesPerEvent  int           `json:"max_attendees_per_event"`
	DefaultReminderTime   int           `json:"default_reminder_time"` // minutes before event
	MaxEventDuration      time.Duration `json:"max_event_duration"`
	MinEventDuration      time.Duration `json:"min_event_duration"`
	ReminderCheckInterval time.Duration `json:"reminder_check_interval"`
	EnableRecurrence      bool          `json:"enable_recurrence"`
	EnableNotifications   bool          `json:"enable_notifications"`
}


func LoadConfig() *Config {
	// Load base configuration
	baseConfig := config.LoadBaseConfig("CALENDAR")
	
	return &Config{
		BaseServiceConfig: *baseConfig,
		Calendar: CalendarConfig{
			DefaultTimeZone:       config.GetEnv("DEFAULT_TIMEZONE", "UTC"),
			MaxEventsPerDay:       config.GetIntEnv("MAX_EVENTS_PER_DAY", 50),
			MaxAttendeesPerEvent:  config.GetIntEnv("MAX_ATTENDEES_PER_EVENT", 100),
			DefaultReminderTime:   config.GetIntEnv("DEFAULT_REMINDER_TIME", 15),
			MaxEventDuration:      config.GetDurationEnv("MAX_EVENT_DURATION", 24*time.Hour),
			MinEventDuration:      config.GetDurationEnv("MIN_EVENT_DURATION", 15*time.Minute),
			ReminderCheckInterval: config.GetDurationEnv("REMINDER_CHECK_INTERVAL", 1*time.Minute),
			EnableRecurrence:      config.GetBoolEnv("ENABLE_RECURRENCE", true),
			EnableNotifications:   config.GetBoolEnv("ENABLE_NOTIFICATIONS", true),
		},
	}
}

