package handlers

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/calendar/internal/models"
	"github.com/swork-team/platform/services/calendar/internal/services"
)

type CalendarHandler struct {
	calendarService *services.CalendarService
}

func NewCalendarHandler(calendarService *services.CalendarService) *CalendarHandler {
	return &CalendarHandler{
		calendarService: calendarService,
	}
}

// Calendar management handlers

// @Summary Create a new calendar
// @Description Create a new calendar for the authenticated user
// @Tags calendars
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body CreateCalendarRequest true "Calendar data"
// @Success 201 {object} utils.APIResponse{data=models.Calendar}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /calendars [post]
func (h *CalendarHandler) CreateCalendar(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	var req CreateCalendarRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Convert handler request to service request
	serviceReq := &services.CreateCalendarRequest{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		TeamID:      req.TeamID,
		Visibility:  req.Visibility,
		IsDefault:   req.IsDefault,
		TimeZone:    req.TimeZone,
	}
	
	calendar, err := h.calendarService.CreateCalendar(c.Request.Context(), userID, serviceReq)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Calendar created successfully", calendar)
}

// @Summary Get calendar by ID
// @Description Get calendar details by ID
// @Tags calendars
// @Security BearerAuth
// @Param id path string true "Calendar ID"
// @Success 200 {object} utils.APIResponse{data=models.Calendar}
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /calendars/{id} [get]
func (h *CalendarHandler) GetCalendar(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	calendarIDStr := c.Param("id")
	calendarID, err := uuid.Parse(calendarIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid calendar ID", err)
		return
	}

	calendar, err := h.calendarService.GetCalendar(c.Request.Context(), calendarID, userID)
	if err != nil {
		if err.Error() == "calendar not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Calendar not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get calendar", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Calendar retrieved successfully", calendar)
}

// @Summary Get user's calendars
// @Description Get list of calendars for the authenticated user
// @Tags calendars
// @Security BearerAuth
// @Success 200 {object} utils.APIResponse{data=[]models.Calendar}
// @Failure 401 {object} utils.APIResponse
// @Router /calendars [get]
func (h *CalendarHandler) GetUserCalendars(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	calendars, err := h.calendarService.GetUserCalendars(c.Request.Context(), userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get calendars", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Calendars retrieved successfully", calendars)
}

// @Summary Update calendar
// @Description Update calendar information
// @Tags calendars
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Calendar ID"
// @Param request body UpdateCalendarRequest true "Updated calendar data"
// @Success 200 {object} utils.APIResponse{data=models.Calendar}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Router /calendars/{id} [put]
func (h *CalendarHandler) UpdateCalendar(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	calendarIDStr := c.Param("id")
	calendarID, err := uuid.Parse(calendarIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid calendar ID", err)
		return
	}

	var req UpdateCalendarRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Convert handler request to service request
	serviceReq := &services.UpdateCalendarRequest{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		Visibility:  req.Visibility,
		IsDefault:   req.IsDefault,
		TimeZone:    req.TimeZone,
	}
	
	calendar, err := h.calendarService.UpdateCalendar(c.Request.Context(), calendarID, userID, serviceReq)
	if err != nil {
		if err.Error() == "calendar not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Calendar not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Calendar updated successfully", calendar)
}

// @Summary Delete calendar
// @Description Delete a calendar
// @Tags calendars
// @Security BearerAuth
// @Param id path string true "Calendar ID"
// @Success 200 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /calendars/{id} [delete]
func (h *CalendarHandler) DeleteCalendar(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	calendarIDStr := c.Param("id")
	calendarID, err := uuid.Parse(calendarIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid calendar ID", err)
		return
	}

	err = h.calendarService.DeleteCalendar(c.Request.Context(), calendarID, userID)
	if err != nil {
		if err.Error() == "calendar not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Calendar not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Calendar deleted successfully", nil)
}

// Event management handlers

// @Summary Create a new event
// @Description Create a new event in a calendar
// @Tags events
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body CreateEventRequest true "Event data"
// @Success 201 {object} utils.APIResponse{data=models.Event}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /events [post]
func (h *CalendarHandler) CreateEvent(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	var req CreateEventRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Convert handler request to service request
	serviceReq := &services.CreateEventRequest{
		CalendarID:        req.CalendarID,
		Title:             req.Title,
		Description:       req.Description,
		Location:          req.Location,
		StartTime:         req.StartTime,
		EndTime:           req.EndTime,
		AllDay:            req.AllDay,
		TimeZone:          req.TimeZone,
		EventType:         req.EventType,
		Status:            req.Status,
		Priority:          req.Priority,
		Visibility:        req.Visibility,
		IsRecurring:       req.IsRecurring,
		RecurrenceRule:    req.RecurrenceRule,
		AttendeeIDs:       req.AttendeeIDs,
		OptionalAttendees: req.OptionalAttendees,
		ReminderMinutes:   req.ReminderMinutes,
		CheckConflicts:    req.CheckConflicts,
	}
	
	event, err := h.calendarService.CreateEvent(c.Request.Context(), userID, serviceReq)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Event created successfully", event)
}

// @Summary Get event by ID
// @Description Get event details by ID
// @Tags events
// @Security BearerAuth
// @Param id path string true "Event ID"
// @Success 200 {object} utils.APIResponse{data=models.Event}
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /events/{id} [get]
func (h *CalendarHandler) GetEvent(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	eventIDStr := c.Param("id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid event ID", err)
		return
	}

	event, err := h.calendarService.GetEvent(c.Request.Context(), eventID, userID)
	if err != nil {
		if err.Error() == "event not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Event not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get event", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Event retrieved successfully", event)
}

// @Summary Get events
// @Description Get events from calendar or for user
// @Tags events
// @Security BearerAuth
// @Param calendar_id query string false "Calendar ID"
// @Param start_time query string false "Start time (RFC3339 format)"
// @Param end_time query string false "End time (RFC3339 format)"
// @Success 200 {object} utils.APIResponse{data=[]models.Event}
// @Failure 401 {object} utils.APIResponse
// @Router /events [get]
func (h *CalendarHandler) GetEvents(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	serviceReq := &services.GetEventsRequest{
		UserID: userID,
	}

	// Parse calendar ID if provided
	if calendarIDStr := c.Query("calendar_id"); calendarIDStr != "" {
		if calendarID, err := uuid.Parse(calendarIDStr); err == nil {
			serviceReq.CalendarID = calendarID
		}
	}

	// Parse time range
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			serviceReq.StartTime = startTime
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			serviceReq.EndTime = endTime
		}
	}

	events, err := h.calendarService.GetEvents(c.Request.Context(), serviceReq)
	if err != nil {
		if err.Error() == "calendar not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Calendar not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Events retrieved successfully", events)
}

// @Summary Search events
// @Description Search events by title, description, or location
// @Tags events
// @Security BearerAuth
// @Param q query string true "Search query"
// @Param start_time query string false "Start time (RFC3339 format)"
// @Param end_time query string false "End time (RFC3339 format)"
// @Param limit query int false "Number of events per page" default(20)
// @Param offset query int false "Number of events to skip" default(0)
// @Success 200 {object} utils.APIResponse{data=[]models.Event}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /events/search [get]
func (h *CalendarHandler) SearchEvents(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	query := c.Query("q")
	if query == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Search query is required", nil)
		return
	}

	limit, offset := middleware.ValidatePagination(c)

	var startTime, endTime time.Time
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = t
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = t
		}
	}

	events, err := h.calendarService.SearchEvents(c.Request.Context(), userID, query, startTime, endTime, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to search events", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Search completed successfully", events)
}

// @Summary Update event
// @Description Update event information
// @Tags events
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Event ID"
// @Param request body UpdateEventRequest true "Updated event data"
// @Success 200 {object} utils.APIResponse{data=models.Event}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Router /events/{id} [put]
func (h *CalendarHandler) UpdateEvent(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	eventIDStr := c.Param("id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid event ID", err)
		return
	}

	var req UpdateEventRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Convert handler request to service request
	serviceReq := &services.UpdateEventRequest{
		Title:          req.Title,
		Description:    req.Description,
		Location:       req.Location,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		AllDay:         req.AllDay,
		EventType:      req.EventType,
		Status:         req.Status,
		Priority:       req.Priority,
		Visibility:     req.Visibility,
		CheckConflicts: req.CheckConflicts,
	}
	
	event, err := h.calendarService.UpdateEvent(c.Request.Context(), eventID, userID, serviceReq)
	if err != nil {
		if err.Error() == "event not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Event not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Event updated successfully", event)
}

// @Summary Delete event
// @Description Delete an event
// @Tags events
// @Security BearerAuth
// @Param id path string true "Event ID"
// @Success 200 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /events/{id} [delete]
func (h *CalendarHandler) DeleteEvent(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	eventIDStr := c.Param("id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid event ID", err)
		return
	}

	err = h.calendarService.DeleteEvent(c.Request.Context(), eventID, userID)
	if err != nil {
		if err.Error() == "event not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Event not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete event", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Event deleted successfully", nil)
}

// @Summary Respond to event invitation
// @Description Accept, decline, or tentatively accept an event invitation
// @Tags events
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Event ID"
// @Param request body RespondToEventRequest true "Response data"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /events/{id}/respond [post]
func (h *CalendarHandler) RespondToEvent(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	eventIDStr := c.Param("id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid event ID", err)
		return
	}

	var req RespondToEventRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Convert handler request to service request
	serviceReq := &services.RespondToEventRequest{
		Status: req.Status,
		Note:   req.Note,
	}
	
	err = h.calendarService.RespondToEvent(c.Request.Context(), eventID, userID, serviceReq)
	if err != nil {
		if err.Error() == "event not found" || err.Error() == "user is not invited to this event" {
			utils.ErrorResponse(c, http.StatusNotFound, err.Error(), nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Response recorded successfully", nil)
}

// Availability handlers

// @Summary Set availability
// @Description Set weekly availability schedule
// @Tags availability
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body SetAvailabilityRequest true "Availability data"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /availability [post]
func (h *CalendarHandler) SetAvailability(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	var req SetAvailabilityRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Convert handler request to service request
	// Parse current user ID to UUID for slots
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID format", err)
		return
	}

	serviceSlots := make([]models.AvailabilitySlot, len(req.Slots))
	for i, slot := range req.Slots {
		serviceSlots[i] = models.AvailabilitySlot{
			UserID:      userUUID, // Use authenticated user's ID
			DayOfWeek:   slot.DayOfWeek,
			StartTime:   slot.StartTime,
			EndTime:     slot.EndTime,
			TimeZone:    slot.TimeZone,
			IsAvailable: slot.IsAvailable,
			SlotType:    models.AvailabilityType(slot.SlotType),
		}
	}
	
	serviceReq := &services.SetAvailabilityRequest{
		Slots: serviceSlots,
	}
	
	err = h.calendarService.SetAvailability(c.Request.Context(), userID, serviceReq)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Availability set successfully", nil)
}

// @Summary Get availability
// @Description Get user's availability for a date range
// @Tags availability
// @Security BearerAuth
// @Param start_date query string true "Start date (YYYY-MM-DD format)"
// @Param end_date query string true "End date (YYYY-MM-DD format)"
// @Success 200 {object} utils.APIResponse{data=AvailabilityResponse}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /availability [get]
func (h *CalendarHandler) GetAvailability(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Start date and end date are required", nil)
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid start date format (use YYYY-MM-DD)", err)
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid end date format (use YYYY-MM-DD)", err)
		return
	}

	serviceAvailability, err := h.calendarService.GetAvailability(c.Request.Context(), userID, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get availability", err)
		return
	}

	// Convert service response to handler response
	handlerSlots := make([]AvailabilitySlot, len(serviceAvailability.Slots))
	for i, slot := range serviceAvailability.Slots {
		handlerSlots[i] = AvailabilitySlot{
			ID:          slot.ID.String(),
			UserID:      slot.UserID.String(),
			DayOfWeek:   slot.DayOfWeek,
			StartTime:   slot.StartTime,
			EndTime:     slot.EndTime,
			TimeZone:    slot.TimeZone,
			IsAvailable: slot.IsAvailable,
			SlotType:    string(slot.SlotType),
		}
	}
	
	handlerExceptions := make([]AvailabilityException, len(serviceAvailability.Exceptions))
	for i, exception := range serviceAvailability.Exceptions {
		handlerExceptions[i] = AvailabilityException{
			ID:          exception.ID.String(),
			UserID:      exception.UserID.String(),
			Date:        exception.Date,
			StartTime:   exception.StartTime,
			EndTime:     exception.EndTime,
			IsAvailable: exception.IsAvailable,
			Reason:      exception.Reason,
		}
	}
	
	availability := AvailabilityResponse{
		Slots:      handlerSlots,
		Exceptions: handlerExceptions,
	}

	utils.SuccessResponse(c, http.StatusOK, "Availability retrieved successfully", availability)
}

// @Summary Check availability
// @Description Check availability of multiple users for a time slot
// @Tags availability
// @Security BearerAuth
// @Param user_ids query string true "Comma-separated user IDs"
// @Param start_time query string true "Start time (RFC3339 format)"
// @Param end_time query string true "End time (RFC3339 format)"
// @Success 200 {object} utils.APIResponse{data=AvailabilityCheckResponse}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /availability/check [get]
func (h *CalendarHandler) CheckAvailability(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by middleware
	}

	userIDsStr := c.Query("user_ids")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if userIDsStr == "" || startTimeStr == "" || endTimeStr == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "User IDs, start time, and end time are required", nil)
		return
	}

	userIDs := strings.Split(userIDsStr, ",")
	for i := range userIDs {
		userIDs[i] = strings.TrimSpace(userIDs[i])
	}

	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid start time format", err)
		return
	}

	endTime, err := time.Parse(time.RFC3339, endTimeStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid end time format", err)
		return
	}

	serviceResult, err := h.calendarService.CheckAvailability(c.Request.Context(), userIDs, startTime, endTime)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to check availability", err)
		return
	}

	// Convert service response to handler response
	conflicts := make(map[string][]ConflictEvent)
	for userID, events := range serviceResult.Conflicts {
		conflictEvents := make([]ConflictEvent, len(events))
		for i, event := range events {
			conflictEvents[i] = ConflictEvent{
				ID:         event.ID.String(),
				Title:      event.Title,
				StartTime:  event.StartTime,
				EndTime:    event.EndTime,
				CalendarID: event.CalendarID.String(),
			}
		}
		conflicts[userID] = conflictEvents
	}
	
	result := AvailabilityCheckResponse{
		AvailableUsers:   serviceResult.AvailableUsers,
		UnavailableUsers: serviceResult.UnavailableUsers,
		Conflicts:        conflicts,
	}

	utils.SuccessResponse(c, http.StatusOK, "Availability check completed", result)
}

// Health check endpoint
func (h *CalendarHandler) HealthCheck(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Calendar service is healthy", map[string]string{
		"service": "calendar-service",
		"status":  "healthy",
	})
}