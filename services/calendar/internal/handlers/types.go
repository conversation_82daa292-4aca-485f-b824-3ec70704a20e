package handlers

import (
	"time"

	"github.com/google/uuid"
	_ "github.com/swork-team/platform/services/calendar/internal/models"
)

// Calendar Request/Response types
type CreateCalendarRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description,omitempty"`
	Color       string `json:"color,omitempty"`
	TeamID      string `json:"team_id,omitempty"`
	Visibility  string `json:"visibility,omitempty"` // "private", "public", "team"
	IsDefault   bool   `json:"is_default,omitempty"`
	TimeZone    string `json:"timezone,omitempty"`
}

type UpdateCalendarRequest struct {
	Name        string  `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	Color       string  `json:"color,omitempty"`
	Visibility  string  `json:"visibility,omitempty"` // "private", "public", "team"
	IsDefault   *bool   `json:"is_default,omitempty"`
	TimeZone    string  `json:"timezone,omitempty"`
}

// Event Request/Response types
type CreateEventRequest struct {
	CalendarID        uuid.UUID       `json:"calendar_id" binding:"required"`
	Title             string          `json:"title" binding:"required"`
	Description       string          `json:"description,omitempty"`
	Location          string          `json:"location,omitempty"`
	StartTime         time.Time       `json:"start_time" binding:"required"`
	EndTime           time.Time       `json:"end_time" binding:"required"`
	AllDay            bool            `json:"all_day,omitempty"`
	TimeZone          string          `json:"timezone,omitempty"`
	EventType         string          `json:"event_type,omitempty"` // "meeting", "task", "reminder", etc.
	Status            string          `json:"status,omitempty"`     // "confirmed", "tentative", "cancelled"
	Priority          string          `json:"priority,omitempty"`   // "low", "normal", "high", "urgent"
	Visibility        string          `json:"visibility,omitempty"` // "public", "private", "team"
	IsRecurring       bool            `json:"is_recurring,omitempty"`
	RecurrenceRule    string          `json:"recurrence_rule,omitempty"`
	AttendeeIDs       []string        `json:"attendee_ids,omitempty"`
	OptionalAttendees map[string]bool `json:"optional_attendees,omitempty"`
	ReminderMinutes   []int           `json:"reminder_minutes,omitempty"`
	CheckConflicts    bool            `json:"check_conflicts,omitempty"`
}

type UpdateEventRequest struct {
	Title          string    `json:"title,omitempty"`
	Description    *string   `json:"description,omitempty"`
	Location       *string   `json:"location,omitempty"`
	StartTime      time.Time `json:"start_time,omitempty"`
	EndTime        time.Time `json:"end_time,omitempty"`
	AllDay         *bool     `json:"all_day,omitempty"`
	EventType      string    `json:"event_type,omitempty"` // "meeting", "task", "reminder", etc.
	Status         string    `json:"status,omitempty"`     // "confirmed", "tentative", "cancelled"
	Priority       string    `json:"priority,omitempty"`   // "low", "normal", "high", "urgent"
	Visibility     string    `json:"visibility,omitempty"` // "public", "private", "team"
	CheckConflicts bool      `json:"check_conflicts,omitempty"`
}

type GetEventsRequest struct {
	UserID     string    `json:"user_id,omitempty"`
	CalendarID uuid.UUID `json:"calendar_id,omitempty"`
	StartTime  time.Time `json:"start_time,omitempty"`
	EndTime    time.Time `json:"end_time,omitempty"`
}

type RespondToEventRequest struct {
	Status string `json:"status" binding:"required"` // "pending", "accepted", "declined", "tentative"
	Note   string `json:"note,omitempty"`
}

// Availability Request/Response types
type AvailabilitySlot struct {
	ID          string `json:"id,omitempty"`
	UserID      string `json:"user_id,omitempty"`
	DayOfWeek   int    `json:"day_of_week"` // 0=Sunday, 1=Monday, etc.
	StartTime   string `json:"start_time"`  // HH:MM format
	EndTime     string `json:"end_time"`    // HH:MM format
	TimeZone    string `json:"timezone,omitempty"`
	IsAvailable bool   `json:"is_available"`
	SlotType    string `json:"slot_type,omitempty"` // "work", "personal", "break", "unavailable"
}

type AvailabilityException struct {
	ID          string    `json:"id,omitempty"`
	UserID      string    `json:"user_id,omitempty"`
	Date        time.Time `json:"date"`
	StartTime   *string   `json:"start_time,omitempty"` // HH:MM format, null for all-day
	EndTime     *string   `json:"end_time,omitempty"`   // HH:MM format, null for all-day
	IsAvailable bool      `json:"is_available"`
	Reason      string    `json:"reason,omitempty"`
}

type ConflictEvent struct {
	ID         string    `json:"id"`
	Title      string    `json:"title"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	CalendarID string    `json:"calendar_id"`
}

type SetAvailabilityRequest struct {
	Slots []AvailabilitySlot `json:"slots" binding:"required"`
}

type AvailabilityResponse struct {
	Slots      []AvailabilitySlot      `json:"slots"`
	Exceptions []AvailabilityException `json:"exceptions"`
}

type AvailabilityCheckResponse struct {
	AvailableUsers   []string                   `json:"available_users"`
	UnavailableUsers []string                   `json:"unavailable_users"`
	Conflicts        map[string][]ConflictEvent `json:"conflicts"`
}
