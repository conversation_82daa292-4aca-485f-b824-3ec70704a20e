package services

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/clients"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/calendar/internal/config"
	"github.com/swork-team/platform/services/calendar/internal/models"
	"github.com/swork-team/platform/services/calendar/internal/repositories"
)

type CalendarService struct {
	calendarRepo   *repositories.CalendarRepository
	config         *config.Config
	userClient     clients.UserServiceClient
	teamClient     clients.TeamServiceClient
	batchPopulator *utils.BatchPopulator
}

func NewCalendarService(calendarRepo *repositories.CalendarRepository, config *config.Config, redisClient *redis.Client) *CalendarService {
	userClient := clients.NewUserServiceClient(config.Services.User, redisClient)
	teamClient := clients.NewTeamServiceClient(config.Services.Team, redisClient)
	
	return &CalendarService{
		calendarRepo:   calendarRepo,
		config:         config,
		userClient:     userClient,
		teamClient:     teamClient,
		batchPopulator: utils.NewBatchPopulator(userClient, teamClient),
	}
}

// Calendar operations
func (s *CalendarService) CreateCalendar(ctx context.Context, userID string, req *CreateCalendarRequest) (*models.Calendar, error) {
	// Validate calendar data
	if err := s.validateCalendarData(req.Name, req.Description); err != nil {
		return nil, err
	}

	// Convert userID string to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Convert TeamID to UUID pointer if provided
	var teamUUID *uuid.UUID
	if req.TeamID != "" {
		teamID, err := uuid.Parse(req.TeamID)
		if err != nil {
			return nil, fmt.Errorf("invalid team ID format: %w", err)
		}
		teamUUID = &teamID
	}

	calendar := &models.Calendar{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		OwnerID:     userUUID,
		OwnerType:   models.CalendarOwnerTypeUser,
		TeamID:      teamUUID,
		Visibility:  models.CalendarVisibility(req.Visibility),
		IsDefault:   req.IsDefault,
		IsActive:    true,
		TimeZone:    req.TimeZone,
	}

	// Set default values
	if calendar.Color == "" {
		calendar.Color = "#3498db"
	}
	if calendar.TimeZone == "" {
		calendar.TimeZone = s.config.Calendar.DefaultTimeZone
	}
	if calendar.Visibility == "" {
		calendar.Visibility = models.CalendarVisibilityPrivate
	} else {
		// Convert string to enum type
		calendar.Visibility = models.CalendarVisibility(req.Visibility)
	}

	// If this is a team calendar, set appropriate values
	if req.TeamID != "" {
		calendar.OwnerType = models.CalendarOwnerTypeTeam
		calendar.OwnerID = *teamUUID
		calendar.Visibility = models.CalendarVisibilityTeam
	}

	if err := s.calendarRepo.CreateCalendar(ctx, calendar); err != nil {
		return nil, fmt.Errorf("failed to create calendar: %w", err)
	}

	return calendar, nil
}

func (s *CalendarService) GetCalendar(ctx context.Context, calendarID uuid.UUID, userID string) (*models.Calendar, error) {
	calendar, err := s.calendarRepo.GetCalendarByID(ctx, calendarID)
	if err != nil {
		return nil, fmt.Errorf("failed to get calendar: %w", err)
	}
	if calendar == nil {
		return nil, errors.New("calendar not found")
	}

	// Check if user can view the calendar
	if !calendar.CanUserView(uuid.MustParse(userID)) {
		return nil, errors.New("access denied")
	}

	// Populate owner info using batch populator
	calendars := []models.Calendar{*calendar}
	s.populateCalendarsBatch(ctx, calendars)
	*calendar = calendars[0]

	// Get event count
	if stats, err := s.calendarRepo.GetCalendarStats(ctx, calendarID); err == nil {
		calendar.EventCount = stats.TotalEvents
	}

	return calendar, nil
}

func (s *CalendarService) GetUserCalendars(ctx context.Context, userID string) ([]models.Calendar, error) {
	calendars, err := s.calendarRepo.GetUserCalendars(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user calendars: %w", err)
	}

	// Populate owner info in batch
	s.populateCalendarsBatch(ctx, calendars)

	return calendars, nil
}

func (s *CalendarService) UpdateCalendar(ctx context.Context, calendarID uuid.UUID, userID string, req *UpdateCalendarRequest) (*models.Calendar, error) {
	calendar, err := s.calendarRepo.GetCalendarByID(ctx, calendarID)
	if err != nil {
		return nil, fmt.Errorf("failed to get calendar: %w", err)
	}
	if calendar == nil {
		return nil, errors.New("calendar not found")
	}

	// Check permissions
	if !calendar.CanUserEdit(uuid.MustParse(userID)) {
		return nil, errors.New("access denied")
	}

	// Update fields
	if req.Name != "" {
		if err := s.validateCalendarName(req.Name); err != nil {
			return nil, err
		}
		calendar.Name = req.Name
	}

	if req.Description != nil {
		calendar.Description = *req.Description
	}

	if req.Color != "" {
		calendar.Color = req.Color
	}

	if req.Visibility != "" {
		calendar.Visibility = models.CalendarVisibility(req.Visibility)
	}

	if req.TimeZone != "" {
		calendar.TimeZone = req.TimeZone
	}

	if req.IsDefault != nil {
		calendar.IsDefault = *req.IsDefault
	}

	if err := s.calendarRepo.UpdateCalendar(ctx, calendar); err != nil {
		return nil, fmt.Errorf("failed to update calendar: %w", err)
	}

	return calendar, nil
}

func (s *CalendarService) DeleteCalendar(ctx context.Context, calendarID uuid.UUID, userID string) error {
	calendar, err := s.calendarRepo.GetCalendarByID(ctx, calendarID)
	if err != nil {
		return fmt.Errorf("failed to get calendar: %w", err)
	}
	if calendar == nil {
		return errors.New("calendar not found")
	}

	// Check permissions - only owner can delete
	if calendar.OwnerID != uuid.MustParse(userID) {
		return errors.New("access denied")
	}

	// Cannot delete default calendar
	if calendar.IsDefault {
		return errors.New("cannot delete default calendar")
	}

	if err := s.calendarRepo.DeleteCalendar(ctx, calendarID); err != nil {
		return fmt.Errorf("failed to delete calendar: %w", err)
	}

	return nil
}

// Event operations
func (s *CalendarService) CreateEvent(ctx context.Context, userID string, req *CreateEventRequest) (*models.Event, error) {
	// Convert userID string to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Validate event data
	if err := s.validateEventData(req); err != nil {
		return nil, err
	}

	// Get calendar and check permissions
	calendar, err := s.calendarRepo.GetCalendarByID(ctx, req.CalendarID)
	if err != nil {
		return nil, fmt.Errorf("failed to get calendar: %w", err)
	}
	if calendar == nil {
		return nil, errors.New("calendar not found")
	}

	if !calendar.CanUserEdit(uuid.MustParse(userID)) {
		return nil, errors.New("access denied to calendar")
	}

	// Check for conflicts if requested
	if req.CheckConflicts {
		conflicts, err := s.calendarRepo.CheckConflicts(ctx, userID, req.StartTime, req.EndTime, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to check conflicts: %w", err)
		}
		if len(conflicts) > 0 {
			return nil, errors.New("event conflicts with existing events")
		}
	}

	event := &models.Event{
		CalendarID:     req.CalendarID,
		Title:          req.Title,
		Description:    req.Description,
		Location:       req.Location,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		AllDay:         req.AllDay,
		TimeZone:       req.TimeZone,
		EventType:      models.EventType(req.EventType),
		Status:         models.EventStatus(req.Status),
		Priority:       models.EventPriority(req.Priority),
		Visibility:     models.EventVisibility(req.Visibility),
		IsRecurring:    req.IsRecurring,
		RecurrenceRule: req.RecurrenceRule,
		CreatorID:      userUUID,
		OrganizerID:    userUUID,
	}

	// Set default values
	if event.TimeZone == "" {
		event.TimeZone = calendar.TimeZone
	}
	if req.EventType == "" {
		event.EventType = models.EventTypeMeeting
	} else {
		event.EventType = models.EventType(req.EventType)
	}
	if req.Status == "" {
		event.Status = models.EventStatusConfirmed
	} else {
		event.Status = models.EventStatus(req.Status)
	}
	if req.Priority == "" {
		event.Priority = models.EventPriorityNormal
	} else {
		event.Priority = models.EventPriority(req.Priority)
	}
	if req.Visibility == "" {
		event.Visibility = models.EventVisibilityPublic
	} else {
		event.Visibility = models.EventVisibility(req.Visibility)
	}

	// Prepare attendees
	if len(req.AttendeeIDs) > 0 {
		if len(req.AttendeeIDs) > s.config.Calendar.MaxAttendeesPerEvent {
			return nil, fmt.Errorf("too many attendees (max %d)", s.config.Calendar.MaxAttendeesPerEvent)
		}

		event.Attendees = make([]models.EventAttendee, len(req.AttendeeIDs))
		for i, attendeeID := range req.AttendeeIDs {
			attendeeUUID, err := uuid.Parse(attendeeID)
			if err != nil {
				return nil, fmt.Errorf("invalid attendee ID format: %w", err)
			}
			event.Attendees[i] = models.EventAttendee{
				UserID:     attendeeUUID,
				Role:       models.AttendeeRoleAttendee,
				Status:     models.AttendeeStatusPending,
				IsOptional: req.OptionalAttendees[attendeeID],
			}
		}

		// Add organizer as attendee
		event.Attendees = append(event.Attendees, models.EventAttendee{
			UserID: userUUID,
			Role:   models.AttendeeRoleOrganizer,
			Status: models.AttendeeStatusAccepted,
		})
	}

	// Prepare reminders
	if len(req.ReminderMinutes) > 0 {
		event.Reminders = make([]models.EventReminder, len(req.ReminderMinutes))
		for i, minutes := range req.ReminderMinutes {
			event.Reminders[i] = models.EventReminder{
				UserID:        userUUID,
				Type:          models.ReminderTypeInApp, // Default type
				MinutesBefore: minutes,
			}
		}
	} else {
		// Add default reminder
		event.Reminders = []models.EventReminder{
			{
				UserID:        userUUID,
				Type:          models.ReminderTypeInApp,
				MinutesBefore: s.config.Calendar.DefaultReminderTime,
			},
		}
	}

	if err := s.calendarRepo.CreateEvent(ctx, event); err != nil {
		return nil, fmt.Errorf("failed to create event: %w", err)
	}

	// Send notifications to attendees
	if len(event.Attendees) > 1 {
		go s.sendEventInvitations(event)
	}

	return event, nil
}

func (s *CalendarService) GetEvent(ctx context.Context, eventID uuid.UUID, userID string) (*models.Event, error) {
	event, err := s.calendarRepo.GetEventByID(ctx, eventID)
	if err != nil {
		return nil, fmt.Errorf("failed to get event: %w", err)
	}
	if event == nil {
		return nil, errors.New("event not found")
	}

	// Check if user can view the event
	if !event.CanUserView(uuid.MustParse(userID)) {
		return nil, errors.New("access denied")
	}

	// Populate creator and organizer info using batch populator
	events := []models.Event{*event}
	s.populateEventsBatch(ctx, events, userID)
	*event = events[0]

	// Set user's attendance status
	userUUID, _ := uuid.Parse(userID) // We already validated this earlier
	if attendance := event.GetUserAttendance(userUUID); attendance != nil {
		event.UserAttendance = attendance
	}

	// Populate attendee user info in batch
	s.populateEventAttendeesBatch(ctx, event)

	return event, nil
}

func (s *CalendarService) GetEvents(ctx context.Context, req *GetEventsRequest) ([]models.Event, error) {
	var events []models.Event
	var err error

	switch {
	case req.CalendarID != uuid.Nil:
		// Get events for specific calendar
		calendar, err := s.calendarRepo.GetCalendarByID(ctx, req.CalendarID)
		if err != nil {
			return nil, fmt.Errorf("failed to get calendar: %w", err)
		}
		if calendar == nil {
			return nil, errors.New("calendar not found")
		}
		userUUID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID format: %w", err)
		}
		if !calendar.CanUserView(userUUID) {
			return nil, errors.New("access denied")
		}
		
		events, err = s.calendarRepo.GetEventsByCalendar(ctx, req.CalendarID, req.StartTime, req.EndTime)
		
	case req.UserID != "":
		// Get events for user (all calendars)
		events, err = s.calendarRepo.GetUserEvents(ctx, req.UserID, req.StartTime, req.EndTime)
		
	default:
		return nil, errors.New("either calendar_id or user_id must be specified")
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get events: %w", err)
	}

	// Populate creator and organizer info in batch
	s.populateEventsBatch(ctx, events, req.UserID)

	return events, nil
}

func (s *CalendarService) SearchEvents(ctx context.Context, userID, query string, startTime, endTime time.Time, limit, offset int) ([]models.Event, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	events, err := s.calendarRepo.SearchEvents(ctx, userID, query, startTime, endTime, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to search events: %w", err)
	}

	// Populate additional info using batch populator
	s.populateEventsBatch(ctx, events, userID)

	return events, nil
}

func (s *CalendarService) UpdateEvent(ctx context.Context, eventID uuid.UUID, userID string, req *UpdateEventRequest) (*models.Event, error) {
	event, err := s.calendarRepo.GetEventByID(ctx, eventID)
	if err != nil {
		return nil, fmt.Errorf("failed to get event: %w", err)
	}
	if event == nil {
		return nil, errors.New("event not found")
	}

	// Check permissions
	if !event.CanUserEdit(uuid.MustParse(userID)) {
		return nil, errors.New("access denied")
	}

	// Update fields
	if req.Title != "" {
		if err := s.validateEventTitle(req.Title); err != nil {
			return nil, err
		}
		event.Title = req.Title
	}

	if req.Description != nil {
		event.Description = *req.Description
	}

	if req.Location != nil {
		event.Location = *req.Location
	}

	if !req.StartTime.IsZero() || !req.EndTime.IsZero() {
		if !req.StartTime.IsZero() {
			event.StartTime = req.StartTime
		}
		if !req.EndTime.IsZero() {
			event.EndTime = req.EndTime
		}

		// Validate time range
		if err := s.validateEventTimes(event.StartTime, event.EndTime); err != nil {
			return nil, err
		}

		// Check for conflicts if time changed
		if req.CheckConflicts {
			conflicts, err := s.calendarRepo.CheckConflicts(ctx, userID, event.StartTime, event.EndTime, &eventID)
			if err != nil {
				return nil, fmt.Errorf("failed to check conflicts: %w", err)
			}
			if len(conflicts) > 0 {
				return nil, errors.New("event conflicts with existing events")
			}
		}
	}

	if req.AllDay != nil {
		event.AllDay = *req.AllDay
	}

	if req.EventType != "" {
		event.EventType = models.EventType(req.EventType)
	}

	if req.Status != "" {
		event.Status = models.EventStatus(req.Status)
	}

	if req.Priority != "" {
		event.Priority = models.EventPriority(req.Priority)
	}

	if req.Visibility != "" {
		event.Visibility = models.EventVisibility(req.Visibility)
	}

	if err := s.calendarRepo.UpdateEvent(ctx, event); err != nil {
		return nil, fmt.Errorf("failed to update event: %w", err)
	}

	// Notify attendees of changes
	go s.sendEventUpdateNotification(event)

	return event, nil
}

func (s *CalendarService) DeleteEvent(ctx context.Context, eventID uuid.UUID, userID string) error {
	event, err := s.calendarRepo.GetEventByID(ctx, eventID)
	if err != nil {
		return fmt.Errorf("failed to get event: %w", err)
	}
	if event == nil {
		return errors.New("event not found")
	}

	// Check permissions
	if !event.CanUserEdit(uuid.MustParse(userID)) {
		return errors.New("access denied")
	}

	if err := s.calendarRepo.DeleteEvent(ctx, eventID); err != nil {
		return fmt.Errorf("failed to delete event: %w", err)
	}

	// Notify attendees of cancellation
	go s.sendEventCancellationNotification(event)

	return nil
}

// Attendee operations
func (s *CalendarService) RespondToEvent(ctx context.Context, eventID uuid.UUID, userID string, req *RespondToEventRequest) error {
	event, err := s.calendarRepo.GetEventByID(ctx, eventID)
	if err != nil {
		return fmt.Errorf("failed to get event: %w", err)
	}
	if event == nil {
		return errors.New("event not found")
	}

	// Check if user is an attendee
	attendee, err := s.calendarRepo.GetAttendee(ctx, eventID, userID)
	if err != nil {
		return fmt.Errorf("failed to get attendee: %w", err)
	}
	if attendee == nil {
		return errors.New("user is not invited to this event")
	}

	// Update attendance status
	attendee.Status = models.AttendeeStatus(req.Status)
	attendee.ResponseNote = req.Note
	now := time.Now()
	attendee.RespondedAt = &now

	if err := s.calendarRepo.UpdateAttendee(ctx, attendee); err != nil {
		return fmt.Errorf("failed to update attendee: %w", err)
	}

	// Notify organizer
	go s.sendAttendeeResponseNotification(event, attendee)

	return nil
}

// Availability operations
func (s *CalendarService) SetAvailability(ctx context.Context, userID string, req *SetAvailabilityRequest) error {
	// Parse userID string to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	for _, slot := range req.Slots {
		if err := s.validateAvailabilitySlot(&slot); err != nil {
			return err
		}

		slot.UserID = userUUID
		if err := s.calendarRepo.CreateAvailabilitySlot(ctx, &slot); err != nil {
			return fmt.Errorf("failed to create availability slot: %w", err)
		}
	}

	return nil
}

func (s *CalendarService) GetAvailability(ctx context.Context, userID string, startDate, endDate time.Time) (*AvailabilityResponse, error) {
	// Get regular availability slots
	slots, err := s.calendarRepo.GetUserAvailability(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get availability: %w", err)
	}

	// Get availability exceptions for the date range
	exceptions, err := s.calendarRepo.GetAvailabilityExceptions(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get availability exceptions: %w", err)
	}

	return &AvailabilityResponse{
		Slots:      slots,
		Exceptions: exceptions,
	}, nil
}

func (s *CalendarService) CheckAvailability(ctx context.Context, userIDs []string, startTime, endTime time.Time) (*AvailabilityCheckResponse, error) {
	result := &AvailabilityCheckResponse{
		AvailableUsers:   []string{},
		UnavailableUsers: []string{},
		Conflicts:        make(map[string][]models.Event),
	}

	for _, userID := range userIDs {
		conflicts, err := s.calendarRepo.CheckConflicts(ctx, userID, startTime, endTime, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to check conflicts for user %s: %w", userID, err)
		}

		if len(conflicts) == 0 {
			result.AvailableUsers = append(result.AvailableUsers, userID)
		} else {
			result.UnavailableUsers = append(result.UnavailableUsers, userID)
			result.Conflicts[userID] = conflicts
		}
	}

	return result, nil
}

// Helper methods
func (s *CalendarService) validateCalendarData(name, description string) error {
	if err := s.validateCalendarName(name); err != nil {
		return err
	}

	if len(description) > 500 {
		return errors.New("description is too long (max 500 characters)")
	}

	return nil
}

func (s *CalendarService) validateCalendarName(name string) error {
	name = strings.TrimSpace(name)
	if name == "" {
		return errors.New("calendar name is required")
	}

	if len(name) < 2 {
		return errors.New("calendar name must be at least 2 characters")
	}

	if len(name) > 100 {
		return errors.New("calendar name is too long (max 100 characters)")
	}

	return nil
}

func (s *CalendarService) validateEventData(req *CreateEventRequest) error {
	if err := s.validateEventTitle(req.Title); err != nil {
		return err
	}

	if err := s.validateEventTimes(req.StartTime, req.EndTime); err != nil {
		return err
	}

	if len(req.Description) > 5000 {
		return errors.New("description is too long (max 5000 characters)")
	}

	if len(req.Location) > 255 {
		return errors.New("location is too long (max 255 characters)")
	}

	return nil
}

func (s *CalendarService) validateEventTitle(title string) error {
	title = strings.TrimSpace(title)
	if title == "" {
		return errors.New("event title is required")
	}

	if len(title) < 2 {
		return errors.New("event title must be at least 2 characters")
	}

	if len(title) > 200 {
		return errors.New("event title is too long (max 200 characters)")
	}

	return nil
}

func (s *CalendarService) validateEventTimes(startTime, endTime time.Time) error {
	if startTime.IsZero() || endTime.IsZero() {
		return errors.New("start time and end time are required")
	}

	if !endTime.After(startTime) {
		return errors.New("end time must be after start time")
	}

	duration := endTime.Sub(startTime)
	if duration < s.config.Calendar.MinEventDuration {
		return fmt.Errorf("event duration too short (min %v)", s.config.Calendar.MinEventDuration)
	}

	if duration > s.config.Calendar.MaxEventDuration {
		return fmt.Errorf("event duration too long (max %v)", s.config.Calendar.MaxEventDuration)
	}

	return nil
}

func (s *CalendarService) validateAvailabilitySlot(slot *models.AvailabilitySlot) error {
	if slot.DayOfWeek < 0 || slot.DayOfWeek > 6 {
		return errors.New("invalid day of week (0=Sunday, 6=Saturday)")
	}

	// Validate time format (HH:MM)
	if !s.isValidTimeFormat(slot.StartTime) || !s.isValidTimeFormat(slot.EndTime) {
		return errors.New("invalid time format (use HH:MM)")
	}

	return nil
}

func (s *CalendarService) isValidTimeFormat(timeStr string) bool {
	// Simple validation for HH:MM format
	if len(timeStr) != 5 || timeStr[2] != ':' {
		return false
	}
	return true
}


// populateCalendarsBatch efficiently populates owner data for multiple calendars
func (s *CalendarService) populateCalendarsBatch(ctx context.Context, calendars []models.Calendar) {
	if len(calendars) == 0 {
		return
	}

	// Extract owner IDs
	var ownerIDs []string
	for _, calendar := range calendars {
		ownerIDs = append(ownerIDs, calendar.OwnerID.String())
	}

	// Fetch users in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, ownerIDs)
	if err != nil {
		return
	}

	// Populate calendars with owner data and stats
	for i := range calendars {
		if owner, exists := userMap[calendars[i].OwnerID.String()]; exists {
			calendars[i].Owner = owner
		}
		
		// Get calendar stats (this could also be batched if needed)
		if stats, err := s.calendarRepo.GetCalendarStats(ctx, calendars[i].ID); err == nil {
			calendars[i].EventCount = stats.TotalEvents
		}
	}
}

// populateEventsBatch efficiently populates user data for multiple events
func (s *CalendarService) populateEventsBatch(ctx context.Context, events []models.Event, userID string) {
	if len(events) == 0 {
		return
	}

	// Extract creator and organizer IDs
	var userIDs []string
	for _, event := range events {
		userIDs = append(userIDs, event.CreatorID.String(), event.OrganizerID.String())
	}

	// Fetch users in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, userIDs)
	if err != nil {
		return
	}

	// Populate events with user data
	for i := range events {
		if creator, exists := userMap[events[i].CreatorID.String()]; exists {
			events[i].Creator = creator
		}
		
		if organizer, exists := userMap[events[i].OrganizerID.String()]; exists {
			events[i].Organizer = organizer
		}

		// Set user's attendance status if applicable
		if userID != "" {
			if userUUID, err := uuid.Parse(userID); err == nil {
				if attendance := events[i].GetUserAttendance(userUUID); attendance != nil {
					events[i].UserAttendance = attendance
				}
			}
		}
	}
}

// populateEventAttendeesBatch efficiently populates user data for event attendees
func (s *CalendarService) populateEventAttendeesBatch(ctx context.Context, event *models.Event) {
	if len(event.Attendees) == 0 {
		return
	}

	// Extract attendee user IDs
	var userIDs []string
	for _, attendee := range event.Attendees {
		userIDs = append(userIDs, attendee.UserID.String())
	}

	// Fetch users in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, userIDs)
	if err != nil {
		return
	}

	// Populate attendees with user data
	for i := range event.Attendees {
		if user, exists := userMap[event.Attendees[i].UserID.String()]; exists {
			event.Attendees[i].User = user
		}
	}
}

func (s *CalendarService) sendEventInvitations(event *models.Event) {
	// TODO: Send invitation notifications to attendees
}

func (s *CalendarService) sendEventUpdateNotification(event *models.Event) {
	// TODO: Send update notifications to attendees
}

func (s *CalendarService) sendEventCancellationNotification(event *models.Event) {
	// TODO: Send cancellation notifications to attendees
}

func (s *CalendarService) sendAttendeeResponseNotification(event *models.Event, attendee *models.EventAttendee) {
	// TODO: Send attendee response notification to organizer
}

// Service Request/Response types
type CreateCalendarRequest struct {
	Name        string
	Description string
	Color       string
	TeamID      string
	Visibility  string
	IsDefault   bool
	TimeZone    string
}

type UpdateCalendarRequest struct {
	Name        string
	Description *string
	Color       string
	Visibility  string
	IsDefault   *bool
	TimeZone    string
}

type CreateEventRequest struct {
	CalendarID         uuid.UUID
	Title              string
	Description        string
	Location           string
	StartTime          time.Time
	EndTime            time.Time
	AllDay             bool
	TimeZone           string
	EventType          string
	Status             string
	Priority           string
	Visibility         string
	IsRecurring        bool
	RecurrenceRule     string
	AttendeeIDs        []string
	OptionalAttendees  map[string]bool
	ReminderMinutes    []int
	CheckConflicts     bool
}

type UpdateEventRequest struct {
	Title          string
	Description    *string
	Location       *string
	StartTime      time.Time
	EndTime        time.Time
	AllDay         *bool
	EventType      string
	Status         string
	Priority       string
	Visibility     string
	CheckConflicts bool
}

type GetEventsRequest struct {
	UserID     string
	CalendarID uuid.UUID
	StartTime  time.Time
	EndTime    time.Time
}

type RespondToEventRequest struct {
	Status string
	Note   string
}

type SetAvailabilityRequest struct {
	Slots []models.AvailabilitySlot
}

type AvailabilityResponse struct {
	Slots      []models.AvailabilitySlot
	Exceptions []models.AvailabilityException
}

type AvailabilityCheckResponse struct {
	AvailableUsers   []string
	UnavailableUsers []string
	Conflicts        map[string][]models.Event
}