package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/clients"
	sharedModels "github.com/swork-team/platform/pkg/models"
	"gorm.io/gorm"
)

type Calendar struct {
	ID          uuid.UUID         `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	Name        string            `json:"name" gorm:"not null;size:100"`
	Description string            `json:"description" gorm:"size:500"`
	Color       string            `json:"color" gorm:"size:7;default:'#3498db'"` // Hex color
	
	// Ownership
	OwnerID     uuid.UUID         `json:"owner_id" gorm:"type:uuid;not null;index"`
	OwnerType   CalendarOwnerType `json:"owner_type" gorm:"not null"` // user or team
	TeamID      *uuid.UUID        `json:"team_id,omitempty" gorm:"type:uuid;index"`
	
	// Settings
	Visibility  CalendarVisibility `json:"visibility" gorm:"default:'private'"`
	IsDefault   bool              `json:"is_default" gorm:"default:false"`
	IsActive    bool              `json:"is_active" gorm:"default:true"`
	TimeZone    string            `json:"timezone" gorm:"default:'UTC'"`
	
	// Timestamps
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	DeletedAt   gorm.DeletedAt    `json:"-" gorm:"index"`
	
	// Relationships
	Events      []Event           `json:"events,omitempty" gorm:"foreignKey:CalendarID"`
	Shares      []CalendarShare   `json:"shares,omitempty" gorm:"foreignKey:CalendarID"`
	
	// Virtual fields
	Owner       *clients.User     `json:"owner,omitempty" gorm:"-"`
	Team        *clients.Team     `json:"team,omitempty" gorm:"-"`
	EventCount  int64             `json:"event_count,omitempty" gorm:"-"`
}

type Event struct {
	ID          uuid.UUID     `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	CalendarID  uuid.UUID     `json:"calendar_id" gorm:"type:uuid;not null;index"`
	Title       string        `json:"title" gorm:"not null;size:200"`
	Description string        `json:"description" gorm:"type:text"`
	Location    string        `json:"location" gorm:"size:255"`
	
	// Time and duration
	StartTime   time.Time     `json:"start_time" gorm:"not null;index"`
	EndTime     time.Time     `json:"end_time" gorm:"not null;index"`
	AllDay      bool          `json:"all_day" gorm:"default:false"`
	TimeZone    string        `json:"timezone" gorm:"default:'UTC'"`
	
	// Event properties
	EventType   EventType     `json:"event_type" gorm:"default:'meeting'"`
	Status      EventStatus   `json:"status" gorm:"default:'confirmed'"`
	Priority    EventPriority `json:"priority" gorm:"default:'normal'"`
	Visibility  EventVisibility `json:"visibility" gorm:"default:'public'"`
	
	// Recurrence
	IsRecurring bool          `json:"is_recurring" gorm:"default:false"`
	RecurrenceRule string     `json:"recurrence_rule,omitempty"` // RRULE format
	RecurrenceID *uuid.UUID   `json:"recurrence_id,omitempty" gorm:"type:uuid;index"`
	
	// Creator and metadata
	CreatorID   uuid.UUID     `json:"creator_id" gorm:"type:uuid;not null;index"`
	OrganizerID uuid.UUID     `json:"organizer_id" gorm:"type:uuid;not null;index"`
	
	// External integration
	ExternalID  string        `json:"external_id,omitempty" gorm:"index"`
	ExternalURL string        `json:"external_url,omitempty"`
	
	// Timestamps
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relationships
	Calendar    Calendar      `json:"calendar" gorm:"foreignKey:CalendarID"`
	Attendees   []EventAttendee `json:"attendees,omitempty" gorm:"foreignKey:EventID"`
	Reminders   []EventReminder `json:"reminders,omitempty" gorm:"foreignKey:EventID"`
	Attachments []EventAttachment `json:"attachments,omitempty" gorm:"foreignKey:EventID"`
	
	// Virtual fields
	Creator     *clients.User `json:"creator,omitempty" gorm:"-"`
	Organizer   *clients.User `json:"organizer,omitempty" gorm:"-"`
	UserAttendance *EventAttendee `json:"user_attendance,omitempty" gorm:"-"`
}

type EventAttendee struct {
	ID          uuid.UUID         `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	EventID     uuid.UUID         `json:"event_id" gorm:"type:uuid;not null;index"`
	UserID      uuid.UUID         `json:"user_id" gorm:"type:uuid;not null;index"`
	Email       string            `json:"email,omitempty" gorm:"index"`
	Role        AttendeeRole      `json:"role" gorm:"default:'attendee'"`
	Status      AttendeeStatus    `json:"status" gorm:"default:'pending'"`
	IsOptional  bool              `json:"is_optional" gorm:"default:false"`
	ResponseNote string           `json:"response_note" gorm:"size:500"`
	RespondedAt *time.Time        `json:"responded_at,omitempty"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	
	// Relationships
	Event       Event             `json:"event" gorm:"foreignKey:EventID"`
	
	// Virtual fields
	User        *clients.User     `json:"user,omitempty" gorm:"-"`
}

type EventReminder struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	EventID     uuid.UUID      `json:"event_id" gorm:"type:uuid;not null;index"`
	UserID      uuid.UUID      `json:"user_id" gorm:"type:uuid;not null;index"`
	Type        ReminderType   `json:"type" gorm:"not null"`
	MinutesBefore int          `json:"minutes_before" gorm:"not null"`
	IsSent      bool           `json:"is_sent" gorm:"default:false"`
	SentAt      *time.Time     `json:"sent_at,omitempty"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	
	// Relationships
	Event       Event          `json:"event" gorm:"foreignKey:EventID"`
}

type EventAttachment struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	EventID     uuid.UUID `json:"event_id" gorm:"type:uuid;not null;index"`
	AttachmentID string   `json:"attachment_id" gorm:"not null;size:255"` // Reference to shared attachment
	UploadedBy  uuid.UUID `json:"uploaded_by" gorm:"type:uuid;not null"`
	CreatedAt   time.Time `json:"created_at"`
	
	// Relationships
	Event       Event     `json:"event" gorm:"foreignKey:EventID"`
	
	// Virtual field
	Attachment  *sharedModels.AttachmentBasic `json:"attachment,omitempty" gorm:"-"`
}

type CalendarShare struct {
	ID          uuid.UUID           `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	CalendarID  uuid.UUID           `json:"calendar_id" gorm:"type:uuid;not null;index"`
	SharedBy    uuid.UUID           `json:"shared_by" gorm:"type:uuid;not null"`
	SharedWith  uuid.UUID           `json:"shared_with" gorm:"type:uuid;not null;index"`
	Permission  CalendarPermission  `json:"permission" gorm:"not null"`
	CanEdit     bool                `json:"can_edit" gorm:"default:false"`
	CanDelete   bool                `json:"can_delete" gorm:"default:false"`
	ExpiresAt   *time.Time          `json:"expires_at,omitempty"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
	
	// Relationships
	Calendar    Calendar            `json:"calendar" gorm:"foreignKey:CalendarID"`
	
	// Virtual fields
	SharedByUser   *clients.User    `json:"shared_by_user,omitempty" gorm:"-"`
	SharedWithUser *clients.User    `json:"shared_with_user,omitempty" gorm:"-"`
}

type AvailabilitySlot struct {
	ID          uuid.UUID         `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	UserID      uuid.UUID         `json:"user_id" gorm:"type:uuid;not null;index"`
	DayOfWeek   int               `json:"day_of_week" gorm:"not null"` // 0=Sunday, 1=Monday, etc.
	StartTime   string            `json:"start_time" gorm:"not null"`  // HH:MM format
	EndTime     string            `json:"end_time" gorm:"not null"`    // HH:MM format
	TimeZone    string            `json:"timezone" gorm:"default:'UTC'"`
	IsAvailable bool              `json:"is_available" gorm:"default:true"`
	SlotType    AvailabilityType  `json:"slot_type" gorm:"default:'work'"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

type AvailabilityException struct {
	ID          uuid.UUID         `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	UserID      uuid.UUID         `json:"user_id" gorm:"type:uuid;not null;index"`
	Date        time.Time         `json:"date" gorm:"not null;index"`
	StartTime   *string           `json:"start_time,omitempty"` // HH:MM format, null for all-day
	EndTime     *string           `json:"end_time,omitempty"`   // HH:MM format, null for all-day
	IsAvailable bool              `json:"is_available" gorm:"default:false"`
	Reason      string            `json:"reason" gorm:"size:255"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}


// Enums
type CalendarOwnerType string

const (
	CalendarOwnerTypeUser CalendarOwnerType = "user"
	CalendarOwnerTypeTeam CalendarOwnerType = "team"
)

type CalendarVisibility string

const (
	CalendarVisibilityPrivate CalendarVisibility = "private"
	CalendarVisibilityPublic  CalendarVisibility = "public"
	CalendarVisibilityTeam    CalendarVisibility = "team"
)

type EventType string

const (
	EventTypeMeeting     EventType = "meeting"
	EventTypeTask        EventType = "task"
	EventTypeReminder    EventType = "reminder"
	EventTypeDeadline    EventType = "deadline"
	EventTypeAppointment EventType = "appointment"
	EventTypeEvent       EventType = "event"
	EventTypeHoliday     EventType = "holiday"
)

type EventStatus string

const (
	EventStatusConfirmed EventStatus = "confirmed"
	EventStatusTentative EventStatus = "tentative"
	EventStatusCancelled EventStatus = "cancelled"
)

type EventPriority string

const (
	EventPriorityLow    EventPriority = "low"
	EventPriorityNormal EventPriority = "normal"
	EventPriorityHigh   EventPriority = "high"
	EventPriorityUrgent EventPriority = "urgent"
)

type EventVisibility string

const (
	EventVisibilityPublic  EventVisibility = "public"
	EventVisibilityPrivate EventVisibility = "private"
	EventVisibilityTeam    EventVisibility = "team"
)

type AttendeeRole string

const (
	AttendeeRoleOrganizer AttendeeRole = "organizer"
	AttendeeRoleRequired  AttendeeRole = "required"
	AttendeeRoleOptional  AttendeeRole = "optional"
	AttendeeRoleAttendee  AttendeeRole = "attendee"
)

type AttendeeStatus string

const (
	AttendeeStatusPending  AttendeeStatus = "pending"
	AttendeeStatusAccepted AttendeeStatus = "accepted"
	AttendeeStatusDeclined AttendeeStatus = "declined"
	AttendeeStatusTentative AttendeeStatus = "tentative"
)

type ReminderType string

const (
	ReminderTypeEmail        ReminderType = "email"
	ReminderTypePush         ReminderType = "push"
	ReminderTypeInApp        ReminderType = "in_app"
	ReminderTypeDesktop      ReminderType = "desktop"
)

type CalendarPermission string

const (
	CalendarPermissionRead     CalendarPermission = "read"
	CalendarPermissionWrite    CalendarPermission = "write"
	CalendarPermissionAdmin    CalendarPermission = "admin"
)

type AvailabilityType string

const (
	AvailabilityTypeWork    AvailabilityType = "work"
	AvailabilityTypePersonal AvailabilityType = "personal"
	AvailabilityTypeBreak   AvailabilityType = "break"
	AvailabilityTypeUnavailable AvailabilityType = "unavailable"
)

// Helper methods
func (c *Calendar) CanUserView(userID uuid.UUID) bool {
	// Owner can always view
	if c.OwnerID == userID {
		return true
	}
	
	// Public calendars can be viewed by anyone
	if c.Visibility == CalendarVisibilityPublic {
		return true
	}
	
	// Check explicit shares
	for _, share := range c.Shares {
		if share.SharedWith == userID && (share.ExpiresAt == nil || share.ExpiresAt.After(time.Now())) {
			return true
		}
	}
	
	return false
}

func (c *Calendar) CanUserEdit(userID uuid.UUID) bool {
	// Owner can always edit
	if c.OwnerID == userID {
		return true
	}
	
	// Check explicit shares with edit permission
	for _, share := range c.Shares {
		if share.SharedWith == userID && 
		   share.CanEdit &&
		   (share.ExpiresAt == nil || share.ExpiresAt.After(time.Now())) {
			return true
		}
	}
	
	return false
}

func (c *Calendar) GetUserPermission(userID uuid.UUID) *CalendarPermission {
	// Owner has admin permission
	if c.OwnerID == userID {
		perm := CalendarPermissionAdmin
		return &perm
	}
	
	// Check explicit shares
	for _, share := range c.Shares {
		if share.SharedWith == userID && (share.ExpiresAt == nil || share.ExpiresAt.After(time.Now())) {
			return &share.Permission
		}
	}
	
	// Public calendars have read permission
	if c.Visibility == CalendarVisibilityPublic {
		perm := CalendarPermissionRead
		return &perm
	}
	
	return nil
}

func (e *Event) CanUserView(userID uuid.UUID) bool {
	// Creator and organizer can always view
	if e.CreatorID == userID || e.OrganizerID == userID {
		return true
	}
	
	// Public events can be viewed by anyone
	if e.Visibility == EventVisibilityPublic {
		return true
	}
	
	// Check if user is an attendee
	for _, attendee := range e.Attendees {
		if attendee.UserID == userID {
			return true
		}
	}
	
	return false
}

func (e *Event) CanUserEdit(userID uuid.UUID) bool {
	// Creator and organizer can edit
	return e.CreatorID == userID || e.OrganizerID == userID
}

func (e *Event) GetUserAttendance(userID uuid.UUID) *EventAttendee {
	for _, attendee := range e.Attendees {
		if attendee.UserID == userID {
			return &attendee
		}
	}
	return nil
}

func (e *Event) IsUserAttending(userID uuid.UUID) bool {
	attendance := e.GetUserAttendance(userID)
	return attendance != nil && attendance.Status == AttendeeStatusAccepted
}

func (e *Event) GetDuration() time.Duration {
	return e.EndTime.Sub(e.StartTime)
}

func (e *Event) IsUpcoming() bool {
	return e.StartTime.After(time.Now())
}

func (e *Event) IsOngoing() bool {
	now := time.Now()
	return e.StartTime.Before(now) && e.EndTime.After(now)
}

func (e *Event) IsPast() bool {
	return e.EndTime.Before(time.Now())
}

func (ea *EventAttendee) CanRespond() bool {
	return ea.Status == AttendeeStatusPending
}

func (ea *EventAttendee) HasResponded() bool {
	return ea.RespondedAt != nil
}

// Custom table names
func (Calendar) TableName() string {
	return "calendars"
}

func (Event) TableName() string {
	return "events"
}

func (EventAttendee) TableName() string {
	return "event_attendees"
}

func (EventReminder) TableName() string {
	return "event_reminders"
}

func (EventAttachment) TableName() string {
	return "event_attachments"
}

func (CalendarShare) TableName() string {
	return "calendar_shares"
}

func (AvailabilitySlot) TableName() string {
	return "availability_slots"
}

func (AvailabilityException) TableName() string {
	return "availability_exceptions"
}