package main

import (
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	"github.com/swork-team/platform/services/calendar/internal/config"
	"github.com/swork-team/platform/services/calendar/internal/handlers"
	"github.com/swork-team/platform/services/calendar/internal/models"
	"github.com/swork-team/platform/services/calendar/internal/repositories"
	"github.com/swork-team/platform/services/calendar/internal/services"
)

func main() {
	cfg := config.LoadConfig()

	// Initialize standardized logger
	loggerManager := logger.NewServiceLoggerManager("calendar")
	serviceLogger := loggerManager.GetLogger()

	// Log service startup
	serviceLogger.Info("Calendar service starting up",
		logger.F("environment", os.Getenv("APP_ENV")),
		logger.F("port", cfg.Server.Port),
		logger.F("enable_notifications", cfg.Calendar.EnableNotifications),
		logger.F("reminder_check_interval", cfg.Calendar.ReminderCheckInterval),
	)

	// Create config adapter for bootstrap
	configAdapter := server.NewConfigAdapter(cfg.Server, cfg.Database, cfg.Redis)

	// Define models to migrate
	models := []interface{}{
		&models.Calendar{},
		&models.Event{},
		&models.EventAttendee{},
		&models.EventReminder{},
		&models.EventAttachment{},
		&models.CalendarShare{},
		&models.AvailabilitySlot{},
		&models.AvailabilityException{},
	}

	// Setup router function
	routerSetup := func(components *server.ServiceComponents) *gin.Engine {
		calendarRepo := repositories.NewCalendarRepository(components.Database)
		calendarService := services.NewCalendarService(calendarRepo, cfg, components.RedisClient)
		calendarHandler := handlers.NewCalendarHandler(calendarService)

		// Start reminder check routine
		go startReminderCheckRoutine(calendarService, cfg, serviceLogger)

		return setupRouter(calendarHandler, cfg, serviceLogger)
	}

	// Cleanup function
	cleanupFunc := func() error {
		serviceLogger.Info("Calendar service shutting down")
		return nil
	}

	// Bootstrap the service
	server.BootstrapService(server.ServiceOptions{
		ServiceName:        "calendar",
		Config:             configAdapter,
		Models:             models,
		RouterSetup:        routerSetup,
		EnableExtensions:   false,
		ServiceCleanupFunc: cleanupFunc,
		DatabaseType:       server.PostgreSQL,
	})
}

func setupRouter(calendarHandler *handlers.CalendarHandler, cfg *config.Config, serviceLogger logger.ServiceLogger) *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// Use standardized logging middleware
	router.Use(logger.RequestLoggingMiddleware(serviceLogger))
	router.Use(logger.UserContextMiddleware())
	router.Use(logger.ErrorLoggingMiddleware())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// Health check endpoint
	router.GET("/health", calendarHandler.HealthCheck)

	// Calendar API routes
	api := router.Group("/calendar")
	api.Use(middleware.AuthMiddleware())
	{
		// Calendar management
		api.POST("/calendars", calendarHandler.CreateCalendar)
		api.GET("/calendars", calendarHandler.GetUserCalendars)
		api.GET("/calendars/:id", calendarHandler.GetCalendar)
		api.PUT("/calendars/:id", calendarHandler.UpdateCalendar)
		api.DELETE("/calendars/:id", calendarHandler.DeleteCalendar)

		// Event management
		api.POST("/events", calendarHandler.CreateEvent)
		api.GET("/events", calendarHandler.GetEvents)
		api.GET("/events/search", calendarHandler.SearchEvents)
		api.GET("/events/:id", calendarHandler.GetEvent)
		api.PUT("/events/:id", calendarHandler.UpdateEvent)
		api.DELETE("/events/:id", calendarHandler.DeleteEvent)
		api.POST("/events/:id/respond", calendarHandler.RespondToEvent)

		// Availability management
		api.POST("/availability", calendarHandler.SetAvailability)
		api.GET("/availability", calendarHandler.GetAvailability)
		api.GET("/availability/check", calendarHandler.CheckAvailability)
	}

	return router
}

func startReminderCheckRoutine(calendarService *services.CalendarService, cfg *config.Config, serviceLogger logger.ServiceLogger) {
	if !cfg.Calendar.EnableNotifications {
		serviceLogger.Info("Reminder notifications are disabled")
		return
	}

	ticker := time.NewTicker(cfg.Calendar.ReminderCheckInterval)
	defer ticker.Stop()

	serviceLogger.Info("Starting reminder check routine",
		logger.F("interval", cfg.Calendar.ReminderCheckInterval),
	)

	for {
		select {
		case <-ticker.C:
			// TODO: Implement reminder checking and sending
			// This would:
			// 1. Get pending reminders that should be sent
			// 2. Send notifications via notification service
			// 3. Mark reminders as sent
			serviceLogger.Debug("Checking for pending reminders")
		}
	}
}