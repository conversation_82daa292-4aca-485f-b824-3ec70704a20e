package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/config"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/container"
)

func main() {
	// Load configuration
	cfg, err := config.LoadDriveConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Set Gin mode based on environment
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize container with dependency injection
	app, err := container.NewContainer(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize application container: %v", err)
	}
	defer app.Close()

	// Start background services
	ctx := context.Background()

	// Start thumbnail processing workers
	if app.ThumbnailService != nil {
		if err := app.ThumbnailService.Start(ctx); err != nil {
			log.Fatalf("Failed to start thumbnail service: %v", err)
		}
		log.Printf("Thumbnail processing workers started with %d workers", cfg.Thumbnail.WorkerCount)
	}

	// Create Gin engine
	engine := gin.New()

	// Setup routes - single unified router with all functionality
	app.Router.SetupRoutes(engine)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      engine,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting Drive service on %s", server.Addr)
		log.Printf("Environment: %s", cfg.Server.Environment)
		log.Printf("Debug mode: %v", cfg.Server.Debug)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Start monitoring server if enabled
	if cfg.Monitoring.Enabled {
		go startMonitoringServer(cfg, app)
	}

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	} else {
		log.Println("Server gracefully stopped")
	}
}

func startMonitoringServer(cfg *config.DriveConfig, app *container.Container) {
	// Create a simple monitoring server for health checks and metrics
	monitoringEngine := gin.New()

	// Health check endpoint
	monitoringEngine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0",
			"service":   "drive",
		})
	})

	// Readiness check endpoint for K8s readiness probes
	monitoringEngine.GET("/ready", func(c *gin.Context) {
		status := "ready"
		httpStatus := 200
		checks := gin.H{}

		// Check database connectivity and migration status
		if app.DB != nil {
			sqlDB, err := app.DB.DB()
			if err != nil {
				checks["database"] = "error: failed to get underlying DB"
				status = "not_ready"
				httpStatus = 503
			} else if err := sqlDB.Ping(); err != nil {
				checks["database"] = "error: database ping failed"
				status = "not_ready"
				httpStatus = 503
			} else {
				// Check if tables exist (migration completed)
				var tableCount int64
				err := app.DB.Raw(`
					SELECT COUNT(*) FROM information_schema.tables 
					WHERE table_schema = 'public' 
					AND table_name IN ('files', 'folders', 'upload_sessions', 'audit_logs', 'file_shares', 'folder_shares')
				`).Scan(&tableCount).Error

				if err != nil || tableCount != 6 {
					checks["database"] = "error: schema migration not complete"
					status = "not_ready"
					httpStatus = 503
				} else {
					checks["database"] = "ok"
				}
			}
		} else {
			checks["database"] = "error: database not initialized"
			status = "not_ready"
			httpStatus = 503
		}

		// Check storage health
		if app.StoragePort != nil {
			if err := app.StoragePort.Health(c.Request.Context()); err != nil {
				checks["storage"] = "error: " + err.Error()
				status = "not_ready"
				httpStatus = 503
			} else {
				checks["storage"] = "ok"
			}
		} else {
			checks["storage"] = "error: storage not initialized"
			status = "not_ready"
			httpStatus = 503
		}

		c.JSON(httpStatus, gin.H{
			"status":    status,
			"checks":    checks,
			"timestamp": time.Now().UTC(),
		})
	})

	// Metrics endpoint (placeholder for Prometheus metrics)
	monitoringEngine.GET("/metrics", func(c *gin.Context) {
		c.String(200, "# HELP drive_requests_total Total number of requests\n# TYPE drive_requests_total counter\ndrive_requests_total 0\n")
	})

	monitoringAddr := fmt.Sprintf(":%d", cfg.Monitoring.HealthCheckPort)
	log.Printf("Starting monitoring server on %s", monitoringAddr)

	if err := http.ListenAndServe(monitoringAddr, monitoringEngine); err != nil {
		log.Printf("Monitoring server error: %v", err)
	}
}
