# Drive Service

A modern, scalable file storage service built using **Hexagonal Architecture** and **Domain-Driven Design** principles in Go.

## Architecture Overview

This service follows hexagonal architecture (ports and adapters pattern) with clear separation of concerns:

```
┌─────────────────────────────────────────┐
│                HTTP Layer               │
│  Handlers │ Middleware │ Routes         │
└─────────────┬───────────────────────────┘
              │
┌─────────────▼───────────────────────────┐
│           Application Layer             │
│  Commands │ Queries │ Use Cases         │
└─────────────┬───────────────────────────┘
              │
┌─────────────▼───────────────────────────┐
│             Domain Layer                │
│  Entities │ Services │ Repositories     │
└─────────────┬───────────────────────────┘
              │
┌─────────────▼───────────────────────────┐
│        Infrastructure Layer            │
│  Database │ Storage │ External APIs     │
└─────────────────────────────────────────┘
```

## Features

### Core Functionality
- ✅ File upload with chunked upload support
- ✅ File download with presigned URLs
- ✅ File management (CRUD operations)
- ✅ Folder organization and management
- ✅ File sharing with granular permissions
- ✅ File search with advanced filtering
- ✅ Multi-provider storage (Backblaze B2, MinIO)
- ✅ Comprehensive audit logging
- ✅ Thumbnail generation for images and videos

### Enterprise Features
- ✅ Multi-provider storage with automatic failover
- ✅ Comprehensive audit trails with integrity validation
- ✅ Geographic location tracking
- ✅ Device fingerprinting
- ✅ Configurable retention policies

### Security
- ✅ JWT-based authentication
- ✅ Role-based access control
- ✅ File type validation
- ✅ Path traversal protection
- ✅ Rate limiting
- ✅ Audit logging with integrity checksums

### Scalability
- ✅ Horizontal scaling support
- ✅ Background job processing
- ✅ Connection pooling
- ✅ Async audit logging
- ✅ Health monitoring

## Project Structure

```
services/drive/
├── cmd/
│   └── main.go                    # Application entry point
├── internal/
│   ├── domain/                    # Domain layer (business logic)
│   │   ├── file/
│   │   │   ├── entities/          # File and folder entities
│   │   │   ├── repositories/      # Repository interfaces
│   │   │   ├── services/          # Domain services
│   │   │   └── valueobjects/      # Value objects
│   │   ├── sharing/               # Sharing domain
│   │   ├── upload/                # Upload session domain
│   │   └── audit/                 # Audit domain
│   ├── application/               # Application layer (use cases)
│   │   ├── file/
│   │   │   ├── commands/          # Command handlers
│   │   │   └── queries/           # Query handlers
│   │   ├── sharing/
│   │   └── ports/                 # Port interfaces
│   └── infrastructure/            # Infrastructure layer
│       ├── http/                  # HTTP handlers and routes
│       ├── persistence/           # Database implementations
│       ├── storage/               # Storage providers
│       ├── audit/                 # Audit adapters
│       ├── config/                # Configuration
│       └── container/             # Dependency injection
├── go.mod
└── README.md
```

## Domain Model

### Bounded Contexts

1. **File Management** - Core file and folder operations
2. **Sharing** - File and folder sharing with permissions
3. **Upload Sessions** - Chunked upload management
4. **Audit** - Comprehensive audit logging

### Key Entities

- **File**: Core file entity with metadata, versioning, and access control
- **Folder**: Hierarchical folder structure
- **FileShare/FolderShare**: Granular sharing permissions
- **UploadSession**: Chunked upload session management
- **AuditLog**: Comprehensive audit events

## Configuration

The service is configured via environment variables:

### Database
```bash
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_DATABASE=drive
DB_SSL_MODE=disable
```

### Storage (Primary - Backblaze B2)
```bash
STORAGE_PRIMARY_TYPE=backblaze
STORAGE_PRIMARY_ACCESS_KEY=your_key_id
STORAGE_PRIMARY_SECRET_KEY=your_secret_key
STORAGE_PRIMARY_BUCKET=your_bucket
```

### Storage (Replica - MinIO)
```bash
STORAGE_REPLICA_TYPE=minio
STORAGE_REPLICA_ENDPOINT=http://localhost:9000
STORAGE_REPLICA_ACCESS_KEY=minio_key
STORAGE_REPLICA_SECRET_KEY=minio_secret
STORAGE_REPLICA_BUCKET=replica_bucket
```

### Security
```bash
JWT_SECRET=your_jwt_secret
RATE_LIMIT_ENABLED=true
RATE_LIMIT_RPS=100
```

### Audit
```bash
AUDIT_ENABLED=true
AUDIT_BATCH_SIZE=100
AUDIT_ENABLE_ENRICHMENT=true
AUDIT_RETENTION_DAYS=365
```

## API Endpoints

### File Operations
```
POST   /api/v1/drive/upload           # Upload file
GET    /api/v1/drive/files            # List files
GET    /api/v1/drive/files/:id        # Get file
DELETE /api/v1/drive/files/:id        # Delete file
GET    /api/v1/drive/files/:id/download # Get download URL
GET    /api/v1/drive/search           # Search files
```

### Chunked Upload
```
POST   /api/v1/drive/upload/session            # Create upload session
POST   /api/v1/drive/upload/session/:id/chunk  # Upload chunk
POST   /api/v1/drive/upload/session/:id/complete # Complete upload
GET    /api/v1/drive/upload/session/:id        # Get session status
DELETE /api/v1/drive/upload/session/:id        # Cancel session
```

### Sharing
```
POST   /api/v1/drive/files/:id/share        # Share file
DELETE /api/v1/drive/files/:id/unshare/:userId # Unshare file
GET    /api/v1/drive/shared                 # Get shared files
```

### Folders
```
POST   /api/v1/drive/folders          # Create folder
GET    /api/v1/drive/folders/:id      # Get folder contents
PUT    /api/v1/drive/folders/:id      # Update folder
DELETE /api/v1/drive/folders/:id      # Delete folder
```


### Admin - System
```
GET    /api/v1/admin/health           # System health
GET    /api/v1/admin/audit            # Get audit logs
```

## Running the Service

### Prerequisites
- Go 1.21+
- PostgreSQL 13+
- Backblaze B2 account (for primary storage)
- MinIO (optional, for replica storage)

### Setup

1. **Clone and build**:
```bash
cd services/drive
go mod tidy
go build -o bin/drive cmd/main.go
```

2. **Set environment variables**:
```bash
export DB_PASSWORD=your_db_password
export STORAGE_PRIMARY_ACCESS_KEY=your_b2_key_id
export STORAGE_PRIMARY_SECRET_KEY=your_b2_secret_key
export STORAGE_PRIMARY_BUCKET=your_b2_bucket
export JWT_SECRET=your_jwt_secret
```

3. **Run database migrations** (implement separately):
```bash
# TODO: Implement database migrations
```

4. **Start the service**:
```bash
./bin/drive
```

### Docker

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o drive cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/drive .
CMD ["./drive"]
```

## Development

### Testing
```bash
go test ./...
```

### Linting
```bash
golangci-lint run
```

### Database Migrations
Database migrations should be implemented using a migration tool like `golang-migrate` or similar.

## Monitoring

The service exposes monitoring endpoints:

- `GET /health` - Health check
- `GET /ready` - Readiness check  
- `GET /metrics` - Prometheus metrics

## Security Considerations

1. **Authentication**: JWT tokens with configurable expiration
2. **Authorization**: Role-based access control for files and folders
3. **Input Validation**: File type, size, and name validation
4. **Path Security**: Protection against path traversal attacks
5. **Audit**: Comprehensive logging with integrity validation
6. **Storage**: Encrypted storage with provider failover

## Performance Features

1. **Caching**: File metadata and permission caching
2. **Async Processing**: Background job processing for heavy operations
3. **Connection Pooling**: Database connection management
4. **Batch Operations**: Efficient bulk operations for audit logs

## Scalability

The service is designed to scale horizontally:

- Stateless application servers
- Database connection pooling
- Background job processing
- External storage providers
- Audit log batching

## Contributing

1. Follow the existing architecture patterns
2. Maintain clear separation between layers
3. Write comprehensive tests
4. Update documentation
5. Follow Go best practices

## License

[Add your license here]