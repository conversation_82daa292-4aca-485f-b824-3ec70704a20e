package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/utils"
	uploadentities "github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
)

type GetUploadSessionQuery struct {
	SessionID uuid.UUID
	UserID    uuid.UUID
}

type GetUploadSessionResult struct {
	Session *uploadentities.UploadSession
}

type GetUploadSessionHandler interface {
	Handle(ctx context.Context, query *GetUploadSessionQuery) (*GetUploadSessionResult, error)
}

type getUploadSessionHandler struct {
	uploadRepo uploadrepositories.UploadRepository
}

func NewGetUploadSessionHandler(uploadRepo uploadrepositories.UploadRepository) GetUploadSessionHandler {
	return &getUploadSessionHandler{
		uploadRepo: uploadRepo,
	}
}

func (h *getUploadSessionHandler) Handle(ctx context.Context, query *GetUploadSessionQuery) (*GetUploadSessionResult, error) {
	sessionID := uploadentities.UploadSessionID(query.SessionID)

	// Get upload session
	session, err := h.uploadRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get upload session: %w", err)
	}

	if session == nil {
		return nil, fmt.Errorf("upload session not found")
	}

	// Critical security check: Validate session ownership
	if !session.BelongsTo(query.UserID) {
		// Log security event for unauthorized access attempt
		utils.LogSecurityEvent(nil, "unauthorized_session_access", false,
			logger.F("user_id", query.UserID.String()),
			logger.F("session_id", query.SessionID.String()),
			logger.F("session_owner", session.UserID().String()),
		)
		return nil, fmt.Errorf("access denied: session does not belong to user")
	}

	return &GetUploadSessionResult{
		Session: session,
	}, nil
}
