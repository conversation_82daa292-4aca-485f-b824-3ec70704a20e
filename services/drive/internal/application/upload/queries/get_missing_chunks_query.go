package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/utils"
	uploadentities "github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
)

type GetMissingChunksQuery struct {
	SessionID uuid.UUID
	UserID    uuid.UUID
}

type GetMissingChunksResult struct {
	SessionID     uuid.UUID `json:"sessionId"`
	MissingChunks []int64   `json:"missingChunks"`
	TotalChunks   int64     `json:"totalChunks"`
	UploadedCount int64     `json:"uploadedCount"`
}

type GetMissingChunksHandler interface {
	Handle(ctx context.Context, query *GetMissingChunksQuery) (*GetMissingChunksResult, error)
}

type getMissingChunksHandler struct {
	uploadRepo uploadrepositories.UploadRepository
}

func NewGetMissingChunksHandler(uploadRepo uploadrepositories.UploadRepository) GetMissingChunksHandler {
	return &getMissingChunksHandler{
		uploadRepo: uploadRepo,
	}
}

func (h *getMissingChunksHandler) Handle(ctx context.Context, query *GetMissingChunksQuery) (*GetMissingChunksResult, error) {
	sessionID := uploadentities.UploadSessionID(query.SessionID)

	// Get upload session
	session, err := h.uploadRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get upload session: %w", err)
	}

	if session == nil {
		return nil, fmt.Errorf("upload session not found")
	}

	// Critical security check: Validate session ownership
	if !session.BelongsTo(query.UserID) {
		// Log security event for unauthorized access attempt
		utils.LogSecurityEvent(nil, "unauthorized_chunks_access", false,
			logger.F("user_id", query.UserID.String()),
			logger.F("session_id", query.SessionID.String()),
			logger.F("session_owner", session.UserID().String()),
		)
		return nil, fmt.Errorf("access denied: session does not belong to user")
	}

	// Get missing chunks from session
	missingChunks := session.GetMissingChunks()

	return &GetMissingChunksResult{
		SessionID:     query.SessionID,
		MissingChunks: missingChunks,
		TotalChunks:   session.TotalChunks(),
		UploadedCount: session.GetUploadedChunkCount(),
	}, nil
}
