package commands

import (
	"context"
	"crypto/sha256"
	"fmt"
	"hash"
	"io"
	"strings"
	"sync"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	uploadentities "github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/security"
)

type CompleteUploadCommand struct {
	SessionID uuid.UUID
	UserID    uuid.UUID
	MimeType  string
	TeamID    *uuid.UUID
	FolderID  *fileentities.FolderID
}

type CompleteUploadResult struct {
	File *fileentities.File
}

type CompleteUploadHandler interface {
	Handle(ctx context.Context, cmd *CompleteUploadCommand) (*CompleteUploadResult, error)
}

type completeUploadHandler struct {
	uploadRepo       uploadrepositories.UploadRepository
	fileRepo         filerepositories.FileRepository
	storagePort      ports.StoragePort
	auditPort        ports.AuditPort
	thumbnailService ThumbnailService
	logger           logger.Logger
}

// ThumbnailService interface for thumbnail generation
type ThumbnailService interface {
	CreateThumbnailJob(ctx context.Context, fileID, userID uuid.UUID, sourcePath, mimeType string, sizes []string, formats []string) (any, error)
}

func NewCompleteUploadHandler(
	uploadRepo uploadrepositories.UploadRepository,
	fileRepo filerepositories.FileRepository,
	storagePort ports.StoragePort,
	auditPort ports.AuditPort,
	thumbnailService ThumbnailService,
	logger logger.Logger,
) CompleteUploadHandler {
	return &completeUploadHandler{
		uploadRepo:       uploadRepo,
		fileRepo:         fileRepo,
		storagePort:      storagePort,
		auditPort:        auditPort,
		thumbnailService: thumbnailService,
		logger:           logger,
	}
}

func (h *completeUploadHandler) Handle(ctx context.Context, cmd *CompleteUploadCommand) (*CompleteUploadResult, error) {
	sessionID := uploadentities.UploadSessionID(cmd.SessionID)

	// Get upload session
	session, err := h.uploadRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get upload session: %w", err)
	}

	if session == nil {
		return nil, fmt.Errorf("upload session not found")
	}

	// Validate session
	if !session.BelongsTo(cmd.UserID) {
		return nil, fmt.Errorf("access denied: session does not belong to user")
	}

	if !session.IsComplete() {
		return nil, fmt.Errorf("upload session is not complete")
	}

	// Reassemble chunks into final file
	finalPath, checksum, err := h.reassembleChunks(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to reassemble chunks: %w", err)
	}

	// Create file entity
	file := fileentities.NewFile(session.FileName(), finalPath, cmd.MimeType, checksum, session.FileSize(), cmd.UserID)

	if cmd.TeamID != nil {
		file.SetTeamID(*cmd.TeamID)
	}

	if cmd.FolderID != nil {
		file.SetFolderID(*cmd.FolderID)
	}

	// Save file
	err = h.fileRepo.Create(ctx, file)
	if err != nil {
		return nil, fmt.Errorf("failed to create file: %w", err)
	}

	// Mark session as completed and cleanup
	session.MarkCompleted()
	err = h.uploadRepo.Update(ctx, session)
	if err != nil {
		h.logger.WithContext(ctx).WithError(err).Error("Failed to update session status to completed")
		// Continue execution, don't fail for session update errors
	}

	// Create audit log with sanitized file name
	sanitizedFileName := sanitizeFileName(session.FileName())
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileCreate,
		"upload_completed",
		fmt.Sprintf("File upload completed: %s", sanitizedFileName),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(uuid.UUID(file.ID()))
	auditLog.SetResourceType("file")
	auditLog.SetMetadata("session_id", session.ID().String())
	auditLog.SetMetadata("file_size", session.FileSize())
	auditLog.SetMetadata("total_chunks", session.TotalChunks())

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		h.logger.WithContext(ctx).WithError(err).Error("Failed to log audit event for upload completion")
		// Continue execution, don't fail for audit logging errors
	}

	// Trigger automatic thumbnail generation for supported file types
	if h.thumbnailService != nil && h.shouldGenerateThumbnails(cmd.MimeType) {
		go func() {
			// Use a background context for thumbnail generation to avoid blocking the response
			bgCtx := context.Background()

			// Generate thumbnails asynchronously
			_, err := h.thumbnailService.CreateThumbnailJob(
				bgCtx,
				uuid.UUID(file.ID()),
				cmd.UserID,
				finalPath,
				cmd.MimeType,
				[]string{"small", "medium", "large"}, // Default sizes
				[]string{"jpeg", "webp"},             // Default formats
			)
			if err != nil {
				h.logger.WithContext(bgCtx).WithError(err).WithFields(
					logger.F("file_id", file.ID().String()),
					logger.F("mime_type", cmd.MimeType),
				).Error("Failed to create thumbnail job")
			} else {
				h.logger.WithContext(bgCtx).WithFields(
					logger.F("file_id", file.ID().String()),
					logger.F("mime_type", cmd.MimeType),
				).Info("Thumbnail generation job created successfully")
			}
		}()
	}

	return &CompleteUploadResult{
		File: file,
	}, nil
}

// shouldGenerateThumbnails determines if thumbnails should be generated for the given MIME type
func (h *completeUploadHandler) shouldGenerateThumbnails(mimeType string) bool {
	// Support common image and video formats
	supportedTypes := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/bmp",
		"image/tiff",
		"video/mp4",
		"video/avi",
		"video/mov",
		"video/wmv",
		"video/flv",
		"video/webm",
		"video/mkv",
	}

	for _, supportedType := range supportedTypes {
		if mimeType == supportedType {
			return true
		}
	}

	return false
}

func (h *completeUploadHandler) reassembleChunks(ctx context.Context, session *uploadentities.UploadSession) (string, string, error) {
	// Create final file path using session ID (unique and secure)
	sessionID := session.ID().String()
	finalPath := fmt.Sprintf("files/%s/%s", sessionID[:2], sessionID[2:])

	// Validate the final path for security
	pathValidator := security.NewPathTraversalValidator()
	if err := pathValidator.ValidateStoragePath(finalPath); err != nil {
		return "", "", fmt.Errorf("generated path is invalid: %w", err)
	}

	// Create a streaming chunk reader that reassembles chunks on-demand and calculates checksum
	chunkReader := &streamingChunkReader{
		ctx:          ctx,
		session:      session,
		storagePort:  h.storagePort,
		totalChunks:  session.TotalChunks(),
		currentChunk: 0,
		hasher:       sha256.New(),
	}

	// Upload reassembled file using streaming reader
	_, err := h.storagePort.Upload(ctx, chunkReader, finalPath, "application/octet-stream")
	if err != nil {
		// Ensure cleanup even if upload fails
		chunkReader.Close()
		return "", "", fmt.Errorf("failed to upload final file: %w", err)
	}

	// Get calculated checksum
	checksum := chunkReader.GetChecksum()

	// Clean up chunks
	err = h.cleanupChunks(ctx, session)
	if err != nil {
		fmt.Printf("Failed to cleanup chunks: %v\n", err)
	}

	return finalPath, checksum, nil
}

// streamingChunkReader implements io.ReadCloser for streaming chunk reassembly
type streamingChunkReader struct {
	ctx           context.Context
	session       *uploadentities.UploadSession
	storagePort   ports.StoragePort
	totalChunks   int64
	currentChunk  int64
	currentReader io.ReadCloser
	hasher        hash.Hash
	mu            sync.Mutex // Protects currentChunk, currentReader, and hasher
	closed        bool
}

func (r *streamingChunkReader) Read(p []byte) (n int, err error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.closed {
		return 0, io.ErrClosedPipe
	}

	for {
		// If we don't have a current reader, get the next chunk
		if r.currentReader == nil {
			if r.currentChunk >= r.totalChunks {
				return 0, io.EOF
			}

			// Validate and sanitize session ID for safe path construction
			sessionID := r.session.ID().String()
			pathValidator := security.NewPathTraversalValidator()
			if err := pathValidator.ValidateUUID(sessionID); err != nil {
				return 0, fmt.Errorf("invalid session ID format: %w", err)
			}
			chunkPath := fmt.Sprintf("chunks/%s/chunk_%d", sessionID, r.currentChunk)

			// Validate chunk path for additional security
			if err := pathValidator.ValidateStoragePath(chunkPath); err != nil {
				return 0, fmt.Errorf("invalid chunk path: %w", err)
			}
			reader, err := r.storagePort.Download(r.ctx, chunkPath)
			if err != nil {
				return 0, fmt.Errorf("failed to download chunk %d: %w", r.currentChunk, err)
			}
			r.currentReader = reader
		}

		// Try to read from current chunk
		n, err := r.currentReader.Read(p)
		if err == io.EOF {
			// Current chunk is finished, close it and move to next
			if closeErr := r.currentReader.Close(); closeErr != nil {
				// Log but don't fail for close errors
			}
			r.currentReader = nil
			r.currentChunk++

			// If we read some data, return it first
			if n > 0 {
				return n, nil
			}
			// Otherwise continue to next chunk
			continue
		}

		if err != nil {
			return n, err
		}

		// Update checksum with the data we read
		if n > 0 {
			r.hasher.Write(p[:n])
		}

		return n, nil
	}
}

func (r *streamingChunkReader) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.closed {
		return nil
	}

	r.closed = true
	if r.currentReader != nil {
		return r.currentReader.Close()
	}
	return nil
}

func (r *streamingChunkReader) GetChecksum() string {
	r.mu.Lock()
	defer r.mu.Unlock()
	return fmt.Sprintf("%x", r.hasher.Sum(nil))
}

func (h *completeUploadHandler) cleanupChunks(ctx context.Context, session *uploadentities.UploadSession) error {
	var cleanupErrors []string

	// Clean up individual chunks
	for i := int64(0); i < session.TotalChunks(); i++ {
		chunkPath := fmt.Sprintf("chunks/%s/chunk_%d", session.ID().String(), i)
		err := h.storagePort.Delete(ctx, chunkPath)
		if err != nil {
			cleanupErrors = append(cleanupErrors, fmt.Sprintf("chunk %d: %v", i, err))
			h.logger.WithContext(ctx).WithError(err).WithFields(logger.F("chunk_path", chunkPath)).Error("Failed to delete chunk")
		}
	}

	// Try to remove chunk directory
	chunkDir := fmt.Sprintf("chunks/%s", session.ID().String())
	err := h.storagePort.Delete(ctx, chunkDir)
	if err != nil {
		h.logger.WithContext(ctx).WithError(err).WithFields(logger.F("chunk_dir", chunkDir)).Debug("Failed to delete chunk directory (may not be supported)")
	}

	// Return aggregated errors if any
	if len(cleanupErrors) > 0 {
		return fmt.Errorf("chunk cleanup errors: %s", strings.Join(cleanupErrors, "; "))
	}

	return nil
}

// sanitizeFileName removes potentially dangerous characters from file names for logging
func sanitizeFileName(name string) string {
	sanitized := ""
	for _, r := range name {
		if r >= 32 && r < 127 { // Printable ASCII only
			sanitized += string(r)
		} else {
			sanitized += "_"
		}
	}

	if len(sanitized) > 100 {
		sanitized = sanitized[:100] + "..."
	}

	return sanitized
}
