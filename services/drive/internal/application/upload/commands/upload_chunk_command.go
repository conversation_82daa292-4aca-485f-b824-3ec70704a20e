package commands

import (
	"context"
	"fmt"
	"io"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	uploadentities "github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
)

type UploadChunkCommand struct {
	SessionID  uuid.UUID
	ChunkIndex int64
	ChunkData  io.Reader
	ChunkSize  int64
	ChunkHash  string
	UserID     uuid.UUID
}

type UploadChunkResult struct {
	Session    *uploadentities.UploadSession
	IsComplete bool
	Progress   float64
}

type UploadChunkHandler interface {
	Handle(ctx context.Context, cmd *UploadChunkCommand) (*UploadChunkResult, error)
}

type uploadChunkHandler struct {
	uploadRepo  uploadrepositories.UploadRepository
	storagePort ports.StoragePort
	auditPort   ports.AuditPort
}

func NewUploadChunkHandler(
	uploadRepo uploadrepositories.UploadRepository,
	storagePort ports.StoragePort,
	auditPort ports.AuditPort,
) UploadChunkHandler {
	return &uploadChunkHandler{
		uploadRepo:  uploadRepo,
		storagePort: storagePort,
		auditPort:   auditPort,
	}
}

func (h *uploadChunkHandler) Handle(ctx context.Context, cmd *UploadChunkCommand) (*UploadChunkResult, error) {
	sessionID := uploadentities.UploadSessionID(cmd.SessionID)

	// Get upload session
	session, err := h.uploadRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get upload session: %w", err)
	}

	if session == nil {
		return nil, fmt.Errorf("upload session not found")
	}

	// Validate session
	if !session.IsActive() {
		return nil, fmt.Errorf("upload session is not active")
	}

	if !session.BelongsTo(cmd.UserID) {
		return nil, fmt.Errorf("access denied: session does not belong to user")
	}

	if cmd.ChunkIndex < 0 || cmd.ChunkIndex >= session.TotalChunks() {
		return nil, fmt.Errorf("invalid chunk index: %d", cmd.ChunkIndex)
	}

	if session.IsChunkUploaded(cmd.ChunkIndex) {
		return nil, fmt.Errorf("chunk %d already uploaded", cmd.ChunkIndex)
	}

	// Generate chunk storage path
	chunkPath := fmt.Sprintf("chunks/%s/chunk_%d", session.ID().String(), cmd.ChunkIndex)

	// Upload chunk to storage
	_, err = h.storagePort.Upload(ctx, cmd.ChunkData, chunkPath, "application/octet-stream")
	if err != nil {
		return nil, fmt.Errorf("failed to upload chunk: %w", err)
	}

	// Mark chunk as uploaded
	session.MarkChunkUploaded(cmd.ChunkIndex)

	// Update session
	err = h.uploadRepo.Update(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to update upload session: %w", err)
	}

	// Check if upload is complete
	isComplete := session.IsComplete()
	if isComplete {
		session.MarkCompleted()
		err = h.uploadRepo.Update(ctx, session)
		if err != nil {
			return nil, fmt.Errorf("failed to mark session as completed: %w", err)
		}
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileUpload,
		"chunk_uploaded",
		fmt.Sprintf("Chunk %d uploaded for session %s", cmd.ChunkIndex, session.ID().String()),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(uuid.UUID(session.ID()))
	auditLog.SetResourceType("upload_session")
	auditLog.SetMetadata("chunk_index", cmd.ChunkIndex)
	auditLog.SetMetadata("chunk_size", cmd.ChunkSize)
	auditLog.SetMetadata("progress", session.GetProgress())
	auditLog.SetMetadata("is_complete", isComplete)

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &UploadChunkResult{
		Session:    session,
		IsComplete: isComplete,
		Progress:   session.GetProgress(),
	}, nil
}
