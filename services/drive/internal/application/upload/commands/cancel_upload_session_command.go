package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	uploadentities "github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
)

type CancelUploadSessionCommand struct {
	SessionID uuid.UUID
	UserID    uuid.UUID
	Reason    string // Optional reason for cancellation
}

type CancelUploadSessionResult struct {
	Success   bool   `json:"success"`
	SessionID string `json:"sessionId"`
}

type CancelUploadSessionHandler interface {
	Handle(ctx context.Context, cmd *CancelUploadSessionCommand) (*CancelUploadSessionResult, error)
}

type cancelUploadSessionHandler struct {
	uploadRepo  uploadrepositories.UploadRepository
	storagePort ports.StoragePort
	auditPort   ports.AuditPort
}

func NewCancelUploadSessionHandler(
	uploadRepo uploadrepositories.UploadRepository,
	storagePort ports.StoragePort,
	auditPort ports.AuditPort,
) CancelUploadSessionHandler {
	return &cancelUploadSessionHandler{
		uploadRepo:  uploadRepo,
		storagePort: storagePort,
		auditPort:   auditPort,
	}
}

func (h *cancelUploadSessionHandler) Handle(ctx context.Context, cmd *CancelUploadSessionCommand) (*CancelUploadSessionResult, error) {
	sessionID := uploadentities.UploadSessionID(cmd.SessionID)

	// Get upload session
	session, err := h.uploadRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get upload session: %w", err)
	}

	if session == nil {
		return nil, fmt.Errorf("upload session not found")
	}

	// Critical security check: Validate session ownership
	if !session.BelongsTo(cmd.UserID) {
		// Log security event for unauthorized access attempt
		fmt.Printf("SECURITY: Unauthorized session cancel attempt - user_id: %s, session_id: %s, session_owner: %s\n",
			cmd.UserID.String(), cmd.SessionID.String(), session.UserID().String())
		return nil, fmt.Errorf("access denied: session does not belong to user")
	}

	// Cancel the session (mark as cancelled)
	session.MarkCanceled()

	// Update session in repository
	err = h.uploadRepo.Update(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to update session status: %w", err)
	}

	// Clean up uploaded chunks in background (best effort)
	go h.cleanupChunksAsync(context.Background(), session)

	// Create audit log
	reason := cmd.Reason
	if reason == "" {
		reason = "user_cancelled"
	}

	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileUpload,
		"upload_cancelled",
		fmt.Sprintf("Upload session cancelled: %s", reason),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(cmd.SessionID)
	auditLog.SetResourceType("upload_session")
	auditLog.SetMetadata("reason", reason)
	auditLog.SetMetadata("chunks_uploaded", session.GetUploadedChunkCount())
	auditLog.SetMetadata("total_chunks", session.TotalChunks())

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Failed to log audit event for session cancellation: %v\n", err)
	}

	return &CancelUploadSessionResult{
		Success:   true,
		SessionID: cmd.SessionID.String(),
	}, nil
}

func (h *cancelUploadSessionHandler) cleanupChunksAsync(ctx context.Context, session *uploadentities.UploadSession) {
	// This is a best-effort cleanup, errors are logged but not propagated
	for i := int64(0); i < session.TotalChunks(); i++ {
		chunkPath := fmt.Sprintf("chunks/%s/chunk_%d", session.ID().String(), i)
		err := h.storagePort.Delete(ctx, chunkPath)
		if err != nil {
			fmt.Printf("Failed to delete chunk %s during cleanup: %v\n", chunkPath, err)
		}
	}

	// Try to remove chunk directory
	chunkDir := fmt.Sprintf("chunks/%s", session.ID().String())
	err := h.storagePort.Delete(ctx, chunkDir)
	if err != nil {
		fmt.Printf("Failed to delete chunk directory %s during cleanup: %v\n", chunkDir, err)
	}
}
