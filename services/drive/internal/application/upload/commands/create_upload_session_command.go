package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	uploadentities "github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
)

type CreateUploadSessionCommand struct {
	UserID    uuid.UUID
	FileName  string
	FileSize  int64
	ChunkSize int64
	MimeType  string
}

type CreateUploadSessionResult struct {
	Session *uploadentities.UploadSession
}

type CreateUploadSessionHandler interface {
	Handle(ctx context.Context, cmd *CreateUploadSessionCommand) (*CreateUploadSessionResult, error)
}

type createUploadSessionHandler struct {
	uploadRepo uploadrepositories.UploadRepository
	auditPort  ports.AuditPort
}

func NewCreateUploadSessionHandler(
	uploadRepo uploadrepositories.UploadRepository,
	auditPort ports.AuditPort,
) CreateUploadSessionHandler {
	return &createUploadSessionHandler{
		uploadRepo: uploadRepo,
		auditPort:  auditPort,
	}
}

func (h *createUploadSessionHandler) Handle(ctx context.Context, cmd *CreateUploadSessionCommand) (*CreateUploadSessionResult, error) {
	// Validate input
	if cmd.FileSize <= 0 {
		return nil, fmt.Errorf("file size must be greater than 0")
	}

	if cmd.ChunkSize <= 0 {
		cmd.ChunkSize = 5 * 1024 * 1024 // Default 5MB chunks
	}

	if cmd.FileSize > 100*1024*1024 { // 100MB limit
		return nil, fmt.Errorf("file size exceeds maximum allowed size")
	}

	// Create upload session
	session := uploadentities.NewUploadSession(cmd.UserID, cmd.FileName, cmd.FileSize, cmd.ChunkSize)

	// Save session
	err := h.uploadRepo.Create(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to create upload session: %w", err)
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileUpload,
		"upload_session_created",
		fmt.Sprintf("Upload session created for file: %s", cmd.FileName),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(uuid.UUID(session.ID()))
	auditLog.SetResourceType("upload_session")
	auditLog.SetMetadata("file_name", cmd.FileName)
	auditLog.SetMetadata("file_size", cmd.FileSize)
	auditLog.SetMetadata("chunk_size", cmd.ChunkSize)
	auditLog.SetMetadata("total_chunks", session.TotalChunks())

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &CreateUploadSessionResult{Session: session}, nil
}
