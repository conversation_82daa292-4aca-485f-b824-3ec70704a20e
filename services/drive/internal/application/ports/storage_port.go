package ports

import (
	"context"
	"io"
)

type StoragePort interface {
	Upload(ctx context.Context, content io.Reader, fileName, mimeType string) (string, error)
	Download(ctx context.Context, path string) (io.ReadCloser, error)
	Delete(ctx context.Context, path string) error
	GenerateDownloadURL(ctx context.Context, path string, expiresInSeconds int) (string, error)
	GetFileInfo(ctx context.Context, path string) (*FileInfo, error)
	Exists(ctx context.Context, path string) (bool, error)
	Copy(ctx context.Context, sourcePath, destinationPath string) error
	Move(ctx context.Context, sourcePath, destinationPath string) error
	Health(ctx context.Context) error
}

type FileInfo struct {
	Path         string
	Size         int64
	ContentType  string
	LastModified string
	ETag         string
}

type StorageProvider interface {
	Name() string
	IsHealthy(ctx context.Context) bool
	Upload(ctx context.Context, content io.Reader, path, mimeType string) error
	Download(ctx context.Context, path string) (io.ReadCloser, error)
	Delete(ctx context.Context, path string) error
	GeneratePresignedURL(ctx context.Context, path string, expiresInSeconds int) (string, error)
	GetObjectInfo(ctx context.Context, path string) (*FileInfo, error)
	Exists(ctx context.Context, path string) (bool, error)
	Copy(ctx context.Context, sourcePath, destinationPath string) error
	Move(ctx context.Context, sourcePath, destinationPath string) error
}
