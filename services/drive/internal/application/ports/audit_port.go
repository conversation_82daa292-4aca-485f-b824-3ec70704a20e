package ports

import (
	"context"

	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
)

type AuditPort interface {
	LogEvent(ctx context.Context, log *auditentities.AuditLog) error
	LogEventBatch(ctx context.Context, logs []*auditentities.AuditLog) error
	EnrichWithLocation(ctx context.Context, log *auditentities.AuditLog, ipAddress string) error
	EnrichWithDevice(ctx context.Context, log *auditentities.AuditLog, userAgent string) error
	ValidateIntegrity(ctx context.Context, log *auditentities.AuditLog) error
	GenerateChecksum(ctx context.Context, log *auditentities.AuditLog) (string, error)
}

type LocationEnricher interface {
	GetLocation(ctx context.Context, ipAddress string) (*LocationInfo, error)
}

type LocationInfo struct {
	Country   string
	City      string
	Latitude  *float64
	Longitude *float64
}

type DeviceEnricher interface {
	ParseUserAgent(userAgent string) (*DeviceInfo, error)
}

type DeviceInfo struct {
	Browser   string
	OS        string
	Device    string
	IsMobile  bool
	IsTablet  bool
	IsDesktop bool
}
