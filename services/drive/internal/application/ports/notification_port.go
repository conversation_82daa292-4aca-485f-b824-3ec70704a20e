package ports

import (
	"context"

	"github.com/google/uuid"
)

type NotificationPort interface {
	SendFileSharedNotification(ctx context.Context, userID uuid.UUID, fileName string, sharedBy string) error
	SendFileUploadedNotification(ctx context.Context, userID uuid.UUID, fileName string) error
	SendFileDeletedNotification(ctx context.Context, userID uuid.UUID, fileName string) error
	SendQuotaExceededNotification(ctx context.Context, userID uuid.UUID, currentUsage, quotaLimit int64) error
	SendSecurityAlertNotification(ctx context.Context, userID uuid.UUID, alertType, message string) error
}

type NotificationProvider interface {
	SendEmail(ctx context.Context, to, subject, body string) error
	SendPushNotification(ctx context.Context, userID uuid.UUID, title, body string) error
	SendSlackNotification(ctx context.Context, channel, message string) error
	SendWebhook(ctx context.Context, url string, payload interface{}) error
}

type NotificationTemplate struct {
	Subject  string
	Body     string
	Template string
	Data     map[string]interface{}
}

type NotificationChannel string

const (
	NotificationChannelEmail   NotificationChannel = "email"
	NotificationChannelPush    NotificationChannel = "push"
	NotificationChannelSlack   NotificationChannel = "slack"
	NotificationChannelWebhook NotificationChannel = "webhook"
)
