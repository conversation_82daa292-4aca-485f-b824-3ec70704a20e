package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
)

type UpdateFileCommand struct {
	FileID     uuid.UUID
	UserID     uuid.UUID
	Name       *string
	FolderID   *fileentities.FolderID
	TeamID     *uuid.UUID
	Visibility *fileentities.Visibility
	Metadata   map[string]interface{}
}

type UpdateFileResult struct {
	File *fileentities.File
}

type UpdateFileHandler interface {
	Handle(ctx context.Context, cmd *UpdateFileCommand) (*UpdateFileResult, error)
}

type updateFileHandler struct {
	fileRepo         filerepositories.FileRepository
	folderRepo       filerepositories.FolderRepository
	accessControlSvc fileservices.AccessControlService
}

func NewUpdateFileHandler(
	fileRepo filerepositories.FileRepository,
	folderRepo filerepositories.FolderRepository,
	accessControlSvc fileservices.AccessControlService,
) UpdateFileHandler {
	return &updateFileHandler{
		fileRepo:         fileRepo,
		folderRepo:       folderRepo,
		accessControlSvc: accessControlSvc,
	}
}

func (h *updateFileHandler) Handle(ctx context.Context, cmd *UpdateFileCommand) (*UpdateFileResult, error) {
	fileID := fileentities.FileID(cmd.FileID)

	// Get file
	file, err := h.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	if file.IsDeleted() {
		return nil, fmt.Errorf("file is deleted")
	}

	// Validate file ownership or write access
	userTeams := []string{} // In a real implementation, this would come from the context
	if cmd.TeamID != nil {
		userTeams = append(userTeams, cmd.TeamID.String())
	}

	_, err = h.accessControlSvc.ValidateFileAccess(ctx, cmd.UserID, userTeams, fileID, fileservices.AccessOperationWrite)
	if err != nil {
		return nil, fmt.Errorf("access denied: %w", err)
	}

	// Validate folder if moving to a folder
	if cmd.FolderID != nil && *cmd.FolderID != fileentities.FolderID(uuid.Nil) {
		targetFolder, err := h.folderRepo.GetByID(ctx, *cmd.FolderID)
		if err != nil {
			return nil, fmt.Errorf("failed to get target folder: %w", err)
		}
		if targetFolder == nil {
			return nil, fmt.Errorf("target folder not found")
		}
		if targetFolder.IsDeleted() {
			return nil, fmt.Errorf("target folder is deleted")
		}
		// User must have access to target folder (same user or same team)
		if !targetFolder.IsOwnedBy(cmd.UserID) && (cmd.TeamID == nil || targetFolder.TeamID() == nil || *targetFolder.TeamID() != *cmd.TeamID) {
			return nil, fmt.Errorf("access denied: insufficient permissions for target folder")
		}
	}

	// Update file fields
	if cmd.Name != nil {
		file.UpdateName(*cmd.Name)
	}

	if cmd.FolderID != nil {
		// Handle moving to root folder
		if *cmd.FolderID == fileentities.FolderID(uuid.Nil) {
			file.ClearFolderID()
		} else {
			file.SetFolderID(*cmd.FolderID)
		}
	}

	if cmd.TeamID != nil {
		file.SetTeamID(*cmd.TeamID)
	}

	if cmd.Visibility != nil {
		file.SetVisibility(*cmd.Visibility)
	}

	// Update metadata if provided
	if cmd.Metadata != nil {
		for key, value := range cmd.Metadata {
			file.SetMetadata(key, value)
		}
	}

	// Save updated file
	err = h.fileRepo.Update(ctx, file)
	if err != nil {
		return nil, fmt.Errorf("failed to update file: %w", err)
	}

	return &UpdateFileResult{File: file}, nil
}