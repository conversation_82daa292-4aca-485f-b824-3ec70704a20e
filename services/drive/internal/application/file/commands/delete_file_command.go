package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
)

type DeleteFileCommand struct {
	FileID uuid.UUID
	UserID uuid.UUID
}

type DeleteFileResult struct {
	Success bool
}

type DeleteFileHandler interface {
	Handle(ctx context.Context, cmd *DeleteFileCommand) (*DeleteFileResult, error)
}

type deleteFileHandler struct {
	fileRepo    filerepositories.FileRepository
	fileService fileservices.FileService
	storagePort ports.StoragePort
	auditPort   ports.AuditPort
}

func NewDeleteFileHandler(
	fileRepo filerepositories.FileRepository,
	fileService fileservices.FileService,
	storagePort ports.StoragePort,
	auditPort ports.AuditPort,
) DeleteFileHandler {
	return &deleteFileHandler{
		fileRepo:    fileRepo,
		fileService: fileService,
		storagePort: storagePort,
		auditPort:   auditPort,
	}
}

func (h *deleteFileHandler) Handle(ctx context.Context, cmd *DeleteFileCommand) (*DeleteFileResult, error) {
	fileID := fileentities.FileID(cmd.FileID)

	// Validate file ownership
	err := h.fileService.ValidateFileOwnership(ctx, fileID, cmd.UserID)
	if err != nil {
		return nil, fmt.Errorf("access denied: %w", err)
	}

	// Get file
	file, err := h.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	if file.IsDeleted() {
		return &DeleteFileResult{Success: true}, nil
	}

	// Soft delete file
	file.Delete()
	err = h.fileRepo.Update(ctx, file)
	if err != nil {
		return nil, fmt.Errorf("failed to delete file: %w", err)
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileDelete,
		"file_delete",
		fmt.Sprintf("File deleted: %s", file.Name()),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(uuid.UUID(file.ID()))
	auditLog.SetResourceType("file")
	auditLog.SetMetadata("file_name", file.Name())
	auditLog.SetMetadata("file_size", file.Size())

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log error but don't fail the delete
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &DeleteFileResult{Success: true}, nil
}
