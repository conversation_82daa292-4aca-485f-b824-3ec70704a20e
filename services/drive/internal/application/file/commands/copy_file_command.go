package commands

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
)

type CopyFileCommand struct {
	SourceFileID uuid.UUID
	UserID       uuid.UUID
	NewName      *string // Optional: new name for the copy
	FolderID     *fileentities.FolderID
	TeamID       *uuid.UUID
}

type CopyFileResult struct {
	OriginalFile *fileentities.File
	CopiedFile   *fileentities.File
}

type CopyFileHandler interface {
	Handle(ctx context.Context, cmd *CopyFileCommand) (*CopyFileResult, error)
}

type copyFileHandler struct {
	fileRepo         filerepositories.FileRepository
	accessControlSvc fileservices.AccessControlService
	storagePort      ports.StoragePort
	auditPort        ports.AuditPort
}

func NewCopyFileHandler(
	fileRepo filerepositories.FileRepository,
	accessControlSvc fileservices.AccessControlService,
	storagePort ports.StoragePort,
	auditPort ports.AuditPort,
) CopyFileHandler {
	return &copyFileHandler{
		fileRepo:         fileRepo,
		accessControlSvc: accessControlSvc,
		storagePort:      storagePort,
		auditPort:        auditPort,
	}
}

func (h *copyFileHandler) Handle(ctx context.Context, cmd *CopyFileCommand) (*CopyFileResult, error) {
	sourceFileID := fileentities.FileID(cmd.SourceFileID)

	// Get source file
	sourceFile, err := h.fileRepo.GetByID(ctx, sourceFileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source file: %w", err)
	}

	if sourceFile == nil {
		return nil, fmt.Errorf("source file not found")
	}

	if sourceFile.IsDeleted() {
		return nil, fmt.Errorf("cannot copy deleted file")
	}

	// Validate read access to source file
	userTeams := []string{} // In a real implementation, this would come from the context
	if cmd.TeamID != nil {
		userTeams = append(userTeams, cmd.TeamID.String())
	}

	_, err = h.accessControlSvc.ValidateFileAccess(ctx, cmd.UserID, userTeams, sourceFileID, fileservices.AccessOperationRead)
	if err != nil {
		return nil, fmt.Errorf("access denied to source file: %w", err)
	}

	// Determine the new file name
	newName := sourceFile.Name()
	if cmd.NewName != nil && *cmd.NewName != "" {
		newName = *cmd.NewName
	} else {
		// Add "copy" suffix if no new name provided
		ext := filepath.Ext(sourceFile.Name())
		nameWithoutExt := sourceFile.Name()[:len(sourceFile.Name())-len(ext)]
		newName = fmt.Sprintf("%s - Copy%s", nameWithoutExt, ext)
	}

	// Generate new path for the copied file
	newPath := h.generateNewPath(sourceFile.Path(), newName)

	// Copy file in storage
	err = h.storagePort.Copy(ctx, sourceFile.Path(), newPath)
	if err != nil {
		return nil, fmt.Errorf("failed to copy file in storage: %w", err)
	}

	// Create new file entity
	copiedFile := fileentities.NewFile(
		newName,
		newPath,
		sourceFile.MimeType(),
		sourceFile.Checksum(), // Same checksum as it's the same content
		sourceFile.Size(),
		cmd.UserID,
	)

	// Set optional fields
	if cmd.FolderID != nil {
		copiedFile.SetFolderID(*cmd.FolderID)
	}

	if cmd.TeamID != nil {
		copiedFile.SetTeamID(*cmd.TeamID)
	}

	// Copy metadata from source file
	for key, value := range sourceFile.Metadata() {
		copiedFile.SetMetadata(key, value)
	}

	// Set copied file metadata
	copiedFile.SetMetadata("copied_from", sourceFile.ID().String())
	copiedFile.SetMetadata("copied_at", time.Now().Format(time.RFC3339))

	// Save the new file entity
	err = h.fileRepo.Create(ctx, copiedFile)
	if err != nil {
		// If database creation fails, clean up the storage copy
		_ = h.storagePort.Delete(ctx, newPath)
		return nil, fmt.Errorf("failed to create copied file record: %w", err)
	}

	// Create audit log
	if h.auditPort != nil {
		auditLog := auditentities.NewAuditLog(
			auditentities.EventTypeFileCopy,
			"file_copy",
			fmt.Sprintf("File copied: %s to %s", sourceFile.Name(), copiedFile.Name()),
		)
		auditLog.SetUserID(cmd.UserID)
		auditLog.SetResourceID(uuid.UUID(copiedFile.ID()))
		auditLog.SetResourceType("file")
		auditLog.SetMetadata("source_file_id", sourceFile.ID().String())
		auditLog.SetMetadata("source_file_name", sourceFile.Name())
		auditLog.SetMetadata("copied_file_name", copiedFile.Name())
		auditLog.SetMetadata("copied_file_path", copiedFile.Path())

		_ = h.auditPort.LogEvent(ctx, auditLog)
	}

	return &CopyFileResult{
		OriginalFile: sourceFile,
		CopiedFile:   copiedFile,
	}, nil
}

// generateNewPath creates a new storage path for the copied file
func (h *copyFileHandler) generateNewPath(originalPath, newName string) string {
	dir := filepath.Dir(originalPath)
	ext := filepath.Ext(newName)
	
	// Generate a unique filename with timestamp
	timestamp := time.Now().Format("20060102150405")
	nameWithoutExt := newName[:len(newName)-len(ext)]
	uniqueName := fmt.Sprintf("%s_%s%s", nameWithoutExt, timestamp, ext)
	
	return filepath.Join(dir, uniqueName)
}