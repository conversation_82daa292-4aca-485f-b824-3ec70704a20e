package commands

import (
	"context"
	"fmt"
	"io"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
	"github.com/swork-team/platform/services/drive/internal/domain/file/valueobjects"
)

type UploadFileCommand struct {
	UserID      uuid.UUID
	TeamID      *uuid.UUID
	FolderID    *fileentities.FolderID
	FileName    string
	FileContent io.Reader
	Size        int64
	MimeType    string
	Checksum    string
}

type UploadFileResult struct {
	File *fileentities.File
}

type UploadFileHandler interface {
	Handle(ctx context.Context, cmd *UploadFileCommand) (*UploadFileResult, error)
}

type uploadFileHandler struct {
	fileRepo    filerepositories.FileRepository
	fileService fileservices.FileService
	storagePort ports.StoragePort
	auditPort   ports.AuditPort
}

func NewUploadFileHandler(
	fileRepo filerepositories.FileRepository,
	fileService fileservices.FileService,
	storagePort ports.StoragePort,
	auditPort ports.AuditPort,
) UploadFileHandler {
	return &uploadFileHandler{
		fileRepo:    fileRepo,
		fileService: fileService,
		storagePort: storagePort,
		auditPort:   auditPort,
	}
}

func (h *uploadFileHandler) Handle(ctx context.Context, cmd *UploadFileCommand) (*UploadFileResult, error) {
	// Validate file info
	fileInfo, err := valueobjects.NewFileInfo(cmd.FileName, cmd.MimeType, cmd.Size)
	if err != nil {
		return nil, fmt.Errorf("invalid file info: %w", err)
	}

	err = h.fileService.ValidateFileInfo(fileInfo)
	if err != nil {
		return nil, fmt.Errorf("file validation failed: %w", err)
	}

	// Upload file content
	storagePath, err := h.storagePort.Upload(ctx, cmd.FileContent, cmd.FileName, cmd.MimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	// Create file entity
	file := fileentities.NewFile(cmd.FileName, storagePath, cmd.MimeType, cmd.Checksum, cmd.Size, cmd.UserID)

	if cmd.TeamID != nil {
		file.SetTeamID(*cmd.TeamID)
	}

	if cmd.FolderID != nil {
		file.SetFolderID(*cmd.FolderID)
	}

	// Save file
	err = h.fileRepo.Create(ctx, file)
	if err != nil {
		return nil, fmt.Errorf("failed to create file: %w", err)
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileUpload,
		"file_upload",
		fmt.Sprintf("File uploaded: %s", cmd.FileName),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(uuid.UUID(file.ID()))
	auditLog.SetResourceType("file")
	auditLog.SetMetadata("file_size", cmd.Size)
	auditLog.SetMetadata("mime_type", cmd.MimeType)

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log error but don't fail the upload - use structured logging
		// Note: In production, this should use the logger from context
		// For now, we'll leave a TODO to implement proper logging injection
		// TODO: Inject logger service and use structured logging
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &UploadFileResult{
		File: file,
	}, nil
}
