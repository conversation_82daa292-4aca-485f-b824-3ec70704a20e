package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type ListFilesQuery struct {
	UserID   uuid.UUID
	TeamID   *uuid.UUID
	FolderID *fileentities.FolderID
	Offset   int
	Limit    int
}

type ListFilesResult struct {
	Files      []*fileentities.File
	TotalCount int64
}

type ListFilesHandler interface {
	Handle(ctx context.Context, query *ListFilesQuery) (*ListFilesResult, error)
}

type listFilesHandler struct {
	fileRepo filerepositories.FileRepository
}

func NewListFilesHandler(fileRepo filerepositories.FileRepository) ListFilesHandler {
	return &listFilesHandler{
		fileRepo: fileRepo,
	}
}

func (h *listFilesHandler) Handle(ctx context.Context, query *ListFilesQuery) (*ListFilesResult, error) {
	var files []*fileentities.File
	var totalCount int64
	var err error

	// Determine which files to list based on query parameters
	if query.FolderID != nil {
		files, err = h.fileRepo.GetByFolderID(ctx, *query.FolderID, query.Offset, query.Limit)
		if err != nil {
			return nil, fmt.Errorf("failed to get files by folder: %w", err)
		}
		// Note: Would need a count method for folders
		totalCount = int64(len(files))
	} else if query.TeamID != nil {
		files, err = h.fileRepo.GetByTeamID(ctx, *query.TeamID, query.Offset, query.Limit)
		if err != nil {
			return nil, fmt.Errorf("failed to get files by team: %w", err)
		}
		totalCount, err = h.fileRepo.CountByTeamID(ctx, *query.TeamID)
		if err != nil {
			return nil, fmt.Errorf("failed to count files by team: %w", err)
		}
	} else {
		files, err = h.fileRepo.GetByUserID(ctx, query.UserID, query.Offset, query.Limit)
		if err != nil {
			return nil, fmt.Errorf("failed to get files by user: %w", err)
		}
		totalCount, err = h.fileRepo.CountByUserID(ctx, query.UserID)
		if err != nil {
			return nil, fmt.Errorf("failed to count files by user: %w", err)
		}
	}

	// Filter out deleted files
	var activeFiles []*fileentities.File
	for _, file := range files {
		if !file.IsDeleted() {
			activeFiles = append(activeFiles, file)
		}
	}

	return &ListFilesResult{
		Files:      activeFiles,
		TotalCount: totalCount,
	}, nil
}
