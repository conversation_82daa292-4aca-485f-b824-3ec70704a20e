package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/helpers"
)

type SearchFilesQuery struct {
	UserID     uuid.UUID
	Query      string
	FileType   string
	MinSize    *int64
	MaxSize    *int64
	Extension  string
	TeamID     *uuid.UUID
	FolderID   *fileentities.FolderID
	Visibility *fileentities.Visibility
	Offset     int
	Limit      int
}

type SearchFilesResult struct {
	Files      []*fileentities.File
	TotalCount int64
	Query      string
}

type SearchFilesHandler interface {
	Handle(ctx context.Context, query *SearchFilesQuery) (*SearchFilesResult, error)
}

type searchFilesHandler struct {
	fileRepo  filerepositories.FileRepository
	auditPort ports.AuditPort
}

func NewSearchFilesHandler(
	fileRepo filerepositories.FileRepository,
	auditPort ports.AuditPort,
) SearchFilesHandler {
	return &searchFilesHandler{
		fileRepo:  fileRepo,
		auditPort: auditPort,
	}
}

func (h *searchFilesHandler) Handle(ctx context.Context, query *SearchFilesQuery) (*SearchFilesResult, error) {
	// Enhanced security validation for search query
	if err := helpers.ValidateSearchQuery(query.Query); err != nil {
		// Log security event for invalid search query
		fmt.Printf("SECURITY: Invalid search query attempted - user_id: %s, query: %s, error: %s\n",
			query.UserID.String(), query.Query, err.Error())
		return nil, fmt.Errorf("invalid search query: %w", err)
	}

	// Validate pagination parameters
	if err := helpers.ValidatePaginationParams(query.Offset, query.Limit); err != nil {
		return nil, fmt.Errorf("invalid pagination parameters: %w", err)
	}

	if query.Limit <= 0 {
		query.Limit = 20
	}

	if query.Limit > 100 {
		query.Limit = 100
	}

	// Sanitize the search query before using it
	sanitizedQuery := helpers.SanitizeSearchQuery(query.Query)

	// Search files with sanitized query
	files, err := h.fileRepo.Search(ctx, query.UserID, sanitizedQuery, query.Offset, query.Limit)
	if err != nil {
		return nil, fmt.Errorf("failed to search files: %w", err)
	}

	// Filter files based on additional criteria
	filteredFiles := h.filterFiles(files, query)

	// Create audit log with sanitized query for security
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileRead,
		"file_search",
		"File search performed",
	)
	auditLog.SetUserID(query.UserID)
	auditLog.SetMetadata("search_query", sanitizedQuery) // Use sanitized query
	auditLog.SetMetadata("results_count", len(filteredFiles))
	auditLog.SetMetadata("file_type", query.FileType)
	auditLog.SetMetadata("extension", query.Extension)

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &SearchFilesResult{
		Files:      filteredFiles,
		TotalCount: int64(len(filteredFiles)), // TODO: Implement proper count
		Query:      sanitizedQuery,            // Return sanitized query
	}, nil
}

func (h *searchFilesHandler) filterFiles(files []*fileentities.File, query *SearchFilesQuery) []*fileentities.File {
	var filtered []*fileentities.File

	for _, file := range files {
		// Skip deleted files
		if file.IsDeleted() {
			continue
		}

		// Filter by file type
		if query.FileType != "" {
			if !h.matchesFileType(file, query.FileType) {
				continue
			}
		}

		// Filter by size range
		if query.MinSize != nil && file.Size() < *query.MinSize {
			continue
		}
		if query.MaxSize != nil && file.Size() > *query.MaxSize {
			continue
		}

		// Filter by extension
		if query.Extension != "" {
			if !h.matchesExtension(file, query.Extension) {
				continue
			}
		}

		// Filter by team
		if query.TeamID != nil {
			if !file.IsInTeam(*query.TeamID) {
				continue
			}
		}

		// Filter by folder
		if query.FolderID != nil {
			if file.FolderID() == nil || *file.FolderID() != *query.FolderID {
				continue
			}
		}

		// Filter by visibility
		if query.Visibility != nil {
			if file.Visibility() != *query.Visibility {
				continue
			}
		}

		filtered = append(filtered, file)
	}

	return filtered
}

func (h *searchFilesHandler) matchesFileType(file *fileentities.File, fileType string) bool {
	mimeType := file.MimeType()

	switch fileType {
	case "image":
		return mimeType[:6] == "image/"
	case "video":
		return mimeType[:6] == "video/"
	case "audio":
		return mimeType[:6] == "audio/"
	case "document":
		return mimeType == "application/pdf" ||
			mimeType == "application/msword" ||
			mimeType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
			mimeType == "application/vnd.ms-excel" ||
			mimeType == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
			mimeType == "application/vnd.ms-powerpoint" ||
			mimeType == "application/vnd.openxmlformats-officedocument.presentationml.presentation"
	case "text":
		return mimeType[:5] == "text/"
	case "archive":
		return mimeType == "application/zip" ||
			mimeType == "application/x-rar-compressed" ||
			mimeType == "application/x-7z-compressed" ||
			mimeType == "application/x-tar" ||
			mimeType == "application/gzip"
	default:
		return true
	}
}

func (h *searchFilesHandler) matchesExtension(file *fileentities.File, extension string) bool {
	fileName := file.Name()
	if len(fileName) < len(extension) {
		return false
	}

	fileExt := fileName[len(fileName)-len(extension):]
	return fileExt == extension
}
