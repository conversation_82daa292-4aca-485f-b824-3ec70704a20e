package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
)

type GetFileQuery struct {
	FileID    uuid.UUID
	UserID    uuid.UUID
	UserTeams []string
}

type GetFileResult struct {
	File *fileentities.File
}

type GetFileHandler interface {
	Handle(ctx context.Context, query *GetFileQuery) (*GetFileResult, error)
}

type getFileHandler struct {
	fileRepo    filerepositories.FileRepository
	fileService fileservices.FileService
	auditPort   ports.AuditPort
}

func NewGetFileHandler(
	fileRepo filerepositories.FileRepository,
	fileService fileservices.FileService,
	auditPort ports.AuditPort,
) GetFileHandler {
	return &getFileHandler{
		fileRepo:    fileRepo,
		fileService: fileService,
		auditPort:   auditPort,
	}
}

func (h *getFileHandler) Handle(ctx context.Context, query *GetFileQuery) (*GetFileResult, error) {
	fileID := fileentities.FileID(query.FileID)

	// Validate file access
	err := h.fileService.ValidateFileAccess(ctx, fileID, query.UserID, query.UserTeams)
	if err != nil {
		return nil, fmt.Errorf("access denied: %w", err)
	}

	// Get file
	file, err := h.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	if file.IsDeleted() {
		return nil, fmt.Errorf("file is deleted")
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileRead,
		"file_read",
		fmt.Sprintf("File accessed: %s", file.Name()),
	)
	auditLog.SetUserID(query.UserID)
	auditLog.SetResourceID(uuid.UUID(file.ID()))
	auditLog.SetResourceType("file")
	auditLog.SetMetadata("file_name", file.Name())

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log error but don't fail the query
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &GetFileResult{File: file}, nil
}
