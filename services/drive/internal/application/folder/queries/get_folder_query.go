package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type GetFolderQuery struct {
	FolderID  uuid.UUID
	UserID    uuid.UUID
	UserTeams []string
}

type GetFolderResult struct {
	Folder     *fileentities.Folder
	Files      []*fileentities.File
	Subfolders []*fileentities.Folder
}

type GetFolderHandler interface {
	Handle(ctx context.Context, query *GetFolderQuery) (*GetFolderResult, error)
}

type getFolderHandler struct {
	folderRepo filerepositories.FolderRepository
	fileRepo   filerepositories.FileRepository
	auditPort  ports.AuditPort
	logger     logger.Logger
}

func NewGetFolderHandler(
	folderRepo filerepositories.FolderRepository,
	fileRepo filerepositories.FileRepository,
	auditPort ports.AuditPort,
	logger logger.Logger,
) GetFolderHandler {
	return &getFolderHandler{
		folderRepo: folderRepo,
		fileRepo:   fileRepo,
		auditPort:  auditPort,
		logger:     logger,
	}
}

func (h *getFolderHandler) Handle(ctx context.Context, query *GetFolderQuery) (*GetFolderResult, error) {
	folderID := fileentities.FolderID(query.FolderID)

	// Get folder
	folder, err := h.folderRepo.GetByID(ctx, folderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	if folder == nil {
		return nil, fmt.Errorf("folder not found")
	}

	if folder.IsDeleted() {
		return nil, fmt.Errorf("folder is deleted")
	}

	// Check access permissions
	if !h.canUserAccessFolder(folder, query.UserID, query.UserTeams) {
		return nil, fmt.Errorf("access denied")
	}

	// Get files in folder
	files, err := h.fileRepo.GetByFolderID(ctx, folderID, 0, 1000) // TODO: Add pagination
	if err != nil {
		return nil, fmt.Errorf("failed to get files in folder: %w", err)
	}

	// Get subfolders
	subfolders, err := h.folderRepo.GetByParentID(ctx, folderID, 0, 1000) // TODO: Add pagination
	if err != nil {
		return nil, fmt.Errorf("failed to get subfolders: %w", err)
	}

	// Create audit log with sanitized folder name
	sanitizedFolderName := sanitizeFolderName(folder.Name())
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFolderRead,
		"folder_read",
		fmt.Sprintf("Folder accessed: %s", sanitizedFolderName),
	)
	auditLog.SetUserID(query.UserID)
	auditLog.SetResourceID(uuid.UUID(folder.ID()))
	auditLog.SetResourceType("folder")
	auditLog.SetMetadata("folder_name", sanitizedFolderName)
	auditLog.SetMetadata("files_count", len(files))
	auditLog.SetMetadata("subfolders_count", len(subfolders))

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		h.logger.WithContext(ctx).WithError(err).Error("Failed to log audit event for folder access")
		// Continue execution, don't fail the request for audit logging failure
	}

	return &GetFolderResult{
		Folder:     folder,
		Files:      files,
		Subfolders: subfolders,
	}, nil
}

func (h *getFolderHandler) canUserAccessFolder(folder *fileentities.Folder, userID uuid.UUID, userTeams []string) bool {
	// Check if user owns the folder
	if folder.IsOwnedBy(userID) {
		return true
	}

	// Check if folder is public
	if folder.IsPublic() {
		return true
	}

	// Check team access for team-visible folders
	if folder.IsTeamVisible() && folder.TeamID() != nil {
		folderTeamID := folder.TeamID().String()
		for _, userTeam := range userTeams {
			if userTeam == folderTeamID {
				return true
			}
		}
	}

	return false
}

// sanitizeFolderName removes potentially dangerous characters from folder names for logging
func sanitizeFolderName(name string) string {
	// Remove control characters and limit length for secure logging
	sanitized := ""
	for _, r := range name {
		if r >= 32 && r < 127 { // Printable ASCII only
			sanitized += string(r)
		} else {
			sanitized += "_" // Replace non-printable with underscore
		}
	}

	// Limit length to prevent log injection
	if len(sanitized) > 100 {
		sanitized = sanitized[:100] + "..."
	}

	return sanitized
}
