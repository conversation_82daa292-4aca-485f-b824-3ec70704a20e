package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type GetRootFolderQuery struct {
	UserID    uuid.UUID
	TeamID    *uuid.UUID
	UserTeams []string
	Offset    int
	Limit     int
}

type GetRootFolderResult struct {
	Files      []*fileentities.File
	Folders    []*fileentities.Folder
	TotalFiles int64
	TotalFolders int64
}

type GetRootFolderHandler interface {
	Handle(ctx context.Context, query *GetRootFolderQuery) (*GetRootFolderResult, error)
}

type getRootFolderHandler struct {
	folderRepo filerepositories.FolderRepository
	fileRepo   filerepositories.FileRepository
	auditPort  ports.AuditPort
	logger     logger.Logger
}

func NewGetRootFolderHandler(
	folderRepo filerepositories.FolderRepository,
	fileRepo filerepositories.FileRepository,
	auditPort ports.AuditPort,
	logger logger.Logger,
) GetRootFolderHandler {
	return &getRootFolderHandler{
		folderRepo: folderRepo,
		fileRepo:   fileRepo,
		auditPort:  auditPort,
		logger:     logger,
	}
}

func (h *getRootFolderHandler) Handle(ctx context.Context, query *GetRootFolderQuery) (*GetRootFolderResult, error) {
	// Get root folders (folders with parentID = nil)
	var rootFolders []*fileentities.Folder
	var err error

	if query.TeamID != nil {
		rootFolders, err = h.folderRepo.GetRootFoldersByTeamID(ctx, *query.TeamID, query.Offset, query.Limit)
	} else {
		rootFolders, err = h.folderRepo.GetRootFoldersByUserID(ctx, query.UserID, query.Offset, query.Limit)
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get root folders: %w", err)
	}

	// Filter folders by access permissions
	var accessibleFolders []*fileentities.Folder
	for _, folder := range rootFolders {
		if h.canUserAccessFolder(folder, query.UserID, query.UserTeams) {
			accessibleFolders = append(accessibleFolders, folder)
		}
	}

	// Get root files (files with folderID = nil)
	var rootFiles []*fileentities.File
	if query.TeamID != nil {
		rootFiles, err = h.fileRepo.GetRootFilesByTeamID(ctx, *query.TeamID, query.Offset, query.Limit)
	} else {
		rootFiles, err = h.fileRepo.GetRootFilesByUserID(ctx, query.UserID, query.Offset, query.Limit)
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get root files: %w", err)
	}

	// Filter files by access permissions
	var accessibleFiles []*fileentities.File
	for _, file := range rootFiles {
		if h.canUserAccessFile(file, query.UserID, query.UserTeams) {
			accessibleFiles = append(accessibleFiles, file)
		}
	}

	// Get total counts
	var totalFolders, totalFiles int64
	if query.TeamID != nil {
		totalFolders, err = h.folderRepo.CountRootFoldersByTeamID(ctx, *query.TeamID)
		if err != nil {
			h.logger.WithContext(ctx).WithError(err).Error("Failed to count root folders by team")
			totalFolders = int64(len(accessibleFolders))
		}
		totalFiles, err = h.fileRepo.CountRootFilesByTeamID(ctx, *query.TeamID)
		if err != nil {
			h.logger.WithContext(ctx).WithError(err).Error("Failed to count root files by team")
			totalFiles = int64(len(accessibleFiles))
		}
	} else {
		totalFolders, err = h.folderRepo.CountRootFoldersByUserID(ctx, query.UserID)
		if err != nil {
			h.logger.WithContext(ctx).WithError(err).Error("Failed to count root folders by user")
			totalFolders = int64(len(accessibleFolders))
		}
		totalFiles, err = h.fileRepo.CountRootFilesByUserID(ctx, query.UserID)
		if err != nil {
			h.logger.WithContext(ctx).WithError(err).Error("Failed to count root files by user")
			totalFiles = int64(len(accessibleFiles))
		}
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFolderRead,
		"root_folder_accessed",
		"Root folder accessed",
	)
	auditLog.SetUserID(query.UserID)
	auditLog.SetResourceType("root_folder")
	auditLog.SetMetadata("files_count", len(accessibleFiles))
	auditLog.SetMetadata("folders_count", len(accessibleFolders))
	if query.TeamID != nil {
		auditLog.SetMetadata("team_id", query.TeamID.String())
	}

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		h.logger.WithContext(ctx).WithError(err).Error("Failed to log audit event for root folder access")
	}

	return &GetRootFolderResult{
		Files:        accessibleFiles,
		Folders:      accessibleFolders,
		TotalFiles:   totalFiles,
		TotalFolders: totalFolders,
	}, nil
}

func (h *getRootFolderHandler) canUserAccessFolder(folder *fileentities.Folder, userID uuid.UUID, userTeams []string) bool {
	if folder.IsOwnedBy(userID) {
		return true
	}

	if folder.IsPublic() {
		return true
	}

	if folder.IsTeamVisible() && folder.TeamID() != nil {
		folderTeamID := folder.TeamID().String()
		for _, userTeam := range userTeams {
			if userTeam == folderTeamID {
				return true
			}
		}
	}

	return false
}

func (h *getRootFolderHandler) canUserAccessFile(file *fileentities.File, userID uuid.UUID, userTeams []string) bool {
	if file.IsOwnedBy(userID) {
		return true
	}

	if file.IsPublic() {
		return true
	}

	if file.IsTeamVisible() && file.TeamID() != nil {
		fileTeamID := file.TeamID().String()
		for _, userTeam := range userTeams {
			if userTeam == fileTeamID {
				return true
			}
		}
	}

	return false
}