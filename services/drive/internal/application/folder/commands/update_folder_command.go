package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type UpdateFolderCommand struct {
	FolderID    uuid.UUID
	UserID      uuid.UUID
	Name        *string
	Description *string
	Visibility  *fileentities.Visibility
	ParentID    *fileentities.FolderID
}

type UpdateFolderResult struct {
	Folder *fileentities.Folder
}

type UpdateFolderHandler interface {
	Handle(ctx context.Context, cmd *UpdateFolderCommand) (*UpdateFolderResult, error)
}

type updateFolderHandler struct {
	folderRepo filerepositories.FolderRepository
}

func NewUpdateFolderHandler(
	folderRepo filerepositories.FolderRepository,
) UpdateFolderHandler {
	return &updateFolderHandler{
		folderRepo: folderRepo,
	}
}

func (h *updateFolderHandler) Handle(ctx context.Context, cmd *UpdateFolderCommand) (*UpdateFolderResult, error) {
	folderID := fileentities.FolderID(cmd.FolderID)

	// Get folder
	folder, err := h.folderRepo.GetByID(ctx, folderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	if folder == nil {
		return nil, fmt.Errorf("folder not found")
	}

	if folder.IsDeleted() {
		return nil, fmt.Errorf("folder is deleted")
	}

	// Validate folder ownership
	if !folder.IsOwnedBy(cmd.UserID) {
		return nil, fmt.Errorf("access denied: user does not own this folder")
	}

	// Validate parent folder if moving
	if cmd.ParentID != nil {
		// If not moving to root (parentID is not nil), validate parent exists and is accessible
		if *cmd.ParentID != fileentities.FolderID(uuid.Nil) {
			parentFolder, err := h.folderRepo.GetByID(ctx, *cmd.ParentID)
			if err != nil {
				return nil, fmt.Errorf("failed to get parent folder: %w", err)
			}
			if parentFolder == nil {
				return nil, fmt.Errorf("parent folder not found")
			}
			if parentFolder.IsDeleted() {
				return nil, fmt.Errorf("parent folder is deleted")
			}
			// User must have access to parent folder (same user or same team)
			if !parentFolder.IsOwnedBy(cmd.UserID) && parentFolder.TeamID() != folder.TeamID() {
				return nil, fmt.Errorf("access denied: insufficient permissions for parent folder")
			}

			// Check for circular dependency (folder cannot be moved to itself or its descendants)
			wouldCreateCycle, err := h.folderRepo.WouldCreateCycle(ctx, folderID, *cmd.ParentID)
			if err != nil {
				return nil, fmt.Errorf("failed to validate folder hierarchy: %w", err)
			}
			if wouldCreateCycle {
				return nil, fmt.Errorf("circular reference detected: cannot move folder into its own descendant")
			}
		}
	}

	// Update folder fields
	if cmd.Name != nil {
		folder.UpdateName(*cmd.Name)
	}

	if cmd.Description != nil {
		folder.SetDescription(*cmd.Description)
	}

	if cmd.Visibility != nil {
		folder.SetVisibility(*cmd.Visibility)
	}

	if cmd.ParentID != nil {
		// Handle moving to root folder
		if *cmd.ParentID == fileentities.FolderID(uuid.Nil) {
			folder.SetParentID(nil)
		} else {
			folder.SetParentID(cmd.ParentID)
		}
	}

	// Save updated folder
	err = h.folderRepo.Update(ctx, folder)
	if err != nil {
		return nil, fmt.Errorf("failed to update folder: %w", err)
	}

	return &UpdateFolderResult{Folder: folder}, nil
}