package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type CreateFolderCommand struct {
	UserID      uuid.UUID
	TeamID      *uuid.UUID
	ParentID    *fileentities.FolderID
	Name        string
	Description string
	Visibility  fileentities.Visibility
}

type CreateFolderResult struct {
	Folder *fileentities.Folder
}

type Create<PERSON><PERSON>erHandler interface {
	Handle(ctx context.Context, cmd *CreateFolderCommand) (*CreateFolderResult, error)
}

type createFolderHandler struct {
	folderRepo filerepositories.FolderRepository
	auditPort  ports.AuditPort
}

func NewCreateFolderHandler(
	folderRepo filerepositories.FolderRepository,
	auditPort ports.AuditPort,
) CreateFolderHandler {
	return &createFolderHandler{
		folderRepo: folderRepo,
		auditPort:  auditPort,
	}
}

func (h *createFolderHandler) Handle(ctx context.Context, cmd *CreateFolderCommand) (*CreateFolderResult, error) {
	// Validate input
	if cmd.Name == "" {
		return nil, fmt.Errorf("folder name is required")
	}

	// Generate folder path
	var path string
	if cmd.ParentID != nil {
		// Get parent folder to build path
		parentFolder, err := h.folderRepo.GetByID(ctx, *cmd.ParentID)
		if err != nil {
			return nil, fmt.Errorf("failed to get parent folder: %w", err)
		}
		if parentFolder == nil {
			return nil, fmt.Errorf("parent folder not found")
		}
		path = fmt.Sprintf("%s/%s", parentFolder.Path(), cmd.Name)
	} else {
		path = cmd.Name
	}

	// Create folder entity
	folder := fileentities.NewFolder(cmd.Name, path, cmd.UserID)

	if cmd.TeamID != nil {
		folder.SetTeamID(*cmd.TeamID)
	}

	if cmd.ParentID != nil {
		folder.SetParentID(cmd.ParentID)
	}

	if cmd.Description != "" {
		folder.SetDescription(cmd.Description)
	}

	if cmd.Visibility != "" {
		folder.SetVisibility(cmd.Visibility)
	}

	// Save folder
	err := h.folderRepo.Create(ctx, folder)
	if err != nil {
		return nil, fmt.Errorf("failed to create folder: %w", err)
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFolderCreate,
		"folder_create",
		fmt.Sprintf("Folder created: %s", cmd.Name),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(uuid.UUID(folder.ID()))
	auditLog.SetResourceType("folder")
	auditLog.SetMetadata("folder_name", cmd.Name)
	auditLog.SetMetadata("folder_path", path)
	auditLog.SetMetadata("visibility", string(folder.Visibility()))

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &CreateFolderResult{Folder: folder}, nil
}
