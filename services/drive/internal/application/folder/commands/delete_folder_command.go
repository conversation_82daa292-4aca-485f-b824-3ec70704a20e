package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type DeleteFolderCommand struct {
	FolderID uuid.UUID
	UserID   uuid.UUID
}

type DeleteFolderResult struct {
	Success bool
}

type DeleteFolderHandler interface {
	Handle(ctx context.Context, cmd *DeleteFolderCommand) (*DeleteFolderResult, error)
}

type deleteFolderHandler struct {
	folderRepo filerepositories.FolderRepository
	auditPort  ports.AuditPort
}

func NewDeleteFolderHandler(
	folderRepo filerepositories.FolderRepository,
	auditPort ports.AuditPort,
) DeleteFolderHandler {
	return &deleteFolderHandler{
		folderRepo: folderRepo,
		auditPort:  auditPort,
	}
}

func (h *deleteFolderHandler) Handle(ctx context.Context, cmd *DeleteFolderCommand) (*DeleteFolderResult, error) {
	folderID := fileentities.FolderID(cmd.FolderID)

	// Get folder
	folder, err := h.folderRepo.GetByID(ctx, folderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	if folder == nil {
		return nil, fmt.Errorf("folder not found")
	}

	if folder.IsDeleted() {
		return &DeleteFolderResult{Success: true}, nil
	}

	// Validate folder ownership
	if !folder.IsOwnedBy(cmd.UserID) {
		return nil, fmt.Errorf("access denied: user does not own this folder")
	}

	// Check if folder is empty (contains no files or subfolders)
	hasFiles, err := h.folderRepo.HasFiles(ctx, folderID)
	if err != nil {
		return nil, fmt.Errorf("failed to check folder contents: %w", err)
	}

	if hasFiles {
		return nil, fmt.Errorf("cannot delete folder: folder is not empty")
	}

	hasSubfolders, err := h.folderRepo.HasSubfolders(ctx, folderID)
	if err != nil {
		return nil, fmt.Errorf("failed to check folder contents: %w", err)
	}

	if hasSubfolders {
		return nil, fmt.Errorf("cannot delete folder: folder contains subfolders")
	}

	// Soft delete folder
	folder.Delete()
	err = h.folderRepo.Update(ctx, folder)
	if err != nil {
		return nil, fmt.Errorf("failed to delete folder: %w", err)
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFolderDelete,
		"folder_delete",
		fmt.Sprintf("Folder deleted: %s", folder.Name()),
	)
	auditLog.SetUserID(cmd.UserID)
	auditLog.SetResourceID(uuid.UUID(folder.ID()))
	auditLog.SetResourceType("folder")
	auditLog.SetMetadata("folder_name", folder.Name())
	auditLog.SetMetadata("folder_path", folder.Path())

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log error but don't fail the delete
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &DeleteFolderResult{Success: true}, nil
}