package commands

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
	sharingentities "github.com/swork-team/platform/services/drive/internal/domain/sharing/entities"
	sharingservices "github.com/swork-team/platform/services/drive/internal/domain/sharing/services"
)

type ShareFileCommand struct {
	FileID     uuid.UUID
	SharedWith uuid.UUID
	SharedBy   uuid.UUID
	Permission sharingentities.Permission
	ExpiresAt  *time.Time
}

type ShareFileResult struct {
	Share *sharingentities.FileShare
}

type ShareFileHandler interface {
	Handle(ctx context.Context, cmd *ShareFileCommand) (*ShareFileResult, error)
}

type shareFileHandler struct {
	fileService    fileservices.FileService
	sharingService sharingservices.SharingService
	auditPort      ports.AuditPort
}

func NewShareFileHandler(
	fileService fileservices.FileService,
	sharingService sharingservices.SharingService,
	auditPort ports.AuditPort,
) ShareFileHandler {
	return &shareFileHandler{
		fileService:    fileService,
		sharingService: sharingService,
		auditPort:      auditPort,
	}
}

func (h *shareFileHandler) Handle(ctx context.Context, cmd *ShareFileCommand) (*ShareFileResult, error) {
	fileID := fileentities.FileID(cmd.FileID)

	// Validate file ownership
	err := h.fileService.ValidateFileOwnership(ctx, fileID, cmd.SharedBy)
	if err != nil {
		return nil, fmt.Errorf("access denied: %w", err)
	}

	// Share file
	share, err := h.sharingService.ShareFile(ctx, fileID, cmd.SharedWith, cmd.SharedBy, cmd.Permission)
	if err != nil {
		return nil, fmt.Errorf("failed to share file: %w", err)
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileShare,
		"file_share",
		fmt.Sprintf("File shared with user %s", cmd.SharedWith.String()),
	)
	auditLog.SetUserID(cmd.SharedBy)
	auditLog.SetResourceID(cmd.FileID)
	auditLog.SetResourceType("file")
	auditLog.SetMetadata("shared_with", cmd.SharedWith.String())
	auditLog.SetMetadata("permission", string(cmd.Permission))

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log error but don't fail the share
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	return &ShareFileResult{Share: share}, nil
}
