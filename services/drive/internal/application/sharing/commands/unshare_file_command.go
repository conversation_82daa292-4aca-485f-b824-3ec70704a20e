package commands

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
	sharingrepositories "github.com/swork-team/platform/services/drive/internal/domain/sharing/repositories"
)

type UnshareFileCommand struct {
	FileID      uuid.UUID
	SharedWith  uuid.UUID
	CurrentUser uuid.UUID
}

type UnshareFileResult struct {
	Success bool
}

type UnshareFileHandler interface {
	Handle(ctx context.Context, cmd *UnshareFileCommand) (*UnshareFileResult, error)
}

type unshareFileHandler struct {
	sharingRepo      sharingrepositories.SharingRepository
	accessControlSvc fileservices.AccessControlService
	auditPort        ports.AuditPort
}

func NewUnshareFileHandler(
	sharingRepo sharingrepositories.SharingRepository,
	accessControlSvc fileservices.AccessControlService,
	auditPort ports.AuditPort,
) UnshareFileHandler {
	return &unshareFileHandler{
		sharingRepo:      sharingRepo,
		accessControlSvc: accessControlSvc,
		auditPort:        auditPort,
	}
}

func (h *unshareFileHandler) Handle(ctx context.Context, cmd *UnshareFileCommand) (*UnshareFileResult, error) {
	// Validate access - only file owner or admin can unshare
	_, err := h.accessControlSvc.ValidateFileAccess(
		ctx,
		cmd.CurrentUser,
		[]string{}, // TODO: Get user teams
		fileentities.FileID(cmd.FileID),
		fileservices.AccessOperationShare, // Need share permission to unshare
	)
	if err != nil {
		return nil, fmt.Errorf("access denied: %w", err)
	}

	// Get existing share
	shares, err := h.sharingRepo.GetByFileIDAndUser(ctx, fileentities.FileID(cmd.FileID), cmd.SharedWith)
	if err != nil {
		return nil, fmt.Errorf("failed to get file shares: %w", err)
	}

	if len(shares) == 0 {
		return nil, fmt.Errorf("no share found for this user")
	}

	// Delete all shares for this user (in case of multiple shares)
	for _, share := range shares {
		err = h.sharingRepo.DeleteFileShare(ctx, share.ID())
		if err != nil {
			return nil, fmt.Errorf("failed to delete share: %w", err)
		}
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileUnshare,
		"file_unshare",
		"File unshared with user",
	)
	auditLog.SetUserID(cmd.CurrentUser)
	auditLog.SetResourceID(cmd.FileID)
	auditLog.SetResourceType("file")
	auditLog.SetMetadata("shared_with", cmd.SharedWith.String())
	auditLog.SetMetadata("shares_removed", len(shares))

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log but don't fail - audit logging failure should not stop the operation
	}

	return &UnshareFileResult{Success: true}, nil
}
