package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
	sharingentities "github.com/swork-team/platform/services/drive/internal/domain/sharing/entities"
	sharingrepositories "github.com/swork-team/platform/services/drive/internal/domain/sharing/repositories"
)

type ListFileSharesQuery struct {
	FileID      uuid.UUID
	CurrentUser uuid.UUID
}

type ListFileSharesResult struct {
	Shares []*sharingentities.FileShare
}

type ListFileSharesHandler interface {
	Handle(ctx context.Context, query *ListFileSharesQuery) (*ListFileSharesResult, error)
}

type listFileSharesHandler struct {
	sharingRepo      sharingrepositories.SharingRepository
	accessControlSvc fileservices.AccessControlService
	auditPort        ports.AuditPort
}

func NewListFileSharesHandler(
	sharingRepo sharingrepositories.SharingRepository,
	accessControlSvc fileservices.AccessControlService,
	auditPort ports.AuditPort,
) ListFileSharesHandler {
	return &listFileSharesHandler{
		sharingRepo:      sharingRepo,
		accessControlSvc: accessControlSvc,
		auditPort:        auditPort,
	}
}

func (h *listFileSharesHandler) Handle(ctx context.Context, query *ListFileSharesQuery) (*ListFileSharesResult, error) {
	// Validate access - only file owner or users with share permission can list shares
	_, err := h.accessControlSvc.ValidateFileAccess(
		ctx,
		query.CurrentUser,
		[]string{}, // TODO: Get user teams
		fileentities.FileID(query.FileID),
		fileservices.AccessOperationRead, // Need read permission to see shares
	)
	if err != nil {
		return nil, fmt.Errorf("access denied: %w", err)
	}

	// Get shares for the file
	shares, err := h.sharingRepo.GetFileSharesByFileID(ctx, fileentities.FileID(query.FileID))
	if err != nil {
		return nil, fmt.Errorf("failed to get file shares: %w", err)
	}

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileRead,
		"file_shares_list",
		"File shares listed",
	)
	auditLog.SetUserID(query.CurrentUser)
	auditLog.SetResourceID(query.FileID)
	auditLog.SetResourceType("file")
	auditLog.SetMetadata("shares_count", len(shares))

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log but don't fail - audit logging failure should not stop the operation
	}

	return &ListFileSharesResult{Shares: shares}, nil
}
