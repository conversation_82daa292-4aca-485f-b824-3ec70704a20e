package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/helpers"
)

type ListSharedFilesQuery struct {
	UserID uuid.UUID
	Offset int
	Limit  int
}

type ListSharedFilesResult struct {
	Files      []*fileentities.File
	TotalCount int64
}

type ListSharedFilesHandler interface {
	Handle(ctx context.Context, query *ListSharedFilesQuery) (*ListSharedFilesResult, error)
}

type listSharedFilesHandler struct {
	fileRepo  filerepositories.FileRepository
	auditPort ports.AuditPort
}

func NewListSharedFilesHandler(
	fileRepo filerepositories.FileRepository,
	auditPort ports.AuditPort,
) ListSharedFilesHandler {
	return &listSharedFilesHandler{
		fileRepo:  fileRepo,
		auditPort: auditPort,
	}
}

func (h *listSharedFilesHandler) Handle(ctx context.Context, query *ListSharedFilesQuery) (*ListSharedFilesResult, error) {
	// Validate pagination parameters
	if err := helpers.ValidatePaginationParams(query.Offset, query.Limit); err != nil {
		return nil, fmt.Errorf("invalid pagination parameters: %w", err)
	}

	if query.Limit <= 0 {
		query.Limit = 20
	}

	if query.Limit > 100 {
		query.Limit = 100
	}

	// Get shared files
	files, err := h.fileRepo.GetSharedWithUser(ctx, query.UserID, query.Offset, query.Limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get shared files: %w", err)
	}

	// For simplicity, we'll use the file count as total count
	// In a real implementation, you'd have a separate count query
	totalCount := int64(len(files))

	// Create audit log
	auditLog := auditentities.NewAuditLog(
		auditentities.EventTypeFileRead,
		"shared_files_list",
		"Shared files listed",
	)
	auditLog.SetUserID(query.UserID)
	auditLog.SetMetadata("files_count", len(files))
	auditLog.SetMetadata("offset", query.Offset)
	auditLog.SetMetadata("limit", query.Limit)

	err = h.auditPort.LogEvent(ctx, auditLog)
	if err != nil {
		// Log but don't fail - audit logging failure should not stop the operation
	}

	return &ListSharedFilesResult{
		Files:      files,
		TotalCount: totalCount,
	}, nil
}
