package repositories

import (
	"context"

	"github.com/google/uuid"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/sharing/entities"
)

type SharingRepository interface {
	CreateFileShare(ctx context.Context, share *entities.FileShare) error
	CreateFolderShare(ctx context.Context, share *entities.FolderShare) error
	GetFileShare(ctx context.Context, id entities.ShareID) (*entities.FileShare, error)
	GetFolderShare(ctx context.Context, id entities.ShareID) (*entities.FolderShare, error)
	GetFileSharesByFileID(ctx context.Context, fileID fileentities.FileID) ([]*entities.FileShare, error)
	GetFolderSharesByFolderID(ctx context.Context, folderID fileentities.FolderID) ([]*entities.FolderShare, error)
	GetFileSharesByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.FileShare, error)
	GetFolderSharesByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.FolderShare, error)
	GetFileShareByFileAndUser(ctx context.Context, fileID fileentities.FileID, userID uuid.UUID) (*entities.FileShare, error)
	GetFolderShareByFolderAndUser(ctx context.Context, folderID fileentities.FolderID, userID uuid.UUID) (*entities.FolderShare, error)
	GetByFileIDAndUser(ctx context.Context, fileID fileentities.FileID, userID uuid.UUID) ([]*entities.FileShare, error)
	UpdateFileShare(ctx context.Context, share *entities.FileShare) error
	UpdateFolderShare(ctx context.Context, share *entities.FolderShare) error
	DeleteFileShare(ctx context.Context, id entities.ShareID) error
	DeleteFolderShare(ctx context.Context, id entities.ShareID) error
	DeleteFileSharesByFileID(ctx context.Context, fileID fileentities.FileID) error
	DeleteFolderSharesByFolderID(ctx context.Context, folderID fileentities.FolderID) error
	GetExpiredShares(ctx context.Context, limit int) ([]*entities.FileShare, []*entities.FolderShare, error)
}
