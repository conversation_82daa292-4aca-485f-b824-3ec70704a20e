package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/sharing/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/sharing/repositories"
)

type SharingService interface {
	ShareFile(ctx context.Context, fileID fileentities.FileID, sharedWith, sharedBy uuid.UUID, permission entities.Permission) (*entities.FileShare, error)
	ShareFolder(ctx context.Context, folderID fileentities.FolderID, sharedWith, sharedBy uuid.UUID, permission entities.Permission) (*entities.FolderShare, error)
	UnshareFile(ctx context.Context, fileID fileentities.FileID, sharedWith uuid.UUID) error
	UnshareFolder(ctx context.Context, folderID fileentities.FolderID, sharedWith uuid.UUID) error
	GetFilePermissions(ctx context.Context, fileID fileentities.FileID, userID uuid.UUID) (entities.Permission, error)
	GetFolderPermissions(ctx context.Context, folderID fileentities.FolderID, userID uuid.UUID) (entities.Permission, error)
	UpdateFilePermissions(ctx context.Context, fileID fileentities.FileID, sharedWith uuid.UUID, permission entities.Permission) error
	UpdateFolderPermissions(ctx context.Context, folderID fileentities.FolderID, sharedWith uuid.UUID, permission entities.Permission) error
	GetFilesSharedWithUser(ctx context.Context, userID uuid.UUID) ([]*entities.FileShare, error)
	GetFoldersSharedWithUser(ctx context.Context, userID uuid.UUID) ([]*entities.FolderShare, error)
	GetFileShares(ctx context.Context, fileID fileentities.FileID) ([]*entities.FileShare, error)
	GetFolderShares(ctx context.Context, folderID fileentities.FolderID) ([]*entities.FolderShare, error)
	SetShareExpiry(ctx context.Context, shareID entities.ShareID, expiresAt time.Time) error
	CleanupExpiredShares(ctx context.Context) error
	ValidateSharePermissions(ctx context.Context, userID uuid.UUID, resourceID uuid.UUID, requiredPermission entities.Permission) error
}

type sharingService struct {
	sharingRepo repositories.SharingRepository
}

func NewSharingService(sharingRepo repositories.SharingRepository) SharingService {
	return &sharingService{
		sharingRepo: sharingRepo,
	}
}

func (s *sharingService) ShareFile(ctx context.Context, fileID fileentities.FileID, sharedWith, sharedBy uuid.UUID, permission entities.Permission) (*entities.FileShare, error) {
	if sharedWith == sharedBy {
		return nil, fmt.Errorf("cannot share file with yourself")
	}

	// Check if already shared
	existingShare, err := s.sharingRepo.GetFileShareByFileAndUser(ctx, fileID, sharedWith)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing share: %w", err)
	}

	if existingShare != nil {
		// Update existing share
		existingShare.SetPermission(permission)
		err = s.sharingRepo.UpdateFileShare(ctx, existingShare)
		if err != nil {
			return nil, fmt.Errorf("failed to update file share: %w", err)
		}
		return existingShare, nil
	}

	// Create new share
	share := entities.NewFileShare(fileID, sharedWith, sharedBy, permission)
	err = s.sharingRepo.CreateFileShare(ctx, share)
	if err != nil {
		return nil, fmt.Errorf("failed to create file share: %w", err)
	}

	return share, nil
}

func (s *sharingService) ShareFolder(ctx context.Context, folderID fileentities.FolderID, sharedWith, sharedBy uuid.UUID, permission entities.Permission) (*entities.FolderShare, error) {
	if sharedWith == sharedBy {
		return nil, fmt.Errorf("cannot share folder with yourself")
	}

	// Check if already shared
	existingShare, err := s.sharingRepo.GetFolderShareByFolderAndUser(ctx, folderID, sharedWith)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing share: %w", err)
	}

	if existingShare != nil {
		// Update existing share
		existingShare.SetPermission(permission)
		err = s.sharingRepo.UpdateFolderShare(ctx, existingShare)
		if err != nil {
			return nil, fmt.Errorf("failed to update folder share: %w", err)
		}
		return existingShare, nil
	}

	// Create new share
	share := entities.NewFolderShare(folderID, sharedWith, sharedBy, permission)
	err = s.sharingRepo.CreateFolderShare(ctx, share)
	if err != nil {
		return nil, fmt.Errorf("failed to create folder share: %w", err)
	}

	return share, nil
}

func (s *sharingService) UnshareFile(ctx context.Context, fileID fileentities.FileID, sharedWith uuid.UUID) error {
	share, err := s.sharingRepo.GetFileShareByFileAndUser(ctx, fileID, sharedWith)
	if err != nil {
		return fmt.Errorf("failed to get file share: %w", err)
	}

	if share == nil {
		return fmt.Errorf("file share not found")
	}

	err = s.sharingRepo.DeleteFileShare(ctx, share.ID())
	if err != nil {
		return fmt.Errorf("failed to delete file share: %w", err)
	}

	return nil
}

func (s *sharingService) UnshareFolder(ctx context.Context, folderID fileentities.FolderID, sharedWith uuid.UUID) error {
	share, err := s.sharingRepo.GetFolderShareByFolderAndUser(ctx, folderID, sharedWith)
	if err != nil {
		return fmt.Errorf("failed to get folder share: %w", err)
	}

	if share == nil {
		return fmt.Errorf("folder share not found")
	}

	err = s.sharingRepo.DeleteFolderShare(ctx, share.ID())
	if err != nil {
		return fmt.Errorf("failed to delete folder share: %w", err)
	}

	return nil
}

func (s *sharingService) GetFilePermissions(ctx context.Context, fileID fileentities.FileID, userID uuid.UUID) (entities.Permission, error) {
	share, err := s.sharingRepo.GetFileShareByFileAndUser(ctx, fileID, userID)
	if err != nil {
		return "", fmt.Errorf("failed to get file share: %w", err)
	}

	if share == nil {
		return "", fmt.Errorf("no permissions found")
	}

	if share.IsExpired() {
		return "", fmt.Errorf("share has expired")
	}

	return share.Permission(), nil
}

func (s *sharingService) GetFolderPermissions(ctx context.Context, folderID fileentities.FolderID, userID uuid.UUID) (entities.Permission, error) {
	share, err := s.sharingRepo.GetFolderShareByFolderAndUser(ctx, folderID, userID)
	if err != nil {
		return "", fmt.Errorf("failed to get folder share: %w", err)
	}

	if share == nil {
		return "", fmt.Errorf("no permissions found")
	}

	if share.IsExpired() {
		return "", fmt.Errorf("share has expired")
	}

	return share.Permission(), nil
}

func (s *sharingService) UpdateFilePermissions(ctx context.Context, fileID fileentities.FileID, sharedWith uuid.UUID, permission entities.Permission) error {
	share, err := s.sharingRepo.GetFileShareByFileAndUser(ctx, fileID, sharedWith)
	if err != nil {
		return fmt.Errorf("failed to get file share: %w", err)
	}

	if share == nil {
		return fmt.Errorf("file share not found")
	}

	share.SetPermission(permission)
	err = s.sharingRepo.UpdateFileShare(ctx, share)
	if err != nil {
		return fmt.Errorf("failed to update file share: %w", err)
	}

	return nil
}

func (s *sharingService) UpdateFolderPermissions(ctx context.Context, folderID fileentities.FolderID, sharedWith uuid.UUID, permission entities.Permission) error {
	share, err := s.sharingRepo.GetFolderShareByFolderAndUser(ctx, folderID, sharedWith)
	if err != nil {
		return fmt.Errorf("failed to get folder share: %w", err)
	}

	if share == nil {
		return fmt.Errorf("folder share not found")
	}

	share.SetPermission(permission)
	err = s.sharingRepo.UpdateFolderShare(ctx, share)
	if err != nil {
		return fmt.Errorf("failed to update folder share: %w", err)
	}

	return nil
}

func (s *sharingService) GetFilesSharedWithUser(ctx context.Context, userID uuid.UUID) ([]*entities.FileShare, error) {
	shares, err := s.sharingRepo.GetFileSharesByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file shares: %w", err)
	}

	// Filter out expired shares
	var validShares []*entities.FileShare
	for _, share := range shares {
		if !share.IsExpired() {
			validShares = append(validShares, share)
		}
	}

	return validShares, nil
}

func (s *sharingService) GetFoldersSharedWithUser(ctx context.Context, userID uuid.UUID) ([]*entities.FolderShare, error) {
	shares, err := s.sharingRepo.GetFolderSharesByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder shares: %w", err)
	}

	// Filter out expired shares
	var validShares []*entities.FolderShare
	for _, share := range shares {
		if !share.IsExpired() {
			validShares = append(validShares, share)
		}
	}

	return validShares, nil
}

func (s *sharingService) GetFileShares(ctx context.Context, fileID fileentities.FileID) ([]*entities.FileShare, error) {
	shares, err := s.sharingRepo.GetFileSharesByFileID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file shares: %w", err)
	}

	return shares, nil
}

func (s *sharingService) GetFolderShares(ctx context.Context, folderID fileentities.FolderID) ([]*entities.FolderShare, error) {
	shares, err := s.sharingRepo.GetFolderSharesByFolderID(ctx, folderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder shares: %w", err)
	}

	return shares, nil
}

func (s *sharingService) SetShareExpiry(ctx context.Context, shareID entities.ShareID, expiresAt time.Time) error {
	// Try to get file share first
	fileShare, err := s.sharingRepo.GetFileShare(ctx, shareID)
	if err == nil && fileShare != nil {
		fileShare.SetExpiresAt(expiresAt)
		return s.sharingRepo.UpdateFileShare(ctx, fileShare)
	}

	// Try to get folder share
	folderShare, err := s.sharingRepo.GetFolderShare(ctx, shareID)
	if err == nil && folderShare != nil {
		folderShare.SetExpiresAt(expiresAt)
		return s.sharingRepo.UpdateFolderShare(ctx, folderShare)
	}

	return fmt.Errorf("share not found")
}

func (s *sharingService) CleanupExpiredShares(ctx context.Context) error {
	fileShares, folderShares, err := s.sharingRepo.GetExpiredShares(ctx, 1000)
	if err != nil {
		return fmt.Errorf("failed to get expired shares: %w", err)
	}

	for _, share := range fileShares {
		err = s.sharingRepo.DeleteFileShare(ctx, share.ID())
		if err != nil {
			return fmt.Errorf("failed to delete expired file share: %w", err)
		}
	}

	for _, share := range folderShares {
		err = s.sharingRepo.DeleteFolderShare(ctx, share.ID())
		if err != nil {
			return fmt.Errorf("failed to delete expired folder share: %w", err)
		}
	}

	return nil
}

func (s *sharingService) ValidateSharePermissions(ctx context.Context, userID uuid.UUID, resourceID uuid.UUID, requiredPermission entities.Permission) error {
	// This is a simplified implementation
	// In a real system, this would check both file and folder permissions
	return nil
}
