package entities

import (
	"time"

	"github.com/google/uuid"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
)

type ShareID uuid.UUID

func NewShareID() ShareID {
	return ShareID(uuid.New())
}

func (id ShareID) String() string {
	return uuid.UUID(id).String()
}

type Permission string

const (
	PermissionRead  Permission = "read"
	PermissionWrite Permission = "write"
	PermissionAdmin Permission = "admin"
)

type FileShare struct {
	id         ShareID
	fileID     fileentities.FileID
	sharedWith uuid.UUID
	sharedBy   uuid.UUID
	permission Permission
	createdAt  time.Time
	updatedAt  time.Time
	expiresAt  *time.Time
}

func NewFileShare(fileID fileentities.FileID, sharedWith, sharedBy uuid.UUID, permission Permission) *FileShare {
	return &FileShare{
		id:         NewShareID(),
		fileID:     fileID,
		sharedWith: sharedWith,
		sharedBy:   sharedBy,
		permission: permission,
		createdAt:  time.Now(),
		updatedAt:  time.Now(),
	}
}

func (fs *FileShare) ID() ShareID {
	return fs.id
}

func (fs *FileShare) FileID() fileentities.FileID {
	return fs.fileID
}

func (fs *FileShare) SharedWith() uuid.UUID {
	return fs.sharedWith
}

func (fs *FileShare) SharedBy() uuid.UUID {
	return fs.sharedBy
}

func (fs *FileShare) Permission() Permission {
	return fs.permission
}

func (fs *FileShare) CreatedAt() time.Time {
	return fs.createdAt
}

func (fs *FileShare) UpdatedAt() time.Time {
	return fs.updatedAt
}

func (fs *FileShare) ExpiresAt() *time.Time {
	return fs.expiresAt
}

func (fs *FileShare) SetPermission(permission Permission) {
	fs.permission = permission
	fs.updatedAt = time.Now()
}

func (fs *FileShare) SetExpiresAt(expiresAt time.Time) {
	fs.expiresAt = &expiresAt
	fs.updatedAt = time.Now()
}

func (fs *FileShare) IsExpired() bool {
	if fs.expiresAt == nil {
		return false
	}
	return time.Now().After(*fs.expiresAt)
}

func (fs *FileShare) CanRead() bool {
	return !fs.IsExpired() && (fs.permission == PermissionRead || fs.permission == PermissionWrite || fs.permission == PermissionAdmin)
}

func (fs *FileShare) CanWrite() bool {
	return !fs.IsExpired() && (fs.permission == PermissionWrite || fs.permission == PermissionAdmin)
}

func (fs *FileShare) CanAdmin() bool {
	return !fs.IsExpired() && fs.permission == PermissionAdmin
}

func (fs *FileShare) IsSharedWith(userID uuid.UUID) bool {
	return fs.sharedWith == userID
}

type FolderShare struct {
	id         ShareID
	folderID   fileentities.FolderID
	sharedWith uuid.UUID
	sharedBy   uuid.UUID
	permission Permission
	createdAt  time.Time
	updatedAt  time.Time
	expiresAt  *time.Time
}

func NewFolderShare(folderID fileentities.FolderID, sharedWith, sharedBy uuid.UUID, permission Permission) *FolderShare {
	return &FolderShare{
		id:         NewShareID(),
		folderID:   folderID,
		sharedWith: sharedWith,
		sharedBy:   sharedBy,
		permission: permission,
		createdAt:  time.Now(),
		updatedAt:  time.Now(),
	}
}

func (fs *FolderShare) ID() ShareID {
	return fs.id
}

func (fs *FolderShare) FolderID() fileentities.FolderID {
	return fs.folderID
}

func (fs *FolderShare) SharedWith() uuid.UUID {
	return fs.sharedWith
}

func (fs *FolderShare) SharedBy() uuid.UUID {
	return fs.sharedBy
}

func (fs *FolderShare) Permission() Permission {
	return fs.permission
}

func (fs *FolderShare) CreatedAt() time.Time {
	return fs.createdAt
}

func (fs *FolderShare) UpdatedAt() time.Time {
	return fs.updatedAt
}

func (fs *FolderShare) ExpiresAt() *time.Time {
	return fs.expiresAt
}

func (fs *FolderShare) SetPermission(permission Permission) {
	fs.permission = permission
	fs.updatedAt = time.Now()
}

func (fs *FolderShare) SetExpiresAt(expiresAt time.Time) {
	fs.expiresAt = &expiresAt
	fs.updatedAt = time.Now()
}

func (fs *FolderShare) IsExpired() bool {
	if fs.expiresAt == nil {
		return false
	}
	return time.Now().After(*fs.expiresAt)
}

func (fs *FolderShare) CanRead() bool {
	return !fs.IsExpired() && (fs.permission == PermissionRead || fs.permission == PermissionWrite || fs.permission == PermissionAdmin)
}

func (fs *FolderShare) CanWrite() bool {
	return !fs.IsExpired() && (fs.permission == PermissionWrite || fs.permission == PermissionAdmin)
}

func (fs *FolderShare) CanAdmin() bool {
	return !fs.IsExpired() && fs.permission == PermissionAdmin
}

func (fs *FolderShare) IsSharedWith(userID uuid.UUID) bool {
	return fs.sharedWith == userID
}
