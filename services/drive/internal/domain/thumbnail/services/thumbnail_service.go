package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/repositories"
)

type ThumbnailService interface {
	// Thumbnail operations
	CreateThumbnail(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) (*entities.Thumbnail, error)
	GetThumbnailsForFile(ctx context.Context, fileID uuid.UUID) ([]*entities.Thumbnail, error)
	GetThumbnail(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) (*entities.Thumbnail, error)
	DeleteThumbnailsForFile(ctx context.Context, fileID uuid.UUID) error
	RefreshThumbnail(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) error

	// Job operations
	CreateThumbnailJob(ctx context.Context, fileID, userID uuid.UUID, sourcePath, mimeType string, sizes []entities.ThumbnailSize, formats []entities.ThumbnailFormat) (*entities.ThumbnailJob, error)
	GetJobStatus(ctx context.Context, jobID entities.JobID) (*entities.ThumbnailJob, error)
	ProcessJob(ctx context.Context, job *entities.ThumbnailJob) error
	RetryJob(ctx context.Context, jobID entities.JobID) error
	CancelJob(ctx context.Context, jobID entities.JobID) error

	// Statistics and monitoring
	GetStatistics(ctx context.Context) (map[string]interface{}, error)
	GetQueueStatistics(ctx context.Context) (map[string]interface{}, error)
	GetStorageUsage(ctx context.Context) (int64, error)

	// Maintenance operations
	CleanupExpiredThumbnails(ctx context.Context, maxAge time.Duration) (int, error)
	CleanupOldJobs(ctx context.Context, maxAge time.Duration) (int, error)
	ValidateFileForThumbnail(ctx context.Context, mimeType string, fileSize int64) error
}

type thumbnailService struct {
	thumbnailRepo repositories.ThumbnailRepository
	jobRepo       repositories.JobRepository
}

func NewThumbnailService(
	thumbnailRepo repositories.ThumbnailRepository,
	jobRepo repositories.JobRepository,
) ThumbnailService {
	return &thumbnailService{
		thumbnailRepo: thumbnailRepo,
		jobRepo:       jobRepo,
	}
}

func (s *thumbnailService) CreateThumbnail(
	ctx context.Context,
	fileID uuid.UUID,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) (*entities.Thumbnail, error) {
	// Check if thumbnail already exists
	exists, err := s.thumbnailRepo.ExistsByFileIDSizeAndFormat(ctx, fileID, size, format)
	if err != nil {
		return nil, fmt.Errorf("failed to check thumbnail existence: %w", err)
	}

	if exists {
		return s.thumbnailRepo.GetByFileIDSizeAndFormat(ctx, fileID, size, format)
	}

	// Create new thumbnail
	thumbnail := entities.NewThumbnail(fileID, size, format)

	if err := s.thumbnailRepo.Create(ctx, thumbnail); err != nil {
		return nil, fmt.Errorf("failed to create thumbnail: %w", err)
	}

	return thumbnail, nil
}

func (s *thumbnailService) GetThumbnailsForFile(ctx context.Context, fileID uuid.UUID) ([]*entities.Thumbnail, error) {
	thumbnails, err := s.thumbnailRepo.GetByFileID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnails for file: %w", err)
	}

	return thumbnails, nil
}

func (s *thumbnailService) GetThumbnail(
	ctx context.Context,
	fileID uuid.UUID,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) (*entities.Thumbnail, error) {
	thumbnail, err := s.thumbnailRepo.GetByFileIDSizeAndFormat(ctx, fileID, size, format)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnail: %w", err)
	}

	return thumbnail, nil
}

func (s *thumbnailService) DeleteThumbnailsForFile(ctx context.Context, fileID uuid.UUID) error {
	if err := s.thumbnailRepo.DeleteByFileID(ctx, fileID); err != nil {
		return fmt.Errorf("failed to delete thumbnails for file: %w", err)
	}

	return nil
}

func (s *thumbnailService) RefreshThumbnail(
	ctx context.Context,
	fileID uuid.UUID,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) error {
	// Delete existing thumbnail
	if err := s.thumbnailRepo.DeleteByFileIDAndSize(ctx, fileID, size); err != nil {
		return fmt.Errorf("failed to delete existing thumbnail: %w", err)
	}

	// Create new thumbnail
	_, err := s.CreateThumbnail(ctx, fileID, size, format)
	if err != nil {
		return fmt.Errorf("failed to create new thumbnail: %w", err)
	}

	return nil
}

func (s *thumbnailService) CreateThumbnailJob(
	ctx context.Context,
	fileID, userID uuid.UUID,
	sourcePath, mimeType string,
	sizes []entities.ThumbnailSize,
	formats []entities.ThumbnailFormat,
) (*entities.ThumbnailJob, error) {
	// Validate file for thumbnail generation
	if err := s.ValidateFileForThumbnail(ctx, mimeType, 0); err != nil {
		return nil, fmt.Errorf("file validation failed: %w", err)
	}

	// Create job
	job := entities.NewThumbnailJob(fileID, userID, sourcePath, mimeType, sizes, formats)

	// Only create in database - enqueuing to Redis is handled by infrastructure layer
	if err := s.jobRepo.Create(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to create thumbnail job: %w", err)
	}

	return job, nil
}

func (s *thumbnailService) GetJobStatus(ctx context.Context, jobID entities.JobID) (*entities.ThumbnailJob, error) {
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("failed to get job status: %w", err)
	}

	return job, nil
}

func (s *thumbnailService) ProcessJob(ctx context.Context, job *entities.ThumbnailJob) error {
	// Mark job as processing
	job.MarkAsProcessing()

	if err := s.jobRepo.Update(ctx, job); err != nil {
		return fmt.Errorf("failed to mark job as processing: %w", err)
	}

	// Create thumbnails for the job
	for _, size := range job.Sizes() {
		for _, format := range job.Formats() {
			thumbnail, err := s.CreateThumbnail(ctx, job.FileID(), size, format)
			if err != nil {
				job.MarkAsFailed(fmt.Sprintf("failed to create thumbnail %s/%s: %v", size, format, err))
				s.jobRepo.Update(ctx, job)
				return fmt.Errorf("failed to create thumbnail: %w", err)
			}

			job.AddToProcessingLog(fmt.Sprintf("Created thumbnail %s/%s: %s", size, format, thumbnail.ID().String()))
		}
	}

	// Mark job as completed
	job.MarkAsCompleted()

	if err := s.jobRepo.Update(ctx, job); err != nil {
		return fmt.Errorf("failed to mark job as completed: %w", err)
	}

	return nil
}

func (s *thumbnailService) RetryJob(ctx context.Context, jobID entities.JobID) error {
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return fmt.Errorf("failed to get job: %w", err)
	}

	if !job.ShouldRetry() {
		return fmt.Errorf("job cannot be retried")
	}

	job.MarkAsRetrying()

	if err := s.jobRepo.Update(ctx, job); err != nil {
		return fmt.Errorf("failed to mark job for retry: %w", err)
	}

	// Re-enqueue job
	if err := s.jobRepo.RequeueJob(ctx, job); err != nil {
		return fmt.Errorf("failed to re-enqueue job: %w", err)
	}

	return nil
}

func (s *thumbnailService) CancelJob(ctx context.Context, jobID entities.JobID) error {
	job, err := s.jobRepo.GetByID(ctx, jobID)
	if err != nil {
		return fmt.Errorf("failed to get job: %w", err)
	}

	if job.Status() == entities.JobStatusProcessing {
		return fmt.Errorf("cannot cancel job that is currently processing")
	}

	job.MarkAsFailed("Job cancelled by user")

	if err := s.jobRepo.Update(ctx, job); err != nil {
		return fmt.Errorf("failed to cancel job: %w", err)
	}

	return nil
}

func (s *thumbnailService) GetStatistics(ctx context.Context) (map[string]interface{}, error) {
	thumbnailStats, err := s.thumbnailRepo.GetStatistics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnail statistics: %w", err)
	}

	jobStats, err := s.jobRepo.GetStatistics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get job statistics: %w", err)
	}

	return map[string]interface{}{
		"thumbnails": thumbnailStats,
		"jobs":       jobStats,
	}, nil
}

func (s *thumbnailService) GetQueueStatistics(ctx context.Context) (map[string]interface{}, error) {
	queueLength, err := s.jobRepo.GetQueueLength(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get queue length: %w", err)
	}

	pendingCount, err := s.jobRepo.CountByStatus(ctx, entities.JobStatusPending)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending job count: %w", err)
	}

	processingCount, err := s.jobRepo.CountByStatus(ctx, entities.JobStatusProcessing)
	if err != nil {
		return nil, fmt.Errorf("failed to get processing job count: %w", err)
	}

	return map[string]interface{}{
		"queue_length":    queueLength,
		"pending_jobs":    pendingCount,
		"processing_jobs": processingCount,
	}, nil
}

func (s *thumbnailService) GetStorageUsage(ctx context.Context) (int64, error) {
	usage, err := s.thumbnailRepo.GetStorageUsage(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to get storage usage: %w", err)
	}

	return usage, nil
}

func (s *thumbnailService) CleanupExpiredThumbnails(ctx context.Context, maxAge time.Duration) (int, error) {
	maxAgeInDays := int(maxAge.Hours() / 24)

	deleted, err := s.thumbnailRepo.DeleteExpired(ctx, maxAgeInDays)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup expired thumbnails: %w", err)
	}

	return deleted, nil
}

func (s *thumbnailService) CleanupOldJobs(ctx context.Context, maxAge time.Duration) (int, error) {
	deleted, err := s.jobRepo.DeleteExpired(ctx, maxAge)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup old jobs: %w", err)
	}

	return deleted, nil
}

func (s *thumbnailService) ValidateFileForThumbnail(ctx context.Context, mimeType string, fileSize int64) error {
	// Check supported MIME types
	supportedImageTypes := []string{
		"image/jpeg",
		"image/png",
		"image/webp",
		"image/gif",
		"image/bmp",
		"image/tiff",
		"image/avif",
	}

	supportedVideoTypes := []string{
		"video/mp4",
		"video/avi",
		"video/quicktime",
		"video/x-msvideo",
		"video/x-flv",
		"video/webm",
		"video/x-matroska",
		"video/x-m4v",
	}

	isSupported := false
	for _, supportedType := range supportedImageTypes {
		if mimeType == supportedType {
			isSupported = true
			break
		}
	}

	if !isSupported {
		for _, supportedType := range supportedVideoTypes {
			if mimeType == supportedType {
				isSupported = true
				break
			}
		}
	}

	if !isSupported {
		return fmt.Errorf("unsupported MIME type for thumbnail generation: %s", mimeType)
	}

	// Check file size limits
	const maxImageSize = 100 * 1024 * 1024  // 100MB for images
	const maxVideoSize = 1024 * 1024 * 1024 // 1GB for videos

	if fileSize > 0 {
		if isImageType(mimeType) && fileSize > maxImageSize {
			return fmt.Errorf("image file too large for thumbnail generation: %d bytes", fileSize)
		}

		if isVideoType(mimeType) && fileSize > maxVideoSize {
			return fmt.Errorf("video file too large for thumbnail generation: %d bytes", fileSize)
		}
	}

	return nil
}

func isImageType(mimeType string) bool {
	imageTypes := []string{
		"image/jpeg",
		"image/png",
		"image/webp",
		"image/gif",
		"image/bmp",
		"image/tiff",
		"image/avif",
	}

	for _, imageType := range imageTypes {
		if mimeType == imageType {
			return true
		}
	}
	return false
}

func isVideoType(mimeType string) bool {
	videoTypes := []string{
		"video/mp4",
		"video/avi",
		"video/quicktime",
		"video/x-msvideo",
		"video/x-flv",
		"video/webm",
		"video/x-matroska",
		"video/x-m4v",
	}

	for _, videoType := range videoTypes {
		if mimeType == videoType {
			return true
		}
	}
	return false
}
