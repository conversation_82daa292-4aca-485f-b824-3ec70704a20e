package entities

import (
	"time"

	"github.com/google/uuid"
)

type JobID struct {
	value string
}

func NewJobID() JobID {
	return JobID{value: uuid.New().String()}
}

func JobIDFromString(id string) JobID {
	return JobID{value: id}
}

func (j JobID) String() string {
	return j.value
}

type JobPriority string

const (
	JobPriorityCritical JobPriority = "critical"
	JobPriorityHigh     JobPriority = "high"
	JobPriorityNormal   JobPriority = "normal"
	JobPriorityLow      JobPriority = "low"
)

type JobStatus string

const (
	JobStatusPending    JobStatus = "pending"
	JobStatusProcessing JobStatus = "processing"
	JobStatusCompleted  JobStatus = "completed"
	JobStatusFailed     JobStatus = "failed"
	JobStatusRetrying   JobStatus = "retrying"
	JobStatusDead       JobStatus = "dead"
)

type JobType string

const (
	JobTypeImageThumbnail JobType = "image_thumbnail"
	JobTypeVideoThumbnail JobType = "video_thumbnail"
	JobTypeBatchProcess   JobType = "batch_process"
)

type ThumbnailJob struct {
	id            JobID
	fileID        uuid.UUID
	userID        uuid.UUID
	sourcePath    string
	mimeType      string
	jobType       JobType
	priority      JobPriority
	status        JobStatus
	sizes         []ThumbnailSize
	formats       []ThumbnailFormat
	retryCount    int
	maxRetries    int
	createdAt     time.Time
	scheduledAt   time.Time
	startedAt     *time.Time
	completedAt   *time.Time
	errorMessage  string
	processingLog []string
	metadata      map[string]interface{}
}

func NewThumbnailJob(
	fileID, userID uuid.UUID,
	sourcePath, mimeType string,
	sizes []ThumbnailSize,
	formats []ThumbnailFormat,
) *ThumbnailJob {
	now := time.Now()

	jobType := JobTypeImageThumbnail
	if isVideoMimeType(mimeType) {
		jobType = JobTypeVideoThumbnail
	}

	return &ThumbnailJob{
		id:            NewJobID(),
		fileID:        fileID,
		userID:        userID,
		sourcePath:    sourcePath,
		mimeType:      mimeType,
		jobType:       jobType,
		priority:      JobPriorityNormal,
		status:        JobStatusPending,
		sizes:         sizes,
		formats:       formats,
		retryCount:    0,
		maxRetries:    3,
		createdAt:     now,
		scheduledAt:   now,
		processingLog: make([]string, 0),
		metadata:      make(map[string]interface{}),
	}
}

func (j *ThumbnailJob) ID() JobID {
	return j.id
}

func (j *ThumbnailJob) FileID() uuid.UUID {
	return j.fileID
}

func (j *ThumbnailJob) UserID() uuid.UUID {
	return j.userID
}

func (j *ThumbnailJob) SourcePath() string {
	return j.sourcePath
}

func (j *ThumbnailJob) MimeType() string {
	return j.mimeType
}

func (j *ThumbnailJob) JobType() JobType {
	return j.jobType
}

func (j *ThumbnailJob) Priority() JobPriority {
	return j.priority
}

func (j *ThumbnailJob) Status() JobStatus {
	return j.status
}

func (j *ThumbnailJob) Sizes() []ThumbnailSize {
	return j.sizes
}

func (j *ThumbnailJob) Formats() []ThumbnailFormat {
	return j.formats
}

func (j *ThumbnailJob) RetryCount() int {
	return j.retryCount
}

func (j *ThumbnailJob) MaxRetries() int {
	return j.maxRetries
}

func (j *ThumbnailJob) CreatedAt() time.Time {
	return j.createdAt
}

func (j *ThumbnailJob) ScheduledAt() time.Time {
	return j.scheduledAt
}

func (j *ThumbnailJob) StartedAt() *time.Time {
	return j.startedAt
}

func (j *ThumbnailJob) CompletedAt() *time.Time {
	return j.completedAt
}

func (j *ThumbnailJob) ErrorMessage() string {
	return j.errorMessage
}

func (j *ThumbnailJob) ProcessingLog() []string {
	return j.processingLog
}

func (j *ThumbnailJob) Metadata() map[string]interface{} {
	return j.metadata
}

func (j *ThumbnailJob) SetPriority(priority JobPriority) {
	j.priority = priority
}

func (j *ThumbnailJob) SetMaxRetries(maxRetries int) {
	j.maxRetries = maxRetries
}

func (j *ThumbnailJob) MarkAsProcessing() {
	j.status = JobStatusProcessing
	now := time.Now()
	j.startedAt = &now
	j.addToProcessingLog("Job started processing")
}

func (j *ThumbnailJob) MarkAsCompleted() {
	j.status = JobStatusCompleted
	now := time.Now()
	j.completedAt = &now
	j.addToProcessingLog("Job completed successfully")
}

func (j *ThumbnailJob) MarkAsFailed(errorMessage string) {
	j.status = JobStatusFailed
	j.errorMessage = errorMessage
	now := time.Now()
	j.completedAt = &now
	j.addToProcessingLog("Job failed: " + errorMessage)
}

func (j *ThumbnailJob) MarkAsRetrying() {
	j.status = JobStatusRetrying
	j.retryCount++
	j.scheduledAt = time.Now().Add(j.getRetryDelay())
	j.addToProcessingLog("Job marked for retry (attempt " + string(rune(j.retryCount)) + ")")
}

func (j *ThumbnailJob) MarkAsDead() {
	j.status = JobStatusDead
	now := time.Now()
	j.completedAt = &now
	j.addToProcessingLog("Job marked as dead after " + string(rune(j.retryCount)) + " retries")
}

func (j *ThumbnailJob) ShouldRetry() bool {
	return j.retryCount < j.maxRetries && j.status == JobStatusFailed
}

func (j *ThumbnailJob) IsRetryReady() bool {
	return j.status == JobStatusRetrying && time.Now().After(j.scheduledAt)
}

func (j *ThumbnailJob) GetRetryDelay() time.Duration {
	return j.getRetryDelay()
}

func (j *ThumbnailJob) getRetryDelay() time.Duration {
	// Exponential backoff: 1s, 2s, 4s, 8s, etc.
	delay := time.Duration(1<<j.retryCount) * time.Second
	if delay > 5*time.Minute {
		delay = 5 * time.Minute
	}
	return delay
}

func (j *ThumbnailJob) addToProcessingLog(message string) {
	timestamp := time.Now().Format(time.RFC3339)
	logEntry := timestamp + ": " + message
	j.processingLog = append(j.processingLog, logEntry)
}

func (j *ThumbnailJob) AddToProcessingLog(message string) {
	j.addToProcessingLog(message)
}

func (j *ThumbnailJob) SetMetadata(key string, value interface{}) {
	j.metadata[key] = value
}

func (j *ThumbnailJob) GetMetadata(key string) (interface{}, bool) {
	value, exists := j.metadata[key]
	return value, exists
}

func (j *ThumbnailJob) GetScore() int64 {
	// Score for priority queue (lower score = higher priority)
	var priorityScore int64
	switch j.priority {
	case JobPriorityCritical:
		priorityScore = 0
	case JobPriorityHigh:
		priorityScore = 1000
	case JobPriorityNormal:
		priorityScore = 2000
	case JobPriorityLow:
		priorityScore = 3000
	}

	// Add scheduled time to score
	return priorityScore + j.scheduledAt.Unix()
}

func (j *ThumbnailJob) IsExpired(ttl time.Duration) bool {
	return time.Since(j.createdAt) > ttl
}

func (j *ThumbnailJob) Duration() time.Duration {
	if j.startedAt == nil || j.completedAt == nil {
		return 0
	}
	return j.completedAt.Sub(*j.startedAt)
}

func (j *ThumbnailJob) IsProcessingTimeout(timeout time.Duration) bool {
	return j.status == JobStatusProcessing &&
		j.startedAt != nil &&
		time.Since(*j.startedAt) > timeout
}

func isVideoMimeType(mimeType string) bool {
	videoTypes := []string{
		"video/mp4",
		"video/avi",
		"video/quicktime",
		"video/x-msvideo",
		"video/x-flv",
		"video/webm",
		"video/x-matroska",
		"video/x-m4v",
	}

	for _, videoType := range videoTypes {
		if mimeType == videoType {
			return true
		}
	}
	return false
}

func GetPriorityValue(priority JobPriority) int {
	switch priority {
	case JobPriorityCritical:
		return 0
	case JobPriorityHigh:
		return 1
	case JobPriorityNormal:
		return 2
	case JobPriorityLow:
		return 3
	default:
		return 2
	}
}

func IsValidPriority(priority string) bool {
	validPriorities := []JobPriority{
		JobPriorityCritical,
		JobPriorityHigh,
		JobPriorityNormal,
		JobPriorityLow,
	}

	for _, validPriority := range validPriorities {
		if string(validPriority) == priority {
			return true
		}
	}
	return false
}

// ReconstructThumbnailJob creates a ThumbnailJob entity from database data
func ReconstructThumbnailJob(
	id JobID,
	fileID, userID uuid.UUID,
	sourcePath, mimeType string,
	jobType JobType,
	priority JobPriority,
	status JobStatus,
	sizes []ThumbnailSize,
	formats []ThumbnailFormat,
	retryCount, maxRetries int,
	createdAt, scheduledAt time.Time,
	startedAt, completedAt *time.Time,
	errorMessage string,
	processingLog []string,
	metadata map[string]interface{},
) *ThumbnailJob {
	return &ThumbnailJob{
		id:            id,
		fileID:        fileID,
		userID:        userID,
		sourcePath:    sourcePath,
		mimeType:      mimeType,
		jobType:       jobType,
		priority:      priority,
		status:        status,
		sizes:         sizes,
		formats:       formats,
		retryCount:    retryCount,
		maxRetries:    maxRetries,
		createdAt:     createdAt,
		scheduledAt:   scheduledAt,
		startedAt:     startedAt,
		completedAt:   completedAt,
		errorMessage:  errorMessage,
		processingLog: processingLog,
		metadata:      metadata,
	}
}
