package entities

import (
	"time"

	"github.com/google/uuid"
)

type ThumbnailID struct {
	value string
}

func NewThumbnailID() ThumbnailID {
	return ThumbnailID{value: uuid.New().String()}
}

func ThumbnailIDFromString(id string) ThumbnailID {
	return ThumbnailID{value: id}
}

func (t ThumbnailID) String() string {
	return t.value
}

type ThumbnailSize string

const (
	ThumbnailSizeSmall   ThumbnailSize = "small"   // 150x150
	ThumbnailSizeMedium  ThumbnailSize = "medium"  // 300x300
	ThumbnailSizeLarge   ThumbnailSize = "large"   // 600x600
	ThumbnailSizeXLarge  ThumbnailSize = "xlarge"  // 1200x1200
	ThumbnailSizePreview ThumbnailSize = "preview" // 1920x1080
)

type ThumbnailFormat string

const (
	ThumbnailFormatJPEG ThumbnailFormat = "jpeg"
	ThumbnailFormatPNG  ThumbnailFormat = "png"
	ThumbnailFormatWebP ThumbnailFormat = "webp"
	ThumbnailFormatAVIF ThumbnailFormat = "avif"
)

type ThumbnailStatus string

const (
	ThumbnailStatusPending    ThumbnailStatus = "pending"
	ThumbnailStatusProcessing ThumbnailStatus = "processing"
	ThumbnailStatusCompleted  ThumbnailStatus = "completed"
	ThumbnailStatusFailed     ThumbnailStatus = "failed"
)

type Thumbnail struct {
	id          ThumbnailID
	fileID      uuid.UUID
	size        ThumbnailSize
	format      ThumbnailFormat
	width       int
	height      int
	fileSize    int64
	storagePath string
	checksum    string
	status      ThumbnailStatus
	createdAt   time.Time
	updatedAt   time.Time
	metadata    map[string]interface{}
}

func NewThumbnail(
	fileID uuid.UUID,
	size ThumbnailSize,
	format ThumbnailFormat,
) *Thumbnail {
	return &Thumbnail{
		id:        NewThumbnailID(),
		fileID:    fileID,
		size:      size,
		format:    format,
		status:    ThumbnailStatusPending,
		createdAt: time.Now(),
		updatedAt: time.Now(),
		metadata:  make(map[string]interface{}),
	}
}

func (t *Thumbnail) ID() ThumbnailID {
	return t.id
}

func (t *Thumbnail) FileID() uuid.UUID {
	return t.fileID
}

func (t *Thumbnail) Size() ThumbnailSize {
	return t.size
}

func (t *Thumbnail) Format() ThumbnailFormat {
	return t.format
}

func (t *Thumbnail) Width() int {
	return t.width
}

func (t *Thumbnail) Height() int {
	return t.height
}

func (t *Thumbnail) FileSize() int64 {
	return t.fileSize
}

func (t *Thumbnail) StoragePath() string {
	return t.storagePath
}

func (t *Thumbnail) Checksum() string {
	return t.checksum
}

func (t *Thumbnail) Status() ThumbnailStatus {
	return t.status
}

func (t *Thumbnail) CreatedAt() time.Time {
	return t.createdAt
}

func (t *Thumbnail) UpdatedAt() time.Time {
	return t.updatedAt
}

func (t *Thumbnail) Metadata() map[string]interface{} {
	return t.metadata
}

func (t *Thumbnail) MarkAsProcessing() {
	t.status = ThumbnailStatusProcessing
	t.updatedAt = time.Now()
}

func (t *Thumbnail) MarkAsCompleted(width, height int, fileSize int64, storagePath, checksum string) {
	t.status = ThumbnailStatusCompleted
	t.width = width
	t.height = height
	t.fileSize = fileSize
	t.storagePath = storagePath
	t.checksum = checksum
	t.updatedAt = time.Now()
}

func (t *Thumbnail) MarkAsFailed() {
	t.status = ThumbnailStatusFailed
	t.updatedAt = time.Now()
}

func (t *Thumbnail) SetMetadata(key string, value interface{}) {
	t.metadata[key] = value
	t.updatedAt = time.Now()
}

func (t *Thumbnail) GetMetadata(key string) (interface{}, bool) {
	value, exists := t.metadata[key]
	return value, exists
}

func (t *Thumbnail) GetDimensions() (width, height int) {
	switch t.size {
	case ThumbnailSizeSmall:
		return 150, 150
	case ThumbnailSizeMedium:
		return 300, 300
	case ThumbnailSizeLarge:
		return 600, 600
	case ThumbnailSizeXLarge:
		return 1200, 1200
	case ThumbnailSizePreview:
		return 1920, 1080
	default:
		return 300, 300
	}
}

func (t *Thumbnail) GetQuality() int {
	switch t.size {
	case ThumbnailSizeSmall:
		return 75
	case ThumbnailSizeMedium:
		return 80
	case ThumbnailSizeLarge:
		return 85
	case ThumbnailSizeXLarge:
		return 90
	case ThumbnailSizePreview:
		return 95
	default:
		return 80
	}
}

func (t *Thumbnail) IsImageFormat() bool {
	switch t.format {
	case ThumbnailFormatJPEG, ThumbnailFormatPNG, ThumbnailFormatWebP, ThumbnailFormatAVIF:
		return true
	default:
		return false
	}
}

func (t *Thumbnail) IsCompleted() bool {
	return t.status == ThumbnailStatusCompleted
}

func (t *Thumbnail) IsFailed() bool {
	return t.status == ThumbnailStatusFailed
}

func (t *Thumbnail) IsProcessing() bool {
	return t.status == ThumbnailStatusProcessing
}

func (t *Thumbnail) IsPending() bool {
	return t.status == ThumbnailStatusPending
}

func GetSupportedSizes() []ThumbnailSize {
	return []ThumbnailSize{
		ThumbnailSizeSmall,
		ThumbnailSizeMedium,
		ThumbnailSizeLarge,
		ThumbnailSizeXLarge,
		ThumbnailSizePreview,
	}
}

func GetSupportedFormats() []ThumbnailFormat {
	return []ThumbnailFormat{
		ThumbnailFormatJPEG,
		ThumbnailFormatPNG,
		ThumbnailFormatWebP,
		ThumbnailFormatAVIF,
	}
}

func IsValidSize(size string) bool {
	for _, validSize := range GetSupportedSizes() {
		if string(validSize) == size {
			return true
		}
	}
	return false
}

func IsValidFormat(format string) bool {
	for _, validFormat := range GetSupportedFormats() {
		if string(validFormat) == format {
			return true
		}
	}
	return false
}

// ReconstructThumbnail creates a Thumbnail entity from database data
func ReconstructThumbnail(
	id ThumbnailID,
	fileID uuid.UUID,
	size ThumbnailSize,
	format ThumbnailFormat,
	width, height int,
	fileSize int64,
	storagePath, checksum string,
	status ThumbnailStatus,
	createdAt, updatedAt time.Time,
	metadata map[string]interface{},
) *Thumbnail {
	return &Thumbnail{
		id:          id,
		fileID:      fileID,
		size:        size,
		format:      format,
		width:       width,
		height:      height,
		fileSize:    fileSize,
		storagePath: storagePath,
		checksum:    checksum,
		status:      status,
		createdAt:   createdAt,
		updatedAt:   updatedAt,
		metadata:    metadata,
	}
}
