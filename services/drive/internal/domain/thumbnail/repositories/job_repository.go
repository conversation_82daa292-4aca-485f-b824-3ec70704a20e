package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
)

type JobRepository interface {
	// Create operations
	Create(ctx context.Context, job *entities.ThumbnailJob) error
	CreateBatch(ctx context.Context, jobs []*entities.ThumbnailJob) error

	// Read operations
	GetByID(ctx context.Context, id entities.JobID) (*entities.ThumbnailJob, error)
	GetByFileID(ctx context.Context, fileID uuid.UUID) ([]*entities.ThumbnailJob, error)
	GetByStatus(ctx context.Context, status entities.JobStatus, limit int) ([]*entities.ThumbnailJob, error)
	GetPendingJobs(ctx context.Context, limit int) ([]*entities.ThumbnailJob, error)
	GetRetryableJobs(ctx context.Context, limit int) ([]*entities.ThumbnailJob, error)
	GetExpiredJobs(ctx context.Context, maxAge time.Duration) ([]*entities.ThumbnailJob, error)
	GetTimeoutJobs(ctx context.Context, timeout time.Duration) ([]*entities.ThumbnailJob, error)
	GetJobsByPriority(ctx context.Context, priority entities.JobPriority, limit int) ([]*entities.ThumbnailJob, error)
	GetJobsByUser(ctx context.Context, userID uuid.UUID, limit int) ([]*entities.ThumbnailJob, error)
	GetJobsForRetry(ctx context.Context, limit int) ([]*entities.ThumbnailJob, error)

	// Update operations
	Update(ctx context.Context, job *entities.ThumbnailJob) error
	UpdateStatus(ctx context.Context, id entities.JobID, status entities.JobStatus) error
	UpdateBatch(ctx context.Context, jobs []*entities.ThumbnailJob) error
	IncrementRetryCount(ctx context.Context, id entities.JobID) error

	// Delete operations
	Delete(ctx context.Context, id entities.JobID) error
	DeleteByFileID(ctx context.Context, fileID uuid.UUID) error
	DeleteExpired(ctx context.Context, maxAge time.Duration) (int, error)
	DeleteCompleted(ctx context.Context, maxAge time.Duration) (int, error)
	DeleteBatch(ctx context.Context, ids []entities.JobID) error

	// Count operations
	Count(ctx context.Context) (int64, error)
	CountByStatus(ctx context.Context, status entities.JobStatus) (int64, error)
	CountByUser(ctx context.Context, userID uuid.UUID) (int64, error)
	CountByFileID(ctx context.Context, fileID uuid.UUID) (int64, error)

	// Queue operations
	EnqueueJob(ctx context.Context, job *entities.ThumbnailJob) error
	DequeueJob(ctx context.Context) (*entities.ThumbnailJob, error)
	RequeueJob(ctx context.Context, job *entities.ThumbnailJob) error
	GetQueueLength(ctx context.Context) (int64, error)

	// Statistics operations
	GetStatistics(ctx context.Context) (map[string]interface{}, error)
	GetThroughputStats(ctx context.Context, since time.Time) (map[string]interface{}, error)
	GetErrorStats(ctx context.Context, since time.Time) (map[string]interface{}, error)

	// Existence checks
	Exists(ctx context.Context, id entities.JobID) (bool, error)
	ExistsByFileID(ctx context.Context, fileID uuid.UUID) (bool, error)

	// Utility operations
	MarkJobsAsTimeout(ctx context.Context, timeout time.Duration) (int, error)
	CleanupOldJobs(ctx context.Context, maxAge time.Duration) (int, error)
	GetJobHistory(ctx context.Context, fileID uuid.UUID, limit int) ([]*entities.ThumbnailJob, error)
}

type JobQueryOptions struct {
	FileID      *uuid.UUID
	UserID      *uuid.UUID
	Status      *entities.JobStatus
	Priority    *entities.JobPriority
	JobType     *entities.JobType
	CreatedAt   *time.Time
	ScheduledAt *time.Time
	Limit       int
	Offset      int
	OrderBy     string
	OrderDir    string
}

type JobRepositoryQuery interface {
	Query(ctx context.Context, options JobQueryOptions) ([]*entities.ThumbnailJob, error)
	QueryWithCount(ctx context.Context, options JobQueryOptions) ([]*entities.ThumbnailJob, int64, error)
}
