package repositories

import (
	"context"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
)

type ThumbnailRepository interface {
	// Create operations
	Create(ctx context.Context, thumbnail *entities.Thumbnail) error
	CreateBatch(ctx context.Context, thumbnails []*entities.Thumbnail) error

	// Read operations
	GetByID(ctx context.Context, id entities.ThumbnailID) (*entities.Thumbnail, error)
	GetByFileID(ctx context.Context, fileID uuid.UUID) ([]*entities.Thumbnail, error)
	GetByFileIDAndSize(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize) ([]*entities.Thumbnail, error)
	GetByFileIDSizeAndFormat(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) (*entities.Thumbnail, error)
	GetByStatus(ctx context.Context, status entities.ThumbnailStatus, limit int) ([]*entities.Thumbnail, error)
	GetPendingForProcessing(ctx context.Context, limit int) ([]*entities.Thumbnail, error)
	GetExpiredThumbnails(ctx context.Context, maxAge int) ([]*entities.Thumbnail, error)

	// Update operations
	Update(ctx context.Context, thumbnail *entities.Thumbnail) error
	UpdateStatus(ctx context.Context, id entities.ThumbnailID, status entities.ThumbnailStatus) error
	UpdateBatch(ctx context.Context, thumbnails []*entities.Thumbnail) error

	// Delete operations
	Delete(ctx context.Context, id entities.ThumbnailID) error
	DeleteByFileID(ctx context.Context, fileID uuid.UUID) error
	DeleteByFileIDAndSize(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize) error
	DeleteExpired(ctx context.Context, maxAge int) (int, error)
	DeleteBatch(ctx context.Context, ids []entities.ThumbnailID) error

	// Count operations
	Count(ctx context.Context) (int64, error)
	CountByStatus(ctx context.Context, status entities.ThumbnailStatus) (int64, error)
	CountByFileID(ctx context.Context, fileID uuid.UUID) (int64, error)

	// Existence checks
	Exists(ctx context.Context, id entities.ThumbnailID) (bool, error)
	ExistsByFileIDSizeAndFormat(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) (bool, error)

	// Utility operations
	GetStatistics(ctx context.Context) (map[string]interface{}, error)
	GetStorageUsage(ctx context.Context) (int64, error)
}

type ThumbnailQueryOptions struct {
	FileID   *uuid.UUID
	Status   *entities.ThumbnailStatus
	Size     *entities.ThumbnailSize
	Format   *entities.ThumbnailFormat
	Limit    int
	Offset   int
	OrderBy  string
	OrderDir string
}

type ThumbnailRepositoryQuery interface {
	Query(ctx context.Context, options ThumbnailQueryOptions) ([]*entities.Thumbnail, error)
	QueryWithCount(ctx context.Context, options ThumbnailQueryOptions) ([]*entities.Thumbnail, int64, error)
}
