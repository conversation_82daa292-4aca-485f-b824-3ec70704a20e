package entities

import (
	"time"

	"github.com/google/uuid"
)

type FolderID uuid.UUID

func NewFolderID() FolderID {
	return FolderID(uuid.New())
}

func (id FolderID) String() string {
	return uuid.UUID(id).String()
}

type Folder struct {
	id          FolderID
	name        string
	path        string
	userID      uuid.UUID
	teamID      *uuid.UUID
	parentID    *FolderID
	visibility  Visibility
	description string
	createdAt   time.Time
	updatedAt   time.Time
	deletedAt   *time.Time
}

func NewFolder(name, path string, userID uuid.UUID) *Folder {
	return &Folder{
		id:         NewFolderID(),
		name:       name,
		path:       path,
		userID:     userID,
		visibility: VisibilityPrivate,
		createdAt:  time.Now(),
		updatedAt:  time.Now(),
	}
}

// ReconstructFolder creates a folder from database data
func ReconstructFolder(
	id FolderID,
	name, path string,
	userID uuid.UUID,
	teamID *uuid.UUID,
	parentID *FolderID,
	visibility Visibility,
	description string,
	createdAt, updatedAt time.Time,
	deletedAt *time.Time,
) *Folder {
	return &Folder{
		id:          id,
		name:        name,
		path:        path,
		userID:      userID,
		teamID:      teamID,
		parentID:    parentID,
		visibility:  visibility,
		description: description,
		createdAt:   createdAt,
		updatedAt:   updatedAt,
		deletedAt:   deletedAt,
	}
}

func (f *Folder) ID() FolderID {
	return f.id
}

func (f *Folder) Name() string {
	return f.name
}

func (f *Folder) Path() string {
	return f.path
}

func (f *Folder) UserID() uuid.UUID {
	return f.userID
}

func (f *Folder) TeamID() *uuid.UUID {
	return f.teamID
}

func (f *Folder) ParentID() *FolderID {
	return f.parentID
}

func (f *Folder) Visibility() Visibility {
	return f.visibility
}

func (f *Folder) Description() string {
	return f.description
}

func (f *Folder) CreatedAt() time.Time {
	return f.createdAt
}

func (f *Folder) UpdatedAt() time.Time {
	return f.updatedAt
}

func (f *Folder) DeletedAt() *time.Time {
	return f.deletedAt
}

func (f *Folder) IsDeleted() bool {
	return f.deletedAt != nil
}

func (f *Folder) UpdateName(name string) {
	f.name = name
	f.updatedAt = time.Now()
}

func (f *Folder) UpdatePath(path string) {
	f.path = path
	f.updatedAt = time.Now()
}

func (f *Folder) SetTeamID(teamID uuid.UUID) {
	f.teamID = &teamID
	f.updatedAt = time.Now()
}

func (f *Folder) SetParentID(parentID *FolderID) {
	f.parentID = parentID
	f.updatedAt = time.Now()
}

func (f *Folder) SetVisibility(visibility Visibility) {
	f.visibility = visibility
	f.updatedAt = time.Now()
}

func (f *Folder) SetDescription(description string) {
	f.description = description
	f.updatedAt = time.Now()
}

func (f *Folder) Delete() {
	now := time.Now()
	f.deletedAt = &now
	f.updatedAt = now
}

func (f *Folder) IsOwnedBy(userID uuid.UUID) bool {
	return f.userID == userID
}

func (f *Folder) IsInTeam(teamID uuid.UUID) bool {
	return f.teamID != nil && *f.teamID == teamID
}

func (f *Folder) IsPublic() bool {
	return f.visibility == VisibilityPublic
}

func (f *Folder) IsTeamVisible() bool {
	return f.visibility == VisibilityTeam
}

func (f *Folder) IsPrivate() bool {
	return f.visibility == VisibilityPrivate
}

func (f *Folder) CanBeAccessedBy(userID uuid.UUID, userTeamID *uuid.UUID) bool {
	if f.IsOwnedBy(userID) {
		return true
	}

	if f.IsPublic() {
		return true
	}

	if f.IsTeamVisible() && userTeamID != nil && f.IsInTeam(*userTeamID) {
		return true
	}

	return false
}
