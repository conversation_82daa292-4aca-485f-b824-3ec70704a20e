package entities

import (
	"time"

	"github.com/google/uuid"
)

type FileID uuid.UUID

func NewFileID() FileID {
	return FileID(uuid.New())
}

func (id FileID) String() string {
	return uuid.UUID(id).String()
}

type FileStatus string

const (
	FileStatusActive      FileStatus = "active"
	FileStatusProcessing  FileStatus = "processing"
	FileStatusArchived    FileStatus = "archived"
	FileStatusQuarantined FileStatus = "quarantined"
)

type Visibility string

const (
	VisibilityPrivate Visibility = "private"
	VisibilityTeam    Visibility = "team"
	VisibilityPublic  Visibility = "public"
)

type File struct {
	id         FileID
	name       string
	path       string
	size       int64
	mimeType   string
	checksum   string
	userID     uuid.UUID
	teamID     *uuid.UUID
	folderID   *FolderID
	parentID   *FileID
	status     FileStatus
	visibility Visibility
	version    int
	metadata   map[string]interface{}
	createdAt  time.Time
	updatedAt  time.Time
	deletedAt  *time.Time
}

func NewFile(name, path, mimeType, checksum string, size int64, userID uuid.UUID) *File {
	return &File{
		id:         NewFileID(),
		name:       name,
		path:       path,
		size:       size,
		mimeType:   mimeType,
		checksum:   checksum,
		userID:     userID,
		status:     FileStatusActive,
		visibility: VisibilityPrivate,
		version:    1,
		metadata:   make(map[string]interface{}),
		createdAt:  time.Now(),
		updatedAt:  time.Now(),
	}
}

func (f *File) ID() FileID {
	return f.id
}

func (f *File) Name() string {
	return f.name
}

func (f *File) Path() string {
	return f.path
}

func (f *File) Size() int64 {
	return f.size
}

func (f *File) MimeType() string {
	return f.mimeType
}

func (f *File) Checksum() string {
	return f.checksum
}

func (f *File) UserID() uuid.UUID {
	return f.userID
}

func (f *File) TeamID() *uuid.UUID {
	return f.teamID
}

func (f *File) FolderID() *FolderID {
	return f.folderID
}

func (f *File) ParentID() *FileID {
	return f.parentID
}

func (f *File) Status() FileStatus {
	return f.status
}

func (f *File) Visibility() Visibility {
	return f.visibility
}

func (f *File) Version() int {
	return f.version
}

func (f *File) Metadata() map[string]interface{} {
	return f.metadata
}

func (f *File) CreatedAt() time.Time {
	return f.createdAt
}

func (f *File) UpdatedAt() time.Time {
	return f.updatedAt
}

func (f *File) DeletedAt() *time.Time {
	return f.deletedAt
}

func (f *File) IsDeleted() bool {
	return f.deletedAt != nil
}

func (f *File) UpdateName(name string) {
	f.name = name
	f.updatedAt = time.Now()
}

func (f *File) UpdatePath(path string) {
	f.path = path
	f.updatedAt = time.Now()
}

func (f *File) SetTeamID(teamID uuid.UUID) {
	f.teamID = &teamID
	f.updatedAt = time.Now()
}

func (f *File) SetFolderID(folderID FolderID) {
	f.folderID = &folderID
	f.updatedAt = time.Now()
}

func (f *File) ClearFolderID() {
	f.folderID = nil
	f.updatedAt = time.Now()
}

func (f *File) SetParentID(parentID FileID) {
	f.parentID = &parentID
	f.updatedAt = time.Now()
}

func (f *File) SetStatus(status FileStatus) {
	f.status = status
	f.updatedAt = time.Now()
}

func (f *File) SetVisibility(visibility Visibility) {
	f.visibility = visibility
	f.updatedAt = time.Now()
}

func (f *File) IncrementVersion() {
	f.version++
	f.updatedAt = time.Now()
}

func (f *File) SetMetadata(key string, value interface{}) {
	f.metadata[key] = value
	f.updatedAt = time.Now()
}

func (f *File) GetMetadata(key string) (interface{}, bool) {
	value, exists := f.metadata[key]
	return value, exists
}

func (f *File) Delete() {
	now := time.Now()
	f.deletedAt = &now
	f.updatedAt = now
	// Modify path to avoid unique constraint conflicts when soft deleting
	f.path = f.path + ".deleted." + now.Format("20060102150405")
}

func (f *File) IsOwnedBy(userID uuid.UUID) bool {
	return f.userID == userID
}

func (f *File) IsInTeam(teamID uuid.UUID) bool {
	return f.teamID != nil && *f.teamID == teamID
}

func (f *File) IsPublic() bool {
	return f.visibility == VisibilityPublic
}

func (f *File) IsTeamVisible() bool {
	return f.visibility == VisibilityTeam
}

func (f *File) IsPrivate() bool {
	return f.visibility == VisibilityPrivate
}

func (f *File) CanBeAccessedBy(userID uuid.UUID, userTeamID *uuid.UUID) bool {
	if f.IsOwnedBy(userID) {
		return true
	}

	if f.IsPublic() {
		return true
	}

	if f.IsTeamVisible() && userTeamID != nil && f.IsInTeam(*userTeamID) {
		return true
	}

	return false
}

// ReconstructFile creates a File entity from database data
func ReconstructFile(
	id FileID,
	name, path, mimeType, checksum string,
	size int64,
	userID uuid.UUID,
	teamID *uuid.UUID,
	folderID *FolderID,
	parentID *FileID,
	status FileStatus,
	visibility Visibility,
	version int,
	metadata map[string]interface{},
	createdAt, updatedAt time.Time,
	deletedAt *time.Time,
) *File {
	file := &File{
		id:         id,
		name:       name,
		path:       path,
		size:       size,
		mimeType:   mimeType,
		checksum:   checksum,
		userID:     userID,
		teamID:     teamID,
		folderID:   folderID,
		parentID:   parentID,
		status:     status,
		visibility: visibility,
		version:    version,
		metadata:   metadata,
		createdAt:  createdAt,
		updatedAt:  updatedAt,
		deletedAt:  deletedAt,
	}
	return file
}
