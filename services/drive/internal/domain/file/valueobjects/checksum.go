package valueobjects

import (
	"crypto/sha256"
	"fmt"
	"io"
	"regexp"
)

type Checksum struct {
	value string
}

func NewChecksum(value string) (*Checksum, error) {
	if !isValidSHA256(value) {
		return nil, fmt.Errorf("invalid SHA256 checksum: %s", value)
	}

	return &Checksum{value: value}, nil
}

func GenerateChecksum(data io.Reader) (*Checksum, error) {
	hasher := sha256.New()
	if _, err := io.Copy(hasher, data); err != nil {
		return nil, fmt.Errorf("failed to generate checksum: %w", err)
	}

	checksum := fmt.Sprintf("%x", hasher.Sum(nil))
	return &Checksum{value: checksum}, nil
}

func (c *Checksum) Value() string {
	return c.value
}

func (c *Checksum) Equals(other *Checksum) bool {
	return c.value == other.value
}

func (c *Checksum) String() string {
	return c.value
}

var sha256Regex = regexp.MustCompile(`^[a-fA-F0-9]{64}$`)

func isValidSHA256(value string) bool {
	return sha256Regex.MatchString(value)
}
