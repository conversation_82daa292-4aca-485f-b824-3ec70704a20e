package valueobjects

import (
	"errors"
	"fmt"
	"path/filepath"
	"strings"
)

type FileInfo struct {
	name      string
	extension string
	mimeType  string
	size      int64
}

func NewFileInfo(name, mimeType string, size int64) (*FileInfo, error) {
	if name == "" {
		return nil, errors.New("file name cannot be empty")
	}

	if size < 0 {
		return nil, errors.New("file size cannot be negative")
	}

	if size > MaxFileSize {
		return nil, fmt.Errorf("file size exceeds maximum allowed size of %d bytes", MaxFileSize)
	}

	extension := strings.ToLower(filepath.Ext(name))
	if extension != "" && !isAllowedExtension(extension) {
		return nil, fmt.Errorf("file extension %s is not allowed", extension)
	}

	return &FileInfo{
		name:      name,
		extension: extension,
		mimeType:  mimeType,
		size:      size,
	}, nil
}

func (fi *FileInfo) Name() string {
	return fi.name
}

func (fi *FileInfo) Extension() string {
	return fi.extension
}

func (fi *FileInfo) MimeType() string {
	return fi.mimeType
}

func (fi *FileInfo) Size() int64 {
	return fi.size
}

func (fi *FileInfo) IsImage() bool {
	return strings.HasPrefix(fi.mimeType, "image/")
}

func (fi *FileInfo) IsVideo() bool {
	return strings.HasPrefix(fi.mimeType, "video/")
}

func (fi *FileInfo) IsAudio() bool {
	return strings.HasPrefix(fi.mimeType, "audio/")
}

func (fi *FileInfo) IsDocument() bool {
	return strings.HasPrefix(fi.mimeType, "application/") ||
		strings.HasPrefix(fi.mimeType, "text/")
}

func (fi *FileInfo) SupportsThumbnails() bool {
	return fi.IsImage() || fi.IsVideo() || fi.isPDF()
}

func (fi *FileInfo) isPDF() bool {
	return fi.mimeType == "application/pdf"
}

const MaxFileSize = 104857600 // 100MB

var allowedExtensions = map[string]bool{
	".jpg":  true,
	".jpeg": true,
	".png":  true,
	".gif":  true,
	".webp": true,
	".bmp":  true,
	".tiff": true,
	".svg":  true,
	".ico":  true,
	".mp4":  true,
	".avi":  true,
	".mov":  true,
	".wmv":  true,
	".flv":  true,
	".webm": true,
	".mkv":  true,
	".mp3":  true,
	".wav":  true,
	".flac": true,
	".aac":  true,
	".ogg":  true,
	".wma":  true,
	".pdf":  true,
	".doc":  true,
	".docx": true,
	".xls":  true,
	".xlsx": true,
	".ppt":  true,
	".pptx": true,
	".txt":  true,
	".csv":  true,
	".json": true,
	".xml":  true,
	".html": true,
	".css":  true,
	".js":   true,
	".ts":   true,
	".go":   true,
	".py":   true,
	".java": true,
	".cpp":  true,
	".c":    true,
	".h":    true,
	".zip":  true,
	".rar":  true,
	".7z":   true,
	".tar":  true,
	".gz":   true,
}

func isAllowedExtension(ext string) bool {
	return allowedExtensions[ext]
}

func GetAllowedExtensions() []string {
	extensions := make([]string, 0, len(allowedExtensions))
	for ext := range allowedExtensions {
		extensions = append(extensions, ext)
	}
	return extensions
}
