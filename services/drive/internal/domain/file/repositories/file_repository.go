package repositories

import (
	"context"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/file/entities"
)

type FileRepository interface {
	Create(ctx context.Context, file *entities.File) error
	GetByID(ctx context.Context, id entities.FileID) (*entities.File, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.File, error)
	GetByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.File, error)
	GetByFolderID(ctx context.Context, folderID entities.FolderID, offset, limit int) ([]*entities.File, error)
	GetSharedWithUser(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.File, error)
	Update(ctx context.Context, file *entities.File) error
	Delete(ctx context.Context, id entities.FileID) error
	Search(ctx context.Context, userID uuid.UUID, query string, offset, limit int) ([]*entities.File, error)
	GetByChecksum(ctx context.Context, checksum string) (*entities.File, error)
	GetVersions(ctx context.Context, parentID entities.FileID) ([]*entities.File, error)
	CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
	CountByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error)
	GetSizeByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
	GetSizeByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error)
	GetExpiredSessions(ctx context.Context, limit int) ([]*entities.File, error)
	// Root folder methods - get files where folderID is NULL
	GetRootFilesByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.File, error)
	GetRootFilesByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.File, error)
	CountRootFilesByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
	CountRootFilesByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error)
}

type FolderRepository interface {
	Create(ctx context.Context, folder *entities.Folder) error
	GetByID(ctx context.Context, id entities.FolderID) (*entities.Folder, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.Folder, error)
	GetByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.Folder, error)
	GetByParentID(ctx context.Context, parentID entities.FolderID, offset, limit int) ([]*entities.Folder, error)
	GetSharedWithUser(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.Folder, error)
	Update(ctx context.Context, folder *entities.Folder) error
	Delete(ctx context.Context, id entities.FolderID) error
	GetChildren(ctx context.Context, folderID entities.FolderID) ([]*entities.Folder, error)
	GetPath(ctx context.Context, folderID entities.FolderID) (string, error)
	CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
	CountByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error)
	HasFiles(ctx context.Context, folderID entities.FolderID) (bool, error)
	HasSubfolders(ctx context.Context, folderID entities.FolderID) (bool, error)
	// Root folder methods - get folders where parentID is NULL
	GetRootFoldersByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.Folder, error)
	GetRootFoldersByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.Folder, error)
	CountRootFoldersByUserID(ctx context.Context, userID uuid.UUID) (int64, error)
	CountRootFoldersByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error)
	// Circular reference detection
	WouldCreateCycle(ctx context.Context, folderID entities.FolderID, newParentID entities.FolderID) (bool, error)
}
