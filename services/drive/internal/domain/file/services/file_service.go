package services

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	"github.com/swork-team/platform/services/drive/internal/domain/file/valueobjects"
)

type FileService interface {
	ValidateFileInfo(fileInfo *valueobjects.FileInfo) error
	ValidateFileAccess(ctx context.Context, fileID entities.FileID, userID uuid.UUID, userTeams []string) error
	ValidateFileOwnership(ctx context.Context, fileID entities.FileID, userID uuid.UUID) error
	GenerateUniqueName(ctx context.Context, baseName string, userID uuid.UUID, folderID *entities.FolderID) (string, error)
	GetFileStatistics(ctx context.Context, userID uuid.UUID) (*FileStatistics, error)
	GetTeamStatistics(ctx context.Context, teamID uuid.UUID) (*FileStatistics, error)
}

type FileStatistics struct {
	TotalFiles  int64
	TotalSize   int64
	FilesByType map[string]int64
	SizeByType  map[string]int64
}

type fileService struct {
	fileRepo   repositories.FileRepository
	folderRepo repositories.FolderRepository
}

func NewFileService(fileRepo repositories.FileRepository, folderRepo repositories.FolderRepository) FileService {
	return &fileService{
		fileRepo:   fileRepo,
		folderRepo: folderRepo,
	}
}

func (s *fileService) ValidateFileInfo(fileInfo *valueobjects.FileInfo) error {
	if fileInfo == nil {
		return fmt.Errorf("file info is required")
	}

	if fileInfo.Name() == "" {
		return fmt.Errorf("file name is required")
	}

	if fileInfo.Size() <= 0 {
		return fmt.Errorf("file size must be greater than 0")
	}

	if fileInfo.Size() > valueobjects.MaxFileSize {
		return fmt.Errorf("file size exceeds maximum allowed size")
	}

	return nil
}

func (s *fileService) ValidateFileAccess(ctx context.Context, fileID entities.FileID, userID uuid.UUID, userTeams []string) error {
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return fmt.Errorf("failed to get file: %w", err)
	}

	if file == nil {
		return fmt.Errorf("file not found")
	}

	if file.IsDeleted() {
		return fmt.Errorf("file is deleted")
	}

	if file.IsOwnedBy(userID) {
		return nil
	}

	if file.IsPublic() {
		return nil
	}

	// Check team access for team-visible files
	if file.IsTeamVisible() && file.TeamID() != nil {
		fileTeamID := file.TeamID().String()
		for _, userTeam := range userTeams {
			if userTeam == fileTeamID {
				return nil
			}
		}
		return fmt.Errorf("access denied: not a member of the required team")
	}

	return fmt.Errorf("access denied")
}

func (s *fileService) ValidateFileOwnership(ctx context.Context, fileID entities.FileID, userID uuid.UUID) error {
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return fmt.Errorf("failed to get file: %w", err)
	}

	if file == nil {
		return fmt.Errorf("file not found")
	}

	if !file.IsOwnedBy(userID) {
		return fmt.Errorf("file is not owned by user")
	}

	return nil
}

func (s *fileService) GenerateUniqueName(ctx context.Context, baseName string, userID uuid.UUID, folderID *entities.FolderID) (string, error) {
	// Simple implementation - would need more sophisticated logic in production
	// to handle name conflicts by appending numbers or timestamps
	return baseName, nil
}


func (s *fileService) GetFileStatistics(ctx context.Context, userID uuid.UUID) (*FileStatistics, error) {
	totalFiles, err := s.fileRepo.CountByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file count: %w", err)
	}

	totalSize, err := s.fileRepo.GetSizeByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file size: %w", err)
	}

	return &FileStatistics{
		TotalFiles:  totalFiles,
		TotalSize:   totalSize,
		FilesByType: make(map[string]int64),
		SizeByType:  make(map[string]int64),
	}, nil
}

func (s *fileService) GetTeamStatistics(ctx context.Context, teamID uuid.UUID) (*FileStatistics, error) {
	totalFiles, err := s.fileRepo.CountByTeamID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file count: %w", err)
	}

	totalSize, err := s.fileRepo.GetSizeByTeamID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file size: %w", err)
	}

	return &FileStatistics{
		TotalFiles:  totalFiles,
		TotalSize:   totalSize,
		FilesByType: make(map[string]int64),
		SizeByType:  make(map[string]int64),
	}, nil
}
