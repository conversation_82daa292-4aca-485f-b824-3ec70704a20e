package services

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	sharingentities "github.com/swork-team/platform/services/drive/internal/domain/sharing/entities"
	sharingrepositories "github.com/swork-team/platform/services/drive/internal/domain/sharing/repositories"
)

// AccessControlService handles file access control logic
type AccessControlService interface {
	CanReadFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error)
	CanWriteFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error)
	CanDeleteFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error)
	CanShareFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error)
	ValidateFileAccess(ctx context.Context, userID uuid.UUID, userTeams []string, fileID entities.FileID, operation AccessOperation) (*entities.File, error)
	IsTeamAdmin(ctx context.Context, userID uuid.UUID, teamID string) (bool, error)
}

// AccessOperation represents different types of file operations
type AccessOperation string

const (
	AccessOperationRead   AccessOperation = "read"
	AccessOperationWrite  AccessOperation = "write"
	AccessOperationDelete AccessOperation = "delete"
	AccessOperationShare  AccessOperation = "share"
)

type accessControlService struct {
	fileRepo    filerepositories.FileRepository
	sharingRepo sharingrepositories.SharingRepository
}

// NewAccessControlService creates a new access control service
func NewAccessControlService(
	fileRepo filerepositories.FileRepository,
	sharingRepo sharingrepositories.SharingRepository,
) AccessControlService {
	return &accessControlService{
		fileRepo:    fileRepo,
		sharingRepo: sharingRepo,
	}
}

// CanReadFile checks if user can read a file
func (s *accessControlService) CanReadFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error) {
	// Owner can always read
	if file.UserID() == userID {
		return true, nil
	}

	// Team member can read if file belongs to their team
	if file.TeamID() != nil && s.isUserInTeam(userTeams, file.TeamID().String()) {
		return true, nil
	}

	// Check if file is shared with user
	shares, err := s.sharingRepo.GetByFileIDAndUser(ctx, file.ID(), userID)
	if err != nil {
		return false, fmt.Errorf("failed to check file shares: %w", err)
	}

	for _, share := range shares {
		if s.isShareActive(share) {
			return true, nil
		}
	}

	// Check if file is public (if visibility is supported)
	if visibility := s.getFileVisibility(file); visibility == "public" {
		return true, nil
	}

	return false, nil
}

// CanWriteFile checks if user can modify a file
func (s *accessControlService) CanWriteFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error) {
	// Owner can always write
	if file.UserID() == userID {
		return true, nil
	}

	// Team member with write access can modify if file belongs to their team
	if file.TeamID() != nil && s.isUserInTeam(userTeams, file.TeamID().String()) {
		// Team members have write access to team files by default
		// In a more complex system, you could implement role-based permissions here
		return true, nil
	}

	// Check if file is shared with write permissions
	shares, err := s.sharingRepo.GetByFileIDAndUser(ctx, file.ID(), userID)
	if err != nil {
		return false, fmt.Errorf("failed to check file shares: %w", err)
	}

	for _, share := range shares {
		if s.isShareActive(share) && s.hasWritePermission(share) {
			return true, nil
		}
	}

	return false, nil
}

// CanDeleteFile checks if user can delete a file
func (s *accessControlService) CanDeleteFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error) {
	// Only owner can delete files
	if file.UserID() == userID {
		return true, nil
	}

	// Team administrators can delete files
	if file.TeamID() != nil && s.isUserInTeam(userTeams, file.TeamID().String()) {
		isAdmin, err := s.IsTeamAdmin(ctx, userID, file.TeamID().String())
		if err != nil {
			return false, fmt.Errorf("failed to check team admin status: %w", err)
		}
		if isAdmin {
			return true, nil
		}
	}

	return false, nil
}

// CanShareFile checks if user can share a file
func (s *accessControlService) CanShareFile(ctx context.Context, userID uuid.UUID, userTeams []string, file *entities.File) (bool, error) {
	// Owner can always share
	if file.UserID() == userID {
		return true, nil
	}

	// Team members can share team files
	if file.TeamID() != nil && s.isUserInTeam(userTeams, file.TeamID().String()) {
		return true, nil
	}

	// Users with existing share permissions can re-share if allowed
	shares, err := s.sharingRepo.GetByFileIDAndUser(ctx, file.ID(), userID)
	if err != nil {
		return false, fmt.Errorf("failed to check file shares: %w", err)
	}

	for _, share := range shares {
		if s.isShareActive(share) && s.hasSharePermission(share) {
			return true, nil
		}
	}

	return false, nil
}

// ValidateFileAccess validates user access to a file for a specific operation
func (s *accessControlService) ValidateFileAccess(ctx context.Context, userID uuid.UUID, userTeams []string, fileID entities.FileID, operation AccessOperation) (*entities.File, error) {
	// Get the file
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	if file == nil {
		return nil, fmt.Errorf("file not found")
	}

	// Check access based on operation
	var hasAccess bool
	switch operation {
	case AccessOperationRead:
		hasAccess, err = s.CanReadFile(ctx, userID, userTeams, file)
	case AccessOperationWrite:
		hasAccess, err = s.CanWriteFile(ctx, userID, userTeams, file)
	case AccessOperationDelete:
		hasAccess, err = s.CanDeleteFile(ctx, userID, userTeams, file)
	case AccessOperationShare:
		hasAccess, err = s.CanShareFile(ctx, userID, userTeams, file)
	default:
		return nil, fmt.Errorf("unsupported access operation: %s", operation)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to check access: %w", err)
	}

	if !hasAccess {
		return nil, fmt.Errorf("access denied for operation %s", operation)
	}

	return file, nil
}

// Helper methods

// isUserInTeam checks if user is in a specific team
func (s *accessControlService) isUserInTeam(userTeams []string, teamID string) bool {
	for _, team := range userTeams {
		if team == teamID {
			return true
		}
	}
	return false
}

// getFileVisibility gets file visibility
func (s *accessControlService) getFileVisibility(file *entities.File) string {
	visibility := file.Visibility()
	return string(visibility)
}

// hasWritePermission checks if share has write permissions
func (s *accessControlService) hasWritePermission(share *sharingentities.FileShare) bool {
	return share.CanWrite()
}

// hasSharePermission checks if share allows re-sharing
func (s *accessControlService) hasSharePermission(share *sharingentities.FileShare) bool {
	// Only admin permissions allow re-sharing for security
	return share.CanAdmin()
}

// isShareActive checks if a share is currently active
func (s *accessControlService) isShareActive(share *sharingentities.FileShare) bool {
	return !share.IsExpired()
}

// IsTeamAdmin checks if a user is an administrator of a specific team
func (s *accessControlService) IsTeamAdmin(ctx context.Context, userID uuid.UUID, teamID string) (bool, error) {
	// For now, implement a basic check. In a real system, this would query
	// a team membership service or database to check user roles within the team.
	// This is a placeholder implementation that assumes the team admin information
	// is available through an external service or could be stored in the user context.

	// TODO: Integrate with actual team management service
	// For now, return false as a safe default until proper integration is implemented

	// In a real implementation, you might:
	// 1. Query a team service API
	// 2. Check a team_members table with role information
	// 3. Validate against an external identity provider
	// 4. Use JWT claims that include team roles

	// Placeholder implementation - always returns false for safety
	// This ensures that only file owners can delete files until proper
	// team admin checking is implemented
	return false, nil
}
