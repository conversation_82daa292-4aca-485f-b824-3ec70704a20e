package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
)

type AuditRepository interface {
	Create(ctx context.Context, log *entities.AuditLog) error
	CreateBatch(ctx context.Context, logs []*entities.AuditLog) error
	GetByID(ctx context.Context, id entities.AuditLogID) (*entities.AuditLog, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.AuditLog, error)
	GetByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.AuditLog, error)
	GetByResourceID(ctx context.Context, resourceID uuid.UUID, offset, limit int) ([]*entities.AuditLog, error)
	GetByEventType(ctx context.Context, eventType entities.EventType, offset, limit int) ([]*entities.AuditLog, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, offset, limit int) ([]*entities.AuditLog, error)
	GetByUserAndTimeRange(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time, offset, limit int) ([]*entities.AuditLog, error)
	GetByTeamAndTimeRange(ctx context.Context, teamID uuid.UUID, startTime, endTime time.Time, offset, limit int) ([]*entities.AuditLog, error)
	GetByIPAddress(ctx context.Context, ipAddress string, offset, limit int) ([]*entities.AuditLog, error)
	GetSecurityEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error)
	GetSystemEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error)
	GetFileEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error)
	GetFolderEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error)
	Search(ctx context.Context, query string, offset, limit int) ([]*entities.AuditLog, error)
	DeleteOldLogs(ctx context.Context, before time.Time) error
	GetStatistics(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
	GetTopUsers(ctx context.Context, startTime, endTime time.Time, limit int) ([]map[string]interface{}, error)
	GetTopActions(ctx context.Context, startTime, endTime time.Time, limit int) ([]map[string]interface{}, error)
	GetActivityByHour(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error)
	GetActivityByDay(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error)
	ValidateIntegrity(ctx context.Context, logs []*entities.AuditLog) error
}