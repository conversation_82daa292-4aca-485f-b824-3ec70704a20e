package entities

import (
	"time"

	"github.com/google/uuid"
)

type AuditLogID uuid.UUID

func NewAuditLogID() AuditLogID {
	return AuditLogID(uuid.New())
}

func (id AuditLogID) String() string {
	return uuid.UUID(id).String()
}

type EventType string

const (
	EventTypeFileCreate        EventType = "file.create"
	EventTypeFileRead          EventType = "file.read"
	EventTypeFileUpdate        EventType = "file.update"
	EventTypeFileDelete        EventType = "file.delete"
	EventTypeFileDownload      EventType = "file.download"
	EventTypeFileUpload        EventType = "file.upload"
	EventTypeFileShare         EventType = "file.share"
	EventTypeFileUnshare       EventType = "file.unshare"
	EventTypeFileCopy          EventType = "file.copy"
	EventTypeFolderCreate      EventType = "folder.create"
	EventTypeFolderRead        EventType = "folder.read"
	EventTypeFolderUpdate      EventType = "folder.update"
	EventTypeFolderDelete      EventType = "folder.delete"
	EventTypeFolderShare       EventType = "folder.share"
	EventTypeFolderUnshare     EventType = "folder.unshare"
	EventTypeUserLogin         EventType = "user.login"
	EventTypeUserLogout        EventType = "user.logout"
	EventTypeUserAccess        EventType = "user.access"
	EventTypeSystemError       EventType = "system.error"
	EventTypeSystemWarning     EventType = "system.warning"
	EventTypeSystemInfo        EventType = "system.info"
	EventTypeQueryExecuted     EventType = "query.executed"
	EventTypeDataExported      EventType = "data.exported"
	EventTypeDataImported      EventType = "data.imported"
	EventTypePermissionChanged EventType = "permission.changed"
)

type AuditLog struct {
	id           AuditLogID
	eventType    EventType
	userID       *uuid.UUID
	teamID       *uuid.UUID
	resourceID   *uuid.UUID
	resourceType string
	action       string
	description  string
	ipAddress    string
	userAgent    string
	country      string
	city         string
	latitude     *float64
	longitude    *float64
	deviceInfo   string
	sessionID    *uuid.UUID
	checksum     string
	timestamp    time.Time
	metadata     map[string]interface{}
}

func NewAuditLog(eventType EventType, action, description string) *AuditLog {
	return &AuditLog{
		id:          NewAuditLogID(),
		eventType:   eventType,
		action:      action,
		description: description,
		timestamp:   time.Now(),
		metadata:    make(map[string]interface{}),
	}
}

func (al *AuditLog) ID() AuditLogID {
	return al.id
}

func (al *AuditLog) EventType() EventType {
	return al.eventType
}

func (al *AuditLog) UserID() *uuid.UUID {
	return al.userID
}

func (al *AuditLog) TeamID() *uuid.UUID {
	return al.teamID
}

func (al *AuditLog) ResourceID() *uuid.UUID {
	return al.resourceID
}

func (al *AuditLog) ResourceType() string {
	return al.resourceType
}

func (al *AuditLog) Action() string {
	return al.action
}

func (al *AuditLog) Description() string {
	return al.description
}

func (al *AuditLog) IPAddress() string {
	return al.ipAddress
}

func (al *AuditLog) UserAgent() string {
	return al.userAgent
}

func (al *AuditLog) Country() string {
	return al.country
}

func (al *AuditLog) City() string {
	return al.city
}

func (al *AuditLog) Latitude() *float64 {
	return al.latitude
}

func (al *AuditLog) Longitude() *float64 {
	return al.longitude
}

func (al *AuditLog) DeviceInfo() string {
	return al.deviceInfo
}

func (al *AuditLog) SessionID() *uuid.UUID {
	return al.sessionID
}

func (al *AuditLog) Checksum() string {
	return al.checksum
}

func (al *AuditLog) Timestamp() time.Time {
	return al.timestamp
}

func (al *AuditLog) Metadata() map[string]interface{} {
	return al.metadata
}

func (al *AuditLog) SetUserID(userID uuid.UUID) {
	al.userID = &userID
}

func (al *AuditLog) SetTeamID(teamID uuid.UUID) {
	al.teamID = &teamID
}

func (al *AuditLog) SetResourceID(resourceID uuid.UUID) {
	al.resourceID = &resourceID
}

func (al *AuditLog) SetResourceType(resourceType string) {
	al.resourceType = resourceType
}

func (al *AuditLog) SetIPAddress(ipAddress string) {
	al.ipAddress = ipAddress
}

func (al *AuditLog) SetUserAgent(userAgent string) {
	al.userAgent = userAgent
}

func (al *AuditLog) SetLocation(country, city string, latitude, longitude *float64) {
	al.country = country
	al.city = city
	al.latitude = latitude
	al.longitude = longitude
}

func (al *AuditLog) SetDeviceInfo(deviceInfo string) {
	al.deviceInfo = deviceInfo
}

func (al *AuditLog) SetSessionID(sessionID uuid.UUID) {
	al.sessionID = &sessionID
}

func (al *AuditLog) SetChecksum(checksum string) {
	al.checksum = checksum
}

func (al *AuditLog) SetMetadata(key string, value interface{}) {
	al.metadata[key] = value
}

func (al *AuditLog) GetMetadata(key string) (interface{}, bool) {
	value, exists := al.metadata[key]
	return value, exists
}

func (al *AuditLog) IsUserEvent() bool {
	return al.userID != nil
}

func (al *AuditLog) IsTeamEvent() bool {
	return al.teamID != nil
}

func (al *AuditLog) IsResourceEvent() bool {
	return al.resourceID != nil
}

func (al *AuditLog) IsSystemEvent() bool {
	return al.eventType == EventTypeSystemError ||
		al.eventType == EventTypeSystemWarning ||
		al.eventType == EventTypeSystemInfo
}

func (al *AuditLog) IsSecurityEvent() bool {
	return al.eventType == EventTypeUserLogin ||
		al.eventType == EventTypeUserLogout ||
		al.eventType == EventTypeUserAccess ||
		al.eventType == EventTypePermissionChanged
}

func (al *AuditLog) IsFileEvent() bool {
	return al.eventType == EventTypeFileCreate ||
		al.eventType == EventTypeFileRead ||
		al.eventType == EventTypeFileUpdate ||
		al.eventType == EventTypeFileDelete ||
		al.eventType == EventTypeFileDownload ||
		al.eventType == EventTypeFileUpload ||
		al.eventType == EventTypeFileShare ||
		al.eventType == EventTypeFileUnshare
}

func (al *AuditLog) IsFolderEvent() bool {
	return al.eventType == EventTypeFolderCreate ||
		al.eventType == EventTypeFolderRead ||
		al.eventType == EventTypeFolderUpdate ||
		al.eventType == EventTypeFolderDelete ||
		al.eventType == EventTypeFolderShare ||
		al.eventType == EventTypeFolderUnshare
}
