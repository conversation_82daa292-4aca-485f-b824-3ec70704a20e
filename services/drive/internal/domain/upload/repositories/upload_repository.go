package repositories

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
)

var (
	ErrUploadSessionNotFound = errors.New("upload session not found")
)

type UploadRepository interface {
	Create(ctx context.Context, session *entities.UploadSession) error
	GetByID(ctx context.Context, id entities.UploadSessionID) (*entities.UploadSession, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.UploadSession, error)
	Update(ctx context.Context, session *entities.UploadSession) error
	Delete(ctx context.Context, id entities.UploadSessionID) error
	GetExpiredSessions(ctx context.Context, limit int) ([]*entities.UploadSession, error)
	GetActiveSessions(ctx context.Context, userID uuid.UUID) ([]*entities.UploadSession, error)
	GetCompletedSessions(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.UploadSession, error)
	CleanupExpiredSessions(ctx context.Context) error
}
