package entities

import (
	"sync"
	"time"

	"github.com/google/uuid"
)

type UploadSessionID uuid.UUID

func NewUploadSessionID() UploadSessionID {
	return UploadSessionID(uuid.New())
}

func (id UploadSessionID) String() string {
	return uuid.UUID(id).String()
}

type UploadSessionStatus string

const (
	UploadSessionStatusActive    UploadSessionStatus = "active"
	UploadSessionStatusCompleted UploadSessionStatus = "completed"
	UploadSessionStatusCanceled  UploadSessionStatus = "canceled"
	UploadSessionStatusExpired   UploadSessionStatus = "expired"
)

type UploadSession struct {
	id             UploadSessionID
	userID         uuid.UUID
	fileName       string
	fileSize       int64
	chunkSize      int64
	totalChunks    int64
	uploadedChunks map[int64]bool
	status         UploadSessionStatus
	expiresAt      time.Time
	createdAt      time.Time
	updatedAt      time.Time
	mu             sync.RWMutex // Protects uploadedChunks, status, expiresAt, and updatedAt
}

func NewUploadSession(userID uuid.UUID, fileName string, fileSize, chunkSize int64) *UploadSession {
	totalChunks := (fileSize + chunkSize - 1) / chunkSize
	return &UploadSession{
		id:             NewUploadSessionID(),
		userID:         userID,
		fileName:       fileName,
		fileSize:       fileSize,
		chunkSize:      chunkSize,
		totalChunks:    totalChunks,
		uploadedChunks: make(map[int64]bool),
		status:         UploadSessionStatusActive,
		expiresAt:      time.Now().Add(24 * time.Hour),
		createdAt:      time.Now(),
		updatedAt:      time.Now(),
	}
}

// ReconstructUploadSession creates an upload session from database data
func ReconstructUploadSession(
	id UploadSessionID,
	userID uuid.UUID,
	fileName string,
	fileSize, chunkSize, totalChunks int64,
	uploadedChunks map[int64]bool,
	status UploadSessionStatus,
	expiresAt, createdAt, updatedAt time.Time,
) *UploadSession {
	return &UploadSession{
		id:             id,
		userID:         userID,
		fileName:       fileName,
		fileSize:       fileSize,
		chunkSize:      chunkSize,
		totalChunks:    totalChunks,
		uploadedChunks: uploadedChunks,
		status:         status,
		expiresAt:      expiresAt,
		createdAt:      createdAt,
		updatedAt:      updatedAt,
	}
}

func (us *UploadSession) ID() UploadSessionID {
	return us.id
}

func (us *UploadSession) UserID() uuid.UUID {
	return us.userID
}

func (us *UploadSession) FileName() string {
	return us.fileName
}

func (us *UploadSession) FileSize() int64 {
	return us.fileSize
}

func (us *UploadSession) ChunkSize() int64 {
	return us.chunkSize
}

func (us *UploadSession) TotalChunks() int64 {
	return us.totalChunks
}

func (us *UploadSession) UploadedChunks() map[int64]bool {
	us.mu.RLock()
	defer us.mu.RUnlock()

	// Return a copy to prevent external modification
	chunks := make(map[int64]bool, len(us.uploadedChunks))
	for k, v := range us.uploadedChunks {
		chunks[k] = v
	}
	return chunks
}

func (us *UploadSession) Status() UploadSessionStatus {
	us.mu.RLock()
	defer us.mu.RUnlock()
	return us.status
}

func (us *UploadSession) ExpiresAt() time.Time {
	us.mu.RLock()
	defer us.mu.RUnlock()
	return us.expiresAt
}

func (us *UploadSession) CreatedAt() time.Time {
	return us.createdAt
}

func (us *UploadSession) UpdatedAt() time.Time {
	us.mu.RLock()
	defer us.mu.RUnlock()
	return us.updatedAt
}

func (us *UploadSession) MarkChunkUploaded(chunkIndex int64) {
	us.mu.Lock()
	defer us.mu.Unlock()
	us.uploadedChunks[chunkIndex] = true
	us.updatedAt = time.Now()
}

func (us *UploadSession) IsChunkUploaded(chunkIndex int64) bool {
	us.mu.RLock()
	defer us.mu.RUnlock()
	return us.uploadedChunks[chunkIndex]
}

func (us *UploadSession) GetUploadedChunkCount() int64 {
	us.mu.RLock()
	defer us.mu.RUnlock()
	count := int64(0)
	for _, uploaded := range us.uploadedChunks {
		if uploaded {
			count++
		}
	}
	return count
}

func (us *UploadSession) IsComplete() bool {
	us.mu.RLock()
	defer us.mu.RUnlock()
	count := int64(0)
	for _, uploaded := range us.uploadedChunks {
		if uploaded {
			count++
		}
	}
	return count == us.totalChunks
}

func (us *UploadSession) GetProgress() float64 {
	us.mu.RLock()
	defer us.mu.RUnlock()
	if us.totalChunks == 0 {
		return 0
	}
	count := int64(0)
	for _, uploaded := range us.uploadedChunks {
		if uploaded {
			count++
		}
	}
	return float64(count) / float64(us.totalChunks)
}

func (us *UploadSession) MarkCompleted() {
	us.mu.Lock()
	defer us.mu.Unlock()
	us.status = UploadSessionStatusCompleted
	us.updatedAt = time.Now()
}

func (us *UploadSession) MarkCanceled() {
	us.mu.Lock()
	defer us.mu.Unlock()
	us.status = UploadSessionStatusCanceled
	us.updatedAt = time.Now()
}

func (us *UploadSession) MarkExpired() {
	us.mu.Lock()
	defer us.mu.Unlock()
	us.status = UploadSessionStatusExpired
	us.updatedAt = time.Now()
}

func (us *UploadSession) IsExpired() bool {
	us.mu.RLock()
	defer us.mu.RUnlock()
	return time.Now().After(us.expiresAt) || us.status == UploadSessionStatusExpired
}

func (us *UploadSession) IsActive() bool {
	us.mu.RLock()
	defer us.mu.RUnlock()
	isExpired := time.Now().After(us.expiresAt) || us.status == UploadSessionStatusExpired
	return us.status == UploadSessionStatusActive && !isExpired
}

func (us *UploadSession) ExtendExpiry(duration time.Duration) {
	us.mu.Lock()
	defer us.mu.Unlock()
	us.expiresAt = time.Now().Add(duration)
	us.updatedAt = time.Now()
}

func (us *UploadSession) GetMissingChunks() []int64 {
	us.mu.RLock()
	defer us.mu.RUnlock()
	var missing []int64
	for i := int64(0); i < us.totalChunks; i++ {
		if !us.uploadedChunks[i] {
			missing = append(missing, i)
		}
	}
	return missing
}

func (us *UploadSession) BelongsTo(userID uuid.UUID) bool {
	return us.userID == userID
}
