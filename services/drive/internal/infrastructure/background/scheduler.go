package background

import (
	"context"
	"sync"
	"time"

	"github.com/google/uuid"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
)

type Scheduler struct {
	workerPool *WorkerPool
	tickers    map[string]*time.Ticker
	stopChans  map[string]chan struct{}
	mu         sync.RWMutex
	running    bool
}

type SchedulerConfig struct {
	CleanupInterval time.Duration
	GCInterval      time.Duration
	AuditInterval   time.Duration
}

func NewScheduler(workerPool *WorkerPool) *Scheduler {
	return &Scheduler{
		workerPool: workerPool,
		tickers:    make(map[string]*time.Ticker),
		stopChans:  make(map[string]chan struct{}),
	}
}

func (s *Scheduler) Start(
	config SchedulerConfig,
	uploadRepo uploadrepositories.UploadRepository,
) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return
	}

	s.running = true

	// Register job handlers
	s.workerPool.RegisterJobHandler("cleanup_expired_sessions", s.handleCleanupExpiredSessions)
	s.workerPool.RegisterJobHandler("garbage_collection", s.handleGarbageCollection)
	s.workerPool.RegisterJobHandler("file_processing", s.handleFileProcessing)
	s.workerPool.RegisterJobHandler("storage_sync", s.handleStorageSync)
	s.workerPool.RegisterJobHandler("audit_cleanup", s.handleAuditCleanup)

	// Schedule cleanup jobs
	if config.CleanupInterval > 0 {
		s.scheduleJob("cleanup", config.CleanupInterval, func() {
			job := NewCleanupExpiredSessionsJob(uploadRepo)
			s.workerPool.SubmitJob(job)
		})
	}

	// Schedule audit cleanup
	if config.AuditInterval > 0 {
		s.scheduleJob("audit", config.AuditInterval, func() {
			job := NewAuditCleanupJob(365) // Keep logs for 1 year
			s.workerPool.SubmitJob(job)
		})
	}
}

func (s *Scheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return
	}

	s.running = false

	// Stop all scheduled jobs
	for name, stopChan := range s.stopChans {
		close(stopChan)
		if ticker, exists := s.tickers[name]; exists {
			ticker.Stop()
		}
	}

	// Clear maps
	s.tickers = make(map[string]*time.Ticker)
	s.stopChans = make(map[string]chan struct{})
}

func (s *Scheduler) scheduleJob(name string, interval time.Duration, job func()) {
	ticker := time.NewTicker(interval)
	stopChan := make(chan struct{})

	s.tickers[name] = ticker
	s.stopChans[name] = stopChan

	go func() {
		for {
			select {
			case <-ticker.C:
				job()
			case <-stopChan:
				return
			}
		}
	}()
}

func (s *Scheduler) SubmitFileProcessingJob(fileID, userID string, filePath, mimeType string) error {
	// Parse UUIDs
	fileUUID, err := uuid.Parse(fileID)
	if err != nil {
		return err
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return err
	}

	job := NewFileProcessingJob(fileUUID, userUUID, filePath, mimeType)
	return s.workerPool.SubmitJob(job)
}

func (s *Scheduler) SubmitStorageSyncJob(sourcePath, targetPath string) error {
	job := NewStorageSyncJob(sourcePath, targetPath)
	return s.workerPool.SubmitJob(job)
}

// Job handlers
func (s *Scheduler) handleCleanupExpiredSessions(ctx context.Context, job Job) error {
	return job.Execute(ctx)
}

func (s *Scheduler) handleGarbageCollection(ctx context.Context, job Job) error {
	return job.Execute(ctx)
}

func (s *Scheduler) handleFileProcessing(ctx context.Context, job Job) error {
	return job.Execute(ctx)
}

func (s *Scheduler) handleStorageSync(ctx context.Context, job Job) error {
	return job.Execute(ctx)
}

func (s *Scheduler) handleAuditCleanup(ctx context.Context, job Job) error {
	return job.Execute(ctx)
}

func (s *Scheduler) GetStats() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	workerStats := s.workerPool.GetStats()

	return map[string]interface{}{
		"running":        s.running,
		"scheduled_jobs": len(s.tickers),
		"worker_stats":   workerStats,
	}
}
