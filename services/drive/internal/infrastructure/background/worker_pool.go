package background

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

type Job interface {
	Execute(ctx context.Context) error
	GetID() string
	GetType() string
	GetRetryCount() int
	IncrementRetryCount()
	GetMaxRetries() int
}

type WorkerPool struct {
	workers     int
	jobQueue    chan Job
	quit        chan struct{}
	wg          sync.WaitGroup
	jobHandlers map[string]JobHandler
	stats       *PoolStats
	mu          sync.RWMutex
}

type JobHandler func(ctx context.Context, job Job) error

type PoolStats struct {
	JobsProcessed int64
	JobsSucceeded int64
	JobsFailed    int64
	JobsRetried   int64
	QueueLength   int64
	ActiveWorkers int64
	mu            sync.RWMutex
}

type WorkerPoolConfig struct {
	Workers   int
	QueueSize int
}

func NewWorkerPool(config WorkerPoolConfig) *WorkerPool {
	return &WorkerPool{
		workers:     config.Workers,
		jobQueue:    make(chan Job, config.QueueSize),
		quit:        make(chan struct{}),
		jobHandlers: make(map[string]JobHandler),
		stats:       &PoolStats{},
	}
}

func (wp *WorkerPool) RegisterJobHandler(jobType string, handler JobHandler) {
	wp.mu.Lock()
	defer wp.mu.Unlock()
	wp.jobHandlers[jobType] = handler
}

func (wp *WorkerPool) Start() {
	for i := 0; i < wp.workers; i++ {
		wp.wg.Add(1)
		go wp.worker(i)
	}
}

func (wp *WorkerPool) Stop() {
	close(wp.quit)
	wp.wg.Wait()
	close(wp.jobQueue)
}

func (wp *WorkerPool) SubmitJob(job Job) error {
	select {
	case wp.jobQueue <- job:
		// Don't manually increment queue length - it will be calculated from actual channel length
		return nil
	default:
		return fmt.Errorf("job queue is full")
	}
}

func (wp *WorkerPool) GetStats() PoolStats {
	wp.stats.mu.RLock()
	defer wp.stats.mu.RUnlock()

	// Get actual queue length from channel
	queueLength := int64(len(wp.jobQueue))

	return PoolStats{
		JobsProcessed: wp.stats.JobsProcessed,
		JobsSucceeded: wp.stats.JobsSucceeded,
		JobsFailed:    wp.stats.JobsFailed,
		JobsRetried:   wp.stats.JobsRetried,
		QueueLength:   queueLength,
		ActiveWorkers: wp.stats.ActiveWorkers,
	}
}

func (wp *WorkerPool) worker(id int) {
	defer wp.wg.Done()

	for {
		select {
		case job := <-wp.jobQueue:
			wp.stats.IncrementActiveWorkers()
			wp.processJob(job)
			wp.stats.DecrementActiveWorkers()
			// Queue length is calculated from actual channel length, no need to decrement
		case <-wp.quit:
			return
		}
	}
}

func (wp *WorkerPool) processJob(job Job) {
	// Create context with timeout for job execution
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	wp.stats.IncrementJobsProcessed()

	// Get handler for job type
	wp.mu.RLock()
	handler, exists := wp.jobHandlers[job.GetType()]
	wp.mu.RUnlock()

	if !exists {
		wp.logError(fmt.Sprintf("No handler registered for job type: %s", job.GetType()))
		wp.stats.IncrementJobsFailed()
		return
	}

	// Execute job
	err := handler(ctx, job)
	if err != nil {
		wp.logError(fmt.Sprintf("Job %s failed: %v", job.GetID(), err))

		// Retry logic
		if job.GetRetryCount() < job.GetMaxRetries() {
			job.IncrementRetryCount()
			wp.stats.IncrementJobsRetried()

			// Schedule retry without blocking current worker
			go wp.scheduleRetry(job)
		} else {
			wp.logError(fmt.Sprintf("Job %s failed permanently after %d retries", job.GetID(), job.GetRetryCount()))
			wp.stats.IncrementJobsFailed()
		}
	} else {
		wp.stats.IncrementJobsSucceeded()
	}
}

func (ps *PoolStats) IncrementJobsProcessed() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	ps.JobsProcessed++
}

func (ps *PoolStats) IncrementJobsSucceeded() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	ps.JobsSucceeded++
}

func (ps *PoolStats) IncrementJobsFailed() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	ps.JobsFailed++
}

func (ps *PoolStats) IncrementJobsRetried() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	ps.JobsRetried++
}

// Queue length methods removed - length is calculated from actual channel length

func (ps *PoolStats) IncrementActiveWorkers() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	ps.ActiveWorkers++
}

func (ps *PoolStats) DecrementActiveWorkers() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	if ps.ActiveWorkers > 0 {
		ps.ActiveWorkers--
	}
}

// scheduleRetry schedules a job retry with exponential backoff without blocking the worker
func (wp *WorkerPool) scheduleRetry(job Job) {
	// Calculate exponential backoff delay
	backoff := time.Duration(job.GetRetryCount()) * time.Second
	if backoff > 5*time.Minute {
		backoff = 5 * time.Minute // Cap backoff at 5 minutes
	}

	// Wait for backoff period
	timer := time.NewTimer(backoff)
	defer timer.Stop()

	select {
	case <-timer.C:
		// Try to resubmit job after backoff
		select {
		case wp.jobQueue <- job:
			// Job resubmitted successfully
		default:
			// Queue is full, mark as failed
			wp.logError(fmt.Sprintf("Failed to resubmit job %s: queue full", job.GetID()))
			wp.stats.IncrementJobsFailed()
		}
	case <-wp.quit:
		// Worker pool is shutting down, abandon retry
		return
	}
}

// logError provides centralized error logging
func (wp *WorkerPool) logError(message string) {
	log.Printf("[WorkerPool] %s", message)
}
