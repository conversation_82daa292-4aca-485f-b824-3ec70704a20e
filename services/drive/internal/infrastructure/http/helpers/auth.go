package helpers

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthHelper provides authentication and authorization utilities
type AuthHelper struct{}

func NewAuthHelper() *AuthHelper {
	return &AuthHelper{}
}

// Context keys - must match pkg/middleware/auth.go
const (
	UserIDKey = "user_id"
	EmailKey  = "email"
	RolesKey  = "roles"
	TeamsKey  = "teams"
)

// GetUserID extracts the user ID from the gin context
func (h *AuthHelper) GetUserID(c *gin.Context) (uuid.UUID, error) {
	userIDStr, exists := c.Get(UserIDKey)
	if !exists {
		return uuid.Nil, fmt.Errorf("user ID not found in context")
	}

	userIDValue, ok := userIDStr.(string)
	if !ok {
		return uuid.Nil, fmt.Errorf("user ID is not a string")
	}

	userID, err := uuid.Parse(userIDValue)
	if err != nil {
		return uuid.Nil, fmt.<PERSON>rf("invalid user ID format: %w", err)
	}

	return userID, nil
}

// GetUserEmail extracts the user email from the gin context
func (h *AuthHelper) GetUserEmail(c *gin.Context) (string, error) {
	email, exists := c.Get(EmailKey)
	if !exists {
		return "", fmt.Errorf("user email not found in context")
	}

	emailStr, ok := email.(string)
	if !ok {
		return "", fmt.Errorf("user email is not a string")
	}

	return emailStr, nil
}

// GetUserRoles extracts the user roles from the gin context
func (h *AuthHelper) GetUserRoles(c *gin.Context) ([]string, error) {
	roles, exists := c.Get(RolesKey)
	if !exists {
		return nil, fmt.Errorf("user roles not found in context")
	}

	rolesSlice, ok := roles.([]string)
	if !ok {
		return nil, fmt.Errorf("user roles is not a string slice")
	}

	return rolesSlice, nil
}

// GetUserTeams extracts the user teams from the gin context
func (h *AuthHelper) GetUserTeams(c *gin.Context) ([]string, error) {
	teams, exists := c.Get(TeamsKey)
	if !exists {
		return nil, fmt.Errorf("user teams not found in context")
	}

	teamsSlice, ok := teams.([]string)
	if !ok {
		return nil, fmt.Errorf("user teams is not a string slice")
	}

	return teamsSlice, nil
}

// HasRole checks if the user has a specific role
func (h *AuthHelper) HasRole(c *gin.Context, role string) bool {
	roles, err := h.GetUserRoles(c)
	if err != nil {
		return false
	}

	for _, r := range roles {
		if r == role {
			return true
		}
	}
	return false
}

// IsInTeam checks if the user is in a specific team
func (h *AuthHelper) IsInTeam(c *gin.Context, teamID string) bool {
	teams, err := h.GetUserTeams(c)
	if err != nil {
		return false
	}

	for _, t := range teams {
		if t == teamID {
			return true
		}
	}
	return false
}

// RequireRole returns an error if the user doesn't have the required role
func (h *AuthHelper) RequireRole(c *gin.Context, role string) error {
	if !h.HasRole(c, role) {
		return fmt.Errorf("insufficient permissions: role '%s' required", role)
	}
	return nil
}

// RequireTeamMembership returns an error if the user is not in the required team
func (h *AuthHelper) RequireTeamMembership(c *gin.Context, teamID string) error {
	if !h.IsInTeam(c, teamID) {
		return fmt.Errorf("access denied: team membership required")
	}
	return nil
}

// Legacy functions for backward compatibility - these delegate to the helper methods
func GetUserID(c *gin.Context) (uuid.UUID, error) {
	helper := NewAuthHelper()
	return helper.GetUserID(c)
}

func GetUserEmail(c *gin.Context) (string, error) {
	helper := NewAuthHelper()
	return helper.GetUserEmail(c)
}

func GetUserRoles(c *gin.Context) ([]string, error) {
	helper := NewAuthHelper()
	return helper.GetUserRoles(c)
}

func GetUserTeams(c *gin.Context) ([]string, error) {
	helper := NewAuthHelper()
	return helper.GetUserTeams(c)
}

func HasRole(c *gin.Context, role string) bool {
	helper := NewAuthHelper()
	return helper.HasRole(c, role)
}

func IsInTeam(c *gin.Context, teamID string) bool {
	helper := NewAuthHelper()
	return helper.IsInTeam(c, teamID)
}

func RequireRole(c *gin.Context, role string) error {
	helper := NewAuthHelper()
	return helper.RequireRole(c, role)
}

func RequireTeamMembership(c *gin.Context, teamID string) error {
	helper := NewAuthHelper()
	return helper.RequireTeamMembership(c, teamID)
}