package helpers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	folderentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
)

// FileResponse represents the HTTP response structure for files
type FileResponse struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Path       string                 `json:"path"`
	Size       int64                  `json:"size"`
	MimeType   string                 `json:"mimeType"`
	Checksum   string                 `json:"checksum"`
	UserID     string                 `json:"userId"`
	TeamID     *string                `json:"teamId,omitempty"`
	FolderID   *string                `json:"folderId,omitempty"`
	Status     string                 `json:"status"`
	Visibility string                 `json:"visibility"`
	Version    int                    `json:"version"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  string                 `json:"createdAt"`
	UpdatedAt  string                 `json:"updatedAt"`
}

// FolderResponse represents the HTTP response structure for folders
type FolderResponse struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Path        string  `json:"path"`
	UserID      string  `json:"userId"`
	TeamID      *string `json:"teamId,omitempty"`
	ParentID    *string `json:"parentId,omitempty"`
	Visibility  string  `json:"visibility"`
	Description string  `json:"description"`
	CreatedAt   string  `json:"createdAt"`
	UpdatedAt   string  `json:"updatedAt"`
}

// ResponseHelper wraps pkg/utils response functions with domain-specific logic
type ResponseHelper struct{}

func NewResponseHelper() *ResponseHelper {
	return &ResponseHelper{}
}

// SuccessResponse sends a successful response
func (h *ResponseHelper) SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

// ErrorResponse sends an error response
func (h *ResponseHelper) ErrorResponse(c *gin.Context, err error) {
	c.JSON(http.StatusBadRequest, gin.H{
		"success": false,
		"error":   err.Error(),
	})
}

// PaginatedResponse sends a paginated response
func (h *ResponseHelper) PaginatedResponse(c *gin.Context, data interface{}, total int64, offset, limit int) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
		"pagination": gin.H{
			"total":  total,
			"offset": offset,
			"limit":  limit,
		},
	})
}

// FileEntityToResponse converts a file entity to HTTP response format
func FileEntityToResponse(file *fileentities.File) FileResponse {
	response := FileResponse{
		ID:         file.ID().String(),
		Name:       file.Name(),
		Path:       file.Path(),
		Size:       file.Size(),
		MimeType:   file.MimeType(),
		Checksum:   file.Checksum(),
		UserID:     file.UserID().String(),
		Status:     string(file.Status()),
		Visibility: string(file.Visibility()),
		Version:    file.Version(),
		Metadata:   file.Metadata(),
		CreatedAt:  file.CreatedAt().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:  file.UpdatedAt().Format("2006-01-02T15:04:05Z07:00"),
	}

	if file.TeamID() != nil {
		teamIDStr := file.TeamID().String()
		response.TeamID = &teamIDStr
	}

	if file.FolderID() != nil {
		folderIDStr := file.FolderID().String()
		response.FolderID = &folderIDStr
	}

	return response
}

// FolderEntityToResponse converts a folder entity to HTTP response format
func FolderEntityToResponse(folder *folderentities.Folder) FolderResponse {
	response := FolderResponse{
		ID:          folder.ID().String(),
		Name:        folder.Name(),
		Path:        folder.Path(),
		UserID:      folder.UserID().String(),
		Visibility:  string(folder.Visibility()),
		Description: folder.Description(),
		CreatedAt:   folder.CreatedAt().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   folder.UpdatedAt().Format("2006-01-02T15:04:05Z07:00"),
	}

	if folder.TeamID() != nil {
		teamIDStr := folder.TeamID().String()
		response.TeamID = &teamIDStr
	}

	if folder.ParentID() != nil {
		parentIDStr := folder.ParentID().String()
		response.ParentID = &parentIDStr
	}

	return response
}