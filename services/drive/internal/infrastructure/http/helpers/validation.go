package helpers

import (
	"fmt"
	"io"
	"mime"
	"net/http"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/security"
)

// ValidationHelper wraps pkg/utils validation with drive-specific rules
type ValidationHelper struct{}

func NewValidationHelper() *ValidationHelper {
	return &ValidationHelper{}
}

// Security constants
const (
	MaxFileNameLength = 255
	MaxFileSize       = 100 * 1024 * 1024 // 100MB
	MaxChunkSize      = 5 * 1024 * 1024   // 5MB
	MaxPathDepth      = 10
)

// Allowed file extensions (whitelist approach)
var AllowedExtensions = map[string]bool{
	".txt": true, ".md": true, ".pdf": true,
	".doc": true, ".docx": true, ".xls": true, ".xlsx": true, ".ppt": true, ".pptx": true,
	".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true, ".svg": true,
	".mp3": true, ".mp4": true, ".avi": true, ".mov": true, ".wav": true,
	".zip": true, ".rar": true, ".7z": true, ".tar": true, ".gz": true,
	".json": true, ".xml": true, ".csv": true, ".yaml": true, ".yml": true,
}

// ValidateFileName performs comprehensive file name validation using pkg/utils + drive-specific rules
func (h *ValidationHelper) ValidateFileName(fileName string) error {
	// Use pkg/utils sanitization first
	sanitized := utils.SanitizeString(fileName)
	if sanitized != fileName {
		return fmt.Errorf("file name contains invalid characters")
	}

	// Drive-specific validations
	if len(fileName) > MaxFileNameLength {
		return fmt.Errorf("file name too long (max %d characters)", MaxFileNameLength)
	}

	// Check extension against whitelist
	ext := strings.ToLower(filepath.Ext(fileName))
	if ext != "" && !AllowedExtensions[ext] {
		return fmt.Errorf("file extension %s is not allowed", ext)
	}

	// Use enhanced path validator for comprehensive validation
	pathValidator := security.NewPathTraversalValidator()
	if err := pathValidator.ValidateFileName(fileName); err != nil {
		return fmt.Errorf("file name validation failed: %w", err)
	}

	return nil
}

// SanitizeFileName sanitizes file name using pkg/utils
func (h *ValidationHelper) SanitizeFileName(fileName string) string {
	return utils.SanitizeString(fileName)
}

// ValidateFilePath validates file paths for storage
func (h *ValidationHelper) ValidateFilePath(path string) error {
	pathValidator := security.NewPathTraversalValidator()
	if err := pathValidator.ValidateStoragePath(path); err != nil {
		return fmt.Errorf("file path validation failed: %w", err)
	}
	return nil
}

// ValidateFileSize validates file size limits
func (h *ValidationHelper) ValidateFileSize(size int64) error {
	if size <= 0 {
		return fmt.Errorf("file size must be positive")
	}

	if size > MaxFileSize {
		return fmt.Errorf("file size too large (max %d bytes)", MaxFileSize)
	}

	return nil
}

// ValidateChunkSize validates chunk size for uploads
func (h *ValidationHelper) ValidateChunkSize(size int64) error {
	if size <= 0 {
		return fmt.Errorf("chunk size must be positive")
	}

	if size > MaxChunkSize {
		return fmt.Errorf("chunk size too large (max %d bytes)", MaxChunkSize)
	}

	return nil
}

// ValidateMimeType validates MIME type against file extension
func (h *ValidationHelper) ValidateMimeType(fileName, mimeType string) error {
	if mimeType == "" {
		return nil // Allow empty MIME type
	}

	// Get expected MIME type from extension
	ext := strings.ToLower(filepath.Ext(fileName))
	if ext == "" {
		return nil // No extension to validate against
	}

	expectedMime := mime.TypeByExtension(ext)
	if expectedMime == "" {
		return nil // Unknown extension, can't validate
	}

	// Basic validation - split on semicolon to handle charset parameters
	providedMime := strings.Split(mimeType, ";")[0]
	expectedMime = strings.Split(expectedMime, ";")[0]

	if !strings.EqualFold(providedMime, expectedMime) {
		return fmt.Errorf("MIME type %s doesn't match file extension %s (expected %s)",
			mimeType, ext, expectedMime)
	}

	return nil
}

// DetectMimeType detects MIME type from file content and falls back to extension
func (h *ValidationHelper) DetectMimeType(fileName string, fileContent io.Reader) (string, error) {
	// Read first 512 bytes to detect content type
	buffer := make([]byte, 512)
	n, err := fileContent.Read(buffer)
	if err != nil && err != io.EOF {
		return "", fmt.Errorf("failed to read file content for MIME type detection: %w", err)
	}

	// Detect from content
	detectedMime := http.DetectContentType(buffer[:n])

	// If detection returns generic type, try to get more specific type from extension
	if detectedMime == "application/octet-stream" || detectedMime == "text/plain; charset=utf-8" {
		ext := strings.ToLower(filepath.Ext(fileName))
		if ext != "" {
			if mimeFromExt := mime.TypeByExtension(ext); mimeFromExt != "" {
				return mimeFromExt, nil
			}
		}
	}

	return detectedMime, nil
}

// ValidateChunkIndex validates chunk index against total chunks
func (h *ValidationHelper) ValidateChunkIndex(index, totalChunks int64) error {
	if index < 0 {
		return fmt.Errorf("chunk index cannot be negative")
	}

	if totalChunks > 0 && index >= totalChunks {
		return fmt.Errorf("chunk index %d exceeds total chunks %d", index, totalChunks)
	}

	return nil
}

// ValidatePaginationParams validates offset and limit parameters
func (h *ValidationHelper) ValidatePaginationParams(offset, limit int) error {
	if offset < 0 {
		return fmt.Errorf("offset cannot be negative")
	}

	if limit <= 0 {
		return fmt.Errorf("limit must be positive")
	}

	if limit > 1000 {
		return fmt.Errorf("limit too large (max 1000)")
	}

	return nil
}

// ValidateSearchQuery validates search queries using pkg/utils
func (h *ValidationHelper) ValidateSearchQuery(query string) error {
	if query == "" {
		return fmt.Errorf("search query cannot be empty")
	}

	if len(query) > 200 {
		return fmt.Errorf("search query too long (max 200 characters)")
	}

	// Use pkg/utils sanitization check
	sanitized := utils.SanitizeString(query)
	if sanitized != query {
		return fmt.Errorf("search query contains forbidden characters")
	}

	return nil
}

// SanitizeSearchQuery sanitizes search queries using pkg/utils
func (h *ValidationHelper) SanitizeSearchQuery(query string) string {
	sanitized := utils.SanitizeString(query)
	
	// Trim whitespace and limit length
	sanitized = strings.TrimSpace(sanitized)
	if len(sanitized) > 200 {
		sanitized = sanitized[:200]
	}

	return sanitized
}

// ValidateEmail validates email format
func (h *ValidationHelper) ValidateEmail(email string) error {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

// ValidatePassword validates password strength
func (h *ValidationHelper) ValidatePassword(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}
	return nil
}

// Legacy functions for backward compatibility
func ValidateSearchQuery(query string) error {
	helper := NewValidationHelper()
	return helper.ValidateSearchQuery(query)
}

func SanitizeSearchQuery(query string) string {
	helper := NewValidationHelper()
	return helper.SanitizeSearchQuery(query)
}

func ValidatePaginationParams(offset, limit int) error {
	helper := NewValidationHelper()
	return helper.ValidatePaginationParams(offset, limit)
}

func ValidateFileName(fileName string) error {
	helper := NewValidationHelper()
	return helper.ValidateFileName(fileName)
}

func SanitizeFileName(fileName string) string {
	helper := NewValidationHelper()
	return helper.SanitizeFileName(fileName)
}

func ValidateFilePath(path string) error {
	helper := NewValidationHelper()
	return helper.ValidateFilePath(path)
}

func ValidateFileSize(size int64) error {
	helper := NewValidationHelper()
	return helper.ValidateFileSize(size)
}

func ValidateChunkSize(size int64) error {
	helper := NewValidationHelper()
	return helper.ValidateChunkSize(size)
}

func ValidateMimeType(fileName, mimeType string) error {
	helper := NewValidationHelper()
	return helper.ValidateMimeType(fileName, mimeType)
}

func DetectMimeType(fileName string, fileContent io.Reader) (string, error) {
	helper := NewValidationHelper()
	return helper.DetectMimeType(fileName, fileContent)
}

func ValidateChunkIndex(index, totalChunks int64) error {
	helper := NewValidationHelper()
	return helper.ValidateChunkIndex(index, totalChunks)
}