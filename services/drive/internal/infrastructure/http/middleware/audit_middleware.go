package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
)

type AuditMiddleware struct {
	auditPort ports.AuditPort
}

func NewAuditMiddleware(auditPort ports.AuditPort) *AuditMiddleware {
	return &AuditMiddleware{
		auditPort: auditPort,
	}
}

func (m *AuditMiddleware) AuditRequest() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Process request
		c.Next()

		// Create audit log after request is processed
		duration := time.Since(start)

		// Determine event type based on method and path
		eventType := m.getEventType(c.Request.Method, c.FullPath())

		// Create audit log
		auditLog := auditentities.NewAuditLog(
			eventType,
			c.Request.Method,
			m.getDescription(c.Request.Method, c.FullPath()),
		)

		// Set user ID if available
		if userID, exists := c.Get("userID"); exists {
			auditLog.SetUserID(userID.(uuid.UUID))
		}

		// Set request details
		auditLog.SetIPAddress(c.ClientIP())
		auditLog.SetUserAgent(c.Request.UserAgent())

		// Set metadata
		auditLog.SetMetadata("method", c.Request.Method)
		auditLog.SetMetadata("path", c.FullPath())
		auditLog.SetMetadata("status_code", c.Writer.Status())
		auditLog.SetMetadata("duration_ms", duration.Milliseconds())
		auditLog.SetMetadata("request_size", c.Request.ContentLength)
		auditLog.SetMetadata("response_size", c.Writer.Size())

		// Add request ID if available
		if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
			auditLog.SetMetadata("request_id", requestID)
		}

		// Add resource ID if available in path parameters
		if fileID := c.Param("id"); fileID != "" {
			if resourceID, err := uuid.Parse(fileID); err == nil {
				auditLog.SetResourceID(resourceID)
				auditLog.SetResourceType("file")
			}
		}

		// Log the audit event
		err := m.auditPort.LogEvent(c.Request.Context(), auditLog)
		if err != nil {
			// Log error but don't fail the request
			// In production, you might want to use a proper logger
			// log.Printf("Failed to log audit event: %v", err)
		}
	}
}

func (m *AuditMiddleware) getEventType(method, path string) auditentities.EventType {
	switch {
	case method == "POST" && path == "/drive/upload":
		return auditentities.EventTypeFileUpload
	case method == "GET" && path == "/drive/files/:id":
		return auditentities.EventTypeFileRead
	case method == "PUT" && path == "/drive/files/:id":
		return auditentities.EventTypeFileUpdate
	case method == "DELETE" && path == "/drive/files/:id":
		return auditentities.EventTypeFileDelete
	case method == "POST" && path == "/drive/files/:id/share":
		return auditentities.EventTypeFileShare
	case method == "DELETE" && path == "/drive/files/:id/unshare/:userId":
		return auditentities.EventTypeFileUnshare
	case method == "GET" && path == "/drive/files/:id/download":
		return auditentities.EventTypeFileDownload
	case method == "POST" && path == "/drive/folders":
		return auditentities.EventTypeFolderCreate
	case method == "GET" && path == "/drive/folders/:id":
		return auditentities.EventTypeFolderRead
	case method == "PUT" && path == "/drive/folders/:id":
		return auditentities.EventTypeFolderUpdate
	case method == "DELETE" && path == "/drive/folders/:id":
		return auditentities.EventTypeFolderDelete
	default:
		return auditentities.EventTypeUserAccess
	}
}

func (m *AuditMiddleware) getDescription(method, path string) string {
	switch {
	case method == "POST" && path == "/drive/upload":
		return "File uploaded"
	case method == "GET" && path == "/drive/files/:id":
		return "File accessed"
	case method == "PUT" && path == "/drive/files/:id":
		return "File updated"
	case method == "DELETE" && path == "/drive/files/:id":
		return "File deleted"
	case method == "POST" && path == "/drive/files/:id/share":
		return "File shared"
	case method == "DELETE" && path == "/drive/files/:id/unshare/:userId":
		return "File unshared"
	case method == "GET" && path == "/drive/files/:id/download":
		return "File downloaded"
	case method == "POST" && path == "/drive/folders":
		return "Folder created"
	case method == "GET" && path == "/drive/folders/:id":
		return "Folder accessed"
	case method == "PUT" && path == "/drive/folders/:id":
		return "Folder updated"
	case method == "DELETE" && path == "/drive/folders/:id":
		return "Folder deleted"
	default:
		return "API access"
	}
}
