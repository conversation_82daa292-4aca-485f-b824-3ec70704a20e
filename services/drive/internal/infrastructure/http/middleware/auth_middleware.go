package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	commonmiddleware "github.com/swork-team/platform/pkg/middleware"
)

// DriveAuthMiddleware wraps the common auth middleware for drive service
type DriveAuthMiddleware struct{}

// NewDriveAuthMiddleware creates a new drive auth middleware instance
func NewDriveAuthMiddleware() *DriveAuthMiddleware {
	return &DriveAuthMiddleware{}
}

// RequireAuth returns a middleware that requires authentication
func (m *DriveAuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// If running behind gateway, use forwarded headers
		if userID := c.GetHeader("X-User-ID"); userID != "" {
			// Set context values from forwarded headers
			c.Set("user_id", userID)
			if email := c.GetHeader("X-User-Email"); email != "" {
				c.Set("email", email)
			}
			if rolesHeader := c.GetHeader("X-User-Roles"); rolesHeader != "" {
				roles := strings.Split(rolesHeader, ",")
				for i := range roles {
					roles[i] = strings.TrimSpace(roles[i])
				}
				c.Set("roles", roles)
			}
			c.Next()
			return
		}
		
		// Fallback to JWT validation for direct access
		commonmiddleware.AuthMiddleware()(c)
	}
}

// OptionalAuth returns a middleware that optionally handles authentication
func (m *DriveAuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// If running behind gateway, use forwarded headers
		if userID := c.GetHeader("X-User-ID"); userID != "" {
			// Set context values from forwarded headers
			c.Set("user_id", userID)
			if email := c.GetHeader("X-User-Email"); email != "" {
				c.Set("email", email)
			}
			if rolesHeader := c.GetHeader("X-User-Roles"); rolesHeader != "" {
				roles := strings.Split(rolesHeader, ",")
				for i := range roles {
					roles[i] = strings.TrimSpace(roles[i])
				}
				c.Set("roles", roles)
			}
			c.Next()
			return
		}
		
		// Fallback to JWT validation for direct access
		commonmiddleware.OptionalAuthMiddleware()(c)
	}
}
