package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
)

type StorageHandler struct {
	storagePort ports.StoragePort
}

type StorageHealthResponse struct {
	Status    string                 `json:"status"`
	Providers []ProviderHealthStatus `json:"providers"`
	Timestamp string                 `json:"timestamp"`
}

type ProviderHealthStatus struct {
	Name      string `json:"name"`
	Healthy   bool   `json:"healthy"`
	LastCheck string `json:"lastCheck"`
	Error     string `json:"error,omitempty"`
}

type StorageStatsResponse struct {
	TotalFiles     int64           `json:"totalFiles"`
	TotalSize      int64           `json:"totalSize"`
	AvailableSpace int64           `json:"availableSpace"`
	UsedSpace      int64           `json:"usedSpace"`
	Providers      []ProviderStats `json:"providers"`
}

type ProviderStats struct {
	Name     string `json:"name"`
	Files    int64  `json:"files"`
	Size     int64  `json:"size"`
	Healthy  bool   `json:"healthy"`
	LastSync string `json:"lastSync"`
}

func NewStorageHandler(storagePort ports.StoragePort) *StorageHandler {
	return &StorageHandler{
		storagePort: storagePort,
	}
}

func (h *StorageHandler) GetHealth(c *gin.Context) {
	err := h.storagePort.Health(c.Request.Context())

	status := "healthy"
	if err != nil {
		status = "unhealthy"
	}

	// TODO: Get actual provider health status from storage manager
	providers := []ProviderHealthStatus{
		{
			Name:      "primary",
			Healthy:   err == nil,
			LastCheck: time.Now().Format(time.RFC3339),
			Error:     getErrorString(err),
		},
	}

	response := StorageHealthResponse{
		Status:    status,
		Providers: providers,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	if err != nil {
		c.JSON(http.StatusServiceUnavailable, response)
	} else {
		c.JSON(http.StatusOK, response)
	}
}

func (h *StorageHandler) GetStats(c *gin.Context) {
	// TODO: Implement actual storage statistics collection
	// This would require extending the storage port interface

	response := StorageStatsResponse{
		TotalFiles:     1000,                    // Mock data
		TotalSize:      1024 * 1024 * 1024,      // 1GB
		AvailableSpace: 10 * 1024 * 1024 * 1024, // 10GB
		UsedSpace:      1024 * 1024 * 1024,      // 1GB
		Providers: []ProviderStats{
			{
				Name:     "primary",
				Files:    800,
				Size:     800 * 1024 * 1024, // 800MB
				Healthy:  true,
				LastSync: time.Now().Add(-5 * time.Minute).Format(time.RFC3339),
			},
			{
				Name:     "replica",
				Files:    200,
				Size:     200 * 1024 * 1024, // 200MB
				Healthy:  true,
				LastSync: time.Now().Add(-2 * time.Minute).Format(time.RFC3339),
			},
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *StorageHandler) TriggerSync(c *gin.Context) {
	// TODO: Implement manual storage sync trigger
	// This would require extending the storage manager interface

	c.JSON(http.StatusOK, gin.H{
		"message": "Storage sync triggered",
		"status":  "in_progress",
	})
}

func (h *StorageHandler) GetSyncStatus(c *gin.Context) {
	// TODO: Implement sync status checking
	// This would require tracking sync operations

	c.JSON(http.StatusOK, gin.H{
		"status":     "idle",
		"lastSync":   time.Now().Add(-10 * time.Minute).Format(time.RFC3339),
		"nextSync":   time.Now().Add(50 * time.Minute).Format(time.RFC3339),
		"pendingOps": 0,
		"failedOps":  0,
	})
}

func getErrorString(err error) string {
	if err != nil {
		return err.Error()
	}
	return ""
}
