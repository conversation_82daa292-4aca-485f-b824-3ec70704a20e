package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/utils"
	sharingcommands "github.com/swork-team/platform/services/drive/internal/application/sharing/commands"
	sharingqueries "github.com/swork-team/platform/services/drive/internal/application/sharing/queries"
	sharingentities "github.com/swork-team/platform/services/drive/internal/domain/sharing/entities"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/helpers"
)

type SharingHandler struct {
	shareFileHandler       sharingcommands.ShareFileHandler
	unshareFileHandler     sharingcommands.UnshareFileHandler
	listFileSharesHandler  sharingqueries.ListFileSharesHandler
	listSharedFilesHandler sharingqueries.ListSharedFilesHandler
}

type ShareFileRequest struct {
	SharedWith string `json:"sharedWith" binding:"required,uuid"`
	Permission string `json:"permission" binding:"required,oneof=read write admin"`
	ExpiresAt  string `json:"expiresAt,omitempty"` // Optional expiration date
}

type ShareFileResponse struct {
	ID         string `json:"id"`
	FileID     string `json:"fileId"`
	SharedWith string `json:"sharedWith"`
	SharedBy   string `json:"sharedBy"`
	Permission string `json:"permission"`
	CreatedAt  string `json:"createdAt"`
	ExpiresAt  string `json:"expiresAt,omitempty"`
}

func NewSharingHandler(
	shareFileHandler sharingcommands.ShareFileHandler,
	unshareFileHandler sharingcommands.UnshareFileHandler,
	listFileSharesHandler sharingqueries.ListFileSharesHandler,
	listSharedFilesHandler sharingqueries.ListSharedFilesHandler,
) *SharingHandler {
	return &SharingHandler{
		shareFileHandler:       shareFileHandler,
		unshareFileHandler:     unshareFileHandler,
		listFileSharesHandler:  listFileSharesHandler,
		listSharedFilesHandler: listSharedFilesHandler,
	}
}

func (h *SharingHandler) ShareFile(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	var req ShareFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Parse shared with user ID
	sharedWith, err := uuid.Parse(req.SharedWith)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid sharedWith user ID format"})
		return
	}

	// Validate that user is not sharing with themselves
	if sharedWith == userID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "cannot share file with yourself"})
		return
	}

	// Parse permission
	permission := sharingentities.Permission(req.Permission)

	// Create command
	cmd := &sharingcommands.ShareFileCommand{
		FileID:     fileID,
		SharedWith: sharedWith,
		SharedBy:   userID,
		Permission: permission,
	}

	// Parse optional expiration date
	if req.ExpiresAt != "" {
		if expiresAt, err := time.Parse("2006-01-02T15:04:05Z07:00", req.ExpiresAt); err == nil {
			cmd.ExpiresAt = &expiresAt
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid expires_at format, use RFC3339"})
			return
		}
	}

	// Execute command
	result, err := h.shareFileHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := ShareFileResponse{
		ID:         result.Share.ID().String(),
		FileID:     result.Share.FileID().String(),
		SharedWith: result.Share.SharedWith().String(),
		SharedBy:   result.Share.SharedBy().String(),
		Permission: string(result.Share.Permission()),
		CreatedAt:  result.Share.CreatedAt().Format("2006-01-02T15:04:05Z07:00"),
	}

	if result.Share.ExpiresAt() != nil {
		response.ExpiresAt = result.Share.ExpiresAt().Format("2006-01-02T15:04:05Z07:00")
	}

	utils.SuccessResponse(c, http.StatusCreated, "File shared successfully", response)
}

func (h *SharingHandler) UnshareFile(c *gin.Context) {
	fileIDStr := c.Param("id")
	userIDStr := c.Param("userId")

	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	sharedWith, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user ID"})
		return
	}

	// Get current user ID from context
	currentUser, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Create command
	cmd := &sharingcommands.UnshareFileCommand{
		FileID:      fileID,
		SharedWith:  sharedWith,
		CurrentUser: currentUser,
	}

	// Execute command
	result, err := h.unshareFileHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "File unshared successfully", gin.H{"success": result.Success})
}

func (h *SharingHandler) ListFileShares(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	// Get current user ID from context
	currentUser, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Create query
	query := &sharingqueries.ListFileSharesQuery{
		FileID:      fileID,
		CurrentUser: currentUser,
	}

	// Execute query
	result, err := h.listFileSharesHandler.Handle(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response format
	shares := make([]ShareFileResponse, len(result.Shares))
	for i, share := range result.Shares {
		shares[i] = ShareFileResponse{
			ID:         share.ID().String(),
			FileID:     share.FileID().String(),
			SharedWith: share.SharedWith().String(),
			SharedBy:   share.SharedBy().String(),
			Permission: string(share.Permission()),
			CreatedAt:  share.CreatedAt().Format("2006-01-02T15:04:05Z07:00"),
		}
		if share.ExpiresAt() != nil {
			shares[i].ExpiresAt = share.ExpiresAt().Format("2006-01-02T15:04:05Z07:00")
		}
	}

	utils.SuccessResponse(c, http.StatusOK, "File shares retrieved successfully", gin.H{
		"shares": shares,
		"count":  len(shares),
	})
}

func (h *SharingHandler) ListSharedFiles(c *gin.Context) {
	// Get current user ID from context
	currentUser, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Parse pagination parameters
	offset := 0
	limit := 20

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if parsedOffset, err := strconv.Atoi(offsetStr); err == nil {
			offset = parsedOffset
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil {
			limit = parsedLimit
		}
	}

	// Create query
	query := &sharingqueries.ListSharedFilesQuery{
		UserID: currentUser,
		Offset: offset,
		Limit:  limit,
	}

	// Execute query
	result, err := h.listSharedFilesHandler.Handle(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response format
	files := make([]gin.H, len(result.Files))
	for i, file := range result.Files {
		files[i] = gin.H{
			"id":        file.ID().String(),
			"name":      file.Name(),
			"size":      file.Size(),
			"mimeType":  file.MimeType(),
			"createdAt": file.CreatedAt().Format("2006-01-02T15:04:05Z07:00"),
			"updatedAt": file.UpdatedAt().Format("2006-01-02T15:04:05Z07:00"),
		}
	}

	utils.SuccessResponse(c, http.StatusOK, "Shared files retrieved successfully", gin.H{
		"files":      files,
		"totalCount": result.TotalCount,
		"offset":     offset,
		"limit":      limit,
	})
}
