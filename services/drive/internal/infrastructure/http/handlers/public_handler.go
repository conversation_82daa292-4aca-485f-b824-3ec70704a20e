package handlers

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/utils"
)

// PublicHandler handles public file access operations
type PublicHandler struct {
	// For now, we keep this simple since the current implementation is mostly mock data
	// In a real implementation, this would include services for share validation, etc.
}

// NewPublicHandler creates a new public handler
func NewPublicHandler() *PublicHandler {
	return &PublicHandler{}
}

// GetPublicFile handles public file access with share token validation
func (ph *PublicHandler) GetPublicFile(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	// Get optional share token from query parameter
	shareToken := c.Query("token")

	// For demonstration, implement basic share token validation
	// In production, this would involve:
	// 1. Decoding/validating the share token
	// 2. Checking file visibility settings
	// 3. Verifying expiration dates
	// 4. Logging access attempts

	if shareToken == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "share token required for public file access",
			"note":  "add ?token=<share_token> to access shared files",
		})
		return
	}

	// Basic token validation (placeholder implementation)
	if len(shareToken) < 32 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "invalid share token format",
		})
		return
	}

	// Mock file information for demonstration
	// In production, this would query the actual file after validating the share
	mockFileInfo := gin.H{
		"id":          fileID.String(),
		"name":        "shared_document.pdf",
		"size":        1048576,
		"mimeType":    "application/pdf",
		"isPublic":    true,
		"sharedBy":    "<EMAIL>",
		"sharedAt":    time.Now().Add(-24 * time.Hour).UTC(),
		"downloadUrl": fmt.Sprintf("/api/v1/public/files/%s/download?token=%s", fileID.String(), shareToken),
		"expiresAt":   time.Now().Add(7 * 24 * time.Hour).UTC(), // 7 days from now
		"permissions": []string{"read"},
		"note":        "Mock shared file for demonstration",
	}

	utils.SuccessResponse(c, http.StatusOK, "Public file retrieved successfully", gin.H{
		"file": mockFileInfo,
	})
}