package handlers

import (
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	commonmiddleware "github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	filecommands "github.com/swork-team/platform/services/drive/internal/application/file/commands"
	filequeries "github.com/swork-team/platform/services/drive/internal/application/file/queries"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/helpers"
)

type FileHandler struct {
	uploadHandler      filecommands.UploadFileHandler
	updateHandler      filecommands.UpdateFileHandler
	copyHandler        filecommands.CopyFileHandler
	deleteHandler      filecommands.DeleteFileHandler
	getFileHandler     filequeries.GetFileHandler
	listFilesHandler   filequeries.ListFilesHandler
	searchFilesHandler filequeries.SearchFilesHandler
	accessControlSvc   fileservices.AccessControlService
	storagePort        ports.StoragePort
}

type UploadFileRequest struct {
	FileName string `form:"fileName" binding:"required"`
	TeamID   string `form:"teamId"`
	FolderID string `form:"folderId"`
	MimeType string `form:"mimeType"`
	Checksum string `form:"checksum" binding:"required"`
}

// FileResponse is now imported from helpers
type FileResponse = helpers.FileResponse

type ListFilesResponse struct {
	Files      []FileResponse `json:"files"`
	TotalCount int64          `json:"totalCount"`
	Offset     int            `json:"offset"`
	Limit      int            `json:"limit"`
}

type UploadFileResponse struct {
	File FileResponse `json:"file"`
}

type UpdateFileRequest struct {
	Name       *string                `json:"name"`
	FolderID   *string                `json:"folderId"`
	TeamID     *string                `json:"teamId"`
	Visibility *string                `json:"visibility"`
	Metadata   map[string]interface{} `json:"metadata"`
}

type UpdateFileResponse struct {
	File FileResponse `json:"file"`
}

type CopyFileRequest struct {
	NewName  *string `json:"newName"`
	FolderID *string `json:"folderId"`
	TeamID   *string `json:"teamId"`
}

type CopyFileResponse struct {
	OriginalFile FileResponse `json:"originalFile"`
	CopiedFile   FileResponse `json:"copiedFile"`
}

func NewFileHandler(
	uploadHandler filecommands.UploadFileHandler,
	updateHandler filecommands.UpdateFileHandler,
	copyHandler filecommands.CopyFileHandler,
	deleteHandler filecommands.DeleteFileHandler,
	getFileHandler filequeries.GetFileHandler,
	listFilesHandler filequeries.ListFilesHandler,
	searchFilesHandler filequeries.SearchFilesHandler,
	accessControlSvc fileservices.AccessControlService,
	storagePort ports.StoragePort,
) *FileHandler {
	return &FileHandler{
		uploadHandler:      uploadHandler,
		updateHandler:      updateHandler,
		copyHandler:        copyHandler,
		deleteHandler:      deleteHandler,
		getFileHandler:     getFileHandler,
		listFilesHandler:   listFilesHandler,
		searchFilesHandler: searchFilesHandler,
		accessControlSvc:   accessControlSvc,
		storagePort:        storagePort,
	}
}

func (h *FileHandler) UploadFile(c *gin.Context) {
	var req UploadFileRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and sanitize file name
	if err := helpers.ValidateFileName(req.FileName); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid file name: %v", err)})
		return
	}
	req.FileName = helpers.SanitizeFileName(req.FileName)

	// Get file from form
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "file is required"})
		return
	}

	// Validate file size
	if err := helpers.ValidateFileSize(file.Size); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid file size: %v", err)})
		return
	}

	// Validate MIME type matches file extension
	if req.MimeType != "" {
		if err := helpers.ValidateMimeType(req.FileName, req.MimeType); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("MIME type validation failed: %v", err)})
			return
		}
	}

	// Open file
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to open file"})
		return
	}
	defer src.Close()

	// Auto-detect MIME type if not provided or empty
	var detectedMimeType string
	if req.MimeType == "" {
		detectedMimeType, err = helpers.DetectMimeType(req.FileName, src)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to detect MIME type: %v", err)})
			return
		}
		req.MimeType = detectedMimeType

		// Reset file pointer after reading for MIME detection
		if seeker, ok := src.(io.Seeker); ok {
			if _, err := seeker.Seek(0, io.SeekStart); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to reset file pointer"})
				return
			}
		}
	}

	// Get user ID from context (set by auth middleware)
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Parse optional fields
	var teamID *uuid.UUID
	if req.TeamID != "" {
		parsed, err := uuid.Parse(req.TeamID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	var folderID *fileentities.FolderID
	if req.FolderID != "" {
		parsed, err := uuid.Parse(req.FolderID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
			return
		}
		fID := fileentities.FolderID(parsed)
		folderID = &fID
	}

	// Create command
	cmd := &filecommands.UploadFileCommand{
		UserID:      userID,
		TeamID:      teamID,
		FolderID:    folderID,
		FileName:    req.FileName,
		FileContent: src,
		Size:        file.Size,
		MimeType:    req.MimeType,
		Checksum:    req.Checksum,
	}

	// Execute command
	result, err := h.uploadHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := UploadFileResponse{
		File: h.fileEntityToResponse(result.File),
	}

	utils.SuccessResponse(c, http.StatusCreated, "File uploaded successfully", response)
}

func (h *FileHandler) GetFile(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Get user teams from context
	userTeams, err := helpers.GetUserTeams(c)
	if err != nil {
		// If teams aren't available, use empty slice (user not in any teams)
		userTeams = []string{}
	}

	// Create query
	query := &filequeries.GetFileQuery{
		FileID:    fileID,
		UserID:    userID,
		UserTeams: userTeams,
	}

	// Execute query
	result, err := h.getFileHandler.Handle(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	response := h.fileEntityToResponse(result.File)
	utils.SuccessResponse(c, http.StatusOK, "File retrieved successfully", response)
}

// GetDownloadURL generates a presigned download URL for a file
func (h *FileHandler) GetDownloadURL(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	// Get user ID from context using proper middleware helper
	userIDStr, err := commonmiddleware.GetUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
		return
	}

	// Get user teams from context
	userTeamsValue, _ := c.Get("teams")
	userTeams := []string{}
	if userTeamsValue != nil {
		if teams, ok := userTeamsValue.([]string); ok {
			userTeams = teams
		}
	}

	// Validate read access to the file
	_, err = h.accessControlSvc.ValidateFileAccess(
		c.Request.Context(),
		userID,
		userTeams,
		fileentities.FileID(fileID),
		fileservices.AccessOperationRead,
	)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
		return
	}

	// Get file information to generate download URL
	getFileQuery := &filequeries.GetFileQuery{
		FileID:    fileID,
		UserID:    userID,
		UserTeams: userTeams,
	}

	result, err := h.getFileHandler.Handle(c.Request.Context(), getFileQuery)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "file not found"})
		return
	}

	// Generate presigned URL (expires in 1 hour)
	expiresInSeconds := 3600
	downloadURL, err := h.storagePort.GenerateDownloadURL(
		c.Request.Context(),
		result.File.Path(),
		expiresInSeconds,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to generate download URL"})
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Download URL generated successfully", gin.H{
		"downloadURL": downloadURL,
		"expiresIn":   expiresInSeconds,
		"fileName":    result.File.Name(),
		"size":        result.File.Size(),
		"mimeType":    result.File.MimeType(),
	})
}

func (h *FileHandler) UpdateFile(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	var req UpdateFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Validate and sanitize file name if provided
	if req.Name != nil {
		if err := helpers.ValidateFileName(*req.Name); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid file name: %v", err)})
			return
		}
		sanitized := helpers.SanitizeFileName(*req.Name)
		req.Name = &sanitized
	}

	// Parse optional fields
	var teamID *uuid.UUID
	if req.TeamID != nil && *req.TeamID != "" {
		parsed, err := uuid.Parse(*req.TeamID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	var folderID *fileentities.FolderID
	if req.FolderID != nil {
		if *req.FolderID == "" || *req.FolderID == "null" {
			// Moving to root folder (null folder ID)
			nilUUID := uuid.Nil
			fID := fileentities.FolderID(nilUUID)
			folderID = &fID
		} else {
			parsed, err := uuid.Parse(*req.FolderID)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
				return
			}
			fID := fileentities.FolderID(parsed)
			folderID = &fID
		}
	}

	var visibility *fileentities.Visibility
	if req.Visibility != nil {
		vis := fileentities.Visibility(*req.Visibility)
		visibility = &vis
	}

	// Create command
	cmd := &filecommands.UpdateFileCommand{
		FileID:     fileID,
		UserID:     userID,
		Name:       req.Name,
		FolderID:   folderID,
		TeamID:     teamID,
		Visibility: visibility,
		Metadata:   req.Metadata,
	}

	// Execute command
	result, err := h.updateHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := UpdateFileResponse{
		File: h.fileEntityToResponse(result.File),
	}

	utils.SuccessResponse(c, http.StatusOK, "File updated successfully", response)
}

func (h *FileHandler) CopyFile(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	var req CopyFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Validate and sanitize new file name if provided
	if req.NewName != nil && *req.NewName != "" {
		if err := helpers.ValidateFileName(*req.NewName); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid file name: %v", err)})
			return
		}
		sanitized := helpers.SanitizeFileName(*req.NewName)
		req.NewName = &sanitized
	}

	// Parse optional fields
	var teamID *uuid.UUID
	if req.TeamID != nil && *req.TeamID != "" {
		parsed, err := uuid.Parse(*req.TeamID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	var folderID *fileentities.FolderID
	if req.FolderID != nil && *req.FolderID != "" {
		parsed, err := uuid.Parse(*req.FolderID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
			return
		}
		fID := fileentities.FolderID(parsed)
		folderID = &fID
	}

	// Create command
	cmd := &filecommands.CopyFileCommand{
		SourceFileID: fileID,
		UserID:       userID,
		NewName:      req.NewName,
		FolderID:     folderID,
		TeamID:       teamID,
	}

	// Execute command
	result, err := h.copyHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := CopyFileResponse{
		OriginalFile: h.fileEntityToResponse(result.OriginalFile),
		CopiedFile:   h.fileEntityToResponse(result.CopiedFile),
	}

	utils.SuccessResponse(c, http.StatusCreated, "File copied successfully", response)
}

func (h *FileHandler) ListFiles(c *gin.Context) {
	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Parse query parameters
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "20")
	teamIDStr := c.Query("teamId")
	folderIDStr := c.Query("folderId")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid offset"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid limit"})
		return
	}

	// Validate pagination parameters
	if err := helpers.ValidatePaginationParams(offset, limit); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid pagination: %v", err)})
		return
	}

	// Parse optional fields
	var teamID *uuid.UUID
	if teamIDStr != "" {
		parsed, err := uuid.Parse(teamIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	var folderID *fileentities.FolderID
	if folderIDStr != "" {
		parsed, err := uuid.Parse(folderIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
			return
		}
		fID := fileentities.FolderID(parsed)
		folderID = &fID
	}

	// Create query
	query := &filequeries.ListFilesQuery{
		UserID:   userID,
		TeamID:   teamID,
		FolderID: folderID,
		Offset:   offset,
		Limit:    limit,
	}

	// Execute query
	result, err := h.listFilesHandler.Handle(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	files := make([]FileResponse, len(result.Files))
	for i, file := range result.Files {
		files[i] = h.fileEntityToResponse(file)
	}

	response := ListFilesResponse{
		Files:      files,
		TotalCount: result.TotalCount,
		Offset:     offset,
		Limit:      limit,
	}

	utils.SuccessResponse(c, http.StatusOK, "Files retrieved successfully", response)
}

func (h *FileHandler) DeleteFile(c *gin.Context) {
	fileIDStr := c.Param("id")
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file ID"})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Get user teams from context
	userTeams, err := helpers.GetUserTeams(c)
	if err != nil {
		// If teams aren't available, use empty slice
		userTeams = []string{}
	}

	// Validate delete access
	_, err = h.accessControlSvc.ValidateFileAccess(c.Request.Context(), userID, userTeams, fileentities.FileID(fileID), fileservices.AccessOperationDelete)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	// Create command
	cmd := &filecommands.DeleteFileCommand{
		FileID: fileID,
		UserID: userID,
	}

	// Execute command
	_, err = h.deleteHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "File deleted successfully", nil)
}

func (h *FileHandler) fileEntityToResponse(file *fileentities.File) FileResponse {
	return helpers.FileEntityToResponse(file)
}

// Expose services for use in router
func (h *FileHandler) AccessControlService() fileservices.AccessControlService {
	return h.accessControlSvc
}

func (h *FileHandler) GetFileHandler() filequeries.GetFileHandler {
	return h.getFileHandler
}

func (h *FileHandler) SearchFilesHandler() filequeries.SearchFilesHandler {
	return h.searchFilesHandler
}

func (h *FileHandler) ListFilesHandler() filequeries.ListFilesHandler {
	return h.listFilesHandler
}
