package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/utils"
	foldercommands "github.com/swork-team/platform/services/drive/internal/application/folder/commands"
	folderqueries "github.com/swork-team/platform/services/drive/internal/application/folder/queries"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/helpers"
)

type FolderHandler struct {
	createFolderHandler  foldercommands.CreateFolderHandler
	getFolderHandler     folderqueries.GetFolderHandler
	getRootFolderHandler folderqueries.GetRootFolderHandler
	updateFolderHandler  foldercommands.UpdateFolderHandler
	deleteFolderHandler  foldercommands.DeleteFolderHandler
}

type CreateFolderRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	ParentID    string `json:"parentId"`
	TeamID      string `json:"teamId"`
	Visibility  string `json:"visibility"`
}

type FolderResponse struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Path        string  `json:"path"`
	UserID      string  `json:"userId"`
	TeamID      *string `json:"teamId,omitempty"`
	ParentID    *string `json:"parentId,omitempty"`
	Visibility  string  `json:"visibility"`
	Description string  `json:"description"`
	CreatedAt   string  `json:"createdAt"`
	UpdatedAt   string  `json:"updatedAt"`
}

type GetFolderResponse struct {
	Folder     FolderResponse         `json:"folder"`
	Files      []helpers.FileResponse `json:"files"`
	Subfolders []FolderResponse       `json:"subfolders"`
}

type GetRootFolderResponse struct {
	Files        []helpers.FileResponse `json:"files"`
	Folders      []FolderResponse       `json:"folders"`
	TotalFiles   int64                  `json:"totalFiles"`
	TotalFolders int64                  `json:"totalFolders"`
	Offset       int                    `json:"offset"`
	Limit        int                    `json:"limit"`
}

func NewFolderHandler(
	createFolderHandler foldercommands.CreateFolderHandler,
	getFolderHandler folderqueries.GetFolderHandler,
	getRootFolderHandler folderqueries.GetRootFolderHandler,
	updateFolderHandler foldercommands.UpdateFolderHandler,
	deleteFolderHandler foldercommands.DeleteFolderHandler,
) *FolderHandler {
	return &FolderHandler{
		createFolderHandler:  createFolderHandler,
		getFolderHandler:     getFolderHandler,
		getRootFolderHandler: getRootFolderHandler,
		updateFolderHandler:  updateFolderHandler,
		deleteFolderHandler:  deleteFolderHandler,
	}
}

func (h *FolderHandler) CreateFolder(c *gin.Context) {
	var req CreateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and sanitize folder name
	if err := helpers.ValidateFileName(req.Name); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid folder name: %v", err)})
		return
	}
	req.Name = helpers.SanitizeFileName(req.Name)

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Parse optional fields
	var teamID *uuid.UUID
	if req.TeamID != "" {
		parsed, err := uuid.Parse(req.TeamID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	var parentID *fileentities.FolderID
	if req.ParentID != "" {
		parsed, err := uuid.Parse(req.ParentID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid parent ID"})
			return
		}
		fID := fileentities.FolderID(parsed)
		parentID = &fID
	}

	var visibility fileentities.Visibility
	if req.Visibility != "" {
		visibility = fileentities.Visibility(req.Visibility)
	} else {
		visibility = fileentities.VisibilityPrivate
	}

	// Create command
	cmd := &foldercommands.CreateFolderCommand{
		UserID:      userID,
		TeamID:      teamID,
		ParentID:    parentID,
		Name:        req.Name,
		Description: req.Description,
		Visibility:  visibility,
	}

	// Execute command
	result, err := h.createFolderHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := h.folderEntityToResponse(result.Folder)
	utils.SuccessResponse(c, http.StatusCreated, "Folder created successfully", response)
}

func (h *FolderHandler) GetFolder(c *gin.Context) {
	folderIDStr := c.Param("id")
	folderID, err := uuid.Parse(folderIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Get user teams from context
	userTeams, err := helpers.GetUserTeams(c)
	if err != nil {
		// If teams aren't available, use empty slice (user not in any teams)
		userTeams = []string{}
	}

	// Create query
	query := &folderqueries.GetFolderQuery{
		FolderID:  folderID,
		UserID:    userID,
		UserTeams: userTeams,
	}

	// Execute query
	result, err := h.getFolderHandler.Handle(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	files := make([]helpers.FileResponse, len(result.Files))
	for i, file := range result.Files {
		files[i] = helpers.FileEntityToResponse(file)
	}

	subfolders := make([]FolderResponse, len(result.Subfolders))
	for i, folder := range result.Subfolders {
		subfolders[i] = h.folderEntityToResponse(folder)
	}

	response := GetFolderResponse{
		Folder:     h.folderEntityToResponse(result.Folder),
		Files:      files,
		Subfolders: subfolders,
	}

	utils.SuccessResponse(c, http.StatusOK, "Folder retrieved successfully", response)
}

func (h *FolderHandler) GetRootFolder(c *gin.Context) {
	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Get user teams from context
	userTeams, err := helpers.GetUserTeams(c)
	if err != nil {
		// If teams aren't available, use empty slice (user not in any teams)
		userTeams = []string{}
	}

	// Parse query parameters
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "50")
	teamIDStr := c.Query("teamId")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid offset"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid limit (must be 1-100)"})
		return
	}

	// Parse optional team ID
	var teamID *uuid.UUID
	if teamIDStr != "" {
		parsed, err := uuid.Parse(teamIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	// Create query
	query := &folderqueries.GetRootFolderQuery{
		UserID:    userID,
		TeamID:    teamID,
		UserTeams: userTeams,
		Offset:    offset,
		Limit:     limit,
	}

	// Execute query
	result, err := h.getRootFolderHandler.Handle(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	files := make([]helpers.FileResponse, len(result.Files))
	for i, file := range result.Files {
		files[i] = helpers.FileEntityToResponse(file)
	}

	folders := make([]FolderResponse, len(result.Folders))
	for i, folder := range result.Folders {
		folders[i] = h.folderEntityToResponse(folder)
	}

	response := GetRootFolderResponse{
		Files:        files,
		Folders:      folders,
		TotalFiles:   result.TotalFiles,
		TotalFolders: result.TotalFolders,
		Offset:       offset,
		Limit:        limit,
	}

	utils.SuccessResponse(c, http.StatusOK, "Root folder retrieved successfully", response)
}

func (h *FolderHandler) DeleteFolder(c *gin.Context) {
	folderIDStr := c.Param("id")
	folderID, err := uuid.Parse(folderIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Create command
	cmd := &foldercommands.DeleteFolderCommand{
		FolderID: folderID,
		UserID:   userID,
	}

	// Execute command
	_, err = h.deleteFolderHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Folder deleted successfully", nil)
}

type UpdateFolderRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	Visibility  *string `json:"visibility"`
	ParentID    *string `json:"parentId"`
}

func (h *FolderHandler) UpdateFolder(c *gin.Context) {
	folderIDStr := c.Param("id")
	folderID, err := uuid.Parse(folderIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Parse request body
	var req UpdateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and sanitize folder name if provided
	if req.Name != nil {
		if err := helpers.ValidateFileName(*req.Name); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid folder name: %v", err)})
			return
		}
		sanitized := helpers.SanitizeFileName(*req.Name)
		req.Name = &sanitized
	}

	// Convert visibility if provided
	var visibility *fileentities.Visibility
	if req.Visibility != nil {
		vis := fileentities.Visibility(*req.Visibility)
		visibility = &vis
	}

	// Convert parentID if provided
	var parentID *fileentities.FolderID
	if req.ParentID != nil {
		if *req.ParentID == "" || *req.ParentID == "null" {
			// Moving to root folder
			nilUUID := uuid.Nil
			parentFolderID := fileentities.FolderID(nilUUID)
			parentID = &parentFolderID
		} else {
			parentUUID, err := uuid.Parse(*req.ParentID)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "invalid parent folder ID"})
				return
			}
			parentFolderID := fileentities.FolderID(parentUUID)
			parentID = &parentFolderID
		}
	}

	// Create command
	cmd := &foldercommands.UpdateFolderCommand{
		FolderID:    folderID,
		UserID:      userID,
		Name:        req.Name,
		Description: req.Description,
		Visibility:  visibility,
		ParentID:    parentID,
	}

	// Execute command
	result, err := h.updateFolderHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := h.folderEntityToResponse(result.Folder)
	utils.SuccessResponse(c, http.StatusOK, "Folder updated successfully", response)
}

func (h *FolderHandler) folderEntityToResponse(folder *fileentities.Folder) FolderResponse {
	response := FolderResponse{
		ID:          folder.ID().String(),
		Name:        folder.Name(),
		Path:        folder.Path(),
		UserID:      folder.UserID().String(),
		Visibility:  string(folder.Visibility()),
		Description: folder.Description(),
		CreatedAt:   folder.CreatedAt().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   folder.UpdatedAt().Format("2006-01-02T15:04:05Z07:00"),
	}

	if folder.TeamID() != nil {
		teamIDStr := folder.TeamID().String()
		response.TeamID = &teamIDStr
	}

	if folder.ParentID() != nil {
		parentIDStr := folder.ParentID().String()
		response.ParentID = &parentIDStr
	}

	return response
}
