package handlers

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail"
)

// NewGenerationTracker creates a new generation tracker
func NewGenerationTracker() *GenerationTracker {
	return &GenerationTracker{
		inProgress: make(map[string]time.Time),
	}
}

// StartGeneration marks a thumbnail generation as in progress
func (gt *GenerationTracker) StartGeneration(fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) {
	gt.mu.Lock()
	defer gt.mu.Unlock()
	
	key := fmt.Sprintf("%s_%s_%s", fileID.String(), string(size), string(format))
	gt.inProgress[key] = time.Now()
}

// FinishGeneration marks a thumbnail generation as finished
func (gt *GenerationTracker) FinishGeneration(fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) {
	gt.mu.Lock()
	defer gt.mu.Unlock()
	
	key := fmt.Sprintf("%s_%s_%s", fileID.String(), string(size), string(format))
	delete(gt.inProgress, key)
}

// IsInProgress checks if a thumbnail generation is in progress
func (gt *GenerationTracker) IsInProgress(fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) bool {
	gt.mu.RLock()
	defer gt.mu.RUnlock()
	
	key := fmt.Sprintf("%s_%s_%s", fileID.String(), string(size), string(format))
	startTime, exists := gt.inProgress[key]
	
	if !exists {
		return false
	}
	
	// Clean up stale entries (older than 10 minutes)
	if time.Since(startTime) > 10*time.Minute {
		delete(gt.inProgress, key)
		return false
	}
	
	return true
}

// GetInProgressCount returns the number of thumbnails currently being generated
func (gt *GenerationTracker) GetInProgressCount() int {
	gt.mu.RLock()
	defer gt.mu.RUnlock()
	
	// Clean up stale entries
	now := time.Now()
	for key, startTime := range gt.inProgress {
		if now.Sub(startTime) > 10*time.Minute {
			delete(gt.inProgress, key)
		}
	}
	
	return len(gt.inProgress)
}

// NewSyncThumbnailProcessor creates a new synchronous thumbnail processor
func NewSyncThumbnailProcessor(thumbnailService *thumbnail.ThumbnailServiceImpl, config *ThumbnailConfig) *SyncThumbnailProcessor {
	return &SyncThumbnailProcessor{
		thumbnailService: thumbnailService,
		config:          config,
	}
}

// GenerateThumbnail generates a thumbnail synchronously
func (stp *SyncThumbnailProcessor) GenerateThumbnail(
	ctx context.Context,
	file *fileentities.File,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
	userID uuid.UUID,
) (*entities.Thumbnail, error) {
	// Use the existing service to process thumbnail synchronously
	thumbnails, err := stp.thumbnailService.ProcessJobSync(
		ctx,
		uuid.UUID(file.ID()),
		userID,
		file.Path(),
		file.MimeType(),
		[]entities.ThumbnailSize{size},
		[]entities.ThumbnailFormat{format},
	)
	
	if err != nil {
		return nil, fmt.Errorf("failed to generate thumbnail: %w", err)
	}
	
	if len(thumbnails) == 0 {
		return nil, fmt.Errorf("no thumbnails generated")
	}
	
	// Return the first (and should be only) thumbnail
	return thumbnails[0], nil
}

// CanProcessSync checks if a file can be processed synchronously
func (stp *SyncThumbnailProcessor) CanProcessSync(file *fileentities.File) bool {
	// Check file size
	if file.Size() > stp.config.MaxSyncFileSize {
		return false
	}
	
	// Check MIME type
	mimeType := file.MimeType()
	
	// Allow sync for supported image types
	for _, supportedType := range stp.config.SupportedImageTypes {
		if mimeType == supportedType {
			return true
		}
	}
	
	// Don't allow sync for video files
	if strings.HasPrefix(mimeType, "video/") {
		return false
	}
	
	return false
}

// NewPlaceholderManager creates a new placeholder manager
func NewPlaceholderManager(placeholderDir, defaultPlaceholder string) *PlaceholderManager {
	pm := &PlaceholderManager{
		placeholderDir: placeholderDir,
		defaultImage:   filepath.Join(placeholderDir, defaultPlaceholder),
		placeholders:   make(map[string]string),
	}
	
	// Initialize common placeholders
	pm.initializePlaceholders()
	
	return pm
}

// initializePlaceholders sets up common placeholder mappings
func (pm *PlaceholderManager) initializePlaceholders() {
	// Image placeholders
	pm.placeholders["image/jpeg"] = filepath.Join(pm.placeholderDir, "image.png")
	pm.placeholders["image/png"] = filepath.Join(pm.placeholderDir, "image.png")
	pm.placeholders["image/webp"] = filepath.Join(pm.placeholderDir, "image.png")
	pm.placeholders["image/gif"] = filepath.Join(pm.placeholderDir, "gif.png")
	
	// Video placeholders
	pm.placeholders["video/mp4"] = filepath.Join(pm.placeholderDir, "video.png")
	pm.placeholders["video/avi"] = filepath.Join(pm.placeholderDir, "video.png")
	pm.placeholders["video/quicktime"] = filepath.Join(pm.placeholderDir, "video.png")
	
	// Document placeholders
	pm.placeholders["application/pdf"] = filepath.Join(pm.placeholderDir, "pdf.png")
	pm.placeholders["application/msword"] = filepath.Join(pm.placeholderDir, "doc.png")
	pm.placeholders["application/vnd.ms-excel"] = filepath.Join(pm.placeholderDir, "excel.png")
	
	// Default for unknown types
	pm.placeholders["default"] = pm.defaultImage
}

// GetPlaceholder returns the appropriate placeholder for a MIME type
func (pm *PlaceholderManager) GetPlaceholder(mimeType string) string {
	// Try exact match first
	if placeholder, exists := pm.placeholders[mimeType]; exists {
		return placeholder
	}
	
	// Try general type match
	if strings.HasPrefix(mimeType, "image/") {
		if placeholder, exists := pm.placeholders["image/jpeg"]; exists {
			return placeholder
		}
	}
	
	if strings.HasPrefix(mimeType, "video/") {
		if placeholder, exists := pm.placeholders["video/mp4"]; exists {
			return placeholder
		}
	}
	
	if strings.HasPrefix(mimeType, "application/pdf") {
		if placeholder, exists := pm.placeholders["application/pdf"]; exists {
			return placeholder
		}
	}
	
	// Return default placeholder
	return pm.defaultImage
}

// AddPlaceholder adds a custom placeholder for a MIME type
func (pm *PlaceholderManager) AddPlaceholder(mimeType, placeholderPath string) {
	pm.placeholders[mimeType] = placeholderPath
}

// GetLoadingPlaceholder returns a loading placeholder (can be animated)
func (pm *PlaceholderManager) GetLoadingPlaceholder() string {
	loadingPlaceholder := filepath.Join(pm.placeholderDir, "loading.gif")
	return loadingPlaceholder
}

// ThumbnailGenerationMetrics tracks metrics for thumbnail generation
type ThumbnailGenerationMetrics struct {
	mu                    sync.RWMutex
	syncGenerations       int64
	asyncGenerations      int64
	placeholderServes     int64
	syncSuccessCount      int64
	syncFailureCount      int64
	asyncSuccessCount     int64
	asyncFailureCount     int64
	averageSyncDuration   time.Duration
	averageAsyncDuration  time.Duration
}

// NewThumbnailGenerationMetrics creates a new metrics tracker
func NewThumbnailGenerationMetrics() *ThumbnailGenerationMetrics {
	return &ThumbnailGenerationMetrics{}
}

// RecordSyncGeneration records a synchronous generation
func (tgm *ThumbnailGenerationMetrics) RecordSyncGeneration(duration time.Duration, success bool) {
	tgm.mu.Lock()
	defer tgm.mu.Unlock()
	
	tgm.syncGenerations++
	if success {
		tgm.syncSuccessCount++
	} else {
		tgm.syncFailureCount++
	}
	
	// Update average duration (simple moving average)
	if tgm.averageSyncDuration == 0 {
		tgm.averageSyncDuration = duration
	} else {
		tgm.averageSyncDuration = (tgm.averageSyncDuration + duration) / 2
	}
}

// RecordAsyncGeneration records an asynchronous generation
func (tgm *ThumbnailGenerationMetrics) RecordAsyncGeneration(success bool) {
	tgm.mu.Lock()
	defer tgm.mu.Unlock()
	
	tgm.asyncGenerations++
	if success {
		tgm.asyncSuccessCount++
	} else {
		tgm.asyncFailureCount++
	}
}

// RecordPlaceholderServe records a placeholder being served
func (tgm *ThumbnailGenerationMetrics) RecordPlaceholderServe() {
	tgm.mu.Lock()
	defer tgm.mu.Unlock()
	
	tgm.placeholderServes++
}

// GetMetrics returns current metrics
func (tgm *ThumbnailGenerationMetrics) GetMetrics() map[string]interface{} {
	tgm.mu.RLock()
	defer tgm.mu.RUnlock()
	
	syncSuccessRate := float64(0)
	if tgm.syncGenerations > 0 {
		syncSuccessRate = float64(tgm.syncSuccessCount) / float64(tgm.syncGenerations)
	}
	
	asyncSuccessRate := float64(0)
	if tgm.asyncGenerations > 0 {
		asyncSuccessRate = float64(tgm.asyncSuccessCount) / float64(tgm.asyncGenerations)
	}
	
	return map[string]interface{}{
		"sync_generations":       tgm.syncGenerations,
		"async_generations":      tgm.asyncGenerations,
		"placeholder_serves":     tgm.placeholderServes,
		"sync_success_rate":      syncSuccessRate,
		"async_success_rate":     asyncSuccessRate,
		"average_sync_duration":  tgm.averageSyncDuration.Milliseconds(),
		"average_async_duration": tgm.averageAsyncDuration.Milliseconds(),
	}
}

// FileAnalyzer analyzes files to determine optimal processing strategy
type FileAnalyzer struct {
	config *ThumbnailConfig
}

// NewFileAnalyzer creates a new file analyzer
func NewFileAnalyzer(config *ThumbnailConfig) *FileAnalyzer {
	return &FileAnalyzer{
		config: config,
	}
}

// AnalyzeFile analyzes a file and returns processing recommendations
func (fa *FileAnalyzer) AnalyzeFile(file *fileentities.File) *FileAnalysis {
	analysis := &FileAnalysis{
		FileID:      uuid.UUID(file.ID()),
		Size:        file.Size(),
		MimeType:    file.MimeType(),
		IsSupported: fa.isSupported(file.MimeType()),
	}
	
	// Determine complexity
	analysis.Complexity = fa.determineComplexity(file)
	
	// Determine optimal processing strategy
	analysis.RecommendedStrategy = fa.determineStrategy(analysis)
	
	// Estimate processing time
	analysis.EstimatedProcessingTime = fa.estimateProcessingTime(analysis)
	
	return analysis
}

// FileAnalysis contains analysis results for a file
type FileAnalysis struct {
	FileID                   uuid.UUID
	Size                     int64
	MimeType                 string
	IsSupported              bool
	Complexity               FileComplexity
	RecommendedStrategy      ProcessingStrategy
	EstimatedProcessingTime  time.Duration
}

// FileComplexity represents the complexity of processing a file
type FileComplexity int

const (
	ComplexityLow FileComplexity = iota
	ComplexityMedium
	ComplexityHigh
)

// ProcessingStrategy represents the recommended processing strategy
type ProcessingStrategy int

const (
	StrategySync ProcessingStrategy = iota
	StrategyAsync
	StrategySkip
)

// isSupported checks if a file type is supported for thumbnail generation
func (fa *FileAnalyzer) isSupported(mimeType string) bool {
	supportedTypes := append(fa.config.SupportedImageTypes, fa.config.SupportedVideoTypes...)
	
	for _, supportedType := range supportedTypes {
		if mimeType == supportedType {
			return true
		}
	}
	
	return false
}

// determineComplexity determines the complexity of processing a file
func (fa *FileAnalyzer) determineComplexity(file *fileentities.File) FileComplexity {
	mimeType := file.MimeType()
	size := file.Size()
	
	// High complexity files
	if strings.HasPrefix(mimeType, "video/") {
		return ComplexityHigh
	}
	
	if mimeType == "image/gif" || mimeType == "image/tiff" {
		return ComplexityHigh
	}
	
	if size > 50*1024*1024 { // Files larger than 50MB
		return ComplexityHigh
	}
	
	// Medium complexity files
	if size > 10*1024*1024 { // Files larger than 10MB
		return ComplexityMedium
	}
	
	if mimeType == "image/png" && size > 5*1024*1024 { // Large PNG files
		return ComplexityMedium
	}
	
	// Low complexity files
	return ComplexityLow
}

// determineStrategy determines the optimal processing strategy
func (fa *FileAnalyzer) determineStrategy(analysis *FileAnalysis) ProcessingStrategy {
	if !analysis.IsSupported {
		return StrategySkip
	}
	
	switch analysis.Complexity {
	case ComplexityLow:
		return StrategySync
	case ComplexityMedium:
		if analysis.Size <= fa.config.MaxSyncFileSize {
			return StrategySync
		}
		return StrategyAsync
	case ComplexityHigh:
		return StrategyAsync
	default:
		return StrategyAsync
	}
}

// estimateProcessingTime estimates how long processing will take
func (fa *FileAnalyzer) estimateProcessingTime(analysis *FileAnalysis) time.Duration {
	baseTime := 1 * time.Second
	
	// Adjust based on file size
	sizeFactor := float64(analysis.Size) / float64(1024*1024) // MB
	timeMultiplier := 1.0 + (sizeFactor * 0.1)               // 100ms per MB
	
	// Adjust based on complexity
	switch analysis.Complexity {
	case ComplexityLow:
		timeMultiplier *= 1.0
	case ComplexityMedium:
		timeMultiplier *= 2.0
	case ComplexityHigh:
		timeMultiplier *= 5.0
	}
	
	// Adjust based on file type
	if strings.HasPrefix(analysis.MimeType, "video/") {
		timeMultiplier *= 10.0 // Video processing is much slower
	}
	
	return time.Duration(float64(baseTime) * timeMultiplier)
}