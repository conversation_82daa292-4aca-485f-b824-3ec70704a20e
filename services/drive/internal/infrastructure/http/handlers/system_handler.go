package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	commonmiddleware "github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	filequeries "github.com/swork-team/platform/services/drive/internal/application/file/queries"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
)

// SystemHandler handles system-level operations like search and statistics
type SystemHandler struct {
	fileHandler      *FileHandler
	fileService      fileservices.FileService
	folderRepository filerepositories.FolderRepository
}

// NewSystemHandler creates a new system handler
func NewSystemHandler(
	fileHandler *FileHandler,
	fileService fileservices.FileService,
	folderRepository filerepositories.FolderRepository,
) *SystemHandler {
	return &SystemHandler{
		fileHandler:      fileHandler,
		fileService:      fileService,
		folderRepository: folderRepository,
	}
}

// SearchFiles handles file search operations
func (sh *SystemHandler) SearchFiles(c *gin.Context) {
	// Get user ID from context using proper middleware helper
	userIDStr, err := commonmiddleware.GetUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
		return
	}

	// Parse query parameters
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "search query required"})
		return
	}

	teamIDStr := c.Query("teamId")
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "20")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid offset"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid limit (must be 1-100)"})
		return
	}

	var teamID *uuid.UUID
	if teamIDStr != "" {
		parsed, err := uuid.Parse(teamIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	// Create search query
	searchQuery := &filequeries.SearchFilesQuery{
		UserID: userID,
		TeamID: teamID,
		Query:  query,
		Offset: offset,
		Limit:  limit,
	}

	// Execute search
	result, err := sh.fileHandler.SearchFilesHandler().Handle(c.Request.Context(), searchQuery)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "search failed"})
		return
	}

	// Convert files to response format
	files := make([]map[string]any, len(result.Files))
	for i, file := range result.Files {
		var folderIDStr any
		if file.FolderID() != nil {
			folderIDStr = file.FolderID().String()
		}

		files[i] = map[string]any{
			"id":        file.ID().String(),
			"name":      file.Name(),
			"size":      file.Size(),
			"mimeType":  file.MimeType(),
			"createdAt": file.CreatedAt(),
			"updatedAt": file.UpdatedAt(),
			"folderId":  folderIDStr,
		}
	}

	utils.SuccessResponse(c, http.StatusOK, "Search completed successfully", gin.H{
		"files":      files,
		"totalCount": result.TotalCount,
		"offset":     offset,
		"limit":      limit,
		"query":      query,
	})
}

// GetStats handles retrieving file and folder statistics
func (sh *SystemHandler) GetStats(c *gin.Context) {
	// Get user ID from context using proper middleware helper
	userIDStr, err := commonmiddleware.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid user ID", err)
		return
	}

	// Get file statistics using the FileService
	fileStats, err := sh.fileService.GetFileStatistics(c.Request.Context(), userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get file statistics", err)
		return
	}

	// Get folder statistics using the folder repository
	totalFolders := int64(0)
	sharedFiles := int64(0)  // TODO: implement shared files count
	recentFiles := int64(0) // TODO: implement recent files count

	// Get folder count from folder repository
	if folderCount, err := sh.folderRepository.CountByUserID(c.Request.Context(), userID); err != nil {
		// Log error but don't fail the request
		totalFolders = 0
	} else {
		totalFolders = folderCount
	}

	utils.SuccessResponse(c, http.StatusOK, "Statistics retrieved successfully", gin.H{
		"totalFiles":   fileStats.TotalFiles,
		"totalSize":    fileStats.TotalSize,
		"totalFolders": totalFolders,
		"recentFiles":  recentFiles,
		"sharedFiles":  sharedFiles,
		"filesByType":  fileStats.FilesByType,
		"sizeByType":   fileStats.SizeByType,
		"userID":       userID.String(),
		"timestamp":    time.Now().UTC(),
	})
}