package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
)

// AdminHandler handles administrative operations
type AdminHandler struct {
	storagePort ports.StoragePort
}

// NewAdminHandler creates a new admin handler
func NewAdminHandler(storagePort ports.StoragePort) *AdminHandler {
	return &AdminHandler{
		storagePort: storagePort,
	}
}

// GetSystemHealth handles system health checks for admin users
func (ah *AdminHandler) GetSystemHealth(c *gin.Context) {
	ctx := c.Request.Context()

	health := gin.H{
		"status":     "healthy",
		"timestamp":  time.Now().UTC(),
		"service":    "drive",
		"version":    "1.0.0",
		"components": gin.H{},
	}

	// Check storage health
	storageHealthy := true
	if err := ah.storagePort.Health(ctx); err != nil {
		storageHealthy = false
	}
	health["components"].(gin.H)["storage"] = gin.H{
		"status":  map[bool]string{true: "healthy", false: "unhealthy"}[storageHealthy],
		"healthy": storageHealthy,
	}

	// Overall status based on components
	overallHealthy := storageHealthy

	if !overallHealthy {
		health["status"] = "degraded"
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Service health degraded", nil)
	} else {
		utils.SuccessResponse(c, http.StatusOK, "Service is healthy", health)
	}
}

// GetAuditLogs handles retrieving audit logs for admin users
func (ah *AdminHandler) GetAuditLogs(c *gin.Context) {
	// Parse query parameters for filtering
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "50")
	userIDStr := c.Query("userId")
	eventType := c.Query("eventType")
	startDate := c.Query("startDate")
	endDate := c.Query("endDate")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid offset"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid limit (must be 1-100)"})
		return
	}

	// Validate date formats if provided
	if startDate != "" {
		if _, err := time.Parse("2006-01-02", startDate); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid startDate format, use YYYY-MM-DD"})
			return
		}
	}

	if endDate != "" {
		if _, err := time.Parse("2006-01-02", endDate); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid endDate format, use YYYY-MM-DD"})
			return
		}
	}

	// Mock audit logs for demonstration
	mockLogs := []gin.H{
		{
			"id":        uuid.New().String(),
			"timestamp": time.Now().Add(-1 * time.Hour).UTC(),
			"eventType": "file_upload",
			"userId":    userIDStr,
			"resource":  "file",
			"action":    "create",
			"details":   gin.H{"fileName": "document.pdf", "size": 1024},
			"ipAddress": c.ClientIP(),
			"userAgent": c.GetHeader("User-Agent"),
		},
		{
			"id":        uuid.New().String(),
			"timestamp": time.Now().Add(-2 * time.Hour).UTC(),
			"eventType": "file_share",
			"userId":    userIDStr,
			"resource":  "file",
			"action":    "share",
			"details":   gin.H{"fileName": "report.xlsx", "sharedWith": "<EMAIL>"},
			"ipAddress": c.ClientIP(),
			"userAgent": c.GetHeader("User-Agent"),
		},
	}

	// Apply filtering if eventType is specified
	var filteredLogs []gin.H
	for _, log := range mockLogs {
		if eventType == "" || log["eventType"] == eventType {
			filteredLogs = append(filteredLogs, log)
		}
	}

	// Apply pagination
	start := offset
	end := offset + limit
	if start > len(filteredLogs) {
		start = len(filteredLogs)
	}
	if end > len(filteredLogs) {
		end = len(filteredLogs)
	}

	paginatedLogs := filteredLogs[start:end]

	utils.SuccessResponse(c, http.StatusOK, "Audit logs retrieved successfully", gin.H{
		"logs":       paginatedLogs,
		"totalCount": len(filteredLogs),
		"offset":     offset,
		"limit":      limit,
		"filters": gin.H{
			"userId":    userIDStr,
			"eventType": eventType,
			"startDate": startDate,
			"endDate":   endDate,
		},
		"note": "Mock audit logs for demonstration purposes",
	})
}