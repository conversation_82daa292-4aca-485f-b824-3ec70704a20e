package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/utils"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/helpers"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail"
)

type ThumbnailHandler struct {
	thumbnailService   *thumbnail.ThumbnailServiceImpl
	fileRepository     filerepositories.FileRepository
	placeholderManager *PlaceholderManager
	generationTracker  *GenerationTracker
	syncProcessor      *SyncThumbnailProcessor
	config             *ThumbnailConfig
	metrics            *ThumbnailGenerationMetrics
}

// ThumbnailConfig holds configuration for thumbnail processing
type ThumbnailConfig struct {
	// Sync generation thresholds
	MaxSyncFileSize int64         // Files smaller than this are processed synchronously
	MaxSyncTimeout  time.Duration // Max time for sync processing

	// File type settings
	SupportedImageTypes []string
	SupportedVideoTypes []string

	// Placeholder settings
	PlaceholderDir     string
	DefaultPlaceholder string

	// Cache settings
	CacheEnabled bool
	CacheMaxAge  time.Duration
}

// GenerationTracker keeps track of ongoing thumbnail generation
type GenerationTracker struct {
	inProgress map[string]time.Time // fileID+size+format -> started time
	mu         sync.RWMutex
}

// SyncThumbnailProcessor handles synchronous thumbnail generation
type SyncThumbnailProcessor struct {
	thumbnailService *thumbnail.ThumbnailServiceImpl
	config           *ThumbnailConfig
}

// PlaceholderManager handles placeholder images
type PlaceholderManager struct {
	placeholderDir string
	defaultImage   string
	placeholders   map[string]string // mimeType -> placeholder path
}

type ProcessThumbnailRequest struct {
	FileID     string                     `json:"fileId" binding:"required"`
	SourcePath string                     `json:"sourcePath,omitempty"`
	MimeType   string                     `json:"mimeType,omitempty"`
	Sizes      []entities.ThumbnailSize   `json:"sizes"`
	Formats    []entities.ThumbnailFormat `json:"formats"`
	Priority   entities.JobPriority       `json:"priority"`
	Async      bool                       `json:"async"`
}

type ProcessThumbnailResponse struct {
	JobID      string           `json:"job_id,omitempty"`
	Thumbnails []*ThumbnailInfo `json:"thumbnails,omitempty"`
	Status     string           `json:"status"`
	Message    string           `json:"message"`
}

type ThumbnailInfo struct {
	ID          string                   `json:"id"`
	FileID      string                   `json:"file_id"`
	Size        entities.ThumbnailSize   `json:"size"`
	Format      entities.ThumbnailFormat `json:"format"`
	Width       int                      `json:"width"`
	Height      int                      `json:"height"`
	FileSize    int64                    `json:"file_size"`
	StoragePath string                   `json:"storage_path"`
	Status      entities.ThumbnailStatus `json:"status"`
	CreatedAt   time.Time                `json:"created_at"`
	URL         string                   `json:"url"`
}

type JobStatusResponse struct {
	JobID         string             `json:"job_id"`
	Status        entities.JobStatus `json:"status"`
	Progress      int                `json:"progress"`
	CreatedAt     time.Time          `json:"created_at"`
	StartedAt     *time.Time         `json:"started_at,omitempty"`
	CompletedAt   *time.Time         `json:"completed_at,omitempty"`
	ErrorMessage  string             `json:"error_message,omitempty"`
	ProcessingLog []string           `json:"processing_log,omitempty"`
	Thumbnails    []*ThumbnailInfo   `json:"thumbnails,omitempty"`
}

type BatchProcessRequest struct {
	Jobs []ProcessThumbnailRequest `json:"jobs" binding:"required"`
}

type BatchProcessResponse struct {
	BatchID     string    `json:"batch_id"`
	JobIDs      []string  `json:"job_ids"`
	TotalJobs   int       `json:"total_jobs"`
	Status      string    `json:"status"`
	SubmittedAt time.Time `json:"submitted_at"`
}

type ThumbnailStatsResponse struct {
	TotalThumbnails    int64                              `json:"total_thumbnails"`
	ThumbnailsByStatus map[entities.ThumbnailStatus]int64 `json:"thumbnails_by_status"`
	ThumbnailsBySize   map[entities.ThumbnailSize]int64   `json:"thumbnails_by_size"`
	ThumbnailsByFormat map[entities.ThumbnailFormat]int64 `json:"thumbnails_by_format"`
	StorageUsage       int64                              `json:"storage_usage"`
	QueueStats         *QueueStatsResponse                `json:"queue_stats"`
	ServiceHealth      map[string]interface{}             `json:"service_health"`
}

type QueueStatsResponse struct {
	QueueLength      int64                        `json:"queue_length"`
	ProcessingCount  int64                        `json:"processing_count"`
	CompletedCount   int64                        `json:"completed_count"`
	FailedCount      int64                        `json:"failed_count"`
	RetryCount       int64                        `json:"retry_count"`
	DeadCount        int64                        `json:"dead_count"`
	TotalProcessed   int64                        `json:"total_processed"`
	ThroughputPerMin float64                      `json:"throughput_per_min"`
	ErrorRate        float64                      `json:"error_rate"`
	StatusCounts     map[entities.JobStatus]int64 `json:"status_counts"`
}

type ValidateRequest struct {
	MimeType string `json:"mime_type" binding:"required"`
	FileSize int64  `json:"file_size"`
}

type ValidateResponse struct {
	Valid            bool                       `json:"valid"`
	SupportedSizes   []entities.ThumbnailSize   `json:"supported_sizes"`
	SupportedFormats []entities.ThumbnailFormat `json:"supported_formats"`
	Message          string                     `json:"message,omitempty"`
}

func NewThumbnailHandler(thumbnailService *thumbnail.ThumbnailServiceImpl, fileRepository filerepositories.FileRepository, config *ThumbnailConfig) *ThumbnailHandler {
	if config == nil {
		config = DefaultThumbnailConfig()
	}
	return &ThumbnailHandler{
		thumbnailService:   thumbnailService,
		fileRepository:     fileRepository,
		placeholderManager: NewPlaceholderManager(config.PlaceholderDir, config.DefaultPlaceholder),
		generationTracker:  NewGenerationTracker(),
		syncProcessor:      NewSyncThumbnailProcessor(thumbnailService, config),
		config:             config,
		metrics:            NewThumbnailGenerationMetrics(),
	}
}

// GetThumbnailService returns the thumbnail service for external access
func (h *ThumbnailHandler) GetThumbnailService() *thumbnail.ThumbnailServiceImpl {
	return h.thumbnailService
}

// POST /thumbnails/process
func (h *ThumbnailHandler) ProcessThumbnail(c *gin.Context) {
	var req ProcessThumbnailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse file ID
	fileID, err := uuid.Parse(req.FileID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid fileId"})
		return
	}

	// Extract user ID from authenticated context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	// Look up file information if sourcePath or mimeType not provided
	if req.SourcePath == "" || req.MimeType == "" {
		file, err := h.fileRepository.GetByID(c.Request.Context(), fileentities.FileID(fileID))
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("file not found: %v", err)})
			return
		}

		// Additional safety check to prevent nil pointer dereference
		if file == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "file not found or has been deleted"})
			return
		}

		if req.SourcePath == "" {
			req.SourcePath = file.Path()
		}
		if req.MimeType == "" {
			req.MimeType = file.MimeType()
		}
	}

	// Set defaults
	if len(req.Sizes) == 0 {
		req.Sizes = []entities.ThumbnailSize{entities.ThumbnailSizeMedium}
	}
	if len(req.Formats) == 0 {
		req.Formats = []entities.ThumbnailFormat{entities.ThumbnailFormatJPEG}
	}

	if req.Async {
		// Asynchronous processing
		job, err := h.thumbnailService.CreateThumbnailJob(
			c.Request.Context(),
			fileID, userID,
			req.SourcePath, req.MimeType,
			req.Sizes, req.Formats,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		response := ProcessThumbnailResponse{
			JobID:   job.ID().String(),
			Status:  "accepted",
			Message: "Thumbnail processing job submitted",
		}

		utils.SuccessResponse(c, http.StatusAccepted, "Thumbnail processing job submitted", response)
	} else {
		// Synchronous processing - create separate context to avoid HTTP request timeout
		processCtx, cancel := context.WithTimeout(context.Background(), h.config.MaxSyncTimeout)
		defer cancel()
		
		thumbnails, err := h.thumbnailService.ProcessJobSync(
			processCtx,
			fileID, userID,
			req.SourcePath, req.MimeType,
			req.Sizes, req.Formats,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		thumbnailInfos := make([]*ThumbnailInfo, len(thumbnails))
		for i, thumbnail := range thumbnails {
			thumbnailInfos[i] = h.convertToThumbnailInfo(thumbnail)
		}

		response := ProcessThumbnailResponse{
			Thumbnails: thumbnailInfos,
			Status:     "completed",
			Message:    "Thumbnails processed successfully",
		}

		utils.SuccessResponse(c, http.StatusOK, "Thumbnails processed successfully", response)
	}
}

// POST /thumbnails/process/sync
func (h *ThumbnailHandler) ProcessThumbnailSync(c *gin.Context) {
	var req ProcessThumbnailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Force synchronous processing
	req.Async = false
	h.ProcessThumbnail(c)
}

// GET /thumbnails/:file_id
func (h *ThumbnailHandler) GetThumbnailsForFile(c *gin.Context) {
	fileIDStr := c.Param("file_id")
	fileID, err := uuid.Parse(fileIDStr)
	_ = fileID
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file_id"})
		return
	}

	thumbnails, err := h.thumbnailService.GetThumbnailsForFile(c.Request.Context(), fileID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	thumbnailInfos := make([]*ThumbnailInfo, len(thumbnails))
	for i, thumbnail := range thumbnails {
		thumbnailInfos[i] = h.convertToThumbnailInfo(thumbnail)
	}

	utils.SuccessResponse(c, http.StatusOK, "Thumbnails retrieved successfully", gin.H{
		"file_id":    fileIDStr,
		"thumbnails": thumbnailInfos,
		"count":      len(thumbnailInfos),
	})
}

// GET /thumbnails/:file_id/:size
func (h *ThumbnailHandler) GetThumbnail(c *gin.Context) {
	fileIDStr := c.Param("file_id")
	sizeStr := c.Param("size")

	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file_id"})
		return
	}

	if !entities.IsValidSize(sizeStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid size"})
		return
	}

	size := entities.ThumbnailSize(sizeStr)

	// Get format from query param or default to JPEG
	formatStr := c.DefaultQuery("format", "jpeg")
	if !entities.IsValidFormat(formatStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid format"})
		return
	}

	format := entities.ThumbnailFormat(formatStr)

	thumbnail, err := h.thumbnailService.GetThumbnail(c.Request.Context(), fileID, size, format)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "thumbnail not found"})
		return
	}

	// Check if thumbnail file exists
	if thumbnail.StoragePath() == "" {
		c.JSON(http.StatusNotFound, gin.H{"error": "thumbnail file not available"})
		return
	}

	// Set appropriate headers for image serving
	c.Header("Content-Type", h.getMimeTypeForFormat(format))
	c.Header("Cache-Control", "public, max-age=3600") // Cache for 1 hour
	c.Header("ETag", thumbnail.Checksum())

	// Check if client has cached version
	if match := c.GetHeader("If-None-Match"); match == thumbnail.Checksum() {
		c.Status(http.StatusNotModified)
		return
	}

	// Serve the thumbnail file
	c.File(thumbnail.StoragePath())
}

// GET /thumbnails/:file_id/:size/info
func (h *ThumbnailHandler) GetThumbnailInfo(c *gin.Context) {
	fileIDStr := c.Param("file_id")
	sizeStr := c.Param("size")

	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file_id"})
		return
	}

	if !entities.IsValidSize(sizeStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid size"})
		return
	}

	size := entities.ThumbnailSize(sizeStr)

	// Get format from query param or default to JPEG
	formatStr := c.DefaultQuery("format", "jpeg")
	if !entities.IsValidFormat(formatStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid format"})
		return
	}

	format := entities.ThumbnailFormat(formatStr)

	thumbnail, err := h.thumbnailService.GetThumbnail(c.Request.Context(), fileID, size, format)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "thumbnail not found"})
		return
	}

	thumbnailInfo := h.convertToThumbnailInfo(thumbnail)
	utils.SuccessResponse(c, http.StatusOK, "Thumbnail retrieved successfully", thumbnailInfo)
}

// DELETE /thumbnails/:file_id
func (h *ThumbnailHandler) DeleteThumbnails(c *gin.Context) {
	fileIDStr := c.Param("file_id")
	fileID, err := uuid.Parse(fileIDStr)
	_ = fileID
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file_id"})
		return
	}

	if err := h.thumbnailService.DeleteThumbnailsForFile(c.Request.Context(), fileID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Thumbnails deleted successfully", gin.H{
		"file_id":    fileIDStr,
		"deleted_at": time.Now(),
	})
}

// POST /thumbnails/:file_id/:size/refresh
func (h *ThumbnailHandler) RefreshThumbnail(c *gin.Context) {
	fileIDStr := c.Param("file_id")
	sizeStr := c.Param("size")

	fileID, err := uuid.Parse(fileIDStr)
	_ = fileID
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file_id"})
		return
	}

	if !entities.IsValidSize(sizeStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid size"})
		return
	}

	size := entities.ThumbnailSize(sizeStr)

	// Get format from query param or default to JPEG
	formatStr := c.DefaultQuery("format", "jpeg")
	if !entities.IsValidFormat(formatStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid format"})
		return
	}

	format := entities.ThumbnailFormat(formatStr)

	// This would trigger a refresh job
	utils.SuccessResponse(c, http.StatusAccepted, "Thumbnail refresh initiated", gin.H{
		"file_id": fileIDStr,
		"size":    size,
		"format":  format,
	})
}

// GET /thumbnails/jobs/:job_id
func (h *ThumbnailHandler) GetJobStatus(c *gin.Context) {
	jobIDStr := c.Param("job_id")
	jobID := entities.JobIDFromString(jobIDStr)

	job, err := h.thumbnailService.GetJobStatus(c.Request.Context(), jobID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "job not found"})
		return
	}

	response := JobStatusResponse{
		JobID:         job.ID().String(),
		Status:        job.Status(),
		CreatedAt:     job.CreatedAt(),
		StartedAt:     job.StartedAt(),
		CompletedAt:   job.CompletedAt(),
		ErrorMessage:  job.ErrorMessage(),
		ProcessingLog: job.ProcessingLog(),
	}

	// Calculate progress
	if job.Status() == entities.JobStatusCompleted {
		response.Progress = 100
	} else if job.Status() == entities.JobStatusProcessing {
		response.Progress = 50 // Simplified progress calculation
	} else {
		response.Progress = 0
	}

	// Get thumbnails if job is completed
	if job.Status() == entities.JobStatusCompleted {
		thumbnails, err := h.thumbnailService.GetThumbnailsForFile(c.Request.Context(), job.FileID())
		if err == nil {
			response.Thumbnails = make([]*ThumbnailInfo, len(thumbnails))
			for i, thumbnail := range thumbnails {
				response.Thumbnails[i] = h.convertToThumbnailInfo(thumbnail)
			}
		}
	}

	utils.SuccessResponse(c, http.StatusOK, "Job status retrieved successfully", response)
}

// POST /thumbnails/batch
func (h *ThumbnailHandler) ProcessBatch(c *gin.Context) {
	var req BatchProcessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.Jobs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "no jobs provided"})
		return
	}

	// Extract user ID from authenticated context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	batchID := uuid.New().String()
	jobIDs := make([]string, 0, len(req.Jobs))

	// Submit all jobs
	for _, jobReq := range req.Jobs {
		fileID, err := uuid.Parse(jobReq.FileID)
		if err != nil {
			continue // Skip invalid jobs
		}

		// Set defaults
		if len(jobReq.Sizes) == 0 {
			jobReq.Sizes = []entities.ThumbnailSize{entities.ThumbnailSizeMedium}
		}
		if len(jobReq.Formats) == 0 {
			jobReq.Formats = []entities.ThumbnailFormat{entities.ThumbnailFormatJPEG}
		}

		job, err := h.thumbnailService.CreateThumbnailJob(
			c.Request.Context(),
			fileID, userID,
			jobReq.SourcePath, jobReq.MimeType,
			jobReq.Sizes, jobReq.Formats,
		)
		if err != nil {
			continue // Skip failed jobs
		}

		jobIDs = append(jobIDs, job.ID().String())
	}

	response := BatchProcessResponse{
		BatchID:     batchID,
		JobIDs:      jobIDs,
		TotalJobs:   len(jobIDs),
		Status:      "submitted",
		SubmittedAt: time.Now(),
	}

	utils.SuccessResponse(c, http.StatusAccepted, "Batch processing submitted", response)
}

// GET /thumbnails/stats
func (h *ThumbnailHandler) GetStatistics(c *gin.Context) {
	stats, err := h.thumbnailService.GetStatistics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	health := h.thumbnailService.GetHealthStatus(c.Request.Context())

	response := ThumbnailStatsResponse{
		ServiceHealth: health,
	}

	// Convert stats to response format
	if serviceStats, ok := stats["service"].(map[string]interface{}); ok {
		if thumbnailStats, ok := serviceStats["thumbnails"].(map[string]interface{}); ok {
			if total, ok := thumbnailStats["total"].(int64); ok {
				response.TotalThumbnails = total
			}
			if usage, ok := thumbnailStats["storage_usage"].(int64); ok {
				response.StorageUsage = usage
			}
		}
	}

	if queueStats, ok := stats["queue"].(map[string]interface{}); ok {
		response.QueueStats = &QueueStatsResponse{}
		if queueLength, ok := queueStats["queue_length"].(int64); ok {
			response.QueueStats.QueueLength = queueLength
		}
		if processingCount, ok := queueStats["processing_count"].(int64); ok {
			response.QueueStats.ProcessingCount = processingCount
		}
		if completedCount, ok := queueStats["completed_count"].(int64); ok {
			response.QueueStats.CompletedCount = completedCount
		}
		if failedCount, ok := queueStats["failed_count"].(int64); ok {
			response.QueueStats.FailedCount = failedCount
		}
	}

	utils.SuccessResponse(c, http.StatusOK, "Thumbnail statistics retrieved successfully", response)
}

// GET /thumbnails/health
func (h *ThumbnailHandler) GetHealth(c *gin.Context) {
	health := h.thumbnailService.GetHealthStatus(c.Request.Context())

	if health["status"] != "healthy" {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Thumbnail service is unhealthy", nil)
	} else {
		utils.SuccessResponse(c, http.StatusOK, "Thumbnail service is healthy", health)
	}
}

// GET /thumbnails/formats
func (h *ThumbnailHandler) GetSupportedFormats(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Supported formats retrieved successfully", gin.H{
		"sizes":   entities.GetSupportedSizes(),
		"formats": entities.GetSupportedFormats(),
	})
}

// POST /thumbnails/validate
func (h *ThumbnailHandler) ValidateRequest(c *gin.Context) {
	var req ValidateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response := ValidateResponse{
		Valid:            true,
		SupportedSizes:   entities.GetSupportedSizes(),
		SupportedFormats: entities.GetSupportedFormats(),
	}

	// Validate MIME type
	supportedTypes := []string{
		"image/jpeg", "image/png", "image/webp", "image/gif", "image/bmp", "image/tiff",
		"video/mp4", "video/avi", "video/quicktime", "video/x-msvideo", "video/x-flv",
		"video/webm", "video/x-matroska", "video/x-m4v",
	}

	found := false
	for _, supportedType := range supportedTypes {
		if req.MimeType == supportedType {
			found = true
			break
		}
	}

	if !found {
		response.Valid = false
		response.Message = "Unsupported MIME type: " + req.MimeType
	}

	// Validate file size
	if req.FileSize > 0 {
		maxImageSize := int64(100 * 1024 * 1024)  // 100MB
		maxVideoSize := int64(1024 * 1024 * 1024) // 1GB

		if strings.HasPrefix(req.MimeType, "image/") && req.FileSize > maxImageSize {
			response.Valid = false
			response.Message = "Image file too large (max 100MB)"
		} else if strings.HasPrefix(req.MimeType, "video/") && req.FileSize > maxVideoSize {
			response.Valid = false
			response.Message = "Video file too large (max 1GB)"
		}
	}

	if !response.Valid {
		utils.ErrorResponse(c, http.StatusBadRequest, response.Message, nil)
	} else {
		utils.SuccessResponse(c, http.StatusOK, "Request validation successful", response)
	}
}

func (h *ThumbnailHandler) convertToThumbnailInfo(thumbnail *entities.Thumbnail) *ThumbnailInfo {
	return &ThumbnailInfo{
		ID:          thumbnail.ID().String(),
		FileID:      thumbnail.FileID().String(),
		Size:        thumbnail.Size(),
		Format:      thumbnail.Format(),
		Width:       thumbnail.Width(),
		Height:      thumbnail.Height(),
		FileSize:    thumbnail.FileSize(),
		StoragePath: thumbnail.StoragePath(),
		Status:      thumbnail.Status(),
		CreatedAt:   thumbnail.CreatedAt(),
		URL:         h.generateThumbnailURL(thumbnail),
	}
}

func (h *ThumbnailHandler) generateThumbnailURL(thumbnail *entities.Thumbnail) string {
	// Generate URL for accessing the thumbnail - match actual route structure
	return "/api/v1/drive/thumbnails/" + thumbnail.FileID().String() + "/" + string(thumbnail.Size()) +
		"?format=" + string(thumbnail.Format())
}

// getMimeTypeForFormat returns the appropriate MIME type for thumbnail format
func (h *ThumbnailHandler) getMimeTypeForFormat(format entities.ThumbnailFormat) string {
	switch format {
	case entities.ThumbnailFormatJPEG:
		return "image/jpeg"
	case entities.ThumbnailFormatPNG:
		return "image/png"
	case entities.ThumbnailFormatWebP:
		return "image/webp"
	case entities.ThumbnailFormatAVIF:
		return "image/avif"
	default:
		return "image/jpeg"
	}
}

// GetThumbnailHybrid implements the hybrid approach for thumbnail retrieval
func (h *ThumbnailHandler) GetThumbnailHybrid(c *gin.Context) {
	fileIDStr := c.Param("file_id")
	sizeStr := c.Param("size")

	// Validate parameters
	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file_id"})
		return
	}

	if !entities.IsValidSize(sizeStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid size"})
		return
	}

	size := entities.ThumbnailSize(sizeStr)
	formatStr := c.DefaultQuery("format", "jpeg")

	if !entities.IsValidFormat(formatStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid format"})
		return
	}

	format := entities.ThumbnailFormat(formatStr)

	// Validate user access
	userID, err := helpers.GetUserID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	// Get user teams from context
	userTeamsValue, _ := c.Get("teams")
	userTeams := []string{}
	if userTeamsValue != nil {
		if teams, ok := userTeamsValue.([]string); ok {
			userTeams = teams
		}
	}

	// Validate file access permissions
	if !h.validateFileAccess(c.Request.Context(), userID, userTeams, fileID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
		return
	}

	// Try to get existing thumbnail
	thumbnail, err := h.thumbnailService.GetThumbnail(c.Request.Context(), fileID, size, format)
	if err == nil && thumbnail.IsCompleted() {
		// Serve existing thumbnail
		h.serveThumbnail(c, thumbnail)
		return
	}

	// Thumbnail doesn't exist or is incomplete - get file info
	file, err := h.fileRepository.GetByID(c.Request.Context(), fileentities.FileID(fileID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "file not found"})
		return
	}

	// Check if generation is already in progress
	if h.generationTracker.IsInProgress(fileID, size, format) {
		// Generation in progress - return 202 with retry information
		c.Header("Retry-After", "3")
		c.JSON(http.StatusAccepted, gin.H{
			"message":    "thumbnail generation in progress",
			"retryAfter": 3,
			"status":     "processing",
		})
		return
	}

	// Determine processing strategy based on file characteristics
	if h.shouldGenerateSync(file) {
		// Generate synchronously for small/fast files
		h.generateSyncThumbnail(c, file, size, format, userID)
	} else {
		// Generate asynchronously for large/slow files
		h.generateAsyncThumbnail(c, file, size, format, userID)
	}
}

// IsGenerationInProgress checks if thumbnail generation is in progress
func (h *ThumbnailHandler) IsGenerationInProgress(fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) bool {
	return h.generationTracker.IsInProgress(fileID, size, format)
}

// GetGenerationMetrics returns generation metrics
func (h *ThumbnailHandler) GetGenerationMetrics() map[string]interface{} {
	generationMetrics := h.metrics.GetMetrics()

	// Add additional metrics
	generationMetrics["in_progress_count"] = h.generationTracker.GetInProgressCount()
	generationMetrics["cache_enabled"] = h.config.CacheEnabled
	generationMetrics["max_sync_size"] = h.config.MaxSyncFileSize
	generationMetrics["max_sync_timeout"] = h.config.MaxSyncTimeout.String()

	return generationMetrics
}

// GetThumbnailStatus gets the status of thumbnail generation for a specific file/size/format
func (h *ThumbnailHandler) GetThumbnailStatus(c *gin.Context) {
	fileIDStr := c.Param("file_id")
	sizeStr := c.Param("size")

	fileID, err := uuid.Parse(fileIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid file_id"})
		return
	}

	if !entities.IsValidSize(sizeStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid size"})
		return
	}

	size := entities.ThumbnailSize(sizeStr)
	formatStr := c.DefaultQuery("format", "jpeg")
	
	if !entities.IsValidFormat(formatStr) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid format"})
		return
	}

	format := entities.ThumbnailFormat(formatStr)

	// Check if thumbnail exists
	thumbnail, err := h.thumbnailService.GetThumbnail(c.Request.Context(), fileID, size, format)
	
	status := gin.H{
		"file_id": fileIDStr,
		"size":    size,
		"format":  format,
	}
	
	if err != nil {
		// Check if generation is in progress
		if h.IsGenerationInProgress(fileID, size, format) {
			status["status"] = "processing"
			status["message"] = "Thumbnail generation is in progress"
			status["estimated_completion"] = time.Now().Add(30 * time.Second).Format(time.RFC3339)
		} else {
			status["status"] = "not_generated"
			status["message"] = "Thumbnail has not been generated yet"
			status["actions"] = []string{"generate_sync", "generate_async"}
		}
	} else {
		status["status"] = string(thumbnail.Status())
		status["thumbnail_id"] = thumbnail.ID().String()
		status["created_at"] = thumbnail.CreatedAt().Format(time.RFC3339)
		status["updated_at"] = thumbnail.UpdatedAt().Format(time.RFC3339)
		status["file_size"] = thumbnail.FileSize()
		status["dimensions"] = gin.H{
			"width":  thumbnail.Width(),
			"height": thumbnail.Height(),
		}
		
		if thumbnail.IsCompleted() {
			status["url"] = fmt.Sprintf("/api/v1/drive/thumbnails/%s/%s?format=%s", fileIDStr, size, format)
		}
	}

	utils.SuccessResponse(c, http.StatusOK, "Thumbnail status retrieved successfully", status)
}

// GetThumbnailMetrics returns combined metrics for thumbnail operations
func (h *ThumbnailHandler) GetThumbnailMetrics(c *gin.Context) {
	// Get generation metrics
	generationMetrics := h.GetGenerationMetrics()
	
	// Get service metrics
	serviceMetrics, err := h.thumbnailService.GetStatistics(c.Request.Context())
	if err != nil {
		serviceMetrics = map[string]interface{}{
			"error": "failed to get service metrics",
		}
	}
	
	// Combine metrics
	metrics := gin.H{
		"generation_metrics": generationMetrics,
		"service_metrics":    serviceMetrics,
		"timestamp":          time.Now().Format(time.RFC3339),
	}

	utils.SuccessResponse(c, http.StatusOK, "Thumbnail metrics retrieved successfully", metrics)
}

// shouldGenerateSync determines if a file should be processed synchronously
func (h *ThumbnailHandler) shouldGenerateSync(file *fileentities.File) bool {
	// Check file size threshold
	if file.Size() > h.config.MaxSyncFileSize {
		return false
	}

	// Check file type - only process simple image formats synchronously
	mimeType := file.MimeType()

	// Allow sync for common image formats
	syncFormats := []string{
		"image/jpeg", "image/jpg", "image/png", "image/webp",
	}

	for _, format := range syncFormats {
		if mimeType == format {
			return true
		}
	}

	// Skip sync for complex formats
	skipSyncFormats := []string{
		"image/gif",  // Animated GIFs
		"image/tiff", // Large TIFF files
		"image/bmp",  // Uncompressed BMP
		"video/",     // All video formats
	}

	for _, format := range skipSyncFormats {
		if strings.HasPrefix(mimeType, format) {
			return false
		}
	}

	return true
}

// generateSyncThumbnail generates thumbnail synchronously
func (h *ThumbnailHandler) generateSyncThumbnail(c *gin.Context, file *fileentities.File, size entities.ThumbnailSize, format entities.ThumbnailFormat, userID uuid.UUID) {
	// Mark as in progress
	h.generationTracker.StartGeneration(uuid.UUID(file.ID()), size, format)
	defer h.generationTracker.FinishGeneration(uuid.UUID(file.ID()), size, format)

	// Set timeout for sync generation
	ctx, cancel := context.WithTimeout(c.Request.Context(), h.config.MaxSyncTimeout)
	defer cancel()

	// Track start time for metrics
	startTime := time.Now()

	// Generate thumbnail
	thumbnail, err := h.syncProcessor.GenerateThumbnail(ctx, file, size, format, userID)
	duration := time.Since(startTime)

	if err != nil {
		// Log error (using fmt for now until proper logger is set up)
		fmt.Printf("Failed to generate thumbnail synchronously: %v, fileID: %s\n", err, file.ID())

		// Record failed sync generation
		h.metrics.RecordSyncGeneration(duration, false)

		// Fall back to async generation
		h.triggerAsyncGeneration(c.Request.Context(), file, size, format, userID)
		h.servePlaceholder(c, file.MimeType())
		return
	}

	// Record successful sync generation
	h.metrics.RecordSyncGeneration(duration, true)

	// Serve the newly generated thumbnail
	h.serveThumbnail(c, thumbnail)
}

// generateAsyncThumbnail triggers async generation and serves placeholder
func (h *ThumbnailHandler) generateAsyncThumbnail(c *gin.Context, file *fileentities.File, size entities.ThumbnailSize, format entities.ThumbnailFormat, userID uuid.UUID) {
	// Mark as in progress
	h.generationTracker.StartGeneration(uuid.UUID(file.ID()), size, format)

	// Trigger async generation
	go func() {
		defer h.generationTracker.FinishGeneration(uuid.UUID(file.ID()), size, format)

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
		defer cancel()

		err := h.triggerAsyncGeneration(ctx, file, size, format, userID)
		if err != nil {
			// Log error (using fmt for now until proper logger is set up)
			fmt.Printf("Failed to trigger async thumbnail generation: %v, fileID: %s\n", err, file.ID())
			// Record failed async generation
			h.metrics.RecordAsyncGeneration(false)
		} else {
			// Record successful async generation
			h.metrics.RecordAsyncGeneration(true)
		}
	}()

	// Record placeholder serve
	h.metrics.RecordPlaceholderServe()

	// Serve placeholder immediately
	h.servePlaceholder(c, file.MimeType())
}

// triggerAsyncGeneration triggers async thumbnail generation
func (h *ThumbnailHandler) triggerAsyncGeneration(ctx context.Context, file *fileentities.File, size entities.ThumbnailSize, format entities.ThumbnailFormat, userID uuid.UUID) error {
	// Create async job
	_, err := h.thumbnailService.CreateThumbnailJob(
		ctx,
		uuid.UUID(file.ID()),
		userID,
		file.Path(),
		file.MimeType(),
		[]entities.ThumbnailSize{size},
		[]entities.ThumbnailFormat{format},
	)

	return err
}

// serveThumbnail serves an existing thumbnail file
func (h *ThumbnailHandler) serveThumbnail(c *gin.Context, thumbnail *entities.Thumbnail) {
	// Set caching headers
	if h.config.CacheEnabled {
		c.Header("Cache-Control", fmt.Sprintf("public, max-age=%d", int(h.config.CacheMaxAge.Seconds())))
		c.Header("ETag", thumbnail.Checksum())

		// Check if client has cached version
		if match := c.GetHeader("If-None-Match"); match == thumbnail.Checksum() {
			c.Status(http.StatusNotModified)
			return
		}
	}

	// Set content type
	c.Header("Content-Type", h.getMimeTypeForFormat(thumbnail.Format()))

	// Serve the file
	if thumbnail.StoragePath() != "" {
		c.File(thumbnail.StoragePath())
	} else {
		c.JSON(http.StatusNotFound, gin.H{"error": "thumbnail file not available"})
	}
}

// servePlaceholder serves a placeholder image
func (h *ThumbnailHandler) servePlaceholder(c *gin.Context, mimeType string) {
	placeholderPath := h.placeholderManager.GetPlaceholder(mimeType)

	// Set appropriate headers for placeholder
	c.Header("Cache-Control", "public, max-age=300") // 5 minutes cache for placeholders
	c.Header("Content-Type", "image/png")
	c.Header("X-Thumbnail-Status", "placeholder")

	// Serve placeholder file
	c.File(placeholderPath)
}

// validateFileAccess checks if user has access to the file
func (h *ThumbnailHandler) validateFileAccess(ctx context.Context, userID uuid.UUID, userTeams []string, fileID uuid.UUID) bool {
	file, err := h.fileRepository.GetByID(ctx, fileentities.FileID(fileID))
	if err != nil {
		return false
	}

	// Check if user owns the file
	if file.IsOwnedBy(userID) {
		return true
	}

	// Check team access
	if len(userTeams) > 0 {
		for _, teamIDStr := range userTeams {
			if teamID, err := uuid.Parse(teamIDStr); err == nil {
				if file.IsInTeam(teamID) && file.IsTeamVisible() {
					return true
				}
			}
		}
	}

	// Check if file is public
	if file.IsPublic() {
		return true
	}

	// TODO: Check sharing permissions

	return false
}

// DefaultThumbnailConfig returns default configuration
func DefaultThumbnailConfig() *ThumbnailConfig {
	return &ThumbnailConfig{
		MaxSyncFileSize:     5 * 1024 * 1024, // 5MB
		MaxSyncTimeout:      300 * time.Second, // 5 minutes for thumbnail processing
		SupportedImageTypes: []string{"image/jpeg", "image/png", "image/webp", "image/gif"},
		SupportedVideoTypes: []string{"video/mp4", "video/avi", "video/quicktime"},
		PlaceholderDir:      "/app/assets/placeholders",
		DefaultPlaceholder:  "default.png",
		CacheEnabled:        true,
		CacheMaxAge:         1 * time.Hour,
	}
}
