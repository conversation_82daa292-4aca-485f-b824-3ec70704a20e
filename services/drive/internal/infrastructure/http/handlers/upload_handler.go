package handlers

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/utils"
	uploadcommands "github.com/swork-team/platform/services/drive/internal/application/upload/commands"
	uploadqueries "github.com/swork-team/platform/services/drive/internal/application/upload/queries"
	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/helpers"
)

type UploadHandler struct {
	createSessionHandler       uploadcommands.CreateUploadSessionHandler
	uploadChunkHandler         uploadcommands.UploadChunkHandler
	completeUploadHandler      uploadcommands.CompleteUploadHandler
	getUploadSessionHandler    uploadqueries.GetUploadSessionHandler
	getMissingChunksHandler    uploadqueries.GetMissingChunksHandler
	cancelUploadSessionHandler uploadcommands.CancelUploadSessionHandler
}

type CreateUploadSessionRequest struct {
	FileName  string `json:"fileName" binding:"required"`
	FileSize  int64  `json:"fileSize" binding:"required"`
	ChunkSize int64  `json:"chunkSize"`
	MimeType  string `json:"mimeType"`
}

type CreateUploadSessionResponse struct {
	SessionID   string `json:"sessionId"`
	FileName    string `json:"fileName"`
	FileSize    int64  `json:"fileSize"`
	ChunkSize   int64  `json:"chunkSize"`
	TotalChunks int64  `json:"totalChunks"`
	ExpiresAt   string `json:"expiresAt"`
}

type UploadChunkRequest struct {
	ChunkIndex int64  `form:"chunkIndex"`
	ChunkHash  string `form:"chunkHash"`
}

type UploadChunkResponse struct {
	ChunkIndex int64   `json:"chunkIndex"`
	Progress   float64 `json:"progress"`
	IsComplete bool    `json:"isComplete"`
	Uploaded   int64   `json:"uploaded"`
	Total      int64   `json:"total"`
}

type CompleteUploadRequest struct {
	MimeType string `json:"mimeType"`
	TeamID   string `json:"teamId"`
	FolderID string `json:"folderId"`
}

type CompleteUploadResponse struct {
	File helpers.FileResponse `json:"file"`
}

func NewUploadHandler(
	createSessionHandler uploadcommands.CreateUploadSessionHandler,
	uploadChunkHandler uploadcommands.UploadChunkHandler,
	completeUploadHandler uploadcommands.CompleteUploadHandler,
	getUploadSessionHandler uploadqueries.GetUploadSessionHandler,
	getMissingChunksHandler uploadqueries.GetMissingChunksHandler,
	cancelUploadSessionHandler uploadcommands.CancelUploadSessionHandler,
) *UploadHandler {
	return &UploadHandler{
		createSessionHandler:       createSessionHandler,
		uploadChunkHandler:         uploadChunkHandler,
		completeUploadHandler:      completeUploadHandler,
		getUploadSessionHandler:    getUploadSessionHandler,
		getMissingChunksHandler:    getMissingChunksHandler,
		cancelUploadSessionHandler: cancelUploadSessionHandler,
	}
}

func (h *UploadHandler) CreateUploadSession(c *gin.Context) {
	var req CreateUploadSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and sanitize file name
	if err := helpers.ValidateFileName(req.FileName); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid file name: %v", err)})
		return
	}
	req.FileName = helpers.SanitizeFileName(req.FileName)

	// Validate file size
	if err := helpers.ValidateFileSize(req.FileSize); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid file size: %v", err)})
		return
	}

	// Validate chunk size if provided
	if req.ChunkSize > 0 {
		if err := helpers.ValidateChunkSize(req.ChunkSize); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid chunk size: %v", err)})
			return
		}
	}

	// Validate MIME type matches file extension
	if req.MimeType != "" {
		if err := helpers.ValidateMimeType(req.FileName, req.MimeType); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("MIME type validation failed: %v", err)})
			return
		}
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Create command
	cmd := &uploadcommands.CreateUploadSessionCommand{
		UserID:    userID,
		FileName:  req.FileName,
		FileSize:  req.FileSize,
		ChunkSize: req.ChunkSize,
		MimeType:  req.MimeType,
	}

	// Execute command
	result, err := h.createSessionHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := CreateUploadSessionResponse{
		SessionID:   result.Session.ID().String(),
		FileName:    result.Session.FileName(),
		FileSize:    result.Session.FileSize(),
		ChunkSize:   result.Session.ChunkSize(),
		TotalChunks: result.Session.TotalChunks(),
		ExpiresAt:   result.Session.ExpiresAt().Format("2006-01-02T15:04:05Z07:00"),
	}

	utils.SuccessResponse(c, http.StatusCreated, "Upload session created successfully", response)
}

func (h *UploadHandler) UploadChunk(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := uuid.Parse(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid session ID"})
		return
	}

	var req UploadChunkRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if chunkIndex was provided in the form
	chunkIndexStr := c.PostForm("chunkIndex")
	if chunkIndexStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "chunkIndex is required"})
		return
	}

	// Validate chunk index (we don't know total chunks yet, will be validated in command)
	if req.ChunkIndex < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "chunk index cannot be negative"})
		return
	}

	// Get chunk data from form file
	file, err := c.FormFile("chunk")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "chunk data is required"})
		return
	}

	// Validate chunk size
	if err := helpers.ValidateChunkSize(file.Size); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid chunk size: %v", err)})
		return
	}

	// Open chunk file
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to open chunk file"})
		return
	}
	defer src.Close()

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Create command
	cmd := &uploadcommands.UploadChunkCommand{
		SessionID:  sessionID,
		ChunkIndex: req.ChunkIndex,
		ChunkData:  src,
		ChunkSize:  file.Size,
		ChunkHash:  req.ChunkHash,
		UserID:     userID,
	}

	// Execute command
	result, err := h.uploadChunkHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := UploadChunkResponse{
		ChunkIndex: req.ChunkIndex,
		Progress:   result.Progress,
		IsComplete: result.IsComplete,
		Uploaded:   result.Session.GetUploadedChunkCount(),
		Total:      result.Session.TotalChunks(),
	}

	utils.SuccessResponse(c, http.StatusOK, "Chunk uploaded successfully", response)
}

func (h *UploadHandler) CompleteUpload(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := uuid.Parse(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid session ID"})
		return
	}

	var req CompleteUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Parse optional fields
	var teamID *uuid.UUID
	if req.TeamID != "" {
		parsed, err := uuid.Parse(req.TeamID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid team ID"})
			return
		}
		teamID = &parsed
	}

	var folderID *fileentities.FolderID
	if req.FolderID != "" {
		parsed, err := uuid.Parse(req.FolderID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid folder ID"})
			return
		}
		fID := fileentities.FolderID(parsed)
		folderID = &fID
	}

	// Create command
	cmd := &uploadcommands.CompleteUploadCommand{
		SessionID: sessionID,
		UserID:    userID,
		MimeType:  req.MimeType,
		TeamID:    teamID,
		FolderID:  folderID,
	}

	// Execute command
	result, err := h.completeUploadHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response
	response := CompleteUploadResponse{
		File: helpers.FileEntityToResponse(result.File),
	}

	utils.SuccessResponse(c, http.StatusOK, "Upload completed successfully", response)
}

func (h *UploadHandler) GetUploadSession(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := uuid.Parse(sessionIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid session ID format", err)
		return
	}

	// Get user ID from context using secure helper
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Use the secure query handler with ownership validation
	query := &uploadqueries.GetUploadSessionQuery{
		SessionID: sessionID,
		UserID:    userID,
	}

	result, err := h.getUploadSessionHandler.Handle(c.Request.Context(), query)
	if err != nil {
		// Log security event for potential unauthorized access
		utils.LogSecurityEvent(c, "get_upload_session_failed", false,
			logger.F("session_id", sessionIDStr),
			logger.F("error", err.Error()),
		)
		utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
		return
	}

	session := result.Session
	response := gin.H{
		"sessionId":   session.ID().String(),
		"fileName":    session.FileName(),
		"fileSize":    session.FileSize(),
		"chunkSize":   session.ChunkSize(),
		"totalChunks": session.TotalChunks(),
		"uploaded":    session.GetUploadedChunkCount(),
		"status":      string(session.Status()),
		"expiresAt":   session.ExpiresAt().Format("2006-01-02T15:04:05Z07:00"),
		"createdAt":   session.CreatedAt().Format("2006-01-02T15:04:05Z07:00"),
	}

	utils.SuccessResponse(c, http.StatusOK, "Upload session retrieved successfully", response)
}

func (h *UploadHandler) CancelUploadSession(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := uuid.Parse(sessionIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid session ID format", err)
		return
	}

	// Get user ID from context using secure helper
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Get optional reason from request body
	var requestBody struct {
		Reason string `json:"reason"`
	}
	c.ShouldBindJSON(&requestBody)

	// Use the secure command handler with ownership validation
	cmd := &uploadcommands.CancelUploadSessionCommand{
		SessionID: sessionID,
		UserID:    userID,
		Reason:    requestBody.Reason,
	}

	result, err := h.cancelUploadSessionHandler.Handle(c.Request.Context(), cmd)
	if err != nil {
		// Log security event for potential unauthorized access
		utils.LogSecurityEvent(c, "cancel_upload_session_failed", false,
			logger.F("session_id", sessionIDStr),
			logger.F("error", err.Error()),
		)
		utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Upload session cancelled successfully", result)
}

func (h *UploadHandler) GetMissingChunks(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := uuid.Parse(sessionIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid session ID format", err)
		return
	}

	// Get user ID from context using secure helper
	userID, err := helpers.GetUserID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}

	// Use the secure query handler with ownership validation
	query := &uploadqueries.GetMissingChunksQuery{
		SessionID: sessionID,
		UserID:    userID,
	}

	result, err := h.getMissingChunksHandler.Handle(c.Request.Context(), query)
	if err != nil {
		// Log security event for potential unauthorized access
		utils.LogSecurityEvent(c, "get_missing_chunks_failed", false,
			logger.F("session_id", sessionIDStr),
			logger.F("error", err.Error()),
		)
		utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Missing chunks retrieved successfully", result)
}
