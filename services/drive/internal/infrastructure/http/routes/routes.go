package routes

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	commonmiddleware "github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/handlers"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/middleware"
)

type Router struct {
	fileHandler      *handlers.FileHandler
	folderHandler    *handlers.FolderHandler
	uploadHandler    *handlers.UploadHandler
	sharingHandler   *handlers.SharingHandler
	thumbnailHandler *handlers.ThumbnailHandler
	systemHandler    *handlers.SystemHandler
	publicHandler    *handlers.PublicHandler
	adminHandler     *handlers.AdminHandler
	authMiddleware   *middleware.DriveAuthMiddleware
	auditMiddleware  *middleware.AuditMiddleware
	rateLimiter      *commonmiddleware.RateLimiter
	storagePort      ports.StoragePort
	fileService      fileservices.FileService
	folderRepository filerepositories.FolderRepository
}

func NewRouter(
	fileHandler *handlers.FileHandler,
	folderHandler *handlers.FolderHandler,
	uploadHandler *handlers.UploadHandler,
	sharingHandler *handlers.SharingHandler,
	thumbnailHandler *handlers.ThumbnailHandler,
	systemHandler *handlers.SystemHandler,
	publicHandler *handlers.PublicHandler,
	adminHandler *handlers.AdminHandler,
	authMiddleware *middleware.DriveAuthMiddleware,
	auditMiddleware *middleware.AuditMiddleware,
	rateLimiter *commonmiddleware.RateLimiter,
	storagePort ports.StoragePort,
	fileService fileservices.FileService,
	folderRepository filerepositories.FolderRepository,
) *Router {
	return &Router{
		fileHandler:      fileHandler,
		folderHandler:    folderHandler,
		uploadHandler:    uploadHandler,
		sharingHandler:   sharingHandler,
		thumbnailHandler: thumbnailHandler,
		systemHandler:    systemHandler,
		publicHandler:    publicHandler,
		adminHandler:     adminHandler,
		authMiddleware:   authMiddleware,
		auditMiddleware:  auditMiddleware,
		rateLimiter:      rateLimiter,
		storagePort:      storagePort,
		fileService:      fileService,
		folderRepository: folderRepository,
	}
}

func (r *Router) SetupRoutes(engine *gin.Engine) {
	// Global middleware
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())

	// Enhanced security headers
	engine.Use(commonmiddleware.SecurityHeadersMiddleware())

	// CORS middleware
	engine.Use(commonmiddleware.CORSMiddleware())

	// Request size limiting (100MB for drive service due to file uploads)
	engine.Use(commonmiddleware.RequestSizeLimitMiddleware(100 * 1024 * 1024))

	// Audit middleware
	engine.Use(r.auditMiddleware.AuditRequest())

	// Health check
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "healthy"})
	})

	// API v1 routes
	v1 := engine.Group("/api/v1")

	// Apply global rate limiting - 1000 requests per minute per IP
	if r.rateLimiter != nil {
		v1.Use(r.rateLimiter.LimitByIP(1000, time.Minute))
	}

	{
		// Drive routes
		drive := v1.Group("/drive")
		
		// Health endpoint for drive service (no auth required)
		drive.GET("/health", func(c *gin.Context) {
			utils.SuccessResponse(c, http.StatusOK, "Drive service is healthy", gin.H{
				"service": "drive",
				"status":  "healthy",
			})
		})
		
		drive.Use(r.authMiddleware.RequireAuth())

		// Apply CSRF protection for state-changing operations
		drive.Use(func(c *gin.Context) {
			// Skip CSRF for read operations
			if c.Request.Method == "GET" || c.Request.Method == "HEAD" || c.Request.Method == "OPTIONS" {
				c.Next()
				return
			}
			// Apply CSRF middleware for other operations
			commonmiddleware.CSRFProtectionMiddleware()(c)
		})

		// Apply stricter rate limiting for drive operations - 300 requests per minute per user
		if r.rateLimiter != nil {
			drive.Use(r.rateLimiter.LimitByUser(300, time.Minute))
		}

		{
			// File operations
			uploadGroup := drive.Group("")
			if r.rateLimiter != nil {
				// More restrictive limits for upload operations - 60 per minute
				uploadGroup.Use(r.rateLimiter.LimitByUser(60, time.Minute))
			}
			uploadGroup.POST("/upload", r.fileHandler.UploadFile)

			drive.GET("/files", r.fileHandler.ListFiles)
			drive.GET("/files/:id", r.fileHandler.GetFile)
			drive.PUT("/files/:id", r.fileHandler.UpdateFile)
			drive.POST("/files/:id/copy", r.fileHandler.CopyFile)
			drive.DELETE("/files/:id", r.fileHandler.DeleteFile)

			// Chunked upload with restrictive limits
			chunkUploadGroup := drive.Group("/upload")
			if r.rateLimiter != nil {
				// Very restrictive limits for chunked uploads - 100 per minute
				chunkUploadGroup.Use(r.rateLimiter.LimitByUser(100, time.Minute))
			}
			chunkUploadGroup.POST("/session", r.uploadHandler.CreateUploadSession)
			chunkUploadGroup.POST("/session/:sessionId/chunk", r.uploadHandler.UploadChunk)
			chunkUploadGroup.POST("/session/:sessionId/complete", r.uploadHandler.CompleteUpload)
			drive.GET("/upload/session/:sessionId", r.uploadHandler.GetUploadSession)
			drive.DELETE("/upload/session/:sessionId", r.uploadHandler.CancelUploadSession)
			drive.GET("/upload/session/:sessionId/chunks", r.uploadHandler.GetMissingChunks)

			// File sharing
			drive.POST("/files/:id/share", r.sharingHandler.ShareFile)
			drive.DELETE("/files/:id/unshare/:userId", r.sharingHandler.UnshareFile)
			drive.GET("/files/:id/shares", r.sharingHandler.ListFileShares)

			// File download (might be handled separately for presigned URLs)
			drive.GET("/files/:id/download", r.fileHandler.GetDownloadURL)

			// Folder operations
			drive.GET("/root", r.folderHandler.GetRootFolder)
			drive.POST("/folders", r.folderHandler.CreateFolder)
			drive.GET("/folders/:id", r.folderHandler.GetFolder)
			drive.PUT("/folders/:id", r.folderHandler.UpdateFolder)
			drive.DELETE("/folders/:id", r.folderHandler.DeleteFolder)

			// Search
			drive.GET("/search", r.systemHandler.SearchFiles)

			// Statistics
			drive.GET("/stats", r.systemHandler.GetStats)

			// Shared files
			drive.GET("/shared", r.sharingHandler.ListSharedFiles)

			// Thumbnail operations
			thumbnailGroup := drive.Group("/thumbnails")
			if r.rateLimiter != nil {
				// Moderate limits for thumbnail operations - 200 per minute
				thumbnailGroup.Use(r.rateLimiter.LimitByUser(200, time.Minute))
			}
			thumbnailGroup.POST("/process", r.thumbnailHandler.ProcessThumbnail)
			thumbnailGroup.POST("/process/sync", r.thumbnailHandler.ProcessThumbnailSync)
			thumbnailGroup.POST("/batch", r.thumbnailHandler.ProcessBatch)
			thumbnailGroup.POST("/validate", r.thumbnailHandler.ValidateRequest)
			thumbnailGroup.GET("/jobs/:job_id", r.thumbnailHandler.GetJobStatus)
			thumbnailGroup.GET("/:file_id", r.thumbnailHandler.GetThumbnailsForFile)
			thumbnailGroup.GET("/:file_id/:size", r.thumbnailHandler.GetThumbnailHybrid)
			thumbnailGroup.GET("/:file_id/:size/info", r.thumbnailHandler.GetThumbnailInfo)
			thumbnailGroup.GET("/:file_id/:size/download", r.thumbnailHandler.GetThumbnailHybrid)
			thumbnailGroup.GET("/:file_id/:size/status", r.thumbnailHandler.GetThumbnailStatus)
			thumbnailGroup.POST("/:file_id/:size/refresh", r.thumbnailHandler.RefreshThumbnail)
			
			// Metrics endpoint for monitoring
			thumbnailGroup.GET("/metrics", r.thumbnailHandler.GetThumbnailMetrics)
		}

		// Public routes (no auth required)
		public := v1.Group("/public")
		{
			// Public file access (for shared files)
			public.GET("/files/:id", r.publicHandler.GetPublicFile)
		}

		// Admin routes
		admin := v1.Group("/admin")
		admin.Use(r.authMiddleware.RequireAuth())
		admin.Use(r.requireAdminRole())
		{
			// System health
			admin.GET("/health", r.adminHandler.GetSystemHealth)

			// Audit logs
			admin.GET("/audit", r.adminHandler.GetAuditLogs)
		}
	}
}

// requireAdminRole middleware to check for admin role
func (r *Router) requireAdminRole() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user roles from context (set by auth middleware)
		rolesInterface, exists := c.Get("roles")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient permissions"})
			c.Abort()
			return
		}

		roles, ok := rolesInterface.([]string)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{"error": "invalid role data"})
			c.Abort()
			return
		}

		// Check if user has admin role
		hasAdminRole := false
		for _, role := range roles {
			if role == "admin" || role == "system_admin" {
				hasAdminRole = true
				break
			}
		}

		if !hasAdminRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "admin role required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

