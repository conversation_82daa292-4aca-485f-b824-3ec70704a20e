package processors

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
)

type VideoProcessor struct {
	tempDir     string
	maxFileSize int64
	ffmpegPath  string
	ffprobePath string
	timeout     time.Duration
}

type VideoProcessorConfig struct {
	TempDir     string
	MaxFileSize int64
	FFmpegPath  string
	FFprobePath string
	Timeout     time.Duration
}

type VideoMetadata struct {
	Duration  time.Duration
	Width     int
	Height    int
	Bitrate   int64
	FrameRate float64
	Codec     string
	Format    string
	Size      int64
}

type FrameExtractOptions struct {
	TimeOffset time.Duration
	Size       entities.ThumbnailSize
	Format     entities.ThumbnailFormat
	Quality    int
}

func NewVideoProcessor(config VideoProcessorConfig) *VideoProcessor {
	return &VideoProcessor{
		tempDir:     config.TempDir,
		maxFileSize: config.MaxFileSize,
		ffmpegPath:  config.FFmpegPath,
		ffprobePath: config.FFprobePath,
		timeout:     config.Timeout,
	}
}

func (p *VideoProcessor) ProcessVideo(ctx context.Context, sourcePath string, options ProcessingOptions) (*ProcessingResult, error) {
	// Validate video file
	if err := p.ValidateVideo(sourcePath); err != nil {
		return nil, fmt.Errorf("video validation failed: %w", err)
	}

	// Get video metadata
	metadata, err := p.GetVideoMetadata(ctx, sourcePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get video metadata: %w", err)
	}

	// Calculate optimal time offset for frame extraction
	timeOffset := p.calculateOptimalTimeOffset(metadata.Duration)

	// Extract frame
	frameOptions := FrameExtractOptions{
		TimeOffset: timeOffset,
		Size:       options.Size,
		Format:     options.Format,
		Quality:    options.Quality,
	}

	result, err := p.ExtractFrame(ctx, sourcePath, frameOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to extract frame: %w", err)
	}

	return result, nil
}

func (p *VideoProcessor) ExtractFrame(ctx context.Context, sourcePath string, options FrameExtractOptions) (*ProcessingResult, error) {
	// Generate output path
	outputPath := p.generateOutputPath(sourcePath, options.Size, options.Format)

	// Get target dimensions
	targetWidth, targetHeight := p.getTargetDimensions(options.Size)

	// Build FFmpeg command
	args := []string{
		"-i", sourcePath,
		"-ss", fmt.Sprintf("%.2f", options.TimeOffset.Seconds()),
		"-vframes", "1",
		"-vf", fmt.Sprintf("scale=%d:%d:force_original_aspect_ratio=decrease,pad=%d:%d:(ow-iw)/2:(oh-ih)/2",
			targetWidth, targetHeight, targetWidth, targetHeight),
		"-y", // Overwrite output file
	}

	// Add format-specific options
	switch options.Format {
	case entities.ThumbnailFormatJPEG:
		args = append(args, "-f", "image2", "-q:v", strconv.Itoa(100-options.Quality))
	case entities.ThumbnailFormatPNG:
		args = append(args, "-f", "image2")
	case entities.ThumbnailFormatWebP:
		args = append(args, "-f", "webp", "-quality", strconv.Itoa(options.Quality))
	default:
		return nil, fmt.Errorf("unsupported output format: %s", options.Format)
	}

	args = append(args, outputPath)

	// Execute FFmpeg command
	cmd := exec.CommandContext(ctx, p.ffmpegPath, args...)
	cmd.Stdout = nil
	cmd.Stderr = nil

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("ffmpeg command failed: %w", err)
	}

	// Verify output file was created
	fileInfo, err := os.Stat(outputPath)
	if err != nil {
		return nil, fmt.Errorf("output file not created: %w", err)
	}

	// Calculate checksum
	checksum, err := p.calculateChecksum(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	return &ProcessingResult{
		OutputPath: outputPath,
		Width:      targetWidth,
		Height:     targetHeight,
		FileSize:   fileInfo.Size(),
		Format:     options.Format,
		Checksum:   checksum,
	}, nil
}

func (p *VideoProcessor) GetVideoMetadata(ctx context.Context, sourcePath string) (*VideoMetadata, error) {
	// Build FFprobe command
	args := []string{
		"-v", "quiet",
		"-print_format", "json",
		"-show_format",
		"-show_streams",
		sourcePath,
	}

	cmd := exec.CommandContext(ctx, p.ffprobePath, args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("ffprobe command failed: %w", err)
	}

	// Parse JSON output (simplified - in real implementation would use JSON parsing)
	metadata := &VideoMetadata{}

	// This is a simplified implementation
	// In real code, you would parse the JSON output properly
	outputStr := string(output)

	// Extract duration (simplified)
	if strings.Contains(outputStr, "duration") {
		metadata.Duration = 30 * time.Second // Default for demo
	}

	// Extract dimensions (simplified)
	metadata.Width = 1920  // Default for demo
	metadata.Height = 1080 // Default for demo

	// Extract other metadata
	metadata.Bitrate = 5000000 // Default for demo
	metadata.FrameRate = 30.0  // Default for demo
	metadata.Codec = "h264"    // Default for demo
	metadata.Format = "mp4"    // Default for demo

	// Get file size
	fileInfo, err := os.Stat(sourcePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file size: %w", err)
	}
	metadata.Size = fileInfo.Size()

	return metadata, nil
}

func (p *VideoProcessor) ValidateVideo(sourcePath string) error {
	// Check file size
	fileInfo, err := os.Stat(sourcePath)
	if err != nil {
		return fmt.Errorf("failed to stat video file: %w", err)
	}

	if fileInfo.Size() > p.maxFileSize {
		return fmt.Errorf("video file too large: %d bytes (max: %d)", fileInfo.Size(), p.maxFileSize)
	}

	// Check if FFmpeg and FFprobe are available
	if _, err := exec.LookPath(p.ffmpegPath); err != nil {
		return fmt.Errorf("ffmpeg not found: %w", err)
	}

	if _, err := exec.LookPath(p.ffprobePath); err != nil {
		return fmt.Errorf("ffprobe not found: %w", err)
	}

	return nil
}

func (p *VideoProcessor) ExtractMultipleFrames(ctx context.Context, sourcePath string, timeOffsets []time.Duration, options ProcessingOptions) ([]*ProcessingResult, error) {
	results := make([]*ProcessingResult, 0, len(timeOffsets))

	for i, timeOffset := range timeOffsets {
		frameOptions := FrameExtractOptions{
			TimeOffset: timeOffset,
			Size:       options.Size,
			Format:     options.Format,
			Quality:    options.Quality,
		}

		// Modify output path to include frame number
		outputPath := p.generateMultiFrameOutputPath(sourcePath, options.Size, options.Format, i)

		result, err := p.extractFrameWithCustomPath(ctx, sourcePath, frameOptions, outputPath)
		if err != nil {
			return nil, fmt.Errorf("failed to extract frame %d: %w", i, err)
		}

		results = append(results, result)
	}

	return results, nil
}

func (p *VideoProcessor) CreateAnimatedGIF(ctx context.Context, sourcePath string, startTime, duration time.Duration, options ProcessingOptions) (*ProcessingResult, error) {
	// Generate output path for GIF
	outputPath := p.generateGIFOutputPath(sourcePath, options.Size)

	// Get target dimensions
	targetWidth, targetHeight := p.getTargetDimensions(options.Size)

	// Build FFmpeg command for GIF creation
	args := []string{
		"-i", sourcePath,
		"-ss", fmt.Sprintf("%.2f", startTime.Seconds()),
		"-t", fmt.Sprintf("%.2f", duration.Seconds()),
		"-vf", fmt.Sprintf("fps=10,scale=%d:%d:flags=lanczos", targetWidth, targetHeight),
		"-y", // Overwrite output file
		outputPath,
	}

	// Execute FFmpeg command
	cmd := exec.CommandContext(ctx, p.ffmpegPath, args...)
	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("ffmpeg GIF creation failed: %w", err)
	}

	// Verify output file was created
	fileInfo, err := os.Stat(outputPath)
	if err != nil {
		return nil, fmt.Errorf("GIF output file not created: %w", err)
	}

	// Calculate checksum
	checksum, err := p.calculateChecksum(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	return &ProcessingResult{
		OutputPath: outputPath,
		Width:      targetWidth,
		Height:     targetHeight,
		FileSize:   fileInfo.Size(),
		Format:     "gif", // Custom format for GIF
		Checksum:   checksum,
	}, nil
}

func (p *VideoProcessor) calculateOptimalTimeOffset(duration time.Duration) time.Duration {
	// Extract frame at 10% of video duration or 5 seconds, whichever is smaller
	tenPercent := duration / 10
	fiveSeconds := 5 * time.Second

	if tenPercent < fiveSeconds {
		return tenPercent
	}
	return fiveSeconds
}

func (p *VideoProcessor) getTargetDimensions(size entities.ThumbnailSize) (int, int) {
	switch size {
	case entities.ThumbnailSizeSmall:
		return 150, 150
	case entities.ThumbnailSizeMedium:
		return 300, 300
	case entities.ThumbnailSizeLarge:
		return 600, 600
	case entities.ThumbnailSizeXLarge:
		return 1200, 1200
	case entities.ThumbnailSizePreview:
		return 1920, 1080
	default:
		return 300, 300
	}
}

func (p *VideoProcessor) generateOutputPath(sourcePath string, size entities.ThumbnailSize, format entities.ThumbnailFormat) string {
	sourceBase := filepath.Base(sourcePath)
	sourceExt := filepath.Ext(sourceBase)
	sourceName := strings.TrimSuffix(sourceBase, sourceExt)

	var outputExt string
	switch format {
	case entities.ThumbnailFormatJPEG:
		outputExt = ".jpg"
	case entities.ThumbnailFormatPNG:
		outputExt = ".png"
	case entities.ThumbnailFormatWebP:
		outputExt = ".webp"
	default:
		outputExt = ".jpg"
	}

	outputName := fmt.Sprintf("%s_video_%s%s", sourceName, size, outputExt)
	return filepath.Join(p.tempDir, outputName)
}

func (p *VideoProcessor) generateMultiFrameOutputPath(sourcePath string, size entities.ThumbnailSize, format entities.ThumbnailFormat, frameNum int) string {
	sourceBase := filepath.Base(sourcePath)
	sourceExt := filepath.Ext(sourceBase)
	sourceName := strings.TrimSuffix(sourceBase, sourceExt)

	var outputExt string
	switch format {
	case entities.ThumbnailFormatJPEG:
		outputExt = ".jpg"
	case entities.ThumbnailFormatPNG:
		outputExt = ".png"
	case entities.ThumbnailFormatWebP:
		outputExt = ".webp"
	default:
		outputExt = ".jpg"
	}

	outputName := fmt.Sprintf("%s_video_%s_frame_%d%s", sourceName, size, frameNum, outputExt)
	return filepath.Join(p.tempDir, outputName)
}

func (p *VideoProcessor) generateGIFOutputPath(sourcePath string, size entities.ThumbnailSize) string {
	sourceBase := filepath.Base(sourcePath)
	sourceExt := filepath.Ext(sourceBase)
	sourceName := strings.TrimSuffix(sourceBase, sourceExt)

	outputName := fmt.Sprintf("%s_animated_%s.gif", sourceName, size)
	return filepath.Join(p.tempDir, outputName)
}

func (p *VideoProcessor) extractFrameWithCustomPath(ctx context.Context, sourcePath string, options FrameExtractOptions, outputPath string) (*ProcessingResult, error) {
	// Get target dimensions
	targetWidth, targetHeight := p.getTargetDimensions(options.Size)

	// Build FFmpeg command
	args := []string{
		"-i", sourcePath,
		"-ss", fmt.Sprintf("%.2f", options.TimeOffset.Seconds()),
		"-vframes", "1",
		"-vf", fmt.Sprintf("scale=%d:%d:force_original_aspect_ratio=decrease,pad=%d:%d:(ow-iw)/2:(oh-ih)/2",
			targetWidth, targetHeight, targetWidth, targetHeight),
		"-y", // Overwrite output file
	}

	// Add format-specific options
	switch options.Format {
	case entities.ThumbnailFormatJPEG:
		args = append(args, "-f", "image2", "-q:v", strconv.Itoa(100-options.Quality))
	case entities.ThumbnailFormatPNG:
		args = append(args, "-f", "image2")
	case entities.ThumbnailFormatWebP:
		args = append(args, "-f", "webp", "-quality", strconv.Itoa(options.Quality))
	default:
		return nil, fmt.Errorf("unsupported output format: %s", options.Format)
	}

	args = append(args, outputPath)

	// Execute FFmpeg command
	cmd := exec.CommandContext(ctx, p.ffmpegPath, args...)
	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("ffmpeg command failed: %w", err)
	}

	// Verify output file was created
	fileInfo, err := os.Stat(outputPath)
	if err != nil {
		return nil, fmt.Errorf("output file not created: %w", err)
	}

	// Calculate checksum
	checksum, err := p.calculateChecksum(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	return &ProcessingResult{
		OutputPath: outputPath,
		Width:      targetWidth,
		Height:     targetHeight,
		FileSize:   fileInfo.Size(),
		Format:     options.Format,
		Checksum:   checksum,
	}, nil
}

func (p *VideoProcessor) calculateChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file for checksum: %w", err)
	}
	defer file.Close()

	hasher := sha256.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", fmt.Errorf("failed to calculate checksum: %w", err)
	}

	return hex.EncodeToString(hasher.Sum(nil)), nil
}

func (p *VideoProcessor) GetSupportedFormats() []entities.ThumbnailFormat {
	return []entities.ThumbnailFormat{
		entities.ThumbnailFormatJPEG,
		entities.ThumbnailFormatPNG,
		entities.ThumbnailFormatWebP,
	}
}

func (p *VideoProcessor) GetSupportedSizes() []entities.ThumbnailSize {
	return entities.GetSupportedSizes()
}

func (p *VideoProcessor) Cleanup() error {
	// Clean up temporary files
	if err := os.RemoveAll(p.tempDir); err != nil {
		return fmt.Errorf("failed to cleanup temp directory: %w", err)
	}
	return nil
}
