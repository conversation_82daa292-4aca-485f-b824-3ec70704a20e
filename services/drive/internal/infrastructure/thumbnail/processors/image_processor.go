package processors

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/disintegration/imaging"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
)

type ImageProcessor struct {
	tempDir     string
	maxFileSize int64
}

type ImageProcessorConfig struct {
	TempDir     string
	MaxFileSize int64
}

type ProcessingOptions struct {
	Size    entities.ThumbnailSize
	Format  entities.ThumbnailFormat
	Quality int
}

type ProcessingResult struct {
	OutputPath string
	Width      int
	Height     int
	FileSize   int64
	Format     entities.ThumbnailFormat
	Checksum   string
}

func NewImageProcessor(config ImageProcessorConfig) *ImageProcessor {
	return &ImageProcessor{
		tempDir:     config.TempDir,
		maxFileSize: config.MaxFileSize,
	}
}

func (p *ImageProcessor) ProcessImage(ctx context.Context, sourcePath string, options ProcessingOptions) (*ProcessingResult, error) {
	// Open source image
	src, err := imaging.Open(sourcePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open source image: %w", err)
	}

	// Get target dimensions
	targetWidth, targetHeight := p.getTargetDimensions(options.Size)

	// Resize image
	resized := p.resizeImage(src, targetWidth, targetHeight)

	// Generate output filename
	outputPath := p.generateOutputPath(sourcePath, options.Size, options.Format)

	// Save processed image
	actualWidth, actualHeight, fileSize, err := p.saveImage(resized, outputPath, options.Format, options.Quality)
	if err != nil {
		return nil, fmt.Errorf("failed to save processed image: %w", err)
	}

	// Calculate checksum
	checksum, err := p.calculateChecksum(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	return &ProcessingResult{
		OutputPath: outputPath,
		Width:      actualWidth,
		Height:     actualHeight,
		FileSize:   fileSize,
		Format:     options.Format,
		Checksum:   checksum,
	}, nil
}

func (p *ImageProcessor) ProcessImageBatch(ctx context.Context, sourcePath string, optionsList []ProcessingOptions) ([]*ProcessingResult, error) {
	results := make([]*ProcessingResult, 0, len(optionsList))

	for _, options := range optionsList {
		result, err := p.ProcessImage(ctx, sourcePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to process image with options %+v: %w", options, err)
		}
		results = append(results, result)
	}

	return results, nil
}

func (p *ImageProcessor) ValidateImage(sourcePath string) error {
	// Check file size
	fileInfo, err := os.Stat(sourcePath)
	if err != nil {
		return fmt.Errorf("failed to stat source file: %w", err)
	}

	if fileInfo.Size() > p.maxFileSize {
		return fmt.Errorf("image file too large: %d bytes (max: %d)", fileInfo.Size(), p.maxFileSize)
	}

	// Try to decode image to validate format
	file, err := os.Open(sourcePath)
	if err != nil {
		return fmt.Errorf("failed to open source file: %w", err)
	}
	defer file.Close()

	_, format, err := image.DecodeConfig(file)
	if err != nil {
		return fmt.Errorf("failed to decode image config: %w", err)
	}

	// Check if format is supported
	supportedFormats := map[string]bool{
		"jpeg": true,
		"png":  true,
		"gif":  true,
		"webp": true,
		"bmp":  true,
		"tiff": true,
	}

	if !supportedFormats[format] {
		return fmt.Errorf("unsupported image format: %s", format)
	}

	return nil
}

func (p *ImageProcessor) getTargetDimensions(size entities.ThumbnailSize) (int, int) {
	switch size {
	case entities.ThumbnailSizeSmall:
		return 150, 150
	case entities.ThumbnailSizeMedium:
		return 300, 300
	case entities.ThumbnailSizeLarge:
		return 600, 600
	case entities.ThumbnailSizeXLarge:
		return 1200, 1200
	case entities.ThumbnailSizePreview:
		return 1920, 1080
	default:
		return 300, 300
	}
}

func (p *ImageProcessor) resizeImage(src image.Image, targetWidth, targetHeight int) image.Image {
	srcBounds := src.Bounds()
	srcWidth := srcBounds.Dx()
	srcHeight := srcBounds.Dy()

	// Calculate aspect ratios
	srcRatio := float64(srcWidth) / float64(srcHeight)
	targetRatio := float64(targetWidth) / float64(targetHeight)

	var resized image.Image

	if srcRatio > targetRatio {
		// Source is wider, fit to target height
		newWidth := int(float64(targetHeight) * srcRatio)
		resized = imaging.Resize(src, newWidth, targetHeight, imaging.Lanczos)
		// Crop to target width
		resized = imaging.CropCenter(resized, targetWidth, targetHeight)
	} else {
		// Source is taller, fit to target width
		newHeight := int(float64(targetWidth) / srcRatio)
		resized = imaging.Resize(src, targetWidth, newHeight, imaging.Lanczos)
		// Crop to target height
		resized = imaging.CropCenter(resized, targetWidth, targetHeight)
	}

	// Apply sharpening for better quality
	resized = imaging.Sharpen(resized, 0.5)

	return resized
}

func (p *ImageProcessor) saveImage(img image.Image, outputPath string, format entities.ThumbnailFormat, quality int) (int, int, int64, error) {
	// Ensure output directory exists
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return 0, 0, 0, fmt.Errorf("failed to create output directory: %w", err)
	}

	// Create output file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return 0, 0, 0, fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	// Get actual dimensions
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// Encode based on format
	switch format {
	case entities.ThumbnailFormatJPEG:
		err = jpeg.Encode(outputFile, img, &jpeg.Options{Quality: quality})
	case entities.ThumbnailFormatPNG:
		err = png.Encode(outputFile, img)
	case entities.ThumbnailFormatWebP:
		return 0, 0, 0, fmt.Errorf("WebP format not supported in this build")
	case entities.ThumbnailFormatAVIF:
		// AVIF support would require additional libraries
		return 0, 0, 0, fmt.Errorf("AVIF format not yet supported")
	default:
		return 0, 0, 0, fmt.Errorf("unsupported output format: %s", format)
	}

	if err != nil {
		return 0, 0, 0, fmt.Errorf("failed to encode image: %w", err)
	}

	// Get file size
	fileInfo, err := os.Stat(outputPath)
	if err != nil {
		return 0, 0, 0, fmt.Errorf("failed to stat output file: %w", err)
	}

	return width, height, fileInfo.Size(), nil
}

func (p *ImageProcessor) generateOutputPath(sourcePath string, size entities.ThumbnailSize, format entities.ThumbnailFormat) string {
	sourceBase := filepath.Base(sourcePath)
	sourceExt := filepath.Ext(sourceBase)
	sourceName := strings.TrimSuffix(sourceBase, sourceExt)

	var outputExt string
	switch format {
	case entities.ThumbnailFormatJPEG:
		outputExt = ".jpg"
	case entities.ThumbnailFormatPNG:
		outputExt = ".png"
	case entities.ThumbnailFormatWebP:
		outputExt = ".webp"
	case entities.ThumbnailFormatAVIF:
		outputExt = ".avif"
	default:
		outputExt = ".jpg"
	}

	outputName := fmt.Sprintf("%s_%s%s", sourceName, size, outputExt)
	return filepath.Join(p.tempDir, outputName)
}

func (p *ImageProcessor) calculateChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file for checksum: %w", err)
	}
	defer file.Close()

	hasher := sha256.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", fmt.Errorf("failed to calculate checksum: %w", err)
	}

	return hex.EncodeToString(hasher.Sum(nil)), nil
}

func (p *ImageProcessor) OptimizeForWeb(img image.Image) image.Image {
	// Apply web optimization filters
	optimized := imaging.AdjustContrast(img, 5)
	optimized = imaging.AdjustSaturation(optimized, 10)
	return optimized
}

func (p *ImageProcessor) OptimizeForMobile(img image.Image) image.Image {
	// Apply mobile optimization filters
	optimized := imaging.AdjustBrightness(img, 5)
	optimized = imaging.AdjustContrast(optimized, 10)
	return optimized
}

func (p *ImageProcessor) GetSupportedFormats() []entities.ThumbnailFormat {
	return []entities.ThumbnailFormat{
		entities.ThumbnailFormatJPEG,
		entities.ThumbnailFormatPNG,
		// entities.ThumbnailFormatWebP, // Not supported in this build
		// entities.ThumbnailFormatAVIF, // Not yet supported
	}
}

func (p *ImageProcessor) GetSupportedSizes() []entities.ThumbnailSize {
	return entities.GetSupportedSizes()
}

func (p *ImageProcessor) Cleanup() error {
	// Clean up temporary files
	if err := os.RemoveAll(p.tempDir); err != nil {
		return fmt.Errorf("failed to cleanup temp directory: %w", err)
	}
	return nil
}
