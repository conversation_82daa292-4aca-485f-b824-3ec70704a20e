package processors

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/disintegration/imaging"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
)

type MinIOThumbnailProcessor struct {
	tempDir        string
	maxFileSize    int64
	replicaStorage ports.StorageProvider // Replica storage (B2) for backup
	primaryStorage ports.StorageProvider // Primary storage (MinIO) for fast access
	storageManager StorageManager        // Storage manager for sync operations
}

type StorageManager interface {
	SyncThumbnailToReplica(ctx context.Context, thumbnailPath string) error
}

type MinIOThumbnailProcessorConfig struct {
	TempDir        string
	MaxFileSize    int64
	ReplicaStorage ports.StorageProvider
	PrimaryStorage ports.StorageProvider
	StorageManager StorageManager
}

func NewMinIOThumbnailProcessor(config MinIOThumbnailProcessorConfig) *MinIOThumbnailProcessor {
	return &MinIOThumbnailProcessor{
		tempDir:        config.TempDir,
		maxFileSize:    config.MaxFileSize,
		replicaStorage: config.ReplicaStorage,
		primaryStorage: config.PrimaryStorage,
		storageManager: config.StorageManager,
	}
}

// ProcessThumbnailFromMinIO downloads from MinIO primary, processes locally, uploads to primary
func (p *MinIOThumbnailProcessor) ProcessThumbnailFromMinIO(ctx context.Context, storagePath string, options ProcessingOptions) (*ProcessingResult, error) {
	log.Printf("[ThumbnailProcessor] Starting thumbnail processing for path: %s, size: %s, format: %s", storagePath, options.Size, options.Format)
	start := time.Now()

	// Step 1: Download source image from MinIO primary (fast local access)
	log.Printf("[ThumbnailProcessor] Downloading source image from MinIO primary: %s", storagePath)
	tempSourcePath, err := p.downloadFromMinIO(ctx, storagePath)
	if err != nil {
		log.Printf("[ThumbnailProcessor] Failed to download from MinIO primary: %v", err)
		return nil, fmt.Errorf("failed to download from MinIO primary: %w", err)
	}
	defer func() {
		if removeErr := os.Remove(tempSourcePath); removeErr != nil {
			log.Printf("[ThumbnailProcessor] Warning: failed to clean up temp source file %s: %v", tempSourcePath, removeErr)
		}
	}()

	// Step 2: Process image locally
	log.Printf("[ThumbnailProcessor] Processing image locally from: %s", tempSourcePath)
	result, err := p.processImageLocal(ctx, tempSourcePath, options)
	if err != nil {
		log.Printf("[ThumbnailProcessor] Failed to process image locally: %v", err)
		return nil, fmt.Errorf("failed to process image locally: %w", err)
	}
	defer func() {
		if removeErr := os.Remove(result.OutputPath); removeErr != nil {
			log.Printf("[ThumbnailProcessor] Warning: failed to clean up processed temp file %s: %v", result.OutputPath, removeErr)
		}
	}()

	// Step 3: Upload processed thumbnail to MinIO primary storage
	log.Printf("[ThumbnailProcessor] Uploading thumbnail to MinIO primary: %s", result.OutputPath)
	thumbnailStoragePath, err := p.uploadThumbnailToPrimary(ctx, result.OutputPath, storagePath, options)
	if err != nil {
		log.Printf("[ThumbnailProcessor] Failed to upload thumbnail to MinIO primary: %v", err)
		return nil, fmt.Errorf("failed to upload thumbnail to MinIO primary: %w", err)
	}

	// Step 4: Queue async sync to B2 replica
	if p.storageManager != nil {
		log.Printf("[ThumbnailProcessor] Queuing thumbnail sync to B2 replica: %s", thumbnailStoragePath)
		go func() {
			if err := p.storageManager.SyncThumbnailToReplica(context.Background(), thumbnailStoragePath); err != nil {
				log.Printf("[ThumbnailProcessor] Warning: failed to sync thumbnail to replica: %v", err)
			}
		}()
	}

	// Update result with storage path
	result.OutputPath = thumbnailStoragePath
	log.Printf("[ThumbnailProcessor] Successfully processed thumbnail in %v: %s", time.Since(start), thumbnailStoragePath)
	return result, nil
}

// ProcessThumbnailBatchFromMinIO processes multiple sizes/formats efficiently
func (p *MinIOThumbnailProcessor) ProcessThumbnailBatchFromMinIO(ctx context.Context, storagePath string, optionsList []ProcessingOptions) ([]*ProcessingResult, error) {
	// Step 1: Download source image from MinIO replica once
	tempSourcePath, err := p.downloadFromMinIO(ctx, storagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to download from MinIO replica: %w", err)
	}
	defer os.Remove(tempSourcePath)

	// Step 2: Load source image once
	src, err := imaging.Open(tempSourcePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open source image: %w", err)
	}

	results := make([]*ProcessingResult, 0, len(optionsList))

	// Step 3: Process each size/format combination
	for _, options := range optionsList {
		result, err := p.processImageFromLoaded(ctx, src, storagePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to process image with options %+v: %w", options, err)
		}
		
		// Upload to primary storage
		thumbnailStoragePath, err := p.uploadThumbnailToPrimary(ctx, result.OutputPath, storagePath, options)
		if err != nil {
			os.Remove(result.OutputPath)
			return nil, fmt.Errorf("failed to upload thumbnail to primary storage: %w", err)
		}

		// Upload to MinIO replica
		if err := p.uploadThumbnailToMinIO(ctx, result.OutputPath, thumbnailStoragePath, options); err != nil {
			fmt.Printf("Warning: failed to sync thumbnail to MinIO replica: %v\n", err)
		}

		// Clean up local temp file
		os.Remove(result.OutputPath)
		
		// Update result with storage path
		result.OutputPath = thumbnailStoragePath
		results = append(results, result)
	}

	return results, nil
}

// downloadFromMinIO downloads file from MinIO replica to local temp file with robust error handling
func (p *MinIOThumbnailProcessor) downloadFromMinIO(ctx context.Context, storagePath string) (string, error) {
	// Ensure temp directory exists and is writable, fallback to system temp
	tempDir := p.tempDir
	if tempDir == "./thumbnails" || tempDir == "" {
		tempDir = os.TempDir()
	}
	
	// Create temp directory if it doesn't exist
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create temp directory %s: %w", tempDir, err)
	}
	
	// Create temp file
	tempFile, err := os.CreateTemp(tempDir, "minio-source-*")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file in %s: %w", tempDir, err)
	}
	defer tempFile.Close()

	// Create timeout context for download API calls only
	// Use background context to avoid parent timeout conflicts
	downloadCtx, downloadCancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer downloadCancel()

	// Download from MinIO primary storage (fast local access)
	var reader io.ReadCloser
	var downloadErr error
	
	// Primary attempt: MinIO primary
	log.Printf("[ThumbnailProcessor] Attempting download from MinIO primary: %s", storagePath)
	reader, downloadErr = p.primaryStorage.Download(downloadCtx, storagePath)
	
	if downloadErr != nil {
		log.Printf("[ThumbnailProcessor] MinIO primary download failed: %v, falling back to replica storage", downloadErr)
		// Fallback to replica storage (B2)
		reader, downloadErr = p.replicaStorage.Download(downloadCtx, storagePath)
		if downloadErr != nil {
			os.Remove(tempFile.Name())
			return "", fmt.Errorf("failed to download from both primary and replica storage: %w", downloadErr)
		}
		log.Printf("[ThumbnailProcessor] Successfully downloaded from replica storage")
	} else {
		log.Printf("[ThumbnailProcessor] Successfully downloaded from MinIO primary")
	}
	defer reader.Close()

	// Copy data to temp file - the MinIO provider now handles buffering
	log.Printf("[ThumbnailProcessor] Copying data to temp file")
	copied, err := io.Copy(tempFile, reader)
	if err != nil {
		os.Remove(tempFile.Name())
		log.Printf("[ThumbnailProcessor] Copy operation failed (copied %d bytes): %v", copied, err)
		return "", fmt.Errorf("failed to copy to temp file (copied %d bytes): %w", copied, err)
	}

	// Verify file was written correctly
	if copied == 0 {
		os.Remove(tempFile.Name())
		return "", fmt.Errorf("no data was copied from storage")
	}
	
	log.Printf("[ThumbnailProcessor] Successfully downloaded %d bytes to temp file: %s", copied, tempFile.Name())

	return tempFile.Name(), nil
}

// processImageLocal processes image from local temp file
func (p *MinIOThumbnailProcessor) processImageLocal(ctx context.Context, sourcePath string, options ProcessingOptions) (*ProcessingResult, error) {
	// Open source image
	src, err := imaging.Open(sourcePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open source image: %w", err)
	}

	return p.processImageFromLoaded(ctx, src, sourcePath, options)
}

// processImageFromLoaded processes from already loaded image
func (p *MinIOThumbnailProcessor) processImageFromLoaded(ctx context.Context, src image.Image, originalPath string, options ProcessingOptions) (*ProcessingResult, error) {
	// Get target dimensions
	targetWidth, targetHeight := p.getTargetDimensions(options.Size)

	// Resize image maintaining aspect ratio
	resized := imaging.Fit(src, targetWidth, targetHeight, imaging.Lanczos)

	// Generate temp output path
	outputPath := p.generateTempOutputPath(originalPath, options.Size, options.Format)

	// Save processed image
	actualWidth, actualHeight, fileSize, err := p.saveImage(resized, outputPath, options.Format, options.Quality)
	if err != nil {
		return nil, fmt.Errorf("failed to save processed image: %w", err)
	}

	// Calculate checksum
	checksum, err := p.calculateChecksum(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	return &ProcessingResult{
		OutputPath: outputPath,
		Width:      actualWidth,
		Height:     actualHeight,
		FileSize:   fileSize,
		Format:     options.Format,
		Checksum:   checksum,
	}, nil
}

// uploadThumbnailToPrimary uploads processed thumbnail to primary storage with robust error handling
func (p *MinIOThumbnailProcessor) uploadThumbnailToPrimary(ctx context.Context, localPath, originalStoragePath string, options ProcessingOptions) (string, error) {
	// Generate storage path for thumbnail
	thumbnailStoragePath := p.generateThumbnailStoragePath(originalStoragePath, options.Size, options.Format)

	// Verify local file exists and is readable
	fileInfo, err := os.Stat(localPath)
	if err != nil {
		return "", fmt.Errorf("failed to stat local file %s: %w", localPath, err)
	}
	if fileInfo.Size() == 0 {
		return "", fmt.Errorf("local file %s is empty", localPath)
	}

	// Open local file
	file, err := os.Open(localPath)
	if err != nil {
		return "", fmt.Errorf("failed to open local file %s: %w", localPath, err)
	}
	defer file.Close()

	// Create timeout context for upload operation
	uploadCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	// Upload to primary storage with retry logic
	var uploadErr error
	for attempt := 0; attempt < 3; attempt++ {
		if attempt > 0 {
			// Reset file pointer for retry
			file.Seek(0, 0)
			select {
			case <-uploadCtx.Done():
				return "", fmt.Errorf("upload operation timed out after %d attempts", attempt)
			case <-time.After(time.Duration(attempt) * time.Second):
				// Exponential backoff
			}
		}

		uploadErr = p.primaryStorage.Upload(uploadCtx, file, thumbnailStoragePath, p.getMimeType(options.Format))
		if uploadErr == nil {
			break
		}
	}

	if uploadErr != nil {
		return "", fmt.Errorf("failed to upload to primary storage after 3 attempts: %w", uploadErr)
	}

	return thumbnailStoragePath, nil
}

// uploadThumbnailToMinIO uploads thumbnail to MinIO replica for consistency with robust error handling
func (p *MinIOThumbnailProcessor) uploadThumbnailToMinIO(ctx context.Context, localPath, thumbnailStoragePath string, options ProcessingOptions) error {
	// Verify local file exists and is readable
	fileInfo, err := os.Stat(localPath)
	if err != nil {
		return fmt.Errorf("failed to stat local file %s: %w", localPath, err)
	}
	if fileInfo.Size() == 0 {
		return fmt.Errorf("local file %s is empty", localPath)
	}

	// Open local file
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("failed to open local file %s: %w", localPath, err)
	}
	defer file.Close()

	// Create timeout context for upload operation
	uploadCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Upload to MinIO replica with retry logic (best effort)
	var uploadErr error
	for attempt := 0; attempt < 2; attempt++ {
		if attempt > 0 {
			// Reset file pointer for retry
			file.Seek(0, 0)
			select {
			case <-uploadCtx.Done():
				return fmt.Errorf("MinIO replica upload timed out after %d attempts", attempt)
			case <-time.After(time.Duration(attempt) * time.Second):
				// Exponential backoff
			}
		}

		uploadErr = p.replicaStorage.Upload(uploadCtx, file, thumbnailStoragePath, p.getMimeType(options.Format))
		if uploadErr == nil {
			break
		}
	}

	if uploadErr != nil {
		return fmt.Errorf("failed to upload to MinIO replica after 2 attempts: %w", uploadErr)
	}

	return nil
}

// generateThumbnailStoragePath generates storage path for thumbnail
func (p *MinIOThumbnailProcessor) generateThumbnailStoragePath(originalPath string, size entities.ThumbnailSize, format entities.ThumbnailFormat) string {
	// Extract directory and filename from original path
	dir := filepath.Dir(originalPath)
	filename := filepath.Base(originalPath)
	
	// Remove extension
	nameWithoutExt := strings.TrimSuffix(filename, filepath.Ext(filename))
	
	// Generate thumbnail filename
	thumbnailName := fmt.Sprintf("%s_%s.%s", nameWithoutExt, size, format)
	
	// Combine with thumbnails subdirectory
	return filepath.Join(dir, "thumbnails", thumbnailName)
}

// generateTempOutputPath generates temporary output path with proper directory creation
func (p *MinIOThumbnailProcessor) generateTempOutputPath(sourcePath string, size entities.ThumbnailSize, format entities.ThumbnailFormat) string {
	filename := filepath.Base(sourcePath)
	nameWithoutExt := strings.TrimSuffix(filename, filepath.Ext(filename))
	
	timestamp := time.Now().Format("20060102_150405")
	thumbnailName := fmt.Sprintf("%s_%s_%s.%s", nameWithoutExt, size, timestamp, format)
	
	// Ensure temp directory exists
	tempDir := p.tempDir
	if tempDir == "./thumbnails" || tempDir == "" {
		tempDir = os.TempDir()
	}
	
	// Create temp directory if it doesn't exist
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		// Fallback to system temp directory if creation fails
		tempDir = os.TempDir()
	}
	
	return filepath.Join(tempDir, thumbnailName)
}

// getTargetDimensions returns target dimensions for thumbnail size
func (p *MinIOThumbnailProcessor) getTargetDimensions(size entities.ThumbnailSize) (int, int) {
	switch size {
	case entities.ThumbnailSizeSmall:
		return 150, 150
	case entities.ThumbnailSizeMedium:
		return 300, 300
	case entities.ThumbnailSizeLarge:
		return 600, 600
	default:
		return 150, 150
	}
}

// saveImage saves image to file
func (p *MinIOThumbnailProcessor) saveImage(img image.Image, outputPath string, format entities.ThumbnailFormat, quality int) (int, int, int64, error) {
	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return 0, 0, 0, fmt.Errorf("failed to create output directory: %w", err)
	}

	// Create output file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return 0, 0, 0, fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	// Save based on format
	switch format {
	case entities.ThumbnailFormatJPEG:
		err = jpeg.Encode(outputFile, img, &jpeg.Options{Quality: quality})
	case entities.ThumbnailFormatPNG:
		err = png.Encode(outputFile, img)
	default:
		err = jpeg.Encode(outputFile, img, &jpeg.Options{Quality: quality})
	}

	if err != nil {
		return 0, 0, 0, fmt.Errorf("failed to encode image: %w", err)
	}

	// Get file info
	fileInfo, err := outputFile.Stat()
	if err != nil {
		return 0, 0, 0, fmt.Errorf("failed to get file info: %w", err)
	}

	bounds := img.Bounds()
	return bounds.Dx(), bounds.Dy(), fileInfo.Size(), nil
}

// calculateChecksum calculates SHA256 checksum of file
func (p *MinIOThumbnailProcessor) calculateChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", fmt.Errorf("failed to calculate hash: %w", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// checkTempDirectoryAccess verifies temp directory is accessible and writable
func (p *MinIOThumbnailProcessor) checkTempDirectoryAccess() error {
	tempDir := p.tempDir
	if tempDir == "./thumbnails" || tempDir == "" {
		tempDir = os.TempDir()
	}

	// Check if directory exists and is writable
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory %s: %w", tempDir, err)
	}

	// Test write access
	testFile := filepath.Join(tempDir, "health-check-test")
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		return fmt.Errorf("failed to write test file to %s: %w", tempDir, err)
	}

	// Clean up test file
	if err := os.Remove(testFile); err != nil {
		log.Printf("[ThumbnailProcessor] Warning: failed to clean up test file %s: %v", testFile, err)
	}

	return nil
}

// isContextCanceled checks if context is canceled or timed out
func isContextCanceled(ctx context.Context) bool {
	select {
	case <-ctx.Done():
		return true
	default:
		return false
	}
}

// isRetryableError determines if an error is retryable
func isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// Check for common retryable errors
	errorStr := err.Error()
	retryablePatterns := []string{
		"timeout",
		"connection reset",
		"connection refused",
		"temporary failure",
		"service unavailable",
		"too many requests",
	}

	for _, pattern := range retryablePatterns {
		if strings.Contains(strings.ToLower(errorStr), pattern) {
			return true
		}
	}

	return false
}

// getMimeType returns MIME type for format
func (p *MinIOThumbnailProcessor) getMimeType(format entities.ThumbnailFormat) string {
	switch format {
	case entities.ThumbnailFormatJPEG:
		return "image/jpeg"
	case entities.ThumbnailFormatPNG:
		return "image/png"
	case entities.ThumbnailFormatWebP:
		return "image/webp"
	case entities.ThumbnailFormatAVIF:
		return "image/avif"
	default:
		return "image/jpeg"
	}
}

// HealthCheck verifies both MinIO replica and primary storage are accessible
func (p *MinIOThumbnailProcessor) HealthCheck(ctx context.Context) error {
	// Create health check context with timeout
	healthCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Check MinIO replica
	log.Printf("[ThumbnailProcessor] Checking MinIO replica health")
	if !p.replicaStorage.IsHealthy(healthCtx) {
		log.Printf("[ThumbnailProcessor] MinIO replica storage is not healthy")
		return fmt.Errorf("MinIO replica storage is not healthy")
	}

	// Check primary storage
	log.Printf("[ThumbnailProcessor] Checking primary storage health")
	if !p.primaryStorage.IsHealthy(healthCtx) {
		log.Printf("[ThumbnailProcessor] Primary storage is not healthy")
		return fmt.Errorf("primary storage is not healthy")
	}

	// Check temp directory access
	log.Printf("[ThumbnailProcessor] Checking temp directory access: %s", p.tempDir)
	if err := p.checkTempDirectoryAccess(); err != nil {
		log.Printf("[ThumbnailProcessor] Temp directory access failed: %v", err)
		return fmt.Errorf("temp directory access failed: %w", err)
	}

	log.Printf("[ThumbnailProcessor] Health check passed")
	return nil
}