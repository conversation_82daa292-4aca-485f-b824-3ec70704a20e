package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
)

type RedisQueue struct {
	client        *redis.Client
	queueName     string
	processingKey string
	resultKey     string
	statsKey      string
	retryKey      string
	deadKey       string
	maxRetries    int
	retryDelay    time.Duration
	jobTTL        time.Duration
}

type RedisQueueConfig struct {
	RedisAddr     string
	RedisPassword string
	RedisDB       int
	QueueName     string
	MaxRetries    int
	RetryDelay    time.Duration
	JobTTL        time.Duration
}

type QueueStats struct {
	QueueLength      int64                        `json:"queue_length"`
	ProcessingCount  int64                        `json:"processing_count"`
	CompletedCount   int64                        `json:"completed_count"`
	FailedCount      int64                        `json:"failed_count"`
	RetryCount       int64                        `json:"retry_count"`
	DeadCount        int64                        `json:"dead_count"`
	TotalProcessed   int64                        `json:"total_processed"`
	AverageTime      time.Duration                `json:"average_time"`
	ThroughputPerMin float64                      `json:"throughput_per_min"`
	ErrorRate        float64                      `json:"error_rate"`
	StatusCounts     map[entities.JobStatus]int64 `json:"status_counts"`
}

type JobWrapper struct {
	Job         *entities.ThumbnailJob `json:"job"`
	EnqueuedAt  time.Time              `json:"enqueued_at"`
	ProcessedAt *time.Time             `json:"processed_at,omitempty"`
	WorkerID    string                 `json:"worker_id,omitempty"`
}

func NewRedisQueue(config RedisQueueConfig) *RedisQueue {
	client := redis.NewClient(&redis.Options{
		Addr:     config.RedisAddr,
		Password: config.RedisPassword,
		DB:       config.RedisDB,
	})

	return &RedisQueue{
		client:        client,
		queueName:     config.QueueName,
		processingKey: config.QueueName + ":processing",
		resultKey:     config.QueueName + ":results",
		statsKey:      config.QueueName + ":stats",
		retryKey:      config.QueueName + ":retry",
		deadKey:       config.QueueName + ":dead",
		maxRetries:    config.MaxRetries,
		retryDelay:    config.RetryDelay,
		jobTTL:        config.JobTTL,
	}
}

func (rq *RedisQueue) EnqueueJob(ctx context.Context, job *entities.ThumbnailJob) error {
	// Wrap job with metadata
	wrapper := &JobWrapper{
		Job:        job,
		EnqueuedAt: time.Now(),
	}

	// Serialize job
	jobData, err := json.Marshal(wrapper)
	if err != nil {
		return fmt.Errorf("failed to serialize job: %w", err)
	}

	// Add to priority queue using sorted set
	score := float64(job.GetScore())

	if err := rq.client.ZAdd(ctx, rq.queueName, redis.Z{
		Score:  score,
		Member: jobData,
	}).Err(); err != nil {
		return fmt.Errorf("failed to enqueue job: %w", err)
	}

	// Update stats
	rq.incrementStat(ctx, "enqueued", 1)

	return nil
}

func (rq *RedisQueue) DequeueJob(ctx context.Context, workerID string) (*entities.ThumbnailJob, error) {
	// Get job with lowest score (highest priority)
	result, err := rq.client.ZPopMin(ctx, rq.queueName, 1).Result()
	if err == redis.Nil {
		return nil, nil // No jobs available
	}
	if err != nil {
		return nil, fmt.Errorf("failed to dequeue job: %w", err)
	}

	if len(result) == 0 {
		return nil, nil // No jobs available
	}

	// Deserialize job
	var wrapper JobWrapper
	if err := json.Unmarshal([]byte(result[0].Member.(string)), &wrapper); err != nil {
		return nil, fmt.Errorf("failed to deserialize job: %w", err)
	}

	// Mark as processing
	wrapper.ProcessedAt = &time.Time{}
	*wrapper.ProcessedAt = time.Now()
	wrapper.WorkerID = workerID

	// Move to processing set
	processingData, err := json.Marshal(wrapper)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize processing job: %w", err)
	}

	if err := rq.client.HSet(ctx, rq.processingKey, wrapper.Job.ID().String(), processingData).Err(); err != nil {
		return nil, fmt.Errorf("failed to mark job as processing: %w", err)
	}

	// Set expiration for processing job
	if err := rq.client.Expire(ctx, rq.processingKey, rq.jobTTL).Err(); err != nil {
		// Log error but don't fail
		fmt.Printf("Warning: failed to set expiration for processing job: %v\n", err)
	}

	// Update stats
	rq.incrementStat(ctx, "dequeued", 1)

	return wrapper.Job, nil
}

func (rq *RedisQueue) CompleteJob(ctx context.Context, jobID entities.JobID) error {
	// Remove from processing
	if err := rq.client.HDel(ctx, rq.processingKey, jobID.String()).Err(); err != nil {
		return fmt.Errorf("failed to remove job from processing: %w", err)
	}

	// Store result
	resultData := map[string]interface{}{
		"job_id":       jobID.String(),
		"status":       entities.JobStatusCompleted,
		"completed_at": time.Now(),
	}

	resultJson, err := json.Marshal(resultData)
	if err != nil {
		return fmt.Errorf("failed to serialize result: %w", err)
	}

	if err := rq.client.Set(ctx, rq.resultKey+":"+jobID.String(), resultJson, rq.jobTTL).Err(); err != nil {
		return fmt.Errorf("failed to store result: %w", err)
	}

	// Update stats
	rq.incrementStat(ctx, "completed", 1)

	return nil
}

func (rq *RedisQueue) FailJob(ctx context.Context, jobID entities.JobID, errorMessage string) error {
	// Get job from processing
	processingData, err := rq.client.HGet(ctx, rq.processingKey, jobID.String()).Result()
	if err != nil {
		return fmt.Errorf("failed to get processing job: %w", err)
	}

	var wrapper JobWrapper
	if err := json.Unmarshal([]byte(processingData), &wrapper); err != nil {
		return fmt.Errorf("failed to deserialize processing job: %w", err)
	}

	// Remove from processing
	if err := rq.client.HDel(ctx, rq.processingKey, jobID.String()).Err(); err != nil {
		return fmt.Errorf("failed to remove job from processing: %w", err)
	}

	// Check if job should be retried
	if wrapper.Job.ShouldRetry() {
		// Move to retry queue
		wrapper.Job.MarkAsRetrying()

		retryData, err := json.Marshal(wrapper)
		if err != nil {
			return fmt.Errorf("failed to serialize retry job: %w", err)
		}

		// Add to retry queue with delay
		retryTime := time.Now().Add(wrapper.Job.GetRetryDelay())
		score := float64(retryTime.Unix())

		if err := rq.client.ZAdd(ctx, rq.retryKey, redis.Z{
			Score:  score,
			Member: retryData,
		}).Err(); err != nil {
			return fmt.Errorf("failed to enqueue retry job: %w", err)
		}

		rq.incrementStat(ctx, "retried", 1)
	} else {
		// Move to dead letter queue
		wrapper.Job.MarkAsDead()

		deadData, err := json.Marshal(wrapper)
		if err != nil {
			return fmt.Errorf("failed to serialize dead job: %w", err)
		}

		if err := rq.client.LPush(ctx, rq.deadKey, deadData).Err(); err != nil {
			return fmt.Errorf("failed to enqueue dead job: %w", err)
		}

		rq.incrementStat(ctx, "dead", 1)
	}

	// Store failure result
	resultData := map[string]interface{}{
		"job_id":    jobID.String(),
		"status":    entities.JobStatusFailed,
		"error":     errorMessage,
		"failed_at": time.Now(),
	}

	resultJson, err := json.Marshal(resultData)
	if err != nil {
		return fmt.Errorf("failed to serialize failure result: %w", err)
	}

	if err := rq.client.Set(ctx, rq.resultKey+":"+jobID.String(), resultJson, rq.jobTTL).Err(); err != nil {
		return fmt.Errorf("failed to store failure result: %w", err)
	}

	// Update stats
	rq.incrementStat(ctx, "failed", 1)

	return nil
}

func (rq *RedisQueue) GetJobResult(ctx context.Context, jobID entities.JobID) (map[string]interface{}, error) {
	resultData, err := rq.client.Get(ctx, rq.resultKey+":"+jobID.String()).Result()
	if err == redis.Nil {
		return nil, nil // No result available
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get job result: %w", err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal([]byte(resultData), &result); err != nil {
		return nil, fmt.Errorf("failed to deserialize result: %w", err)
	}

	return result, nil
}

func (rq *RedisQueue) GetQueueStats(ctx context.Context) (*QueueStats, error) {
	stats := &QueueStats{
		StatusCounts: make(map[entities.JobStatus]int64),
	}

	// Get queue length
	queueLength, err := rq.client.ZCard(ctx, rq.queueName).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get queue length: %w", err)
	}
	stats.QueueLength = queueLength

	// Get processing count
	processingCount, err := rq.client.HLen(ctx, rq.processingKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get processing count: %w", err)
	}
	stats.ProcessingCount = processingCount

	// Get retry count
	retryCount, err := rq.client.ZCard(ctx, rq.retryKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get retry count: %w", err)
	}
	stats.RetryCount = retryCount

	// Get dead count
	deadCount, err := rq.client.LLen(ctx, rq.deadKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get dead count: %w", err)
	}
	stats.DeadCount = deadCount

	// Get accumulated stats
	statsData, err := rq.client.HGetAll(ctx, rq.statsKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get stats: %w", err)
	}

	for key, value := range statsData {
		if val, err := strconv.ParseInt(value, 10, 64); err == nil {
			switch key {
			case "completed":
				stats.CompletedCount = val
			case "failed":
				stats.FailedCount = val
			case "total_processed":
				stats.TotalProcessed = val
			}
		}
	}

	// Calculate derived stats
	if stats.TotalProcessed > 0 {
		stats.ErrorRate = float64(stats.FailedCount) / float64(stats.TotalProcessed)
	}

	return stats, nil
}

func (rq *RedisQueue) ProcessRetryQueue(ctx context.Context) error {
	// Get jobs ready for retry
	now := time.Now()
	results, err := rq.client.ZRangeByScore(ctx, rq.retryKey, &redis.ZRangeBy{
		Min:   "0",
		Max:   strconv.FormatInt(now.Unix(), 10),
		Count: 100, // Process up to 100 retry jobs at once
	}).Result()

	if err != nil {
		return fmt.Errorf("failed to get retry jobs: %w", err)
	}

	for _, result := range results {
		// Deserialize job
		var wrapper JobWrapper
		if err := json.Unmarshal([]byte(result), &wrapper); err != nil {
			continue // Skip invalid jobs
		}

		// Move back to main queue
		score := float64(wrapper.Job.GetScore())

		if err := rq.client.ZAdd(ctx, rq.queueName, redis.Z{
			Score:  score,
			Member: result,
		}).Err(); err != nil {
			continue // Skip on error
		}

		// Remove from retry queue
		if err := rq.client.ZRem(ctx, rq.retryKey, result).Err(); err != nil {
			// Log error but continue
			fmt.Printf("Warning: failed to remove job from retry queue: %v\n", err)
		}
	}

	return nil
}

func (rq *RedisQueue) CleanupExpiredJobs(ctx context.Context) error {
	// Clean up expired processing jobs
	processingJobs, err := rq.client.HGetAll(ctx, rq.processingKey).Result()
	if err != nil {
		return fmt.Errorf("failed to get processing jobs: %w", err)
	}

	for jobID, jobData := range processingJobs {
		var wrapper JobWrapper
		if err := json.Unmarshal([]byte(jobData), &wrapper); err != nil {
			continue // Skip invalid jobs
		}

		// Check if job has expired
		if wrapper.ProcessedAt != nil && time.Since(*wrapper.ProcessedAt) > rq.jobTTL {
			// Move to dead letter queue
			wrapper.Job.MarkAsDead()

			deadData, err := json.Marshal(wrapper)
			if err != nil {
				continue // Skip on error
			}

			if err := rq.client.LPush(ctx, rq.deadKey, deadData).Err(); err != nil {
				continue // Skip on error
			}

			// Remove from processing
			if err := rq.client.HDel(ctx, rq.processingKey, jobID).Err(); err != nil {
				fmt.Printf("Warning: failed to remove expired job from processing: %v\n", err)
			}

			rq.incrementStat(ctx, "expired", 1)
		}
	}

	return nil
}

func (rq *RedisQueue) GetQueueLength(ctx context.Context) (int64, error) {
	return rq.client.ZCard(ctx, rq.queueName).Result()
}

func (rq *RedisQueue) GetProcessingCount(ctx context.Context) (int64, error) {
	return rq.client.HLen(ctx, rq.processingKey).Result()
}

func (rq *RedisQueue) PurgeQueue(ctx context.Context) error {
	// Clear all queues
	keys := []string{
		rq.queueName,
		rq.processingKey,
		rq.retryKey,
		rq.deadKey,
	}

	for _, key := range keys {
		if err := rq.client.Del(ctx, key).Err(); err != nil {
			return fmt.Errorf("failed to purge queue %s: %w", key, err)
		}
	}

	return nil
}

func (rq *RedisQueue) incrementStat(ctx context.Context, stat string, count int64) {
	// Increment stat counter
	rq.client.HIncrBy(ctx, rq.statsKey, stat, count)

	// Update total processed
	if stat == "completed" || stat == "failed" {
		rq.client.HIncrBy(ctx, rq.statsKey, "total_processed", count)
	}
}

func (rq *RedisQueue) Close() error {
	return rq.client.Close()
}

func (rq *RedisQueue) Ping(ctx context.Context) error {
	return rq.client.Ping(ctx).Err()
}

func (rq *RedisQueue) GetHealthStatus(ctx context.Context) map[string]interface{} {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
	}

	// Check Redis connection
	if err := rq.Ping(ctx); err != nil {
		health["status"] = "unhealthy"
		health["error"] = err.Error()
		return health
	}

	// Get basic stats
	stats, err := rq.GetQueueStats(ctx)
	if err != nil {
		health["status"] = "degraded"
		health["error"] = err.Error()
		return health
	}

	health["stats"] = stats
	return health
}
