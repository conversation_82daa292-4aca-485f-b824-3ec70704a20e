package thumbnail

import (
	"context"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"image/png"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail/processors"
)

// TestThumbnailIntegration tests the complete thumbnail generation pipeline
func TestThumbnailIntegration(t *testing.T) {
	// Setup test environment
	tempDir := t.TempDir()

	// Create test image files
	testImageJPEG := createTestJPEGImage(t, tempDir, "test_image.jpg", 800, 600)
	testImagePNG := createTestPNGImage(t, tempDir, "test_image.png", 1024, 768)

	// Test cases
	testCases := []struct {
		name       string
		sourcePath string
		mimeType   string
		sizes      []entities.ThumbnailSize
		formats    []entities.ThumbnailFormat
	}{
		{
			name:       "JPEG Image - Multiple Sizes",
			sourcePath: testImageJPEG,
			mimeType:   "image/jpeg",
			sizes:      []entities.ThumbnailSize{entities.ThumbnailSizeSmall, entities.ThumbnailSizeMedium, entities.ThumbnailSizeLarge},
			formats:    []entities.ThumbnailFormat{entities.ThumbnailFormatJPEG},
		},
		{
			name:       "PNG Image - Multiple Formats",
			sourcePath: testImagePNG,
			mimeType:   "image/png",
			sizes:      []entities.ThumbnailSize{entities.ThumbnailSizeMedium},
			formats:    []entities.ThumbnailFormat{entities.ThumbnailFormatJPEG, entities.ThumbnailFormatPNG},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test image processor
			processor := processors.NewImageProcessor(processors.ImageProcessorConfig{
				TempDir:     tempDir,
				MaxFileSize: 100 * 1024 * 1024, // 100MB
			})

			// Validate source image
			err := processor.ValidateImage(tc.sourcePath)
			require.NoError(t, err, "Source image should be valid")

			// Process thumbnails for each size and format combination
			for _, size := range tc.sizes {
				for _, format := range tc.formats {
					t.Run(fmt.Sprintf("Size_%s_Format_%s", size, format), func(t *testing.T) {
						// Process thumbnail
						result, err := processor.ProcessImage(context.Background(), tc.sourcePath, processors.ProcessingOptions{
							Size:    size,
							Format:  format,
							Quality: 85,
						})
						require.NoError(t, err, "Thumbnail processing should succeed")
						require.NotNil(t, result, "Processing result should not be nil")

						// Verify result properties
						assert.NotEmpty(t, result.OutputPath, "Output path should not be empty")
						assert.Greater(t, result.Width, 0, "Width should be positive")
						assert.Greater(t, result.Height, 0, "Height should be positive")
						assert.Greater(t, result.FileSize, int64(0), "File size should be positive")
						assert.Equal(t, format, result.Format, "Format should match requested format")
						assert.NotEmpty(t, result.Checksum, "Checksum should not be empty")

						// Verify output file exists
						assert.FileExists(t, result.OutputPath, "Output file should exist")

						// Verify file size matches reported size
						fileInfo, err := os.Stat(result.OutputPath)
						require.NoError(t, err, "Should be able to stat output file")
						assert.Equal(t, result.FileSize, fileInfo.Size(), "Reported file size should match actual file size")

						// Verify thumbnail dimensions are within expected ranges
						expectedWidth, expectedHeight := getExpectedDimensions(size)
						assert.LessOrEqual(t, result.Width, expectedWidth, "Width should not exceed expected maximum")
						assert.LessOrEqual(t, result.Height, expectedHeight, "Height should not exceed expected maximum")

						// Verify the generated thumbnail is a valid image
						verifyThumbnailImage(t, result.OutputPath, format)

						// Clean up
						os.Remove(result.OutputPath)
					})
				}
			}
		})
	}
}

// TestThumbnailBatchProcessing tests batch processing functionality
func TestThumbnailBatchProcessing(t *testing.T) {
	tempDir := t.TempDir()
	testImage := createTestJPEGImage(t, tempDir, "batch_test.jpg", 1200, 900)

	processor := processors.NewImageProcessor(processors.ImageProcessorConfig{
		TempDir:     tempDir,
		MaxFileSize: 100 * 1024 * 1024,
	})

	// Create batch processing options
	optionsList := []processors.ProcessingOptions{
		{Size: entities.ThumbnailSizeSmall, Format: entities.ThumbnailFormatJPEG, Quality: 85},
		{Size: entities.ThumbnailSizeMedium, Format: entities.ThumbnailFormatJPEG, Quality: 85},
		{Size: entities.ThumbnailSizeLarge, Format: entities.ThumbnailFormatJPEG, Quality: 85},
	}

	// Process batch
	results, err := processor.ProcessImageBatch(context.Background(), testImage, optionsList)
	require.NoError(t, err, "Batch processing should succeed")
	require.Len(t, results, len(optionsList), "Should have result for each option")

	// Verify each result
	for i, result := range results {
		assert.NotNil(t, result, "Result %d should not be nil", i)
		assert.FileExists(t, result.OutputPath, "Output file %d should exist", i)
		assert.Greater(t, result.FileSize, int64(0), "File size %d should be positive", i)

		// Clean up
		os.Remove(result.OutputPath)
	}
}

// TestThumbnailErrorHandling tests error handling scenarios
func TestThumbnailErrorHandling(t *testing.T) {
	tempDir := t.TempDir()
	processor := processors.NewImageProcessor(processors.ImageProcessorConfig{
		TempDir:     tempDir,
		MaxFileSize: 1024, // Very small limit for testing
	})

	// Test with non-existent file
	_, err := processor.ProcessImage(context.Background(), "non_existent_file.jpg", processors.ProcessingOptions{
		Size:    entities.ThumbnailSizeMedium,
		Format:  entities.ThumbnailFormatJPEG,
		Quality: 85,
	})
	assert.Error(t, err, "Should fail with non-existent file")

	// Test with file too large
	largeImage := createTestJPEGImage(t, tempDir, "large_image.jpg", 2000, 2000)
	err = processor.ValidateImage(largeImage)
	assert.Error(t, err, "Should fail validation for large file")

	// Test with invalid file
	invalidFile := filepath.Join(tempDir, "invalid.txt")
	err = os.WriteFile(invalidFile, []byte("not an image"), 0644)
	require.NoError(t, err)

	err = processor.ValidateImage(invalidFile)
	assert.Error(t, err, "Should fail validation for non-image file")
}

// Helper functions

func createTestJPEGImage(t *testing.T, dir, filename string, width, height int) string {
	path := filepath.Join(dir, filename)

	// Create a simple test image
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// Fill with a gradient pattern
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			r := uint8((x * 255) / width)
			g := uint8((y * 255) / height)
			b := uint8(128)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	file, err := os.Create(path)
	require.NoError(t, err)
	defer file.Close()

	err = jpeg.Encode(file, img, &jpeg.Options{Quality: 90})
	require.NoError(t, err)

	return path
}

func createTestPNGImage(t *testing.T, dir, filename string, width, height int) string {
	path := filepath.Join(dir, filename)

	// Create a simple test image with transparency
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// Fill with a checkerboard pattern
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			if (x/50+y/50)%2 == 0 {
				img.Set(x, y, color.RGBA{255, 0, 0, 255}) // Red
			} else {
				img.Set(x, y, color.RGBA{0, 0, 255, 128}) // Semi-transparent blue
			}
		}
	}

	file, err := os.Create(path)
	require.NoError(t, err)
	defer file.Close()

	err = png.Encode(file, img)
	require.NoError(t, err)

	return path
}

func getExpectedDimensions(size entities.ThumbnailSize) (int, int) {
	switch size {
	case entities.ThumbnailSizeSmall:
		return 150, 150
	case entities.ThumbnailSizeMedium:
		return 300, 300
	case entities.ThumbnailSizeLarge:
		return 600, 600
	case entities.ThumbnailSizeXLarge:
		return 1200, 1200
	case entities.ThumbnailSizePreview:
		return 1920, 1080
	default:
		return 300, 300
	}
}

func verifyThumbnailImage(t *testing.T, path string, format entities.ThumbnailFormat) {
	file, err := os.Open(path)
	require.NoError(t, err)
	defer file.Close()

	// Try to decode the image to verify it's valid
	_, imageFormat, err := image.DecodeConfig(file)
	require.NoError(t, err, "Generated thumbnail should be a valid image")

	// Verify format matches (note: format names might differ slightly)
	switch format {
	case entities.ThumbnailFormatJPEG:
		assert.Equal(t, "jpeg", imageFormat, "Should be JPEG format")
	case entities.ThumbnailFormatPNG:
		assert.Equal(t, "png", imageFormat, "Should be PNG format")
	}
}

// TestThumbnailServiceEndToEnd tests the complete thumbnail service workflow
func TestThumbnailServiceEndToEnd(t *testing.T) {
	// This test would require a full service setup with database and storage
	// For now, we'll create a simplified version that tests the core components

	tempDir := t.TempDir()
	testImage := createTestJPEGImage(t, tempDir, "e2e_test.jpg", 1000, 800)

	// Test the complete workflow:
	// 1. Create thumbnail job
	// 2. Process thumbnail
	// 3. Store thumbnail metadata
	// 4. Verify thumbnail can be retrieved

	fileID := uuid.New()

	// Create processing options
	sizes := []entities.ThumbnailSize{entities.ThumbnailSizeSmall, entities.ThumbnailSizeMedium}
	formats := []entities.ThumbnailFormat{entities.ThumbnailFormatJPEG}

	// Process thumbnails
	processor := processors.NewImageProcessor(processors.ImageProcessorConfig{
		TempDir:     tempDir,
		MaxFileSize: 100 * 1024 * 1024,
	})

	for _, size := range sizes {
		for _, format := range formats {
			result, err := processor.ProcessImage(context.Background(), testImage, processors.ProcessingOptions{
				Size:    size,
				Format:  format,
				Quality: 85,
			})
			require.NoError(t, err, "Thumbnail processing should succeed")

			// Create thumbnail entity
			thumbnail := entities.NewThumbnail(fileID, size, format)

			// Mark as completed with processing results
			thumbnail.MarkAsCompleted(
				result.Width,
				result.Height,
				result.FileSize,
				result.OutputPath,
				result.Checksum,
			)

			// Verify thumbnail entity
			assert.Equal(t, fileID, thumbnail.FileID(), "File ID should match")
			assert.Equal(t, size, thumbnail.Size(), "Size should match")
			assert.Equal(t, format, thumbnail.Format(), "Format should match")
			assert.Equal(t, result.Width, thumbnail.Width(), "Width should match")
			assert.Equal(t, result.Height, thumbnail.Height(), "Height should match")
			assert.Equal(t, result.FileSize, thumbnail.FileSize(), "File size should match")
			assert.Equal(t, result.Checksum, thumbnail.Checksum(), "Checksum should match")
			assert.Equal(t, entities.ThumbnailStatusCompleted, thumbnail.Status(), "Status should be completed")

			// Clean up
			os.Remove(result.OutputPath)
		}
	}
}

// TestThumbnailPerformance tests thumbnail generation performance
func TestThumbnailPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	tempDir := t.TempDir()

	// Create multiple test images of different sizes
	testImages := []struct {
		name   string
		width  int
		height int
	}{
		{"small", 400, 300},
		{"medium", 1024, 768},
		{"large", 2048, 1536},
		{"xlarge", 4096, 3072},
	}

	processor := processors.NewImageProcessor(processors.ImageProcessorConfig{
		TempDir:     tempDir,
		MaxFileSize: 100 * 1024 * 1024,
	})

	for _, img := range testImages {
		t.Run(img.name, func(t *testing.T) {
			// Create test image
			imagePath := createTestJPEGImage(t, tempDir, fmt.Sprintf("perf_%s.jpg", img.name), img.width, img.height)
			defer os.Remove(imagePath)

			// Measure processing time
			start := time.Now()
			result, err := processor.ProcessImage(context.Background(), imagePath, processors.ProcessingOptions{
				Size:    entities.ThumbnailSizeMedium,
				Format:  entities.ThumbnailFormatJPEG,
				Quality: 85,
			})
			duration := time.Since(start)

			require.NoError(t, err, "Processing should succeed")
			require.NotNil(t, result, "Result should not be nil")

			// Log performance metrics
			t.Logf("Image %s (%dx%d): processed in %v, output size: %d bytes",
				img.name, img.width, img.height, duration, result.FileSize)

			// Performance assertions (adjust thresholds as needed)
			assert.Less(t, duration, 10*time.Second, "Processing should complete within 10 seconds")
			assert.Greater(t, result.FileSize, int64(1000), "Output should be at least 1KB")
			assert.Less(t, result.FileSize, int64(1024*1024), "Output should be less than 1MB")

			// Clean up
			os.Remove(result.OutputPath)
		})
	}
}
