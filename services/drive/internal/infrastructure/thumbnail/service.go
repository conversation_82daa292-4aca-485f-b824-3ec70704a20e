package thumbnail

import (
	"context"
	"fmt"
	"log"
	"path/filepath"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/repositories"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/services"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail/processors"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail/queue"
)

type ThumbnailServiceImpl struct {
	thumbnailRepo    repositories.ThumbnailRepository
	jobRepo          repositories.JobRepository
	imageProcessor   *processors.MinIOThumbnailProcessor
	videoProcessor   *processors.VideoProcessor
	redisQueue       *queue.RedisQueue
	thumbnailService services.ThumbnailService
	config           ServiceConfig
	workers          []*ThumbnailWorker
	workersMutex     sync.RWMutex
	metrics          *ServiceMetrics
	healthStatus     *HealthStatus
}

type ServiceConfig struct {
	TempDir         string
	MaxWorkers      int
	ProcessTimeout  time.Duration
	MaxFileSize     int64
	EnableBatching  bool
	BatchSize       int
	CleanupInterval time.Duration
	StoragePath     string
}

type ThumbnailWorker struct {
	id        string
	service   *ThumbnailServiceImpl
	queue     *queue.RedisQueue
	processor *ThumbnailProcessor
	stats     *WorkerStats
	stopCh    chan struct{}
}

type ThumbnailProcessor struct {
	imageProcessor *processors.MinIOThumbnailProcessor
	videoProcessor *processors.VideoProcessor
	tempDir        string
	thumbnailRepo  repositories.ThumbnailRepository
}

type WorkerStats struct {
	JobsProcessed int64
	JobsSucceeded int64
	JobsFailed    int64
	AverageTime   time.Duration
	LastActive    time.Time
	CurrentJob    string
	WorkerID      string
}

type ServiceMetrics struct {
	TotalJobs      int64
	SuccessfulJobs int64
	FailedJobs     int64
	AverageJobTime time.Duration
	ActiveWorkers  int
	QueueSize      int64
	LastUpdated    time.Time
	mutex          sync.RWMutex
}

type HealthStatus struct {
	Status          string
	LastHealthCheck time.Time
	ImageProcessor  bool
	VideoProcessor  bool
	RedisQueue      bool
	TempDirectory   bool
	PrimaryStorage  bool
	ReplicaStorage  bool
	mutex           sync.RWMutex
}

func NewThumbnailServiceImpl(
	thumbnailRepo repositories.ThumbnailRepository,
	jobRepo repositories.JobRepository,
	imageProcessor *processors.MinIOThumbnailProcessor,
	videoProcessor *processors.VideoProcessor,
	redisQueue *queue.RedisQueue,
	config ServiceConfig,
) *ThumbnailServiceImpl {
	thumbnailService := services.NewThumbnailService(thumbnailRepo, jobRepo)

	return &ThumbnailServiceImpl{
		thumbnailRepo:    thumbnailRepo,
		jobRepo:          jobRepo,
		imageProcessor:   imageProcessor,
		videoProcessor:   videoProcessor,
		redisQueue:       redisQueue,
		thumbnailService: thumbnailService,
		config:           config,
		workers:          make([]*ThumbnailWorker, 0),
		metrics: &ServiceMetrics{
			LastUpdated: time.Now(),
		},
		healthStatus: &HealthStatus{
			Status:          "starting",
			LastHealthCheck: time.Now(),
		},
	}
}

func (ts *ThumbnailServiceImpl) Start(ctx context.Context) error {
	log.Printf("[ThumbnailService] Starting thumbnail service with %d workers", ts.config.MaxWorkers)

	// Perform initial health check
	if err := ts.performHealthCheck(ctx); err != nil {
		log.Printf("[ThumbnailService] Initial health check failed: %v", err)
		return fmt.Errorf("initial health check failed: %w", err)
	}

	// Start worker pool
	ts.workersMutex.Lock()
	for i := 0; i < ts.config.MaxWorkers; i++ {
		workerID := fmt.Sprintf("worker-%d", i)
		worker := &ThumbnailWorker{
			id:      workerID,
			service: ts,
			queue:   ts.redisQueue,
			processor: &ThumbnailProcessor{
				imageProcessor: ts.imageProcessor,
				videoProcessor: ts.videoProcessor,
				tempDir:        ts.config.TempDir,
				thumbnailRepo:  ts.thumbnailRepo, // CRITICAL FIX: Pass repository to processor
			},
			stats: &WorkerStats{
				WorkerID:   workerID,
				LastActive: time.Now(),
			},
			stopCh: make(chan struct{}),
		}

		ts.workers = append(ts.workers, worker)
		go worker.Start(ctx)
		log.Printf("[ThumbnailService] Started worker: %s", workerID)
	}
	ts.workersMutex.Unlock()

	// Start background cleanup
	log.Printf("[ThumbnailService] Starting cleanup routine")
	go ts.startCleanupRoutine(ctx)

	// Start retry processor
	log.Printf("[ThumbnailService] Starting retry processor")
	go ts.startRetryProcessor(ctx)

	// Start metrics updater
	log.Printf("[ThumbnailService] Starting metrics updater")
	go ts.startMetricsUpdater(ctx)

	// Start health checker
	log.Printf("[ThumbnailService] Starting health checker")
	go ts.startHealthChecker(ctx)

	log.Printf("[ThumbnailService] Thumbnail service started successfully")
	return nil
}

func (ts *ThumbnailServiceImpl) CreateThumbnailJob(
	ctx context.Context,
	fileID, userID uuid.UUID,
	sourcePath, mimeType string,
	sizes []entities.ThumbnailSize,
	formats []entities.ThumbnailFormat,
) (*entities.ThumbnailJob, error) {
	// Check if there's already a pending or processing job for this file
	existingJobs, err := ts.jobRepo.GetByFileID(ctx, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing jobs: %w", err)
	}

	// Check if there's a pending or processing job
	for _, job := range existingJobs {
		if job.Status() == entities.JobStatusPending || job.Status() == entities.JobStatusProcessing {
			return job, nil // Return existing job instead of creating new one
		}
	}

	// Create job in database first
	job, err := ts.thumbnailService.CreateThumbnailJob(ctx, fileID, userID, sourcePath, mimeType, sizes, formats)
	if err != nil {
		return nil, fmt.Errorf("failed to create thumbnail job: %w", err)
	}

	// Enqueue job to Redis for processing
	if err := ts.redisQueue.EnqueueJob(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to enqueue thumbnail job: %w", err)
	}

	return job, nil
}

func (ts *ThumbnailServiceImpl) ProcessJobSync(
	ctx context.Context,
	fileID, userID uuid.UUID,
	sourcePath, mimeType string,
	sizes []entities.ThumbnailSize,
	formats []entities.ThumbnailFormat,
) ([]*entities.Thumbnail, error) {
	log.Printf("[ThumbnailService] Processing sync job for file: %s, sizes: %v, formats: %v", fileID, sizes, formats)
	start := time.Now()

	// Create processor
	processor := &ThumbnailProcessor{
		imageProcessor: ts.imageProcessor,
		videoProcessor: ts.videoProcessor,
		tempDir:        ts.config.TempDir,
	}

	thumbnails := make([]*entities.Thumbnail, 0)

	// Process each size/format combination
	for _, size := range sizes {
		for _, format := range formats {
			log.Printf("[ThumbnailService] Processing thumbnail %s/%s for file: %s", size, format, fileID)
			thumbnail, err := processor.ProcessThumbnail(ctx, fileID, sourcePath, mimeType, size, format)
			if err != nil {
				log.Printf("[ThumbnailService] Failed to process thumbnail %s/%s for file %s: %v", size, format, fileID, err)
				ts.updateMetrics(false, time.Since(start))
				return nil, fmt.Errorf("failed to process thumbnail %s/%s: %w", size, format, err)
			}

			// Save thumbnail to database
			if err := ts.thumbnailRepo.Create(ctx, thumbnail); err != nil {
				log.Printf("[ThumbnailService] Failed to save thumbnail %s/%s for file %s: %v", size, format, fileID, err)
				ts.updateMetrics(false, time.Since(start))
				return nil, fmt.Errorf("failed to save thumbnail: %w", err)
			}

			thumbnails = append(thumbnails, thumbnail)
			log.Printf("[ThumbnailService] Successfully processed thumbnail %s/%s for file: %s", size, format, fileID)
		}
	}

	ts.updateMetrics(true, time.Since(start))
	log.Printf("[ThumbnailService] Completed sync job for file: %s in %v", fileID, time.Since(start))
	return thumbnails, nil
}

func (ts *ThumbnailServiceImpl) GetThumbnail(
	ctx context.Context,
	fileID uuid.UUID,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) (*entities.Thumbnail, error) {
	return ts.thumbnailService.GetThumbnail(ctx, fileID, size, format)
}

func (ts *ThumbnailServiceImpl) GetThumbnailsForFile(ctx context.Context, fileID uuid.UUID) ([]*entities.Thumbnail, error) {
	return ts.thumbnailService.GetThumbnailsForFile(ctx, fileID)
}

func (ts *ThumbnailServiceImpl) DeleteThumbnailsForFile(ctx context.Context, fileID uuid.UUID) error {
	return ts.thumbnailService.DeleteThumbnailsForFile(ctx, fileID)
}

func (ts *ThumbnailServiceImpl) GetJobStatus(ctx context.Context, jobID entities.JobID) (*entities.ThumbnailJob, error) {
	return ts.thumbnailService.GetJobStatus(ctx, jobID)
}

func (ts *ThumbnailServiceImpl) GetStatistics(ctx context.Context) (map[string]interface{}, error) {
	// Get service statistics
	serviceStats, err := ts.thumbnailService.GetStatistics(ctx)
	if err != nil {
		return nil, err
	}

	// Get queue statistics
	queueStats, err := ts.redisQueue.GetQueueStats(ctx)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"service": serviceStats,
		"queue":   queueStats,
		"workers": ts.config.MaxWorkers,
	}, nil
}

func (ts *ThumbnailServiceImpl) GetHealthStatus(ctx context.Context) map[string]interface{} {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
	}

	// Check queue health
	queueHealth := ts.redisQueue.GetHealthStatus(ctx)
	if queueHealth["status"] != "healthy" {
		health["status"] = "degraded"
		health["queue_error"] = queueHealth["error"]
	}

	// Check processors
	if err := ts.imageProcessor.HealthCheck(ctx); err == nil {
		health["image_processor"] = "healthy"
	} else {
		health["image_processor"] = "unhealthy"
		health["status"] = "degraded"
	}

	if err := ts.videoProcessor.ValidateVideo("/dev/null"); err == nil {
		health["video_processor"] = "healthy"
	} else {
		health["video_processor"] = "unhealthy"
		health["status"] = "degraded"
	}

	return health
}

func (ts *ThumbnailServiceImpl) startCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(ts.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// Cleanup expired thumbnails
			ts.thumbnailService.CleanupExpiredThumbnails(ctx, 7*24*time.Hour) // 7 days

			// Cleanup old jobs
			ts.thumbnailService.CleanupOldJobs(ctx, 30*24*time.Hour) // 30 days

			// Cleanup expired queue jobs
			ts.redisQueue.CleanupExpiredJobs(ctx)
		}
	}
}

func (ts *ThumbnailServiceImpl) startRetryProcessor(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// Process retry queue
			ts.redisQueue.ProcessRetryQueue(ctx)
		}
	}
}

// Worker implementation
func (w *ThumbnailWorker) Start(ctx context.Context) {
	w.stats.LastActive = time.Now()

	for {
		select {
		case <-w.stopCh:
			return
		case <-ctx.Done():
			return
		default:
			// Try to get a job
			job, err := w.queue.DequeueJob(ctx, w.id)
			if err != nil {
				time.Sleep(1 * time.Second)
				continue
			}

			if job == nil {
				time.Sleep(1 * time.Second)
				continue
			}

			// Process the job
			w.processJob(ctx, job)
		}
	}
}

func (w *ThumbnailWorker) processJob(ctx context.Context, job *entities.ThumbnailJob) {
	startTime := time.Now()
	w.stats.JobsProcessed++
	w.stats.LastActive = time.Now()

	// Mark job as processing in database
	job.MarkAsProcessing()
	if err := w.service.jobRepo.Update(ctx, job); err != nil {
		// Log error but continue processing
		fmt.Printf("Warning: failed to mark job as processing in database: %v\n", err)
	}

	// Process thumbnails for the job
	err := w.processor.ProcessJob(ctx, job)

	duration := time.Since(startTime)
	w.updateAverageTime(duration)

	if err != nil {
		// Job failed - update both Redis and database
		w.stats.JobsFailed++

		// Update job status in database
		job.MarkAsFailed(err.Error())
		if dbErr := w.service.jobRepo.Update(ctx, job); dbErr != nil {
			fmt.Printf("Warning: failed to mark job as failed in database: %v\n", dbErr)
		}

		// Update Redis queue
		w.queue.FailJob(ctx, job.ID(), err.Error())
		return
	}

	// Job succeeded - update both Redis and database
	w.stats.JobsSucceeded++

	// Update job status in database
	job.MarkAsCompleted()
	if dbErr := w.service.jobRepo.Update(ctx, job); dbErr != nil {
		fmt.Printf("Warning: failed to mark job as completed in database: %v\n", dbErr)
	}

	// Update Redis queue
	w.queue.CompleteJob(ctx, job.ID())
}

func (w *ThumbnailWorker) updateAverageTime(duration time.Duration) {
	if w.stats.JobsProcessed == 1 {
		w.stats.AverageTime = duration
	} else {
		// Exponential moving average
		alpha := 0.1
		w.stats.AverageTime = time.Duration(float64(w.stats.AverageTime)*(1-alpha) + float64(duration)*alpha)
	}
}

func (w *ThumbnailWorker) Stop() {
	close(w.stopCh)
}

// Processor implementation
func (p *ThumbnailProcessor) ProcessJob(ctx context.Context, job *entities.ThumbnailJob) error {
	// Process each size/format combination
	for _, size := range job.Sizes() {
		for _, format := range job.Formats() {
			thumbnail, err := p.ProcessThumbnail(ctx, job.FileID(), job.SourcePath(), job.MimeType(), size, format)
			if err != nil {
				return fmt.Errorf("failed to process thumbnail %s/%s: %w", size, format, err)
			}

			// CRITICAL FIX: Save thumbnail to database
			if p.thumbnailRepo != nil {
				if err := p.thumbnailRepo.Create(ctx, thumbnail); err != nil {
					log.Printf("[ThumbnailProcessor] Failed to save thumbnail %s/%s for file %s: %v", size, format, job.FileID(), err)
					return fmt.Errorf("failed to save thumbnail %s/%s: %w", size, format, err)
				}
				log.Printf("[ThumbnailProcessor] Successfully saved thumbnail %s/%s for file: %s", size, format, job.FileID())
			} else {
				log.Printf("[ThumbnailProcessor] WARNING: thumbnailRepo is nil, cannot save thumbnail to database")
			}
		}
	}

	return nil
}

func (p *ThumbnailProcessor) ProcessThumbnail(
	ctx context.Context,
	fileID uuid.UUID,
	sourcePath, mimeType string,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) (*entities.Thumbnail, error) {
	// Create thumbnail entity
	thumbnail := entities.NewThumbnail(fileID, size, format)
	thumbnail.MarkAsProcessing()

	var result *processors.ProcessingResult
	var err error

	// Process based on file type
	if isVideoMimeType(mimeType) {
		// Process video
		options := processors.ProcessingOptions{
			Size:    size,
			Format:  format,
			Quality: thumbnail.GetQuality(),
		}
		result, err = p.videoProcessor.ProcessVideo(ctx, sourcePath, options)
	} else {
		// Process image
		options := processors.ProcessingOptions{
			Size:    size,
			Format:  format,
			Quality: thumbnail.GetQuality(),
		}
		result, err = p.imageProcessor.ProcessThumbnailFromMinIO(ctx, sourcePath, options)
	}

	if err != nil {
		thumbnail.MarkAsFailed()
		return nil, fmt.Errorf("processing failed: %w", err)
	}

	// Move result to final storage location
	finalPath := p.generateFinalPath(fileID, size, format)
	if err := p.moveToStorage(result.OutputPath, finalPath); err != nil {
		thumbnail.MarkAsFailed()
		return nil, fmt.Errorf("failed to move to storage: %w", err)
	}

	// Mark as completed
	thumbnail.MarkAsCompleted(
		result.Width,
		result.Height,
		result.FileSize,
		finalPath,
		result.Checksum,
	)

	return thumbnail, nil
}

func (p *ThumbnailProcessor) generateFinalPath(fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) string {
	fileIDStr := fileID.String()

	// Create hierarchical path: first2/next2/fileID/fileID_size.format
	dir1 := fileIDStr[0:2]
	dir2 := fileIDStr[2:4]

	var ext string
	switch format {
	case entities.ThumbnailFormatJPEG:
		ext = ".jpg"
	case entities.ThumbnailFormatPNG:
		ext = ".png"
	case entities.ThumbnailFormatWebP:
		ext = ".webp"
	case entities.ThumbnailFormatAVIF:
		ext = ".avif"
	default:
		ext = ".jpg"
	}

	filename := fmt.Sprintf("%s_%s%s", fileIDStr, size, ext)
	return filepath.Join(p.tempDir, "thumbnails", dir1, dir2, fileIDStr, filename)
}

func (p *ThumbnailProcessor) moveToStorage(tempPath, finalPath string) error {
	// Implementation would move file from temp to final storage
	// For now, just copy the logic
	return nil
}

func isVideoMimeType(mimeType string) bool {
	videoTypes := []string{
		"video/mp4",
		"video/avi",
		"video/quicktime",
		"video/x-msvideo",
		"video/x-flv",
		"video/webm",
		"video/x-matroska",
		"video/x-m4v",
	}

	for _, videoType := range videoTypes {
		if mimeType == videoType {
			return true
		}
	}
	return false
}

// updateMetrics updates service metrics
func (ts *ThumbnailServiceImpl) updateMetrics(success bool, duration time.Duration) {
	ts.metrics.mutex.Lock()
	defer ts.metrics.mutex.Unlock()

	ts.metrics.TotalJobs++
	if success {
		ts.metrics.SuccessfulJobs++
	} else {
		ts.metrics.FailedJobs++
	}

	// Update average job time using exponential moving average
	if ts.metrics.TotalJobs == 1 {
		ts.metrics.AverageJobTime = duration
	} else {
		alpha := 0.1
		ts.metrics.AverageJobTime = time.Duration(float64(ts.metrics.AverageJobTime)*(1-alpha) + float64(duration)*alpha)
	}

	ts.metrics.LastUpdated = time.Now()
}

// performHealthCheck performs comprehensive health check
func (ts *ThumbnailServiceImpl) performHealthCheck(ctx context.Context) error {
	ts.healthStatus.mutex.Lock()
	defer ts.healthStatus.mutex.Unlock()

	ts.healthStatus.LastHealthCheck = time.Now()

	// Check image processor
	if err := ts.imageProcessor.HealthCheck(ctx); err != nil {
		ts.healthStatus.ImageProcessor = false
		ts.healthStatus.Status = "unhealthy"
		return fmt.Errorf("image processor health check failed: %w", err)
	}
	ts.healthStatus.ImageProcessor = true

	// Check video processor
	if err := ts.videoProcessor.ValidateVideo("/dev/null"); err != nil {
		ts.healthStatus.VideoProcessor = false
		// Video processor failure is not critical
		log.Printf("[ThumbnailService] Video processor health check failed: %v", err)
	} else {
		ts.healthStatus.VideoProcessor = true
	}

	// Check Redis queue
	queueHealth := ts.redisQueue.GetHealthStatus(ctx)
	if queueHealth["status"] != "healthy" {
		ts.healthStatus.RedisQueue = false
		ts.healthStatus.Status = "degraded"
		return fmt.Errorf("redis queue health check failed: %v", queueHealth["error"])
	}
	ts.healthStatus.RedisQueue = true

	ts.healthStatus.Status = "healthy"
	return nil
}

// startMetricsUpdater starts metrics update routine
func (ts *ThumbnailServiceImpl) startMetricsUpdater(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			ts.updateServiceMetrics(ctx)
		}
	}
}

// startHealthChecker starts health check routine
func (ts *ThumbnailServiceImpl) startHealthChecker(ctx context.Context) {
	ticker := time.NewTicker(60 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if err := ts.performHealthCheck(ctx); err != nil {
				log.Printf("[ThumbnailService] Health check failed: %v", err)
			}
		}
	}
}

// updateServiceMetrics updates service-level metrics
func (ts *ThumbnailServiceImpl) updateServiceMetrics(ctx context.Context) {
	ts.metrics.mutex.Lock()
	defer ts.metrics.mutex.Unlock()

	// Update active workers count
	ts.workersMutex.RLock()
	activeWorkers := 0
	for _, worker := range ts.workers {
		if time.Since(worker.stats.LastActive) < 2*time.Minute {
			activeWorkers++
		}
	}
	ts.metrics.ActiveWorkers = activeWorkers
	ts.workersMutex.RUnlock()

	// Update queue size
	queueStats, err := ts.redisQueue.GetQueueStats(ctx)
	if err == nil {
		ts.metrics.QueueSize = queueStats.QueueLength
	}

	ts.metrics.LastUpdated = time.Now()
}

// GetServiceMetrics returns current service metrics
func (ts *ThumbnailServiceImpl) GetServiceMetrics() *ServiceMetrics {
	ts.metrics.mutex.RLock()
	defer ts.metrics.mutex.RUnlock()

	return &ServiceMetrics{
		TotalJobs:      ts.metrics.TotalJobs,
		SuccessfulJobs: ts.metrics.SuccessfulJobs,
		FailedJobs:     ts.metrics.FailedJobs,
		AverageJobTime: ts.metrics.AverageJobTime,
		ActiveWorkers:  ts.metrics.ActiveWorkers,
		QueueSize:      ts.metrics.QueueSize,
		LastUpdated:    ts.metrics.LastUpdated,
	}
}

// GetDetailedHealthStatus returns detailed health status
func (ts *ThumbnailServiceImpl) GetDetailedHealthStatus() *HealthStatus {
	ts.healthStatus.mutex.RLock()
	defer ts.healthStatus.mutex.RUnlock()

	return &HealthStatus{
		Status:          ts.healthStatus.Status,
		LastHealthCheck: ts.healthStatus.LastHealthCheck,
		ImageProcessor:  ts.healthStatus.ImageProcessor,
		VideoProcessor:  ts.healthStatus.VideoProcessor,
		RedisQueue:      ts.healthStatus.RedisQueue,
		TempDirectory:   ts.healthStatus.TempDirectory,
		PrimaryStorage:  ts.healthStatus.PrimaryStorage,
		ReplicaStorage:  ts.healthStatus.ReplicaStorage,
	}
}
