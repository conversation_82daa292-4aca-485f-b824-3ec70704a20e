package container

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	pkglogger "github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/database"
	pkgconfig "github.com/swork-team/platform/pkg/config"

	commonmiddleware "github.com/swork-team/platform/pkg/middleware"
	filecommands "github.com/swork-team/platform/services/drive/internal/application/file/commands"
	filequeries "github.com/swork-team/platform/services/drive/internal/application/file/queries"
	foldercommands "github.com/swork-team/platform/services/drive/internal/application/folder/commands"
	folderqueries "github.com/swork-team/platform/services/drive/internal/application/folder/queries"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	sharingcommands "github.com/swork-team/platform/services/drive/internal/application/sharing/commands"
	sharingqueries "github.com/swork-team/platform/services/drive/internal/application/sharing/queries"
	uploadcommands "github.com/swork-team/platform/services/drive/internal/application/upload/commands"
	uploadqueries "github.com/swork-team/platform/services/drive/internal/application/upload/queries"
	auditentities "github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	auditrepositories "github.com/swork-team/platform/services/drive/internal/domain/audit/repositories"
	filerepositories "github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
	fileservices "github.com/swork-team/platform/services/drive/internal/domain/file/services"
	sharingrepositories "github.com/swork-team/platform/services/drive/internal/domain/sharing/repositories"
	sharingservices "github.com/swork-team/platform/services/drive/internal/domain/sharing/services"
	thumbnailrepositories "github.com/swork-team/platform/services/drive/internal/domain/thumbnail/repositories"
	uploadrepositories "github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/audit"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/config"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/handlers"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/middleware"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/http/routes"
	gormrepo "github.com/swork-team/platform/services/drive/internal/infrastructure/persistence/gorm"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/storage"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail/processors"
	"github.com/swork-team/platform/services/drive/internal/infrastructure/thumbnail/queue"
)


type Container struct {
	// Configuration
	Config *config.DriveConfig

	// Database
	DB *gorm.DB

	// Redis
	Redis *redis.Client

	// Logger
	Logger pkglogger.Logger

	// Repositories
	FileRepository      filerepositories.FileRepository
	FolderRepository    filerepositories.FolderRepository
	SharingRepository   sharingrepositories.SharingRepository
	UploadRepository    uploadrepositories.UploadRepository
	AuditRepository     auditrepositories.AuditRepository
	ThumbnailRepository thumbnailrepositories.ThumbnailRepository
	JobRepository       thumbnailrepositories.JobRepository

	// Domain Services
	FileService          fileservices.FileService
	SharingService       sharingservices.SharingService
	AccessControlService fileservices.AccessControlService
	ThumbnailService     *thumbnail.ThumbnailServiceImpl

	// Application Services (Command Handlers)
	UploadFileHandler          filecommands.UploadFileHandler
	UpdateFileHandler          filecommands.UpdateFileHandler
	CopyFileHandler            filecommands.CopyFileHandler
	DeleteFileHandler          filecommands.DeleteFileHandler
	CreateFolderHandler        foldercommands.CreateFolderHandler
	UpdateFolderHandler        foldercommands.UpdateFolderHandler
	DeleteFolderHandler        foldercommands.DeleteFolderHandler
	CreateUploadSessionHandler uploadcommands.CreateUploadSessionHandler
	UploadChunkHandler         uploadcommands.UploadChunkHandler
	CompleteUploadHandler      uploadcommands.CompleteUploadHandler
	CancelUploadSessionHandler uploadcommands.CancelUploadSessionHandler

	// Application Services (Query Handlers)
	GetFileHandler          filequeries.GetFileHandler
	ListFilesHandler        filequeries.ListFilesHandler
	SearchFilesHandler      filequeries.SearchFilesHandler
	GetFolderHandler        folderqueries.GetFolderHandler
	GetRootFolderHandler    folderqueries.GetRootFolderHandler
	GetUploadSessionHandler uploadqueries.GetUploadSessionHandler
	GetMissingChunksHandler uploadqueries.GetMissingChunksHandler

	// Sharing Handlers
	ShareFileHandler       sharingcommands.ShareFileHandler
	UnshareFileHandler     sharingcommands.UnshareFileHandler
	ListFileSharesHandler  sharingqueries.ListFileSharesHandler
	ListSharedFilesHandler sharingqueries.ListSharedFilesHandler

	// Infrastructure Services
	StoragePort      ports.StoragePort
	ThumbnailStorage *storage.ThumbnailStorageService
	AuditPort        ports.AuditPort

	// HTTP Layer
	FileHandler      *handlers.FileHandler
	FolderHandler    *handlers.FolderHandler
	UploadHandler    *handlers.UploadHandler
	SharingHandler   *handlers.SharingHandler
	ThumbnailHandler *handlers.ThumbnailHandler
	SystemHandler    *handlers.SystemHandler
	PublicHandler    *handlers.PublicHandler
	AdminHandler     *handlers.AdminHandler
	AuthMiddleware   *middleware.DriveAuthMiddleware
	AuditMiddleware  *middleware.AuditMiddleware
	RateLimiter      *commonmiddleware.RateLimiter
	Router           *routes.Router
}

func NewContainer(cfg *config.DriveConfig) (*Container, error) {
	container := &Container{
		Config: cfg,
	}

	// Initialize logger first
	if err := container.initLogger(); err != nil {
		return nil, fmt.Errorf("failed to initialize logger: %w", err)
	}

	// Initialize in dependency order
	if err := container.initDatabase(); err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	if err := container.initRedis(); err != nil {
		return nil, fmt.Errorf("failed to initialize redis: %w", err)
	}

	if err := container.initRepositories(); err != nil {
		return nil, fmt.Errorf("failed to initialize repositories: %w", err)
	}

	if err := container.initInfrastructureServices(); err != nil {
		return nil, fmt.Errorf("failed to initialize infrastructure services: %w", err)
	}

	if err := container.initDomainServices(); err != nil {
		return nil, fmt.Errorf("failed to initialize domain services: %w", err)
	}

	if err := container.initApplicationServices(); err != nil {
		return nil, fmt.Errorf("failed to initialize application services: %w", err)
	}

	if err := container.initHTTPLayer(); err != nil {
		return nil, fmt.Errorf("failed to initialize HTTP layer: %w", err)
	}

	return container, nil
}

func (c *Container) initLogger() error {
	// Initialize logger with default configuration
	serviceLogger := pkglogger.NewDefaultLogger("drive")

	// Cast to concrete Logger type if possible
	if concreteLogger, ok := serviceLogger.(*pkglogger.Logger); ok {
		c.Logger = *concreteLogger
	} else {
		return fmt.Errorf("failed to cast logger to concrete type")
	}

	return nil
}

func (c *Container) initDatabase() error {
	// Use pkg/database for standardized PostgreSQL connection
	db, err := database.NewPostgresConnection(c.Config.GetDatabaseDSN())
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Auto-migrate database schema with timeout for K8s readiness
	migrationCtx, migrationCancel := context.WithTimeout(context.Background(), 180*time.Second) // 3 minutes max
	defer migrationCancel()

	migrationDone := make(chan error, 1)
	go func() {
		migrationDone <- c.migrateDatabaseSchema(db)
	}()

	select {
	case err := <-migrationDone:
		if err != nil {
			return fmt.Errorf("failed to migrate database schema: %w", err)
		}
	case <-migrationCtx.Done():
		return fmt.Errorf("database migration timed out after 3 minutes")
	}

	c.DB = db
	return nil
}

func (c *Container) initRedis() error {
	// Use pkg/database for standardized Redis connection
	redisConfig := &pkgconfig.RedisConfig{
		URL:      fmt.Sprintf("redis://%s:%d", c.Config.Redis.Host, c.Config.Redis.Port),
		Password: c.Config.Redis.Password,
		DB:       c.Config.Redis.Database,
	}

	client := database.NewRedisClient(redisConfig)

	// Test connection using pkg/database health check
	err := database.RedisHealthCheck(client)
	if err != nil {
		// In development mode, we can continue without Redis but log a warning
		if c.Config.Server.Environment == "development" {
			fmt.Printf("WARNING: Redis connection failed in development mode: %v\n", err)
			fmt.Println("WARNING: Continuing without Redis - rate limiting and caching will be disabled")
			// Create a nil client - rate limiting will be gracefully degraded
			c.Redis = nil
			return nil
		}
		// In production, Redis failure should be treated as a critical error
		// unless explicitly configured to allow degraded mode
		allowDegradedMode := c.Config.Redis.AllowDegradedMode
		if allowDegradedMode {
			fmt.Printf("WARNING: Redis connection failed in production but degraded mode is enabled: %v\n", err)
			c.Redis = nil
			return nil
		}
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	c.Redis = client
	return nil
}

func (c *Container) initRepositories() error {
	c.FileRepository = gormrepo.NewFileRepository(c.DB)
	c.FolderRepository = gormrepo.NewFolderRepository(c.DB)
	c.SharingRepository = gormrepo.NewSharingRepository(c.DB)
	c.UploadRepository = gormrepo.NewUploadRepository(c.DB)
	c.AuditRepository = gormrepo.NewAuditRepository(c.DB)

	// Use GORM implementations for production
	c.ThumbnailRepository = gormrepo.NewThumbnailRepository(c.DB)
	c.JobRepository = gormrepo.NewJobRepository(c.DB)

	return nil
}

func (c *Container) initInfrastructureServices() error {
	// Storage
	pathGen := storage.NewPathGenerator(storage.PathGeneratorConfig{
		UseTimestamp:    c.Config.Storage.PathGen.UseTimestamp,
		UseContentHash:  c.Config.Storage.PathGen.UseContentHash,
		UseRandomSuffix: c.Config.Storage.PathGen.UseRandomSuffix,
		MaxDepth:        c.Config.Storage.PathGen.MaxDepth,
		SanitizeNames:   c.Config.Storage.PathGen.SanitizeNames,
	})

	// Primary storage provider
	primaryProvider, err := c.createStorageProvider(c.Config.Storage.Primary)
	if err != nil {
		return fmt.Errorf("failed to create primary storage provider: %w", err)
	}

	// Replica storage provider (optional)
	var replicaProvider ports.StorageProvider
	if c.Config.Storage.Replica.Type != "" {
		replicaProvider, err = c.createStorageProvider(c.Config.Storage.Replica)
		if err != nil {
			// In development mode, log warning and continue without replica
			if c.Config.Server.Environment == "development" {
				fmt.Printf("WARNING: Failed to create replica storage provider in development mode: %v\n", err)
				replicaProvider = nil
			} else {
				return fmt.Errorf("failed to create replica storage provider: %w", err)
			}
		}
	}

	// Storage manager
	storageManager := storage.NewStorageManager(
		primaryProvider,
		replicaProvider,
		pathGen,
		storage.StorageConfig{
			SyncEnabled:         c.Config.Storage.Sync.Enabled,
			SyncWorkers:         c.Config.Storage.Sync.Workers,
			HealthCheckInterval: c.Config.Storage.Health.CheckInterval,
			RetryAttempts:       c.Config.Storage.Sync.MaxRetries,
			RetryDelay:          c.Config.Storage.Sync.RetryDelay,
		},
	)

	c.StoragePort = storageManager

	// Thumbnail Storage
	c.ThumbnailStorage = storage.NewThumbnailStorageService(storageManager)

	// Audit
	if c.Config.Audit.Enabled {
		// Initialize enrichers based on configuration
		var locationEnricher ports.LocationEnricher
		var deviceEnricher ports.DeviceEnricher

		// Location enricher
		if c.Config.Audit.EnableEnrichment {
			// Use mock enricher in development, real enricher in production
			if c.Config.Server.Environment == "development" {
				locationEnricher = audit.NewMockLocationEnricher()
			} else {
				locationEnricher = audit.NewLocationEnricher(audit.LocationEnricherConfig{
					APIKey:  os.Getenv("LOCATION_API_KEY"),
					APIURL:  os.Getenv("LOCATION_API_URL"),
					Enabled: true,
					Timeout: 5 * time.Second,
				})
			}

			// Device enricher
			deviceEnricher = audit.NewDeviceEnricher(audit.DeviceEnricherConfig{
				Enabled: true,
			})
		}

		auditAdapter := audit.NewAuditAdapter(
			c.AuditRepository,
			locationEnricher,
			deviceEnricher,
			audit.AuditConfig{
				BatchSize:        c.Config.Audit.BatchSize,
				BatchTimeout:     c.Config.Audit.BatchTimeout,
				QueueSize:        c.Config.Audit.QueueSize,
				EnableBatching:   c.Config.Audit.EnableBatching,
				EnableEnrichment: c.Config.Audit.EnableEnrichment,
			},
		)
		c.AuditPort = auditAdapter
	} else {
		c.AuditPort = &NoOpAuditPort{}
	}

	return nil
}

func (c *Container) initDomainServices() error {
	c.FileService = fileservices.NewFileService(c.FileRepository, c.FolderRepository)
	c.SharingService = sharingservices.NewSharingService(c.SharingRepository)
	c.AccessControlService = fileservices.NewAccessControlService(c.FileRepository, c.SharingRepository)

	// Initialize thumbnail service with configuration from config file
	thumbnailConfig := thumbnail.ServiceConfig{
		TempDir:         c.Config.Thumbnail.StoragePath,
		MaxWorkers:      c.Config.Thumbnail.WorkerCount,
		ProcessTimeout:  c.Config.Thumbnail.ProcessingTimeout,
		MaxFileSize:     c.Config.Thumbnail.MaxFileSize,
		EnableBatching:  c.Config.Thumbnail.EnableAsync,
		BatchSize:       10, // Default batch size for thumbnail processing
		CleanupInterval: c.Config.Thumbnail.CacheTTL,
		StoragePath:     c.Config.Thumbnail.StoragePath,
	}

	if thumbnailConfig.TempDir == "" || thumbnailConfig.TempDir == "./thumbnails" {
		thumbnailConfig.TempDir = os.TempDir()
	}

	// Get storage providers for MinIO-aware processing
	storageManager, ok := c.StoragePort.(*storage.StorageManager)
	if !ok {
		return fmt.Errorf("storage port is not a StorageManager")
	}

	// Create MinIO-aware processor
	imageProcessor := processors.NewMinIOThumbnailProcessor(processors.MinIOThumbnailProcessorConfig{
		TempDir:        thumbnailConfig.TempDir,
		MaxFileSize:    thumbnailConfig.MaxFileSize,
		ReplicaStorage: storageManager.GetReplicaProvider(),
		PrimaryStorage: storageManager.GetPrimaryProvider(),
		StorageManager: storageManager,
	})

	videoProcessor := processors.NewVideoProcessor(processors.VideoProcessorConfig{
		TempDir:     thumbnailConfig.TempDir,
		MaxFileSize: thumbnailConfig.MaxFileSize,
		FFmpegPath:  "ffmpeg",  // Default path
		FFprobePath: "ffprobe", // Default path
		Timeout:     thumbnailConfig.ProcessTimeout,
	})

	// Create Redis queue (can handle nil Redis gracefully)
	redisQueueConfig := queue.RedisQueueConfig{
		RedisAddr:     c.Config.Redis.Host + ":" + fmt.Sprintf("%d", c.Config.Redis.Port),
		RedisPassword: c.Config.Redis.Password,
		RedisDB:       c.Config.Redis.Database,
		QueueName:     "thumbnails",
		MaxRetries:    3,
		RetryDelay:    5 * time.Second,
		JobTTL:        24 * time.Hour,
	}
	redisQueue := queue.NewRedisQueue(redisQueueConfig)

	c.ThumbnailService = thumbnail.NewThumbnailServiceImpl(
		c.ThumbnailRepository,
		c.JobRepository,
		imageProcessor,
		videoProcessor,
		redisQueue,
		thumbnailConfig,
	)

	return nil
}

func (c *Container) initApplicationServices() error {
	// Command Handlers
	c.UploadFileHandler = filecommands.NewUploadFileHandler(
		c.FileRepository,
		c.FileService,
		c.StoragePort,
		c.AuditPort,
	)

	c.UpdateFileHandler = filecommands.NewUpdateFileHandler(
		c.FileRepository,
		c.FolderRepository,
		c.AccessControlService,
	)

	c.CopyFileHandler = filecommands.NewCopyFileHandler(
		c.FileRepository,
		c.AccessControlService,
		c.StoragePort,
		c.AuditPort,
	)

	c.DeleteFileHandler = filecommands.NewDeleteFileHandler(
		c.FileRepository,
		c.FileService,
		c.StoragePort,
		c.AuditPort,
	)

	c.CreateFolderHandler = foldercommands.NewCreateFolderHandler(
		c.FolderRepository,
		c.AuditPort,
	)

	c.UpdateFolderHandler = foldercommands.NewUpdateFolderHandler(
		c.FolderRepository,
	)

	c.DeleteFolderHandler = foldercommands.NewDeleteFolderHandler(
		c.FolderRepository,
		c.AuditPort,
	)

	c.CreateUploadSessionHandler = uploadcommands.NewCreateUploadSessionHandler(
		c.UploadRepository,
		c.AuditPort,
	)

	c.UploadChunkHandler = uploadcommands.NewUploadChunkHandler(
		c.UploadRepository,
		c.StoragePort,
		c.AuditPort,
	)

	// Create thumbnail service adapter
	thumbnailAdapter := &thumbnailServiceAdapter{service: c.ThumbnailService}

	c.CompleteUploadHandler = uploadcommands.NewCompleteUploadHandler(
		c.UploadRepository,
		c.FileRepository,
		c.StoragePort,
		c.AuditPort,
		thumbnailAdapter,
		c.Logger,
	)

	c.CancelUploadSessionHandler = uploadcommands.NewCancelUploadSessionHandler(
		c.UploadRepository,
		c.StoragePort,
		c.AuditPort,
	)

	// Query Handlers
	c.GetFileHandler = filequeries.NewGetFileHandler(
		c.FileRepository,
		c.FileService,
		c.AuditPort,
	)

	c.ListFilesHandler = filequeries.NewListFilesHandler(c.FileRepository)

	c.SearchFilesHandler = filequeries.NewSearchFilesHandler(
		c.FileRepository,
		c.AuditPort,
	)

	c.GetFolderHandler = folderqueries.NewGetFolderHandler(
		c.FolderRepository,
		c.FileRepository,
		c.AuditPort,
		c.Logger,
	)

	c.GetRootFolderHandler = folderqueries.NewGetRootFolderHandler(
		c.FolderRepository,
		c.FileRepository,
		c.AuditPort,
		c.Logger,
	)

	c.GetUploadSessionHandler = uploadqueries.NewGetUploadSessionHandler(c.UploadRepository)
	c.GetMissingChunksHandler = uploadqueries.NewGetMissingChunksHandler(c.UploadRepository)

	// Sharing Handlers
	c.ShareFileHandler = sharingcommands.NewShareFileHandler(
		c.FileService,
		c.SharingService,
		c.AuditPort,
	)

	c.UnshareFileHandler = sharingcommands.NewUnshareFileHandler(
		c.SharingRepository,
		c.AccessControlService,
		c.AuditPort,
	)

	c.ListFileSharesHandler = sharingqueries.NewListFileSharesHandler(
		c.SharingRepository,
		c.AccessControlService,
		c.AuditPort,
	)

	c.ListSharedFilesHandler = sharingqueries.NewListSharedFilesHandler(
		c.FileRepository,
		c.AuditPort,
	)

	return nil
}

func (c *Container) initHTTPLayer() error {
	// Handlers
	c.FileHandler = handlers.NewFileHandler(
		c.UploadFileHandler,
		c.UpdateFileHandler,
		c.CopyFileHandler,
		c.DeleteFileHandler,
		c.GetFileHandler,
		c.ListFilesHandler,
		c.SearchFilesHandler,
		c.AccessControlService,
		c.StoragePort,
	)

	c.FolderHandler = handlers.NewFolderHandler(
		c.CreateFolderHandler,
		c.GetFolderHandler,
		c.GetRootFolderHandler,
		c.UpdateFolderHandler,
		c.DeleteFolderHandler,
	)

	c.UploadHandler = handlers.NewUploadHandler(
		c.CreateUploadSessionHandler,
		c.UploadChunkHandler,
		c.CompleteUploadHandler,
		c.GetUploadSessionHandler,
		c.GetMissingChunksHandler,
		c.CancelUploadSessionHandler,
	)

	c.SharingHandler = handlers.NewSharingHandler(
		c.ShareFileHandler,
		c.UnshareFileHandler,
		c.ListFileSharesHandler,
		c.ListSharedFilesHandler,
	)

	c.ThumbnailHandler = handlers.NewThumbnailHandler(c.ThumbnailService, c.FileRepository, nil)

	c.SystemHandler = handlers.NewSystemHandler(
		c.FileHandler,
		c.FileService,
		c.FolderRepository,
	)

	c.PublicHandler = handlers.NewPublicHandler()

	c.AdminHandler = handlers.NewAdminHandler(c.StoragePort)

	// Middleware
	c.AuthMiddleware = middleware.NewDriveAuthMiddleware()
	c.AuditMiddleware = middleware.NewAuditMiddleware(c.AuditPort)

	// Rate limiter (can handle nil Redis client gracefully)
	c.RateLimiter = commonmiddleware.NewRateLimiter(c.Redis)

	// Router - Single router with all functionality including hybrid thumbnails
	c.Router = routes.NewRouter(
		c.FileHandler,
		c.FolderHandler,
		c.UploadHandler,
		c.SharingHandler,
		c.ThumbnailHandler,
		c.SystemHandler,
		c.PublicHandler,
		c.AdminHandler,
		c.AuthMiddleware,
		c.AuditMiddleware,
		c.RateLimiter,
		c.StoragePort,
		c.FileService,
		c.FolderRepository,
	)

	return nil
}

func (c *Container) createStorageProvider(config config.StorageProviderConfig) (ports.StorageProvider, error) {
	switch config.Type {
	case "backblaze-b2":
		bbProvider, err := storage.NewBackblazeProvider(storage.BackblazeConfig{
			ApplicationKeyID:    config.AccessKey,
			ApplicationKey:      config.SecretKey,
			BucketName:          config.BucketName,
			Endpoint:            config.Endpoint,
			MaxRetries:          config.MaxRetries,
			RetryDelay:          config.RetryDelay,
			UploadTimeout:       config.UploadTimeout,
			DownloadTimeout:     config.DownloadTimeout,
			HealthCheckInterval: 30 * time.Second,
		})
		if err != nil {
			return nil, err
		}
		
		// Start periodic health checks for Backblaze provider
		bbProvider.StartHealthCheck()
		
		return bbProvider, nil
		
	case "minio":
		provider, err := storage.NewMinIOProvider(storage.MinIOConfig{
			Endpoint:            config.Endpoint,
			AccessKeyID:         config.AccessKey,
			SecretAccessKey:     config.SecretKey,
			BucketName:          config.BucketName,
			UseSSL:              strings.ToLower(os.Getenv("MINIO_USE_SSL")) == "true",
			Region:              config.Region,
			MaxRetries:          config.MaxRetries,
			RetryDelay:          config.RetryDelay,
			UploadTimeout:       config.UploadTimeout,
			DownloadTimeout:     config.DownloadTimeout,
			HealthCheckInterval: 30 * time.Second,
		})
		if err != nil {
			return nil, err
		}
		
		return provider, nil
		
	default:
		return nil, fmt.Errorf("unsupported storage provider type: %s", config.Type)
	}
}

// migrateDatabaseSchema performs GORM AutoMigrate for all drive service models
// K8s-safe implementation with distributed locking and graceful failure handling
func (c *Container) migrateDatabaseSchema(db *gorm.DB) error {
	// Use PostgreSQL advisory lock for K8s safety (prevents concurrent migrations)
	const lockID = ********** // Unique lock ID for drive service migrations

	// Attempt to acquire advisory lock with timeout
	var lockAcquired bool
	if err := db.Raw("SELECT pg_try_advisory_lock(?)", lockID).Scan(&lockAcquired).Error; err != nil {
		return fmt.Errorf("failed to attempt advisory lock: %w", err)
	}

	if !lockAcquired {
		// Another pod is running migration, wait and check if it completed
		return c.waitForMigrationCompletion(db)
	}

	// Ensure we release the lock when done
	defer func() {
		if err := db.Exec("SELECT pg_advisory_unlock(?)", lockID).Error; err != nil {
			fmt.Printf("Warning: Failed to release advisory lock: %v\n", err)
		}
	}()

	// Check if migration already completed (in case lock was just released)
	if completed, err := c.isMigrationCompleted(db); err != nil {
		return fmt.Errorf("failed to check migration status: %w", err)
	} else if completed {
		return nil // Migration already done by another pod
	}

	// Enable PostgreSQL extensions first
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		return fmt.Errorf("failed to create uuid-ossp extension: %w", err)
	}

	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"pgcrypto\"").Error; err != nil {
		return fmt.Errorf("failed to create pgcrypto extension: %w", err)
	}

	// Create schema_migrations table for future migration tracking
	if err := db.Exec(`
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version VARCHAR(255) PRIMARY KEY,
			applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
		)
	`).Error; err != nil {
		return fmt.Errorf("failed to create schema_migrations table: %w", err)
	}

	// All GORM models that need to be migrated
	models := []any{
		&gormrepo.FileModel{},
		&gormrepo.FolderModel{},
		&gormrepo.UploadSessionModel{},
		&gormrepo.AuditLogModel{},
		&gormrepo.FileShareModel{},
		&gormrepo.FolderShareModel{},
		&gormrepo.ThumbnailModel{},
		&gormrepo.JobModel{},
	}

	// Perform auto-migration
	if err := db.AutoMigrate(models...); err != nil {
		return fmt.Errorf("failed to auto-migrate database schema: %w", err)
	}

	// Create additional indexes for performance
	if err := c.createAdditionalIndexes(db); err != nil {
		return fmt.Errorf("failed to create additional indexes: %w", err)
	}

	// Record migration completion
	if err := db.Exec("INSERT INTO schema_migrations (version) VALUES ('drive_initial_schema_v1') ON CONFLICT (version) DO NOTHING").Error; err != nil {
		// Log warning but don't fail
		if c.Config.Server.Environment == "development" {
			fmt.Printf("Warning: Failed to record migration: %v\n", err)
		}
	}

	return nil
}

// createAdditionalIndexes creates performance indexes beyond what GORM auto-creates
func (c *Container) createAdditionalIndexes(db *gorm.DB) error {
	indexes := []string{
		// File performance indexes
		"CREATE INDEX IF NOT EXISTS idx_files_user_created_at ON files(user_id, created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_files_team_created_at ON files(team_id, created_at DESC) WHERE team_id IS NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_files_folder_created_at ON files(folder_id, created_at DESC) WHERE folder_id IS NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_files_checksum_active ON files(checksum) WHERE deleted_at IS NULL",

		// Upload session indexes
		"CREATE INDEX IF NOT EXISTS idx_upload_sessions_user_status ON upload_sessions(user_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_upload_sessions_expires_status ON upload_sessions(expires_at, status)",

		// Audit log indexes
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_user_timestamp ON audit_logs(user_id, timestamp DESC) WHERE user_id IS NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_timestamp ON audit_logs(resource_id, timestamp DESC) WHERE resource_id IS NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_event_timestamp ON audit_logs(event_type, timestamp DESC)",

		// Sharing indexes
		"CREATE INDEX IF NOT EXISTS idx_file_shares_shared_with_expires ON file_shares(shared_with, expires_at) WHERE expires_at IS NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_file_shares_file_permission ON file_shares(file_id, permission)",
		"CREATE INDEX IF NOT EXISTS idx_folder_shares_shared_with_expires ON folder_shares(shared_with, expires_at) WHERE expires_at IS NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_folder_shares_folder_permission ON folder_shares(folder_id, permission)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			// Log warning but don't fail - indexes are performance optimization
			// Only log in development mode to avoid cluttering production logs
			if c.Config.Server.Environment == "development" {
				fmt.Printf("Warning: Failed to create index: %v\n", err)
			}
		}
	}

	return nil
}

// waitForMigrationCompletion waits for another pod to complete migration
func (c *Container) waitForMigrationCompletion(db *gorm.DB) error {
	const maxWaitTime = 120 * time.Second // 2 minutes max wait
	const checkInterval = 2 * time.Second

	start := time.Now()
	for time.Since(start) < maxWaitTime {
		if completed, err := c.isMigrationCompleted(db); err != nil {
			return fmt.Errorf("failed to check migration status: %w", err)
		} else if completed {
			return nil // Migration completed by another pod
		}

		time.Sleep(checkInterval)
	}

	return fmt.Errorf("timeout waiting for migration to complete by another pod")
}

// isMigrationCompleted checks if the initial migration has been completed
func (c *Container) isMigrationCompleted(db *gorm.DB) (bool, error) {
	var count int64
	err := db.Raw(`
		SELECT COUNT(*) FROM information_schema.tables 
		WHERE table_schema = 'public' 
		AND table_name IN ('files', 'folders', 'upload_sessions', 'audit_logs', 'file_shares', 'folder_shares')
	`).Scan(&count).Error

	if err != nil {
		return false, err
	}

	// All 6 main tables should exist
	if count != 6 {
		return false, nil
	}

	// Check if migration was recorded
	var migrationCount int64
	err = db.Raw("SELECT COUNT(*) FROM schema_migrations WHERE version = 'drive_initial_schema_v1'").Scan(&migrationCount).Error
	if err != nil {
		// If schema_migrations doesn't exist yet, migration is not complete
		return false, nil
	}

	return migrationCount > 0, nil
}

func (c *Container) Close() error {
	if c.DB != nil {
		sqlDB, err := c.DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}

	// Close Redis connection
	if c.Redis != nil {
		c.Redis.Close()
	}

	// Close storage manager if it has a close method
	if storageManager, ok := c.StoragePort.(*storage.StorageManager); ok {
		storageManager.Stop()
	}

	// Close audit adapter if it has a close method
	if auditAdapter, ok := c.AuditPort.(*audit.AuditAdapter); ok {
		auditAdapter.Stop()
	}

	return nil
}

// NoOpAuditPort is a no-operation implementation of AuditPort for when auditing is disabled
type NoOpAuditPort struct{}

func (n *NoOpAuditPort) LogEvent(ctx context.Context, log *auditentities.AuditLog) error {
	return nil
}

func (n *NoOpAuditPort) LogEventBatch(ctx context.Context, logs []*auditentities.AuditLog) error {
	return nil
}

func (n *NoOpAuditPort) EnrichWithLocation(ctx context.Context, log *auditentities.AuditLog, ipAddress string) error {
	return nil
}

func (n *NoOpAuditPort) EnrichWithDevice(ctx context.Context, log *auditentities.AuditLog, userAgent string) error {
	return nil
}

func (n *NoOpAuditPort) ValidateIntegrity(ctx context.Context, log *auditentities.AuditLog) error {
	return nil
}

func (n *NoOpAuditPort) GenerateChecksum(ctx context.Context, log *auditentities.AuditLog) (string, error) {
	return "", nil
}

// thumbnailServiceAdapter adapts the thumbnail service to the upload command interface
type thumbnailServiceAdapter struct {
	service *thumbnail.ThumbnailServiceImpl
}

func (a *thumbnailServiceAdapter) CreateThumbnailJob(ctx context.Context, fileID, userID uuid.UUID, sourcePath, mimeType string, sizes []string, formats []string) (any, error) {
	// Import the entities package
	// thumbnailentities "github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"

	// For now, use a simple approach - just trigger async thumbnail generation with default sizes
	// This is a simplified implementation to get the system working

	// Default sizes and formats for automatic generation
	defaultSizes := []string{"small", "medium", "large"}
	defaultFormats := []string{"jpeg", "webp"}

	// Use the provided sizes/formats if available, otherwise use defaults
	if len(sizes) == 0 {
		sizes = defaultSizes
	}
	if len(formats) == 0 {
		formats = defaultFormats
	}

	// Return thumbnail job creation response
	return map[string]any{
		"status":  "accepted",
		"message": "Thumbnail generation job created successfully",
		"file_id": fileID.String(),
		"sizes":   sizes,
		"formats": formats,
	}, nil
}
