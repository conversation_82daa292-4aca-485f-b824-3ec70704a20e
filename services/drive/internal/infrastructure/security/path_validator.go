package security

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
	"unicode/utf8"
)

// PathTraversalValidator provides enhanced path traversal protection
type PathTraversalValidator struct {
	maxDepth       int
	maxLength      int
	allowedChars   *regexp.Regexp
	forbiddenPaths []string
	forbiddenNames []string
}

// NewPathTraversalValidator creates a new path validator with secure defaults
func NewPathTraversalValidator() *PathTraversalValidator {
	return &PathTraversalValidator{
		maxDepth:  10,
		maxLength: 1024,
		// Allow only safe characters: alphanumeric, underscore, hyphen, dot, forward slash
		allowedChars: regexp.MustCompile(`^[a-zA-Z0-9._/-]+$`),
		forbiddenPaths: []string{
			"..",
			"./",
			"../",
			"/..",
			"/../",
			"\\",
			"//",
			"..\\",
			".\\.\\",
			"..\\..\\",
		},
		forbiddenNames: []string{
			"con", "prn", "aux", "nul",
			"com1", "com2", "com3", "com4", "com5", "com6", "com7", "com8", "com9",
			"lpt1", "lpt2", "lpt3", "lpt4", "lpt5", "lpt6", "lpt7", "lpt8", "lpt9",
		},
	}
}

// ValidateStoragePath performs comprehensive validation of storage paths
func (v *PathTraversalValidator) ValidateStoragePath(inputPath string) error {
	if inputPath == "" {
		return fmt.Errorf("path cannot be empty")
	}

	// Length check
	if len(inputPath) > v.maxLength {
		return fmt.Errorf("path too long (max %d characters)", v.maxLength)
	}

	// UTF-8 validation
	if !utf8.ValidString(inputPath) {
		return fmt.Errorf("path contains invalid UTF-8 characters")
	}

	// Control character check
	for _, r := range inputPath {
		if r < 32 || r == 127 {
			return fmt.Errorf("path contains control characters")
		}
	}

	// Normalize path separators and clean
	normalizedPath := filepath.ToSlash(inputPath)
	cleanPath := filepath.Clean(normalizedPath)

	// Check for path traversal patterns
	if err := v.checkTraversalPatterns(normalizedPath); err != nil {
		return err
	}

	// Verify clean path doesn't escape
	if strings.Contains(cleanPath, "..") {
		return fmt.Errorf("path contains directory traversal")
	}

	// Check depth
	if err := v.checkDepth(cleanPath); err != nil {
		return err
	}

	// Check for forbidden names
	if err := v.checkForbiddenNames(cleanPath); err != nil {
		return err
	}

	// Additional security checks
	if err := v.performAdditionalChecks(normalizedPath); err != nil {
		return err
	}

	return nil
}

// ValidateFileName validates file names with enhanced security
func (v *PathTraversalValidator) ValidateFileName(fileName string) error {
	if fileName == "" {
		return fmt.Errorf("file name cannot be empty")
	}

	if len(fileName) > 255 {
		return fmt.Errorf("file name too long (max 255 characters)")
	}

	// UTF-8 validation
	if !utf8.ValidString(fileName) {
		return fmt.Errorf("file name contains invalid UTF-8 characters")
	}

	// Check for path separators
	if strings.ContainsAny(fileName, "/\\") {
		return fmt.Errorf("file name cannot contain path separators")
	}

	// Check for dangerous characters
	dangerousChars := []string{"<", ">", ":", "\"", "|", "?", "*", "\x00"}
	for _, char := range dangerousChars {
		if strings.Contains(fileName, char) {
			return fmt.Errorf("file name contains forbidden character: %s", char)
		}
	}

	// Check for control characters
	for _, r := range fileName {
		if r < 32 || r == 127 {
			return fmt.Errorf("file name contains control characters")
		}
	}

	// Check for reserved names
	baseName := strings.ToLower(fileName)
	if strings.Contains(baseName, ".") {
		baseName = strings.Split(baseName, ".")[0]
	}

	for _, forbidden := range v.forbiddenNames {
		if baseName == forbidden {
			return fmt.Errorf("file name uses reserved system name: %s", forbidden)
		}
	}

	// Check for hidden files (starting with dot)
	if strings.HasPrefix(fileName, ".") {
		return fmt.Errorf("hidden files not allowed")
	}

	// Check for ending with dots or spaces (Windows issue)
	trimmed := strings.TrimRight(fileName, ". ")
	if len(trimmed) != len(fileName) {
		return fmt.Errorf("file name cannot end with dots or spaces")
	}

	return nil
}

// SanitizeStoragePath safely sanitizes storage paths
func (v *PathTraversalValidator) SanitizeStoragePath(inputPath string) (string, error) {
	if inputPath == "" {
		return "", fmt.Errorf("path cannot be empty")
	}

	// Normalize and clean
	normalizedPath := filepath.ToSlash(inputPath)
	cleanPath := filepath.Clean(normalizedPath)

	// Remove any remaining dangerous patterns
	sanitized := v.removeDangerousPatterns(cleanPath)

	// Validate the sanitized path
	if err := v.ValidateStoragePath(sanitized); err != nil {
		return "", fmt.Errorf("path sanitization failed: %w", err)
	}

	return sanitized, nil
}

// checkTraversalPatterns checks for common path traversal patterns
func (v *PathTraversalValidator) checkTraversalPatterns(path string) error {
	lowerPath := strings.ToLower(path)

	for _, pattern := range v.forbiddenPaths {
		if strings.Contains(lowerPath, strings.ToLower(pattern)) {
			return fmt.Errorf("path contains forbidden pattern: %s", pattern)
		}
	}

	// Additional pattern checks
	if strings.Contains(path, "..") {
		return fmt.Errorf("path contains directory traversal sequence")
	}

	return nil
}

// checkDepth validates path depth
func (v *PathTraversalValidator) checkDepth(path string) error {
	// Remove leading slash for counting
	cleanPath := strings.TrimPrefix(path, "/")
	if cleanPath == "" {
		return nil
	}

	parts := strings.Split(cleanPath, "/")
	if len(parts) > v.maxDepth {
		return fmt.Errorf("path depth exceeds maximum (%d levels)", v.maxDepth)
	}

	return nil
}

// checkForbiddenNames validates against forbidden file/directory names
func (v *PathTraversalValidator) checkForbiddenNames(path string) error {
	parts := strings.Split(path, "/")

	for _, part := range parts {
		if part == "" {
			continue
		}

		// Check base name without extension
		baseName := strings.ToLower(part)
		if strings.Contains(baseName, ".") {
			baseName = strings.Split(baseName, ".")[0]
		}

		for _, forbidden := range v.forbiddenNames {
			if baseName == forbidden {
				return fmt.Errorf("path contains forbidden name: %s", forbidden)
			}
		}
	}

	return nil
}

// performAdditionalChecks performs additional security validations
func (v *PathTraversalValidator) performAdditionalChecks(path string) error {
	// Check for null bytes
	if strings.Contains(path, "\x00") {
		return fmt.Errorf("path contains null bytes")
	}

	// Check for excessive dots
	if strings.Contains(path, "...") {
		return fmt.Errorf("path contains excessive dots")
	}

	// Check for mixed separators
	if strings.Contains(path, "\\") && strings.Contains(path, "/") {
		return fmt.Errorf("path contains mixed separators")
	}

	// Check for leading/trailing spaces in path components
	parts := strings.Split(path, "/")
	for _, part := range parts {
		if part != strings.TrimSpace(part) {
			return fmt.Errorf("path component has leading/trailing spaces")
		}
	}

	return nil
}

// removeDangerousPatterns removes dangerous patterns from paths
func (v *PathTraversalValidator) removeDangerousPatterns(path string) string {
	// Remove null bytes
	sanitized := strings.ReplaceAll(path, "\x00", "")

	// Remove excessive dots
	sanitized = regexp.MustCompile(`\.{3,}`).ReplaceAllString(sanitized, ".")

	// Remove dangerous character sequences
	dangerousPatterns := []string{
		"../", "..\\", "./", ".\\",
		"//", "\\\\",
	}

	for _, pattern := range dangerousPatterns {
		sanitized = strings.ReplaceAll(sanitized, pattern, "")
	}

	// Clean again after removals
	sanitized = filepath.Clean(sanitized)

	return sanitized
}

// ValidateUUID validates UUID format
func (v *PathTraversalValidator) ValidateUUID(id string) error {
	if id == "" {
		return fmt.Errorf("UUID cannot be empty")
	}

	if len(id) != 36 {
		return fmt.Errorf("UUID must be exactly 36 characters")
	}

	// Check UUID format: 8-4-4-4-12
	pattern := regexp.MustCompile(`^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$`)
	if !pattern.MatchString(id) {
		return fmt.Errorf("invalid UUID format")
	}

	return nil
}
