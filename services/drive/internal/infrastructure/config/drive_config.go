package config

import (
	"fmt"
	"strings"
	"time"

	baseconfig "github.com/swork-team/platform/pkg/config"
)

// DriveConfig contains all configuration for the drive service
type DriveConfig struct {
	Server     ServerConfig     `json:"server"`
	Database   DatabaseConfig   `json:"database"`
	Redis      RedisConfig      `json:"redis"`
	Security   SecurityConfig   `json:"security"`
	Storage    StorageConfig    `json:"storage"`
	Audit      AuditConfig      `json:"audit"`
	Drive      DriveSettings    `json:"drive"`
	Thumbnail  ThumbnailConfig  `json:"thumbnail"`
	Monitoring MonitoringConfig `json:"monitoring"`
}

// ServerConfig defines server configuration
type ServerConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	ReadTimeout  time.Duration `json:"readTimeout"`
	WriteTimeout time.Duration `json:"writeTimeout"`
	IdleTimeout  time.Duration `json:"idleTimeout"`
	Environment  string        `json:"environment"`
	Debug        bool          `json:"debug"`
}

// DatabaseConfig defines database configuration
type DatabaseConfig struct {
	Host            string        `json:"host"`
	Port            int           `json:"port"`
	Username        string        `json:"username"`
	Password        string        `json:"password"`
	Database        string        `json:"database"`
	SSLMode         string        `json:"sslMode"`
	MaxOpenConns    int           `json:"maxOpenConns"`
	MaxIdleConns    int           `json:"maxIdleConns"`
	ConnMaxLifetime time.Duration `json:"connMaxLifetime"`
	ConnMaxIdleTime time.Duration `json:"connMaxIdleTime"`
}

// RedisConfig defines Redis configuration
type RedisConfig struct {
	Host              string `json:"host"`
	Port              int    `json:"port"`
	Password          string `json:"password"`
	Database          int    `json:"database"`
	PoolSize          int    `json:"poolSize"`
	AllowDegradedMode bool   `json:"allowDegradedMode"`
}

// SecurityConfig defines security configuration
type SecurityConfig struct {
	JWTSecret          string   `json:"jwtSecret"`
	JWTExpirationHours int      `json:"jwtExpirationHours"`
	AllowedOrigins     []string `json:"allowedOrigins"`
	RateLimitEnabled   bool     `json:"rateLimitEnabled"`
	RateLimitRPS       int      `json:"rateLimitRPS"`
	RateLimitBurst     int      `json:"rateLimitBurst"`
}

// StorageConfig defines storage provider configuration
type StorageConfig struct {
	Primary StorageProviderConfig `json:"primary"`
	Replica StorageProviderConfig `json:"replica"`
	PathGen PathGeneratorConfig   `json:"pathGen"`
	Sync    SyncConfig            `json:"sync"`
	Health  HealthConfig          `json:"health"`
}

type StorageProviderConfig struct {
	Type            string        `json:"type"`
	Endpoint        string        `json:"endpoint"`
	AccessKey       string        `json:"accessKey"`
	SecretKey       string        `json:"secretKey"`
	BucketName      string        `json:"bucketName"`
	Region          string        `json:"region"`
	MaxRetries      int           `json:"maxRetries"`
	RetryDelay      time.Duration `json:"retryDelay"`
	UploadTimeout   time.Duration `json:"uploadTimeout"`
	DownloadTimeout time.Duration `json:"downloadTimeout"`
}

type PathGeneratorConfig struct {
	UseTimestamp    bool `json:"useTimestamp"`
	UseContentHash  bool `json:"useContentHash"`
	UseRandomSuffix bool `json:"useRandomSuffix"`
	MaxDepth        int  `json:"maxDepth"`
	SanitizeNames   bool `json:"sanitizeNames"`
}

type SyncConfig struct {
	Enabled    bool          `json:"enabled"`
	Workers    int           `json:"workers"`
	QueueSize  int           `json:"queueSize"`
	RetryDelay time.Duration `json:"retryDelay"`
	MaxRetries int           `json:"maxRetries"`
}

type HealthConfig struct {
	CheckInterval time.Duration `json:"checkInterval"`
	Timeout       time.Duration `json:"timeout"`
}

type AuditConfig struct {
	Enabled          bool          `json:"enabled"`
	BatchSize        int           `json:"batchSize"`
	BatchTimeout     time.Duration `json:"batchTimeout"`
	QueueSize        int           `json:"queueSize"`
	EnableBatching   bool          `json:"enableBatching"`
	EnableEnrichment bool          `json:"enableEnrichment"`
	RetentionDays    int           `json:"retentionDays"`
}

type DriveSettings struct {
	MaxFileSize       int64         `json:"maxFileSize"`
	MaxChunkSize      int64         `json:"maxChunkSize"`
	SessionExpiry     time.Duration `json:"sessionExpiry"`
	CleanupInterval   time.Duration `json:"cleanupInterval"`
	AllowedExtensions []string      `json:"allowedExtensions"`
	MaxFilesPerUser   int           `json:"maxFilesPerUser"`
	MaxStoragePerUser int64         `json:"maxStoragePerUser"`
}

type ThumbnailConfig struct {
	StoragePath       string        `json:"storagePath"`
	MaxFileSize       int64         `json:"maxFileSize"`
	Quality           int           `json:"quality"`
	EnableAsync       bool          `json:"enableAsync"`
	WorkerCount       int           `json:"workerCount"`
	ProcessingTimeout time.Duration `json:"processingTimeout"`
	CacheEnabled      bool          `json:"cacheEnabled"`
	CacheTTL          time.Duration `json:"cacheTTL"`
}

type MonitoringConfig struct {
	Enabled         bool   `json:"enabled"`
	MetricsPort     int    `json:"metricsPort"`
	HealthCheckPort int    `json:"healthCheckPort"`
	LogLevel        string `json:"logLevel"`
	EnableTracing   bool   `json:"enableTracing"`
	TracingEndpoint string `json:"tracingEndpoint"`
}

// LoadDriveConfig loads configuration using pkg/config helpers
func LoadDriveConfig() (*DriveConfig, error) {
	config := &DriveConfig{
		Server:     loadServerConfig(),
		Database:   loadDatabaseConfig(),
		Redis:      loadRedisConfig(),
		Security:   loadSecurityConfig(),
		Storage:    loadStorageConfig(),
		Audit:      loadAuditConfig(),
		Drive:      loadDriveSettings(),
		Thumbnail:  loadThumbnailConfig(),
		Monitoring: loadMonitoringConfig(),
	}

	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return config, nil
}

func loadServerConfig() ServerConfig {
	return ServerConfig{
		Host:         baseconfig.GetEnv("SERVER_HOST", baseconfig.GetEnv("HOST", "0.0.0.0")),
		Port:         baseconfig.GetIntEnv("SERVER_PORT", baseconfig.GetIntEnv("PORT", 8080)),
		ReadTimeout:  baseconfig.GetDurationEnv("READ_TIMEOUT", baseconfig.GetDurationEnv("SERVER_READ_TIMEOUT", 30*time.Second)),
		WriteTimeout: baseconfig.GetDurationEnv("WRITE_TIMEOUT", baseconfig.GetDurationEnv("SERVER_WRITE_TIMEOUT", 30*time.Second)),
		IdleTimeout:  baseconfig.GetDurationEnv("SERVER_IDLE_TIMEOUT", 60*time.Second),
		Environment:  baseconfig.GetEnv("APP_ENV", "development"),
		Debug:        baseconfig.GetBoolEnv("DEBUG", false),
	}
}

func loadDatabaseConfig() DatabaseConfig {
	return DatabaseConfig{
		Host:            baseconfig.GetEnv("DB_HOST", "localhost"),
		Port:            baseconfig.GetIntEnv("DB_PORT", 5432),
		Username:        baseconfig.GetEnv("DB_USERNAME", baseconfig.GetEnv("POSTGRES_USER", "postgres")),
		Password:        baseconfig.GetEnv("DB_PASSWORD", baseconfig.GetEnv("POSTGRES_PASSWORD", "")),
		Database:        baseconfig.GetEnv("DRIVE_DB_DATABASE", baseconfig.GetEnv("POSTGRES_DB", "swork_drive")),
		SSLMode:         baseconfig.GetEnv("DB_SSL_MODE", "disable"),
		MaxOpenConns:    baseconfig.GetIntEnv("DB_MAX_OPEN_CONNS", 25),
		MaxIdleConns:    baseconfig.GetIntEnv("DB_MAX_IDLE_CONNS", 25),
		ConnMaxLifetime: baseconfig.GetDurationEnv("DB_CONN_MAX_LIFETIME", 5*time.Minute),
		ConnMaxIdleTime: baseconfig.GetDurationEnv("DB_CONN_MAX_IDLE_TIME", 5*time.Minute),
	}
}

func loadRedisConfig() RedisConfig {
	return RedisConfig{
		Host:              baseconfig.GetEnv("REDIS_HOST", "localhost"),
		Port:              baseconfig.GetIntEnv("REDIS_PORT", 6379),
		Password:          baseconfig.GetEnv("REDIS_PASSWORD", ""),
		Database:          baseconfig.GetIntEnv("REDIS_DB", 0),
		PoolSize:          baseconfig.GetIntEnv("REDIS_POOL_SIZE", 10),
		AllowDegradedMode: baseconfig.GetBoolEnv("REDIS_ALLOW_DEGRADED_MODE", false),
	}
}

func loadSecurityConfig() SecurityConfig {
	return SecurityConfig{
		JWTSecret:          baseconfig.GetEnv("JWT_SECRET", ""),
		JWTExpirationHours: baseconfig.GetIntEnv("JWT_EXPIRATION_HOURS", 24),
		AllowedOrigins:     parseAllowedOrigins(),
		RateLimitEnabled:   baseconfig.GetBoolEnv("RATE_LIMIT_ENABLED", true),
		RateLimitRPS:       baseconfig.GetIntEnv("RATE_LIMIT_RPS", 100),
		RateLimitBurst:     baseconfig.GetIntEnv("RATE_LIMIT_BURST", 200),
	}
}

func loadStorageConfig() StorageConfig {
	return StorageConfig{
		Primary: StorageProviderConfig{
			Type:            baseconfig.GetEnv("STORAGE_PRIMARY_TYPE", "minio"),
			Endpoint:        baseconfig.GetEnv("MINIO_ENDPOINT", ""),
			AccessKey:       baseconfig.GetEnv("MINIO_ACCESS_KEY", ""),
			SecretKey:       baseconfig.GetEnv("MINIO_SECRET_KEY", ""),
			BucketName:      baseconfig.GetEnv("STORAGE_PRIMARY_BUCKET", baseconfig.GetEnv("STORAGE_REPLICA_BUCKET", "")),
			Region:          baseconfig.GetEnv("STORAGE_PRIMARY_REGION", "us-east-1"),
			MaxRetries:      baseconfig.GetIntEnv("STORAGE_PRIMARY_MAX_RETRIES", 3),
			RetryDelay:      baseconfig.GetDurationEnv("STORAGE_PRIMARY_RETRY_DELAY", 1*time.Second),
			UploadTimeout:   baseconfig.GetDurationEnv("STORAGE_PRIMARY_UPLOAD_TIMEOUT", 5*time.Minute),
			DownloadTimeout: baseconfig.GetDurationEnv("STORAGE_PRIMARY_DOWNLOAD_TIMEOUT", 2*time.Minute),
		},
		Replica: StorageProviderConfig{
			Type:            baseconfig.GetEnv("STORAGE_REPLICA_TYPE", "backblaze-b2"),
			Endpoint:        baseconfig.GetEnv("STORAGE_REPLICA_ENDPOINT", ""),
			AccessKey:       baseconfig.GetEnv("STORAGE_REPLICA_ACCESS_KEY", baseconfig.GetEnv("B2_APPLICATION_KEY_ID", "")),
			SecretKey:       baseconfig.GetEnv("STORAGE_REPLICA_SECRET_KEY", baseconfig.GetEnv("B2_APPLICATION_KEY", "")),
			BucketName:      baseconfig.GetEnv("STORAGE_REPLICA_BUCKET", baseconfig.GetEnv("STORAGE_PRIMARY_BUCKET", "")),
			Region:          baseconfig.GetEnv("STORAGE_REPLICA_REGION", "us-east-1"),
			MaxRetries:      baseconfig.GetIntEnv("STORAGE_REPLICA_MAX_RETRIES", 3),
			RetryDelay:      baseconfig.GetDurationEnv("STORAGE_REPLICA_RETRY_DELAY", 1*time.Second),
			UploadTimeout:   baseconfig.GetDurationEnv("STORAGE_REPLICA_UPLOAD_TIMEOUT", 5*time.Minute),
			DownloadTimeout: baseconfig.GetDurationEnv("STORAGE_REPLICA_DOWNLOAD_TIMEOUT", 2*time.Minute),
		},
		PathGen: PathGeneratorConfig{
			UseTimestamp:    baseconfig.GetBoolEnv("STORAGE_PATH_USE_TIMESTAMP", true),
			UseContentHash:  baseconfig.GetBoolEnv("STORAGE_PATH_USE_CONTENT_HASH", true),
			UseRandomSuffix: baseconfig.GetBoolEnv("STORAGE_PATH_USE_RANDOM_SUFFIX", false),
			MaxDepth:        baseconfig.GetIntEnv("STORAGE_PATH_MAX_DEPTH", 3),
			SanitizeNames:   baseconfig.GetBoolEnv("STORAGE_PATH_SANITIZE_NAMES", true),
		},
		Sync: SyncConfig{
			Enabled:    baseconfig.GetBoolEnv("STORAGE_SYNC_ENABLED", true),
			Workers:    baseconfig.GetIntEnv("STORAGE_SYNC_WORKERS", 5),
			QueueSize:  baseconfig.GetIntEnv("STORAGE_SYNC_QUEUE_SIZE", 1000),
			RetryDelay: baseconfig.GetDurationEnv("STORAGE_SYNC_RETRY_DELAY", 5*time.Second),
			MaxRetries: baseconfig.GetIntEnv("STORAGE_SYNC_MAX_RETRIES", 3),
		},
		Health: HealthConfig{
			CheckInterval: baseconfig.GetDurationEnv("STORAGE_HEALTH_CHECK_INTERVAL", 30*time.Second),
			Timeout:       baseconfig.GetDurationEnv("STORAGE_HEALTH_TIMEOUT", 10*time.Second),
		},
	}
}

func loadAuditConfig() AuditConfig {
	return AuditConfig{
		Enabled:          baseconfig.GetBoolEnv("AUDIT_ENABLED", true),
		BatchSize:        baseconfig.GetIntEnv("AUDIT_BATCH_SIZE", 100),
		BatchTimeout:     baseconfig.GetDurationEnv("AUDIT_BATCH_TIMEOUT", 5*time.Second),
		QueueSize:        baseconfig.GetIntEnv("AUDIT_QUEUE_SIZE", 10000),
		EnableBatching:   baseconfig.GetBoolEnv("AUDIT_ENABLE_BATCHING", true),
		EnableEnrichment: baseconfig.GetBoolEnv("AUDIT_ENABLE_ENRICHMENT", true),
		RetentionDays:    baseconfig.GetIntEnv("AUDIT_RETENTION_DAYS", 365),
	}
}

func loadDriveSettings() DriveSettings {
	return DriveSettings{
		MaxFileSize:       baseconfig.GetInt64Env("MAX_FILE_SIZE", 104857600),                                              // 100MB
		MaxChunkSize:      baseconfig.GetInt64Env("CHUNK_SIZE", baseconfig.GetInt64Env("DRIVE_MAX_CHUNK_SIZE", 5242880)), // 5MB
		SessionExpiry:     baseconfig.GetDurationEnv("SESSION_EXPIRY", baseconfig.GetDurationEnv("DRIVE_SESSION_EXPIRY", 24*time.Hour)),
		CleanupInterval:   baseconfig.GetDurationEnv("CLEANUP_INTERVAL", baseconfig.GetDurationEnv("DRIVE_CLEANUP_INTERVAL", 1*time.Hour)),
		MaxFilesPerUser:   baseconfig.GetIntEnv("DRIVE_MAX_FILES_PER_USER", 10000),
		MaxStoragePerUser: baseconfig.GetInt64Env("DRIVE_MAX_STORAGE_PER_USER", 10737418240), // 10GB
	}
}

func loadThumbnailConfig() ThumbnailConfig {
	return ThumbnailConfig{
		StoragePath:       baseconfig.GetEnv("THUMBNAIL_STORAGE_PATH", "./thumbnails"),
		MaxFileSize:       baseconfig.GetInt64Env("THUMBNAIL_MAX_FILE_SIZE", 100*1024*1024), // 100MB
		Quality:           baseconfig.GetIntEnv("THUMBNAIL_QUALITY", 85),
		EnableAsync:       baseconfig.GetBoolEnv("THUMBNAIL_ENABLE_ASYNC", true),
		WorkerCount:       baseconfig.GetIntEnv("THUMBNAIL_WORKER_COUNT", 4),
		ProcessingTimeout: baseconfig.GetDurationEnv("THUMBNAIL_PROCESSING_TIMEOUT", 300*time.Second),
		CacheEnabled:      baseconfig.GetBoolEnv("THUMBNAIL_CACHE_ENABLED", true),
		CacheTTL:          baseconfig.GetDurationEnv("THUMBNAIL_CACHE_TTL", 24*time.Hour),
	}
}

func loadMonitoringConfig() MonitoringConfig {
	return MonitoringConfig{
		Enabled:         baseconfig.GetBoolEnv("MONITORING_ENABLED", true),
		MetricsPort:     baseconfig.GetIntEnv("MONITORING_METRICS_PORT", 9090),
		HealthCheckPort: baseconfig.GetIntEnv("MONITORING_HEALTH_PORT", 9091),
		LogLevel:        baseconfig.GetEnv("LOG_LEVEL", "info"),
		EnableTracing:   baseconfig.GetBoolEnv("TRACING_ENABLED", false),
		TracingEndpoint: baseconfig.GetEnv("TRACING_ENDPOINT", ""),
	}
}

// Helper functions
func (c *DriveConfig) IsDevelopment() bool {
	return c.Server.Environment == "development"
}

func (c *DriveConfig) IsProduction() bool {
	return c.Server.Environment == "production"
}

func (c *DriveConfig) GetDatabaseDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Database.Host,
		c.Database.Port,
		c.Database.Username,
		c.Database.Password,
		c.Database.Database,
		c.Database.SSLMode,
	)
}

func (c *DriveConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Redis.Host, c.Redis.Port)
}

func (c *DriveConfig) Validate() error {
	// Validate database configuration
	if c.Database.Password == "" {
		if !c.IsDevelopment() {
			return fmt.Errorf("database password is required")
		}
		fmt.Println("WARNING: Running without database password in development mode")
	}

	// Validate storage configuration
	if c.Storage.Primary.AccessKey == "" || c.Storage.Primary.SecretKey == "" {
		if !c.IsDevelopment() {
			return fmt.Errorf("primary storage credentials are required")
		}
		fmt.Println("WARNING: Using empty storage credentials in development mode")
	}

	// Allow test credentials in development mode
	if c.IsDevelopment() && c.Storage.Primary.AccessKey == "test_key_id" {
		fmt.Println("WARNING: Using test storage credentials in development mode")
	}

	// Validate JWT secret
	if c.Security.JWTSecret == "" {
		return fmt.Errorf("JWT secret is required")
	}

	// Check for default/weak secrets
	if c.Security.JWTSecret == "your-secret-key" || c.Security.JWTSecret == "secret" ||
		c.Security.JWTSecret == "jwt-secret" || len(c.Security.JWTSecret) < 32 {
		if !c.IsDevelopment() {
			return fmt.Errorf("JWT secret must be at least 32 characters and not use default values")
		}
		fmt.Println("WARNING: Using weak JWT secret in development mode")
	}

	// Validate thumbnail configuration
	if err := c.validateThumbnailConfig(); err != nil {
		return err
	}

	return nil
}

func (c *DriveConfig) validateThumbnailConfig() error {
	// Validate thumbnail quality
	if c.Thumbnail.Quality < 1 || c.Thumbnail.Quality > 100 {
		return fmt.Errorf("thumbnail quality must be between 1 and 100, got %d", c.Thumbnail.Quality)
	}

	// Validate worker count
	if c.Thumbnail.WorkerCount < 1 || c.Thumbnail.WorkerCount > 50 {
		return fmt.Errorf("thumbnail worker count must be between 1 and 50, got %d", c.Thumbnail.WorkerCount)
	}

	// Validate max file size
	if c.Thumbnail.MaxFileSize < 1024 { // Minimum 1KB
		return fmt.Errorf("thumbnail max file size must be at least 1KB, got %d bytes", c.Thumbnail.MaxFileSize)
	}

	// Validate processing timeout
	if c.Thumbnail.ProcessingTimeout < time.Second {
		return fmt.Errorf("thumbnail processing timeout must be at least 1 second, got %v", c.Thumbnail.ProcessingTimeout)
	}

	// Validate cache TTL if caching is enabled
	if c.Thumbnail.CacheEnabled && c.Thumbnail.CacheTTL < time.Minute {
		return fmt.Errorf("thumbnail cache TTL must be at least 1 minute when caching is enabled, got %v", c.Thumbnail.CacheTTL)
	}

	return nil
}

// parseAllowedOrigins parses CORS allowed origins from environment
func parseAllowedOrigins() []string {
	origins := baseconfig.GetEnv("ALLOWED_ORIGINS", "")
	if origins == "" {
		// Default origins for development
		if baseconfig.GetEnv("APP_ENV", "development") == "development" {
			return []string{"http://localhost:3000", "http://127.0.0.1:3000"}
		}
		// Production must explicitly set allowed origins
		return []string{}
	}

	// Parse comma-separated origins
	originList := strings.Split(origins, ",")
	var trimmedOrigins []string
	for _, origin := range originList {
		trimmed := strings.TrimSpace(origin)
		if trimmed != "" {
			trimmedOrigins = append(trimmedOrigins, trimmed)
		}
	}
	return trimmedOrigins
}