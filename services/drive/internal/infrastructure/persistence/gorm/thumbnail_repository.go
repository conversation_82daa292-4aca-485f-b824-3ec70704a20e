package gorm

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/repositories"
)

type thumbnailRepository struct {
	db *gorm.DB
}

type ThumbnailModel struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	FileID      uuid.UUID `gorm:"type:uuid;not null;index"`
	Size        string    `gorm:"not null;index"`
	Format      string    `gorm:"not null;index"`
	Width       int       `gorm:"not null"`
	Height      int       `gorm:"not null"`
	FileSize    int64     `gorm:"column:file_size;not null"`
	StoragePath string    `gorm:"column:storage_path;not null"`
	Checksum    string    `gorm:"not null;index"`
	Status      string    `gorm:"not null;index"`
	CreatedAt   time.Time `gorm:"not null"`
	UpdatedAt   time.Time `gorm:"not null"`
	Metadata    string    `gorm:"type:jsonb;default:'{}';column:metadata"`
}

func (ThumbnailModel) TableName() string {
	return "thumbnails"
}

func NewThumbnailRepository(db *gorm.DB) repositories.ThumbnailRepository {
	return &thumbnailRepository{db: db}
}

func (r *thumbnailRepository) Create(ctx context.Context, thumbnail *entities.Thumbnail) error {
	model := r.entityToModel(thumbnail)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create thumbnail: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) CreateBatch(ctx context.Context, thumbnails []*entities.Thumbnail) error {
	if len(thumbnails) == 0 {
		return nil
	}

	models := make([]*ThumbnailModel, len(thumbnails))
	for i, thumbnail := range thumbnails {
		models[i] = r.entityToModel(thumbnail)
	}

	err := r.db.WithContext(ctx).CreateInBatches(models, 100).Error
	if err != nil {
		return fmt.Errorf("failed to create thumbnails batch: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) GetByID(ctx context.Context, id entities.ThumbnailID) (*entities.Thumbnail, error) {
	var model ThumbnailModel
	err := r.db.WithContext(ctx).Where("id = ?", id.String()).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get thumbnail by ID: %w", err)
	}

	return r.modelToEntity(&model), nil
}

func (r *thumbnailRepository) GetByFileID(ctx context.Context, fileID uuid.UUID) ([]*entities.Thumbnail, error) {
	var models []ThumbnailModel
	err := r.db.WithContext(ctx).Where("file_id = ?", fileID).Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnails by file ID: %w", err)
	}

	thumbnails := make([]*entities.Thumbnail, len(models))
	for i, model := range models {
		thumbnails[i] = r.modelToEntity(&model)
	}

	return thumbnails, nil
}

func (r *thumbnailRepository) GetByFileIDAndSize(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize) ([]*entities.Thumbnail, error) {
	var models []ThumbnailModel
	err := r.db.WithContext(ctx).Where("file_id = ? AND size = ?", fileID, string(size)).Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnails by file ID and size: %w", err)
	}

	thumbnails := make([]*entities.Thumbnail, len(models))
	for i, model := range models {
		thumbnails[i] = r.modelToEntity(&model)
	}

	return thumbnails, nil
}

func (r *thumbnailRepository) GetByFileIDSizeAndFormat(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) (*entities.Thumbnail, error) {
	var model ThumbnailModel
	err := r.db.WithContext(ctx).Where("file_id = ? AND size = ? AND format = ?", fileID, string(size), string(format)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get thumbnail by file ID, size and format: %w", err)
	}

	return r.modelToEntity(&model), nil
}

func (r *thumbnailRepository) GetByStatus(ctx context.Context, status entities.ThumbnailStatus, limit int) ([]*entities.Thumbnail, error) {
	var models []ThumbnailModel
	query := r.db.WithContext(ctx).Where("status = ?", string(status))
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get thumbnails by status: %w", err)
	}

	thumbnails := make([]*entities.Thumbnail, len(models))
	for i, model := range models {
		thumbnails[i] = r.modelToEntity(&model)
	}

	return thumbnails, nil
}

func (r *thumbnailRepository) Update(ctx context.Context, thumbnail *entities.Thumbnail) error {
	model := r.entityToModel(thumbnail)

	err := r.db.WithContext(ctx).Where("id = ?", model.ID).Updates(model).Error
	if err != nil {
		return fmt.Errorf("failed to update thumbnail: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) UpdateStatus(ctx context.Context, id entities.ThumbnailID, status entities.ThumbnailStatus) error {
	err := r.db.WithContext(ctx).Model(&ThumbnailModel{}).Where("id = ?", id.String()).Update("status", string(status)).Error
	if err != nil {
		return fmt.Errorf("failed to update thumbnail status: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) UpdateBatch(ctx context.Context, thumbnails []*entities.Thumbnail) error {
	if len(thumbnails) == 0 {
		return nil
	}

	// Use transaction for batch updates
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, thumbnail := range thumbnails {
			model := r.entityToModel(thumbnail)
			if err := tx.Where("id = ?", model.ID).Updates(model).Error; err != nil {
				return fmt.Errorf("failed to update thumbnail %s: %w", model.ID, err)
			}
		}
		return nil
	})
}

func (r *thumbnailRepository) Delete(ctx context.Context, id entities.ThumbnailID) error {
	err := r.db.WithContext(ctx).Delete(&ThumbnailModel{}, "id = ?", id.String()).Error
	if err != nil {
		return fmt.Errorf("failed to delete thumbnail: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) DeleteByFileID(ctx context.Context, fileID uuid.UUID) error {
	err := r.db.WithContext(ctx).Delete(&ThumbnailModel{}, "file_id = ?", fileID).Error
	if err != nil {
		return fmt.Errorf("failed to delete thumbnails by file ID: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) DeleteByFileIDAndSize(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize) error {
	err := r.db.WithContext(ctx).Delete(&ThumbnailModel{}, "file_id = ? AND size = ?", fileID, string(size)).Error
	if err != nil {
		return fmt.Errorf("failed to delete thumbnails by file ID and size: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) DeleteExpired(ctx context.Context, maxAge int) (int, error) {
	cutoffTime := time.Now().AddDate(0, 0, -maxAge)

	result := r.db.WithContext(ctx).Delete(&ThumbnailModel{}, "created_at < ?", cutoffTime)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to delete expired thumbnails: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

func (r *thumbnailRepository) DeleteBatch(ctx context.Context, ids []entities.ThumbnailID) error {
	if len(ids) == 0 {
		return nil
	}

	idStrings := make([]string, len(ids))
	for i, id := range ids {
		idStrings[i] = id.String()
	}

	err := r.db.WithContext(ctx).Delete(&ThumbnailModel{}, "id IN ?", idStrings).Error
	if err != nil {
		return fmt.Errorf("failed to delete thumbnails batch: %w", err)
	}

	return nil
}

func (r *thumbnailRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&ThumbnailModel{}).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count thumbnails: %w", err)
	}

	return count, nil
}

func (r *thumbnailRepository) CountByStatus(ctx context.Context, status entities.ThumbnailStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&ThumbnailModel{}).Where("status = ?", string(status)).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count thumbnails by status: %w", err)
	}

	return count, nil
}

func (r *thumbnailRepository) CountByFileID(ctx context.Context, fileID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&ThumbnailModel{}).Where("file_id = ?", fileID).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count thumbnails by file ID: %w", err)
	}

	return count, nil
}

func (r *thumbnailRepository) Exists(ctx context.Context, id entities.ThumbnailID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&ThumbnailModel{}).Where("id = ?", id.String()).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check thumbnail existence: %w", err)
	}

	return count > 0, nil
}

func (r *thumbnailRepository) ExistsByFileIDSizeAndFormat(ctx context.Context, fileID uuid.UUID, size entities.ThumbnailSize, format entities.ThumbnailFormat) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&ThumbnailModel{}).Where("file_id = ? AND size = ? AND format = ?", fileID, string(size), string(format)).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check thumbnail existence by file ID, size and format: %w", err)
	}

	return count > 0, nil
}

func (r *thumbnailRepository) GetStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total count
	totalCount, err := r.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}
	stats["total_count"] = totalCount

	// Count by status
	statusCounts := make(map[string]int64)
	statuses := []entities.ThumbnailStatus{
		entities.ThumbnailStatusPending,
		entities.ThumbnailStatusProcessing,
		entities.ThumbnailStatusCompleted,
		entities.ThumbnailStatusFailed,
	}

	for _, status := range statuses {
		count, err := r.CountByStatus(ctx, status)
		if err != nil {
			return nil, fmt.Errorf("failed to get count for status %s: %w", status, err)
		}
		statusCounts[string(status)] = count
	}
	stats["status_counts"] = statusCounts

	return stats, nil
}

func (r *thumbnailRepository) GetStorageUsage(ctx context.Context) (int64, error) {
	var totalSize int64
	err := r.db.WithContext(ctx).Model(&ThumbnailModel{}).Select("COALESCE(SUM(file_size), 0)").Scan(&totalSize).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get storage usage: %w", err)
	}

	return totalSize, nil
}

// Helper methods for entity/model conversion
func (r *thumbnailRepository) entityToModel(thumbnail *entities.Thumbnail) *ThumbnailModel {
	metadata := "{}"
	if thumbnail.Metadata() != nil {
		if metadataBytes, err := json.Marshal(thumbnail.Metadata()); err == nil {
			metadata = string(metadataBytes)
		}
	}

	// Parse the thumbnail ID string as UUID
	thumbnailUUID, err := uuid.Parse(thumbnail.ID().String())
	if err != nil {
		// If parsing fails, generate a new UUID
		thumbnailUUID = uuid.New()
	}

	return &ThumbnailModel{
		ID:          thumbnailUUID,
		FileID:      thumbnail.FileID(),
		Size:        string(thumbnail.Size()),
		Format:      string(thumbnail.Format()),
		Width:       thumbnail.Width(),
		Height:      thumbnail.Height(),
		FileSize:    thumbnail.FileSize(),
		StoragePath: thumbnail.StoragePath(),
		Checksum:    thumbnail.Checksum(),
		Status:      string(thumbnail.Status()),
		CreatedAt:   thumbnail.CreatedAt(),
		UpdatedAt:   thumbnail.UpdatedAt(),
		Metadata:    metadata,
	}
}

func (r *thumbnailRepository) modelToEntity(model *ThumbnailModel) *entities.Thumbnail {
	// Parse metadata
	var metadata map[string]interface{}
	if model.Metadata != "" {
		json.Unmarshal([]byte(model.Metadata), &metadata)
	}
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	return entities.ReconstructThumbnail(
		entities.ThumbnailIDFromString(model.ID.String()),
		model.FileID,
		entities.ThumbnailSize(model.Size),
		entities.ThumbnailFormat(model.Format),
		model.Width,
		model.Height,
		model.FileSize,
		model.StoragePath,
		model.Checksum,
		entities.ThumbnailStatus(model.Status),
		model.CreatedAt,
		model.UpdatedAt,
		metadata,
	)
}

func (r *thumbnailRepository) GetPendingForProcessing(ctx context.Context, limit int) ([]*entities.Thumbnail, error) {
	return r.GetByStatus(ctx, entities.ThumbnailStatusPending, limit)
}

func (r *thumbnailRepository) GetExpiredThumbnails(ctx context.Context, maxAge int) ([]*entities.Thumbnail, error) {
	cutoffTime := time.Now().AddDate(0, 0, -maxAge)

	var models []ThumbnailModel
	err := r.db.WithContext(ctx).Where("created_at < ?", cutoffTime).Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get expired thumbnails: %w", err)
	}

	thumbnails := make([]*entities.Thumbnail, len(models))
	for i, model := range models {
		thumbnails[i] = r.modelToEntity(&model)
	}

	return thumbnails, nil
}
