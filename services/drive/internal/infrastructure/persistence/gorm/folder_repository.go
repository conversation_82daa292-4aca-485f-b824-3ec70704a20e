package gorm

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type folderRepository struct {
	db *gorm.DB
}

type FolderModel struct {
	ID          uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string     `gorm:"not null;index"`
	Path        string     `gorm:"not null;index"`
	UserID      uuid.UUID  `gorm:"type:uuid;not null;index"`
	OwnerID     uuid.UUID  `gorm:"column:owner_id;type:uuid;not null;index"`
	TeamID      *uuid.UUID `gorm:"type:uuid;index"`
	ParentID    *uuid.UUID `gorm:"type:uuid;index"`
	Visibility  string     `gorm:"not null;default:'private'"`
	Description string     `gorm:"type:text"`
	CreatedAt   time.Time  `gorm:"not null"`
	UpdatedAt   time.Time  `gorm:"not null"`
	DeletedAt   *time.Time `gorm:"index"`
}

func (FolderModel) TableName() string {
	return "folders"
}

func NewFolderRepository(db *gorm.DB) repositories.FolderRepository {
	return &folderRepository{db: db}
}

func (r *folderRepository) Create(ctx context.Context, folder *entities.Folder) error {
	model := r.entityToModel(folder)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create folder: %w", err)
	}

	return nil
}

func (r *folderRepository) GetByID(ctx context.Context, id entities.FolderID) (*entities.Folder, error) {
	var model FolderModel
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", uuid.UUID(id)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get folder by ID: %w", err)
	}

	return r.modelToEntity(&model), nil
}

func (r *folderRepository) GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.Folder, error) {
	var models []FolderModel
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get folders by user ID: %w", err)
	}

	folders := make([]*entities.Folder, len(models))
	for i, model := range models {
		folders[i] = r.modelToEntity(&model)
	}

	return folders, nil
}

func (r *folderRepository) GetByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.Folder, error) {
	var models []FolderModel
	err := r.db.WithContext(ctx).
		Where("team_id = ? AND deleted_at IS NULL", teamID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get folders by team ID: %w", err)
	}

	folders := make([]*entities.Folder, len(models))
	for i, model := range models {
		folders[i] = r.modelToEntity(&model)
	}

	return folders, nil
}

func (r *folderRepository) GetByParentID(ctx context.Context, parentID entities.FolderID, offset, limit int) ([]*entities.Folder, error) {
	var models []FolderModel
	err := r.db.WithContext(ctx).
		Where("parent_id = ? AND deleted_at IS NULL", uuid.UUID(parentID)).
		Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get folders by parent ID: %w", err)
	}

	folders := make([]*entities.Folder, len(models))
	for i, model := range models {
		folders[i] = r.modelToEntity(&model)
	}

	return folders, nil
}

func (r *folderRepository) GetSharedWithUser(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.Folder, error) {
	var models []FolderModel
	err := r.db.WithContext(ctx).
		Joins("JOIN folder_shares ON folders.id = folder_shares.folder_id").
		Where("folder_shares.shared_with = ? AND folder_shares.expires_at > ? AND folders.deleted_at IS NULL", userID, time.Now()).
		Order("folders.created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get shared folders: %w", err)
	}

	folders := make([]*entities.Folder, len(models))
	for i, model := range models {
		folders[i] = r.modelToEntity(&model)
	}

	return folders, nil
}

func (r *folderRepository) Update(ctx context.Context, folder *entities.Folder) error {
	model := r.entityToModel(folder)

	updateData := map[string]interface{}{
		"name":        model.Name,
		"path":        model.Path,
		"visibility":  model.Visibility,
		"description": model.Description,
		"parent_id":   model.ParentID,
		"updated_at":  model.UpdatedAt,
	}

	// Handle soft delete - set deleted_at if folder is deleted
	if model.DeletedAt != nil {
		updateData["deleted_at"] = model.DeletedAt
	}

	err := r.db.WithContext(ctx).Model(&FolderModel{}).Where("id = ?", uuid.UUID(folder.ID())).Updates(updateData).Error
	if err != nil {
		return fmt.Errorf("failed to update folder: %w", err)
	}

	return nil
}

func (r *folderRepository) Delete(ctx context.Context, id entities.FolderID) error {
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).Delete(&FolderModel{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete folder: %w", err)
	}

	return nil
}

func (r *folderRepository) GetChildren(ctx context.Context, folderID entities.FolderID) ([]*entities.Folder, error) {
	var models []FolderModel
	err := r.db.WithContext(ctx).
		Where("parent_id = ? AND deleted_at IS NULL", uuid.UUID(folderID)).
		Order("name ASC").
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get folder children: %w", err)
	}

	folders := make([]*entities.Folder, len(models))
	for i, model := range models {
		folders[i] = r.modelToEntity(&model)
	}

	return folders, nil
}

func (r *folderRepository) GetPath(ctx context.Context, folderID entities.FolderID) (string, error) {
	var model FolderModel
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", uuid.UUID(folderID)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", fmt.Errorf("folder not found")
		}
		return "", fmt.Errorf("failed to get folder path: %w", err)
	}

	return model.Path, nil
}

func (r *folderRepository) CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FolderModel{}).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count folders by user ID: %w", err)
	}

	return count, nil
}

func (r *folderRepository) CountByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FolderModel{}).
		Where("team_id = ? AND deleted_at IS NULL", teamID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count folders by team ID: %w", err)
	}

	return count, nil
}

func (r *folderRepository) HasFiles(ctx context.Context, folderID entities.FolderID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FileModel{}).
		Where("folder_id = ? AND deleted_at IS NULL", uuid.UUID(folderID)).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check if folder has files: %w", err)
	}

	return count > 0, nil
}

func (r *folderRepository) HasSubfolders(ctx context.Context, folderID entities.FolderID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FolderModel{}).
		Where("parent_id = ? AND deleted_at IS NULL", uuid.UUID(folderID)).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check if folder has subfolders: %w", err)
	}

	return count > 0, nil
}

func (r *folderRepository) GetRootFoldersByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.Folder, error) {
	var models []FolderModel
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND parent_id IS NULL AND deleted_at IS NULL", userID).
		Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get root folders by user ID: %w", err)
	}

	folders := make([]*entities.Folder, len(models))
	for i, model := range models {
		folders[i] = r.modelToEntity(&model)
	}

	return folders, nil
}

func (r *folderRepository) GetRootFoldersByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.Folder, error) {
	var models []FolderModel
	err := r.db.WithContext(ctx).
		Where("team_id = ? AND parent_id IS NULL AND deleted_at IS NULL", teamID).
		Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get root folders by team ID: %w", err)
	}

	folders := make([]*entities.Folder, len(models))
	for i, model := range models {
		folders[i] = r.modelToEntity(&model)
	}

	return folders, nil
}

func (r *folderRepository) CountRootFoldersByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FolderModel{}).
		Where("user_id = ? AND parent_id IS NULL AND deleted_at IS NULL", userID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count root folders by user ID: %w", err)
	}

	return count, nil
}

func (r *folderRepository) CountRootFoldersByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FolderModel{}).
		Where("team_id = ? AND parent_id IS NULL AND deleted_at IS NULL", teamID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count root folders by team ID: %w", err)
	}

	return count, nil
}

// WouldCreateCycle checks if moving folderID to newParentID would create a circular reference
func (r *folderRepository) WouldCreateCycle(ctx context.Context, folderID entities.FolderID, newParentID entities.FolderID) (bool, error) {
	// Convert to UUIDs for database queries
	folderUUID := uuid.UUID(folderID)
	newParentUUID := uuid.UUID(newParentID)
	
	// If trying to move to itself, that's a cycle
	if folderUUID == newParentUUID {
		return true, nil
	}
	
	// Traverse up the parent chain from newParentID to see if we encounter folderID
	currentParentID := &newParentUUID
	visited := make(map[uuid.UUID]bool) // Prevent infinite loops in case of existing cycles
	
	for currentParentID != nil {
		// If we've seen this ID before, there's already a cycle in the database
		if visited[*currentParentID] {
			return false, fmt.Errorf("existing cycle detected in folder hierarchy")
		}
		visited[*currentParentID] = true
		
		// If the current parent is the folder we're trying to move, that would create a cycle
		if *currentParentID == folderUUID {
			return true, nil
		}
		
		// Get the parent of the current folder
		var parentID *uuid.UUID
		err := r.db.WithContext(ctx).
			Model(&FolderModel{}).
			Select("parent_id").
			Where("id = ? AND deleted_at IS NULL", *currentParentID).
			Scan(&parentID).Error
		
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// Parent folder doesn't exist, no cycle possible
				return false, nil
			}
			return false, fmt.Errorf("failed to check parent folder: %w", err)
		}
		
		// Move to the next parent (nil means we've reached the root)
		currentParentID = parentID
	}
	
	// No cycle detected
	return false, nil
}

func (r *folderRepository) entityToModel(folder *entities.Folder) *FolderModel {
	userID := folder.UserID()
	model := &FolderModel{
		ID:          uuid.UUID(folder.ID()),
		Name:        folder.Name(),
		Path:        folder.Path(),
		UserID:      userID,
		OwnerID:     userID,
		Visibility:  string(folder.Visibility()),
		Description: folder.Description(),
		CreatedAt:   folder.CreatedAt(),
		UpdatedAt:   folder.UpdatedAt(),
	}

	if folder.TeamID() != nil {
		model.TeamID = folder.TeamID()
	}

	if folder.ParentID() != nil {
		parentUUID := uuid.UUID(*folder.ParentID())
		model.ParentID = &parentUUID
	}

	if folder.DeletedAt() != nil {
		model.DeletedAt = folder.DeletedAt()
	}

	return model
}

func (r *folderRepository) modelToEntity(model *FolderModel) *entities.Folder {
	folderID := entities.FolderID(model.ID)
	visibility := entities.Visibility(model.Visibility)

	var parentID *entities.FolderID
	if model.ParentID != nil {
		pid := entities.FolderID(*model.ParentID)
		parentID = &pid
	}

	return entities.ReconstructFolder(
		folderID,
		model.Name,
		model.Path,
		model.UserID,
		model.TeamID,
		parentID,
		visibility,
		model.Description,
		model.CreatedAt,
		model.UpdatedAt,
		model.DeletedAt,
	)
}
