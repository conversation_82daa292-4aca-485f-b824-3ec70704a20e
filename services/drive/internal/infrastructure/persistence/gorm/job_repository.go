package gorm

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/repositories"
)

type jobRepository struct {
	db *gorm.DB
}

type JobModel struct {
	ID            uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	FileID        uuid.UUID  `gorm:"type:uuid;not null;index"`
	UserID        uuid.UUID  `gorm:"type:uuid;not null;index"`
	SourcePath    string     `gorm:"column:source_path;not null"`
	MimeType      string     `gorm:"column:mime_type;not null"`
	JobType       string     `gorm:"column:job_type;not null;index"`
	Priority      string     `gorm:"not null;index"`
	Status        string     `gorm:"not null;index"`
	Sizes         string     `gorm:"type:jsonb;default:'[]';column:sizes"`
	Formats       string     `gorm:"type:jsonb;default:'[]';column:formats"`
	RetryCount    int        `gorm:"column:retry_count;default:0"`
	MaxRetries    int        `gorm:"column:max_retries;default:3"`
	CreatedAt     time.Time  `gorm:"not null"`
	ScheduledAt   time.Time  `gorm:"column:scheduled_at;not null;index"`
	StartedAt     *time.Time `gorm:"column:started_at"`
	CompletedAt   *time.Time `gorm:"column:completed_at"`
	ErrorMessage  string     `gorm:"column:error_message;type:text"`
	ProcessingLog string     `gorm:"column:processing_log;type:jsonb;default:'[]'"`
	Metadata      string     `gorm:"type:jsonb;default:'{}';column:metadata"`
}

func (JobModel) TableName() string {
	return "thumbnail_jobs"
}

func NewJobRepository(db *gorm.DB) repositories.JobRepository {
	return &jobRepository{db: db}
}

func (r *jobRepository) Create(ctx context.Context, job *entities.ThumbnailJob) error {
	model := r.entityToModelForCreate(job)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	return nil
}

func (r *jobRepository) CreateBatch(ctx context.Context, jobs []*entities.ThumbnailJob) error {
	if len(jobs) == 0 {
		return nil
	}

	models := make([]*JobModel, len(jobs))
	for i, job := range jobs {
		models[i] = r.entityToModelForCreate(job)
	}

	err := r.db.WithContext(ctx).CreateInBatches(models, 100).Error
	if err != nil {
		return fmt.Errorf("failed to create jobs batch: %w", err)
	}

	return nil
}

func (r *jobRepository) GetByID(ctx context.Context, id entities.JobID) (*entities.ThumbnailJob, error) {
	var model JobModel
	err := r.db.WithContext(ctx).Where("id = ?", id.String()).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get job by ID: %w", err)
	}

	return r.modelToEntity(&model), nil
}

func (r *jobRepository) GetByFileID(ctx context.Context, fileID uuid.UUID) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	err := r.db.WithContext(ctx).Where("file_id = ?", fileID).Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by file ID: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) GetByStatus(ctx context.Context, status entities.JobStatus, limit int) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	query := r.db.WithContext(ctx).Where("status = ?", string(status))
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by status: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) GetPendingJobs(ctx context.Context, limit int) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	query := r.db.WithContext(ctx).Where("status = ? AND scheduled_at <= ?", string(entities.JobStatusPending), time.Now()).Order("priority ASC, scheduled_at ASC")
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get pending jobs: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) GetRetryableJobs(ctx context.Context, limit int) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	query := r.db.WithContext(ctx).Where("status = ? AND scheduled_at <= ?", string(entities.JobStatusRetrying), time.Now()).Order("priority ASC, scheduled_at ASC")
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get retryable jobs: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) GetExpiredJobs(ctx context.Context, maxAge time.Duration) ([]*entities.ThumbnailJob, error) {
	cutoffTime := time.Now().Add(-maxAge)

	var models []JobModel
	err := r.db.WithContext(ctx).Where("created_at < ?", cutoffTime).Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get expired jobs: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) GetTimeoutJobs(ctx context.Context, timeout time.Duration) ([]*entities.ThumbnailJob, error) {
	cutoffTime := time.Now().Add(-timeout)

	var models []JobModel
	err := r.db.WithContext(ctx).Where("status = ? AND started_at < ?", string(entities.JobStatusProcessing), cutoffTime).Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get timeout jobs: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) Update(ctx context.Context, job *entities.ThumbnailJob) error {
	model := r.entityToModel(job)

	err := r.db.WithContext(ctx).Where("id = ?", model.ID).Updates(model).Error
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	return nil
}

func (r *jobRepository) UpdateStatus(ctx context.Context, id entities.JobID, status entities.JobStatus) error {
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("id = ?", id.String()).Update("status", string(status)).Error
	if err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}

	return nil
}

func (r *jobRepository) UpdateBatch(ctx context.Context, jobs []*entities.ThumbnailJob) error {
	if len(jobs) == 0 {
		return nil
	}

	// Use transaction for batch updates
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, job := range jobs {
			model := r.entityToModel(job)
			if err := tx.Where("id = ?", model.ID).Updates(model).Error; err != nil {
				return fmt.Errorf("failed to update job %s: %w", model.ID, err)
			}
		}
		return nil
	})
}

func (r *jobRepository) Delete(ctx context.Context, id entities.JobID) error {
	err := r.db.WithContext(ctx).Delete(&JobModel{}, "id = ?", id.String()).Error
	if err != nil {
		return fmt.Errorf("failed to delete job: %w", err)
	}

	return nil
}

func (r *jobRepository) DeleteByFileID(ctx context.Context, fileID uuid.UUID) error {
	err := r.db.WithContext(ctx).Delete(&JobModel{}, "file_id = ?", fileID).Error
	if err != nil {
		return fmt.Errorf("failed to delete jobs by file ID: %w", err)
	}

	return nil
}

func (r *jobRepository) DeleteExpired(ctx context.Context, maxAge time.Duration) (int, error) {
	cutoffTime := time.Now().Add(-maxAge)

	result := r.db.WithContext(ctx).Delete(&JobModel{}, "created_at < ?", cutoffTime)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to delete expired jobs: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

func (r *jobRepository) DeleteBatch(ctx context.Context, ids []entities.JobID) error {
	if len(ids) == 0 {
		return nil
	}

	idStrings := make([]string, len(ids))
	for i, id := range ids {
		idStrings[i] = id.String()
	}

	err := r.db.WithContext(ctx).Delete(&JobModel{}, "id IN ?", idStrings).Error
	if err != nil {
		return fmt.Errorf("failed to delete jobs batch: %w", err)
	}

	return nil
}

func (r *jobRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs: %w", err)
	}

	return count, nil
}

func (r *jobRepository) CountByStatus(ctx context.Context, status entities.JobStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("status = ?", string(status)).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs by status: %w", err)
	}

	return count, nil
}

func (r *jobRepository) CountByFileID(ctx context.Context, fileID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("file_id = ?", fileID).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs by file ID: %w", err)
	}

	return count, nil
}

func (r *jobRepository) Exists(ctx context.Context, id entities.JobID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("id = ?", id.String()).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check job existence: %w", err)
	}

	return count > 0, nil
}

func (r *jobRepository) ExistsByFileID(ctx context.Context, fileID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("file_id = ?", fileID).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check job existence by file ID: %w", err)
	}

	return count > 0, nil
}

// Queue operations - Enhanced to support Redis integration
func (r *jobRepository) EnqueueJob(ctx context.Context, job *entities.ThumbnailJob) error {
	// Create job in database first to persist it
	if err := r.Create(ctx, job); err != nil {
		return fmt.Errorf("failed to create job in database: %w", err)
	}
	
	// Note: Redis enqueuing is handled by the thumbnail service layer
	// This ensures proper separation of concerns
	return nil
}

func (r *jobRepository) DequeueJob(ctx context.Context) (*entities.ThumbnailJob, error) {
	// Get the highest priority pending job
	jobs, err := r.GetPendingJobs(ctx, 1)
	if err != nil {
		return nil, fmt.Errorf("failed to dequeue job: %w", err)
	}

	if len(jobs) == 0 {
		return nil, nil // No jobs available
	}

	job := jobs[0]

	// Mark as processing
	job.MarkAsProcessing()
	if err := r.Update(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to mark job as processing: %w", err)
	}

	return job, nil
}

func (r *jobRepository) RequeueJob(ctx context.Context, job *entities.ThumbnailJob) error {
	return r.Update(ctx, job)
}

func (r *jobRepository) GetQueueLength(ctx context.Context) (int64, error) {
	return r.CountByStatus(ctx, entities.JobStatusPending)
}

// Statistics operations
func (r *jobRepository) GetStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total count
	totalCount, err := r.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}
	stats["total_count"] = totalCount

	// Count by status
	statusCounts := make(map[string]int64)
	statuses := []entities.JobStatus{
		entities.JobStatusPending,
		entities.JobStatusProcessing,
		entities.JobStatusCompleted,
		entities.JobStatusFailed,
		entities.JobStatusRetrying,
		entities.JobStatusDead,
	}

	for _, status := range statuses {
		count, err := r.CountByStatus(ctx, status)
		if err != nil {
			return nil, fmt.Errorf("failed to get count for status %s: %w", status, err)
		}
		statusCounts[string(status)] = count
	}
	stats["status_counts"] = statusCounts

	return stats, nil
}

func (r *jobRepository) GetThroughputStats(ctx context.Context, since time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Count completed jobs since the given time
	var completedCount int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("status = ? AND completed_at >= ?", string(entities.JobStatusCompleted), since).Count(&completedCount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get completed count: %w", err)
	}
	stats["completed_count"] = completedCount

	// Count failed jobs since the given time
	var failedCount int64
	err = r.db.WithContext(ctx).Model(&JobModel{}).Where("status = ? AND completed_at >= ?", string(entities.JobStatusFailed), since).Count(&failedCount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get failed count: %w", err)
	}
	stats["failed_count"] = failedCount

	// Calculate throughput (jobs per hour)
	duration := time.Since(since)
	if duration.Hours() > 0 {
		stats["jobs_per_hour"] = float64(completedCount+failedCount) / duration.Hours()
	} else {
		stats["jobs_per_hour"] = 0.0
	}

	return stats, nil
}

func (r *jobRepository) GetErrorStats(ctx context.Context, since time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Count failed jobs since the given time
	var failedCount int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("status = ? AND completed_at >= ?", string(entities.JobStatusFailed), since).Count(&failedCount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get failed count: %w", err)
	}
	stats["failed_count"] = failedCount

	// Count dead jobs since the given time
	var deadCount int64
	err = r.db.WithContext(ctx).Model(&JobModel{}).Where("status = ? AND completed_at >= ?", string(entities.JobStatusDead), since).Count(&deadCount).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get dead count: %w", err)
	}
	stats["dead_count"] = deadCount

	// Get most common error messages
	var errorMessages []string
	err = r.db.WithContext(ctx).Model(&JobModel{}).Where("status IN ? AND completed_at >= ? AND error_message != ''", []string{string(entities.JobStatusFailed), string(entities.JobStatusDead)}, since).Pluck("error_message", &errorMessages).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get error messages: %w", err)
	}
	stats["error_messages"] = errorMessages

	return stats, nil
}

// Cleanup operations
func (r *jobRepository) CleanupOldJobs(ctx context.Context, maxAge time.Duration) (int, error) {
	cutoffTime := time.Now().Add(-maxAge)

	result := r.db.WithContext(ctx).Delete(&JobModel{}, "status IN ? AND completed_at < ?", []string{string(entities.JobStatusCompleted), string(entities.JobStatusFailed), string(entities.JobStatusDead)}, cutoffTime)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old jobs: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

func (r *jobRepository) CountByUser(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("user_id = ?", userID).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs by user: %w", err)
	}

	return count, nil
}

// Helper methods for entity/model conversion
func (r *jobRepository) entityToModelForCreate(job *entities.ThumbnailJob) *JobModel {
	// Use the entity's own ID to avoid duplicate key issues
	model := r.entityToModel(job)
	// Keep the entity's ID instead of forcing database generation
	// This prevents race conditions when multiple requests try to create jobs simultaneously
	return model
}

func (r *jobRepository) entityToModel(job *entities.ThumbnailJob) *JobModel {
	// Marshal sizes and formats to JSON
	sizesJSON := "[]"
	if job.Sizes() != nil {
		if sizesBytes, err := json.Marshal(job.Sizes()); err == nil {
			sizesJSON = string(sizesBytes)
		}
	}

	formatsJSON := "[]"
	if job.Formats() != nil {
		if formatsBytes, err := json.Marshal(job.Formats()); err == nil {
			formatsJSON = string(formatsBytes)
		}
	}

	// Marshal processing log to JSON
	processingLogJSON := "[]"
	if job.ProcessingLog() != nil {
		if logBytes, err := json.Marshal(job.ProcessingLog()); err == nil {
			processingLogJSON = string(logBytes)
		}
	}

	// Marshal metadata to JSON
	metadataJSON := "{}"
	if job.Metadata() != nil {
		if metadataBytes, err := json.Marshal(job.Metadata()); err == nil {
			metadataJSON = string(metadataBytes)
		}
	}

	// For new job creation, use nil UUID (database will generate)
	// For existing jobs (updates), use the provided UUID
	var jobUUID uuid.UUID
	// Check if this is a new job by seeing if the ID was just generated
	// New jobs will have a UUID but should use database generation instead
	if job.ID().String() != "" {
		parsedUUID, err := uuid.Parse(job.ID().String())
		if err != nil {
			// If parsing fails, let database generate
			jobUUID = uuid.Nil
		} else {
			// For existing jobs being updated, use the existing UUID
			// For new jobs, we'll let the Create method override this to uuid.Nil
			jobUUID = parsedUUID
		}
	} else {
		// For jobs without ID, let database generate
		jobUUID = uuid.Nil
	}

	return &JobModel{
		ID:            jobUUID,
		FileID:        job.FileID(),
		UserID:        job.UserID(),
		SourcePath:    job.SourcePath(),
		MimeType:      job.MimeType(),
		JobType:       string(job.JobType()),
		Priority:      string(job.Priority()),
		Status:        string(job.Status()),
		Sizes:         sizesJSON,
		Formats:       formatsJSON,
		RetryCount:    job.RetryCount(),
		MaxRetries:    job.MaxRetries(),
		CreatedAt:     job.CreatedAt(),
		ScheduledAt:   job.ScheduledAt(),
		StartedAt:     job.StartedAt(),
		CompletedAt:   job.CompletedAt(),
		ErrorMessage:  job.ErrorMessage(),
		ProcessingLog: processingLogJSON,
		Metadata:      metadataJSON,
	}
}

func (r *jobRepository) modelToEntity(model *JobModel) *entities.ThumbnailJob {
	// Parse sizes from JSON
	var sizes []entities.ThumbnailSize
	if model.Sizes != "" {
		json.Unmarshal([]byte(model.Sizes), &sizes)
	}

	// Parse formats from JSON
	var formats []entities.ThumbnailFormat
	if model.Formats != "" {
		json.Unmarshal([]byte(model.Formats), &formats)
	}

	// Parse processing log from JSON
	var processingLog []string
	if model.ProcessingLog != "" {
		json.Unmarshal([]byte(model.ProcessingLog), &processingLog)
	}

	// Parse metadata from JSON
	var metadata map[string]interface{}
	if model.Metadata != "" {
		json.Unmarshal([]byte(model.Metadata), &metadata)
	}
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	return entities.ReconstructThumbnailJob(
		entities.JobIDFromString(model.ID.String()),
		model.FileID,
		model.UserID,
		model.SourcePath,
		model.MimeType,
		entities.JobType(model.JobType),
		entities.JobPriority(model.Priority),
		entities.JobStatus(model.Status),
		sizes,
		formats,
		model.RetryCount,
		model.MaxRetries,
		model.CreatedAt,
		model.ScheduledAt,
		model.StartedAt,
		model.CompletedAt,
		model.ErrorMessage,
		processingLog,
		metadata,
	)
}

// Additional missing methods
func (r *jobRepository) DeleteCompleted(ctx context.Context, maxAge time.Duration) (int, error) {
	cutoffTime := time.Now().Add(-maxAge)

	result := r.db.WithContext(ctx).Delete(&JobModel{}, "status = ? AND completed_at < ?", string(entities.JobStatusCompleted), cutoffTime)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to delete completed jobs: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

func (r *jobRepository) GetJobsByUser(ctx context.Context, userID uuid.UUID, limit int) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by user: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) GetJobsForRetry(ctx context.Context, limit int) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	query := r.db.WithContext(ctx).Where("status = ? AND retry_count < max_retries", string(entities.JobStatusFailed))
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs for retry: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) IncrementRetryCount(ctx context.Context, id entities.JobID) error {
	err := r.db.WithContext(ctx).Model(&JobModel{}).Where("id = ?", id.String()).Update("retry_count", gorm.Expr("retry_count + 1")).Error
	if err != nil {
		return fmt.Errorf("failed to increment retry count: %w", err)
	}

	return nil
}

func (r *jobRepository) MarkJobsAsTimeout(ctx context.Context, timeout time.Duration) (int, error) {
	cutoffTime := time.Now().Add(-timeout)

	result := r.db.WithContext(ctx).Model(&JobModel{}).Where("status = ? AND started_at < ?", string(entities.JobStatusProcessing), cutoffTime).Update("status", string(entities.JobStatusFailed))
	if result.Error != nil {
		return 0, fmt.Errorf("failed to mark jobs as timeout: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

func (r *jobRepository) GetJobHistory(ctx context.Context, fileID uuid.UUID, limit int) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	query := r.db.WithContext(ctx).Where("file_id = ?", fileID).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get job history: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}

func (r *jobRepository) GetJobsByPriority(ctx context.Context, priority entities.JobPriority, limit int) ([]*entities.ThumbnailJob, error) {
	var models []JobModel
	query := r.db.WithContext(ctx).Where("priority = ?", string(priority))
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by priority: %w", err)
	}

	jobs := make([]*entities.ThumbnailJob, len(models))
	for i, model := range models {
		jobs[i] = r.modelToEntity(&model)
	}

	return jobs, nil
}
