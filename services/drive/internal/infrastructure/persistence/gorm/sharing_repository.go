package gorm

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	fileentities "github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/sharing/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/sharing/repositories"
)

type sharingRepository struct {
	db *gorm.DB
}

type FileShareModel struct {
	ID         uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	FileID     uuid.UUID  `gorm:"type:uuid;not null;index"`
	SharedWith uuid.UUID  `gorm:"type:uuid;not null;index"`
	SharedBy   uuid.UUID  `gorm:"type:uuid;not null;index"`
	Permission string     `gorm:"not null"`
	CreatedAt  time.Time  `gorm:"not null"`
	UpdatedAt  time.Time  `gorm:"not null"`
	ExpiresAt  *time.Time `gorm:"index"`
}

type FolderShareModel struct {
	ID         uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	FolderID   uuid.UUID  `gorm:"type:uuid;not null;index"`
	SharedWith uuid.UUID  `gorm:"type:uuid;not null;index"`
	SharedBy   uuid.UUID  `gorm:"type:uuid;not null;index"`
	Permission string     `gorm:"not null"`
	CreatedAt  time.Time  `gorm:"not null"`
	UpdatedAt  time.Time  `gorm:"not null"`
	ExpiresAt  *time.Time `gorm:"index"`
}

func (FileShareModel) TableName() string {
	return "file_shares"
}

func (FolderShareModel) TableName() string {
	return "folder_shares"
}

func NewSharingRepository(db *gorm.DB) repositories.SharingRepository {
	return &sharingRepository{db: db}
}

func (r *sharingRepository) CreateFileShare(ctx context.Context, share *entities.FileShare) error {
	model := r.fileShareEntityToModel(share)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create file share: %w", err)
	}

	return nil
}

func (r *sharingRepository) CreateFolderShare(ctx context.Context, share *entities.FolderShare) error {
	model := r.folderShareEntityToModel(share)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create folder share: %w", err)
	}

	return nil
}

func (r *sharingRepository) GetFileShare(ctx context.Context, id entities.ShareID) (*entities.FileShare, error) {
	var model FileShareModel
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get file share by ID: %w", err)
	}

	return r.fileShareModelToEntity(&model), nil
}

func (r *sharingRepository) GetFolderShare(ctx context.Context, id entities.ShareID) (*entities.FolderShare, error) {
	var model FolderShareModel
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get folder share by ID: %w", err)
	}

	return r.folderShareModelToEntity(&model), nil
}

func (r *sharingRepository) GetFileSharesByFileID(ctx context.Context, fileID fileentities.FileID) ([]*entities.FileShare, error) {
	var models []FileShareModel
	err := r.db.WithContext(ctx).
		Where("file_id = ?", uuid.UUID(fileID)).
		Order("created_at DESC").
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get file shares by file ID: %w", err)
	}

	shares := make([]*entities.FileShare, len(models))
	for i, model := range models {
		shares[i] = r.fileShareModelToEntity(&model)
	}

	return shares, nil
}

func (r *sharingRepository) GetFolderSharesByFolderID(ctx context.Context, folderID fileentities.FolderID) ([]*entities.FolderShare, error) {
	var models []FolderShareModel
	err := r.db.WithContext(ctx).
		Where("folder_id = ?", uuid.UUID(folderID)).
		Order("created_at DESC").
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get folder shares by folder ID: %w", err)
	}

	shares := make([]*entities.FolderShare, len(models))
	for i, model := range models {
		shares[i] = r.folderShareModelToEntity(&model)
	}

	return shares, nil
}

func (r *sharingRepository) GetFileSharesByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.FileShare, error) {
	var models []FileShareModel
	err := r.db.WithContext(ctx).
		Where("shared_with = ?", userID).
		Order("created_at DESC").
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get file shares by user ID: %w", err)
	}

	shares := make([]*entities.FileShare, len(models))
	for i, model := range models {
		shares[i] = r.fileShareModelToEntity(&model)
	}

	return shares, nil
}

func (r *sharingRepository) GetFolderSharesByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.FolderShare, error) {
	var models []FolderShareModel
	err := r.db.WithContext(ctx).
		Where("shared_with = ?", userID).
		Order("created_at DESC").
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get folder shares by user ID: %w", err)
	}

	shares := make([]*entities.FolderShare, len(models))
	for i, model := range models {
		shares[i] = r.folderShareModelToEntity(&model)
	}

	return shares, nil
}

func (r *sharingRepository) GetFileShareByFileAndUser(ctx context.Context, fileID fileentities.FileID, userID uuid.UUID) (*entities.FileShare, error) {
	var model FileShareModel
	err := r.db.WithContext(ctx).
		Where("file_id = ? AND shared_with = ?", uuid.UUID(fileID), userID).
		First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get file share by file and user: %w", err)
	}

	return r.fileShareModelToEntity(&model), nil
}

func (r *sharingRepository) GetFolderShareByFolderAndUser(ctx context.Context, folderID fileentities.FolderID, userID uuid.UUID) (*entities.FolderShare, error) {
	var model FolderShareModel
	err := r.db.WithContext(ctx).
		Where("folder_id = ? AND shared_with = ?", uuid.UUID(folderID), userID).
		First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get folder share by folder and user: %w", err)
	}

	return r.folderShareModelToEntity(&model), nil
}

func (r *sharingRepository) UpdateFileShare(ctx context.Context, share *entities.FileShare) error {
	model := r.fileShareEntityToModel(share)

	err := r.db.WithContext(ctx).Save(model).Error
	if err != nil {
		return fmt.Errorf("failed to update file share: %w", err)
	}

	return nil
}

func (r *sharingRepository) UpdateFolderShare(ctx context.Context, share *entities.FolderShare) error {
	model := r.folderShareEntityToModel(share)

	err := r.db.WithContext(ctx).Save(model).Error
	if err != nil {
		return fmt.Errorf("failed to update folder share: %w", err)
	}

	return nil
}

func (r *sharingRepository) DeleteFileShare(ctx context.Context, id entities.ShareID) error {
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).Delete(&FileShareModel{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete file share: %w", err)
	}

	return nil
}

func (r *sharingRepository) DeleteFolderShare(ctx context.Context, id entities.ShareID) error {
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).Delete(&FolderShareModel{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete folder share: %w", err)
	}

	return nil
}

func (r *sharingRepository) DeleteFileSharesByFileID(ctx context.Context, fileID fileentities.FileID) error {
	err := r.db.WithContext(ctx).Where("file_id = ?", uuid.UUID(fileID)).Delete(&FileShareModel{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete file shares by file ID: %w", err)
	}

	return nil
}

func (r *sharingRepository) DeleteFolderSharesByFolderID(ctx context.Context, folderID fileentities.FolderID) error {
	err := r.db.WithContext(ctx).Where("folder_id = ?", uuid.UUID(folderID)).Delete(&FolderShareModel{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete folder shares by folder ID: %w", err)
	}

	return nil
}

func (r *sharingRepository) GetExpiredShares(ctx context.Context, limit int) ([]*entities.FileShare, []*entities.FolderShare, error) {
	// Get expired file shares
	var fileShareModels []FileShareModel
	err := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Limit(limit).
		Find(&fileShareModels).Error
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get expired file shares: %w", err)
	}

	fileShares := make([]*entities.FileShare, len(fileShareModels))
	for i, model := range fileShareModels {
		fileShares[i] = r.fileShareModelToEntity(&model)
	}

	// Get expired folder shares
	var folderShareModels []FolderShareModel
	err = r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Limit(limit).
		Find(&folderShareModels).Error
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get expired folder shares: %w", err)
	}

	folderShares := make([]*entities.FolderShare, len(folderShareModels))
	for i, model := range folderShareModels {
		folderShares[i] = r.folderShareModelToEntity(&model)
	}

	return fileShares, folderShares, nil
}

// GetByFileIDAndUser gets all file shares for a specific file and user
func (r *sharingRepository) GetByFileIDAndUser(ctx context.Context, fileID fileentities.FileID, userID uuid.UUID) ([]*entities.FileShare, error) {
	var models []FileShareModel
	err := r.db.WithContext(ctx).
		Where("file_id = ? AND shared_with = ?", uuid.UUID(fileID), userID).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get file shares by file ID and user: %w", err)
	}

	shares := make([]*entities.FileShare, len(models))
	for i, model := range models {
		shares[i] = r.fileShareModelToEntity(&model)
	}

	return shares, nil
}

func (r *sharingRepository) fileShareEntityToModel(share *entities.FileShare) *FileShareModel {
	model := &FileShareModel{
		ID:         uuid.UUID(share.ID()),
		FileID:     uuid.UUID(share.FileID()),
		SharedWith: share.SharedWith(),
		SharedBy:   share.SharedBy(),
		Permission: string(share.Permission()),
		CreatedAt:  share.CreatedAt(),
		UpdatedAt:  share.UpdatedAt(),
	}

	if share.ExpiresAt() != nil {
		model.ExpiresAt = share.ExpiresAt()
	}

	return model
}

func (r *sharingRepository) folderShareEntityToModel(share *entities.FolderShare) *FolderShareModel {
	model := &FolderShareModel{
		ID:         uuid.UUID(share.ID()),
		FolderID:   uuid.UUID(share.FolderID()),
		SharedWith: share.SharedWith(),
		SharedBy:   share.SharedBy(),
		Permission: string(share.Permission()),
		CreatedAt:  share.CreatedAt(),
		UpdatedAt:  share.UpdatedAt(),
	}

	if share.ExpiresAt() != nil {
		model.ExpiresAt = share.ExpiresAt()
	}

	return model
}

func (r *sharingRepository) fileShareModelToEntity(model *FileShareModel) *entities.FileShare {
	share := entities.NewFileShare(
		fileentities.FileID(model.FileID),
		model.SharedWith,
		model.SharedBy,
		entities.Permission(model.Permission),
	)

	// This is simplified - in production you'd want proper entity reconstruction
	return share
}

func (r *sharingRepository) folderShareModelToEntity(model *FolderShareModel) *entities.FolderShare {
	share := entities.NewFolderShare(
		fileentities.FolderID(model.FolderID),
		model.SharedWith,
		model.SharedBy,
		entities.Permission(model.Permission),
	)

	// This is simplified - in production you'd want proper entity reconstruction
	return share
}
