package gorm

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/audit/repositories"
)

type auditRepository struct {
	db *gorm.DB
}

type AuditLogModel struct {
	ID           uuid.UUID              `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	EventType    string                 `gorm:"not null;index"`
	UserID       *uuid.UUID             `gorm:"type:uuid;index"`
	TeamID       *uuid.UUID             `gorm:"type:uuid;index"`
	ResourceID   *uuid.UUID             `gorm:"type:uuid;index"`
	ResourceType string                 `gorm:"index"`
	Action       string                 `gorm:"not null"`
	Description  string                 `gorm:"not null"`
	IPAddress    string                 `gorm:"index"`
	UserAgent    string                 `gorm:""`
	Country      string                 `gorm:""`
	City         string                 `gorm:""`
	Latitude     *float64               `gorm:""`
	Longitude    *float64               `gorm:""`
	DeviceInfo   string                 `gorm:""`
	SessionID    *uuid.UUID             `gorm:"type:uuid;index"`
	Checksum     string                 `gorm:"not null"`
	Timestamp    time.Time              `gorm:"not null;index"`
	Metadata     map[string]interface{} `gorm:"type:jsonb"`
}

func (AuditLogModel) TableName() string {
	return "audit_logs"
}

func NewAuditRepository(db *gorm.DB) repositories.AuditRepository {
	return &auditRepository{db: db}
}

func (r *auditRepository) Create(ctx context.Context, log *entities.AuditLog) error {
	model := r.entityToModel(log)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create audit log: %w", err)
	}

	return nil
}

func (r *auditRepository) CreateBatch(ctx context.Context, logs []*entities.AuditLog) error {
	if len(logs) == 0 {
		return nil
	}

	models := make([]AuditLogModel, len(logs))
	for i, log := range logs {
		models[i] = *r.entityToModel(log)
	}

	err := r.db.WithContext(ctx).CreateInBatches(models, 100).Error
	if err != nil {
		return fmt.Errorf("failed to create audit log batch: %w", err)
	}

	return nil
}

func (r *auditRepository) GetByID(ctx context.Context, id entities.AuditLogID) (*entities.AuditLog, error) {
	var model AuditLogModel
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get audit log by ID: %w", err)
	}

	return r.modelToEntity(&model), nil
}

func (r *auditRepository) GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by user ID: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("team_id = ?", teamID).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by team ID: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetByResourceID(ctx context.Context, resourceID uuid.UUID, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("resource_id = ?", resourceID).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by resource ID: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetByEventType(ctx context.Context, eventType entities.EventType, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("event_type = ?", string(eventType)).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by event type: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetByTimeRange(ctx context.Context, startTime, endTime time.Time, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("timestamp BETWEEN ? AND ?", startTime, endTime).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by time range: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetByUserAndTimeRange(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND timestamp BETWEEN ? AND ?", userID, startTime, endTime).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by user and time range: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetByTeamAndTimeRange(ctx context.Context, teamID uuid.UUID, startTime, endTime time.Time, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("team_id = ? AND timestamp BETWEEN ? AND ?", teamID, startTime, endTime).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by team and time range: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetByIPAddress(ctx context.Context, ipAddress string, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("ip_address = ?", ipAddress).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get audit logs by IP address: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetSecurityEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error) {
	securityEvents := []string{
		string(entities.EventTypeUserLogin),
		string(entities.EventTypeUserLogout),
		string(entities.EventTypeUserAccess),
		string(entities.EventTypePermissionChanged),
	}

	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("event_type IN ?", securityEvents).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get security events: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetSystemEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error) {
	systemEvents := []string{
		string(entities.EventTypeSystemError),
		string(entities.EventTypeSystemWarning),
		string(entities.EventTypeSystemInfo),
	}

	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("event_type IN ?", systemEvents).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get system events: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetFileEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error) {
	fileEvents := []string{
		string(entities.EventTypeFileCreate),
		string(entities.EventTypeFileRead),
		string(entities.EventTypeFileUpdate),
		string(entities.EventTypeFileDelete),
		string(entities.EventTypeFileDownload),
		string(entities.EventTypeFileUpload),
		string(entities.EventTypeFileShare),
		string(entities.EventTypeFileUnshare),
	}

	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("event_type IN ?", fileEvents).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get file events: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) GetFolderEvents(ctx context.Context, offset, limit int) ([]*entities.AuditLog, error) {
	folderEvents := []string{
		string(entities.EventTypeFolderCreate),
		string(entities.EventTypeFolderRead),
		string(entities.EventTypeFolderUpdate),
		string(entities.EventTypeFolderDelete),
		string(entities.EventTypeFolderShare),
		string(entities.EventTypeFolderUnshare),
	}

	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("event_type IN ?", folderEvents).
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get folder events: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) Search(ctx context.Context, query string, offset, limit int) ([]*entities.AuditLog, error) {
	var models []AuditLogModel
	err := r.db.WithContext(ctx).
		Where("description ILIKE ? OR action ILIKE ?", "%"+query+"%", "%"+query+"%").
		Order("timestamp DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to search audit logs: %w", err)
	}

	logs := make([]*entities.AuditLog, len(models))
	for i, model := range models {
		logs[i] = r.modelToEntity(&model)
	}

	return logs, nil
}

func (r *auditRepository) DeleteOldLogs(ctx context.Context, before time.Time) error {
	err := r.db.WithContext(ctx).Where("timestamp < ?", before).Delete(&AuditLogModel{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete old audit logs: %w", err)
	}

	return nil
}

func (r *auditRepository) GetStatistics(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total events count
	var totalEvents int64
	err := r.db.WithContext(ctx).Model(&AuditLogModel{}).
		Where("timestamp BETWEEN ? AND ?", startTime, endTime).
		Count(&totalEvents).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get total events count: %w", err)
	}
	stats["total_events"] = totalEvents

	// Events by type
	var eventTypes []struct {
		EventType string `json:"event_type"`
		Count     int64  `json:"count"`
	}
	err = r.db.WithContext(ctx).Model(&AuditLogModel{}).
		Select("event_type, COUNT(*) as count").
		Where("timestamp BETWEEN ? AND ?", startTime, endTime).
		Group("event_type").
		Scan(&eventTypes).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get events by type: %w", err)
	}
	stats["events_by_type"] = eventTypes

	return stats, nil
}

func (r *auditRepository) GetTopUsers(ctx context.Context, startTime, endTime time.Time, limit int) ([]map[string]interface{}, error) {
	var results []struct {
		UserID string `json:"user_id"`
		Count  int64  `json:"count"`
	}

	err := r.db.WithContext(ctx).Model(&AuditLogModel{}).
		Select("user_id, COUNT(*) as count").
		Where("user_id IS NOT NULL AND timestamp BETWEEN ? AND ?", startTime, endTime).
		Group("user_id").
		Order("count DESC").
		Limit(limit).
		Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}

	topUsers := make([]map[string]interface{}, len(results))
	for i, result := range results {
		topUsers[i] = map[string]interface{}{
			"user_id": result.UserID,
			"count":   result.Count,
		}
	}

	return topUsers, nil
}

func (r *auditRepository) GetTopActions(ctx context.Context, startTime, endTime time.Time, limit int) ([]map[string]interface{}, error) {
	var results []struct {
		Action string `json:"action"`
		Count  int64  `json:"count"`
	}

	err := r.db.WithContext(ctx).Model(&AuditLogModel{}).
		Select("action, COUNT(*) as count").
		Where("timestamp BETWEEN ? AND ?", startTime, endTime).
		Group("action").
		Order("count DESC").
		Limit(limit).
		Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get top actions: %w", err)
	}

	topActions := make([]map[string]interface{}, len(results))
	for i, result := range results {
		topActions[i] = map[string]interface{}{
			"action": result.Action,
			"count":  result.Count,
		}
	}

	return topActions, nil
}

func (r *auditRepository) GetActivityByHour(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var results []struct {
		Hour  int   `json:"hour"`
		Count int64 `json:"count"`
	}

	err := r.db.WithContext(ctx).Model(&AuditLogModel{}).
		Select("EXTRACT(hour FROM timestamp) as hour, COUNT(*) as count").
		Where("timestamp BETWEEN ? AND ?", startTime, endTime).
		Group("hour").
		Order("hour").
		Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get activity by hour: %w", err)
	}

	activity := make([]map[string]interface{}, len(results))
	for i, result := range results {
		activity[i] = map[string]interface{}{
			"hour":  result.Hour,
			"count": result.Count,
		}
	}

	return activity, nil
}

func (r *auditRepository) GetActivityByDay(ctx context.Context, startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var results []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	err := r.db.WithContext(ctx).Model(&AuditLogModel{}).
		Select("DATE(timestamp) as date, COUNT(*) as count").
		Where("timestamp BETWEEN ? AND ?", startTime, endTime).
		Group("date").
		Order("date").
		Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get activity by day: %w", err)
	}

	activity := make([]map[string]interface{}, len(results))
	for i, result := range results {
		activity[i] = map[string]interface{}{
			"date":  result.Date,
			"count": result.Count,
		}
	}

	return activity, nil
}

func (r *auditRepository) ValidateIntegrity(ctx context.Context, logs []*entities.AuditLog) error {
	// This is a simplified integrity validation
	// In production, you'd implement more sophisticated checks
	for _, log := range logs {
		if log.Checksum() == "" {
			return fmt.Errorf("audit log missing checksum: %s", log.ID())
		}
	}
	return nil
}

func (r *auditRepository) entityToModel(log *entities.AuditLog) *AuditLogModel {
	model := &AuditLogModel{
		ID:          uuid.UUID(log.ID()),
		EventType:   string(log.EventType()),
		Action:      log.Action(),
		Description: log.Description(),
		IPAddress:   log.IPAddress(),
		UserAgent:   log.UserAgent(),
		Country:     log.Country(),
		City:        log.City(),
		Latitude:    log.Latitude(),
		Longitude:   log.Longitude(),
		DeviceInfo:  log.DeviceInfo(),
		Checksum:    log.Checksum(),
		Timestamp:   log.Timestamp(),
		Metadata:    log.Metadata(),
	}

	if log.UserID() != nil {
		model.UserID = log.UserID()
	}

	if log.TeamID() != nil {
		model.TeamID = log.TeamID()
	}

	if log.ResourceID() != nil {
		model.ResourceID = log.ResourceID()
	}

	if log.ResourceType() != "" {
		model.ResourceType = log.ResourceType()
	}

	if log.SessionID() != nil {
		model.SessionID = log.SessionID()
	}

	return model
}

func (r *auditRepository) modelToEntity(model *AuditLogModel) *entities.AuditLog {
	log := entities.NewAuditLog(
		entities.EventType(model.EventType),
		model.Action,
		model.Description,
	)

	// This is simplified - in production you'd want proper entity reconstruction
	return log
}
