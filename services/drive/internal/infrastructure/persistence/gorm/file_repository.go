package gorm

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/swork-team/platform/services/drive/internal/domain/file/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/file/repositories"
)

type fileRepository struct {
	db *gorm.DB
}

// Helper functions for metadata JSON handling
func marshalMetadata(metadata map[string]interface{}) string {
	if metadata == nil {
		return "{}"
	}
	bytes, err := json.Marshal(metadata)
	if err != nil {
		return "{}"
	}
	return string(bytes)
}

func unmarshalMetadata(metadataStr string) map[string]interface{} {
	if metadataStr == "" || metadataStr == "{}" {
		return make(map[string]interface{})
	}
	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(metadataStr), &metadata); err != nil {
		return make(map[string]interface{})
	}
	return metadata
}

type FileModel struct {
	ID           uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name         string     `gorm:"not null;index"`
	OriginalName string     `gorm:"column:original_name;not null"`
	Path         string     `gorm:"not null;unique"`
	URL          string     `gorm:"type:varchar(500);not null"`
	Size         int64      `gorm:"not null"`
	MimeType     string     `gorm:"column:mime_type;not null"`
	Extension    string     `gorm:"type:varchar(10)"`
	Checksum     string     `gorm:"not null;index"`
	Width        *int64     `gorm:"column:width"`
	Height       *int64     `gorm:"column:height"`
	Duration     *int64     `gorm:"column:duration"`
	UserID       uuid.UUID  `gorm:"column:user_id;type:uuid;not null;index"`
	OwnerID      uuid.UUID  `gorm:"column:owner_id;type:uuid;not null;index"`
	TeamID       *uuid.UUID `gorm:"column:team_id;type:uuid;index"`
	FolderID     *uuid.UUID `gorm:"column:folder_id;type:uuid;index"`
	Visibility   string     `gorm:"not null;default:'private'"`
	IsPublic     bool       `gorm:"column:is_public;default:false"`
	ShareToken   *string    `gorm:"column:share_token"`
	Version      int64      `gorm:"not null;default:1"`
	ParentID     *uuid.UUID `gorm:"column:parent_id;type:uuid;index"`
	IsLatest     bool       `gorm:"column:is_latest;default:true"`
	Status       string     `gorm:"not null;default:'active'"`
	IsDeleted    bool       `gorm:"column:is_deleted;default:false"`
	DeletedAt    *time.Time `gorm:"column:deleted_at"`
	DeletedBy    *uuid.UUID `gorm:"column:deleted_by;type:uuid"`
	CreatedAt    time.Time  `gorm:"column:created_at"`
	UpdatedAt    time.Time  `gorm:"column:updated_at"`
	Metadata     string     `gorm:"type:jsonb;default:'{}';column:metadata"`
}

func (FileModel) TableName() string {
	return "files"
}

func NewFileRepository(db *gorm.DB) repositories.FileRepository {
	return &fileRepository{db: db}
}

func (r *fileRepository) Create(ctx context.Context, file *entities.File) error {
	model := r.entityToModel(file)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}

	return nil
}

func (r *fileRepository) GetByID(ctx context.Context, id entities.FileID) (*entities.File, error) {
	var model FileModel
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", uuid.UUID(id)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("file not found with ID: %s", id.String())
		}
		return nil, fmt.Errorf("failed to get file by ID: %w", err)
	}

	file := r.modelToEntity(&model)
	if file == nil {
		return nil, fmt.Errorf("failed to convert model to entity for file ID: %s", id.String())
	}

	return file, nil
}

func (r *fileRepository) GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.File, error) {
	var models []FileModel
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get files by user ID: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) GetByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.File, error) {
	var models []FileModel
	err := r.db.WithContext(ctx).
		Where("team_id = ? AND deleted_at IS NULL", teamID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get files by team ID: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) GetByFolderID(ctx context.Context, folderID entities.FolderID, offset, limit int) ([]*entities.File, error) {
	var models []FileModel
	err := r.db.WithContext(ctx).
		Where("folder_id = ? AND deleted_at IS NULL", uuid.UUID(folderID)).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get files by folder ID: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) GetSharedWithUser(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.File, error) {
	var models []FileModel
	err := r.db.WithContext(ctx).
		Joins("JOIN file_shares ON files.id = file_shares.file_id").
		Where("file_shares.shared_with = ? AND file_shares.expires_at > ? AND files.deleted_at IS NULL", userID, time.Now()).
		Order("files.created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get shared files: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) Update(ctx context.Context, file *entities.File) error {
	model := r.entityToModel(file)

	err := r.db.WithContext(ctx).Save(model).Error
	if err != nil {
		return fmt.Errorf("failed to update file: %w", err)
	}

	return nil
}

func (r *fileRepository) Delete(ctx context.Context, id entities.FileID) error {
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).Delete(&FileModel{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	return nil
}

func (r *fileRepository) Search(ctx context.Context, userID uuid.UUID, query string, offset, limit int) ([]*entities.File, error) {
	var models []FileModel

	// Enhanced sanitization and validation
	if err := r.validateSearchQuery(query); err != nil {
		return nil, fmt.Errorf("invalid search query: %w", err)
	}

	// Use PostgreSQL's full-text search capabilities for better security and performance
	searchQuery := r.buildSecureSearchQuery(query)

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Where("(to_tsvector('english', name || ' ' || COALESCE(path, '')) @@ plainto_tsquery('english', ?) OR name ILIKE ? OR path ILIKE ?)",
			searchQuery, "%"+r.escapeForLike(searchQuery)+"%", "%"+r.escapeForLike(searchQuery)+"%").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to search files: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

// validateSearchQuery validates search query for security
func (r *fileRepository) validateSearchQuery(query string) error {
	if query == "" {
		return fmt.Errorf("search query cannot be empty")
	}

	if len(query) > 200 {
		return fmt.Errorf("search query too long (max 200 characters)")
	}

	// Check for SQL injection patterns
	dangerousPatterns := []string{
		"'", "\"", ";", "--", "/*", "*/", "xp_", "sp_",
		"union", "select", "insert", "update", "delete", "drop", "create", "alter",
		"exec", "execute", "declare", "script", "javascript", "vbscript",
	}

	lowerQuery := strings.ToLower(query)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(lowerQuery, pattern) {
			return fmt.Errorf("search query contains forbidden pattern: %s", pattern)
		}
	}

	// Check for control characters
	for _, r := range query {
		if r < 32 || r == 127 {
			return fmt.Errorf("search query contains control characters")
		}
	}

	return nil
}

// buildSecureSearchQuery builds a secure search query
func (r *fileRepository) buildSecureSearchQuery(query string) string {
	// Remove dangerous characters and normalize
	query = strings.TrimSpace(query)

	// Replace multiple spaces with single space
	query = regexp.MustCompile(`\s+`).ReplaceAllString(query, " ")

	// Remove any remaining potentially dangerous characters
	query = regexp.MustCompile(`[^\w\s.-]`).ReplaceAllString(query, " ")

	return query
}

// escapeForLike properly escapes characters for LIKE queries
func (r *fileRepository) escapeForLike(input string) string {
	// Escape special LIKE characters
	escaped := strings.ReplaceAll(input, "\\", "\\\\")
	escaped = strings.ReplaceAll(escaped, "%", "\\%")
	escaped = strings.ReplaceAll(escaped, "_", "\\_")
	return escaped
}

func (r *fileRepository) GetByChecksum(ctx context.Context, checksum string) (*entities.File, error) {
	var model FileModel
	err := r.db.WithContext(ctx).Where("checksum = ? AND deleted_at IS NULL", checksum).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get file by checksum: %w", err)
	}

	return r.modelToEntity(&model), nil
}

func (r *fileRepository) GetVersions(ctx context.Context, parentID entities.FileID) ([]*entities.File, error) {
	var models []FileModel
	err := r.db.WithContext(ctx).
		Where("parent_id = ? AND deleted_at IS NULL", uuid.UUID(parentID)).
		Order("version DESC").
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get file versions: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FileModel{}).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count files by user ID: %w", err)
	}

	return count, nil
}

func (r *fileRepository) CountByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FileModel{}).
		Where("team_id = ? AND deleted_at IS NULL", teamID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count files by team ID: %w", err)
	}

	return count, nil
}

func (r *fileRepository) GetSizeByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var totalSize int64
	err := r.db.WithContext(ctx).Model(&FileModel{}).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Select("COALESCE(SUM(size), 0)").
		Scan(&totalSize).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get total size by user ID: %w", err)
	}

	return totalSize, nil
}

func (r *fileRepository) GetSizeByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error) {
	var totalSize int64
	err := r.db.WithContext(ctx).Model(&FileModel{}).
		Where("team_id = ? AND deleted_at IS NULL", teamID).
		Select("COALESCE(SUM(size), 0)").
		Scan(&totalSize).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get total size by team ID: %w", err)
	}

	return totalSize, nil
}

func (r *fileRepository) GetExpiredSessions(ctx context.Context, limit int) ([]*entities.File, error) {
	// This would typically be for upload sessions, but keeping it simple for now
	var models []FileModel
	err := r.db.WithContext(ctx).
		Where("status = ? AND updated_at < ?", "processing", time.Now().Add(-24*time.Hour)).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get expired sessions: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) GetRootFilesByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.File, error) {
	var models []FileModel
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND folder_id IS NULL AND deleted_at IS NULL", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get root files by user ID: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) GetRootFilesByTeamID(ctx context.Context, teamID uuid.UUID, offset, limit int) ([]*entities.File, error) {
	var models []FileModel
	err := r.db.WithContext(ctx).
		Where("team_id = ? AND folder_id IS NULL AND deleted_at IS NULL", teamID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get root files by team ID: %w", err)
	}

	files := make([]*entities.File, len(models))
	for i, model := range models {
		files[i] = r.modelToEntity(&model)
	}

	return files, nil
}

func (r *fileRepository) CountRootFilesByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FileModel{}).
		Where("user_id = ? AND folder_id IS NULL AND deleted_at IS NULL", userID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count root files by user ID: %w", err)
	}

	return count, nil
}

func (r *fileRepository) CountRootFilesByTeamID(ctx context.Context, teamID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&FileModel{}).
		Where("team_id = ? AND folder_id IS NULL AND deleted_at IS NULL", teamID).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count root files by team ID: %w", err)
	}

	return count, nil
}

func (r *fileRepository) entityToModel(file *entities.File) *FileModel {
	userID := file.UserID()
	model := &FileModel{
		ID:           uuid.UUID(file.ID()),
		Name:         file.Name(),
		OriginalName: file.Name(), // Use Name as OriginalName for now
		Path:         file.Path(),
		URL:          file.Path(), // Use Path as URL for now
		Size:         file.Size(),
		MimeType:     file.MimeType(),
		Checksum:     file.Checksum(),
		UserID:       userID,
		OwnerID:      userID,
		Status:       string(file.Status()),
		Visibility:   string(file.Visibility()),
		Version:      int64(file.Version()),
		IsLatest:     true,  // Default to true for new files
		IsDeleted:    false, // Default to false for new files
		IsPublic:     file.Visibility() == entities.VisibilityPublic,
		Metadata:     marshalMetadata(file.Metadata()),
		CreatedAt:    file.CreatedAt(),
		UpdatedAt:    file.UpdatedAt(),
	}

	if file.TeamID() != nil {
		model.TeamID = file.TeamID()
	}

	if file.FolderID() != nil {
		folderUUID := uuid.UUID(*file.FolderID())
		model.FolderID = &folderUUID
	}

	if file.ParentID() != nil {
		parentUUID := uuid.UUID(*file.ParentID())
		model.ParentID = &parentUUID
	}

	if file.DeletedAt() != nil {
		model.DeletedAt = file.DeletedAt()
		model.IsDeleted = true
	}

	return model
}

func (r *fileRepository) modelToEntity(model *FileModel) *entities.File {
	// Convert pointers for optional fields
	var teamID *uuid.UUID
	if model.TeamID != nil {
		teamID = model.TeamID
	}

	var folderID *entities.FolderID
	if model.FolderID != nil {
		fid := entities.FolderID(*model.FolderID)
		folderID = &fid
	}

	var parentID *entities.FileID
	if model.ParentID != nil {
		pid := entities.FileID(*model.ParentID)
		parentID = &pid
	}

	// Parse metadata
	metadata := unmarshalMetadata(model.Metadata)

	// Use ReconstructFile to properly create entity from database data
	file := entities.ReconstructFile(
		entities.FileID(model.ID),
		model.Name,
		model.Path,
		model.MimeType,
		model.Checksum,
		model.Size,
		model.UserID,
		teamID,
		folderID,
		parentID,
		entities.FileStatus(model.Status),
		entities.Visibility(model.Visibility),
		int(model.Version),
		metadata,
		model.CreatedAt,
		model.UpdatedAt,
		model.DeletedAt,
	)

	return file
}
