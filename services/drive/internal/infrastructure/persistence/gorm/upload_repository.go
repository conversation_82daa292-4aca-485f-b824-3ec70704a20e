package gorm

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/swork-team/platform/services/drive/internal/domain/upload/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/upload/repositories"
)

type uploadRepository struct {
	db *gorm.DB
}

// Helper functions for uploaded chunks JSON handling
func marshalUploadedChunks(chunks map[string]interface{}) string {
	if chunks == nil {
		return "{}"
	}
	bytes, err := json.Marshal(chunks)
	if err != nil {
		return "{}"
	}
	return string(bytes)
}

func unmarshalUploadedChunks(chunksStr string) map[string]interface{} {
	if chunksStr == "" || chunksStr == "{}" {
		return make(map[string]interface{})
	}
	var chunks map[string]interface{}
	if err := json.Unmarshal([]byte(chunksStr), &chunks); err != nil {
		return make(map[string]interface{})
	}
	return chunks
}

type UploadSessionModel struct {
	ID             uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID         uuid.UUID `gorm:"type:uuid;not null;index"`
	FileName       string    `gorm:"not null"`
	FileSize       int64     `gorm:"not null"`
	ChunkSize      int64     `gorm:"not null"`
	TotalChunks    int64     `gorm:"not null"`
	UploadedChunks string    `gorm:"type:jsonb;default:'{}'"`
	Status         string    `gorm:"not null;default:'active'"`
	ExpiresAt      time.Time `gorm:"not null"`
	CreatedAt      time.Time `gorm:"not null"`
	UpdatedAt      time.Time `gorm:"not null"`
}

func (UploadSessionModel) TableName() string {
	return "upload_sessions"
}

func NewUploadRepository(db *gorm.DB) repositories.UploadRepository {
	return &uploadRepository{db: db}
}

func (r *uploadRepository) Create(ctx context.Context, session *entities.UploadSession) error {
	model := r.entityToModel(session)

	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		return fmt.Errorf("failed to create upload session: %w", err)
	}

	return nil
}

func (r *uploadRepository) GetByID(ctx context.Context, id entities.UploadSessionID) (*entities.UploadSession, error) {
	var model UploadSessionModel
	err := r.db.WithContext(ctx).Where("id = ?", uuid.UUID(id)).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repositories.ErrUploadSessionNotFound
		}
		return nil, fmt.Errorf("failed to get upload session: %w", err)
	}

	entity, err := r.modelToEntity(&model)
	if err != nil {
		return nil, fmt.Errorf("failed to convert model to entity: %w", err)
	}
	return entity, nil
}

func (r *uploadRepository) GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.UploadSession, error) {
	var models []UploadSessionModel
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get upload sessions by user: %w", err)
	}

	sessions := make([]*entities.UploadSession, 0, len(models))
	for _, model := range models {
		entity, err := r.modelToEntity(&model)
		if err != nil {
			continue // Skip invalid entities
		}
		sessions = append(sessions, entity)
	}

	return sessions, nil
}

func (r *uploadRepository) Update(ctx context.Context, session *entities.UploadSession) error {
	model := r.entityToModel(session)
	
	// Use raw SQL update to ensure it works correctly
	query := `UPDATE upload_sessions SET 
		uploaded_chunks = ?, 
		status = ?, 
		expires_at = ?, 
		updated_at = ? 
		WHERE id = ?`
	
	result := r.db.WithContext(ctx).Exec(query,
		model.UploadedChunks,
		model.Status,
		model.ExpiresAt,
		model.UpdatedAt,
		model.ID,
	)
	
	if result.Error != nil {
		return fmt.Errorf("failed to update upload session: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no upload session found with ID %s to update", model.ID.String())
	}

	return nil
}

func (r *uploadRepository) Delete(ctx context.Context, id entities.UploadSessionID) error {
	result := r.db.WithContext(ctx).Delete(&UploadSessionModel{}, "id = ?", uuid.UUID(id))
	if result.Error != nil {
		return fmt.Errorf("failed to delete upload session: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return repositories.ErrUploadSessionNotFound
	}
	return nil
}

func (r *uploadRepository) GetExpiredSessions(ctx context.Context, limit int) ([]*entities.UploadSession, error) {
	var models []UploadSessionModel
	err := r.db.WithContext(ctx).
		Where("expires_at < ? OR status = ?", time.Now(), "expired").
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get expired upload sessions: %w", err)
	}

	sessions := make([]*entities.UploadSession, 0, len(models))
	for _, model := range models {
		entity, err := r.modelToEntity(&model)
		if err != nil {
			continue // Skip invalid entities
		}
		sessions = append(sessions, entity)
	}

	return sessions, nil
}

func (r *uploadRepository) GetActiveSessions(ctx context.Context, userID uuid.UUID) ([]*entities.UploadSession, error) {
	var models []UploadSessionModel
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = ? AND expires_at > ?", userID, "active", time.Now()).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get active upload sessions: %w", err)
	}

	sessions := make([]*entities.UploadSession, 0, len(models))
	for _, model := range models {
		entity, err := r.modelToEntity(&model)
		if err != nil {
			continue // Skip invalid entities
		}
		sessions = append(sessions, entity)
	}

	return sessions, nil
}

func (r *uploadRepository) GetCompletedSessions(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*entities.UploadSession, error) {
	var models []UploadSessionModel
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, "completed").
		Order("updated_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get completed upload sessions: %w", err)
	}

	sessions := make([]*entities.UploadSession, 0, len(models))
	for _, model := range models {
		entity, err := r.modelToEntity(&model)
		if err != nil {
			continue // Skip invalid entities
		}
		sessions = append(sessions, entity)
	}

	return sessions, nil
}

func (r *uploadRepository) CleanupExpiredSessions(ctx context.Context) error {
	result := r.db.WithContext(ctx).
		Where("expires_at < ? OR status IN (?)", time.Now(), []string{"expired", "completed"}).
		Delete(&UploadSessionModel{})

	if result.Error != nil {
		return fmt.Errorf("failed to cleanup expired sessions: %w", result.Error)
	}

	return nil
}

func (r *uploadRepository) entityToModel(session *entities.UploadSession) *UploadSessionModel {
	// Convert uploaded chunks map[int64]bool to JSON string
	uploadedChunks := make(map[string]interface{})
	for chunk, uploaded := range session.UploadedChunks() {
		uploadedChunks[fmt.Sprintf("%d", chunk)] = uploaded
	}

	return &UploadSessionModel{
		ID:             uuid.UUID(session.ID()),
		UserID:         session.UserID(),
		FileName:       session.FileName(),
		FileSize:       session.FileSize(),
		ChunkSize:      session.ChunkSize(),
		TotalChunks:    session.TotalChunks(),
		UploadedChunks: marshalUploadedChunks(uploadedChunks),
		Status:         string(session.Status()),
		ExpiresAt:      session.ExpiresAt(),
		CreatedAt:      session.CreatedAt(),
		UpdatedAt:      session.UpdatedAt(),
	}
}

func (r *uploadRepository) modelToEntity(model *UploadSessionModel) (*entities.UploadSession, error) {
	sessionID := entities.UploadSessionID(model.ID)
	status := entities.UploadSessionStatus(model.Status)
	
	// Convert uploaded chunks from JSON to map[int64]bool
	uploadedChunks := make(map[int64]bool)
	if model.UploadedChunks != "" && model.UploadedChunks != "{}" {
		chunksInterface := unmarshalUploadedChunks(model.UploadedChunks)
		for chunkStr, uploadedInterface := range chunksInterface {
			chunkIndex, err := strconv.ParseInt(chunkStr, 10, 64)
			if err != nil {
				continue // Skip invalid chunk indices
			}

			uploaded, ok := uploadedInterface.(bool)
			if !ok {
				continue // Skip non-boolean values
			}

			uploadedChunks[chunkIndex] = uploaded
		}
	}

	// Use ReconstructUploadSession to properly restore all fields
	return entities.ReconstructUploadSession(
		sessionID,
		model.UserID,
		model.FileName,
		model.FileSize,
		model.ChunkSize,
		model.TotalChunks,
		uploadedChunks,
		status,
		model.ExpiresAt,
		model.CreatedAt,
		model.UpdatedAt,
	), nil
}
