package storage

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"strings"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
	"github.com/swork-team/platform/services/drive/internal/domain/thumbnail/entities"
)

// ThumbnailStorageService provides storage operations specifically for thumbnails
type ThumbnailStorageService struct {
	storageManager *StorageManager
	pathGenerator  *ThumbnailPathGenerator
}

// ThumbnailPathGenerator generates storage paths for thumbnails
type ThumbnailPathGenerator struct {
	basePathGenerator PathGenerator
}

// NewThumbnailStorageService creates a new thumbnail storage service
func NewThumbnailStorageService(storageManager *StorageManager) *ThumbnailStorageService {
	return &ThumbnailStorageService{
		storageManager: storageManager,
		pathGenerator:  NewThumbnailPathGenerator(storageManager.pathGenerator),
	}
}

// NewThumbnailPathGenerator creates a new thumbnail path generator
func NewThumbnailPathGenerator(baseGenerator PathGenerator) *ThumbnailPathGenerator {
	return &ThumbnailPathGenerator{
		basePathGenerator: baseGenerator,
	}
}

// GenerateThumbnailPath generates a storage path for a thumbnail
func (tpg *ThumbnailPathGenerator) GenerateThumbnailPath(
	fileID uuid.UUID,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) string {
	// Create a filename for the thumbnail
	fileName := fmt.Sprintf("%s_%s.%s", fileID.String(), string(size), string(format))

	// Use the base path generator to create a path structure
	basePath := tpg.basePathGenerator.GeneratePath(fileName, getMimeTypeForFormat(format), "")

	// Prepend with thumbnails/ directory
	return fmt.Sprintf("thumbnails/%s", basePath)
}

// GenerateThumbnailPathFromOriginal generates a thumbnail path based on the original file path
func (tpg *ThumbnailPathGenerator) GenerateThumbnailPathFromOriginal(
	originalPath string,
	fileID uuid.UUID,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) string {
	// Extract directory structure from original path
	dir := filepath.Dir(originalPath)
	if dir == "." {
		dir = ""
	}

	// Create thumbnail filename
	fileName := fmt.Sprintf("%s_%s.%s", fileID.String(), string(size), string(format))

	// Combine with thumbnails prefix
	if dir != "" {
		return fmt.Sprintf("thumbnails/%s/%s", dir, fileName)
	}
	return fmt.Sprintf("thumbnails/%s", fileName)
}

// SanitizePath sanitizes a thumbnail path
func (tpg *ThumbnailPathGenerator) SanitizePath(path string) string {
	return tpg.basePathGenerator.SanitizePath(path)
}

// UploadThumbnail uploads a thumbnail to storage
func (ts *ThumbnailStorageService) UploadThumbnail(
	ctx context.Context,
	content io.Reader,
	fileID uuid.UUID,
	size entities.ThumbnailSize,
	format entities.ThumbnailFormat,
) (string, error) {
	// Generate thumbnail path
	path := ts.pathGenerator.GenerateThumbnailPath(fileID, size, format)

	// Get MIME type for the format
	mimeType := getMimeTypeForFormat(format)

	// Upload using the storage manager with the specific path
	return ts.uploadToPath(ctx, content, path, mimeType)
}

// UploadThumbnailToPath uploads a thumbnail to a specific path
func (ts *ThumbnailStorageService) UploadThumbnailToPath(
	ctx context.Context,
	content io.Reader,
	path string,
	format entities.ThumbnailFormat,
) error {
	// Ensure path is in thumbnails directory
	if !strings.HasPrefix(path, "thumbnails/") {
		path = fmt.Sprintf("thumbnails/%s", path)
	}

	// Sanitize the path
	path = ts.pathGenerator.SanitizePath(path)

	// Get MIME type for the format
	mimeType := getMimeTypeForFormat(format)

	// Upload using the storage manager
	_, err := ts.uploadToPath(ctx, content, path, mimeType)
	return err
}

// DownloadThumbnail downloads a thumbnail from storage
func (ts *ThumbnailStorageService) DownloadThumbnail(ctx context.Context, path string) (io.ReadCloser, error) {
	// Ensure path is in thumbnails directory
	if !strings.HasPrefix(path, "thumbnails/") {
		path = fmt.Sprintf("thumbnails/%s", path)
	}

	return ts.storageManager.Download(ctx, path)
}

// DeleteThumbnail deletes a thumbnail from storage
func (ts *ThumbnailStorageService) DeleteThumbnail(ctx context.Context, path string) error {
	// Ensure path is in thumbnails directory
	if !strings.HasPrefix(path, "thumbnails/") {
		path = fmt.Sprintf("thumbnails/%s", path)
	}

	return ts.storageManager.Delete(ctx, path)
}

// DeleteThumbnailsByFileID deletes all thumbnails for a specific file
func (ts *ThumbnailStorageService) DeleteThumbnailsByFileID(ctx context.Context, fileID uuid.UUID) error {
	// This would require listing functionality which might not be available in all storage providers
	// For now, we'll return nil and let the cleanup process handle orphaned thumbnails
	// TODO: Implement when storage providers support listing by prefix
	return nil
}

// GenerateDownloadURL generates a download URL for a thumbnail
func (ts *ThumbnailStorageService) GenerateDownloadURL(ctx context.Context, path string, expiresInSeconds int) (string, error) {
	// Ensure path is in thumbnails directory
	if !strings.HasPrefix(path, "thumbnails/") {
		path = fmt.Sprintf("thumbnails/%s", path)
	}

	return ts.storageManager.GenerateDownloadURL(ctx, path, expiresInSeconds)
}

// GetThumbnailInfo gets information about a thumbnail file
func (ts *ThumbnailStorageService) GetThumbnailInfo(ctx context.Context, path string) (*ports.FileInfo, error) {
	// Ensure path is in thumbnails directory
	if !strings.HasPrefix(path, "thumbnails/") {
		path = fmt.Sprintf("thumbnails/%s", path)
	}

	return ts.storageManager.GetFileInfo(ctx, path)
}

// ThumbnailExists checks if a thumbnail exists in storage
func (ts *ThumbnailStorageService) ThumbnailExists(ctx context.Context, path string) (bool, error) {
	// Ensure path is in thumbnails directory
	if !strings.HasPrefix(path, "thumbnails/") {
		path = fmt.Sprintf("thumbnails/%s", path)
	}

	return ts.storageManager.Exists(ctx, path)
}

// CopyThumbnail copies a thumbnail from one path to another
func (ts *ThumbnailStorageService) CopyThumbnail(ctx context.Context, sourcePath, destinationPath string) error {
	// Ensure both paths are in thumbnails directory
	if !strings.HasPrefix(sourcePath, "thumbnails/") {
		sourcePath = fmt.Sprintf("thumbnails/%s", sourcePath)
	}
	if !strings.HasPrefix(destinationPath, "thumbnails/") {
		destinationPath = fmt.Sprintf("thumbnails/%s", destinationPath)
	}

	return ts.storageManager.Copy(ctx, sourcePath, destinationPath)
}

// MoveThumbnail moves a thumbnail from one path to another
func (ts *ThumbnailStorageService) MoveThumbnail(ctx context.Context, sourcePath, destinationPath string) error {
	// Ensure both paths are in thumbnails directory
	if !strings.HasPrefix(sourcePath, "thumbnails/") {
		sourcePath = fmt.Sprintf("thumbnails/%s", sourcePath)
	}
	if !strings.HasPrefix(destinationPath, "thumbnails/") {
		destinationPath = fmt.Sprintf("thumbnails/%s", destinationPath)
	}

	return ts.storageManager.Move(ctx, sourcePath, destinationPath)
}

// uploadToPath is a helper method to upload content to a specific path
func (ts *ThumbnailStorageService) uploadToPath(ctx context.Context, content io.Reader, path, mimeType string) (string, error) {
	// We need to add a method to the storage manager to upload to a specific path
	// For now, we'll use the UploadToPath method that we need to add to the storage manager
	return ts.storageManager.UploadToPath(ctx, content, path, mimeType)
}

// getMimeTypeForFormat returns the MIME type for a thumbnail format
func getMimeTypeForFormat(format entities.ThumbnailFormat) string {
	switch format {
	case entities.ThumbnailFormatJPEG:
		return "image/jpeg"
	case entities.ThumbnailFormatPNG:
		return "image/png"
	case entities.ThumbnailFormatWebP:
		return "image/webp"
	case entities.ThumbnailFormatAVIF:
		return "image/avif"
	default:
		return "image/jpeg" // Default to JPEG
	}
}
