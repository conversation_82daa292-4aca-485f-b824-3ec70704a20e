package storage

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
)

type MinIOProvider struct {
	client     *minio.Client
	bucketName string
	config     MinIOConfig
	mu         sync.RWMutex
	healthy    bool
}

type MinIOConfig struct {
	Endpoint            string
	AccessKeyID         string
	SecretAccessKey     string
	BucketName          string
	UseSSL              bool
	Region              string
	MaxRetries          int
	RetryDelay          time.Duration
	UploadTimeout       time.Duration
	DownloadTimeout     time.Duration
	HealthCheckInterval time.Duration
}

func NewMinIOProvider(config MinIOConfig) (*MinIOProvider, error) {
	// Initialize minio client
	client, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, ""),
		Secure: config.UseSSL,
		Region: config.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}

	provider := &MinIOProvider{
		client:     client,
		bucketName: config.BucketName,
		config:     config,
		healthy:    true,
	}

	// Check if bucket exists, create if not
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	exists, err := client.BucketExists(ctx, config.BucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to check bucket existence: %w", err)
	}

	if !exists {
		err = client.MakeBucket(ctx, config.BucketName, minio.MakeBucketOptions{Region: config.Region})
		if err != nil {
			return nil, fmt.Errorf("failed to create bucket: %w", err)
		}
	}

	return provider, nil
}

func (mp *MinIOProvider) Name() string {
	return "minio"
}

func (mp *MinIOProvider) IsHealthy(ctx context.Context) bool {
	// Perform real-time health check instead of relying on cached status
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Try to list objects to verify connectivity
	objectCh := mp.client.ListObjects(ctx, mp.bucketName, minio.ListObjectsOptions{MaxKeys: 1})

	select {
	case obj := <-objectCh:
		if obj.Err != nil {
			mp.setHealthy(false)
			return false
		}
		mp.setHealthy(true)
		return true
	case <-ctx.Done():
		mp.setHealthy(false)
		return false
	}
}

func (mp *MinIOProvider) Upload(ctx context.Context, content io.Reader, path, mimeType string) error {
	ctx, cancel := context.WithTimeout(ctx, mp.config.UploadTimeout)
	defer cancel()

	// Prepare upload options
	opts := minio.PutObjectOptions{
		ContentType: mimeType,
	}

	var lastErr error
	for attempt := 0; attempt <= mp.config.MaxRetries; attempt++ {
		// For non-seekable readers, we need to read the content first
		// This is a limitation of the retry mechanism
		var reader io.Reader = content
		var size int64 = -1

		// Upload the object
		info, err := mp.client.PutObject(ctx, mp.bucketName, path, reader, size, opts)
		if err != nil {
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		// Upload successful
		mp.setHealthy(true)
		fmt.Printf("Successfully uploaded %s of size %d\n", info.Key, info.Size)
		return nil
	}

	mp.setHealthy(false)
	return fmt.Errorf("failed to upload after %d attempts: %w", mp.config.MaxRetries+1, lastErr)
}

func (mp *MinIOProvider) Download(ctx context.Context, path string) (io.ReadCloser, error) {
	// Use background context to avoid parent timeout cancellation
	// This prevents the reader from being cancelled when the parent HTTP context times out
	downloadCtx, cancel := context.WithTimeout(context.Background(), mp.config.DownloadTimeout)
	defer cancel()

	var lastErr error
	for attempt := 0; attempt <= mp.config.MaxRetries; attempt++ {
		object, err := mp.client.GetObject(downloadCtx, mp.bucketName, path, minio.GetObjectOptions{})
		if err != nil {
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		// Verify the object can be read by checking its stat
		_, err = object.Stat()
		if err != nil {
			object.Close()
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		// Read the entire content into memory to avoid context cancellation issues
		// The MinIO reader is bound to the context and gets cancelled when the context times out
		buffer := make([]byte, 0, 10*1024*1024) // Start with 10MB capacity
		tempBuf := make([]byte, 64*1024)        // 64KB chunks
		
		for {
			n, readErr := object.Read(tempBuf)
			if n > 0 {
				buffer = append(buffer, tempBuf[:n]...)
			}
			if readErr != nil {
				if readErr == io.EOF {
					break
				}
				object.Close()
				lastErr = readErr
				if attempt < mp.config.MaxRetries {
					time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
					continue
				}
				break
			}
		}
		
		object.Close()
		
		// Return a bytes reader wrapped in a ReadCloser
		mp.setHealthy(true)
		return &bytesReadCloser{bytes.NewReader(buffer)}, nil
	}

	mp.setHealthy(false)
	return nil, fmt.Errorf("failed to download after %d attempts: %w", mp.config.MaxRetries+1, lastErr)
}

func (mp *MinIOProvider) Delete(ctx context.Context, path string) error {
	var lastErr error
	for attempt := 0; attempt <= mp.config.MaxRetries; attempt++ {
		err := mp.client.RemoveObject(ctx, mp.bucketName, path, minio.RemoveObjectOptions{})
		if err != nil {
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		mp.setHealthy(true)
		return nil
	}

	mp.setHealthy(false)
	return fmt.Errorf("failed to delete after %d attempts: %w", mp.config.MaxRetries+1, lastErr)
}

func (mp *MinIOProvider) GeneratePresignedURL(ctx context.Context, path string, expiresInSeconds int) (string, error) {
	expires := time.Duration(expiresInSeconds) * time.Second

	var lastErr error
	for attempt := 0; attempt <= mp.config.MaxRetries; attempt++ {
		url, err := mp.client.PresignedGetObject(ctx, mp.bucketName, path, expires, nil)
		if err != nil {
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		mp.setHealthy(true)
		return url.String(), nil
	}

	mp.setHealthy(false)
	return "", fmt.Errorf("failed to generate presigned URL after %d attempts: %w", mp.config.MaxRetries+1, lastErr)
}

func (mp *MinIOProvider) GetObjectInfo(ctx context.Context, path string) (*ports.FileInfo, error) {
	var lastErr error
	for attempt := 0; attempt <= mp.config.MaxRetries; attempt++ {
		objInfo, err := mp.client.StatObject(ctx, mp.bucketName, path, minio.StatObjectOptions{})
		if err != nil {
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		fileInfo := &ports.FileInfo{
			Path:         path,
			Size:         objInfo.Size,
			ContentType:  objInfo.ContentType,
			LastModified: objInfo.LastModified.Format(time.RFC3339),
			ETag:         objInfo.ETag,
		}

		mp.setHealthy(true)
		return fileInfo, nil
	}

	mp.setHealthy(false)
	return nil, fmt.Errorf("failed to get object info after %d attempts: %w", mp.config.MaxRetries+1, lastErr)
}

func (mp *MinIOProvider) Exists(ctx context.Context, path string) (bool, error) {
	var lastErr error
	for attempt := 0; attempt <= mp.config.MaxRetries; attempt++ {
		_, err := mp.client.StatObject(ctx, mp.bucketName, path, minio.StatObjectOptions{})
		if err != nil {
			// Check if it's a not found error
			if mp.isNotFoundError(err) {
				return false, nil
			}
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		mp.setHealthy(true)
		return true, nil
	}

	mp.setHealthy(false)
	return false, fmt.Errorf("failed to check existence after %d attempts: %w", mp.config.MaxRetries+1, lastErr)
}

func (mp *MinIOProvider) Copy(ctx context.Context, sourcePath, destinationPath string) error {
	// Use background context to avoid parent timeout cancellation
	copyCtx, cancel := context.WithTimeout(context.Background(), mp.config.DownloadTimeout)
	defer cancel()

	var lastErr error
	for attempt := 0; attempt <= mp.config.MaxRetries; attempt++ {
		// Source object specification
		srcOpts := minio.CopySrcOptions{
			Bucket: mp.bucketName,
			Object: sourcePath,
		}

		// Destination object specification
		dstOpts := minio.CopyDestOptions{
			Bucket: mp.bucketName,
			Object: destinationPath,
		}

		// Copy the object
		_, err := mp.client.CopyObject(copyCtx, dstOpts, srcOpts)
		if err != nil {
			lastErr = err
			if attempt < mp.config.MaxRetries {
				time.Sleep(mp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		mp.setHealthy(true)
		return nil
	}

	mp.setHealthy(false)
	return fmt.Errorf("failed to copy after %d attempts: %w", mp.config.MaxRetries+1, lastErr)
}

func (mp *MinIOProvider) Move(ctx context.Context, sourcePath, destinationPath string) error {
	// Copy the object to the new location
	err := mp.Copy(ctx, sourcePath, destinationPath)
	if err != nil {
		return err
	}

	// Delete the original object
	err = mp.Delete(ctx, sourcePath)
	if err != nil {
		// Try to clean up the copied file
		mp.Delete(ctx, destinationPath)
		return err
	}

	return nil
}

func (mp *MinIOProvider) setHealthy(healthy bool) {
	mp.mu.Lock()
	defer mp.mu.Unlock()
	mp.healthy = healthy
}

func (mp *MinIOProvider) performHealthCheck() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Try to list objects to check health
	objectCh := mp.client.ListObjects(ctx, mp.bucketName, minio.ListObjectsOptions{MaxKeys: 1})

	// Check if we can successfully iterate (even if no objects exist)
	select {
	case obj := <-objectCh:
		if obj.Err != nil {
			mp.setHealthy(false)
		} else {
			mp.setHealthy(true)
		}
	case <-ctx.Done():
		mp.setHealthy(false)
	}
}

// isNotFoundError checks if the error indicates a file not found
func (mp *MinIOProvider) isNotFoundError(err error) bool {
	if err == nil {
		return false
	}

	// Check for MinIO specific error patterns
	errorStr := err.Error()
	return strings.Contains(strings.ToLower(errorStr), "not found") ||
		strings.Contains(strings.ToLower(errorStr), "no such key") ||
		strings.Contains(strings.ToLower(errorStr), "404") ||
		strings.Contains(strings.ToLower(errorStr), "does not exist")
}

func (mp *MinIOProvider) StartHealthCheck() {
	ticker := time.NewTicker(mp.config.HealthCheckInterval)
	go func() {
		defer ticker.Stop()
		for range ticker.C {
			mp.performHealthCheck()
		}
	}()
}

// bytesReadCloser wraps a bytes.Reader to implement io.ReadCloser
type bytesReadCloser struct {
	*bytes.Reader
}

func (brc *bytesReadCloser) Close() error {
	return nil // No-op for bytes.Reader
}
