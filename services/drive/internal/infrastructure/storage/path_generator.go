package storage

import (
	"crypto/md5"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

type PathGeneratorImpl struct {
	config PathGeneratorConfig
}

type PathGeneratorConfig struct {
	UseTimestamp    bool
	UseContentHash  bool
	UseRandomSuffix bool
	MaxDepth        int
	SanitizeNames   bool
}

func NewPathGenerator(config PathGeneratorConfig) *PathGeneratorImpl {
	return &PathGeneratorImpl{
		config: config,
	}
}

func (pg *PathGeneratorImpl) GeneratePath(fileName, mimeType, contentHash string) string {
	// Sanitize filename
	if pg.config.SanitizeNames {
		fileName = pg.sanitizeFileName(fileName)
	}

	// Generate directory structure
	var pathParts []string

	// Add timestamp-based directory structure
	if pg.config.UseTimestamp {
		now := time.Now()
		pathParts = append(pathParts, fmt.Sprintf("%d", now.Year()))
		pathParts = append(pathParts, fmt.Sprintf("%02d", now.Month()))
		pathParts = append(pathParts, fmt.Sprintf("%02d", now.Day()))
	}

	// Add content hash-based directory structure
	if pg.config.UseContentHash && contentHash != "" {
		// Use first few characters of content hash for directory structure
		if len(contentHash) >= 4 {
			pathParts = append(pathParts, contentHash[:2])
			pathParts = append(pathParts, contentHash[2:4])
		}
	}

	// Generate final filename
	finalName := fileName
	if pg.config.UseContentHash && contentHash != "" {
		ext := filepath.Ext(fileName)
		nameWithoutExt := strings.TrimSuffix(fileName, ext)
		finalName = fmt.Sprintf("%s_%s%s", nameWithoutExt, contentHash[:8], ext)
	}

	// Combine all parts
	pathParts = append(pathParts, finalName)
	return strings.Join(pathParts, "/")
}

func (pg *PathGeneratorImpl) SanitizePath(path string) string {
	// Remove any path traversal attempts
	path = strings.ReplaceAll(path, "..", "")
	path = strings.ReplaceAll(path, "\\", "/")

	// Remove leading slashes
	path = strings.TrimPrefix(path, "/")

	// Split into parts and sanitize each
	parts := strings.Split(path, "/")
	var sanitizedParts []string

	for _, part := range parts {
		sanitized := pg.sanitizeFileName(part)
		if sanitized != "" {
			sanitizedParts = append(sanitizedParts, sanitized)
		}
	}

	return strings.Join(sanitizedParts, "/")
}

func (pg *PathGeneratorImpl) sanitizeFileName(fileName string) string {
	// Remove or replace dangerous characters
	reg := regexp.MustCompile(`[<>:"/\\|?*\x00-\x1f]`)
	fileName = reg.ReplaceAllString(fileName, "_")

	// Remove leading/trailing spaces and dots
	fileName = strings.Trim(fileName, " .")

	// Ensure filename is not empty
	if fileName == "" {
		fileName = "unnamed_file"
	}

	// Limit filename length
	if len(fileName) > 255 {
		ext := filepath.Ext(fileName)
		nameWithoutExt := strings.TrimSuffix(fileName, ext)
		maxNameLen := 255 - len(ext)
		if maxNameLen > 0 {
			fileName = nameWithoutExt[:maxNameLen] + ext
		} else {
			fileName = nameWithoutExt[:255]
		}
	}

	return fileName
}

func (pg *PathGeneratorImpl) GenerateUniqueFileName(baseName string) string {
	timestamp := time.Now().Unix()
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)

	// Create hash from name and timestamp
	hash := md5.Sum([]byte(fmt.Sprintf("%s_%d", nameWithoutExt, timestamp)))
	hashStr := fmt.Sprintf("%x", hash)[:8]

	return fmt.Sprintf("%s_%s%s", nameWithoutExt, hashStr, ext)
}

func (pg *PathGeneratorImpl) ExtractFileNameFromPath(path string) string {
	return filepath.Base(path)
}

func (pg *PathGeneratorImpl) GetDirectoryFromPath(path string) string {
	return filepath.Dir(path)
}

func (pg *PathGeneratorImpl) IsValidPath(path string) bool {
	// Check for path traversal attempts
	if strings.Contains(path, "..") {
		return false
	}

	// Check for null bytes
	if strings.Contains(path, "\x00") {
		return false
	}

	// Check for excessive path length
	if len(path) > 1024 {
		return false
	}

	return true
}
