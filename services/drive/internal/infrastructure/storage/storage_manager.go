package storage

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"sync"
	"time"

	"github.com/swork-team/platform/services/drive/internal/application/ports"
)

type StorageManager struct {
	primary        ports.StorageProvider
	replica        ports.StorageProvider
	pathGenerator  PathGenerator
	healthChecker  *HealthChecker
	syncQueue      chan SyncTask
	syncWorkers    int
	mu             sync.RWMutex
	primaryHealthy bool
	replicaHealthy bool
	config         StorageConfig
}

type StorageConfig struct {
	PrimaryProvider     string
	ReplicaProvider     string
	SyncEnabled         bool
	SyncWorkers         int
	HealthCheckInterval time.Duration
	RetryAttempts       int
	RetryDelay          time.Duration
}

type SyncTask struct {
	Path     string
	MimeType string
	Content  io.Reader
	Retry    int
}

type PathGenerator interface {
	GeneratePath(fileName, mimeType, contentHash string) string
	SanitizePath(path string) string
}

type HealthChecker struct {
	primary        ports.StorageProvider
	replica        ports.StorageProvider
	checkInterval  time.Duration
	statusCallback func(primary, replica bool)
	stop<PERSON>han       chan struct{}
	mu             sync.RWMutex
	primaryHealthy bool
	replicaHealthy bool
}

func NewStorageManager(primary, replica ports.StorageProvider, pathGen PathGenerator, config StorageConfig) *StorageManager {
	manager := &StorageManager{
		primary:        primary,
		replica:        replica,
		pathGenerator:  pathGen,
		syncQueue:      make(chan SyncTask, 1000),
		syncWorkers:    config.SyncWorkers,
		primaryHealthy: true,
		replicaHealthy: true,
		config:         config,
	}

	// Initialize health checker
	manager.healthChecker = &HealthChecker{
		primary:        primary,
		replica:        replica,
		checkInterval:  config.HealthCheckInterval,
		statusCallback: manager.updateHealthStatus,
		stopChan:       make(chan struct{}),
		primaryHealthy: true,
		replicaHealthy: true,
	}

	// Start background workers
	manager.startSyncWorkers()
	manager.startHealthChecker()

	return manager
}

func (sm *StorageManager) Upload(ctx context.Context, content io.Reader, fileName, mimeType string) (string, error) {
	// Generate path
	path := sm.pathGenerator.GeneratePath(fileName, mimeType, "")

	// Read content into buffer to enable retry and replication
	var contentBuffer []byte
	if seekableContent, ok := content.(io.Seeker); ok {
		// If content is seekable, we can retry without reading to memory
		contentBuffer = nil
		_ = seekableContent // Use the seekable content directly
	} else {
		// Read content to buffer for retry capability
		buffer := make([]byte, 0, 1024*1024) // Start with 1MB buffer
		tempBuffer := make([]byte, 32*1024)  // 32KB read chunks
		for {
			n, err := content.Read(tempBuffer)
			if n > 0 {
				buffer = append(buffer, tempBuffer[:n]...)
			}
			if err == io.EOF {
				break
			}
			if err != nil {
				return "", fmt.Errorf("failed to read upload content: %w", err)
			}
		}
		contentBuffer = buffer
	}

	// Try primary first
	if sm.isPrimaryHealthy() {
		var uploadContent io.Reader
		if contentBuffer != nil {
			uploadContent = bytes.NewReader(contentBuffer)
		} else {
			uploadContent = content
		}

		err := sm.primary.Upload(ctx, uploadContent, path, mimeType)
		if err == nil {
			// Queue for replica sync if enabled (thread-safe)
			if sm.config.SyncEnabled && sm.replica != nil && contentBuffer != nil {
				// Use a copy of the buffer for async sync
				syncBuffer := make([]byte, len(contentBuffer))
				copy(syncBuffer, contentBuffer)
				sm.queueSyncTaskSafe(path, mimeType, bytes.NewReader(syncBuffer))
			}
			return path, nil
		}
	}

	// Fallback to replica if primary fails
	if sm.replica != nil && sm.isReplicaHealthy() {
		var uploadContent io.Reader
		if contentBuffer != nil {
			uploadContent = bytes.NewReader(contentBuffer)
		} else if seekableContent, ok := content.(io.Seeker); ok {
			// Reset seekable content
			seekableContent.Seek(0, io.SeekStart)
			uploadContent = content
		} else {
			return "", fmt.Errorf("cannot retry upload to replica: content not seekable and not buffered")
		}

		err := sm.replica.Upload(ctx, uploadContent, path, mimeType)
		if err == nil {
			return path, nil
		}
	}

	return "", fmt.Errorf("failed to upload to any storage provider")
}

// UploadToPath uploads content to a specific path without generating a new path
func (sm *StorageManager) UploadToPath(ctx context.Context, content io.Reader, path, mimeType string) (string, error) {
	// Buffer content for potential replica sync
	var contentBuffer []byte
	if sm.config.SyncEnabled && sm.replica != nil {
		var err error
		contentBuffer, err = io.ReadAll(content)
		if err != nil {
			return "", fmt.Errorf("failed to buffer content: %w", err)
		}
	}

	// Try primary first
	if sm.isPrimaryHealthy() {
		var uploadContent io.Reader
		if contentBuffer != nil {
			uploadContent = bytes.NewReader(contentBuffer)
		} else {
			uploadContent = content
		}

		err := sm.primary.Upload(ctx, uploadContent, path, mimeType)
		if err == nil {
			// Queue for replica sync if enabled (thread-safe)
			if sm.config.SyncEnabled && sm.replica != nil && contentBuffer != nil {
				// Use a copy of the buffer for async sync
				syncBuffer := make([]byte, len(contentBuffer))
				copy(syncBuffer, contentBuffer)
				sm.queueSyncTaskSafe(path, mimeType, bytes.NewReader(syncBuffer))
			}
			return path, nil
		}
	}

	// Fallback to replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		var uploadContent io.Reader
		if contentBuffer != nil {
			uploadContent = bytes.NewReader(contentBuffer)
		} else {
			uploadContent = content
		}

		err := sm.replica.Upload(ctx, uploadContent, path, mimeType)
		if err == nil {
			return path, nil
		}
	}

	return "", fmt.Errorf("failed to upload to any storage provider")
}

func (sm *StorageManager) Download(ctx context.Context, path string) (io.ReadCloser, error) {
	// Try primary first
	if sm.isPrimaryHealthy() {
		content, err := sm.primary.Download(ctx, path)
		if err == nil {
			return content, nil
		}
	}

	// Fallback to replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		content, err := sm.replica.Download(ctx, path)
		if err == nil {
			return content, nil
		}
	}

	return nil, fmt.Errorf("failed to download from any storage provider")
}

func (sm *StorageManager) Delete(ctx context.Context, path string) error {
	var primaryErr, replicaErr error

	// Delete from primary
	if sm.isPrimaryHealthy() {
		primaryErr = sm.primary.Delete(ctx, path)
	}

	// Delete from replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		replicaErr = sm.replica.Delete(ctx, path)
	}

	// Return error only if both fail
	if primaryErr != nil && replicaErr != nil {
		return fmt.Errorf("failed to delete from both providers: primary=%v, replica=%v", primaryErr, replicaErr)
	}

	return nil
}

func (sm *StorageManager) GenerateDownloadURL(ctx context.Context, path string, expiresInSeconds int) (string, error) {
	// Try primary first
	if sm.isPrimaryHealthy() {
		url, err := sm.primary.GeneratePresignedURL(ctx, path, expiresInSeconds)
		if err == nil {
			return url, nil
		}
	}

	// Fallback to replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		url, err := sm.replica.GeneratePresignedURL(ctx, path, expiresInSeconds)
		if err == nil {
			return url, nil
		}
	}

	return "", fmt.Errorf("failed to generate download URL from any storage provider")
}

// SyncThumbnailToReplica queues a thumbnail for background sync to replica storage
func (sm *StorageManager) SyncThumbnailToReplica(ctx context.Context, thumbnailPath string) error {
	if !sm.config.SyncEnabled || sm.replica == nil {
		return nil // Sync disabled or no replica configured
	}

	// Download from primary (MinIO)
	content, err := sm.primary.Download(ctx, thumbnailPath)
	if err != nil {
		return fmt.Errorf("failed to download thumbnail from primary: %w", err)
	}
	defer content.Close()

	// Read content into buffer for sync
	buffer := make([]byte, 0, 1024*1024) // Start with 1MB buffer
	tempBuffer := make([]byte, 32*1024)  // 32KB read chunks
	for {
		n, err := content.Read(tempBuffer)
		if n > 0 {
			buffer = append(buffer, tempBuffer[:n]...)
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read thumbnail content: %w", err)
		}
	}

	// Queue for async sync to replica
	sm.queueSyncTaskSafe(thumbnailPath, "image/jpeg", bytes.NewReader(buffer))
	return nil
}

func (sm *StorageManager) GetFileInfo(ctx context.Context, path string) (*ports.FileInfo, error) {
	// Try primary first
	if sm.isPrimaryHealthy() {
		info, err := sm.primary.GetObjectInfo(ctx, path)
		if err == nil {
			return info, nil
		}
	}

	// Fallback to replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		info, err := sm.replica.GetObjectInfo(ctx, path)
		if err == nil {
			return info, nil
		}
	}

	return nil, fmt.Errorf("failed to get file info from any storage provider")
}

func (sm *StorageManager) Exists(ctx context.Context, path string) (bool, error) {
	// Try primary first
	if sm.isPrimaryHealthy() {
		exists, err := sm.primary.Exists(ctx, path)
		if err == nil {
			return exists, nil
		}
	}

	// Fallback to replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		exists, err := sm.replica.Exists(ctx, path)
		if err == nil {
			return exists, nil
		}
	}

	return false, fmt.Errorf("failed to check file existence from any storage provider")
}

func (sm *StorageManager) Copy(ctx context.Context, sourcePath, destinationPath string) error {
	// Try primary first
	if sm.isPrimaryHealthy() {
		err := sm.primary.Copy(ctx, sourcePath, destinationPath)
		if err == nil {
			return nil
		}
	}

	// Fallback to replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		err := sm.replica.Copy(ctx, sourcePath, destinationPath)
		if err == nil {
			return nil
		}
	}

	return fmt.Errorf("failed to copy file from any storage provider")
}

func (sm *StorageManager) Move(ctx context.Context, sourcePath, destinationPath string) error {
	// Try primary first
	if sm.isPrimaryHealthy() {
		err := sm.primary.Move(ctx, sourcePath, destinationPath)
		if err == nil {
			return nil
		}
	}

	// Fallback to replica
	if sm.replica != nil && sm.isReplicaHealthy() {
		err := sm.replica.Move(ctx, sourcePath, destinationPath)
		if err == nil {
			return nil
		}
	}

	return fmt.Errorf("failed to move file from any storage provider")
}

func (sm *StorageManager) Health(ctx context.Context) error {
	if !sm.isPrimaryHealthy() && !sm.isReplicaHealthy() {
		return fmt.Errorf("all storage providers are unhealthy")
	}
	return nil
}

func (sm *StorageManager) isPrimaryHealthy() bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.primaryHealthy
}

func (sm *StorageManager) isReplicaHealthy() bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.replicaHealthy
}

func (sm *StorageManager) updateHealthStatus(primary, replica bool) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.primaryHealthy = primary
	sm.replicaHealthy = replica
}

// queueSyncTaskSafe safely queues a sync task without race conditions
func (sm *StorageManager) queueSyncTaskSafe(path, mimeType string, content io.Reader) {
	select {
	case sm.syncQueue <- SyncTask{
		Path:     path,
		MimeType: mimeType,
		Content:  content,
		Retry:    0,
	}:
		// Successfully queued
	default:
		// Queue is full, skip sync (this is acceptable for background sync)
	}
}

// Deprecated: use queueSyncTaskSafe instead
func (sm *StorageManager) queueSyncTask(path, mimeType string, content io.Reader) {
	sm.queueSyncTaskSafe(path, mimeType, content)
}

func (sm *StorageManager) startSyncWorkers() {
	for i := 0; i < sm.syncWorkers; i++ {
		go sm.syncWorker()
	}
}

func (sm *StorageManager) syncWorker() {
	for task := range sm.syncQueue {
		if sm.replica != nil && sm.isReplicaHealthy() {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
			err := sm.replica.Upload(ctx, task.Content, task.Path, task.MimeType)
			cancel()

			if err != nil && task.Retry < sm.config.RetryAttempts {
				// Schedule retry with exponential backoff in a separate goroutine
				go func(retryTask SyncTask) {
					backoffDelay := sm.config.RetryDelay * time.Duration(1<<uint(retryTask.Retry))
					if backoffDelay > 5*time.Minute {
						backoffDelay = 5 * time.Minute
					}

					time.Sleep(backoffDelay)
					retryTask.Retry++
					sm.queueSyncTaskSafe(retryTask.Path, retryTask.MimeType, retryTask.Content)
				}(task)
			}
		}
	}
}

func (sm *StorageManager) startHealthChecker() {
	go sm.healthChecker.Start()
}

func (sm *StorageManager) Stop() {
	close(sm.syncQueue)
	sm.healthChecker.Stop()
}

func (hc *HealthChecker) Start() {
	ticker := time.NewTicker(hc.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			hc.checkHealth()
		case <-hc.stopChan:
			return
		}
	}
}

func (hc *HealthChecker) checkHealth() {
	ctx := context.Background()

	primaryHealthy := hc.primary != nil && hc.primary.IsHealthy(ctx)
	replicaHealthy := hc.replica != nil && hc.replica.IsHealthy(ctx)

	hc.mu.Lock()
	changed := hc.primaryHealthy != primaryHealthy || hc.replicaHealthy != replicaHealthy
	hc.primaryHealthy = primaryHealthy
	hc.replicaHealthy = replicaHealthy
	hc.mu.Unlock()

	if changed && hc.statusCallback != nil {
		hc.statusCallback(primaryHealthy, replicaHealthy)
	}
}

func (hc *HealthChecker) Stop() {
	close(hc.stopChan)
}

// GetPrimaryProvider returns the primary storage provider
func (sm *StorageManager) GetPrimaryProvider() ports.StorageProvider {
	return sm.primary
}

// GetReplicaProvider returns the replica storage provider
func (sm *StorageManager) GetReplicaProvider() ports.StorageProvider {
	return sm.replica
}
