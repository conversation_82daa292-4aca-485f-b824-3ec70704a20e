package storage

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/Backblaze/blazer/b2"
	"github.com/swork-team/platform/services/drive/internal/application/ports"
)

type BackblazeProvider struct {
	client     *b2.Client
	bucket     *b2.Bucket
	bucketName string
	config     BackblazeConfig
	mu         sync.RWMutex
	healthy    bool
}

type BackblazeConfig struct {
	ApplicationKeyID    string
	ApplicationKey      string
	BucketName          string
	Endpoint            string
	MaxRetries          int
	RetryDelay          time.Duration
	UploadTimeout       time.Duration
	DownloadTimeout     time.Duration
	HealthCheckInterval time.Duration
}

func NewBackblazeProvider(config BackblazeConfig) (*BackblazeProvider, error) {
	client, err := b2.NewClient(context.Background(), config.ApplicationKeyID, config.ApplicationKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create B2 client: %w", err)
	}

	bucket, err := client.Bucket(context.Background(), config.BucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket: %w", err)
	}

	provider := &BackblazeProvider{
		client:     client,
		bucket:     bucket,
		bucketName: config.BucketName,
		config:     config,
		healthy:    true,
	}

	return provider, nil
}

func (bp *BackblazeProvider) Name() string {
	return "backblaze"
}

func (bp *BackblazeProvider) IsHealthy(ctx context.Context) bool {
	bp.mu.RLock()
	defer bp.mu.RUnlock()
	return bp.healthy
}

func (bp *BackblazeProvider) Upload(ctx context.Context, content io.Reader, path, mimeType string) error {
	ctx, cancel := context.WithTimeout(ctx, bp.config.UploadTimeout)
	defer cancel()

	// Read content into buffer for retry capability
	var originalContent []byte
	if _, ok := content.(io.Seeker); ok {
		// If content is seekable, use it directly
		originalContent = nil
	} else {
		// Read content into memory for retry capability
		buffer := bytes.NewBuffer(nil)
		_, err := io.Copy(buffer, content)
		if err != nil {
			return fmt.Errorf("failed to read content: %w", err)
		}
		originalContent = buffer.Bytes()
	}

	var lastErr error
	for attempt := 0; attempt <= bp.config.MaxRetries; attempt++ {
		// Prepare reader for this attempt
		var reader io.Reader
		if originalContent != nil {
			reader = bytes.NewReader(originalContent)
		} else {
			// Reset seekable content to beginning
			if seeker, ok := content.(io.Seeker); ok {
				_, err := seeker.Seek(0, io.SeekStart)
				if err != nil {
					return fmt.Errorf("failed to reset content position: %w", err)
				}
			}
			reader = content
		}

		// Create file writer
		writer := bp.bucket.Object(path).NewWriter(ctx)
		writer.ConcurrentUploads = 1

		if mimeType != "" {
			// Note: ContentType setting depends on B2 library version
			// This may need adjustment based on the actual B2 library API
		}

		// Upload content
		_, uploadErr := io.Copy(writer, reader)
		if uploadErr != nil {
			writer.Close()
			lastErr = uploadErr
			if attempt < bp.config.MaxRetries {
				time.Sleep(bp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		// Close writer
		closeErr := writer.Close()
		if closeErr != nil {
			lastErr = closeErr
			if attempt < bp.config.MaxRetries {
				time.Sleep(bp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		// Upload successful
		bp.setHealthy(true)
		return nil
	}

	bp.setHealthy(false)
	return fmt.Errorf("failed to upload after %d attempts: %w", bp.config.MaxRetries+1, lastErr)
}

func (bp *BackblazeProvider) Download(ctx context.Context, path string) (io.ReadCloser, error) {
	ctx, cancel := context.WithTimeout(ctx, bp.config.DownloadTimeout)
	defer cancel()

	var lastErr error
	for attempt := 0; attempt <= bp.config.MaxRetries; attempt++ {
		reader := bp.bucket.Object(path).NewReader(ctx)

		// Simply return the reader without testing it
		// Testing by reading would change the stream position
		// Let the actual consumer handle any read errors
		bp.setHealthy(true)
		return reader, nil
	}

	bp.setHealthy(false)
	return nil, fmt.Errorf("failed to download after %d attempts: %w", bp.config.MaxRetries+1, lastErr)
}

func (bp *BackblazeProvider) Delete(ctx context.Context, path string) error {
	var lastErr error
	for attempt := 0; attempt <= bp.config.MaxRetries; attempt++ {
		err := bp.bucket.Object(path).Delete(ctx)
		if err != nil {
			lastErr = err
			if attempt < bp.config.MaxRetries {
				time.Sleep(bp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		bp.setHealthy(true)
		return nil
	}

	bp.setHealthy(false)
	return fmt.Errorf("failed to delete after %d attempts: %w", bp.config.MaxRetries+1, lastErr)
}

func (bp *BackblazeProvider) GeneratePresignedURL(ctx context.Context, path string, expiresInSeconds int) (string, error) {
	// Note: B2 doesn't support presigned URLs in the same way as S3
	// For now, we'll return the public URL if the bucket is public
	// In production, you might want to implement temporary download authorization

	url := bp.bucket.Object(path).URL()
	if url == "" {
		return "", fmt.Errorf("failed to generate URL for path: %s", path)
	}

	bp.setHealthy(true)
	return url, nil
}

func (bp *BackblazeProvider) GetObjectInfo(ctx context.Context, path string) (*ports.FileInfo, error) {
	var lastErr error
	for attempt := 0; attempt <= bp.config.MaxRetries; attempt++ {
		attrs, err := bp.bucket.Object(path).Attrs(ctx)
		if err != nil {
			lastErr = err
			if attempt < bp.config.MaxRetries {
				time.Sleep(bp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		fileInfo := &ports.FileInfo{
			Path:         path,
			Size:         attrs.Size,
			ContentType:  attrs.ContentType,
			LastModified: attrs.LastModified.Format(time.RFC3339),
			ETag:         attrs.SHA1,
		}

		bp.setHealthy(true)
		return fileInfo, nil
	}

	bp.setHealthy(false)
	return nil, fmt.Errorf("failed to get object info after %d attempts: %w", bp.config.MaxRetries+1, lastErr)
}

func (bp *BackblazeProvider) Exists(ctx context.Context, path string) (bool, error) {
	var lastErr error
	for attempt := 0; attempt <= bp.config.MaxRetries; attempt++ {
		_, err := bp.bucket.Object(path).Attrs(ctx)
		if err != nil {
			// Check if it's a not found error using more robust error handling
			if bp.isNotFoundError(err) {
				return false, nil
			}
			lastErr = err
			if attempt < bp.config.MaxRetries {
				time.Sleep(bp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		bp.setHealthy(true)
		return true, nil
	}

	bp.setHealthy(false)
	return false, fmt.Errorf("failed to check existence after %d attempts: %w", bp.config.MaxRetries+1, lastErr)
}

func (bp *BackblazeProvider) Copy(ctx context.Context, sourcePath, destinationPath string) error {
	var lastErr error
	for attempt := 0; attempt <= bp.config.MaxRetries; attempt++ {
		srcObj := bp.bucket.Object(sourcePath)
		dstObj := bp.bucket.Object(destinationPath)

		// Copy operation - this depends on B2 library version
		// For now, implement as download + upload
		reader := srcObj.NewReader(ctx)
		defer reader.Close()

		writer := dstObj.NewWriter(ctx)
		_, err := io.Copy(writer, reader)
		if err != nil {
			writer.Close()
			return fmt.Errorf("failed to copy content: %w", err)
		}
		err = writer.Close()
		if err != nil {
			lastErr = err
			if attempt < bp.config.MaxRetries {
				time.Sleep(bp.config.RetryDelay * time.Duration(attempt+1))
				continue
			}
			break
		}

		bp.setHealthy(true)
		return nil
	}

	bp.setHealthy(false)
	return fmt.Errorf("failed to copy after %d attempts: %w", bp.config.MaxRetries+1, lastErr)
}

func (bp *BackblazeProvider) Move(ctx context.Context, sourcePath, destinationPath string) error {
	// B2 doesn't have a native move operation, so we copy then delete
	err := bp.Copy(ctx, sourcePath, destinationPath)
	if err != nil {
		return err
	}

	err = bp.Delete(ctx, sourcePath)
	if err != nil {
		// Try to clean up the copied file
		bp.Delete(ctx, destinationPath)
		return err
	}

	return nil
}

func (bp *BackblazeProvider) setHealthy(healthy bool) {
	bp.mu.Lock()
	defer bp.mu.Unlock()
	bp.healthy = healthy
}

func (bp *BackblazeProvider) performHealthCheck() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Try to list objects to check health - an empty bucket is still healthy
	iter := bp.bucket.List(ctx, b2.ListHidden())
	
	// Attempt to get the first item or confirm the listing works
	for iter.Next() {
		// If we can iterate (even if no objects), the bucket is accessible
		break
	}
	
	// Check if there was an error during iteration
	if err := iter.Err(); err != nil {
		log.Printf("[BackblazeProvider] Health check failed: %v", err)
		bp.setHealthy(false)
	} else {
		// Success: either bucket has objects or is empty but accessible
		bp.setHealthy(true)
	}
}

// isNotFoundError checks if the error indicates a file not found
func (bp *BackblazeProvider) isNotFoundError(err error) bool {
	if err == nil {
		return false
	}

	// Check for common "not found" error patterns
	errorStr := err.Error()
	return strings.Contains(strings.ToLower(errorStr), "not found") ||
		strings.Contains(strings.ToLower(errorStr), "404") ||
		strings.Contains(strings.ToLower(errorStr), "does not exist")
}

func (bp *BackblazeProvider) StartHealthCheck() {
	ticker := time.NewTicker(bp.config.HealthCheckInterval)
	go func() {
		defer ticker.Stop()
		for range ticker.C {
			bp.performHealthCheck()
		}
	}()
}
