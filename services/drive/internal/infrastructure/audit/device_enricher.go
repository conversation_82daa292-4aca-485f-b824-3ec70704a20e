package audit

import (
	"regexp"
	"strings"

	"github.com/swork-team/platform/services/drive/internal/application/ports"
)

type DeviceEnricher struct {
	enabled bool
}

type DeviceEnricherConfig struct {
	Enabled bool
}

func NewDeviceEnricher(config DeviceEnricherConfig) *DeviceEnricher {
	return &DeviceEnricher{
		enabled: config.Enabled,
	}
}

func (de *DeviceEnricher) ParseUserAgent(userAgent string) (*ports.DeviceInfo, error) {
	if !de.enabled || userAgent == "" {
		return &ports.DeviceInfo{
			Browser:   "Unknown",
			OS:        "Unknown",
			Device:    "Unknown",
			IsMobile:  false,
			IsTablet:  false,
			IsDesktop: true,
		}, nil
	}

	// Normalize user agent string
	ua := strings.ToLower(userAgent)

	// Parse browser
	browser := de.parseBrowser(ua)

	// Parse operating system
	os := de.parseOS(ua)

	// Parse device type
	device := de.parseDevice(ua)

	// Determine device categories
	isMobile := de.isMobile(ua)
	isTablet := de.isTablet(ua)
	isDesktop := !isMobile && !isTablet

	return &ports.DeviceInfo{
		Browser:   browser,
		OS:        os,
		Device:    device,
		IsMobile:  isMobile,
		IsTablet:  isTablet,
		IsDesktop: isDesktop,
	}, nil
}

func (de *DeviceEnricher) parseBrowser(ua string) string {
	browsers := map[string]string{
		"edg":     "Microsoft Edge",
		"chrome":  "Google Chrome",
		"firefox": "Mozilla Firefox",
		"safari":  "Safari",
		"opera":   "Opera",
		"ie":      "Internet Explorer",
		"edge":    "Microsoft Edge",
	}

	// Check for browsers in order of specificity
	if strings.Contains(ua, "edg/") || strings.Contains(ua, "edge/") {
		return "Microsoft Edge"
	}
	if strings.Contains(ua, "chrome/") && !strings.Contains(ua, "edg") {
		return "Google Chrome"
	}
	if strings.Contains(ua, "firefox/") {
		return "Mozilla Firefox"
	}
	if strings.Contains(ua, "safari/") && !strings.Contains(ua, "chrome") {
		return "Safari"
	}
	if strings.Contains(ua, "opera/") || strings.Contains(ua, "opr/") {
		return "Opera"
	}

	for key, browser := range browsers {
		if strings.Contains(ua, key) {
			return browser
		}
	}

	return "Unknown Browser"
}

func (de *DeviceEnricher) parseOS(ua string) string {
	osPatterns := map[string]string{
		"windows nt 10.0": "Windows 10",
		"windows nt 6.3":  "Windows 8.1",
		"windows nt 6.2":  "Windows 8",
		"windows nt 6.1":  "Windows 7",
		"windows nt":      "Windows",
		"mac os x":        "macOS",
		"macintosh":       "macOS",
		"linux":           "Linux",
		"ubuntu":          "Ubuntu",
		"android":         "Android",
		"iphone":          "iOS",
		"ipad":            "iPadOS",
		"ipod":            "iOS",
	}

	for pattern, os := range osPatterns {
		if strings.Contains(ua, pattern) {
			return os
		}
	}

	return "Unknown OS"
}

func (de *DeviceEnricher) parseDevice(ua string) string {
	// Mobile devices
	if de.isMobile(ua) {
		if strings.Contains(ua, "iphone") {
			return "iPhone"
		}
		if strings.Contains(ua, "android") {
			return "Android Phone"
		}
		return "Mobile Device"
	}

	// Tablets
	if de.isTablet(ua) {
		if strings.Contains(ua, "ipad") {
			return "iPad"
		}
		if strings.Contains(ua, "android") {
			return "Android Tablet"
		}
		return "Tablet"
	}

	// Desktop/Laptop
	if strings.Contains(ua, "windows") {
		return "Windows PC"
	}
	if strings.Contains(ua, "mac") {
		return "Mac"
	}
	if strings.Contains(ua, "linux") {
		return "Linux PC"
	}

	return "Desktop Computer"
}

func (de *DeviceEnricher) isMobile(ua string) bool {
	mobilePatterns := []string{
		"mobile",
		"android",
		"iphone",
		"ipod",
		"blackberry",
		"windows phone",
		"opera mini",
		"nokia",
		"samsung",
		"htc",
		"lg",
	}

	for _, pattern := range mobilePatterns {
		if strings.Contains(ua, pattern) {
			// Exclude tablets from mobile
			if !de.isTablet(ua) {
				return true
			}
		}
	}

	return false
}

func (de *DeviceEnricher) isTablet(ua string) bool {
	tabletPatterns := []string{
		"ipad",
		"tablet",
		"kindle",
		"silk",
		"playbook",
	}

	for _, pattern := range tabletPatterns {
		if strings.Contains(ua, pattern) {
			return true
		}
	}

	// Android tablets typically don't have "mobile" in user agent
	if strings.Contains(ua, "android") && !strings.Contains(ua, "mobile") {
		return true
	}

	return false
}

// MockDeviceEnricher provides a mock implementation for testing
type MockDeviceEnricher struct{}

func NewMockDeviceEnricher() *MockDeviceEnricher {
	return &MockDeviceEnricher{}
}

func (mde *MockDeviceEnricher) ParseUserAgent(userAgent string) (*ports.DeviceInfo, error) {
	// Return mock data for testing
	return &ports.DeviceInfo{
		Browser:   "Google Chrome",
		OS:        "macOS",
		Device:    "Mac",
		IsMobile:  false,
		IsTablet:  false,
		IsDesktop: true,
	}, nil
}

// Advanced User Agent Parser using regex patterns
type AdvancedDeviceEnricher struct {
	enabled      bool
	browserRegex map[string]*regexp.Regexp
	osRegex      map[string]*regexp.Regexp
	deviceRegex  map[string]*regexp.Regexp
}

func NewAdvancedDeviceEnricher(config DeviceEnricherConfig) *AdvancedDeviceEnricher {
	if !config.Enabled {
		return &AdvancedDeviceEnricher{enabled: false}
	}

	return &AdvancedDeviceEnricher{
		enabled:      true,
		browserRegex: initBrowserRegex(),
		osRegex:      initOSRegex(),
		deviceRegex:  initDeviceRegex(),
	}
}

func (ade *AdvancedDeviceEnricher) ParseUserAgent(userAgent string) (*ports.DeviceInfo, error) {
	if !ade.enabled {
		return &ports.DeviceInfo{
			Browser:   "Unknown",
			OS:        "Unknown",
			Device:    "Unknown",
			IsMobile:  false,
			IsTablet:  false,
			IsDesktop: true,
		}, nil
	}

	browser := ade.parseBrowserRegex(userAgent)
	os := ade.parseOSRegex(userAgent)
	device := ade.parseDeviceRegex(userAgent)

	// Simple device type detection
	ua := strings.ToLower(userAgent)
	isMobile := strings.Contains(ua, "mobile") || strings.Contains(ua, "phone")
	isTablet := strings.Contains(ua, "tablet") || strings.Contains(ua, "ipad")
	isDesktop := !isMobile && !isTablet

	return &ports.DeviceInfo{
		Browser:   browser,
		OS:        os,
		Device:    device,
		IsMobile:  isMobile,
		IsTablet:  isTablet,
		IsDesktop: isDesktop,
	}, nil
}

func (ade *AdvancedDeviceEnricher) parseBrowserRegex(ua string) string {
	for name, regex := range ade.browserRegex {
		if regex.MatchString(ua) {
			return name
		}
	}
	return "Unknown Browser"
}

func (ade *AdvancedDeviceEnricher) parseOSRegex(ua string) string {
	for name, regex := range ade.osRegex {
		if regex.MatchString(ua) {
			return name
		}
	}
	return "Unknown OS"
}

func (ade *AdvancedDeviceEnricher) parseDeviceRegex(ua string) string {
	for name, regex := range ade.deviceRegex {
		if regex.MatchString(ua) {
			return name
		}
	}
	return "Unknown Device"
}

func initBrowserRegex() map[string]*regexp.Regexp {
	patterns := map[string]string{
		"Google Chrome":     `Chrome/[\d.]+`,
		"Mozilla Firefox":   `Firefox/[\d.]+`,
		"Safari":            `Version/[\d.]+ Safari/`,
		"Microsoft Edge":    `Edg/[\d.]+`,
		"Opera":             `Opera/[\d.]+|OPR/[\d.]+`,
		"Internet Explorer": `MSIE [\d.]+|Trident/`,
	}

	regexMap := make(map[string]*regexp.Regexp)
	for name, pattern := range patterns {
		regexMap[name] = regexp.MustCompile(pattern)
	}
	return regexMap
}

func initOSRegex() map[string]*regexp.Regexp {
	patterns := map[string]string{
		"Windows 11":  `Windows NT 10\.0.*rv:`,
		"Windows 10":  `Windows NT 10\.0`,
		"Windows 8.1": `Windows NT 6\.3`,
		"Windows 8":   `Windows NT 6\.2`,
		"Windows 7":   `Windows NT 6\.1`,
		"macOS":       `Mac OS X|Macintosh`,
		"iOS":         `iPhone OS|iOS`,
		"Android":     `Android [\d.]+`,
		"Linux":       `Linux`,
		"Ubuntu":      `Ubuntu`,
	}

	regexMap := make(map[string]*regexp.Regexp)
	for name, pattern := range patterns {
		regexMap[name] = regexp.MustCompile(pattern)
	}
	return regexMap
}

func initDeviceRegex() map[string]*regexp.Regexp {
	patterns := map[string]string{
		"iPhone":         `iPhone`,
		"iPad":           `iPad`,
		"Android Phone":  `Android.*Mobile`,
		"Android Tablet": `Android(?!.*Mobile)`,
		"Windows PC":     `Windows NT`,
		"Mac":            `Macintosh|Mac OS X`,
		"Linux PC":       `Linux`,
	}

	regexMap := make(map[string]*regexp.Regexp)
	for name, pattern := range patterns {
		regexMap[name] = regexp.MustCompile(pattern)
	}
	return regexMap
}
