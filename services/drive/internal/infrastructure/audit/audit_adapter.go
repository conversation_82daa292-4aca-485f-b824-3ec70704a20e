package audit

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"github.com/swork-team/platform/services/drive/internal/application/ports"
	"github.com/swork-team/platform/services/drive/internal/domain/audit/entities"
	"github.com/swork-team/platform/services/drive/internal/domain/audit/repositories"
)

type AuditAdapter struct {
	auditRepo        repositories.AuditRepository
	locationEnricher ports.LocationEnricher
	deviceEnricher   ports.DeviceEnricher
	batchSize        int
	batchTimeout     time.Duration
	queue            chan *entities.AuditLog
	batch            []*entities.AuditLog
	mu               sync.Mutex
	stopChan         chan struct{}
	wg               sync.WaitGroup
}

type AuditConfig struct {
	BatchSize        int
	BatchTimeout     time.Duration
	QueueSize        int
	EnableBatching   bool
	EnableEnrichment bool
}

func NewAuditAdapter(
	auditRepo repositories.AuditRepository,
	locationEnricher ports.LocationEnricher,
	deviceEnricher ports.DeviceEnricher,
	config AuditConfig,
) *AuditAdapter {
	adapter := &AuditAdapter{
		auditRepo:        auditRepo,
		locationEnricher: locationEnricher,
		deviceEnricher:   deviceEnricher,
		batchSize:        config.BatchSize,
		batchTimeout:     config.BatchTimeout,
		queue:            make(chan *entities.AuditLog, config.QueueSize),
		batch:            make([]*entities.AuditLog, 0, config.BatchSize),
		stopChan:         make(chan struct{}),
	}

	if config.EnableBatching {
		adapter.startBatchProcessor()
	}

	return adapter
}

func (a *AuditAdapter) LogEvent(ctx context.Context, log *entities.AuditLog) error {
	// Enrich log with additional data
	err := a.enrichLog(ctx, log)
	if err != nil {
		// Don't fail the audit log due to enrichment errors
		fmt.Printf("Failed to enrich audit log: %v\n", err)
	}

	// Generate checksum for integrity
	checksum, err := a.GenerateChecksum(ctx, log)
	if err != nil {
		return fmt.Errorf("failed to generate checksum: %w", err)
	}
	log.SetChecksum(checksum)

	// Send to batch queue or save directly
	select {
	case a.queue <- log:
		return nil
	default:
		// Queue is full, save directly
		return a.auditRepo.Create(ctx, log)
	}
}

func (a *AuditAdapter) LogEventBatch(ctx context.Context, logs []*entities.AuditLog) error {
	// Enrich and generate checksums for all logs
	for _, log := range logs {
		err := a.enrichLog(ctx, log)
		if err != nil {
			fmt.Printf("Failed to enrich audit log: %v\n", err)
		}

		checksum, err := a.GenerateChecksum(ctx, log)
		if err != nil {
			return fmt.Errorf("failed to generate checksum: %w", err)
		}
		log.SetChecksum(checksum)
	}

	return a.auditRepo.CreateBatch(ctx, logs)
}

func (a *AuditAdapter) EnrichWithLocation(ctx context.Context, log *entities.AuditLog, ipAddress string) error {
	if a.locationEnricher == nil || ipAddress == "" {
		return nil
	}

	// Skip enrichment for private/local IPs
	if a.isPrivateIP(ipAddress) {
		return nil
	}

	location, err := a.locationEnricher.GetLocation(ctx, ipAddress)
	if err != nil {
		return fmt.Errorf("failed to get location: %w", err)
	}

	log.SetLocation(location.Country, location.City, location.Latitude, location.Longitude)
	return nil
}

func (a *AuditAdapter) EnrichWithDevice(ctx context.Context, log *entities.AuditLog, userAgent string) error {
	if a.deviceEnricher == nil || userAgent == "" {
		return nil
	}

	device, err := a.deviceEnricher.ParseUserAgent(userAgent)
	if err != nil {
		return fmt.Errorf("failed to parse user agent: %w", err)
	}

	deviceInfo := fmt.Sprintf("%s/%s on %s", device.Browser, device.OS, device.Device)
	log.SetDeviceInfo(deviceInfo)
	return nil
}

func (a *AuditAdapter) ValidateIntegrity(ctx context.Context, log *entities.AuditLog) error {
	expectedChecksum, err := a.GenerateChecksum(ctx, log)
	if err != nil {
		return fmt.Errorf("failed to generate expected checksum: %w", err)
	}

	if log.Checksum() != expectedChecksum {
		return fmt.Errorf("audit log integrity validation failed: expected %s, got %s",
			expectedChecksum, log.Checksum())
	}

	return nil
}

func (a *AuditAdapter) GenerateChecksum(ctx context.Context, log *entities.AuditLog) (string, error) {
	// Create a consistent representation of the log for checksumming
	data := struct {
		EventType    string                 `json:"event_type"`
		Action       string                 `json:"action"`
		Description  string                 `json:"description"`
		UserID       string                 `json:"user_id,omitempty"`
		ResourceID   string                 `json:"resource_id,omitempty"`
		ResourceType string                 `json:"resource_type,omitempty"`
		IPAddress    string                 `json:"ip_address,omitempty"`
		UserAgent    string                 `json:"user_agent,omitempty"`
		Timestamp    string                 `json:"timestamp"`
		Metadata     map[string]interface{} `json:"metadata,omitempty"`
	}{
		EventType:   string(log.EventType()),
		Action:      log.Action(),
		Description: log.Description(),
		IPAddress:   log.IPAddress(),
		UserAgent:   log.UserAgent(),
		Timestamp:   log.Timestamp().Format(time.RFC3339Nano),
		Metadata:    log.Metadata(),
	}

	if log.UserID() != nil {
		data.UserID = log.UserID().String()
	}

	if log.ResourceID() != nil {
		data.ResourceID = log.ResourceID().String()
	}

	if log.ResourceType() != "" {
		data.ResourceType = log.ResourceType()
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal log data: %w", err)
	}

	hash := sha256.Sum256(jsonData)
	return fmt.Sprintf("%x", hash), nil
}

func (a *AuditAdapter) enrichLog(ctx context.Context, log *entities.AuditLog) error {
	// Enrich with location if IP address is available
	if log.IPAddress() != "" {
		err := a.EnrichWithLocation(ctx, log, log.IPAddress())
		if err != nil {
			// Log error but don't fail
			fmt.Printf("Failed to enrich with location: %v\n", err)
		}
	}

	// Enrich with device info if user agent is available
	if log.UserAgent() != "" {
		err := a.EnrichWithDevice(ctx, log, log.UserAgent())
		if err != nil {
			// Log error but don't fail
			fmt.Printf("Failed to enrich with device info: %v\n", err)
		}
	}

	return nil
}

func (a *AuditAdapter) isPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// Check for private IP ranges
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"*********/8",
	}

	for _, rangeStr := range privateRanges {
		_, network, err := net.ParseCIDR(rangeStr)
		if err != nil {
			continue
		}
		if network.Contains(parsedIP) {
			return true
		}
	}

	return false
}

func (a *AuditAdapter) startBatchProcessor() {
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		ticker := time.NewTicker(a.batchTimeout)
		defer ticker.Stop()

		for {
			select {
			case log := <-a.queue:
				a.addToBatch(log)
			case <-ticker.C:
				a.flushBatch()
			case <-a.stopChan:
				a.flushBatch()
				return
			}
		}
	}()
}

func (a *AuditAdapter) addToBatch(log *entities.AuditLog) {
	a.mu.Lock()
	defer a.mu.Unlock()

	a.batch = append(a.batch, log)
	if len(a.batch) >= a.batchSize {
		a.flushBatchUnsafe()
	}
}

func (a *AuditAdapter) flushBatch() {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.flushBatchUnsafe()
}

func (a *AuditAdapter) flushBatchUnsafe() {
	if len(a.batch) == 0 {
		return
	}

	ctx := context.Background()
	err := a.auditRepo.CreateBatch(ctx, a.batch)
	if err != nil {
		fmt.Printf("Failed to flush audit batch: %v\n", err)
		// In production, you might want to retry or send to a dead letter queue
	}

	// Clear the batch
	a.batch = a.batch[:0]
}

func (a *AuditAdapter) Stop() {
	close(a.stopChan)
	a.wg.Wait()
	close(a.queue)
}

// Helper function to mask sensitive data in logs
func (a *AuditAdapter) maskSensitiveData(data string) string {
	// Simple masking - in production you'd want more sophisticated PII detection
	sensitivePatterns := []string{
		`password`,
		`token`,
		`key`,
		`secret`,
		`auth`,
	}

	masked := data
	for _, pattern := range sensitivePatterns {
		if strings.Contains(strings.ToLower(masked), pattern) {
			masked = strings.ReplaceAll(masked, data, "***MASKED***")
			break
		}
	}

	return masked
}
