package audit

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/swork-team/platform/services/drive/internal/application/ports"
)

type LocationEnricher struct {
	client  *http.Client
	apiKey  string
	apiURL  string
	enabled bool
}

type LocationEnricherConfig struct {
	APIKey  string
	APIURL  string
	Enabled bool
	Timeout time.Duration
}

// ipAPIResponse represents the response from a typical IP geolocation API
type ipAPIResponse struct {
	Country   string   `json:"country"`
	City      string   `json:"city"`
	Latitude  *float64 `json:"lat"`
	Longitude *float64 `json:"lon"`
	Status    string   `json:"status"`
	Message   string   `json:"message"`
}

func NewLocationEnricher(config LocationEnricherConfig) *LocationEnricher {
	if !config.Enabled {
		return &LocationEnricher{enabled: false}
	}

	timeout := config.Timeout
	if timeout == 0 {
		timeout = 5 * time.Second
	}

	apiURL := config.APIURL
	if apiURL == "" {
		// Use a free IP geolocation service as default
		// In production, you might want to use a more reliable service
		apiURL = "http://ip-api.com/json"
	}

	return &LocationEnricher{
		client: &http.Client{
			Timeout: timeout,
		},
		apiKey:  config.APIKey,
		apiURL:  apiURL,
		enabled: true,
	}
}

func (le *LocationEnricher) GetLocation(ctx context.Context, ipAddress string) (*ports.LocationInfo, error) {
	if !le.enabled {
		return &ports.LocationInfo{
			Country: "Unknown",
			City:    "Unknown",
		}, nil
	}

	if ipAddress == "" {
		return nil, fmt.Errorf("IP address is required")
	}

	// Build the request URL
	url := fmt.Sprintf("%s/%s", le.apiURL, ipAddress)
	if le.apiKey != "" {
		url += "?key=" + le.apiKey
	}

	// Create request with context
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "Drive-Service/1.0")

	// Make the request
	resp, err := le.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	// Parse the response
	var apiResp ipAPIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Check for API errors
	if apiResp.Status == "fail" {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}

	// Convert to our format
	location := &ports.LocationInfo{
		Country:   apiResp.Country,
		City:      apiResp.City,
		Latitude:  apiResp.Latitude,
		Longitude: apiResp.Longitude,
	}

	return location, nil
}

// MockLocationEnricher provides a mock implementation for testing
type MockLocationEnricher struct{}

func NewMockLocationEnricher() *MockLocationEnricher {
	return &MockLocationEnricher{}
}

func (mle *MockLocationEnricher) GetLocation(ctx context.Context, ipAddress string) (*ports.LocationInfo, error) {
	// Return mock data for testing
	return &ports.LocationInfo{
		Country:   "United States",
		City:      "San Francisco",
		Latitude:  floatPtr(37.7749),
		Longitude: floatPtr(-122.4194),
	}, nil
}

func floatPtr(f float64) *float64 {
	return &f
}
