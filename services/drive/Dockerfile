# Optimized Drive Service Build - Uses storage-specific base with pre-cached dependencies
FROM swork/storage-base:latest AS builder

# Copy source code (dependencies already cached in storage-base)
COPY . .

# Build the drive service with BuildKit cache mounts and optimizations
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux go build \
    -ldflags="-w -s" \
    -a -installsuffix cgo \
    -o drive-service ./services/drive/cmd/main.go

# Optimized runtime stage with media processing capabilities
FROM alpine:3.19

# Install drive service runtime dependencies in one layer
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    curl \
    wget \
    ffmpeg \
    imagemagick \
    && adduser -D -s /bin/sh appuser \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy the optimized binary from builder stage
COPY --from=builder /app/drive-service .
RUN chown appuser:appuser drive-service && chmod +x drive-service

# Create directories for thumbnails, uploads, and placeholders with optimal permissions
RUN mkdir -p /app/thumbnails /data/uploads /app/assets/placeholders && \
    chown -R appuser:appuser /app/thumbnails /data/uploads /app/assets

# Switch to non-root user for security
USER appuser

# Expose port
EXPOSE 8080

# Optimized health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the binary
CMD ["./drive-service"]