-- Add performance indexes for drive service
-- This migration adds missing indexes for frequently queried fields

-- Files table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_checksum ON files(checksum) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_user_id_created_at ON files(user_id, created_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_team_id_created_at ON files(team_id, created_at DESC) WHERE deleted_at IS NULL AND team_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_folder_id_created_at ON files(folder_id, created_at DESC) WHERE deleted_at IS NULL AND folder_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_status ON files(status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_search_text ON files USING gin(to_tsvector('english', name || ' ' || COALESCE(path, ''))) WHERE deleted_at IS NULL;


-- Upload sessions table indexes  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_upload_sessions_user_id ON upload_sessions(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_upload_sessions_status ON upload_sessions(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_upload_sessions_expires_at ON upload_sessions(expires_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_upload_sessions_updated_at ON upload_sessions(updated_at);

-- File shares table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_file_shares_shared_with ON file_shares(shared_with);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_file_shares_file_id ON file_shares(file_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_file_shares_expires_at ON file_shares(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_file_shares_active ON file_shares(file_id, shared_with) WHERE expires_at > NOW();

-- Folders table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_folders_user_id ON folders(user_id) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_folders_team_id ON folders(team_id) WHERE deleted_at IS NULL AND team_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_folders_parent_id ON folders(parent_id) WHERE deleted_at IS NULL AND parent_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_folders_path ON folders(path) WHERE deleted_at IS NULL;

-- Audit logs table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_id_timestamp ON audit_logs(user_id, timestamp DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_resource_type_id ON audit_logs(resource_type, resource_id) WHERE resource_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address) WHERE ip_address IS NOT NULL;

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_user_team_folder ON files(user_id, team_id, folder_id, created_at DESC) WHERE deleted_at IS NULL;

-- Comments about the indexes:
-- 1. CONCURRENTLY option allows index creation without blocking writes
-- 2. Partial indexes with WHERE clauses reduce index size and improve performance
-- 3. Composite indexes support multi-column queries and sorting
-- 4. GIN index for full-text search on file names and paths
-- 5. Indexes are designed to support the most common query patterns identified in the codebase