package models

import (
	"time"

	"github.com/google/uuid"
	sharedModels "github.com/swork-team/platform/pkg/models"
	"gorm.io/gorm"
)

// Use the shared User model - no duplication
type User = sharedModels.User

type FriendRequest struct {
	ID          uuid.UUID           `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	RequesterID uuid.UUID           `json:"requester_id" gorm:"type:uuid;not null;index"`
	ReceiverID  uuid.UUID           `json:"receiver_id" gorm:"type:uuid;not null;index"`
	Status      FriendRequestStatus `json:"status" gorm:"default:'pending'"`
	Message     string              `json:"message" gorm:"type:text"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`

	// Relationships use shared User model
	Requester User `json:"requester" gorm:"foreignKey:RequesterID"`
	Receiver  User `json:"receiver" gorm:"foreignKey:ReceiverID"`
}

type Follow struct {
	ID         uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	FollowerID uuid.UUID `json:"follower_id" gorm:"type:uuid;not null;index"`
	FollowedID uuid.UUID `json:"followed_id" gorm:"type:uuid;not null;index"`
	CreatedAt  time.Time `json:"created_at"`

	// Relationships use shared User model
	Follower User `json:"follower" gorm:"foreignKey:FollowerID"`
	Followed User `json:"followed" gorm:"foreignKey:FollowedID"`
}

type Block struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	BlockerID uuid.UUID `json:"blocker_id" gorm:"type:uuid;not null;index"`
	BlockedID uuid.UUID `json:"blocked_id" gorm:"type:uuid;not null;index"`
	Reason    string    `json:"reason" gorm:"type:text"`
	CreatedAt time.Time `json:"created_at"`

	// Relationships use shared User model
	Blocker User `json:"blocker" gorm:"foreignKey:BlockerID"`
	Blocked User `json:"blocked" gorm:"foreignKey:BlockedID"`
}

type UserSettings struct {
	ID                    uuid.UUID `json:"id" gorm:"type:uuid;primaryKey"`
	UserID                uuid.UUID `json:"user_id" gorm:"type:uuid;not null;uniqueIndex"`
	ProfileVisibility     string    `json:"profile_visibility" gorm:"default:'public'"` // public, friends, private
	EmailNotifications    bool      `json:"email_notifications" gorm:"default:true"`
	PushNotifications     bool      `json:"push_notifications" gorm:"default:true"`
	FriendRequestsEnabled bool      `json:"friend_requests_enabled" gorm:"default:true"`
	ShowOnlineStatus      bool      `json:"show_online_status" gorm:"default:true"`
	ShowLastSeen          bool      `json:"show_last_seen" gorm:"default:true"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`

	// Relationship uses shared User model
	User User `json:"user" gorm:"foreignKey:UserID"`
}

type FriendRequestStatus string

const (
	FriendRequestPending  FriendRequestStatus = "pending"
	FriendRequestAccepted FriendRequestStatus = "accepted"
	FriendRequestRejected FriendRequestStatus = "rejected"
)

// BeforeCreate hooks for UUID generation
func (fr *FriendRequest) BeforeCreate(tx *gorm.DB) (err error) {
	if fr.ID == uuid.Nil {
		fr.ID = uuid.New()
	}
	return
}

func (f *Follow) BeforeCreate(tx *gorm.DB) (err error) {
	if f.ID == uuid.Nil {
		f.ID = uuid.New()
	}
	return
}

func (b *Block) BeforeCreate(tx *gorm.DB) (err error) {
	if b.ID == uuid.Nil {
		b.ID = uuid.New()
	}
	return
}

func (us *UserSettings) BeforeCreate(tx *gorm.DB) (err error) {
	if us.ID == uuid.Nil {
		us.ID = uuid.New()
	}
	return
}

// Custom table names
func (FriendRequest) TableName() string {
	return "friend_requests"
}

func (Follow) TableName() string {
	return "follows"
}

func (Block) TableName() string {
	return "blocks"
}

func (UserSettings) TableName() string {
	return "user_settings"
}
