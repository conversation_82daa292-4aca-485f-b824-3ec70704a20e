package handlers

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/user/internal/models"
	"github.com/swork-team/platform/services/user/internal/services"
	userUtils "github.com/swork-team/platform/services/user/internal/utils"
)

type UserHandler struct {
	userService *services.UserService
}

func NewUserHandler(userService *services.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// @Summary Get user profile
// @Description Get user profile by ID or current user
// @Tags users
// @Security BearerAuth
// @Param id path string false "User ID (optional, defaults to current user)"
// @Success 200 {object} object{data=object}
// @Failure 401 {object} object
// @Failure 404 {object} object
// @Router /users/{id} [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userIDParam := c.Param("id")
	var userID uuid.UUID
	var err error

	if userIDParam == "" || userIDParam == "me" {
		// Get current user
		currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
		if currentUserIDStr == "" {
			return // Error already handled by middleware
		}
		userID, err = uuid.Parse(currentUserIDStr)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
			return
		}
	} else {
		userID = middleware.MustParseUUIDParam(c, "id")
		if userID == uuid.Nil {
			return // Error already handled by middleware
		}
	}

	user, err := h.userService.GetProfile(userID)
	if err != nil {
		if middleware.HandleStandardError(c, err, "User") {
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user profile", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Profile retrieved successfully", user)
}

// @Summary Get profile by username
// @Description Get user profile by username
// @Tags users
// @Param username path string true "Username"
// @Success 200 {object} object{data=object}
// @Failure 404 {object} object
// @Router /users/username/{username} [get]
func (h *UserHandler) GetProfileByUsername(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Username is required", nil)
		return
	}

	user, err := h.userService.GetProfileByUsername(username)
	if err != nil {
		if middleware.HandleStandardError(c, err, "User") {
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user profile", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Profile retrieved successfully", user)
}

// @Summary Get multiple users by IDs
// @Description Get multiple user profiles by their IDs (batch operation)
// @Tags users
// @Security BearerAuth
// @Param ids query string true "Comma-separated user IDs (max 50)"
// @Success 200 {object} object{data=object}
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/batch [get]
func (h *UserHandler) GetUsersBatch(c *gin.Context) {
	idsParam := c.Query("ids")
	if idsParam == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "ids parameter required", nil)
		return
	}

	userIDs := strings.Split(idsParam, ",")
	if len(userIDs) > 50 {
		utils.ErrorResponse(c, http.StatusBadRequest, "too many ids, maximum 50 allowed", nil)
		return
	}

	users, err := h.userService.GetUsersByIDs(c.Request.Context(), userIDs)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "failed to fetch users", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "users retrieved successfully", map[string]interface{}{
		"users": users,
		"count": len(users),
	})
}

// @Summary Update user profile
// @Description Update current user's profile
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body object true "Profile update data"
// @Success 200 {object} object{data=object}
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/me [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	logger.NewDefaultLogger("user-service").Info("DEBUG HANDLER: UpdateProfile called")
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	var reqData map[string]interface{}
	if !middleware.BindJSONRequest(c, &reqData) {
		return // Error already handled by middleware
	}

	// Comprehensive validation using new validation utility
	fmt.Printf("DEBUG HANDLER: About to call ValidateUserProfileUpdate with data: %+v\n", reqData)
	validationErrors := userUtils.ValidateUserProfileUpdate(reqData)
	fmt.Printf("DEBUG HANDLER: ValidateUserProfileUpdate returned %d errors: %v\n", len(validationErrors), validationErrors)
	if len(validationErrors) > 0 {
		fmt.Printf("DEBUG HANDLER: Returning validation error: %s\n", validationErrors.Error())
		utils.ErrorResponse(c, http.StatusBadRequest, validationErrors.Error(), validationErrors)
		return
	}

	// Sanitize and convert to UpdateProfileRequest
	req := services.UpdateProfileRequest{}

	if firstName, ok := reqData["first_name"].(string); ok {
		firstName = userUtils.SanitizeInput(firstName)
		firstName = utils.SanitizeHTML(firstName)
		req.FirstName = &firstName
	}

	if lastName, ok := reqData["last_name"].(string); ok {
		lastName = userUtils.SanitizeInput(lastName)
		lastName = utils.SanitizeHTML(lastName)
		req.LastName = &lastName
	}

	if bio, ok := reqData["bio"].(string); ok {
		bio = userUtils.SanitizeInput(bio)
		bio = utils.SanitizeHTML(bio)
		req.Bio = &bio
	}

	if location, ok := reqData["location"].(string); ok {
		location = userUtils.SanitizeInput(location)
		location = utils.SanitizeHTML(location)
		req.Location = &location
	}

	if website, ok := reqData["website"].(string); ok {
		website = userUtils.SanitizeInput(website)
		req.Website = &website
	}

	if phoneNumber, ok := reqData["phone_number"].(string); ok {
		phoneNumber = userUtils.SanitizeInput(phoneNumber)
		req.PhoneNumber = &phoneNumber
	}

	if gender, ok := reqData["gender"].(string); ok {
		gender = userUtils.SanitizeInput(gender)
		req.Gender = &gender
	}

	if timezone, ok := reqData["timezone"].(string); ok {
		timezone = userUtils.SanitizeInput(timezone)
		req.Timezone = &timezone
	}

	if language, ok := reqData["language"].(string); ok {
		language = userUtils.SanitizeInput(language)
		req.Language = &language
	}

	user, err := h.userService.UpdateProfile(userID, &req)
	if err != nil {
		if middleware.HandleStandardError(c, err, "User") {
			return
		}
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, err.Error(), err,
			"update_profile", logger.F("user_id", userID.String()))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Profile updated successfully", user,
		"update_profile",
		logger.F("user_id", userID.String()),
		logger.F("updated_fields", getUpdatedFields(&req)))
}

// @Summary Delete user profile
// @Description Delete current user's profile
// @Tags users
// @Security BearerAuth
// @Success 200 {object} object
// @Failure 401 {object} object
// @Router /users/me [delete]
func (h *UserHandler) DeleteProfile(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	if err := h.userService.DeleteProfile(userID); err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to delete profile", err,
			"delete_profile", logger.F("user_id", userID.String()))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Profile deleted successfully", nil,
		"delete_profile", logger.F("user_id", userID.String()))
	utils.LogSecurityEvent(c, "user_account_deletion", true,
		logger.F("user_id", userID.String()))
}

// @Summary Search users
// @Description Search for users by name, username, or email
// @Tags users
// @Security BearerAuth
// @Param q query string true "Search query"
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Success 200 {object} object
// @Failure 400 {object} object
// @Router /users/search [get]
func (h *UserHandler) SearchUsers(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Search query is required", nil)
		return
	}

	// Validate and sanitize search query
	query = userUtils.SanitizeInput(query)

	// Validate search query length
	if !userUtils.ValidateTextLength(query, 1, 100) {
		utils.ErrorResponse(c, http.StatusBadRequest, "Search query must be between 1 and 100 characters", nil)
		return
	}

	// Check for security threats in search query
	if result := userUtils.ValidateForSecurityThreats(query); !result.IsValid {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid search query", nil)
		return
	}

	pagination := middleware.ValidatePagination(c)
	limit := pagination.Limit
	offset := 0 // Using cursor-based pagination

	// Additional limit validation
	if limit > 100 {
		limit = 100 // Cap at 100 results per page
	}

	result, err := h.userService.SearchUsers(query, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to search users", err)
		return
	}

	meta := utils.PaginationMeta{
		Page:       (offset / limit) + 1,
		Limit:      limit,
		Total:      result.Total,
		TotalPages: int((result.Total + int64(limit) - 1) / int64(limit)),
	}

	utils.PaginatedSuccessResponse(c, result.Users, meta)
}

// @Summary Get user suggestions
// @Description Get suggested users to connect with
// @Tags users
// @Security BearerAuth
// @Param limit query int false "Number of suggestions" default(10)
// @Success 200 {object} utils.APIResponse{data=[]models.User}
// @Failure 401 {object} object
// @Router /users/suggestions [get]
func (h *UserHandler) GetUserSuggestions(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	users, err := h.userService.GetUserSuggestions(userID, limit)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get suggestions", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Suggestions retrieved successfully", users)
}

// @Summary Send friend request
// @Description Send a friend request to another user
// @Tags friends
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body object true "Friend request data"
// @Success 201 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/friend-requests [post]
func (h *UserHandler) SendFriendRequest(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	var req services.FriendRequestRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Validate and sanitize the message
	if req.Message != "" {
		req.Message = userUtils.SanitizeInput(req.Message)
		req.Message = utils.SanitizeHTML(req.Message)

		// Validate message length
		if !userUtils.ValidateTextLength(req.Message, 0, 500) {
			utils.ErrorResponse(c, http.StatusBadRequest, "Message must be less than 500 characters", nil)
			return
		}

		// Check for security threats in message
		if result := userUtils.ValidateForSecurityThreats(req.Message); !result.IsValid {
			utils.ErrorResponse(c, http.StatusBadRequest, "Message contains invalid characters", nil)
			return
		}
	}

	if err := h.userService.SendFriendRequest(userID, &req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Friend request sent successfully", nil)
}

// @Summary Respond to friend request
// @Description Accept or reject a friend request
// @Tags friends
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Friend request ID"
// @Param request body object true "Response data"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 404 {object} object
// @Router /users/friend-requests/{id}/respond [put]
func (h *UserHandler) RespondToFriendRequest(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	requestID := middleware.MustParseUUIDParam(c, "id")
	if requestID == uuid.Nil {
		return // Error already handled by middleware
	}

	var req struct {
		Action string `json:"action" binding:"required"` // "accept" or "reject"
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	if req.Action != "accept" && req.Action != "reject" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Action must be 'accept' or 'reject'", nil)
		return
	}

	accept := req.Action == "accept"
	if err := h.userService.RespondToFriendRequest(userID, requestID, accept); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	message := "Friend request rejected"
	if accept {
		message = "Friend request accepted"
	}
	utils.SuccessResponse(c, http.StatusOK, message, nil)
}

// @Summary Cancel friend request
// @Description Cancel a sent friend request
// @Tags friends
// @Security BearerAuth
// @Param id path string true "Friend request ID"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 404 {object} object
// @Router /users/friend-requests/{id}/cancel [delete]
func (h *UserHandler) CancelFriendRequest(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	requestID := middleware.MustParseUUIDParam(c, "id")
	if requestID == uuid.Nil {
		return // Error already handled by middleware
	}

	if err := h.userService.CancelFriendRequest(userID, requestID); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Friend request cancelled", nil)
}

// @Summary Remove friend
// @Description Remove a user from friends list
// @Tags friends
// @Security BearerAuth
// @Param id path string true "User ID to unfriend"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/{id}/unfriend [delete]
func (h *UserHandler) UnfriendUser(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	friendID := middleware.MustParseUUIDParam(c, "id")
	if friendID == uuid.Nil {
		return // Error already handled by middleware
	}

	if err := h.userService.UnfriendUser(userID, friendID); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "User removed from friends", nil)
}

// @Summary Get relationship status
// @Description Get the relationship status between current user and another user
// @Tags relationships
// @Security BearerAuth
// @Param id path string true "User ID to check relationship with"
// @Success 200 {object} utils.APIResponse{data=object}
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/{id}/relationship [get]
func (h *UserHandler) GetRelationshipStatus(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	otherUserID := middleware.MustParseUUIDParam(c, "id")
	if otherUserID == uuid.Nil {
		return // Error already handled by middleware
	}

	if userID == otherUserID {
		utils.SuccessResponse(c, http.StatusOK, "Relationship status retrieved", map[string]interface{}{
			"relationship_type": "self",
			"is_friend":         false,
			"is_following":      false,
			"is_followed_by":    false,
			"is_blocked":        false,
		})
		return
	}

	status, err := h.userService.GetRelationshipStatus(userID, otherUserID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get relationship status", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Relationship status retrieved", status)
}

// @Summary Get friend requests
// @Description Get received friend requests
// @Tags friends
// @Security BearerAuth
// @Param status query string false "Filter by status (pending, accepted, rejected)"
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Success 200 {object} utils.PaginatedResponse{data=[]models.FriendRequest}
// @Failure 401 {object} object
// @Router /users/friend-requests [get]
func (h *UserHandler) GetFriendRequests(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	status := models.FriendRequestStatus(c.Query("status"))
	pagination := middleware.ValidatePagination(c)
	limit := pagination.Limit
	offset := 0 // Using cursor-based pagination

	requests, total, err := h.userService.GetFriendRequests(userID, status, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get friend requests", err)
		return
	}

	meta := utils.PaginationMeta{
		Page:       (offset / limit) + 1,
		Limit:      limit,
		Total:      total,
		TotalPages: int((total + int64(limit) - 1) / int64(limit)),
	}

	utils.PaginatedSuccessResponse(c, requests, meta)
}

// @Summary Get sent friend requests
// @Description Get friend requests sent by current user
// @Tags friends
// @Security BearerAuth
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Success 200 {object} utils.PaginatedResponse{data=[]models.FriendRequest}
// @Failure 401 {object} object
// @Router /users/friend-requests/sent [get]
func (h *UserHandler) GetSentFriendRequests(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	pagination := middleware.ValidatePagination(c)
	limit := pagination.Limit
	offset := 0 // Using cursor-based pagination

	requests, total, err := h.userService.GetSentFriendRequests(userID, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get sent friend requests", err)
		return
	}

	meta := utils.PaginationMeta{
		Page:       (offset / limit) + 1,
		Limit:      limit,
		Total:      total,
		TotalPages: int((total + int64(limit) - 1) / int64(limit)),
	}

	utils.PaginatedSuccessResponse(c, requests, meta)
}

// @Summary Get friends
// @Description Get user's friends list
// @Tags friends
// @Security BearerAuth
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Success 200 {object} utils.PaginatedResponse{data=[]models.User}
// @Failure 401 {object} object
// @Router /users/friends [get]
func (h *UserHandler) GetFriends(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	pagination := middleware.ValidatePagination(c)
	limit := pagination.Limit
	offset := 0 // Using cursor-based pagination

	friends, total, err := h.userService.GetFriends(userID, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get friends", err)
		return
	}

	meta := utils.PaginationMeta{
		Page:       (offset / limit) + 1,
		Limit:      limit,
		Total:      total,
		TotalPages: int((total + int64(limit) - 1) / int64(limit)),
	}

	utils.PaginatedSuccessResponse(c, friends, meta)
}

// @Summary Follow user
// @Description Follow another user
// @Tags follows
// @Security BearerAuth
// @Param id path string true "User ID to follow"
// @Success 201 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/{id}/follow [post]
func (h *UserHandler) FollowUser(c *gin.Context) {
	currentUserIDStr := middleware.MustGetAuthenticatedUser(c)
	if currentUserIDStr == "" {
		return // Error already handled by middleware
	}

	followerID, err := uuid.Parse(currentUserIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	followedID := middleware.MustParseUUIDParam(c, "id")
	if followedID == uuid.Nil {
		return // Error already handled by middleware
	}

	if err := h.userService.FollowUser(followerID, followedID); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "User followed successfully", nil)
}

// @Summary Unfollow user
// @Description Unfollow a user
// @Tags follows
// @Security BearerAuth
// @Param id path string true "User ID to unfollow"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/{id}/follow [delete]
func (h *UserHandler) UnfollowUser(c *gin.Context) {
	followerIDStr := middleware.MustGetAuthenticatedUser(c)
	if followerIDStr == "" {
		return // Error already handled by middleware
	}

	followerID, err := uuid.Parse(followerIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	followedID := middleware.MustParseUUIDParam(c, "id")
	if followedID == uuid.Nil {
		return // Error already handled by middleware
	}

	if err := h.userService.UnfollowUser(followerID, followedID); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to unfollow user", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "User unfollowed successfully", nil)
}

// @Summary Get followers
// @Description Get user's followers
// @Tags follows
// @Security BearerAuth
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Success 200 {object} utils.PaginatedResponse{data=[]models.User}
// @Failure 401 {object} object
// @Router /users/followers [get]
func (h *UserHandler) GetFollowers(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	pagination := middleware.ValidatePagination(c)
	limit := pagination.Limit
	offset := 0 // Using cursor-based pagination

	followers, total, err := h.userService.GetFollowers(userID, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get followers", err)
		return
	}

	meta := utils.PaginationMeta{
		Page:       (offset / limit) + 1,
		Limit:      limit,
		Total:      total,
		TotalPages: int((total + int64(limit) - 1) / int64(limit)),
	}

	utils.PaginatedSuccessResponse(c, followers, meta)
}

// @Summary Get following
// @Description Get users that current user is following
// @Tags follows
// @Security BearerAuth
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Success 200 {object} utils.PaginatedResponse{data=[]models.User}
// @Failure 401 {object} object
// @Router /users/following [get]
func (h *UserHandler) GetFollowing(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	pagination := middleware.ValidatePagination(c)
	limit := pagination.Limit
	offset := 0 // Using cursor-based pagination

	following, total, err := h.userService.GetFollowing(userID, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get following", err)
		return
	}

	meta := utils.PaginationMeta{
		Page:       (offset / limit) + 1,
		Limit:      limit,
		Total:      total,
		TotalPages: int((total + int64(limit) - 1) / int64(limit)),
	}

	utils.PaginatedSuccessResponse(c, following, meta)
}

// @Summary Block user
// @Description Block another user
// @Tags blocks
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "User ID to block"
// @Param request body map[string]string false "Block reason"
// @Success 201 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/{id}/block [post]
func (h *UserHandler) BlockUser(c *gin.Context) {
	blockerIDStr := middleware.MustGetAuthenticatedUser(c)
	if blockerIDStr == "" {
		return // Error already handled by middleware
	}

	blockerID, err := uuid.Parse(blockerIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	blockedID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID format", err)
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}
	middleware.BindJSONRequest(c, &req) // Optional binding, errors ignored

	if err := h.userService.BlockUser(blockerID, blockedID, req.Reason); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "User blocked successfully", nil)
}

// @Summary Unblock user
// @Description Unblock a user
// @Tags blocks
// @Security BearerAuth
// @Param id path string true "User ID to unblock"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/{id}/block [delete]
func (h *UserHandler) UnblockUser(c *gin.Context) {
	blockerIDStr := middleware.MustGetAuthenticatedUser(c)
	if blockerIDStr == "" {
		return // Error already handled by middleware
	}

	blockerID, err := uuid.Parse(blockerIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	blockedID := middleware.MustParseUUIDParam(c, "id")
	if blockedID == uuid.Nil {
		return // Error already handled by middleware
	}

	if err := h.userService.UnblockUser(blockerID, blockedID); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to unblock user", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "User unblocked successfully", nil)
}

// @Summary Get blocked users
// @Description Get list of blocked users
// @Tags blocks
// @Security BearerAuth
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Success 200 {object} utils.PaginatedResponse{data=[]models.User}
// @Failure 401 {object} object
// @Router /users/blocked [get]
func (h *UserHandler) GetBlockedUsers(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	pagination := middleware.ValidatePagination(c)
	limit := pagination.Limit
	offset := 0 // Using cursor-based pagination

	blocked, total, err := h.userService.GetBlockedUsers(userID, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get blocked users", err)
		return
	}

	meta := utils.PaginationMeta{
		Page:       (offset / limit) + 1,
		Limit:      limit,
		Total:      total,
		TotalPages: int((total + int64(limit) - 1) / int64(limit)),
	}

	utils.PaginatedSuccessResponse(c, blocked, meta)
}

// @Summary Get user settings
// @Description Get current user's settings
// @Tags settings
// @Security BearerAuth
// @Success 200 {object} object
// @Failure 401 {object} object
// @Router /users/settings [get]
func (h *UserHandler) GetUserSettings(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	settings, err := h.userService.GetUserSettings(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user settings", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Settings retrieved successfully", settings)
}

// @Summary Update user settings
// @Description Update current user's settings
// @Tags settings
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body object true "User settings"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Router /users/settings [put]
func (h *UserHandler) UpdateUserSettings(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	var settings models.UserSettings
	if !middleware.BindJSONRequest(c, &settings) {
		return // Error already handled by middleware
	}

	updatedSettings, err := h.userService.UpdateUserSettings(userID, &settings)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update settings", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Settings updated successfully", updatedSettings)
}

// Health check endpoint
func (h *UserHandler) HealthCheck(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "User service is healthy", map[string]string{
		"service": "user-service",
		"status":  "healthy",
	})
}

// ============================================================================
// INTERNAL SERVICE-TO-SERVICE ENDPOINTS
// ============================================================================

// CreateUserProfileInternal creates a user profile from auth service
func (h *UserHandler) CreateUserProfileInternal(c *gin.Context) {
	// Validate service token
	if !middleware.ValidateServiceToken(c) {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid service token", nil)
		return
	}

	var req struct {
		UserID        uuid.UUID `json:"user_id" binding:"required"`
		Email         string    `json:"email,omitempty"`
		PhoneNumber   string    `json:"phone_number,omitempty"`
		FirstName     string    `json:"first_name" binding:"required"`
		LastName      string    `json:"last_name" binding:"required"`
		Username      string    `json:"username" binding:"required"`
		EmailVerified bool      `json:"email_verified"`
		PhoneVerified bool      `json:"phone_verified"`
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	// Validate input data
	if err := h.validateUserCreationRequest(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	// Check if user already exists (idempotency)
	existingUser, err := h.userService.GetProfile(req.UserID)
	if err == nil && existingUser != nil {
		utils.SuccessResponse(c, http.StatusOK, "User profile already exists", existingUser)
		return
	}

	// Check username availability
	usernameExists, err := h.userService.UsernameExists(req.Username)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to check username", err)
		return
	}
	if usernameExists {
		utils.ErrorResponse(c, http.StatusConflict, "username already taken", nil)
		return
	}

	// Check email if provided
	if req.Email != "" {
		emailExists, err := h.userService.EmailExists(req.Email)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to check email", err)
			return
		}
		if emailExists {
			utils.ErrorResponse(c, http.StatusConflict, "email already exists", nil)
			return
		}
	}

	// Create user profile
	user := &models.User{
		ID:            req.UserID,
		Email:         req.Email,
		PhoneNumber:   req.PhoneNumber,
		FirstName:     req.FirstName,
		LastName:      req.LastName,
		Username:      req.Username,
		EmailVerified: req.EmailVerified,
		PhoneVerified: req.PhoneVerified,
		IsActive:      true,
		Timezone:      "UTC",
		Language:      "en",
	}

	if err := h.userService.CreateUserProfile(user); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create user profile", err)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "User profile created successfully", user)
}

// GetUserProfileInternal gets a user profile by ID for service-to-service calls
func (h *UserHandler) GetUserProfileInternal(c *gin.Context) {
	if !middleware.ValidateServiceToken(c) {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid service token", nil)
		return
	}

	userIDParam := c.Param("id")
	userID, err := uuid.Parse(userIDParam)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}

	user, err := h.userService.GetProfile(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "User not found", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "User profile retrieved", user)
}

// GetUsersBatchInternal gets multiple users by IDs for batch operations
func (h *UserHandler) GetUsersBatchInternal(c *gin.Context) {
	if !middleware.ValidateServiceToken(c) {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid service token", nil)
		return
	}

	var req struct {
		UserIDs []string `json:"user_ids" binding:"required"`
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	if len(req.UserIDs) == 0 {
		utils.SuccessResponse(c, http.StatusOK, "No users requested", []*models.User{})
		return
	}

	if len(req.UserIDs) > 50 {
		utils.ErrorResponse(c, http.StatusBadRequest, "Too many user IDs, maximum 50 allowed", nil)
		return
	}

	users, err := h.userService.GetUsersByIDs(c.Request.Context(), req.UserIDs)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get users", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Users retrieved successfully", users)
}

// ValidateUsernameInternal validates username availability
func (h *UserHandler) ValidateUsernameInternal(c *gin.Context) {
	if !middleware.ValidateServiceToken(c) {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid service token", nil)
		return
	}

	var req struct {
		Value string `json:"value" binding:"required"`
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	exists, err := h.userService.UsernameExists(req.Value)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to validate username", err)
		return
	}

	if exists {
		utils.ErrorResponse(c, http.StatusConflict, "username already taken", nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Username is available", nil)
}

// ValidateEmailInternal validates email availability
func (h *UserHandler) ValidateEmailInternal(c *gin.Context) {
	if !middleware.ValidateServiceToken(c) {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid service token", nil)
		return
	}

	var req struct {
		Value string `json:"value" binding:"required"`
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	if req.Value == "" {
		utils.SuccessResponse(c, http.StatusOK, "Empty email is valid", nil)
		return
	}

	exists, err := h.userService.EmailExists(req.Value)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to validate email", err)
		return
	}

	if exists {
		utils.ErrorResponse(c, http.StatusConflict, "email already exists", nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Email is available", nil)
}

// Helper function to track updated fields for logging
func getUpdatedFields(req *services.UpdateProfileRequest) []string {
	var fields []string
	if req.FirstName != nil {
		fields = append(fields, "first_name")
	}
	if req.LastName != nil {
		fields = append(fields, "last_name")
	}
	if req.Bio != nil {
		fields = append(fields, "bio")
	}
	if req.Location != nil {
		fields = append(fields, "location")
	}
	if req.Website != nil {
		fields = append(fields, "website")
	}
	if req.PhoneNumber != nil {
		fields = append(fields, "phone_number")
	}
	if req.Gender != nil {
		fields = append(fields, "gender")
	}
	if req.Timezone != nil {
		fields = append(fields, "timezone")
	}
	if req.Language != nil {
		fields = append(fields, "language")
	}
	return fields
}

// validateUserCreationRequest validates the user creation request
func (h *UserHandler) validateUserCreationRequest(req *struct {
	UserID        uuid.UUID `json:"user_id" binding:"required"`
	Email         string    `json:"email,omitempty"`
	PhoneNumber   string    `json:"phone_number,omitempty"`
	FirstName     string    `json:"first_name" binding:"required"`
	LastName      string    `json:"last_name" binding:"required"`
	Username      string    `json:"username" binding:"required"`
	EmailVerified bool      `json:"email_verified"`
	PhoneVerified bool      `json:"phone_verified"`
}) error {
	// Validate first name
	if len(strings.TrimSpace(req.FirstName)) == 0 {
		return errors.New("first name cannot be empty")
	}
	if len(req.FirstName) > 100 {
		return errors.New("first name must be less than 100 characters")
	}
	// Security validation for first name
	if secResult := userUtils.ValidateForSecurityThreats(req.FirstName); !secResult.IsValid {
		return errors.New("first name contains invalid characters")
	}

	// Validate last name
	if len(strings.TrimSpace(req.LastName)) == 0 {
		return errors.New("last name cannot be empty")
	}
	if len(req.LastName) > 100 {
		return errors.New("last name must be less than 100 characters")
	}
	// Security validation for last name
	if secResult := userUtils.ValidateForSecurityThreats(req.LastName); !secResult.IsValid {
		return errors.New("last name contains invalid characters")
	}

	// Validate username
	if !userUtils.ValidateUsername(req.Username) {
		return errors.New("username format is invalid (3-50 characters, alphanumeric, underscore, hyphen only)")
	}

	// Validate email if provided
	if req.Email != "" {
		if !userUtils.ValidateEmail(req.Email) {
			return errors.New("email format is invalid")
		}
	}

	// Validate phone number if provided
	if req.PhoneNumber != "" {
		if !userUtils.ValidatePhoneNumber(req.PhoneNumber) {
			return errors.New("phone number format is invalid")
		}
	}

	// Ensure at least one contact method is provided
	if req.Email == "" && req.PhoneNumber == "" {
		return errors.New("either email or phone number must be provided")
	}

	return nil
}
