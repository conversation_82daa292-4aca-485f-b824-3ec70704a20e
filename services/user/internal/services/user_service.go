package services

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/async"
	"github.com/swork-team/platform/pkg/cache"
	"github.com/swork-team/platform/pkg/webhook"
	"github.com/swork-team/platform/services/user/internal/config"
	"github.com/swork-team/platform/services/user/internal/models"
	"github.com/swork-team/platform/services/user/internal/repositories"
	"gorm.io/gorm"
)

type UserService struct {
	userRepo       repositories.CachedUserRepositoryInterface
	config         *config.Config
	cacheManager   *cache.DirectCacheManager
	asyncJobQueue  *async.AsyncJobQueue
	webhookService webhook.WebhookService
}

type UpdateProfileRequest struct {
	FirstName   *string    `json:"first_name"`
	LastName    *string    `json:"last_name"`
	Bio         *string    `json:"bio"`
	Location    *string    `json:"location"`
	Website     *string    `json:"website"`
	DateOfBirth *time.Time `json:"date_of_birth"`
	PhoneNumber *string    `json:"phone_number"`
	Gender      *string    `json:"gender"`
	Timezone    *string    `json:"timezone"`
	Language    *string    `json:"language"`
}

type FriendRequestRequest struct {
	ReceiverID uuid.UUID `json:"receiver_id" binding:"required"`
	Message    string    `json:"message"`
}

type SearchResponse struct {
	Users []models.User `json:"users"`
	Total int64         `json:"total"`
}

// UserEvent struct removed - replaced with async jobs and webhooks

func NewUserService(userRepo repositories.CachedUserRepositoryInterface, config *config.Config, cacheManager *cache.DirectCacheManager, asyncJobQueue *async.AsyncJobQueue, webhookService webhook.WebhookService) *UserService {
	return &UserService{
		userRepo:       userRepo,
		config:         config,
		cacheManager:   cacheManager,
		asyncJobQueue:  asyncJobQueue,
		webhookService: webhookService,
	}
}

// Profile management
func (s *UserService) GetProfile(userID uuid.UUID) (*models.User, error) {
	// Get directly from database - caching handled by event-driven system
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// Update last seen asynchronously
	go s.userRepo.UpdateLastSeen(userID)

	return user, nil
}

func (s *UserService) GetProfileByUsername(username string) (*models.User, error) {
	// Get directly from database - caching handled by event-driven system
	user, err := s.userRepo.GetByUsername(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	return user, nil
}

func (s *UserService) UpdateProfile(userID uuid.UUID, req *UpdateProfileRequest) (*models.User, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// Comprehensive input validation
	if err := s.validateProfileUpdateRequest(req); err != nil {
		return nil, err
	}

	// Track changed fields
	var changedFields []string

	// Update fields if provided
	if req.FirstName != nil && user.FirstName != *req.FirstName {
		user.FirstName = *req.FirstName
		changedFields = append(changedFields, "first_name")
	}
	if req.LastName != nil && user.LastName != *req.LastName {
		user.LastName = *req.LastName
		changedFields = append(changedFields, "last_name")
	}
	if req.Bio != nil && user.Bio != *req.Bio {
		user.Bio = *req.Bio
		changedFields = append(changedFields, "bio")
	}
	if req.Location != nil && user.Location != *req.Location {
		user.Location = *req.Location
		changedFields = append(changedFields, "location")
	}
	if req.Website != nil && user.Website != *req.Website {
		user.Website = *req.Website
		changedFields = append(changedFields, "website")
	}
	if req.DateOfBirth != nil && !reflect.DeepEqual(user.DateOfBirth, req.DateOfBirth) {
		user.DateOfBirth = req.DateOfBirth
		changedFields = append(changedFields, "date_of_birth")
	}
	if req.PhoneNumber != nil && user.PhoneNumber != *req.PhoneNumber {
		user.PhoneNumber = *req.PhoneNumber
		changedFields = append(changedFields, "phone_number")
	}
	if req.Gender != nil && user.Gender != *req.Gender {
		user.Gender = *req.Gender
		changedFields = append(changedFields, "gender")
	}
	if req.Timezone != nil && user.Timezone != *req.Timezone {
		user.Timezone = *req.Timezone
		changedFields = append(changedFields, "timezone")
	}
	if req.Language != nil && user.Language != *req.Language {
		user.Language = *req.Language
		changedFields = append(changedFields, "language")
	}

	if err := s.userRepo.Update(user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Direct cache invalidation with retry logic
	s.invalidateUserCacheWithRetry(userID.String())

	// Async notification (replace Kafka event)
	go s.safeNotify(func() {
		s.sendProfileUpdateNotifications(user, changedFields)
	})

	return user, nil
}

func (s *UserService) DeleteProfile(userID uuid.UUID) error {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	if err := s.userRepo.Delete(userID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	// Direct cache invalidation with retry logic
	s.invalidateUserCacheWithRetry(userID.String())

	// Async notification for profile deletion
	go s.safeNotify(func() {
		s.sendProfileDeleteNotifications(user)
	})

	return nil
}

// Search functionality
func (s *UserService) SearchUsers(query string, limit, offset int) (*SearchResponse, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	var users []models.User
	var total int64
	var err error

	// Use cached search if enabled
	if s.config.Cache.EnableSearchCache {
		users, total, err = s.userRepo.SearchWithCache(context.Background(), query, limit, offset)
	} else {
		users, total, err = s.userRepo.Search(query, limit, offset)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to search users: %w", err)
	}

	return &SearchResponse{
		Users: users,
		Total: total,
	}, nil
}

func (s *UserService) GetUserSuggestions(userID uuid.UUID, limit int) ([]models.User, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	var users []models.User
	var err error

	// Use cached suggestions if enabled
	if s.config.Cache.EnableSuggestionsCache {
		users, err = s.userRepo.GetSuggestionsWithCache(context.Background(), userID, limit)
	} else {
		users, err = s.userRepo.GetSuggestions(userID, limit)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get suggestions: %w", err)
	}

	return users, nil
}

// Friend request management
func (s *UserService) SendFriendRequest(requesterID uuid.UUID, req *FriendRequestRequest) error {
	if requesterID == req.ReceiverID {
		return errors.New("cannot send friend request to yourself")
	}

	// Check if receiver exists
	receiver, err := s.userRepo.GetByID(req.ReceiverID)
	if err != nil {
		return fmt.Errorf("failed to get receiver: %w", err)
	}
	if receiver == nil {
		return errors.New("receiver not found")
	}

	// Check if they're already friends
	areFriends, err := s.userRepo.AreFriends(requesterID, req.ReceiverID)
	if err != nil {
		return fmt.Errorf("failed to check friendship: %w", err)
	}
	if areFriends {
		return errors.New("users are already friends")
	}

	// Check if request already exists
	existingReq, err := s.userRepo.GetFriendRequest(requesterID, req.ReceiverID)
	if err != nil {
		return fmt.Errorf("failed to check existing request: %w", err)
	}
	if existingReq != nil {
		return errors.New("friend request already sent")
	}

	// Check if receiver has sent a request to requester
	reverseReq, err := s.userRepo.GetFriendRequest(req.ReceiverID, requesterID)
	if err != nil {
		return fmt.Errorf("failed to check reverse request: %w", err)
	}
	if reverseReq != nil {
		// Auto-accept the reverse request
		if err := s.userRepo.UpdateFriendRequestStatus(reverseReq.ID, models.FriendRequestAccepted); err != nil {
			return fmt.Errorf("failed to accept reverse request: %w", err)
		}

		// Async notification for friend request acceptance
		go s.sendFriendRequestNotifications("friend_request_accepted", requesterID.String(), req.ReceiverID.String())

		return nil
	}

	// Check if blocked
	blocked, err := s.userRepo.IsBlocked(req.ReceiverID, requesterID)
	if err != nil {
		return fmt.Errorf("failed to check block status: %w", err)
	}
	if blocked {
		return errors.New("cannot send friend request to this user")
	}

	// Create friend request
	friendReq := &models.FriendRequest{
		RequesterID: requesterID,
		ReceiverID:  req.ReceiverID,
		Message:     req.Message,
		Status:      models.FriendRequestPending,
	}

	if err := s.userRepo.CreateFriendRequest(friendReq); err != nil {
		return fmt.Errorf("failed to create friend request: %w", err)
	}

	// Publish event
	// Async notification for friend request
	go s.sendFriendRequestNotifications("friend_request_sent", requesterID.String(), req.ReceiverID.String())

	return nil
}

func (s *UserService) RespondToFriendRequest(userID, requestID uuid.UUID, accept bool) error {
	// Get the friend request to verify it exists and is for this user
	friendRequest, err := s.userRepo.GetFriendRequestByID(requestID)
	if err != nil {
		return fmt.Errorf("failed to get friend request: %w", err)
	}
	if friendRequest == nil {
		return errors.New("friend request not found")
	}

	// Verify the current user is the receiver of this request
	if friendRequest.ReceiverID != userID {
		return errors.New("you can only respond to friend requests sent to you")
	}

	// Check if request is still pending
	if friendRequest.Status != models.FriendRequestPending {
		return errors.New("friend request has already been responded to")
	}

	// Update the request status
	status := models.FriendRequestRejected
	eventType := "friend_request_rejected"
	if accept {
		status = models.FriendRequestAccepted
		eventType = "friend_request_accepted"
	}

	if err := s.userRepo.UpdateFriendRequestStatus(requestID, status); err != nil {
		return fmt.Errorf("failed to update friend request status: %w", err)
	}

	// Send notification
	go s.sendFriendRequestNotifications(eventType, friendRequest.RequesterID.String(), userID.String())

	return nil
}

// CancelFriendRequest cancels a sent friend request
func (s *UserService) CancelFriendRequest(userID, requestID uuid.UUID) error {
	// Get the friend request to verify it exists and is from this user
	friendRequest, err := s.userRepo.GetFriendRequestByID(requestID)
	if err != nil {
		return fmt.Errorf("failed to get friend request: %w", err)
	}
	if friendRequest == nil {
		return errors.New("friend request not found")
	}

	// Verify the current user is the requester
	if friendRequest.RequesterID != userID {
		return errors.New("you can only cancel friend requests you sent")
	}

	// Check if request is still pending
	if friendRequest.Status != models.FriendRequestPending {
		return errors.New("can only cancel pending friend requests")
	}

	// Delete the friend request
	if err := s.userRepo.DeleteFriendRequest(requestID); err != nil {
		return fmt.Errorf("failed to cancel friend request: %w", err)
	}

	// Send notification
	go s.sendFriendRequestNotifications("friend_request_cancelled", userID.String(), friendRequest.ReceiverID.String())

	return nil
}

// UnfriendUser removes friendship between two users
func (s *UserService) UnfriendUser(userID, friendID uuid.UUID) error {
	if userID == friendID {
		return errors.New("cannot unfriend yourself")
	}

	// Check if they are actually friends
	areFriends, err := s.userRepo.AreFriends(userID, friendID)
	if err != nil {
		return fmt.Errorf("failed to check friendship: %w", err)
	}
	if !areFriends {
		return errors.New("users are not friends")
	}

	// Remove the friendship by updating the friend request status
	if err := s.userRepo.RemoveFriendship(userID, friendID); err != nil {
		return fmt.Errorf("failed to remove friendship: %w", err)
	}

	// Send notification
	go s.sendFriendRequestNotifications("friendship_removed", userID.String(), friendID.String())

	return nil
}


// GetRelationshipStatus gets the complete relationship status between two users
func (s *UserService) GetRelationshipStatus(userID, otherUserID uuid.UUID) (*repositories.RelationshipStatus, error) {
	// Use cached relationship status if enabled
	if s.config.Cache.EnableRelationshipCache {
		return s.userRepo.GetRelationshipStatusWithCache(context.Background(), userID, otherUserID)
	}

	// Fallback to non-cached implementation
	status := &repositories.RelationshipStatus{
		RelationshipType: "none",
		IsFriend:         false,
		IsFollowing:      false,
		IsFollowedBy:     false,
		IsBlocked:        false,
		IsBlockedBy:      false,
	}

	// Check if they are friends
	areFriends, err := s.userRepo.AreFriends(userID, otherUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check friendship: %w", err)
	}
	status.IsFriend = areFriends
	if areFriends {
		status.RelationshipType = "friend"
	}

	// Check if blocked
	isBlocked, err := s.userRepo.IsBlocked(userID, otherUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check block status: %w", err)
	}
	status.IsBlocked = isBlocked
	if isBlocked {
		status.RelationshipType = "blocked"
	}

	// Check if blocked by other user
	isBlockedBy, err := s.userRepo.IsBlocked(otherUserID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check reverse block status: %w", err)
	}
	status.IsBlockedBy = isBlockedBy

	// Check following status
	isFollowing, err := s.userRepo.IsFollowing(userID, otherUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check following status: %w", err)
	}
	status.IsFollowing = isFollowing

	isFollowedBy, err := s.userRepo.IsFollowing(otherUserID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check follower status: %w", err)
	}
	status.IsFollowedBy = isFollowedBy

	// Check for pending friend requests if not friends and not blocked
	if !areFriends && !isBlocked && !isBlockedBy {
		// Check if current user sent a request
		sentRequest, err := s.userRepo.GetFriendRequest(userID, otherUserID)
		if err != nil {
			return nil, fmt.Errorf("failed to check sent friend request: %w", err)
		}
		if sentRequest != nil && sentRequest.Status == models.FriendRequestPending {
			status.RelationshipType = "pending_sent"
			status.PendingFriendRequest = sentRequest
			status.FriendRequestStatus = &sentRequest.Status
		}

		// Check if current user received a request
		receivedRequest, err := s.userRepo.GetFriendRequest(otherUserID, userID)
		if err != nil {
			return nil, fmt.Errorf("failed to check received friend request: %w", err)
		}
		if receivedRequest != nil && receivedRequest.Status == models.FriendRequestPending {
			status.RelationshipType = "pending_received"
			status.PendingFriendRequest = receivedRequest
			status.FriendRequestStatus = &receivedRequest.Status
		}
	}

	return status, nil
}

func (s *UserService) GetFriendRequests(userID uuid.UUID, status models.FriendRequestStatus, limit, offset int) ([]models.FriendRequest, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return s.userRepo.GetFriendRequests(userID, status, limit, offset)
}

func (s *UserService) GetSentFriendRequests(userID uuid.UUID, limit, offset int) ([]models.FriendRequest, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return s.userRepo.GetSentFriendRequests(userID, limit, offset)
}

func (s *UserService) GetFriends(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return s.userRepo.GetFriends(userID, limit, offset)
}

// Follow functionality
func (s *UserService) FollowUser(followerID, followedID uuid.UUID) error {
	if followerID == followedID {
		return errors.New("cannot follow yourself")
	}

	// Check if user exists and is active
	user, err := s.userRepo.GetByID(followedID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}
	if !user.IsActive {
		return errors.New("cannot follow inactive user")
	}

	// Check if already following
	isFollowing, err := s.userRepo.IsFollowing(followerID, followedID)
	if err != nil {
		return fmt.Errorf("failed to check follow status: %w", err)
	}
	if isFollowing {
		return errors.New("already following user")
	}

	// Check if blocked by the user we want to follow
	blocked, err := s.userRepo.IsBlocked(followedID, followerID)
	if err != nil {
		return fmt.Errorf("failed to check block status: %w", err)
	}
	if blocked {
		return errors.New("cannot follow this user")
	}

	// Check if we have blocked this user
	hasBlocked, err := s.userRepo.IsBlocked(followerID, followedID)
	if err != nil {
		return fmt.Errorf("failed to check block status: %w", err)
	}
	if hasBlocked {
		return errors.New("cannot follow blocked user")
	}

	follow := &models.Follow{
		FollowerID: followerID,
		FollowedID: followedID,
	}

	if err := s.userRepo.CreateFollow(follow); err != nil {
		return fmt.Errorf("failed to create follow: %w", err)
	}

	// Enhanced notification with user context
	go s.safeNotify(func() {
		s.sendFollowNotifications("user_followed", followerID.String(), followedID.String())
	})

	return nil
}

func (s *UserService) UnfollowUser(followerID, followedID uuid.UUID) error {
	if err := s.userRepo.DeleteFollow(followerID, followedID); err != nil {
		return fmt.Errorf("failed to unfollow user: %w", err)
	}

	// Async notification for unfollow
	go s.safeNotify(func() {
		s.sendFollowNotifications("user_unfollowed", followerID.String(), followedID.String())
	})

	return nil
}

func (s *UserService) GetFollowers(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return s.userRepo.GetFollowers(userID, limit, offset)
}

func (s *UserService) GetFollowing(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return s.userRepo.GetFollowing(userID, limit, offset)
}

// Block functionality
func (s *UserService) BlockUser(blockerID, blockedID uuid.UUID, reason string) error {
	if blockerID == blockedID {
		return errors.New("cannot block yourself")
	}

	// Check if user exists
	user, err := s.userRepo.GetByID(blockedID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return errors.New("user not found")
	}

	// Check if already blocked
	blocked, err := s.userRepo.IsBlocked(blockerID, blockedID)
	if err != nil {
		return fmt.Errorf("failed to check block status: %w", err)
	}
	if blocked {
		return errors.New("user already blocked")
	}

	// Use database transaction for atomic operations
	return s.userRepo.Transaction(func(tx *gorm.DB) error {
		block := &models.Block{
			BlockerID: blockerID,
			BlockedID: blockedID,
			Reason:    reason,
		}

		if err := tx.Create(block).Error; err != nil {
			return fmt.Errorf("failed to create block: %w", err)
		}

		// Remove any existing follows within transaction
		if err := tx.Where("follower_id = ? AND followed_id = ?", blockerID, blockedID).Delete(&models.Follow{}).Error; err != nil {
			return fmt.Errorf("failed to remove follow relationship: %w", err)
		}
		if err := tx.Where("follower_id = ? AND followed_id = ?", blockedID, blockerID).Delete(&models.Follow{}).Error; err != nil {
			return fmt.Errorf("failed to remove reverse follow relationship: %w", err)
		}

		// Send notification after successful transaction
		go s.safeNotify(func() {
			s.sendBlockNotifications("user_blocked", blockerID.String(), blockedID.String())
		})

		return nil
	})
}

func (s *UserService) UnblockUser(blockerID, blockedID uuid.UUID) error {
	if err := s.userRepo.DeleteBlock(blockerID, blockedID); err != nil {
		return fmt.Errorf("failed to unblock user: %w", err)
	}

	// Async notification for unblock
	go s.sendBlockNotifications("user_unblocked", blockerID.String(), blockedID.String())

	return nil
}

func (s *UserService) GetBlockedUsers(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return s.userRepo.GetBlockedUsers(userID, limit, offset)
}

// Settings management
func (s *UserService) GetUserSettings(userID uuid.UUID) (*models.UserSettings, error) {
	settings, err := s.userRepo.GetUserSettings(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user settings: %w", err)
	}

	// Create default settings if none exist
	if settings == nil {
		settings = &models.UserSettings{
			UserID:                userID,
			ProfileVisibility:     "public",
			EmailNotifications:    true,
			PushNotifications:     true,
			FriendRequestsEnabled: true,
			ShowOnlineStatus:      true,
			ShowLastSeen:          true,
		}

		if err := s.userRepo.CreateUserSettings(settings); err != nil {
			return nil, fmt.Errorf("failed to create user settings: %w", err)
		}
	}

	return settings, nil
}

func (s *UserService) UpdateUserSettings(userID uuid.UUID, settings *models.UserSettings) (*models.UserSettings, error) {
	// Use UPSERT pattern to avoid race conditions
	settings.UserID = userID

	if err := s.userRepo.UpsertUserSettings(settings); err != nil {
		return nil, fmt.Errorf("failed to update user settings: %w", err)
	}

	// Publish event
	// Async notification for settings update
	go s.sendSettingsUpdateNotifications(userID.String(), settings)

	return settings, nil
}

// GetUsersByIDs retrieves multiple users by their IDs (batch operation)
func (s *UserService) GetUsersByIDs(ctx context.Context, userIDs []string) ([]*models.User, error) {
	if len(userIDs) == 0 {
		return []*models.User{}, nil
	}

	if len(userIDs) > 50 {
		return nil, errors.New("too many user IDs requested, maximum 50 allowed")
	}

	// Convert strings to UUIDs
	uuids := make([]uuid.UUID, 0, len(userIDs))
	for _, idStr := range userIDs {
		if parsedUUID, err := uuid.Parse(idStr); err == nil {
			uuids = append(uuids, parsedUUID)
		}
	}

	users, err := s.userRepo.GetUsersByIDs(uuids)
	if err != nil {
		return nil, fmt.Errorf("failed to get users by IDs: %w", err)
	}

	// Convert to pointers
	userPtrs := make([]*models.User, len(users))
	for i := range users {
		userPtrs[i] = &users[i]
	}

	return userPtrs, nil
}

// CreateUserProfile creates a new user profile (called from auth service)
func (s *UserService) CreateUserProfile(user *models.User) error {
	// Set defaults if not provided
	if user.Timezone == "" {
		user.Timezone = "UTC"
	}
	if user.Language == "" {
		user.Language = "en"
	}

	// Use database transaction for atomic operations
	return s.userRepo.Transaction(func(tx *gorm.DB) error {
		// Create user profile within transaction
		if err := tx.Create(user).Error; err != nil {
			return fmt.Errorf("failed to create user profile: %w", err)
		}

		// Create default user settings within transaction
		settings := &models.UserSettings{
			UserID:                user.ID,
			ProfileVisibility:     "public",
			EmailNotifications:    true,
			PushNotifications:     true,
			FriendRequestsEnabled: true,
			ShowOnlineStatus:      true,
			ShowLastSeen:          true,
		}

		if err := tx.Create(settings).Error; err != nil {
			return fmt.Errorf("failed to create user settings: %w", err)
		}

		// Cache invalidation after successful transaction with retry logic
		s.invalidateUserCacheWithRetry(user.ID.String())

		return nil
	})
}

// EmailExists checks if an email is already in use
func (s *UserService) EmailExists(email string) (bool, error) {
	if email == "" {
		return false, nil
	}
	return s.userRepo.EmailExists(email)
}

// UsernameExists checks if a username is already in use
func (s *UserService) UsernameExists(username string) (bool, error) {
	return s.userRepo.UsernameExists(username)
}

// Cache helper methods

// invalidateUserCacheWithRetry attempts to invalidate user cache with retry logic
func (s *UserService) invalidateUserCacheWithRetry(userID string) {
	const maxRetries = 3
	const retryDelay = 100 * time.Millisecond

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	for attempt := 1; attempt <= maxRetries; attempt++ {
		if err := s.cacheManager.InvalidateUser(ctx, userID); err != nil {
			if attempt == maxRetries {
				// Log final failure but don't fail the operation
				fmt.Printf("Cache invalidation failed after %d attempts for user %s: %v\n", maxRetries, userID, err)
				return
			}
			// Wait before retry
			time.Sleep(retryDelay * time.Duration(attempt))
			continue
		}
		// Success
		if attempt > 1 {
			fmt.Printf("Cache invalidation succeeded on attempt %d for user %s\n", attempt, userID)
		}
		return
	}
}

// Notification methods (replace Kafka events)

// safeNotify executes notification functions safely with timeout and error handling
func (s *UserService) safeNotify(notifyFunc func()) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("Notification panic recovered: %v\n", r)
		}
	}()

	// Create a context with timeout for notification operations
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	done := make(chan bool, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Notification function panic: %v\n", r)
			}
		}()
		notifyFunc()
		done <- true
	}()

	select {
	case <-done:
		// Notification completed successfully
	case <-ctx.Done():
		fmt.Printf("Notification timeout: %v\n", ctx.Err())
	}
}

func (s *UserService) sendProfileUpdateNotifications(user *models.User, changedFields []string) {
	// Enqueue async job for notifications
	job := &async.AsyncJob{
		Type:   "user_profile_updated",
		UserID: user.ID.String(),
		Data: map[string]interface{}{
			"user_id":        user.ID.String(),
			"username":       user.Username,
			"first_name":     user.FirstName,
			"last_name":      user.LastName,
			"changed_fields": changedFields,
			"timestamp":      time.Now().Format(time.RFC3339),
		},
	}

	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue profile update notification: %v\n", err)
	}

	// Send webhook for external integrations
	webhookReq := &webhook.WebhookRequest{
		Event:  "profile_updated",
		UserID: user.ID.String(),
		Data: map[string]interface{}{
			"user_id":        user.ID.String(),
			"username":       user.Username,
			"changed_fields": changedFields,
			"timestamp":      time.Now().Format(time.RFC3339),
		},
	}

	if err := s.webhookService.SendWebhook(webhookReq); err != nil {
		fmt.Printf("Failed to send profile update webhook: %v\n", err)
	}
}

func (s *UserService) sendProfileDeleteNotifications(user *models.User) {
	// Enqueue async job for delete notifications
	job := &async.AsyncJob{
		Type:   "user_profile_deleted",
		UserID: user.ID.String(),
		Data: map[string]interface{}{
			"user_id":   user.ID.String(),
			"username":  user.Username,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue profile delete notification: %v\n", err)
	}

	// Send webhook for external integrations
	webhookReq := &webhook.WebhookRequest{
		Event:  "profile_deleted",
		UserID: user.ID.String(),
		Data: map[string]interface{}{
			"user_id":   user.ID.String(),
			"username":  user.Username,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	if err := s.webhookService.SendWebhook(webhookReq); err != nil {
		fmt.Printf("Failed to send profile delete webhook: %v\n", err)
	}
}

func (s *UserService) sendFriendRequestNotifications(eventType, userID1, userID2 string) {
	// Get user details for enhanced notifications
	user1UUID, _ := uuid.Parse(userID1)
	user2UUID, _ := uuid.Parse(userID2)

	var user1, user2 *models.User

	user1, _ = s.userRepo.GetByID(user1UUID)
	user2, _ = s.userRepo.GetByID(user2UUID)

	// Determine notification recipient based on event type
	var recipientID, actorID string
	var notificationText string

	switch eventType {
	case "friend_request_sent":
		recipientID = userID2 // Receiver gets notification
		actorID = userID1     // Sender is the actor
		if user1 != nil {
			notificationText = fmt.Sprintf("%s sent you a friend request", user1.GetFullName())
		}
	case "friend_request_accepted":
		recipientID = userID1 // Original sender gets notification
		actorID = userID2     // Accepter is the actor
		if user2 != nil {
			notificationText = fmt.Sprintf("%s accepted your friend request", user2.GetFullName())
		}
	case "friend_request_rejected":
		// Usually don't notify about rejections for privacy
		return
	case "friend_request_cancelled":
		// Usually don't notify about cancellations
		return
	case "friendship_removed":
		// Usually don't notify about unfriending for privacy
		return
	default:
		recipientID = userID2
		actorID = userID1
	}

	if user1 != nil && user2 != nil {
		// Determine which user is the actor for notification data
		var actorUser *models.User
		if actorID == userID1 {
			actorUser = user1
		} else {
			actorUser = user2
		}

		job := &async.AsyncJob{
			Type:   eventType,
			UserID: recipientID,
			Data: map[string]interface{}{
				"event":             eventType,
				"actor_id":          actorID,
				"recipient_id":      recipientID,
				"actor_name":        actorUser.GetFullName(),
				"actor_username":    actorUser.Username,
				"actor_avatar":      actorUser.ProfileImage,
				"recipient_name":    user2.GetFullName(),
				"notification_text": notificationText,
				"timestamp":         time.Now().Format(time.RFC3339),
			},
		}

		if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
			fmt.Printf("Failed to enqueue friend request notification: %v\n", err)
		}

		// Send webhook for external integrations
		webhookReq := &webhook.WebhookRequest{
			Event:  eventType,
			UserID: recipientID,
			Data: map[string]interface{}{
				"event":        eventType,
				"actor_id":     actorID,
				"recipient_id": recipientID,
				"actor_name":   actorUser.GetFullName(),
				"timestamp":    time.Now().Format(time.RFC3339),
			},
		}

		if err := s.webhookService.SendWebhook(webhookReq); err != nil {
			fmt.Printf("Failed to send friend request webhook: %v\n", err)
		}
	}
}

func (s *UserService) sendFollowNotifications(eventType, followerID, followeeID string) {
	// Get user details for richer notifications
	followerUUID, _ := uuid.Parse(followerID)
	followeeUUID, _ := uuid.Parse(followeeID)

	var followerUser, followeeUser *models.User
	var err error

	if followerUser, err = s.userRepo.GetByID(followerUUID); err == nil && followerUser != nil {
		// Create enhanced notification job
		job := &async.AsyncJob{
			Type:   eventType,
			UserID: followeeID, // Send notification to the person being followed
			Data: map[string]interface{}{
				"event":             eventType,
				"follower_id":       followerID,
				"followee_id":       followeeID,
				"follower_name":     followerUser.GetFullName(),
				"follower_username": followerUser.Username,
				"follower_avatar":   followerUser.ProfileImage,
				"timestamp":         time.Now().Format(time.RFC3339),
				"notification_text": fmt.Sprintf("%s started following you", followerUser.GetFullName()),
			},
		}

		if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
			fmt.Printf("Failed to enqueue follow notification: %v\n", err)
		}
	}

	// Send webhook for external integrations
	if followeeUser, err = s.userRepo.GetByID(followeeUUID); err == nil && followeeUser != nil {
		webhookReq := &webhook.WebhookRequest{
			Event:  eventType,
			UserID: followeeID,
			Data: map[string]interface{}{
				"event":         eventType,
				"follower_id":   followerID,
				"followee_id":   followeeID,
				"follower_name": followerUser.GetFullName(),
				"followee_name": followeeUser.GetFullName(),
				"timestamp":     time.Now().Format(time.RFC3339),
			},
		}

		if err := s.webhookService.SendWebhook(webhookReq); err != nil {
			fmt.Printf("Failed to send follow webhook: %v\n", err)
		}
	}
}

func (s *UserService) sendBlockNotifications(eventType, blockerID, blockedID string) {
	job := &async.AsyncJob{
		Type:   eventType,
		UserID: blockerID,
		Data: map[string]interface{}{
			"event":      eventType,
			"blocker_id": blockerID,
			"blocked_id": blockedID,
			"timestamp":  time.Now().Format(time.RFC3339),
		},
	}

	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue block notification: %v\n", err)
	}
}

func (s *UserService) sendSettingsUpdateNotifications(userID string, settings *models.UserSettings) {
	job := &async.AsyncJob{
		Type:   "settings_updated",
		UserID: userID,
		Data: map[string]interface{}{
			"event":     "settings_updated",
			"user_id":   userID,
			"settings":  settings,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue settings update notification: %v\n", err)
	}
}

// validateProfileUpdateRequest validates the profile update request with comprehensive checks
func (s *UserService) validateProfileUpdateRequest(req *UpdateProfileRequest) error {
	if req == nil {
		return errors.New("request cannot be nil")
	}

	// Validate first name
	if req.FirstName != nil {
		if len(strings.TrimSpace(*req.FirstName)) == 0 {
			return errors.New("first name cannot be empty")
		}
		if len(*req.FirstName) > 100 {
			return errors.New("first name must be less than 100 characters")
		}
		// Security validation
		if containsScriptTags(*req.FirstName) {
			return errors.New("first name contains invalid characters")
		}
		if containsSqlInjection(*req.FirstName) {
			return errors.New("first name contains invalid characters")
		}
	}

	// Validate last name
	if req.LastName != nil {
		if len(strings.TrimSpace(*req.LastName)) == 0 {
			return errors.New("last name cannot be empty")
		}
		if len(*req.LastName) > 100 {
			return errors.New("last name must be less than 100 characters")
		}
		// Security validation
		if containsScriptTags(*req.LastName) {
			return errors.New("last name contains invalid characters")
		}
		if containsSqlInjection(*req.LastName) {
			return errors.New("last name contains invalid characters")
		}
	}

	// Validate bio
	if req.Bio != nil {
		if len(*req.Bio) > 500 {
			return errors.New("bio must be less than 500 characters")
		}
		// Security validation for bio (more permissive but still safe)
		if containsScriptTags(*req.Bio) {
			return errors.New("bio contains invalid content")
		}
		if containsSqlInjection(*req.Bio) {
			return errors.New("bio contains invalid content")
		}
	}

	// Validate website URL
	if req.Website != nil && *req.Website != "" {
		if !strings.HasPrefix(*req.Website, "http://") && !strings.HasPrefix(*req.Website, "https://") {
			return errors.New("website must be a valid HTTP/HTTPS URL")
		}
	}

	// Validate phone number
	if req.PhoneNumber != nil && *req.PhoneNumber != "" {
		phoneRegex := regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
		if !phoneRegex.MatchString(*req.PhoneNumber) {
			return errors.New("phone number format is invalid")
		}
	}

	// Validate gender
	if req.Gender != nil && *req.Gender != "" {
		validGenders := []string{"male", "female", "non-binary", "other", "prefer-not-to-say"}
		isValid := false
		for _, valid := range validGenders {
			if *req.Gender == valid {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("gender must be one of: male, female, non-binary, other, prefer-not-to-say")
		}
	}

	return nil
}

// validateProfileUpdate - deprecated method for backward compatibility
func (s *UserService) validateProfileUpdate(req *UpdateProfileRequest) error {
	return s.validateProfileUpdateRequest(req)
}

func (s *UserService) Close() error {
	// No Kafka producers to close anymore
	if s.webhookService != nil {
		return s.webhookService.Close()
	}
	return nil
}

// Security helper functions

// containsScriptTags checks for script tags in input
func containsScriptTags(input string) bool {
	if input == "" {
		return false
	}

	// Convert to lowercase for case-insensitive matching
	lowerInput := strings.ToLower(input)

	// Check for common XSS patterns
	xssPatterns := []string{
		"<script",
		"</script>",
		"javascript:",
		"vbscript:",
		"onload=",
		"onerror=",
		"onclick=",
		"onmouseover=",
		"onfocus=",
		"eval(",
		"settimeout(",
		"setinterval(",
		"alert(",
		"document.cookie",
		"document.write",
	}

	for _, pattern := range xssPatterns {
		if strings.Contains(lowerInput, pattern) {
			return true
		}
	}

	return false
}

// containsSqlInjection checks for common SQL injection patterns
func containsSqlInjection(input string) bool {
	if input == "" {
		return false
	}

	// Convert to lowercase for case-insensitive matching
	lowerInput := strings.ToLower(input)

	// Check for common SQL injection patterns
	sqlPatterns := []string{
		"drop table",
		"drop database",
		"delete from",
		"insert into",
		"update set",
		"union select",
		"union all",
		"' or '1'='1",
		"' or 1=1",
		"'; drop",
		"'; delete",
		"'; insert",
		"'; update",
		"exec(",
		"execute(",
		"sp_",
		"xp_",
		"--",
		"/*",
		"*/",
		"@@version",
		"@@servername",
	}

	for _, pattern := range sqlPatterns {
		if strings.Contains(lowerInput, pattern) {
			return true
		}
	}

	return false
}
