package repositories

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/cache"
	"github.com/swork-team/platform/services/user/internal/models"
	"gorm.io/gorm"
)

// CachedUserRepository wraps UserRepository with caching capabilities
type CachedUserRepository struct {
	*UserRepository
	cacheManager *cache.DirectCacheManager
}

// NewCachedUserRepository creates a new cached user repository
func NewCachedUserRepository(db *gorm.DB, cacheManager *cache.DirectCacheManager) CachedUserRepositoryInterface {
	return &CachedUserRepository{
		UserRepository: NewUserRepository(db),
		cacheManager:   cacheManager,
	}
}

// SearchWithCache implements cached search functionality
func (r *CachedUserRepository) SearchWithCache(ctx context.Context, query string, limit, offset int) ([]models.User, int64, error) {
	// Create cache key based on search parameters
	cacheKey := fmt.Sprintf("search:%x:%d:%d", md5.Sum([]byte(query)), limit, offset)
	
	// Try to get from cache first
	var cachedResult struct {
		Users []models.User `json:"users"`
		Total int64         `json:"total"`
	}
	
	if err := r.cacheManager.Get(ctx, cacheKey, &cachedResult); err == nil {
		return cachedResult.Users, cachedResult.Total, nil
	}
	
	// Cache miss - execute search
	users, total, err := r.UserRepository.Search(query, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	
	// Cache the result for 5 minutes with search tag for invalidation
	result := struct {
		Users []models.User `json:"users"`
		Total int64         `json:"total"`
	}{
		Users: users,
		Total: total,
	}
	
	// Use shorter TTL for search results to ensure freshness
	ttl := 5 * time.Minute
	tags := []string{"search", "users"}
	
	if err := r.cacheManager.SetWithTags(ctx, cacheKey, result, ttl, tags); err != nil {
		// Log cache error but don't fail the request
		fmt.Printf("Failed to cache search result: %v\n", err)
	}
	
	return users, total, nil
}

// GetSuggestionsWithCache implements cached user suggestions
func (r *CachedUserRepository) GetSuggestionsWithCache(ctx context.Context, userID uuid.UUID, limit int) ([]models.User, error) {
	cacheKey := fmt.Sprintf("suggestions:%s:%d", userID.String(), limit)
	
	// Try cache first
	var cachedUsers []models.User
	if err := r.cacheManager.Get(ctx, cacheKey, &cachedUsers); err == nil {
		return cachedUsers, nil
	}
	
	// Cache miss - execute suggestion query
	users, err := r.UserRepository.GetSuggestions(userID, limit)
	if err != nil {
		return nil, err
	}
	
	// Cache suggestions for 30 minutes with user-specific tag
	ttl := 30 * time.Minute
	tags := []string{fmt.Sprintf("user:%s", userID.String()), "suggestions"}
	
	if err := r.cacheManager.SetWithTags(ctx, cacheKey, users, ttl, tags); err != nil {
		fmt.Printf("Failed to cache suggestions: %v\n", err)
	}
	
	return users, nil
}

// GetRelationshipStatusWithCache implements cached relationship status checking
func (r *CachedUserRepository) GetRelationshipStatusWithCache(ctx context.Context, userID, otherUserID uuid.UUID) (*RelationshipStatus, error) {
	// Create deterministic cache key (always put smaller UUID first for consistency)
	var cacheKey string
	if userID.String() < otherUserID.String() {
		cacheKey = fmt.Sprintf("relationship:%s:%s", userID.String(), otherUserID.String())
	} else {
		cacheKey = fmt.Sprintf("relationship:%s:%s", otherUserID.String(), userID.String())
	}
	
	// Try cache first
	var cachedStatus RelationshipStatus
	if err := r.cacheManager.Get(ctx, cacheKey, &cachedStatus); err == nil {
		return &cachedStatus, nil
	}
	
	// Cache miss - compute relationship status
	status := &RelationshipStatus{
		RelationshipType: "none",
		IsFriend:         false,
		IsFollowing:      false,
		IsFollowedBy:     false,
		IsBlocked:        false,
		IsBlockedBy:      false,
	}
	
	// Check if they are friends
	areFriends, err := r.UserRepository.AreFriends(userID, otherUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check friendship: %w", err)
	}
	status.IsFriend = areFriends
	if areFriends {
		status.RelationshipType = "friend"
	}
	
	// Check if blocked
	isBlocked, err := r.UserRepository.IsBlocked(userID, otherUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check block status: %w", err)
	}
	status.IsBlocked = isBlocked
	if isBlocked {
		status.RelationshipType = "blocked"
	}
	
	// Check if blocked by other user
	isBlockedBy, err := r.UserRepository.IsBlocked(otherUserID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check reverse block status: %w", err)
	}
	status.IsBlockedBy = isBlockedBy
	
	// Check following status
	isFollowing, err := r.UserRepository.IsFollowing(userID, otherUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check following status: %w", err)
	}
	status.IsFollowing = isFollowing
	
	isFollowedBy, err := r.UserRepository.IsFollowing(otherUserID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check follower status: %w", err)
	}
	status.IsFollowedBy = isFollowedBy
	
	// Check for pending friend requests if not friends and not blocked
	if !areFriends && !isBlocked && !isBlockedBy {
		// Check if current user sent a request
		sentRequest, err := r.UserRepository.GetFriendRequest(userID, otherUserID)
		if err != nil {
			return nil, fmt.Errorf("failed to check sent friend request: %w", err)
		}
		if sentRequest != nil && sentRequest.Status == models.FriendRequestPending {
			status.RelationshipType = "pending_sent"
			status.PendingFriendRequest = sentRequest
			status.FriendRequestStatus = &sentRequest.Status
		}
		
		// Check if current user received a request
		receivedRequest, err := r.UserRepository.GetFriendRequest(otherUserID, userID)
		if err != nil {
			return nil, fmt.Errorf("failed to check received friend request: %w", err)
		}
		if receivedRequest != nil && receivedRequest.Status == models.FriendRequestPending {
			status.RelationshipType = "pending_received"
			status.PendingFriendRequest = receivedRequest
			status.FriendRequestStatus = &receivedRequest.Status
		}
	}
	
	// Cache relationship status for 10 minutes with both user tags for invalidation
	ttl := 10 * time.Minute
	tags := []string{
		fmt.Sprintf("user:%s", userID.String()),
		fmt.Sprintf("user:%s", otherUserID.String()),
		"relationships",
	}
	
	if err := r.cacheManager.SetWithTags(ctx, cacheKey, status, ttl, tags); err != nil {
		fmt.Printf("Failed to cache relationship status: %v\n", err)
	}
	
	return status, nil
}

// InvalidateUserCache invalidates all cached data for a specific user
func (r *CachedUserRepository) InvalidateUserCache(ctx context.Context, userID uuid.UUID) error {
	userTag := fmt.Sprintf("user:%s", userID.String())
	return r.cacheManager.InvalidateByTag(ctx, userTag)
}

// InvalidateSearchCache invalidates all search-related cached data
func (r *CachedUserRepository) InvalidateSearchCache(ctx context.Context) error {
	return r.cacheManager.InvalidateByTag(ctx, "search")
}

// Override methods that should invalidate cache when data changes

// Create overrides the base Create to invalidate relevant caches
func (r *CachedUserRepository) Create(user *models.User) error {
	err := r.UserRepository.Create(user)
	if err != nil {
		return err
	}
	
	// Invalidate search cache since a new user was added
	ctx := context.Background()
	if err := r.InvalidateSearchCache(ctx); err != nil {
		fmt.Printf("Failed to invalidate search cache after user creation: %v\n", err)
	}
	
	return nil
}

// Update overrides the base Update to invalidate relevant caches
func (r *CachedUserRepository) Update(user *models.User) error {
	err := r.UserRepository.Update(user)
	if err != nil {
		return err
	}
	
	// Invalidate user-specific cache
	ctx := context.Background()
	if err := r.InvalidateUserCache(ctx, user.ID); err != nil {
		fmt.Printf("Failed to invalidate user cache after update: %v\n", err)
	}
	
	// Invalidate search cache since user data changed
	if err := r.InvalidateSearchCache(ctx); err != nil {
		fmt.Printf("Failed to invalidate search cache after user update: %v\n", err)
	}
	
	return nil
}

// CreateFriendRequest overrides to invalidate relationship cache
func (r *CachedUserRepository) CreateFriendRequest(req *models.FriendRequest) error {
	err := r.UserRepository.CreateFriendRequest(req)
	if err != nil {
		return err
	}
	
	// Invalidate relationship cache for both users
	ctx := context.Background()
	r.InvalidateUserCache(ctx, req.RequesterID)
	r.InvalidateUserCache(ctx, req.ReceiverID)
	
	return nil
}

// UpdateFriendRequestStatus overrides to invalidate relationship cache
func (r *CachedUserRepository) UpdateFriendRequestStatus(id uuid.UUID, status models.FriendRequestStatus) error {
	// Get the friend request first to know which users to invalidate
	friendRequest, err := r.UserRepository.GetFriendRequestByID(id)
	if err != nil {
		return err
	}
	
	err = r.UserRepository.UpdateFriendRequestStatus(id, status)
	if err != nil {
		return err
	}
	
	// Invalidate relationship cache for both users
	if friendRequest != nil {
		ctx := context.Background()
		r.InvalidateUserCache(ctx, friendRequest.RequesterID)
		r.InvalidateUserCache(ctx, friendRequest.ReceiverID)
	}
	
	return nil
}

// CreateFollow overrides to invalidate relationship cache
func (r *CachedUserRepository) CreateFollow(follow *models.Follow) error {
	err := r.UserRepository.CreateFollow(follow)
	if err != nil {
		return err
	}
	
	// Invalidate relationship cache for both users
	ctx := context.Background()
	r.InvalidateUserCache(ctx, follow.FollowerID)
	r.InvalidateUserCache(ctx, follow.FollowedID)
	
	return nil
}

// DeleteFollow overrides to invalidate relationship cache
func (r *CachedUserRepository) DeleteFollow(followerID, followedID uuid.UUID) error {
	err := r.UserRepository.DeleteFollow(followerID, followedID)
	if err != nil {
		return err
	}
	
	// Invalidate relationship cache for both users
	ctx := context.Background()
	r.InvalidateUserCache(ctx, followerID)
	r.InvalidateUserCache(ctx, followedID)
	
	return nil
}

// CreateBlock overrides to invalidate relationship cache
func (r *CachedUserRepository) CreateBlock(block *models.Block) error {
	err := r.UserRepository.CreateBlock(block)
	if err != nil {
		return err
	}
	
	// Invalidate relationship cache for both users
	ctx := context.Background()
	r.InvalidateUserCache(ctx, block.BlockerID)
	r.InvalidateUserCache(ctx, block.BlockedID)
	
	return nil
}

// DeleteBlock overrides to invalidate relationship cache
func (r *CachedUserRepository) DeleteBlock(blockerID, blockedID uuid.UUID) error {
	err := r.UserRepository.DeleteBlock(blockerID, blockedID)
	if err != nil {
		return err
	}
	
	// Invalidate relationship cache for both users
	ctx := context.Background()
	r.InvalidateUserCache(ctx, blockerID)
	r.InvalidateUserCache(ctx, blockedID)
	
	return nil
}