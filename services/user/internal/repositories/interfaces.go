package repositories

import (
	"context"
	"github.com/google/uuid"
	"github.com/swork-team/platform/services/user/internal/models"
	"gorm.io/gorm"
)

// UserRepositoryInterface defines the contract for user repository operations
type UserRepositoryInterface interface {
	// Transaction operations
	Transaction(fn func(*gorm.DB) error) error

	// Basic CRUD operations
	Create(user *models.User) error
	GetByID(id uuid.UUID) (*models.User, error)
	GetUsersByIDs(ids []uuid.UUID) ([]models.User, error)
	GetByEmail(email string) (*models.User, error)
	GetByUsername(username string) (*models.User, error)
	Update(user *models.User) error
	Delete(id uuid.UUID) error
	UpdateLastSeen(userID uuid.UUID) error

	// Search operations
	Search(query string, limit, offset int) ([]models.User, int64, error)
	GetSuggestions(userID uuid.UUID, limit int) ([]models.User, error)

	// Friend request operations
	CreateFriendRequest(req *models.FriendRequest) error
	GetFriendRequest(requesterID, receiverID uuid.UUID) (*models.FriendRequest, error)
	GetFriendRequestByID(id uuid.UUID) (*models.FriendRequest, error)
	DeleteFriendRequest(id uuid.UUID) error
	RemoveFriendship(userID1, userID2 uuid.UUID) error
	UpdateFriendRequestStatus(id uuid.UUID, status models.FriendRequestStatus) error
	GetFriendRequests(userID uuid.UUID, status models.FriendRequestStatus, limit, offset int) ([]models.FriendRequest, int64, error)
	GetSentFriendRequests(userID uuid.UUID, limit, offset int) ([]models.FriendRequest, int64, error)
	GetFriends(userID uuid.UUID, limit, offset int) ([]models.User, int64, error)
	AreFriends(userID1, userID2 uuid.UUID) (bool, error)

	// Follow operations
	CreateFollow(follow *models.Follow) error
	DeleteFollow(followerID, followedID uuid.UUID) error
	GetFollowers(userID uuid.UUID, limit, offset int) ([]models.User, int64, error)
	GetFollowing(userID uuid.UUID, limit, offset int) ([]models.User, int64, error)
	IsFollowing(followerID, followedID uuid.UUID) (bool, error)

	// Block operations
	CreateBlock(block *models.Block) error
	DeleteBlock(blockerID, blockedID uuid.UUID) error
	IsBlocked(blockerID, blockedID uuid.UUID) (bool, error)
	GetBlockedUsers(userID uuid.UUID, limit, offset int) ([]models.User, int64, error)

	// User settings operations
	CreateUserSettings(settings *models.UserSettings) error
	GetUserSettings(userID uuid.UUID) (*models.UserSettings, error)
	UpdateUserSettings(settings *models.UserSettings) error
	UpsertUserSettings(settings *models.UserSettings) error

	// Utility methods
	EmailExists(email string) (bool, error)
	UsernameExists(username string) (bool, error)
}

// CachedUserRepositoryInterface extends the base interface with caching operations
type CachedUserRepositoryInterface interface {
	UserRepositoryInterface
	
	// Cached search operations
	SearchWithCache(ctx context.Context, query string, limit, offset int) ([]models.User, int64, error)
	GetSuggestionsWithCache(ctx context.Context, userID uuid.UUID, limit int) ([]models.User, error)
	GetRelationshipStatusWithCache(ctx context.Context, userID, otherUserID uuid.UUID) (*RelationshipStatus, error)
	
	// Cache invalidation
	InvalidateUserCache(ctx context.Context, userID uuid.UUID) error
	InvalidateSearchCache(ctx context.Context) error
}

// RelationshipStatus represents the cached relationship between two users
type RelationshipStatus struct {
	RelationshipType     string                      `json:"relationship_type"` // "friend", "none", "blocked", "pending_sent", "pending_received"
	IsFriend             bool                        `json:"is_friend"`
	IsFollowing          bool                        `json:"is_following"`
	IsFollowedBy         bool                        `json:"is_followed_by"`
	IsBlocked            bool                        `json:"is_blocked"`
	IsBlockedBy          bool                        `json:"is_blocked_by"`
	PendingFriendRequest *models.FriendRequest       `json:"pending_friend_request,omitempty"`
	FriendRequestStatus  *models.FriendRequestStatus `json:"friend_request_status,omitempty"`
}