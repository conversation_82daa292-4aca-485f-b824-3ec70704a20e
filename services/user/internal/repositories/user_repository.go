package repositories

import (
	"errors"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/user/internal/models"
	"gorm.io/gorm"
)

type UserRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

// Transaction executes a function within a database transaction
func (r *UserRepository) Transaction(fn func(*gorm.DB) error) error {
	return r.db.Transaction(fn)
}

// User CRUD operations
func (r *UserRepository) Create(user *models.User) error {
	return r.db.Create(user).Error
}

func (r *UserRepository) GetByID(id uuid.UUID) (*models.User, error) {
	var user models.User
	err := r.db.Where("id = ?", id).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetUsersByIDs(ids []uuid.UUID) ([]models.User, error) {
	var users []models.User
	err := r.db.Where("id IN ?", ids).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (r *UserRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	err := r.db.Where("email = ?", email).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetByUsername(username string) (*models.User, error) {
	var user models.User
	err := r.db.Where("username = ?", username).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) Update(user *models.User) error {
	return r.db.Save(user).Error
}

func (r *UserRepository) Delete(id uuid.UUID) error {
	return r.db.Delete(&models.User{}, id).Error
}

func (r *UserRepository) UpdateLastSeen(userID uuid.UUID) error {
	now := time.Now()
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("last_seen", now).Error
}

// Search operations
func (r *UserRepository) Search(query string, limit, offset int) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	// Clean and prepare search query
	cleanQuery := strings.TrimSpace(query)
	if cleanQuery == "" {
		return users, 0, nil
	}

	// Use full-text search with GIN index for better performance
	// The migration creates: idx_users_search_text ON users USING gin(to_tsvector('english', first_name || ' ' || last_name || ' ' || username))

	db := r.db.Model(&models.User{}).Where("is_active = ?", true)

	// Try full-text search first (most efficient)
	fullTextQuery := db.Where(
		"to_tsvector('english', first_name || ' ' || last_name || ' ' || username) @@ plainto_tsquery('english', ?)",
		cleanQuery,
	)

	// Count with full-text search
	if err := fullTextQuery.Count(&total).Error; err != nil {
		// Fallback to LIKE search if full-text search fails
		return r.searchWithLike(cleanQuery, limit, offset)
	}

	// If no results with full-text search, try prefix matching for usernames
	if total == 0 {
		prefixQuery := db.Where("username ILIKE ?", cleanQuery+"%")
		if err := prefixQuery.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		if total > 0 {
			if err := prefixQuery.Limit(limit).Offset(offset).Find(&users).Error; err != nil {
				return nil, 0, err
			}
			return users, total, nil
		}

		// Final fallback to partial LIKE search
		return r.searchWithLike(cleanQuery, limit, offset)
	}

	// Execute full-text search with pagination
	if err := fullTextQuery.Limit(limit).Offset(offset).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// searchWithLike provides fallback LIKE-based search
func (r *UserRepository) searchWithLike(query string, limit, offset int) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	searchQuery := "%" + strings.ToLower(query) + "%"

	// Use indexed columns first (username, email) then non-indexed
	db := r.db.Model(&models.User{}).Where(
		"(username ILIKE ? OR email ILIKE ? OR LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ?)",
		searchQuery, searchQuery, searchQuery, searchQuery,
	).Where("is_active = ?", true)

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := db.Limit(limit).Offset(offset).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

func (r *UserRepository) GetSuggestions(userID uuid.UUID, limit int) ([]models.User, error) {
	var users []models.User

	// Use optimized materialized view for much faster suggestions
	err := r.db.Raw(`
		SELECT 
			u.id,
			u.email,
			u.first_name,
			u.last_name,
			u.username,
			u.profile_image,
			u.cover_image,
			u.bio,
			u.location,
			u.website,
			u.date_of_birth,
			u.phone_number,
			u.phone_verified,
			u.gender,
			u.timezone,
			u.language,
			u.email_verified,
			u.is_active,
			u.is_verified,
			u.last_seen,
			u.created_at,
			u.updated_at
		FROM user_suggestion_scores uss
		JOIN users u ON u.id = uss.suggested_user_id
		WHERE uss.user_id = ?
			AND u.is_active = true
			-- Ensure suggestions are reasonably fresh (computed within last 24 hours)
			AND uss.computed_at > NOW() - INTERVAL '24 hours'
		ORDER BY uss.suggestion_score DESC, uss.mutual_friends_count DESC, u.created_at DESC
		LIMIT ?
	`, userID, limit).Scan(&users).Error

	// Fallback to real-time computation if no cached suggestions exist
	if err != nil || len(users) == 0 {
		// Refresh suggestions for this user and try again
		if refreshErr := r.db.Exec("SELECT refresh_user_suggestions(?)", userID).Error; refreshErr == nil {
			// Retry the query after refresh
			err = r.db.Raw(`
				SELECT 
					u.id,
					u.email,
					u.first_name,
					u.last_name,
					u.username,
					u.profile_image,
					u.cover_image,
					u.bio,
					u.location,
					u.website,
					u.date_of_birth,
					u.phone_number,
					u.phone_verified,
					u.gender,
					u.timezone,
					u.language,
					u.email_verified,
					u.is_active,
					u.is_verified,
					u.last_seen,
					u.created_at,
					u.updated_at
				FROM user_suggestion_scores uss
				JOIN users u ON u.id = uss.suggested_user_id
				WHERE uss.user_id = ?
					AND u.is_active = true
				ORDER BY uss.suggestion_score DESC, uss.mutual_friends_count DESC, u.created_at DESC
				LIMIT ?
			`, userID, limit).Scan(&users).Error
		}
	}

	return users, err
}

// Friend request operations
func (r *UserRepository) CreateFriendRequest(req *models.FriendRequest) error {
	return r.db.Create(req).Error
}

func (r *UserRepository) GetFriendRequest(requesterID, receiverID uuid.UUID) (*models.FriendRequest, error) {
	var req models.FriendRequest
	err := r.db.Preload("Requester").Preload("Receiver").Where("requester_id = ? AND receiver_id = ?", requesterID, receiverID).First(&req).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &req, nil
}

func (r *UserRepository) GetFriendRequestByID(id uuid.UUID) (*models.FriendRequest, error) {
	var req models.FriendRequest
	err := r.db.Preload("Requester").Preload("Receiver").Where("id = ?", id).First(&req).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &req, nil
}

func (r *UserRepository) DeleteFriendRequest(id uuid.UUID) error {
	return r.db.Delete(&models.FriendRequest{}, id).Error
}

func (r *UserRepository) RemoveFriendship(userID1, userID2 uuid.UUID) error {
	// Remove friendship by deleting the accepted friend request
	return r.db.Where(
		"((requester_id = ? AND receiver_id = ?) OR (requester_id = ? AND receiver_id = ?)) AND status = ?",
		userID1, userID2, userID2, userID1, models.FriendRequestAccepted,
	).Delete(&models.FriendRequest{}).Error
}

func (r *UserRepository) UpdateFriendRequestStatus(id uuid.UUID, status models.FriendRequestStatus) error {
	return r.db.Model(&models.FriendRequest{}).Where("id = ?", id).Update("status", status).Error
}

func (r *UserRepository) GetFriendRequests(userID uuid.UUID, status models.FriendRequestStatus, limit, offset int) ([]models.FriendRequest, int64, error) {
	var requests []models.FriendRequest
	var total int64

	query := r.db.Model(&models.FriendRequest{}).Where("receiver_id = ?", userID)
	if status != "" {
		query = query.Where("status = ?", status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Preload("Requester").Preload("Receiver").Limit(limit).Offset(offset).Order("created_at DESC").Find(&requests).Error; err != nil {
		return nil, 0, err
	}

	return requests, total, nil
}

func (r *UserRepository) GetSentFriendRequests(userID uuid.UUID, limit, offset int) ([]models.FriendRequest, int64, error) {
	var requests []models.FriendRequest
	var total int64

	query := r.db.Model(&models.FriendRequest{}).Where("requester_id = ?", userID)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Preload("Requester").Preload("Receiver").Limit(limit).Offset(offset).Order("created_at DESC").Find(&requests).Error; err != nil {
		return nil, 0, err
	}

	return requests, total, nil
}

func (r *UserRepository) GetFriends(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	var friends []models.User
	var total int64

	// Get accepted friend requests where user is either requester or receiver
	subQuery := r.db.Model(&models.FriendRequest{}).Select("CASE WHEN requester_id = ? THEN receiver_id ELSE requester_id END as friend_id", userID).
		Where("(requester_id = ? OR receiver_id = ?) AND status = ?", userID, userID, models.FriendRequestAccepted)

	query := r.db.Model(&models.User{}).Where("id IN (?)", subQuery)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Limit(limit).Offset(offset).Find(&friends).Error; err != nil {
		return nil, 0, err
	}

	return friends, total, nil
}

func (r *UserRepository) AreFriends(userID1, userID2 uuid.UUID) (bool, error) {
	var count int64
	err := r.db.Model(&models.FriendRequest{}).Where(
		"((requester_id = ? AND receiver_id = ?) OR (requester_id = ? AND receiver_id = ?)) AND status = ?",
		userID1, userID2, userID2, userID1, models.FriendRequestAccepted,
	).Count(&count).Error

	return count > 0, err
}

// Follow operations
func (r *UserRepository) CreateFollow(follow *models.Follow) error {
	return r.db.Create(follow).Error
}

func (r *UserRepository) DeleteFollow(followerID, followedID uuid.UUID) error {
	return r.db.Where("follower_id = ? AND followed_id = ?", followerID, followedID).Delete(&models.Follow{}).Error
}

func (r *UserRepository) GetFollowers(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	var followers []models.User
	var total int64

	subQuery := r.db.Model(&models.Follow{}).Select("follower_id").Where("followed_id = ?", userID)
	query := r.db.Model(&models.User{}).Where("id IN (?)", subQuery)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Limit(limit).Offset(offset).Find(&followers).Error; err != nil {
		return nil, 0, err
	}

	return followers, total, nil
}

func (r *UserRepository) GetFollowing(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	var following []models.User
	var total int64

	subQuery := r.db.Model(&models.Follow{}).Select("followed_id").Where("follower_id = ?", userID)
	query := r.db.Model(&models.User{}).Where("id IN (?)", subQuery)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Limit(limit).Offset(offset).Find(&following).Error; err != nil {
		return nil, 0, err
	}

	return following, total, nil
}

func (r *UserRepository) IsFollowing(followerID, followedID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.Model(&models.Follow{}).Where("follower_id = ? AND followed_id = ?", followerID, followedID).Count(&count).Error
	return count > 0, err
}

// Block operations
func (r *UserRepository) CreateBlock(block *models.Block) error {
	return r.db.Create(block).Error
}

func (r *UserRepository) DeleteBlock(blockerID, blockedID uuid.UUID) error {
	return r.db.Where("blocker_id = ? AND blocked_id = ?", blockerID, blockedID).Delete(&models.Block{}).Error
}

func (r *UserRepository) IsBlocked(blockerID, blockedID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.Model(&models.Block{}).Where("blocker_id = ? AND blocked_id = ?", blockerID, blockedID).Count(&count).Error
	return count > 0, err
}

func (r *UserRepository) GetBlockedUsers(userID uuid.UUID, limit, offset int) ([]models.User, int64, error) {
	var blocked []models.User
	var total int64

	subQuery := r.db.Model(&models.Block{}).Select("blocked_id").Where("blocker_id = ?", userID)
	query := r.db.Model(&models.User{}).Where("id IN (?)", subQuery)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Limit(limit).Offset(offset).Find(&blocked).Error; err != nil {
		return nil, 0, err
	}

	return blocked, total, nil
}

// User settings operations
func (r *UserRepository) CreateUserSettings(settings *models.UserSettings) error {
	return r.db.Create(settings).Error
}

func (r *UserRepository) GetUserSettings(userID uuid.UUID) (*models.UserSettings, error) {
	var settings models.UserSettings
	err := r.db.Preload("User").Where("user_id = ?", userID).First(&settings).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &settings, nil
}

func (r *UserRepository) UpdateUserSettings(settings *models.UserSettings) error {
	// Use explicit update with specific fields to avoid duplicate key issues
	return r.db.Model(&models.UserSettings{}).Where("user_id = ?", settings.UserID).Updates(map[string]interface{}{
		"profile_visibility":      settings.ProfileVisibility,
		"email_notifications":     settings.EmailNotifications,
		"push_notifications":      settings.PushNotifications,
		"friend_requests_enabled": settings.FriendRequestsEnabled,
		"show_online_status":      settings.ShowOnlineStatus,
		"show_last_seen":          settings.ShowLastSeen,
		"updated_at":              "NOW()",
	}).Error
}

func (r *UserRepository) UpsertUserSettings(settings *models.UserSettings) error {
	// First, try to find existing settings
	var existing models.UserSettings
	err := r.db.Where("user_id = ?", settings.UserID).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// Create new settings record
		if settings.ID == uuid.Nil {
			settings.ID = uuid.New()
		}
		now := time.Now()
		settings.CreatedAt = now
		settings.UpdatedAt = now

		// Set defaults if not provided
		if settings.ProfileVisibility == "" {
			settings.ProfileVisibility = "public"
		}

		return r.db.Create(settings).Error
	} else if err != nil {
		return err
	} else {
		// Update existing record
		updates := map[string]interface{}{
			"profile_visibility":      settings.ProfileVisibility,
			"email_notifications":     settings.EmailNotifications,
			"push_notifications":      settings.PushNotifications,
			"friend_requests_enabled": settings.FriendRequestsEnabled,
			"show_online_status":      settings.ShowOnlineStatus,
			"show_last_seen":          settings.ShowLastSeen,
			"updated_at":              time.Now(),
		}

		err = r.db.Model(&existing).Updates(updates).Error
		if err != nil {
			return err
		}

		// Return the updated record
		*settings = existing
		return r.db.Where("user_id = ?", settings.UserID).First(settings).Error
	}
}

// Utility methods
// EmailExists checks if an email address is already registered.
// Uses EXISTS query for better performance than COUNT.
func (r *UserRepository) EmailExists(email string) (bool, error) {
	var exists bool
	err := r.db.Model(&models.User{}).
		Select("1").
		Where("email = ?", email).
		Limit(1).
		Find(&exists).Error
	return exists, err
}

// UsernameExists checks if a username is already taken.
// Uses EXISTS query for better performance than COUNT.
func (r *UserRepository) UsernameExists(username string) (bool, error) {
	var exists bool
	err := r.db.Model(&models.User{}).
		Select("1").
		Where("username = ?", username).
		Limit(1).
		Find(&exists).Error
	return exists, err
}
