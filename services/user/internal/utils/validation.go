package utils

import (
	"fmt"
	"regexp"
	"strings"
	"unicode/utf8"
)

var (
	// SQL injection patterns (context-aware to avoid false positives)
	sqlPatterns = []string{
		// SQL injection with quotes and operators (actual threats)
		"' or '1'='1", "\" or \"1\"=\"1", "' or 1=1", "\" or 1=1",
		"'; drop table", "\"; drop table", "' union select", "\" union select",
		"' and 1=1", "\" and 1=1", "' and 1=2", "\" and 1=2",
		"'; exec", "\"; exec", "'; execute", "\"; execute",
		"'; insert", "\"; insert", "'; delete", "\"; delete",
		// SQL comments and terminators
		"--", "/*", "*/", ";--", ";/*",
		// System functions with dangerous context
		"sp_", "xp_", "fn_", "sys.", "@@", "@@version",
		// Database objects
		"information_schema", "sys.objects", "master.dbo", "msdb", "tempdb",
		"pg_tables", "mysql.user", "sqlite_master", "oracle", "sysobjects",
		// Dangerous functions
		"cast(", "convert(", "substring(", "char(", "ascii(", "hex(",
		"load_file", "into outfile", "into dumpfile",
		// Union-based injection patterns
		" union all select", " union select", "union all select", "union select",
		// Boolean-based injection with operators
		" and 1=", " or 1=", " and true", " or true", " and false", " or false",
		// SQL commands in dangerous contexts (with word boundaries)
		"drop table", "create table", "alter table", "truncate table",
		"select from", "insert into", " update set ", "delete from",
	}

	// XSS patterns (comprehensive list)
	xssPatterns = []string{
		// Script tags
		"<script", "</script", "<iframe", "</iframe", "<object", "</object",
		"<embed", "</embed", "<applet", "</applet", "<meta",
		// Event handlers
		"onload=", "onerror=", "onclick=", "onmouseover=", "onmouseout=",
		"onfocus=", "onblur=", "onchange=", "onsubmit=", "onreset=",
		"onselect=", "onunload=", "onabort=", "onkeydown=", "onkeypress=",
		"onkeyup=", "onresize=", "onscroll=", "ondblclick=", "onmousedown=",
		"onmouseup=", "onmousemove=", "oncontextmenu=", "ondrag=", "ondrop=",
		// JavaScript protocols
		"javascript:", "vbscript:", "data:", "livescript:", "mocha:",
		// Dangerous functions
		"eval(", "expression(", "setTimeout(", "setInterval(", "function(",
		"alert(", "confirm(", "prompt(", "document.", "window.",
		// HTML attributes that can execute scripts
		"style=", "background=", "dynsrc=", "lowsrc=",
	}

	// Path traversal patterns
	pathTraversalPatterns = []string{
		"../", "..\\", "..", "%2e%2e", "%252e%252e", "%c0%ae%c0%ae",
		"....//", "....\\\\", "/etc/passwd", "/proc/", "\\windows\\",
		"..%2f", "..%5c", "%2e%2e%2f", "%2e%2e%5c",
	}

	// Command injection patterns (with word boundaries to avoid false positives)
	commandPatterns = []string{
		"|", "&", ";", "`", "$(", "${", "&&", "||", ">", "<", ">>",
		" cat ", " ls ", " pwd", " whoami", " id;", " id ", " ps ", " netstat",
		" wget ", " curl ", " nc ", " telnet ", " ssh ", " ftp ",
		"/bin/", "/usr/bin/", "/sbin/", "cmd.exe", "powershell",
		// More specific dangerous patterns
		";cat", ";ls", ";id", ";ps", ";whoami", ";wget", ";curl",
		"&&cat", "&&ls", "&&id", "&&ps", "&&whoami",
		"||cat", "||ls", "||id", "||ps", "||whoami",
	}

	// Email regex pattern
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

	// URL regex pattern
	urlRegex = regexp.MustCompile(`^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$`)

	// Username regex pattern (alphanumeric, underscore, hyphen)
	usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)

	// Phone number regex pattern (international format)
	phoneRegex = regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
)

// SecurityValidationResult represents the result of security validation
type SecurityValidationResult struct {
	IsValid bool
	Reason  string
	Pattern string
}

// ValidateForSecurityThreats performs comprehensive security validation
func ValidateForSecurityThreats(input string) SecurityValidationResult {
	input = strings.ToLower(input)

	// Check for SQL injection patterns
	for _, pattern := range sqlPatterns {
		if strings.Contains(input, strings.ToLower(pattern)) {
			return SecurityValidationResult{
				IsValid: false,
				Reason:  "Potential SQL injection detected",
				Pattern: pattern,
			}
		}
	}

	// Check for XSS patterns
	for _, pattern := range xssPatterns {
		if strings.Contains(input, strings.ToLower(pattern)) {
			return SecurityValidationResult{
				IsValid: false,
				Reason:  "Potential XSS attack detected",
				Pattern: pattern,
			}
		}
	}

	// Check for path traversal patterns
	for _, pattern := range pathTraversalPatterns {
		if strings.Contains(input, strings.ToLower(pattern)) {
			return SecurityValidationResult{
				IsValid: false,
				Reason:  "Potential path traversal detected",
				Pattern: pattern,
			}
		}
	}

	// Check for command injection patterns
	for _, pattern := range commandPatterns {
		if strings.Contains(input, strings.ToLower(pattern)) {
			return SecurityValidationResult{
				IsValid: false,
				Reason:  "Potential command injection detected",
				Pattern: pattern,
			}
		}
	}

	return SecurityValidationResult{IsValid: true}
}

// ValidateForSevereSecurityThreats performs less aggressive security validation for bio/description fields
func ValidateForSevereSecurityThreats(input string) SecurityValidationResult {
	input = strings.ToLower(input)

	// Only check for clear SQL injection with quotes/operators
	severePatterns := []string{
		"' or '1'='1", "\" or \"1\"=\"1", "' or 1=1", "\" or 1=1",
		"'; drop table", "\"; drop table", "' union select", "\" union select",
		"'; exec", "\"; exec", "'; execute", "\"; execute",
		"'; insert", "\"; insert", "'; delete", "\"; delete",
		"--", "/*", "*/", ";--", ";/*",
		// Clear XSS threats
		"<script", "</script", "<iframe", "</iframe",
		"javascript:", "vbscript:", "onload=", "onerror=", "onclick=",
		"eval(", "alert(", "document.", "window.",
		// Clear command injection with operators
		"| cat", "| ls", "; cat", "; ls", "&& cat", "&& ls",
		"$(cat", "$(ls", "${cat", "${ls",
	}

	for _, pattern := range severePatterns {
		if strings.Contains(input, strings.ToLower(pattern)) {
			return SecurityValidationResult{
				IsValid: false,
				Reason:  "Severe security threat detected",
				Pattern: pattern,
			}
		}
	}

	return SecurityValidationResult{IsValid: true}
}

// ValidateTextLength validates text length constraints
func ValidateTextLength(input string, minLen, maxLen int) bool {
	length := utf8.RuneCountInString(input)
	return length >= minLen && length <= maxLen
}

// ValidateEmail validates email format
func ValidateEmail(email string) bool {
	if len(email) > 254 { // RFC 5321 limit
		return false
	}
	return emailRegex.MatchString(email)
}

// ValidateURL validates URL format (http/https only)
func ValidateURL(url string) bool {
	if len(url) > 2048 { // Common URL length limit
		return false
	}
	return urlRegex.MatchString(url)
}

// ValidateUsername validates username format
func ValidateUsername(username string) bool {
	if len(username) < 3 || len(username) > 50 {
		return false
	}
	return usernameRegex.MatchString(username)
}

// ValidatePhoneNumber validates phone number format
func ValidatePhoneNumber(phone string) bool {
	if phone == "" {
		return true // Phone is optional
	}
	if len(phone) > 20 {
		return false
	}
	return phoneRegex.MatchString(phone)
}

// ValidateGender validates gender enum values
func ValidateGender(gender string) bool {
	if gender == "" {
		return true // Gender is optional
	}
	allowedGenders := []string{"male", "female", "non-binary", "other", "prefer-not-to-say"}
	for _, allowed := range allowedGenders {
		if gender == allowed {
			return true
		}
	}
	return false
}

// ValidateTimezone validates timezone format (basic check)
func ValidateTimezone(timezone string) bool {
	if timezone == "" {
		return true
	}
	// Basic timezone validation - should contain either UTC, GMT, or timezone format
	return strings.Contains(timezone, "/") ||
		strings.HasPrefix(timezone, "UTC") ||
		strings.HasPrefix(timezone, "GMT") ||
		timezone == "UTC"
}

// ValidateLanguage validates language code (ISO 639-1)
func ValidateLanguage(lang string) bool {
	if lang == "" {
		return true
	}
	// Common language codes
	validLangs := []string{
		"en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh",
		"ar", "hi", "tr", "pl", "nl", "sv", "no", "da", "fi", "he",
	}
	for _, valid := range validLangs {
		if lang == valid {
			return true
		}
	}
	return false
}

// SanitizeInput performs basic input sanitization
func SanitizeInput(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")

	// Trim whitespace
	input = strings.TrimSpace(input)

	// Normalize unicode
	input = strings.ToValidUTF8(input, "")

	return input
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return ""
	}
	var messages []string
	for _, err := range ve {
		messages = append(messages, err.Message)
	}
	return "validation failed: " + strings.Join(messages, "; ")
}

// ValidateUserProfileUpdate validates user profile update request
func ValidateUserProfileUpdate(data map[string]interface{}) ValidationErrors {
	var errors ValidationErrors

	// Validate first_name
	if firstName, ok := data["first_name"].(string); ok {
		if firstName == "" {
			errors = append(errors, ValidationError{
				Field:   "first_name",
				Message: "first name cannot be empty",
				Value:   firstName,
			})
		} else {
			if !ValidateTextLength(firstName, 1, 100) {
				errors = append(errors, ValidationError{
					Field:   "first_name",
					Message: "first name must be between 1 and 100 characters",
					Value:   firstName,
				})
			}
			if result := ValidateForSecurityThreats(firstName); !result.IsValid {
				errors = append(errors, ValidationError{
					Field:   "first_name",
					Message: fmt.Sprintf("first name contains invalid content: %s", result.Reason),
					Value:   firstName,
				})
			}
		}
	}

	// Validate last_name
	if lastName, ok := data["last_name"].(string); ok {
		if lastName == "" {
			errors = append(errors, ValidationError{
				Field:   "last_name",
				Message: "last name cannot be empty",
				Value:   lastName,
			})
		} else {
			if !ValidateTextLength(lastName, 1, 100) {
				errors = append(errors, ValidationError{
					Field:   "last_name",
					Message: "last name must be between 1 and 100 characters",
					Value:   lastName,
				})
			}
			if result := ValidateForSecurityThreats(lastName); !result.IsValid {
				errors = append(errors, ValidationError{
					Field:   "last_name",
					Message: fmt.Sprintf("last name contains invalid content: %s", result.Reason),
					Value:   lastName,
				})
			}
		}
	}

	// Validate bio
	if bio, ok := data["bio"].(string); ok && bio != "" {
		if !ValidateTextLength(bio, 0, 500) {
			errors = append(errors, ValidationError{
				Field:   "bio",
				Message: "bio must be less than 500 characters",
				Value:   bio,
			})
		}
		if result := ValidateForSevereSecurityThreats(bio); !result.IsValid {
			errors = append(errors, ValidationError{
				Field:   "bio",
				Message: fmt.Sprintf("bio contains invalid content: %s", result.Reason),
				Value:   bio,
			})
		}
	}

	// Validate website
	if website, ok := data["website"].(string); ok && website != "" {
		if !ValidateURL(website) {
			errors = append(errors, ValidationError{
				Field:   "website",
				Message: "website must be a valid HTTP/HTTPS URL",
				Value:   website,
			})
		}
	}

	// Validate phone_number
	if phone, ok := data["phone_number"].(string); ok && phone != "" {
		if !ValidatePhoneNumber(phone) {
			errors = append(errors, ValidationError{
				Field:   "phone_number",
				Message: "phone number format is invalid",
				Value:   phone,
			})
		}
	}

	// Validate gender
	if gender, ok := data["gender"].(string); ok && gender != "" {
		if !ValidateGender(gender) {
			errors = append(errors, ValidationError{
				Field:   "gender",
				Message: "gender must be one of: male, female, non-binary, other, prefer-not-to-say",
				Value:   gender,
			})
		}
	}

	// Validate timezone
	if timezone, ok := data["timezone"].(string); ok && timezone != "" {
		if !ValidateTimezone(timezone) {
			errors = append(errors, ValidationError{
				Field:   "timezone",
				Message: "timezone format is invalid",
				Value:   timezone,
			})
		}
	}

	// Validate language
	if language, ok := data["language"].(string); ok && language != "" {
		if !ValidateLanguage(language) {
			errors = append(errors, ValidationError{
				Field:   "language",
				Message: "language must be a valid ISO 639-1 language code",
				Value:   language,
			})
		}
	}

	return errors
}
