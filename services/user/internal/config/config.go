package config

import (
	"os"
	"strconv"
	"time"

	"github.com/swork-team/platform/pkg/config"
)

// Config for User service with enhanced database configuration
type Config struct {
	*config.BaseServiceConfig
	Database DatabaseConfig `json:"database"`
	Cache    CacheConfig    `json:"cache"`
}

// DatabaseConfig contains enhanced database connection settings
type DatabaseConfig struct {
	*config.DatabaseConfig
	// Connection pooling settings
	MaxOpenConns    int           `env:"USER_DB_MAX_OPEN_CONNS" envDefault:"25" json:"max_open_conns"`
	MaxIdleConns    int           `env:"USER_DB_MAX_IDLE_CONNS" envDefault:"5" json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `env:"USER_DB_CONN_MAX_LIFETIME" envDefault:"300s" json:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `env:"USER_DB_CONN_MAX_IDLE_TIME" envDefault:"60s" json:"conn_max_idle_time"`
	
	// Query optimization settings
	SlowQueryThreshold time.Duration `env:"USER_DB_SLOW_QUERY_THRESHOLD" envDefault:"500ms" json:"slow_query_threshold"`
	EnableQueryLogging bool          `env:"USER_DB_ENABLE_QUERY_LOGGING" envDefault:"false" json:"enable_query_logging"`
	
	// Read replica settings (for future implementation)
	ReadReplicaURL    string `env:"USER_DB_READ_REPLICA_URL" json:"read_replica_url"`
	UseReadReplica    bool   `env:"USER_DB_USE_READ_REPLICA" envDefault:"false" json:"use_read_replica"`
}

// CacheConfig contains caching-specific settings
type CacheConfig struct {
	*config.RedisConfig
	// Cache TTL settings
	SearchCacheTTL      time.Duration `env:"USER_SEARCH_CACHE_TTL" envDefault:"5m" json:"search_cache_ttl"`
	RelationshipCacheTTL time.Duration `env:"USER_RELATIONSHIP_CACHE_TTL" envDefault:"10m" json:"relationship_cache_ttl"`
	SuggestionsCacheTTL  time.Duration `env:"USER_SUGGESTIONS_CACHE_TTL" envDefault:"30m" json:"suggestions_cache_ttl"`
	ProfileCacheTTL      time.Duration `env:"USER_PROFILE_CACHE_TTL" envDefault:"15m" json:"profile_cache_ttl"`
	
	// Cache behavior settings
	EnableSearchCache      bool `env:"USER_ENABLE_SEARCH_CACHE" envDefault:"true" json:"enable_search_cache"`
	EnableRelationshipCache bool `env:"USER_ENABLE_RELATIONSHIP_CACHE" envDefault:"true" json:"enable_relationship_cache"`
	EnableSuggestionsCache  bool `env:"USER_ENABLE_SUGGESTIONS_CACHE" envDefault:"true" json:"enable_suggestions_cache"`
	
	// Cache warming settings
	WarmCacheOnStartup bool `env:"USER_WARM_CACHE_ON_STARTUP" envDefault:"false" json:"warm_cache_on_startup"`
}

// LoadConfig loads the enhanced configuration for the User service
func LoadConfig() *Config {
	baseConfig := config.LoadBaseConfig("USER")
	
	// Parse database connection pool settings
	maxOpenConns := parseIntEnv("USER_DB_MAX_OPEN_CONNS", 25)
	maxIdleConns := parseIntEnv("USER_DB_MAX_IDLE_CONNS", 5)
	connMaxLifetime := parseDurationEnv("USER_DB_CONN_MAX_LIFETIME", 300*time.Second)
	connMaxIdleTime := parseDurationEnv("USER_DB_CONN_MAX_IDLE_TIME", 60*time.Second)
	slowQueryThreshold := parseDurationEnv("USER_DB_SLOW_QUERY_THRESHOLD", 500*time.Millisecond)
	enableQueryLogging := parseBoolEnv("USER_DB_ENABLE_QUERY_LOGGING", false)
	
	// Parse cache TTL settings
	searchCacheTTL := parseDurationEnv("USER_SEARCH_CACHE_TTL", 5*time.Minute)
	relationshipCacheTTL := parseDurationEnv("USER_RELATIONSHIP_CACHE_TTL", 10*time.Minute)
	suggestionsCacheTTL := parseDurationEnv("USER_SUGGESTIONS_CACHE_TTL", 30*time.Minute)
	profileCacheTTL := parseDurationEnv("USER_PROFILE_CACHE_TTL", 15*time.Minute)
	
	// Parse cache behavior settings
	enableSearchCache := parseBoolEnv("USER_ENABLE_SEARCH_CACHE", true)
	enableRelationshipCache := parseBoolEnv("USER_ENABLE_RELATIONSHIP_CACHE", true)
	enableSuggestionsCache := parseBoolEnv("USER_ENABLE_SUGGESTIONS_CACHE", true)
	warmCacheOnStartup := parseBoolEnv("USER_WARM_CACHE_ON_STARTUP", false)
	
	return &Config{
		BaseServiceConfig: baseConfig,
		Database: DatabaseConfig{
			DatabaseConfig:     &baseConfig.Database,
			MaxOpenConns:      maxOpenConns,
			MaxIdleConns:      maxIdleConns,
			ConnMaxLifetime:   connMaxLifetime,
			ConnMaxIdleTime:   connMaxIdleTime,
			SlowQueryThreshold: slowQueryThreshold,
			EnableQueryLogging: enableQueryLogging,
			ReadReplicaURL:    os.Getenv("USER_DB_READ_REPLICA_URL"),
			UseReadReplica:    parseBoolEnv("USER_DB_USE_READ_REPLICA", false),
		},
		Cache: CacheConfig{
			RedisConfig:            &baseConfig.Redis,
			SearchCacheTTL:        searchCacheTTL,
			RelationshipCacheTTL:  relationshipCacheTTL,
			SuggestionsCacheTTL:   suggestionsCacheTTL,
			ProfileCacheTTL:       profileCacheTTL,
			EnableSearchCache:     enableSearchCache,
			EnableRelationshipCache: enableRelationshipCache,
			EnableSuggestionsCache:  enableSuggestionsCache,
			WarmCacheOnStartup:    warmCacheOnStartup,
		},
	}
}

// Helper functions for parsing environment variables
func parseIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func parseDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if parsed, err := time.ParseDuration(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func parseBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

