package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/utils"
)

// SecurityMiddleware adds comprehensive security logging and monitoring
func SecurityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Log security-relevant requests
		logSecurityRequest(c)

		// Check for suspicious patterns in request
		if isSuspiciousRequest(c) {
			logSecurityThreat(c, "suspicious_request_pattern")
		}

		c.Next()

		// Log response and timing
		duration := time.Since(start)
		logRequestComplete(c, duration)

		// Log security events for sensitive operations
		if isSensitiveOperation(c) {
			logSensitiveOperation(c, duration)
		}
	}
}

// ValidationErrorMiddleware handles validation errors consistently
func ValidationErrorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Check if there were validation errors
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				if err.Type == gin.ErrorTypeBind {
					logValidationError(c, err.Err)
				}
			}
		}
	}
}

// RateLimitExceededMiddleware logs rate limit violations
func RateLimitExceededMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		if c.Writer.Status() == http.StatusTooManyRequests {
			logRateLimitViolation(c)
		}
	}
}

func logSecurityRequest(c *gin.Context) {
	serviceLogger := logger.NewDefaultLogger("user-service")
	serviceLogger.Info("Security request logged",
		logger.F("event_type", "security_request"),
		logger.F("method", c.Request.Method),
		logger.F("path", c.Request.URL.Path),
		logger.F("user_agent", c.Request.UserAgent()),
		logger.F("client_ip", c.ClientIP()),
		logger.F("content_type", c.Request.Header.Get("Content-Type")),
		logger.F("content_length", c.Request.ContentLength),
		logger.F("timestamp", time.Now()),
	)
}

func isSuspiciousRequest(c *gin.Context) bool {
	// Check for suspicious patterns
	suspiciousPatterns := []string{
		"../", "..\\", "<script", "javascript:", "eval(", "exec(",
		"drop table", "select * from", "union select", "insert into",
		"delete from", " update set ", "--", "/*", "*/",
	}

	// Check URL path
	path := strings.ToLower(c.Request.URL.Path)
	query := strings.ToLower(c.Request.URL.RawQuery)
	userAgent := strings.ToLower(c.Request.UserAgent())

	for _, pattern := range suspiciousPatterns {
		if strings.Contains(path, pattern) ||
			strings.Contains(query, pattern) ||
			strings.Contains(userAgent, pattern) {
			return true
		}
	}

	// Check for unusual content length
	if c.Request.ContentLength > 10*1024*1024 { // 10MB
		return true
	}

	// Check for suspicious headers
	if checkSuspiciousHeaders(c) {
		return true
	}

	return false
}

func checkSuspiciousHeaders(c *gin.Context) bool {
	suspiciousHeaders := map[string][]string{
		"X-Forwarded-For": {"127.0.0.1", "localhost", "0.0.0.0"},
		"User-Agent":      {"sqlmap", "nikto", "nmap", "burp", "owasp"},
	}

	for header, patterns := range suspiciousHeaders {
		value := strings.ToLower(c.Request.Header.Get(header))
		for _, pattern := range patterns {
			if strings.Contains(value, pattern) {
				return true
			}
		}
	}

	return false
}

func logSecurityThreat(c *gin.Context, threatType string) {
	serviceLogger := logger.NewDefaultLogger("user-service")
	serviceLogger.Warn("Security threat detected",
		logger.F("event_type", "security_threat"),
		logger.F("threat_type", threatType),
		logger.F("method", c.Request.Method),
		logger.F("path", c.Request.URL.Path),
		logger.F("query", c.Request.URL.RawQuery),
		logger.F("user_agent", c.Request.UserAgent()),
		logger.F("client_ip", c.ClientIP()),
		logger.F("headers", c.Request.Header),
		logger.F("timestamp", time.Now()),
		logger.F("severity", "high"),
	)
}

func isSensitiveOperation(c *gin.Context) bool {
	sensitiveOps := []string{
		"DELETE", // Any delete operation
	}

	sensitivePaths := []string{
		"/users/me",        // Profile updates
		"/friend-requests", // Friend operations
		"/block",           // Block operations
		"/settings",        // Settings changes
	}

	// Check method
	for _, op := range sensitiveOps {
		if c.Request.Method == op {
			return true
		}
	}

	// Check path
	path := c.Request.URL.Path
	for _, sensPath := range sensitivePaths {
		if strings.Contains(path, sensPath) {
			return true
		}
	}

	return false
}

func logRequestComplete(c *gin.Context, duration time.Duration) {
	statusCode := c.Writer.Status()
	serviceLogger := logger.NewDefaultLogger("user-service")

	fields := []logger.Field{
		logger.F("event_type", "request_complete"),
		logger.F("method", c.Request.Method),
		logger.F("path", c.Request.URL.Path),
		logger.F("status_code", statusCode),
		logger.F("duration_ms", duration.Milliseconds()),
		logger.F("client_ip", c.ClientIP()),
		logger.F("user_id", getUserID(c)),
		logger.F("timestamp", time.Now()),
	}

	if statusCode >= 500 {
		serviceLogger.Error("Request completed", fields...)
	} else if statusCode >= 400 {
		serviceLogger.Warn("Request completed", fields...)
	} else {
		serviceLogger.Info("Request completed", fields...)
	}
}

func logSensitiveOperation(c *gin.Context, duration time.Duration) {
	serviceLogger := logger.NewDefaultLogger("user-service")
	serviceLogger.Info("Sensitive operation performed",
		logger.F("event_type", "sensitive_operation"),
		logger.F("method", c.Request.Method),
		logger.F("path", c.Request.URL.Path),
		logger.F("status_code", c.Writer.Status()),
		logger.F("duration_ms", duration.Milliseconds()),
		logger.F("client_ip", c.ClientIP()),
		logger.F("user_id", getUserID(c)),
		logger.F("user_agent", c.Request.UserAgent()),
		logger.F("timestamp", time.Now()),
		logger.F("severity", "medium"),
	)
}

func logValidationError(c *gin.Context, err error) {
	serviceLogger := logger.NewDefaultLogger("user-service")
	serviceLogger.Warn("Validation error occurred",
		logger.F("event_type", "validation_error"),
		logger.F("method", c.Request.Method),
		logger.F("path", c.Request.URL.Path),
		logger.F("error", err.Error()),
		logger.F("client_ip", c.ClientIP()),
		logger.F("user_id", getUserID(c)),
		logger.F("timestamp", time.Now()),
	)
}

func logRateLimitViolation(c *gin.Context) {
	serviceLogger := logger.NewDefaultLogger("user-service")
	serviceLogger.Warn("Rate limit exceeded",
		logger.F("event_type", "rate_limit_violation"),
		logger.F("method", c.Request.Method),
		logger.F("path", c.Request.URL.Path),
		logger.F("client_ip", c.ClientIP()),
		logger.F("user_id", getUserID(c)),
		logger.F("user_agent", c.Request.UserAgent()),
		logger.F("timestamp", time.Now()),
		logger.F("severity", "medium"),
	)
}

func getUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		return userID.(string)
	}
	return ""
}

// PanicRecoveryMiddleware provides enhanced panic recovery with security logging
func PanicRecoveryMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		serviceLogger := logger.NewDefaultLogger("user-service")
		serviceLogger.Error("Panic recovered",
			logger.F("event_type", "panic_recovery"),
			logger.F("method", c.Request.Method),
			logger.F("path", c.Request.URL.Path),
			logger.F("panic_value", recovered),
			logger.F("client_ip", c.ClientIP()),
			logger.F("user_id", getUserID(c)),
			logger.F("user_agent", c.Request.UserAgent()),
			logger.F("timestamp", time.Now()),
			logger.F("severity", "critical"),
		)

		utils.ErrorResponse(c, http.StatusInternalServerError, "Internal server error", nil)
	})
}
