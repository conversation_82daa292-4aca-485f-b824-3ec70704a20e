package main

import (
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	"github.com/swork-team/platform/services/user/internal/config"
	"github.com/swork-team/platform/services/user/internal/handlers"
	userMiddleware "github.com/swork-team/platform/services/user/internal/middleware"
	"github.com/swork-team/platform/services/user/internal/models"
	"github.com/swork-team/platform/services/user/internal/repositories"
	"github.com/swork-team/platform/services/user/internal/services"
)

func main() {
	cfg := config.LoadConfig()

	// Create config adapter for bootstrap
	configAdapter := server.NewConfigAdapter(cfg.Server, *cfg.Database.DatabaseConfig, *cfg.Cache.RedisConfig)

	// Use the new ServiceBootstrapper
	bootstrapper := server.NewServiceBootstrapper(server.ServiceBootstrapConfig{
		ServiceName:      "user",
		ConfigAdapter:    configAdapter,
		ModelsProvider:   getUserModels,
		RouterSetup:      createUserRouterSetup(),
		EnableExtensions: true,
		DatabaseType:     server.PostgreSQL,
	})

	bootstrapper.Bootstrap()
}

// getUserModels returns models for migration
func getUserModels() []interface{} {
	return []interface{}{
		&models.User{},
		&models.FriendRequest{},
		&models.Follow{},
		&models.Block{},
		&models.UserSettings{},
	}
}

// createUserRouterSetup creates the router setup function for user service
func createUserRouterSetup() server.RouterSetupFunction {
	return func(components *server.ServiceComponents) *gin.Engine {
		cfg := config.LoadConfig()

		// Configure database connection pool
		if db := components.Database; db != nil {
			sqlDB, err := db.DB()
			if err == nil {
				sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
				sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
				sqlDB.SetConnMaxLifetime(cfg.Database.ConnMaxLifetime)
				sqlDB.SetConnMaxIdleTime(cfg.Database.ConnMaxIdleTime)
			}
		}

		// Use cached repository implementation
		userRepo := repositories.NewCachedUserRepository(components.Database, components.CacheManager)
		userService := services.NewUserService(userRepo, cfg, components.CacheManager, components.AsyncJobQueue, components.WebhookService)
		userHandler := handlers.NewUserHandler(userService)

		// Get logger
		serviceLogger := logger.NewServiceLoggerManager("user").GetLogger()

		return setupRouter(userHandler, components.RedisClient, cfg, serviceLogger)
	}
}

func setupRouter(userHandler *handlers.UserHandler, redisClient *redis.Client, cfg *config.Config, serviceLogger logger.ServiceLogger) *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Enhanced security and logging middleware stack
	router.Use(userMiddleware.PanicRecoveryMiddleware())
	router.Use(userMiddleware.SecurityMiddleware())
	router.Use(userMiddleware.ValidationErrorMiddleware())
	router.Use(userMiddleware.RateLimitExceededMiddleware())
	router.Use(logger.RequestLoggingMiddleware(serviceLogger))
	router.Use(logger.UserContextMiddleware())
	router.Use(logger.ErrorLoggingMiddleware())
	router.Use(middleware.CORSMiddleware())

	// Create rate limiter
	rateLimiter := middleware.NewRateLimiter(redisClient)

	// Health check (no rate limiting, no authentication)
	router.GET("/health", userHandler.HealthCheck)

	// Public user routes (no authentication required) - must come before authenticated routes
	router.GET("/users/username/:username", userHandler.GetProfileByUsername)

	// User routes with authentication and tiered rate limiting
	users := router.Group("/users")
	users.Use(middleware.AuthMiddleware())
	
	// Apply request size limiting (1MB max for user service)
	users.Use(func(c *gin.Context) {
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 1<<20) // 1MB
		c.Next()
	})
	
	// Profile management endpoints (higher limits for read operations)
	profileGroup := users.Group("")
	profileGroup.Use(rateLimiter.LimitByUser(200, time.Minute)) // 200 req/min for profile reads
	{
		profileGroup.GET("/me", userHandler.GetProfile)
		profileGroup.GET("/:id", userHandler.GetProfile)
		profileGroup.GET("/batch", userHandler.GetUsersBatch)
		profileGroup.GET("/:id/relationship", userHandler.GetRelationshipStatus)
	}
	
	// Profile modification endpoints (lower limits for write operations)
	profileWriteGroup := users.Group("")
	profileWriteGroup.Use(rateLimiter.LimitByUser(30, time.Minute)) // 30 req/min for profile writes
	{
		profileWriteGroup.PUT("/me", userHandler.UpdateProfile)
		profileWriteGroup.DELETE("/me", userHandler.DeleteProfile)
	}

	// Search endpoints (moderate limits)
	searchGroup := users.Group("")
	searchGroup.Use(rateLimiter.LimitByUser(60, time.Minute)) // 60 req/min for search
	{
		searchGroup.GET("/search", userHandler.SearchUsers)
		searchGroup.GET("/suggestions", userHandler.GetUserSuggestions)
	}

	// Social interaction endpoints (moderate limits)
	socialGroup := users.Group("")
	socialGroup.Use(rateLimiter.LimitByUser(100, time.Minute)) // 100 req/min for social actions
	{
		// Friend requests
		socialGroup.POST("/friend-requests", userHandler.SendFriendRequest)
		socialGroup.GET("/friend-requests", userHandler.GetFriendRequests)
		socialGroup.GET("/friend-requests/sent", userHandler.GetSentFriendRequests)
		socialGroup.PUT("/friend-requests/:id/respond", userHandler.RespondToFriendRequest)
		socialGroup.DELETE("/friend-requests/:id/cancel", userHandler.CancelFriendRequest)
		socialGroup.GET("/friends", userHandler.GetFriends)
		socialGroup.DELETE("/:id/unfriend", userHandler.UnfriendUser)

		// Follow system
		socialGroup.POST("/:id/follow", userHandler.FollowUser)
		socialGroup.DELETE("/:id/follow", userHandler.UnfollowUser)
		socialGroup.GET("/followers", userHandler.GetFollowers)
		socialGroup.GET("/following", userHandler.GetFollowing)

		// Block system (stricter limits for sensitive operations)
		socialGroup.POST("/:id/block", userHandler.BlockUser)
		socialGroup.DELETE("/:id/block", userHandler.UnblockUser)
		socialGroup.GET("/blocked", userHandler.GetBlockedUsers)
	}

	// Settings endpoints (moderate limits)
	settingsGroup := users.Group("")
	settingsGroup.Use(rateLimiter.LimitByUser(50, time.Minute)) // 50 req/min for settings
	{
		settingsGroup.GET("/settings", userHandler.GetUserSettings)
		settingsGroup.PUT("/settings", userHandler.UpdateUserSettings)
	}

	// Internal service-to-service routes (no auth middleware)
	internal := router.Group("/internal")
	{
		internal.POST("/users", userHandler.CreateUserProfileInternal)
		internal.GET("/users/:id", userHandler.GetUserProfileInternal)
		internal.POST("/users/batch", userHandler.GetUsersBatchInternal)
		internal.POST("/users/validate/username", userHandler.ValidateUsernameInternal)
		internal.POST("/users/validate/email", userHandler.ValidateEmailInternal)
	}

	return router
}
