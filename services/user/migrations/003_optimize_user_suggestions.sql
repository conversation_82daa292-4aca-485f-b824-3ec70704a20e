-- Optimized user suggestions using materialized view
-- This migration creates a materialized view that pre-computes suggestion scores
-- to replace the complex CTE query in GetSuggestions

-- Create materialized view for user suggestion scores
CREATE MATERIALIZED VIEW IF NOT EXISTS user_suggestion_scores AS
WITH mutual_friend_counts AS (
    -- Calculate mutual friend counts efficiently
    SELECT 
        u1.id as user_id,
        u2.id as suggested_user_id,
        COUNT(DISTINCT 
            CASE 
                WHEN fr1.requester_id = fr2.requester_id OR 
                     fr1.requester_id = fr2.receiver_id OR
                     fr1.receiver_id = fr2.requester_id OR 
                     fr1.receiver_id = fr2.receiver_id 
                THEN COALESCE(fr2.requester_id, fr2.receiver_id)
            END
        ) as mutual_friends_count
    FROM users u1
    CROSS JOIN users u2
    LEFT JOIN friend_requests fr1 ON 
        (fr1.requester_id = u1.id OR fr1.receiver_id = u1.id) 
        AND fr1.status = 'accepted'
    LEFT JOIN friend_requests fr2 ON 
        (fr2.requester_id != u1.id AND fr2.receiver_id != u1.id)
        AND fr2.status = 'accepted'
        AND (
            fr2.requester_id IN (
                SELECT CASE WHEN fr1.requester_id = u1.id THEN fr1.receiver_id ELSE fr1.requester_id END
                FROM friend_requests fr1 
                WHERE (fr1.requester_id = u1.id OR fr1.receiver_id = u1.id) AND fr1.status = 'accepted'
            )
            OR fr2.receiver_id IN (
                SELECT CASE WHEN fr1.requester_id = u1.id THEN fr1.receiver_id ELSE fr1.requester_id END
                FROM friend_requests fr1 
                WHERE (fr1.requester_id = u1.id OR fr1.receiver_id = u1.id) AND fr1.status = 'accepted'
            )
        )
    WHERE u1.id != u2.id 
        AND u1.is_active = true 
        AND u2.is_active = true
    GROUP BY u1.id, u2.id
),
user_connections AS (
    -- Pre-compute existing connections to exclude from suggestions
    SELECT user_id, connected_user_id
    FROM (
        -- Friends (accepted friend requests)
        SELECT requester_id as user_id, receiver_id as connected_user_id 
        FROM friend_requests WHERE status = 'accepted'
        UNION ALL
        SELECT receiver_id as user_id, requester_id as connected_user_id 
        FROM friend_requests WHERE status = 'accepted'
        UNION ALL
        -- Following relationships
        SELECT follower_id as user_id, followed_id as connected_user_id 
        FROM follows
        UNION ALL
        -- Blocked users
        SELECT blocker_id as user_id, blocked_id as connected_user_id 
        FROM blocks
        UNION ALL
        SELECT blocked_id as user_id, blocker_id as connected_user_id 
        FROM blocks
        UNION ALL
        -- Pending friend requests
        SELECT requester_id as user_id, receiver_id as connected_user_id 
        FROM friend_requests WHERE status = 'pending'
        UNION ALL
        SELECT receiver_id as user_id, requester_id as connected_user_id 
        FROM friend_requests WHERE status = 'pending'
    ) connections
)
SELECT 
    u1.id as user_id,
    u2.id as suggested_user_id,
    u2.username,
    u2.first_name,
    u2.last_name,
    u2.profile_image,
    u2.is_verified,
    -- Calculate suggestion score
    CASE 
        WHEN COALESCE(mfc.mutual_friends_count, 0) > 0 THEN 
            100 + (COALESCE(mfc.mutual_friends_count, 0) * 10)  -- High score for mutual friends
        WHEN u2.last_seen IS NOT NULL AND u2.last_seen > NOW() - INTERVAL '7 days' THEN 
            60  -- Medium score for recently active users
        WHEN u2.last_seen IS NOT NULL AND u2.last_seen > NOW() - INTERVAL '30 days' THEN 
            40  -- Lower score for moderately active users
        WHEN u2.is_verified = true THEN 
            30  -- Bonus for verified users
        ELSE 
            10  -- Base score for other active users
    END as suggestion_score,
    COALESCE(mfc.mutual_friends_count, 0) as mutual_friends_count,
    u2.last_seen,
    u2.created_at,
    NOW() as computed_at
FROM users u1
CROSS JOIN users u2
LEFT JOIN mutual_friend_counts mfc ON mfc.user_id = u1.id AND mfc.suggested_user_id = u2.id
LEFT JOIN user_connections uc ON uc.user_id = u1.id AND uc.connected_user_id = u2.id
WHERE u1.id != u2.id 
    AND u1.is_active = true 
    AND u2.is_active = true
    AND uc.connected_user_id IS NULL  -- Exclude existing connections
    -- Only include users with reasonable suggestion scores
    AND (
        COALESCE(mfc.mutual_friends_count, 0) > 0 OR
        (u2.last_seen IS NOT NULL AND u2.last_seen > NOW() - INTERVAL '90 days') OR
        u2.is_verified = true
    );

-- Create indexes on the materialized view for fast lookups
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_suggestion_scores_unique 
ON user_suggestion_scores (user_id, suggested_user_id);

CREATE INDEX IF NOT EXISTS idx_user_suggestion_scores_user_score 
ON user_suggestion_scores (user_id, suggestion_score DESC, computed_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_suggestion_scores_computed_at 
ON user_suggestion_scores (computed_at);

-- Create function to refresh suggestions for a specific user (for real-time updates)
CREATE OR REPLACE FUNCTION refresh_user_suggestions(target_user_id uuid) RETURNS void AS $$
BEGIN
    -- Delete existing suggestions for this user
    DELETE FROM user_suggestion_scores WHERE user_id = target_user_id;
    
    -- Insert fresh suggestions for this user
    INSERT INTO user_suggestion_scores
    SELECT * FROM (
        WITH mutual_friend_counts AS (
            SELECT 
                target_user_id as user_id,
                u2.id as suggested_user_id,
                COUNT(DISTINCT 
                    CASE 
                        WHEN fr1.requester_id = fr2.requester_id OR 
                             fr1.requester_id = fr2.receiver_id OR
                             fr1.receiver_id = fr2.requester_id OR 
                             fr1.receiver_id = fr2.receiver_id 
                        THEN COALESCE(fr2.requester_id, fr2.receiver_id)
                    END
                ) as mutual_friends_count
            FROM users u2
            LEFT JOIN friend_requests fr1 ON 
                (fr1.requester_id = target_user_id OR fr1.receiver_id = target_user_id) 
                AND fr1.status = 'accepted'
            LEFT JOIN friend_requests fr2 ON 
                (fr2.requester_id != target_user_id AND fr2.receiver_id != target_user_id)
                AND fr2.status = 'accepted'
            WHERE u2.id != target_user_id 
                AND u2.is_active = true
            GROUP BY u2.id
        ),
        user_connections AS (
            SELECT connected_user_id
            FROM (
                SELECT receiver_id as connected_user_id FROM friend_requests 
                WHERE requester_id = target_user_id AND status = 'accepted'
                UNION ALL
                SELECT requester_id as connected_user_id FROM friend_requests 
                WHERE receiver_id = target_user_id AND status = 'accepted'
                UNION ALL
                SELECT followed_id as connected_user_id FROM follows 
                WHERE follower_id = target_user_id
                UNION ALL
                SELECT blocked_id as connected_user_id FROM blocks 
                WHERE blocker_id = target_user_id
                UNION ALL
                SELECT blocker_id as connected_user_id FROM blocks 
                WHERE blocked_id = target_user_id
                UNION ALL
                SELECT receiver_id as connected_user_id FROM friend_requests 
                WHERE requester_id = target_user_id AND status = 'pending'
                UNION ALL
                SELECT requester_id as connected_user_id FROM friend_requests 
                WHERE receiver_id = target_user_id AND status = 'pending'
            ) connections
        )
        SELECT 
            target_user_id as user_id,
            u2.id as suggested_user_id,
            u2.username,
            u2.first_name,
            u2.last_name,
            u2.profile_image,
            u2.is_verified,
            CASE 
                WHEN COALESCE(mfc.mutual_friends_count, 0) > 0 THEN 
                    100 + (COALESCE(mfc.mutual_friends_count, 0) * 10)
                WHEN u2.last_seen IS NOT NULL AND u2.last_seen > NOW() - INTERVAL '7 days' THEN 
                    60
                WHEN u2.last_seen IS NOT NULL AND u2.last_seen > NOW() - INTERVAL '30 days' THEN 
                    40
                WHEN u2.is_verified = true THEN 
                    30
                ELSE 
                    10
            END as suggestion_score,
            COALESCE(mfc.mutual_friends_count, 0) as mutual_friends_count,
            u2.last_seen,
            u2.created_at,
            NOW() as computed_at
        FROM users u2
        LEFT JOIN mutual_friend_counts mfc ON mfc.suggested_user_id = u2.id
        LEFT JOIN user_connections uc ON uc.connected_user_id = u2.id
        WHERE u2.id != target_user_id 
            AND u2.is_active = true
            AND uc.connected_user_id IS NULL
            AND (
                COALESCE(mfc.mutual_friends_count, 0) > 0 OR
                (u2.last_seen IS NOT NULL AND u2.last_seen > NOW() - INTERVAL '90 days') OR
                u2.is_verified = true
            )
    ) fresh_suggestions;
END;
$$ LANGUAGE plpgsql;

-- Create function to automatically refresh materialized view
CREATE OR REPLACE FUNCTION refresh_user_suggestions_materialized_view() RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_suggestion_scores;
    
    -- Log the refresh
    INSERT INTO user_audit_log (user_id, event_type, event_data)
    VALUES (NULL, 'suggestions_refreshed', jsonb_build_object(
        'timestamp', NOW(),
        'row_count', (SELECT COUNT(*) FROM user_suggestion_scores)
    ));
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically refresh user suggestions when relationships change

-- Trigger for friend request changes
CREATE OR REPLACE FUNCTION trigger_refresh_suggestions_on_friend_request() RETURNS trigger AS $$
BEGIN
    -- Refresh suggestions for both users involved
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        PERFORM refresh_user_suggestions(NEW.requester_id);
        PERFORM refresh_user_suggestions(NEW.receiver_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM refresh_user_suggestions(OLD.requester_id);
        PERFORM refresh_user_suggestions(OLD.receiver_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger for follow changes
CREATE OR REPLACE FUNCTION trigger_refresh_suggestions_on_follow() RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM refresh_user_suggestions(NEW.follower_id);
        PERFORM refresh_user_suggestions(NEW.followed_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM refresh_user_suggestions(OLD.follower_id);
        PERFORM refresh_user_suggestions(OLD.followed_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger for block changes
CREATE OR REPLACE FUNCTION trigger_refresh_suggestions_on_block() RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM refresh_user_suggestions(NEW.blocker_id);
        PERFORM refresh_user_suggestions(NEW.blocked_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM refresh_user_suggestions(OLD.blocker_id);
        PERFORM refresh_user_suggestions(OLD.blocked_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the triggers (only if they don't exist)
DO $$
BEGIN
    -- Friend request trigger
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_friend_request_suggestions') THEN
        CREATE TRIGGER trigger_friend_request_suggestions
            AFTER INSERT OR UPDATE OR DELETE ON friend_requests
            FOR EACH ROW EXECUTE FUNCTION trigger_refresh_suggestions_on_friend_request();
    END IF;
    
    -- Follow trigger
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_follow_suggestions') THEN
        CREATE TRIGGER trigger_follow_suggestions
            AFTER INSERT OR DELETE ON follows
            FOR EACH ROW EXECUTE FUNCTION trigger_refresh_suggestions_on_follow();
    END IF;
    
    -- Block trigger
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_block_suggestions') THEN
        CREATE TRIGGER trigger_block_suggestions
            AFTER INSERT OR DELETE ON blocks
            FOR EACH ROW EXECUTE FUNCTION trigger_refresh_suggestions_on_block();
    END IF;
END $$;

-- Initial population of the materialized view
REFRESH MATERIALIZED VIEW user_suggestion_scores;

-- Schedule automatic refresh every hour (this would typically be done via cron or a job scheduler)
-- For now, we'll create a function that can be called periodically
CREATE OR REPLACE FUNCTION schedule_suggestions_refresh() RETURNS void AS $$
BEGIN
    -- This function should be called by a scheduler (like pg_cron) every hour
    -- Example: SELECT cron.schedule('refresh-suggestions', '0 * * * *', 'SELECT refresh_user_suggestions_materialized_view();');
    PERFORM refresh_user_suggestions_materialized_view();
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON MATERIALIZED VIEW user_suggestion_scores IS 'Pre-computed user suggestion scores updated via triggers and periodic refresh';
COMMENT ON FUNCTION refresh_user_suggestions(uuid) IS 'Refreshes suggestions for a specific user in real-time';
COMMENT ON FUNCTION refresh_user_suggestions_materialized_view() IS 'Refreshes the entire materialized view - should be called periodically';
COMMENT ON FUNCTION schedule_suggestions_refresh() IS 'Function to be called by scheduler for periodic refresh';

-- Grant necessary permissions
GRANT SELECT ON user_suggestion_scores TO swork;
GRANT EXECUTE ON FUNCTION refresh_user_suggestions(uuid) TO swork;
GRANT EXECUTE ON FUNCTION refresh_user_suggestions_materialized_view() TO swork;