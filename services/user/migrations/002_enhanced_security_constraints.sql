-- Enhanced security constraints and optimizations for user service
-- This migration adds additional constraints, indexes, and security measures

-- Add additional constraints to users table
ALTER TABLE users ADD CONSTRAINT users_email_format_check 
    CHECK (email ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

ALTER TABLE users ADD CONSTRAINT users_username_format_check 
    CHECK (username ~ '^[a-zA-Z0-9_-]+$' AND length(username) >= 3);

ALTER TABLE users ADD CONSTRAINT users_first_name_length_check 
    CHECK (length(first_name) >= 1 AND length(first_name) <= 100);

ALTER TABLE users ADD CONSTRAINT users_last_name_length_check 
    CHECK (length(last_name) >= 1 AND length(last_name) <= 100);

ALTER TABLE users ADD CONSTRAINT users_bio_length_check 
    CHECK (length(bio) <= 500);

ALTER TABLE users ADD CONSTRAINT users_location_length_check 
    CHECK (length(location) <= 100);

ALTER TABLE users ADD CONSTRAINT users_website_format_check 
    CHECK (website IS NULL OR website ~ '^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$');

ALTER TABLE users ADD CONSTRAINT users_phone_format_check 
    CHECK (phone_number IS NULL OR phone_number ~ '^\+?[1-9]\d{1,14}$');

ALTER TABLE users ADD CONSTRAINT users_gender_check 
    CHECK (gender IS NULL OR gender IN ('male', 'female', 'non-binary', 'other', 'prefer-not-to-say'));

-- Add security constraints to prevent common injection patterns
ALTER TABLE users ADD CONSTRAINT users_no_sql_injection_first_name
    CHECK (first_name !~* '.*(drop|select|insert|delete|update|alter|create|union|exec|execute|truncate|grant|revoke|\-\-|/\*|\*/|@@).*');

ALTER TABLE users ADD CONSTRAINT users_no_sql_injection_last_name
    CHECK (last_name !~* '.*(drop|select|insert|delete|update|alter|create|union|exec|execute|truncate|grant|revoke|\-\-|/\*|\*/|@@).*');

ALTER TABLE users ADD CONSTRAINT users_no_xss_bio
    CHECK (bio !~* '.*(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|eval\(|setTimeout\(|alert\().*');

ALTER TABLE users ADD CONSTRAINT users_no_xss_location
    CHECK (location !~* '.*(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|eval\(|setTimeout\(|alert\().*');

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_users_email_active ON users(email) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_users_username_active ON users(username) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_last_seen ON users(last_seen) WHERE last_seen IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_search_text ON users USING gin(to_tsvector('english', first_name || ' ' || last_name || ' ' || username));

-- Friend requests table constraints and indexes
ALTER TABLE friend_requests ADD CONSTRAINT friend_requests_no_self_request 
    CHECK (requester_id != receiver_id);

ALTER TABLE friend_requests ADD CONSTRAINT friend_requests_status_check 
    CHECK (status IN ('pending', 'accepted', 'rejected'));

ALTER TABLE friend_requests ADD CONSTRAINT friend_requests_message_length_check 
    CHECK (length(message) <= 500);

ALTER TABLE friend_requests ADD CONSTRAINT friend_requests_no_xss_message
    CHECK (message !~* '.*(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|eval\(|setTimeout\(|alert\().*');

-- Prevent duplicate friend requests
CREATE UNIQUE INDEX IF NOT EXISTS idx_friend_requests_unique_pending 
    ON friend_requests(requester_id, receiver_id) 
    WHERE status = 'pending';

-- Friend requests indexes for performance
CREATE INDEX IF NOT EXISTS idx_friend_requests_receiver_status ON friend_requests(receiver_id, status);
CREATE INDEX IF NOT EXISTS idx_friend_requests_requester_status ON friend_requests(requester_id, status);
CREATE INDEX IF NOT EXISTS idx_friend_requests_created_at ON friend_requests(created_at);

-- Follows table constraints and indexes
ALTER TABLE follows ADD CONSTRAINT follows_no_self_follow 
    CHECK (follower_id != followed_id);

-- Prevent duplicate follows
CREATE UNIQUE INDEX IF NOT EXISTS idx_follows_unique 
    ON follows(follower_id, followed_id);

-- Follows indexes for performance
CREATE INDEX IF NOT EXISTS idx_follows_followed_id ON follows(followed_id);
CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_follows_created_at ON follows(created_at);

-- Blocks table constraints and indexes
ALTER TABLE blocks ADD CONSTRAINT blocks_no_self_block 
    CHECK (blocker_id != blocked_id);

ALTER TABLE blocks ADD CONSTRAINT blocks_reason_length_check 
    CHECK (length(reason) <= 500);

ALTER TABLE blocks ADD CONSTRAINT blocks_no_xss_reason
    CHECK (reason !~* '.*(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|eval\(|setTimeout\(|alert\().*');

-- Prevent duplicate blocks
CREATE UNIQUE INDEX IF NOT EXISTS idx_blocks_unique 
    ON blocks(blocker_id, blocked_id);

-- Blocks indexes for performance
CREATE INDEX IF NOT EXISTS idx_blocks_blocked_id ON blocks(blocked_id);
CREATE INDEX IF NOT EXISTS idx_blocks_blocker_id ON blocks(blocker_id);
CREATE INDEX IF NOT EXISTS idx_blocks_created_at ON blocks(created_at);

-- User settings constraints
ALTER TABLE user_settings ADD CONSTRAINT user_settings_visibility_check 
    CHECK (profile_visibility IN ('public', 'friends', 'private'));

-- User settings indexes
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

-- Add audit logging table for security events
CREATE TABLE IF NOT EXISTS user_audit_log (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE SET NULL,
    event_type varchar(100) NOT NULL,
    event_data jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT NOW()
);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_user_audit_log_user_id ON user_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_audit_log_event_type ON user_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_user_audit_log_created_at ON user_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_user_audit_log_ip_address ON user_audit_log(ip_address);

-- Add table for tracking failed login attempts and suspicious activity
CREATE TABLE IF NOT EXISTS security_events (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type varchar(100) NOT NULL,
    severity varchar(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    user_id uuid REFERENCES users(id) ON DELETE SET NULL,
    ip_address inet,
    user_agent text,
    request_path text,
    event_data jsonb,
    created_at timestamp with time zone DEFAULT NOW()
);

-- Security events indexes
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON security_events(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);

-- Add automatic cleanup for old audit logs (keep for 1 year)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs() RETURNS void AS $$
BEGIN
    DELETE FROM user_audit_log WHERE created_at < NOW() - INTERVAL '1 year';
    DELETE FROM security_events WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

-- Create a function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_event_type varchar(100),
    p_severity varchar(20),
    p_user_id uuid DEFAULT NULL,
    p_ip_address inet DEFAULT NULL,
    p_user_agent text DEFAULT NULL,
    p_request_path text DEFAULT NULL,
    p_event_data jsonb DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
    event_id uuid;
BEGIN
    INSERT INTO security_events (event_type, severity, user_id, ip_address, user_agent, request_path, event_data)
    VALUES (p_event_type, p_severity, p_user_id, p_ip_address, p_user_agent, p_request_path, p_event_data)
    RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic audit logging on sensitive operations
CREATE OR REPLACE FUNCTION audit_user_changes() RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO user_audit_log (user_id, event_type, event_data)
        VALUES (NEW.id, 'user_profile_updated', 
                jsonb_build_object(
                    'old_values', to_jsonb(OLD),
                    'new_values', to_jsonb(NEW),
                    'changed_fields', (
                        SELECT jsonb_object_agg(key, value)
                        FROM jsonb_each(to_jsonb(NEW))
                        WHERE to_jsonb(OLD)->>key IS DISTINCT FROM value::text
                    )
                ));
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO user_audit_log (user_id, event_type, event_data)
        VALUES (OLD.id, 'user_deleted', to_jsonb(OLD));
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the audit trigger
DROP TRIGGER IF EXISTS trigger_audit_user_changes ON users;
CREATE TRIGGER trigger_audit_user_changes
    AFTER UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_user_changes();

-- Create similar triggers for other sensitive tables
CREATE OR REPLACE FUNCTION audit_friend_request_changes() RETURNS trigger AS $$
BEGIN
    INSERT INTO user_audit_log (user_id, event_type, event_data)
    VALUES (
        CASE 
            WHEN TG_OP = 'INSERT' THEN NEW.requester_id
            WHEN TG_OP = 'UPDATE' THEN NEW.requester_id
            ELSE OLD.requester_id
        END,
        'friend_request_' || lower(TG_OP),
        CASE 
            WHEN TG_OP = 'INSERT' THEN to_jsonb(NEW)
            WHEN TG_OP = 'UPDATE' THEN jsonb_build_object('old', to_jsonb(OLD), 'new', to_jsonb(NEW))
            ELSE to_jsonb(OLD)
        END
    );
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_audit_friend_request_changes ON friend_requests;
CREATE TRIGGER trigger_audit_friend_request_changes
    AFTER INSERT OR UPDATE OR DELETE ON friend_requests
    FOR EACH ROW EXECUTE FUNCTION audit_friend_request_changes();

-- Add data retention policies
COMMENT ON TABLE user_audit_log IS 'Audit log for user-related security events. Data retained for 1 year.';
COMMENT ON TABLE security_events IS 'Security events log for monitoring and alerting. Data retained for 1 year.';

-- Add performance monitoring view
CREATE VIEW user_activity_summary AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at,
    u.last_seen,
    u.is_active,
    COUNT(DISTINCT fr_sent.id) as friend_requests_sent,
    COUNT(DISTINCT fr_received.id) as friend_requests_received,
    COUNT(DISTINCT f_following.id) as following_count,
    COUNT(DISTINCT f_followers.id) as followers_count,
    COUNT(DISTINCT b_blocked.id) as blocked_users_count
FROM users u
LEFT JOIN friend_requests fr_sent ON u.id = fr_sent.requester_id
LEFT JOIN friend_requests fr_received ON u.id = fr_received.receiver_id
LEFT JOIN follows f_following ON u.id = f_following.follower_id
LEFT JOIN follows f_followers ON u.id = f_followers.followed_id
LEFT JOIN blocks b_blocked ON u.id = b_blocked.blocker_id
WHERE u.is_active = true
GROUP BY u.id, u.username, u.email, u.created_at, u.last_seen, u.is_active;

COMMENT ON VIEW user_activity_summary IS 'Summary view of user activity for performance monitoring';

-- Grant necessary permissions
GRANT SELECT ON user_activity_summary TO swork;