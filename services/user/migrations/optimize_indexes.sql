-- Database optimization indexes for user service

-- Optimize user table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_last_seen 
ON users (is_active, last_seen DESC) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active 
ON users (email, is_active) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username_active 
ON users (username, is_active) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_search 
ON users USING gin(to_tsvector('english', first_name || ' ' || last_name || ' ' || username)) 
WHERE is_active = true;

-- Optimize friend_requests table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_friend_requests_requester_status 
ON friend_requests (requester_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_friend_requests_receiver_status 
ON friend_requests (receiver_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_friend_requests_pair 
ON friend_requests (requester_id, receiver_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_friend_requests_status_created 
ON friend_requests (status, created_at DESC);

-- Optimize follows table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_follower 
ON follows (follower_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_followed 
ON follows (followed_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_pair 
ON follows (follower_id, followed_id);

-- Optimize blocks table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blocks_blocker 
ON blocks (blocker_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blocks_blocked 
ON blocks (blocked_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blocks_pair 
ON blocks (blocker_id, blocked_id);

-- Optimize user_settings table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_settings_user 
ON user_settings (user_id);

-- Add composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_friend_requests_mutual_friends 
ON friend_requests (status, requester_id, receiver_id) 
WHERE status = 'accepted';

-- Statistics update
ANALYZE users;
ANALYZE friend_requests;
ANALYZE follows;
ANALYZE blocks;
ANALYZE user_settings;