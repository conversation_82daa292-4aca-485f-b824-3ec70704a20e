# User Service Configuration

# Database Connection Pool Settings
USER_DB_MAX_OPEN_CONNS=25              # Maximum number of open connections
USER_DB_MAX_IDLE_CONNS=5               # Maximum number of idle connections
USER_DB_CONN_MAX_LIFETIME=300s         # Maximum connection lifetime
USER_DB_CONN_MAX_IDLE_TIME=60s         # Maximum idle time for connections

# Database Query Settings
USER_DB_SLOW_QUERY_THRESHOLD=500ms     # Threshold for logging slow queries
USER_DB_ENABLE_QUERY_LOGGING=false     # Enable detailed query logging

# Read Replica Settings (for future implementation)
USER_DB_READ_REPLICA_URL=              # Read replica database URL
USER_DB_USE_READ_REPLICA=false         # Enable read replica usage

# Cache TTL Settings
USER_SEARCH_CACHE_TTL=5m               # Search results cache duration
USER_RELATIONSHIP_CACHE_TTL=10m        # Relationship status cache duration
USER_SUGGESTIONS_CACHE_TTL=30m         # User suggestions cache duration
USER_PROFILE_CACHE_TTL=15m             # User profile cache duration

# Cache Behavior Settings
USER_ENABLE_SEARCH_CACHE=true          # Enable search result caching
USER_ENABLE_RELATIONSHIP_CACHE=true    # Enable relationship status caching
USER_ENABLE_SUGGESTIONS_CACHE=true     # Enable user suggestions caching

# Cache Warming Settings
USER_WARM_CACHE_ON_STARTUP=false       # Pre-warm cache on service startup

# Performance Tuning Notes:
# - Increase MAX_OPEN_CONNS for high-traffic scenarios (50-100)
# - Decrease cache TTLs for real-time applications
# - Enable query logging only in development/debugging
# - Use read replicas for read-heavy workloads