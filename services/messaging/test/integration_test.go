package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/swork-team/platform/services/messaging/internal/infrastructure/config"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/container"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/http/routes"
)

const testJWTSecret = "test-secret-key-for-integration-testing-at-least-32-characters"

func TestMessageFlow(t *testing.T) {
	// Setup test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set test environment variables
	os.Setenv("TEST_MODE", "true")
	os.Setenv("JWT_SECRET", testJWTSecret)
	defer func() {
		os.Unsetenv("TEST_MODE")
		os.Unsetenv("JWT_SECRET")
	}()
	
	// Create test config
	cfg := config.LoadConfig()

	// Create container (you may need to mock dependencies)
	container := container.NewContainer(cfg)
	defer container.Close()

	// Setup routes
	routes.SetupRoutes(router, container)

	t.Run("Health Check", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/public/health", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, "healthy", response["status"])
	})

	t.Run("Authentication Required", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/conversations/", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Valid JWT Token", func(t *testing.T) {
		// Create a test JWT token
		token := createTestJWT(t, "test-user-id", "<EMAIL>", "user")

		req, _ := http.NewRequest("GET", "/api/v1/conversations/", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should not be unauthorized (might be other errors due to missing dependencies)
		assert.NotEqual(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Send Message Flow", func(t *testing.T) {
		// Create test data
		messageData := map[string]interface{}{
			"conversation_id": uuid.New().String(),
			"content": map[string]interface{}{
				"text": "Hello, world!",
			},
			"message_type": "text",
		}

		jsonData, _ := json.Marshal(messageData)
		token := createTestJWT(t, "test-user-id", "<EMAIL>", "user")

		req, _ := http.NewRequest("POST", "/api/v1/messages/", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// The request should be properly authenticated and reach the handler
		// The exact response depends on your service implementations
		fmt.Printf("Send message response: %d - %s\n", w.Code, w.Body.String())
	})

	t.Run("Admin Role Required", func(t *testing.T) {
		// Test with user role (should fail)
		userToken := createTestJWT(t, "test-user-id", "<EMAIL>", "user")
		req, _ := http.NewRequest("GET", "/api/v1/realtime/stats", nil)
		req.Header.Set("Authorization", "Bearer "+userToken)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		// Test with admin role (should pass auth)
		adminToken := createTestJWT(t, "admin-user-id", "<EMAIL>", "admin")
		req, _ = http.NewRequest("GET", "/api/v1/realtime/stats", nil)
		req.Header.Set("Authorization", "Bearer "+adminToken)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should not be unauthorized, but may be forbidden if role isn't set
		assert.NotEqual(t, http.StatusUnauthorized, w.Code)
	})
}

func TestWebSocketAuth(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set JWT secret for testing
	os.Setenv("JWT_SECRET", testJWTSecret)
	defer os.Unsetenv("JWT_SECRET")
	
	cfg := config.LoadConfig()

	container := container.NewContainer(cfg)
	defer container.Close()

	routes.SetupRoutes(router, container)

	t.Run("WebSocket Without Token", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/ws/", nil)
		req.Header.Set("Connection", "upgrade")
		req.Header.Set("Upgrade", "websocket")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("WebSocket With Token in Query", func(t *testing.T) {
		token := createTestJWT(t, "test-user-id", "<EMAIL>", "user")
		req, _ := http.NewRequest("GET", "/api/v1/ws/?token="+token, nil)
		req.Header.Set("Connection", "upgrade")
		req.Header.Set("Upgrade", "websocket")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should not be unauthorized
		assert.NotEqual(t, http.StatusUnauthorized, w.Code)
	})
}

func TestJWTTokenValidation(t *testing.T) {
	t.Run("Invalid Token Format", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		router := gin.New()

		// Set test environment variables
		os.Setenv("TEST_MODE", "true")
		os.Setenv("JWT_SECRET", testJWTSecret)
		defer func() {
			os.Unsetenv("TEST_MODE")
			os.Unsetenv("JWT_SECRET")
		}()
		
		cfg := config.LoadConfig()

		container := container.NewContainer(cfg)
		defer container.Close()

		routes.SetupRoutes(router, container)

		req, _ := http.NewRequest("GET", "/api/v1/conversations/", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Missing Claims", func(t *testing.T) {
		// Create token without user_id claim
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
			"email": "<EMAIL>",
			"exp":   time.Now().Add(time.Hour).Unix(),
		})

		tokenString, _ := token.SignedString([]byte(testJWTSecret))

		gin.SetMode(gin.TestMode)
		router := gin.New()

		// Set test environment variables
		os.Setenv("TEST_MODE", "true")
		os.Setenv("JWT_SECRET", testJWTSecret)
		defer func() {
			os.Unsetenv("TEST_MODE")
			os.Unsetenv("JWT_SECRET")
		}()
		
		cfg := config.LoadConfig()

		container := container.NewContainer(cfg)
		defer container.Close()

		routes.SetupRoutes(router, container)

		req, _ := http.NewRequest("GET", "/api/v1/conversations/", nil)
		req.Header.Set("Authorization", "Bearer "+tokenString)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Helper function to create test JWT tokens
func createTestJWT(t *testing.T, userID, email, role string) string {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": userID,
		"email":   email,
		"role":    role,
		"exp":     time.Now().Add(time.Hour).Unix(),
		"iat":     time.Now().Unix(),
	})

	tokenString, err := token.SignedString([]byte(testJWTSecret))
	require.NoError(t, err)

	return tokenString
}

// Benchmark test for message handling
func BenchmarkMessageSend(b *testing.B) {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set JWT secret for testing
	os.Setenv("JWT_SECRET", testJWTSecret)
	defer os.Unsetenv("JWT_SECRET")
	
	cfg := config.LoadConfig()

	container := container.NewContainer(cfg)
	defer container.Close()

	routes.SetupRoutes(router, container)

	token := createTestJWTForBenchmark(b)
	messageData := map[string]interface{}{
		"conversation_id": uuid.New().String(),
		"content": map[string]interface{}{
			"text": "Benchmark message",
		},
		"message_type": "text",
	}

	jsonData, _ := json.Marshal(messageData)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			req, _ := http.NewRequest("POST", "/api/v1/messages/", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
		}
	})
}

func createTestJWTForBenchmark(b *testing.B) string {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": "benchmark-user",
		"email":   "<EMAIL>",
		"role":    "user",
		"exp":     time.Now().Add(time.Hour).Unix(),
		"iat":     time.Now().Unix(),
	})

	tokenString, err := token.SignedString([]byte(testJWTSecret))
	if err != nil {
		b.Fatal(err)
	}

	return tokenString
}