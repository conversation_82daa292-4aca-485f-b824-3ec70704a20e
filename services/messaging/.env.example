# Messaging Service Configuration
# Most configuration is centralized in the root .env file
# This file contains development overrides and messaging-specific settings

# Service-specific Redis database (to avoid conflicts)
REDIS_DB=2

# WebSocket-specific configuration
WS_MAX_CONNECTIONS=100000
WS_READ_TIMEOUT=60s
WS_WRITE_TIMEOUT=10s
WS_PONG_TIMEOUT=30s
WS_PING_PERIOD=25s
WS_MAX_MESSAGE_SIZE=8192
WS_ENABLE_COMPRESSION=true
WS_BUFFER_SIZE=1024
WS_CHANNEL_BUFFER_SIZE=256

# Messaging-specific configuration
MESSAGES_PER_DOCUMENT=100
SEQUENCE_BATCH_SIZE=50
GROUP_SEQUENCE_BATCH=100
MESSAGE_CACHE_TTL=24h
SEQUENCE_CACHE_TTL=8760h
CONVERSATION_CACHE_TTL=12h
READ_STATUS_TTL=168h
MESSAGE_MAX_WORKERS=50
MESSAGE_BATCH_SIZE=500
MESSAGE_BATCH_INTERVAL=100ms

# Queue configuration
QUEUE_WORKER_COUNT=10
QUEUE_MAX_WORKER_COUNT=50
QUEUE_MIN_WORKER_COUNT=2
QUEUE_PRIORITY_QUEUES=3
QUEUE_MAX_SIZE=1000
QUEUE_PROCESSING_TIMEOUT=300
QUEUE_RETRY_ATTEMPTS=3
QUEUE_BACKOFF_MULTIPLIER=2.0
QUEUE_MAX_BACKOFF_DURATION=5m
QUEUE_BATCH_PROCESSING=true
QUEUE_BATCH_SIZE=10
QUEUE_BATCH_TIMEOUT=100ms
QUEUE_ENABLE_METRICS=true
QUEUE_METRICS_INTERVAL=30s
QUEUE_HEALTH_CHECK_INTERVAL=1m

# Connection management
CONNECTION_TTL=30m
CONNECTION_PRESENCE_TTL=5m
CONNECTION_METADATA_TTL=1h
CONNECTION_CLEANUP_INTERVAL=5m
CONNECTION_STALE_TIMEOUT=10m
CONNECTION_BATCH_SIZE=100
CONNECTION_MAX_CONCURRENT_OPS=10
CONNECTION_HEALTH_CHECK_INTERVAL=1m
CONNECTION_HEARTBEAT_INTERVAL=30s

# Event processing
EVENTS_REALTIME_ENABLED=true
EVENTS_REALTIME_TIMEOUT=5s
EVENTS_ASYNC_ENABLED=true
EVENTS_ASYNC_RETRIES=3
EVENTS_NOTIFICATIONS_ENABLED=true
EVENTS_OFFLINE_NOTIFICATION_DELAY=30s
EVENTS_FILTER_SYSTEM_MESSAGES=false
EVENTS_FILTER_DELETED_MESSAGES=true
EVENTS_BUFFER_SIZE=1000
EVENTS_BATCH_SIZE=10
EVENTS_FLUSH_INTERVAL=100ms