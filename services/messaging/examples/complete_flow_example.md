# Complete Messaging Flow Example

This document demonstrates the complete messaging flow with all implemented features.

## 1. Authentication

### Get JWT Token
```bash
# Example JWT token generation (you'll need to implement this endpoint)
curl -X POST http://localhost:8005/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

Response:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user_id": "123e4567-e89b-12d3-a456-426614174000"
}
```

## 2. Create a Direct Conversation

```bash
curl -X POST http://localhost:8005/api/v1/conversations/direct \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "participant_id": "987fcdeb-51a2-43d1-9f4e-123456789abc"
  }'
```

Response:
```json
{
  "id": "conv-123e4567-e89b-12d3-a456-426614174000",
  "type": "direct",
  "creator_id": "123e4567-e89b-12d3-a456-426614174000",
  "last_activity": "2023-07-14T10:30:00Z",
  "message_count": 0,
  "max_sequence": 0,
  "created_at": "2023-07-14T10:30:00Z",
  "participants": [
    {
      "user_id": "123e4567-e89b-12d3-a456-426614174000",
      "role": "member",
      "joined_at": "2023-07-14T10:30:00Z",
      "last_read_seq": 0
    },
    {
      "user_id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
      "role": "member",
      "joined_at": "2023-07-14T10:30:00Z",
      "last_read_seq": 0
    }
  ]
}
```

## 3. Send a Message

```bash
curl -X POST http://localhost:8005/api/v1/messages \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "conv-123e4567-e89b-12d3-a456-426614174000",
    "content": {
      "text": "Hello! How are you today?",
      "mentions": []
    },
    "message_type": "text"
  }'
```

Response:
```json
{
  "id": "msg-123e4567-e89b-12d3-a456-426614174001",
  "conversation_id": "conv-123e4567-e89b-12d3-a456-426614174000",
  "sender_id": "123e4567-e89b-12d3-a456-426614174000",
  "content": {
    "text": "Hello! How are you today?",
    "attachments": [],
    "mentions": [],
    "links": []
  },
  "message_type": "text",
  "sequence": 1,
  "status": "sent",
  "timestamp": "2023-07-14T10:31:00Z",
  "read_by": []
}
```

## 4. Send a Message with Attachment

```bash
curl -X POST http://localhost:8005/api/v1/messages \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "conv-123e4567-e89b-12d3-a456-426614174000",
    "content": {
      "text": "Check out this image!",
      "attachments": [
        {
          "id": "att-123e4567-e89b-12d3-a456-426614174002",
          "name": "vacation_photo.jpg",
          "url": "https://storage.example.com/files/vacation_photo.jpg",
          "mime_type": "image/jpeg",
          "size": 2048576
        }
      ]
    },
    "message_type": "image"
  }'
```

## 5. Get Messages from Conversation

```bash
curl -X GET "http://localhost:8005/api/v1/conversations/conv-123e4567-e89b-12d3-a456-426614174000/messages?limit=20&offset=0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "messages": [
    {
      "id": "msg-123e4567-e89b-12d3-a456-426614174001",
      "conversation_id": "conv-123e4567-e89b-12d3-a456-426614174000",
      "sender_id": "123e4567-e89b-12d3-a456-426614174000",
      "content": {
        "text": "Hello! How are you today?",
        "attachments": [],
        "mentions": [],
        "links": []
      },
      "message_type": "text",
      "sequence": 1,
      "status": "sent",
      "timestamp": "2023-07-14T10:31:00Z"
    }
  ],
  "total": 2,
  "limit": 20,
  "offset": 0
}
```

## 6. Edit a Message

```bash
curl -X PUT http://localhost:8005/api/v1/messages/msg-123e4567-e89b-12d3-a456-426614174001 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "content": {
      "text": "Hello! How are you today? (edited)"
    }
  }'
```

## 7. Mark Message as Read

```bash
curl -X POST http://localhost:8005/api/v1/messages/msg-123e4567-e89b-12d3-a456-426614174001/read \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "status": "marked_as_read"
}
```

## 8. Search Messages

```bash
curl -X GET "http://localhost:8005/api/v1/search/messages?q=hello&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "messages": [
    {
      "id": "msg-123e4567-e89b-12d3-a456-426614174001",
      "conversation_id": "conv-123e4567-e89b-12d3-a456-426614174000",
      "content": {
        "text": "Hello! How are you today? (edited)"
      },
      "message_type": "text",
      "sequence": 1,
      "timestamp": "2023-07-14T10:31:00Z"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

## 9. WebSocket Real-time Connection

### Connect to WebSocket

```javascript
// JavaScript WebSocket client example
const token = 'YOUR_JWT_TOKEN';
const ws = new WebSocket(`ws://localhost:8005/api/v1/ws?token=${token}`);

ws.onopen = function() {
    console.log('Connected to messaging service');
    
    // Subscribe to conversation updates
    ws.send(JSON.stringify({
        type: 'subscribe',
        data: {
            topic: 'conversation:conv-123e4567-e89b-12d3-a456-426614174000'
        },
        timestamp: Date.now()
    }));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('Received message:', message);
    
    switch(message.type) {
        case 'message':
            handleNewMessage(message.data);
            break;
        case 'message_edited':
            handleMessageEdit(message.data);
            break;
        case 'message_deleted':
            handleMessageDelete(message.data);
            break;
        case 'read_status':
            handleReadStatus(message.data);
            break;
    }
};

// Send a message via WebSocket
function sendMessage(conversationId, text) {
    const message = {
        type: 'message',
        id: generateClientId(),
        data: {
            conversation_id: conversationId,
            content: {
                text: text,
                attachments: [],
                mentions: []
            },
            message_type: 'text',
            client_id: generateClientId()
        },
        timestamp: Date.now()
    };
    
    ws.send(JSON.stringify(message));
}

function generateClientId() {
    return 'client-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}
```

### Real-time Message Flow

1. User A connects to WebSocket
2. User A subscribes to conversation updates
3. User B sends a message via REST API
4. User A receives the message in real-time via WebSocket
5. User A sends a read receipt via WebSocket
6. User B receives the read status update

## 10. Create Group Conversation

```bash
curl -X POST http://localhost:8005/api/v1/conversations/group \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Project Team Chat",
    "description": "Chat for project collaboration",
    "participants": [
      "987fcdeb-51a2-43d1-9f4e-123456789abc",
      "456789ab-cdef-1234-5678-90abcdef1234",
      "abcdef12-3456-7890-abcd-ef1234567890"
    ]
  }'
```

## 11. Get User Conversations

```bash
curl -X GET "http://localhost:8005/api/v1/conversations?limit=20&offset=0" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "conversations": [
    {
      "id": "conv-123e4567-e89b-12d3-a456-426614174000",
      "type": "direct",
      "creator_id": "123e4567-e89b-12d3-a456-426614174000",
      "last_activity": "2023-07-14T10:35:00Z",
      "message_count": 3,
      "max_sequence": 3,
      "participants": [...]
    },
    {
      "id": "conv-group-456789ab-cdef-1234-5678-90abcdef1234",
      "type": "group",
      "name": "Project Team Chat",
      "description": "Chat for project collaboration",
      "creator_id": "123e4567-e89b-12d3-a456-426614174000",
      "last_activity": "2023-07-14T10:32:00Z",
      "message_count": 1,
      "max_sequence": 1,
      "participants": [...]
    }
  ],
  "total": 2,
  "limit": 20,
  "offset": 0
}
```

## 12. Admin Operations (Requires admin role)

### Get Hub Statistics

```bash
curl -X GET http://localhost:8005/api/v1/realtime/stats \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

Response:
```json
{
  "active_connections": 150,
  "active_users": 120,
  "max_connections": 100000,
  "uptime": "2h 15m 30s"
}
```

### Kick User

```bash
curl -X POST http://localhost:8005/api/v1/realtime/users/123e4567-e89b-12d3-a456-426614174000/kick \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Violation of terms of service"
  }'
```

## Error Handling Examples

### Authentication Error
```json
{
  "error": {
    "code": "unauthorized",
    "message": "Invalid token",
    "timestamp": "2023-07-14T10:30:00Z"
  }
}
```

### Validation Error
```json
{
  "error": {
    "code": "validation_failed",
    "message": "Invalid request",
    "details": "Conversation ID is required",
    "timestamp": "2023-07-14T10:30:00Z"
  }
}
```

### Access Denied
```json
{
  "error": {
    "code": "access_denied",
    "message": "Access denied",
    "details": "User is not a participant in this conversation",
    "timestamp": "2023-07-14T10:30:00Z"
  }
}
```

## Performance Considerations

- **Batch Operations**: Use bulk operations for multiple messages
- **Caching**: Messages are cached for 24 hours for faster retrieval
- **WebSocket Compression**: Enabled by default for supported clients
- **Connection Limits**: Configured for 100K concurrent connections
- **Rate Limiting**: Implement rate limiting for message sending

## Monitoring

- **Health Check**: `GET /api/v1/public/health`
- **Hub Statistics**: `GET /api/v1/realtime/stats` (admin only)
- **Connection Status**: `GET /api/v1/realtime/users/{userId}/status`

This example demonstrates the complete messaging flow with authentication, real-time communication, and all major features implemented.