-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create conversations table
CREATE TABLE conversation_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('direct', 'group', 'team_channel')),
    name VA<PERSON>HA<PERSON>(255),
    description VARCHAR(1000),
    team_id UUID,
    creator_id UUID NOT NULL,
    last_message_id VARCHAR(255),
    last_activity TIMESTAMP NOT NULL DEFAULT NOW(),
    message_count BIGINT DEFAULT 0 CHECK (message_count >= 0),
    max_sequence BIGINT DEFAULT 0 CHECK (max_sequence >= 0),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP
);

-- Create conversation participants table
CREATE TABLE conversation_participant_models (
    conversation_id UUID NOT NULL,
    user_id UUID NOT NULL,
    joined_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_read_seq BIGINT DEFAULT 0 CHECK (last_read_seq >= 0),
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('admin', 'member', 'observer')),
    PRIMARY KEY (conversation_id, user_id),
    FOREIGN KEY (conversation_id) REFERENCES conversation_models(id) ON DELETE CASCADE
);

-- Create indexes for performance optimization

-- Conversations table indexes
CREATE INDEX idx_conversations_type ON conversation_models(type);
CREATE INDEX idx_conversations_team_id ON conversation_models(team_id) WHERE team_id IS NOT NULL;
CREATE INDEX idx_conversations_creator_id ON conversation_models(creator_id);
CREATE INDEX idx_conversations_last_activity ON conversation_models(last_activity DESC);
CREATE INDEX idx_conversations_created_at ON conversation_models(created_at DESC);
CREATE INDEX idx_conversations_deleted_at ON conversation_models(deleted_at) WHERE deleted_at IS NOT NULL;
CREATE INDEX idx_conversations_active ON conversation_models(last_activity DESC) WHERE deleted_at IS NULL;

-- Participants table indexes
CREATE INDEX idx_participants_user_id ON conversation_participant_models(user_id);
CREATE INDEX idx_participants_conversation_id ON conversation_participant_models(conversation_id);
CREATE INDEX idx_participants_role ON conversation_participant_models(role);
CREATE INDEX idx_participants_joined_at ON conversation_participant_models(joined_at DESC);
CREATE INDEX idx_participants_last_read_seq ON conversation_participant_models(last_read_seq);

-- Composite indexes for common queries
CREATE INDEX idx_conversations_user_activity ON conversation_participant_models(user_id, conversation_id) 
INCLUDE (last_read_seq);

CREATE INDEX idx_conversations_team_activity ON conversation_models(team_id, last_activity DESC) 
WHERE team_id IS NOT NULL AND deleted_at IS NULL;

CREATE INDEX idx_conversations_direct_lookup ON conversation_models(type, deleted_at) 
WHERE type = 'direct';

-- Unique constraint for direct conversations (ensure only one direct conversation between two users)
CREATE UNIQUE INDEX idx_direct_conversation_uniqueness 
ON conversation_participant_models(LEAST(user_id::text, conversation_id::text), GREATEST(user_id::text, conversation_id::text))
WHERE EXISTS (
    SELECT 1 FROM conversation_models 
    WHERE conversation_models.id = conversation_participant_models.conversation_id 
    AND conversation_models.type = 'direct'
    AND conversation_models.deleted_at IS NULL
);

-- Create trigger for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON conversation_models 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE conversation_models IS 'Stores conversation metadata and aggregate information';
COMMENT ON TABLE conversation_participant_models IS 'Stores user participation in conversations with read status';

COMMENT ON COLUMN conversation_models.type IS 'Type of conversation: direct, group, or team_channel';
COMMENT ON COLUMN conversation_models.team_id IS 'Reference to team for team_channel conversations';
COMMENT ON COLUMN conversation_models.last_message_id IS 'ID of the last message in this conversation';
COMMENT ON COLUMN conversation_models.message_count IS 'Total number of messages in conversation';
COMMENT ON COLUMN conversation_models.max_sequence IS 'Highest sequence number in conversation';

COMMENT ON COLUMN conversation_participant_models.last_read_seq IS 'Last message sequence number read by user';
COMMENT ON COLUMN conversation_participant_models.role IS 'User role in conversation: admin, member, or observer';