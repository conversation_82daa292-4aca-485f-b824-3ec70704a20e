-- Drop triggers
DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversation_models;
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes (most will be dropped automatically with tables, but being explicit)
DROP INDEX IF EXISTS idx_direct_conversation_uniqueness;
DROP INDEX IF EXISTS idx_conversations_direct_lookup;
DROP INDEX IF EXISTS idx_conversations_team_activity;
DROP INDEX IF EXISTS idx_conversations_user_activity;
DROP INDEX IF EXISTS idx_participants_last_read_seq;
DROP INDEX IF EXISTS idx_participants_joined_at;
DROP INDEX IF EXISTS idx_participants_role;
DROP INDEX IF EXISTS idx_participants_conversation_id;
DROP INDEX IF EXISTS idx_participants_user_id;
DROP INDEX IF EXISTS idx_conversations_active;
DROP INDEX IF EXISTS idx_conversations_deleted_at;
DROP INDEX IF EXISTS idx_conversations_created_at;
DROP INDEX IF EXISTS idx_conversations_last_activity;
DROP INDEX IF EXISTS idx_conversations_creator_id;
DROP INDEX IF EXISTS idx_conversations_team_id;
DROP INDEX IF EXISTS idx_conversations_type;

-- Drop tables (foreign key constraints will be handled automatically)
DROP TABLE IF EXISTS conversation_participant_models;
DROP TABLE IF EXISTS conversation_models;

-- Note: We don't drop uuid-ossp extension as it might be used by other services