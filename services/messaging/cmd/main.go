package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	messagingconfig "github.com/swork-team/platform/services/messaging/internal/infrastructure/config"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/container"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/http/routes"
)

func main() {
	cfg := messagingconfig.LoadConfig()

	container := container.NewContainer(cfg)
	defer container.Close()

	if err := container.Initialize(); err != nil {
		log.Fatalf("Failed to initialize container: %v", err)
	}

	router := setupRouter(container)

	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	go func() {
		log.Printf("Messaging service starting on %s:%s", cfg.Server.Host, cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down messaging service...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Messaging service exited")
}

func setupRouter(container *container.Container) *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	
	// Enable automatic trailing slash redirection
	router.RedirectTrailingSlash = true

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "messaging",
			"status":  "healthy",
		})
	})

	// Setup routes
	routes.SetupRoutes(router, container)

	return router
}