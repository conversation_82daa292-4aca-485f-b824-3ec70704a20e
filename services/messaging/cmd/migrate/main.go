package main

import (
	"context"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"database/sql"
	_ "github.com/lib/pq"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	
	"github.com/swork-team/platform/pkg/config"
)

type Migration struct {
	Version int
	Name    string
	UpSQL   string
	DownSQL string
}

type MigrationRunner struct {
	db          *sql.DB
	mongoClient *mongo.Client
	mongoDB     *mongo.Database
	migrations  []Migration
}

func main() {
	var (
		direction = flag.String("direction", "up", "Migration direction: up or down")
		target    = flag.String("target", "", "Target migration version (optional)")
	)
	flag.Parse()

	// Load centralized configuration
	cfg := config.NewMessagingServiceConfig()
	
	// Build PostgreSQL connection string
	dbURL := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=%s",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
		cfg.Database.SSLMode,
	)
	
	runner, err := NewMigrationRunner(dbURL, cfg.MongoDB.URI, cfg.MongoDB.Database)
	if err != nil {
		log.Fatalf("Failed to create migration runner: %v", err)
	}
	defer runner.Close()

	log.Println("Starting messaging service migrations...")

	if err := runner.LoadMigrations(); err != nil {
		log.Fatalf("Failed to load migrations: %v", err)
	}

	switch *direction {
	case "up":
		if err := runner.MigrateUp(*target); err != nil {
			log.Fatalf("Migration up failed: %v", err)
		}
	case "down":
		if err := runner.MigrateDown(*target); err != nil {
			log.Fatalf("Migration down failed: %v", err)
		}
	default:
		log.Fatalf("Unknown direction: %s. Use 'up' or 'down'", *direction)
	}

	log.Println("Migration completed successfully!")
}

func NewMigrationRunner(dbURL, mongoURL, mongoDBName string) (*MigrationRunner, error) {
	// Connect to PostgreSQL
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	// Connect to MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	mongoClient, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURL))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	if err := mongoClient.Ping(ctx, nil); err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	mongoDB := mongoClient.Database(mongoDBName)

	runner := &MigrationRunner{
		db:          db,
		mongoClient: mongoClient,
		mongoDB:     mongoDB,
	}

	// Create migrations table if it doesn't exist
	if err := runner.createMigrationsTable(); err != nil {
		return nil, fmt.Errorf("failed to create migrations table: %w", err)
	}

	log.Println("Connected to PostgreSQL and MongoDB successfully")
	return runner, nil
}

func (r *MigrationRunner) Close() {
	if r.db != nil {
		r.db.Close()
	}
	if r.mongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		r.mongoClient.Disconnect(ctx)
	}
}

func (r *MigrationRunner) createMigrationsTable() error {
	query := `
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version INTEGER PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			applied_at TIMESTAMP DEFAULT NOW()
		);
	`
	_, err := r.db.Exec(query)
	return err
}

func (r *MigrationRunner) LoadMigrations() error {
	migrationsDir := "migrations"
	
	// Try to find migrations directory
	possiblePaths := []string{
		migrationsDir,
		"../../migrations",
		"./services/messaging/migrations",
	}
	
	var migrationsPath string
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			migrationsPath = path
			break
		}
	}
	
	if migrationsPath == "" {
		return fmt.Errorf("migrations directory not found in any of: %v", possiblePaths)
	}

	log.Printf("Loading migrations from: %s", migrationsPath)

	files, err := ioutil.ReadDir(migrationsPath)
	if err != nil {
		return fmt.Errorf("failed to read migrations directory: %w", err)
	}

	migrations := make(map[int]*Migration)

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		name := file.Name()
		if !strings.HasSuffix(name, ".sql") {
			continue
		}

		// Parse migration version from filename (e.g., "001_initial_schema.up.sql")
		parts := strings.Split(name, "_")
		if len(parts) < 2 {
			log.Printf("Skipping invalid migration file: %s", name)
			continue
		}

		version, err := strconv.Atoi(parts[0])
		if err != nil {
			log.Printf("Skipping migration with invalid version: %s", name)
			continue
		}

		content, err := ioutil.ReadFile(filepath.Join(migrationsPath, name))
		if err != nil {
			return fmt.Errorf("failed to read migration file %s: %w", name, err)
		}

		migration, exists := migrations[version]
		if !exists {
			migration = &Migration{
				Version: version,
				Name:    strings.Join(parts[1:], "_"),
			}
			migrations[version] = migration
		}

		if strings.Contains(name, ".up.sql") {
			migration.UpSQL = string(content)
		} else if strings.Contains(name, ".down.sql") {
			migration.DownSQL = string(content)
		}
	}

	// Convert map to sorted slice
	r.migrations = make([]Migration, 0, len(migrations))
	for _, migration := range migrations {
		r.migrations = append(r.migrations, *migration)
	}

	sort.Slice(r.migrations, func(i, j int) bool {
		return r.migrations[i].Version < r.migrations[j].Version
	})

	log.Printf("Loaded %d migrations", len(r.migrations))
	return nil
}

func (r *MigrationRunner) MigrateUp(target string) error {
	currentVersion, err := r.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("failed to get current version: %w", err)
	}

	targetVersion := -1
	if target != "" {
		targetVersion, err = strconv.Atoi(target)
		if err != nil {
			return fmt.Errorf("invalid target version: %s", target)
		}
	}

	log.Printf("Current version: %d", currentVersion)

	applied := 0
	for _, migration := range r.migrations {
		if migration.Version <= currentVersion {
			continue
		}

		if targetVersion != -1 && migration.Version > targetVersion {
			break
		}

		log.Printf("Applying migration %d: %s", migration.Version, migration.Name)

		if err := r.applyMigration(migration, true); err != nil {
			return fmt.Errorf("failed to apply migration %d: %w", migration.Version, err)
		}

		applied++
	}

	if applied == 0 {
		log.Println("No migrations to apply")
	} else {
		log.Printf("Applied %d migrations", applied)
	}

	// Initialize MongoDB collections and indexes
	if err := r.initializeMongoDB(); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %w", err)
	}

	return nil
}

func (r *MigrationRunner) MigrateDown(target string) error {
	if target == "" {
		return fmt.Errorf("target version required for down migration")
	}

	targetVersion, err := strconv.Atoi(target)
	if err != nil {
		return fmt.Errorf("invalid target version: %s", target)
	}

	currentVersion, err := r.getCurrentVersion()
	if err != nil {
		return fmt.Errorf("failed to get current version: %w", err)
	}

	log.Printf("Current version: %d, target version: %d", currentVersion, targetVersion)

	applied := 0
	// Apply migrations in reverse order
	for i := len(r.migrations) - 1; i >= 0; i-- {
		migration := r.migrations[i]

		if migration.Version <= targetVersion || migration.Version > currentVersion {
			continue
		}

		log.Printf("Reverting migration %d: %s", migration.Version, migration.Name)

		if err := r.applyMigration(migration, false); err != nil {
			return fmt.Errorf("failed to revert migration %d: %w", migration.Version, err)
		}

		applied++
	}

	if applied == 0 {
		log.Println("No migrations to revert")
	} else {
		log.Printf("Reverted %d migrations", applied)
	}

	return nil
}

func (r *MigrationRunner) getCurrentVersion() (int, error) {
	var version int
	err := r.db.QueryRow("SELECT COALESCE(MAX(version), 0) FROM schema_migrations").Scan(&version)
	if err != nil {
		return 0, err
	}
	return version, nil
}

func (r *MigrationRunner) applyMigration(migration Migration, up bool) error {
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	var sql string
	if up {
		sql = migration.UpSQL
	} else {
		sql = migration.DownSQL
	}

	if sql == "" {
		return fmt.Errorf("migration SQL is empty")
	}

	// Execute the migration SQL
	if _, err := tx.Exec(sql); err != nil {
		return fmt.Errorf("failed to execute migration SQL: %w", err)
	}

	// Update migrations table
	if up {
		_, err = tx.Exec(
			"INSERT INTO schema_migrations (version, name, applied_at) VALUES ($1, $2, NOW())",
			migration.Version, migration.Name,
		)
	} else {
		_, err = tx.Exec(
			"DELETE FROM schema_migrations WHERE version = $1",
			migration.Version,
		)
	}

	if err != nil {
		return fmt.Errorf("failed to update migrations table: %w", err)
	}

	return tx.Commit()
}

func (r *MigrationRunner) initializeMongoDB() error {
	log.Println("Initializing MongoDB collections and indexes...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Create collections with validation (this will be handled by the init-mongodb.js script)
	collections := []string{
		"message_documents",
		"message_cache", 
		"sequence_allocation",
		"message_analytics",
		"connection_tracking",
	}

	for _, collName := range collections {
		coll := r.mongoDB.Collection(collName)
		
		// Check if collection exists
		collections, err := r.mongoDB.ListCollectionNames(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to list collections: %w", err)
		}

		exists := false
		for _, existing := range collections {
			if existing == collName {
				exists = true
				break
			}
		}

		if !exists {
			log.Printf("Creating MongoDB collection: %s", collName)
			if err := r.mongoDB.CreateCollection(ctx, collName); err != nil {
				return fmt.Errorf("failed to create collection %s: %w", collName, err)
			}
		}

		// Create basic indexes (detailed indexes are in init-mongodb.js)
		switch collName {
		case "message_documents":
			_, err = coll.Indexes().CreateOne(ctx, mongo.IndexModel{
				Keys: map[string]interface{}{"doc_id": 1},
			})
		case "sequence_allocation":
			_, err = coll.Indexes().CreateOne(ctx, mongo.IndexModel{
				Keys: map[string]interface{}{"conversation_id": 1},
			})
		case "connection_tracking":
			_, err = coll.Indexes().CreateOne(ctx, mongo.IndexModel{
				Keys: map[string]interface{}{"connection_id": 1},
			})
		}

		if err != nil {
			log.Printf("Warning: failed to create indexes for %s: %v", collName, err)
		}
	}

	log.Println("MongoDB initialization completed")
	return nil
}