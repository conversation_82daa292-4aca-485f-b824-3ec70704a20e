package cache

import (
	"container/list"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"sync"
	"time"
)

// LocalCache represents a slot-based LRU cache (OpenIM pattern)
type LocalCache struct {
	slots    []*CacheSlot
	slotMask uint32
	config   LocalCacheConfig
}

// LocalCacheConfig holds configuration for the local cache
type LocalCacheConfig struct {
	SlotCount   int
	SlotSize    int
	SuccessTTL  time.Duration
	FailureTTL  time.Duration
	CleanupInterval time.Duration
}

// CacheSlot represents a single cache slot with LRU eviction
type CacheSlot struct {
	mutex    sync.RWMutex
	items    map[string]*CacheItem
	lruList  *list.List
	maxSize  int
	hitCount int64
	missCount int64
}

// CacheItem represents a cached item
type CacheItem struct {
	key       string
	value     interface{}
	expiresAt time.Time
	isFailure bool
	listNode  *list.Element
	size      int
}

// NewLocalCache creates a new slot-based local cache
func NewLocalCache(config LocalCacheConfig) *LocalCache {
	if config.SlotCount == 0 {
		config.SlotCount = LocalCacheSlots
	}
	if config.SlotSize == 0 {
		config.SlotSize = LocalCacheSize
	}
	if config.SuccessTTL == 0 {
		config.SuccessTTL = LocalCacheSuccessTTL
	}
	if config.FailureTTL == 0 {
		config.FailureTTL = LocalCacheFailureTTL
	}
	if config.CleanupInterval == 0 {
		config.CleanupInterval = 1 * time.Minute
	}
	
	// Ensure slot count is a power of 2 for efficient masking
	slotCount := nextPowerOf2(uint32(config.SlotCount))
	
	cache := &LocalCache{
		slots:    make([]*CacheSlot, slotCount),
		slotMask: slotCount - 1,
		config:   config,
	}
	
	// Initialize slots
	for i := range cache.slots {
		cache.slots[i] = &CacheSlot{
			items:   make(map[string]*CacheItem),
			lruList: list.New(),
			maxSize: config.SlotSize,
		}
	}
	
	// Start cleanup goroutine
	go cache.cleanupLoop()
	
	return cache
}

// Get retrieves a value from the cache
func (lc *LocalCache) Get(key string) (interface{}, bool) {
	slot := lc.getSlot(key)
	slot.mutex.RLock()
	defer slot.mutex.RUnlock()
	
	item, exists := slot.items[key]
	if !exists {
		slot.missCount++
		return nil, false
	}
	
	// Check expiration
	if time.Now().After(item.expiresAt) {
		slot.missCount++
		// Don't delete here to avoid lock upgrade, let cleanup handle it
		return nil, false
	}
	
	// Update LRU position
	slot.lruList.MoveToFront(item.listNode)
	slot.hitCount++
	
	return item.value, true
}

// Set stores a value in the cache
func (lc *LocalCache) Set(key string, value interface{}, ttl time.Duration) {
	if ttl == 0 {
		ttl = lc.config.SuccessTTL
	}
	
	slot := lc.getSlot(key)
	slot.mutex.Lock()
	defer slot.mutex.Unlock()
	
	lc.setInSlot(slot, key, value, ttl, false)
}

// SetFailure stores a failure marker in the cache
func (lc *LocalCache) SetFailure(key string) {
	slot := lc.getSlot(key)
	slot.mutex.Lock()
	defer slot.mutex.Unlock()
	
	lc.setInSlot(slot, key, nil, lc.config.FailureTTL, true)
}

// Delete removes a key from the cache
func (lc *LocalCache) Delete(key string) {
	slot := lc.getSlot(key)
	slot.mutex.Lock()
	defer slot.mutex.Unlock()
	
	if item, exists := slot.items[key]; exists {
		slot.lruList.Remove(item.listNode)
		delete(slot.items, key)
	}
}

// DeleteByPrefix removes all keys with the given prefix
func (lc *LocalCache) DeleteByPrefix(prefix string) {
	for _, slot := range lc.slots {
		slot.mutex.Lock()
		
		keysToDelete := make([]string, 0)
		for key := range slot.items {
			if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
				keysToDelete = append(keysToDelete, key)
			}
		}
		
		for _, key := range keysToDelete {
			if item, exists := slot.items[key]; exists {
				slot.lruList.Remove(item.listNode)
				delete(slot.items, key)
			}
		}
		
		slot.mutex.Unlock()
	}
}

// Clear removes all items from the cache
func (lc *LocalCache) Clear() {
	for _, slot := range lc.slots {
		slot.mutex.Lock()
		slot.items = make(map[string]*CacheItem)
		slot.lruList = list.New()
		slot.hitCount = 0
		slot.missCount = 0
		slot.mutex.Unlock()
	}
}

// GetStats returns cache statistics
func (lc *LocalCache) GetStats() CacheStats {
	var stats CacheStats
	
	for _, slot := range lc.slots {
		slot.mutex.RLock()
		stats.Hits += slot.hitCount
		stats.Misses += slot.missCount
		stats.ItemCount += int64(len(slot.items))
		slot.mutex.RUnlock()
	}
	
	if stats.Hits+stats.Misses > 0 {
		stats.HitRatio = float64(stats.Hits) / float64(stats.Hits+stats.Misses)
	}
	
	return stats
}

// GetSlotStats returns per-slot statistics
func (lc *LocalCache) GetSlotStats() []SlotStats {
	stats := make([]SlotStats, len(lc.slots))
	
	for i, slot := range lc.slots {
		slot.mutex.RLock()
		stats[i] = SlotStats{
			SlotIndex: i,
			ItemCount: len(slot.items),
			Hits:      slot.hitCount,
			Misses:    slot.missCount,
		}
		if stats[i].Hits+stats[i].Misses > 0 {
			stats[i].HitRatio = float64(stats[i].Hits) / float64(stats[i].Hits+stats[i].Misses)
		}
		slot.mutex.RUnlock()
	}
	
	return stats
}

// Internal methods

// getSlot returns the appropriate slot for a key
func (lc *LocalCache) getSlot(key string) *CacheSlot {
	hash := lc.hashKey(key)
	return lc.slots[hash&lc.slotMask]
}

// hashKey creates a hash for the key
func (lc *LocalCache) hashKey(key string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(key))
	return h.Sum32()
}

// setInSlot sets an item in a specific slot (assumes slot is locked)
func (lc *LocalCache) setInSlot(slot *CacheSlot, key string, value interface{}, ttl time.Duration, isFailure bool) {
	now := time.Now()
	expiresAt := now.Add(ttl)
	
	// Calculate item size
	size := lc.calculateSize(value)
	
	// Check if item already exists
	if existingItem, exists := slot.items[key]; exists {
		// Update existing item
		existingItem.value = value
		existingItem.expiresAt = expiresAt
		existingItem.isFailure = isFailure
		existingItem.size = size
		slot.lruList.MoveToFront(existingItem.listNode)
		return
	}
	
	// Create new item
	item := &CacheItem{
		key:       key,
		value:     value,
		expiresAt: expiresAt,
		isFailure: isFailure,
		size:      size,
	}
	
	// Add to front of LRU list
	item.listNode = slot.lruList.PushFront(item)
	slot.items[key] = item
	
	// Evict if necessary
	for len(slot.items) > slot.maxSize {
		lc.evictLRU(slot)
	}
}

// evictLRU removes the least recently used item from a slot
func (lc *LocalCache) evictLRU(slot *CacheSlot) {
	if slot.lruList.Len() == 0 {
		return
	}
	
	// Get least recently used item
	tail := slot.lruList.Back()
	if tail == nil {
		return
	}
	
	item := tail.Value.(*CacheItem)
	
	// Remove from both list and map
	slot.lruList.Remove(tail)
	delete(slot.items, item.key)
}

// calculateSize estimates the memory size of a value
func (lc *LocalCache) calculateSize(value interface{}) int {
	if value == nil {
		return 8 // pointer size
	}
	
	// Try to marshal to JSON to get approximate size
	if data, err := json.Marshal(value); err == nil {
		return len(data)
	}
	
	// Default size estimate
	return 64
}

// cleanupLoop runs periodic cleanup of expired items
func (lc *LocalCache) cleanupLoop() {
	ticker := time.NewTicker(lc.config.CleanupInterval)
	defer ticker.Stop()
	
	for range ticker.C {
		lc.cleanup()
	}
}

// cleanup removes expired items from all slots
func (lc *LocalCache) cleanup() {
	now := time.Now()
	
	for _, slot := range lc.slots {
		slot.mutex.Lock()
		
		// Collect expired keys
		expiredKeys := make([]string, 0)
		for key, item := range slot.items {
			if now.After(item.expiresAt) {
				expiredKeys = append(expiredKeys, key)
			}
		}
		
		// Remove expired items
		for _, key := range expiredKeys {
			if item, exists := slot.items[key]; exists {
				slot.lruList.Remove(item.listNode)
				delete(slot.items, key)
			}
		}
		
		slot.mutex.Unlock()
	}
}

// Helper function to find next power of 2
func nextPowerOf2(n uint32) uint32 {
	if n == 0 {
		return 1
	}
	n--
	n |= n >> 1
	n |= n >> 2
	n |= n >> 4
	n |= n >> 8
	n |= n >> 16
	n++
	return n
}

// CacheStats represents cache statistics
type CacheStats struct {
	Hits      int64
	Misses    int64
	ItemCount int64
	HitRatio  float64
}

// SlotStats represents per-slot statistics
type SlotStats struct {
	SlotIndex int
	ItemCount int
	Hits      int64
	Misses    int64
	HitRatio  float64
}

// String returns a string representation of cache stats
func (cs CacheStats) String() string {
	return fmt.Sprintf("Cache Stats: Hits=%d, Misses=%d, Items=%d, HitRatio=%.2f%%", 
		cs.Hits, cs.Misses, cs.ItemCount, cs.HitRatio*100)
}