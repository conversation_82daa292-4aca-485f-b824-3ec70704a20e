package cache

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
)

// Cache key patterns (OpenIM-inspired)
const (
	// Message caching
	MessageCacheKey      = "MSG_CACHE:%s:%d"           // conversationID:sequence
	MessageDocumentKey   = "MSG_DOC:%s"                // documentID
	MessageBatchKey      = "MSG_BATCH:%s:%d:%d"        // conversationID:startSeq:endSeq
	
	// Conversation caching  
	ConversationCacheKey = "CONV_INFO:%s"              // conversationID
	ConversationSeqKey   = "CONV_SEQ:%s"               // conversationID sequence counter
	ConversationListKey  = "CONV_LIST:%s"              // userID conversation list
	
	// User presence and connections
	UserOnlineKey        = "USER_ONLINE:%s"            // userID
	UserConnectionsKey   = "USER_CONN:%s"              // userID connections
	UserSessionKey       = "USER_SESSION:%s:%s"        // userID:deviceID
	
	// Sequence allocation (Redis-based atomic operations)
	SequenceAllocKey     = "SEQ_ALLOC:%s"              // conversationID
	SequenceLockKey      = "SEQ_LOCK:%s"               // conversationID lock
	
	// Read status tracking
	ReadStatusKey        = "READ_STATUS:%s:%s"         // conversationID:userID
	ConversationReadKey  = "CONV_READ:%s"              // conversationID read tracking
	UnreadCountKey       = "UNREAD_COUNT:%s"           // userID unread count
	
	// Search and indexing
	MessageSearchKey     = "MSG_SEARCH:%s:%s"          // conversationID:queryHash
	GlobalSearchKey      = "GLOBAL_SEARCH:%s:%s"       // userID:queryHash
	
	// Statistics and analytics
	ConversationStatsKey = "CONV_STATS:%s"             // conversationID
	UserStatsKey         = "USER_STATS:%s"             // userID
	MessageStatsKey      = "MSG_STATS:%s:%s"           // conversationID:date
	
	// Session and authentication
	SessionTokenKey      = "SESSION:%s"                // sessionToken
	AuthTokenKey         = "AUTH:%s"                   // authToken
	UserDeviceKey        = "USER_DEVICE:%s:%s"         // userID:deviceID
)

// Multi-tier TTL Strategy
const (
	// Tier 1: High-frequency data
	MessageCacheTTL      = 24 * time.Hour              // Messages cached 24h
	SequenceCacheTTL     = 365 * 24 * time.Hour        // Sequences cached 1 year
	MessageDocumentTTL   = 12 * time.Hour              // Message documents 12h
	
	// Tier 2: Medium-frequency data  
	ConversationCacheTTL = 12 * time.Hour              // Conversation info 12h
	UserOnlineTTL        = 30 * time.Minute            // Online status 30m
	ConversationListTTL  = 6 * time.Hour               // User conversation list 6h
	
	// Tier 3: High-volatility data
	ConnectionCacheTTL   = 5 * time.Minute             // Connection state 5m
	LockTTL              = 10 * time.Second            // Sequence locks 10s
	SessionTTL           = 2 * time.Hour               // Session data 2h
	
	// Tier 4: Analytics data
	ReadStatusTTL        = 7 * 24 * time.Hour          // Read status 7 days
	StatsCacheTTL        = 1 * time.Hour               // Statistics 1h
	SearchCacheTTL       = 15 * time.Minute            // Search results 15m
	
	// Tier 5: Authentication data
	AuthTokenTTL         = 24 * time.Hour              // Auth tokens 24h
	UserDeviceTTL        = 30 * 24 * time.Hour         // User device info 30 days
)

// Local cache configuration (LRU with slots)
const (
	LocalCacheSlots      = 500                         // 500 hash slots
	LocalCacheSize       = 20000                       // 20K items per slot
	LocalCacheSuccessTTL = 5 * time.Minute             // Success TTL
	LocalCacheFailureTTL = 5 * time.Second             // Failure TTL
)

// CacheKeyGenerator provides methods to generate standardized cache keys
type CacheKeyGenerator struct{}

// NewCacheKeyGenerator creates a new cache key generator
func NewCacheKeyGenerator() *CacheKeyGenerator {
	return &CacheKeyGenerator{}
}

// Message cache keys
func (g *CacheKeyGenerator) MessageKey(conversationID conversationentities.ConversationID, sequence int64) string {
	return fmt.Sprintf(MessageCacheKey, conversationID.String(), sequence)
}

func (g *CacheKeyGenerator) MessageDocumentKey(documentID string) string {
	return fmt.Sprintf(MessageDocumentKey, documentID)
}

func (g *CacheKeyGenerator) MessageBatchKey(conversationID conversationentities.ConversationID, startSeq, endSeq int64) string {
	return fmt.Sprintf(MessageBatchKey, conversationID.String(), startSeq, endSeq)
}

// Conversation cache keys
func (g *CacheKeyGenerator) ConversationKey(conversationID conversationentities.ConversationID) string {
	return fmt.Sprintf(ConversationCacheKey, conversationID.String())
}

func (g *CacheKeyGenerator) ConversationSequenceKey(conversationID conversationentities.ConversationID) string {
	return fmt.Sprintf(ConversationSeqKey, conversationID.String())
}

func (g *CacheKeyGenerator) ConversationListKey(userID uuid.UUID) string {
	return fmt.Sprintf(ConversationListKey, userID.String())
}

// User presence cache keys
func (g *CacheKeyGenerator) UserOnlineKey(userID uuid.UUID) string {
	return fmt.Sprintf(UserOnlineKey, userID.String())
}

func (g *CacheKeyGenerator) UserConnectionsKey(userID uuid.UUID) string {
	return fmt.Sprintf(UserConnectionsKey, userID.String())
}

func (g *CacheKeyGenerator) UserSessionKey(userID uuid.UUID, deviceID string) string {
	return fmt.Sprintf(UserSessionKey, userID.String(), deviceID)
}

// Sequence allocation cache keys
func (g *CacheKeyGenerator) SequenceAllocKey(conversationID conversationentities.ConversationID) string {
	return fmt.Sprintf(SequenceAllocKey, conversationID.String())
}

func (g *CacheKeyGenerator) SequenceLockKey(conversationID conversationentities.ConversationID) string {
	return fmt.Sprintf(SequenceLockKey, conversationID.String())
}

// Read status cache keys
func (g *CacheKeyGenerator) ReadStatusKey(conversationID conversationentities.ConversationID, userID uuid.UUID) string {
	return fmt.Sprintf(ReadStatusKey, conversationID.String(), userID.String())
}

func (g *CacheKeyGenerator) ConversationReadKey(conversationID conversationentities.ConversationID) string {
	return fmt.Sprintf(ConversationReadKey, conversationID.String())
}

func (g *CacheKeyGenerator) UnreadCountKey(userID uuid.UUID) string {
	return fmt.Sprintf(UnreadCountKey, userID.String())
}

// Search cache keys
func (g *CacheKeyGenerator) MessageSearchKey(conversationID conversationentities.ConversationID, queryHash string) string {
	return fmt.Sprintf(MessageSearchKey, conversationID.String(), queryHash)
}

func (g *CacheKeyGenerator) GlobalSearchKey(userID uuid.UUID, queryHash string) string {
	return fmt.Sprintf(GlobalSearchKey, userID.String(), queryHash)
}

// Statistics cache keys
func (g *CacheKeyGenerator) ConversationStatsKey(conversationID conversationentities.ConversationID) string {
	return fmt.Sprintf(ConversationStatsKey, conversationID.String())
}

func (g *CacheKeyGenerator) UserStatsKey(userID uuid.UUID) string {
	return fmt.Sprintf(UserStatsKey, userID.String())
}

func (g *CacheKeyGenerator) MessageStatsKey(conversationID conversationentities.ConversationID, date string) string {
	return fmt.Sprintf(MessageStatsKey, conversationID.String(), date)
}

// Authentication cache keys
func (g *CacheKeyGenerator) SessionTokenKey(sessionToken string) string {
	return fmt.Sprintf(SessionTokenKey, sessionToken)
}

func (g *CacheKeyGenerator) AuthTokenKey(authToken string) string {
	return fmt.Sprintf(AuthTokenKey, authToken)
}

func (g *CacheKeyGenerator) UserDeviceKey(userID uuid.UUID, deviceID string) string {
	return fmt.Sprintf(UserDeviceKey, userID.String(), deviceID)
}

// Helper methods for getting TTL values
func (g *CacheKeyGenerator) GetTTL(keyType string) time.Duration {
	switch keyType {
	case "message":
		return MessageCacheTTL
	case "message_document":
		return MessageDocumentTTL
	case "conversation":
		return ConversationCacheTTL
	case "sequence":
		return SequenceCacheTTL
	case "user_online":
		return UserOnlineTTL
	case "connection":
		return ConnectionCacheTTL
	case "lock":
		return LockTTL
	case "read_status":
		return ReadStatusTTL
	case "stats":
		return StatsCacheTTL
	case "search":
		return SearchCacheTTL
	case "auth_token":
		return AuthTokenTTL
	case "session":
		return SessionTTL
	case "user_device":
		return UserDeviceTTL
	case "conversation_list":
		return ConversationListTTL
	default:
		return 1 * time.Hour // Default TTL
	}
}

// Tag generation for cache invalidation
func (g *CacheKeyGenerator) GetMessageTags(message *entities.Message) []string {
	tags := []string{
		fmt.Sprintf("conversation:%s", message.ConversationID().String()),
		fmt.Sprintf("user:%s", message.SenderID().String()),
		fmt.Sprintf("message:%s", message.ID().String()),
	}
	
	if message.ThreadID() != nil {
		tags = append(tags, fmt.Sprintf("thread:%s", message.ThreadID().String()))
	}
	
	return tags
}

func (g *CacheKeyGenerator) GetConversationTags(conversation *conversationentities.Conversation) []string {
	tags := []string{
		fmt.Sprintf("conversation:%s", conversation.ID().String()),
	}
	
	for _, participant := range conversation.Participants() {
		tags = append(tags, fmt.Sprintf("user:%s", participant.UserID().String()))
	}
	
	if conversation.TeamID() != nil {
		tags = append(tags, fmt.Sprintf("team:%s", conversation.TeamID().String()))
	}
	
	return tags
}

// Batch key operations
func (g *CacheKeyGenerator) GetUserRelatedKeys(userID uuid.UUID) []string {
	return []string{
		g.UserOnlineKey(userID),
		g.UserConnectionsKey(userID),
		g.ConversationListKey(userID),
		g.UnreadCountKey(userID),
		g.UserStatsKey(userID),
	}
}

func (g *CacheKeyGenerator) GetConversationRelatedKeys(conversationID conversationentities.ConversationID) []string {
	return []string{
		g.ConversationKey(conversationID),
		g.ConversationSequenceKey(conversationID),
		g.ConversationReadKey(conversationID),
		g.ConversationStatsKey(conversationID),
		g.SequenceAllocKey(conversationID),
	}
}