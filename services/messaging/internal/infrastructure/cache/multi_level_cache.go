package cache

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/config"
)

// MultiLevelCache implements a two-tier caching system (Local LRU + Redis)
type MultiLevelCache struct {
	localCache   *LocalCache
	redisClient  *redis.Client
	keyGenerator *CacheKeyGenerator
	config       *config.MessagingServiceConfig
}

// NewMultiLevelCache creates a new multi-level cache
func NewMultiLevelCache(redisClient *redis.Client, config *config.MessagingServiceConfig) *MultiLevelCache {
	localConfig := LocalCacheConfig{
		SlotCount:       LocalCacheSlots,
		SlotSize:        LocalCacheSize,
		SuccessTTL:      LocalCacheSuccessTTL,
		FailureTTL:      LocalCacheFailureTTL,
		CleanupInterval: 1 * time.Minute,
	}
	
	return &MultiLevelCache{
		localCache:   NewLocalCache(localConfig),
		redisClient:  redisClient,
		keyGenerator: NewCacheKeyGenerator(),
		config:       config,
	}
}

// Get retrieves a value from the cache (checks local first, then Redis)
func (mlc *MultiLevelCache) Get(ctx context.Context, key string) (interface{}, bool) {
	// First try local cache
	if value, found := mlc.localCache.Get(key); found {
		return value, true
	}
	
	// Try Redis cache
	data, err := mlc.redisClient.Get(ctx, key).Result()
	if err != nil {
		if err != redis.Nil {
			log.Printf("Redis GET error for key %s: %v", key, err)
		}
		return nil, false
	}
	
	// Deserialize value
	var value interface{}
	if err := json.Unmarshal([]byte(data), &value); err != nil {
		log.Printf("Failed to unmarshal cached value for key %s: %v", key, err)
		return nil, false
	}
	
	// Store in local cache for faster future access
	mlc.localCache.Set(key, value, mlc.keyGenerator.GetTTL("message"))
	
	return value, true
}

// GetTyped retrieves a value and unmarshals it into the provided type
func (mlc *MultiLevelCache) GetTyped(ctx context.Context, key string, dest interface{}) bool {
	value, found := mlc.Get(ctx, key)
	if !found {
		return false
	}
	
	// If value is already the correct type, use it directly
	switch v := value.(type) {
	case string:
		return json.Unmarshal([]byte(v), dest) == nil
	default:
		// Re-marshal and unmarshal to ensure type safety
		data, err := json.Marshal(value)
		if err != nil {
			return false
		}
		return json.Unmarshal(data, dest) == nil
	}
}

// Set stores a value in both local and Redis caches
func (mlc *MultiLevelCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	// Serialize value
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	
	// Store in Redis
	if err := mlc.redisClient.Set(ctx, key, data, ttl).Err(); err != nil {
		log.Printf("Redis SET error for key %s: %v", key, err)
		// Continue to store in local cache even if Redis fails
	}
	
	// Store in local cache
	mlc.localCache.Set(key, value, ttl)
	
	return nil
}

// SetWithTags stores a value with cache tags for invalidation
func (mlc *MultiLevelCache) SetWithTags(ctx context.Context, key string, value interface{}, ttl time.Duration, tags []string) error {
	// Store the main value
	if err := mlc.Set(ctx, key, value, ttl); err != nil {
		return err
	}
	
	// Store tag mappings in Redis for cache invalidation
	for _, tag := range tags {
		tagKey := "tag:" + tag
		err := mlc.redisClient.SAdd(ctx, tagKey, key).Err()
		if err != nil {
			log.Printf("Failed to add tag %s for key %s: %v", tag, key, err)
		}
		// Set expiration for tag key (longer than the actual cache)
		mlc.redisClient.Expire(ctx, tagKey, ttl+time.Hour)
	}
	
	return nil
}

// Delete removes a value from both caches
func (mlc *MultiLevelCache) Delete(ctx context.Context, key string) error {
	// Delete from local cache
	mlc.localCache.Delete(key)
	
	// Delete from Redis
	return mlc.redisClient.Del(ctx, key).Err()
}

// DeleteByPrefix removes all keys with the given prefix
func (mlc *MultiLevelCache) DeleteByPrefix(ctx context.Context, prefix string) error {
	// Delete from local cache
	mlc.localCache.DeleteByPrefix(prefix)
	
	// Delete from Redis using pattern
	keys, err := mlc.redisClient.Keys(ctx, prefix+"*").Result()
	if err != nil {
		return err
	}
	
	if len(keys) > 0 {
		return mlc.redisClient.Del(ctx, keys...).Err()
	}
	
	return nil
}

// DeleteByTags removes all keys associated with the given tags
func (mlc *MultiLevelCache) DeleteByTags(ctx context.Context, tags []string) error {
	allKeys := make(map[string]bool)
	
	// Collect all keys for each tag
	for _, tag := range tags {
		tagKey := "tag:" + tag
		keys, err := mlc.redisClient.SMembers(ctx, tagKey).Result()
		if err != nil {
			log.Printf("Failed to get keys for tag %s: %v", tag, err)
			continue
		}
		
		for _, key := range keys {
			allKeys[key] = true
		}
		
		// Delete the tag set
		mlc.redisClient.Del(ctx, tagKey)
	}
	
	// Delete all collected keys
	keys := make([]string, 0, len(allKeys))
	for key := range allKeys {
		keys = append(keys, key)
		mlc.localCache.Delete(key) // Also delete from local cache
	}
	
	if len(keys) > 0 {
		return mlc.redisClient.Del(ctx, keys...).Err()
	}
	
	return nil
}

// GetOrSet retrieves a value from cache or sets it using the provided function
func (mlc *MultiLevelCache) GetOrSet(ctx context.Context, key string, ttl time.Duration, setter func() (interface{}, error)) (interface{}, error) {
	// Try to get from cache first
	if value, found := mlc.Get(ctx, key); found {
		return value, nil
	}
	
	// Value not in cache, call setter function
	value, err := setter()
	if err != nil {
		// Cache the failure for a short time to prevent repeated calls
		mlc.localCache.SetFailure(key)
		return nil, err
	}
	
	// Store the successful result in cache
	if err := mlc.Set(ctx, key, value, ttl); err != nil {
		log.Printf("Failed to cache value for key %s: %v", key, err)
		// Return the value even if caching fails
	}
	
	return value, nil
}

// GetMulti retrieves multiple values from cache
func (mlc *MultiLevelCache) GetMulti(ctx context.Context, keys []string) map[string]interface{} {
	results := make(map[string]interface{})
	missingKeys := make([]string, 0)
	
	// Check local cache first
	for _, key := range keys {
		if value, found := mlc.localCache.Get(key); found {
			results[key] = value
		} else {
			missingKeys = append(missingKeys, key)
		}
	}
	
	// Get missing keys from Redis
	if len(missingKeys) > 0 {
		values, err := mlc.redisClient.MGet(ctx, missingKeys...).Result()
		if err != nil {
			log.Printf("Redis MGET error: %v", err)
			return results
		}
		
		for i, value := range values {
			if value != nil {
				key := missingKeys[i]
				if strValue, ok := value.(string); ok {
					var deserializedValue interface{}
					if err := json.Unmarshal([]byte(strValue), &deserializedValue); err == nil {
						results[key] = deserializedValue
						// Cache in local for future access
						mlc.localCache.Set(key, deserializedValue, mlc.keyGenerator.GetTTL("message"))
					}
				}
			}
		}
	}
	
	return results
}

// SetMulti stores multiple values in cache
func (mlc *MultiLevelCache) SetMulti(ctx context.Context, items map[string]interface{}, ttl time.Duration) error {
	// Prepare Redis pipeline
	pipe := mlc.redisClient.Pipeline()
	
	for key, value := range items {
		// Serialize value
		data, err := json.Marshal(value)
		if err != nil {
			log.Printf("Failed to marshal value for key %s: %v", key, err)
			continue
		}
		
		// Add to Redis pipeline
		pipe.Set(ctx, key, data, ttl)
		
		// Store in local cache
		mlc.localCache.Set(key, value, ttl)
	}
	
	// Execute Redis pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.Printf("Redis pipeline execution failed: %v", err)
	}
	
	return err
}

// Increment atomically increments a counter in Redis
func (mlc *MultiLevelCache) Increment(ctx context.Context, key string, delta int64, ttl time.Duration) (int64, error) {
	// Delete from local cache as it will be stale
	mlc.localCache.Delete(key)
	
	// Increment in Redis
	result, err := mlc.redisClient.IncrBy(ctx, key, delta).Result()
	if err != nil {
		return 0, err
	}
	
	// Set TTL if this is a new key
	mlc.redisClient.Expire(ctx, key, ttl)
	
	return result, nil
}

// GetStats returns cache statistics
func (mlc *MultiLevelCache) GetStats() (CacheStats, error) {
	localStats := mlc.localCache.GetStats()
	
	// Get Redis info (simplified)
	redisInfo, err := mlc.redisClient.Info(context.Background(), "memory").Result()
	if err != nil {
		log.Printf("Failed to get Redis info: %v", err)
	}
	
	// You could parse Redis info here for more detailed stats
	_ = redisInfo
	
	return localStats, nil
}

// Clear removes all items from local cache and flushes Redis
func (mlc *MultiLevelCache) Clear(ctx context.Context) error {
	// Clear local cache
	mlc.localCache.Clear()
	
	// Clear Redis (be careful with this in production!)
	return mlc.redisClient.FlushDB(ctx).Err()
}

// Exists checks if a key exists in either cache
func (mlc *MultiLevelCache) Exists(ctx context.Context, key string) bool {
	// Check local cache first
	if _, found := mlc.localCache.Get(key); found {
		return true
	}
	
	// Check Redis
	exists, err := mlc.redisClient.Exists(ctx, key).Result()
	if err != nil {
		log.Printf("Redis EXISTS error for key %s: %v", key, err)
		return false
	}
	
	return exists > 0
}

// TTL returns the remaining time to live for a key
func (mlc *MultiLevelCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	return mlc.redisClient.TTL(ctx, key).Result()
}

// RefreshTTL extends the TTL for a key
func (mlc *MultiLevelCache) RefreshTTL(ctx context.Context, key string, ttl time.Duration) error {
	return mlc.redisClient.Expire(ctx, key, ttl).Err()
}