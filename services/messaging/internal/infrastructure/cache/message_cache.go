package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
)

// MessageCache provides caching operations for messages
type MessageCache struct {
	cache        *MultiLevelCache
	keyGenerator *CacheKeyGenerator
}

// CachedMessage represents a message in cache format
type CachedMessage struct {
	ID             string                 `json:"id"`
	ConversationID string                 `json:"conversation_id"`
	SenderID       string                 `json:"sender_id"`
	Content        CachedMessageContent   `json:"content"`
	MessageType    string                 `json:"message_type"`
	Sequence       int64                  `json:"sequence"`
	ThreadID       *string                `json:"thread_id,omitempty"`
	ReplyToID      *string                `json:"reply_to_id,omitempty"`
	Status         string                 `json:"status"`
	Timestamp      int64                  `json:"timestamp"`
	EditedAt       *int64                 `json:"edited_at,omitempty"`
	DeletedAt      *int64                 `json:"deleted_at,omitempty"`
	Metadata       map[string]interface{} `json:"metadata"`
	ReadBy         []CachedReadStatus     `json:"read_by"`
}

// CachedMessageContent represents message content in cache format
type CachedMessageContent struct {
	Text        string                     `json:"text,omitempty"`
	Attachments []CachedMessageAttachment  `json:"attachments,omitempty"`
	Mentions    []string                   `json:"mentions,omitempty"`
	Links       []string                   `json:"links,omitempty"`
}

// CachedMessageAttachment represents attachment in cache format
type CachedMessageAttachment struct {
	ID       string                 `json:"id"`
	Name     string                 `json:"name"`
	URL      string                 `json:"url"`
	MimeType string                 `json:"mime_type"`
	Size     int64                  `json:"size"`
	Metadata map[string]interface{} `json:"metadata"`
}

// CachedReadStatus represents read status in cache format
type CachedReadStatus struct {
	UserID string `json:"user_id"`
	ReadAt int64  `json:"read_at"`
}

// CachedMessageDocument represents a message document in cache
type CachedMessageDocument struct {
	ID             string           `json:"id"`
	ConversationID string           `json:"conversation_id"`
	ShardIndex     int64            `json:"shard_index"`
	Messages       []*CachedMessage `json:"messages"`
	CreatedAt      int64            `json:"created_at"`
	UpdatedAt      int64            `json:"updated_at"`
}

// MessageSearchResult represents cached search results
type MessageSearchResult struct {
	Messages    []*CachedMessage `json:"messages"`
	Total       int64            `json:"total"`
	Query       string           `json:"query"`
	CachedAt    int64            `json:"cached_at"`
	ExpiresAt   int64            `json:"expires_at"`
}

// NewMessageCache creates a new message cache
func NewMessageCache(cache *MultiLevelCache) *MessageCache {
	return &MessageCache{
		cache:        cache,
		keyGenerator: NewCacheKeyGenerator(),
	}
}

// GetMessage retrieves a message from cache
func (mc *MessageCache) GetMessage(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64) (*entities.Message, bool) {
	key := mc.keyGenerator.MessageKey(conversationID, sequence)
	
	var cachedMsg CachedMessage
	if !mc.cache.GetTyped(ctx, key, &cachedMsg) {
		return nil, false
	}
	
	return mc.cachedMessageToEntity(&cachedMsg), true
}

// SetMessage stores a message in cache
func (mc *MessageCache) SetMessage(ctx context.Context, message *entities.Message) error {
	key := mc.keyGenerator.MessageKey(message.ConversationID(), message.Sequence())
	cachedMsg := mc.entityToCachedMessage(message)
	
	tags := mc.keyGenerator.GetMessageTags(message)
	return mc.cache.SetWithTags(ctx, key, cachedMsg, mc.keyGenerator.GetTTL("message"), tags)
}

// GetMessages retrieves multiple messages from cache
func (mc *MessageCache) GetMessages(ctx context.Context, conversationID conversationentities.ConversationID, sequences []int64) ([]*entities.Message, []int64) {
	keys := make([]string, len(sequences))
	for i, seq := range sequences {
		keys[i] = mc.keyGenerator.MessageKey(conversationID, seq)
	}
	
	results := mc.cache.GetMulti(ctx, keys)
	messages := make([]*entities.Message, 0)
	missingSequences := make([]int64, 0)
	
	for i, key := range keys {
		if value, found := results[key]; found {
			// Convert to CachedMessage
			data, err := json.Marshal(value)
			if err == nil {
				var cachedMsg CachedMessage
				if err := json.Unmarshal(data, &cachedMsg); err == nil {
					messages = append(messages, mc.cachedMessageToEntity(&cachedMsg))
					continue
				}
			}
		}
		missingSequences = append(missingSequences, sequences[i])
	}
	
	return messages, missingSequences
}

// SetMessages stores multiple messages in cache
func (mc *MessageCache) SetMessages(ctx context.Context, messages []*entities.Message) error {
	items := make(map[string]interface{})
	
	for _, message := range messages {
		key := mc.keyGenerator.MessageKey(message.ConversationID(), message.Sequence())
		cachedMsg := mc.entityToCachedMessage(message)
		items[key] = cachedMsg
	}
	
	return mc.cache.SetMulti(ctx, items, mc.keyGenerator.GetTTL("message"))
}

// GetMessageDocument retrieves a message document from cache
func (mc *MessageCache) GetMessageDocument(ctx context.Context, documentID string) (*entities.MessageDocument, bool) {
	key := mc.keyGenerator.MessageDocumentKey(documentID)
	
	var cachedDoc CachedMessageDocument
	if !mc.cache.GetTyped(ctx, key, &cachedDoc) {
		return nil, false
	}
	
	return mc.cachedDocumentToEntity(&cachedDoc), true
}

// SetMessageDocument stores a message document in cache
func (mc *MessageCache) SetMessageDocument(ctx context.Context, document *entities.MessageDocument) error {
	documentID := fmt.Sprintf("%s:%d", document.ConversationID().String(), document.ShardIndex())
	key := mc.keyGenerator.MessageDocumentKey(documentID)
	cachedDoc := mc.entityToCachedDocument(document)
	
	return mc.cache.Set(ctx, key, cachedDoc, mc.keyGenerator.GetTTL("message_document"))
}

// GetMessagesBatch retrieves a batch of messages from a conversation
func (mc *MessageCache) GetMessagesBatch(ctx context.Context, conversationID conversationentities.ConversationID, startSeq, endSeq int64) ([]*entities.Message, bool) {
	key := mc.keyGenerator.MessageBatchKey(conversationID, startSeq, endSeq)
	
	var cachedMessages []*CachedMessage
	if !mc.cache.GetTyped(ctx, key, &cachedMessages) {
		return nil, false
	}
	
	messages := make([]*entities.Message, len(cachedMessages))
	for i, cached := range cachedMessages {
		messages[i] = mc.cachedMessageToEntity(cached)
	}
	
	return messages, true
}

// SetMessagesBatch stores a batch of messages
func (mc *MessageCache) SetMessagesBatch(ctx context.Context, conversationID conversationentities.ConversationID, startSeq, endSeq int64, messages []*entities.Message) error {
	key := mc.keyGenerator.MessageBatchKey(conversationID, startSeq, endSeq)
	
	cachedMessages := make([]*CachedMessage, len(messages))
	for i, msg := range messages {
		cachedMessages[i] = mc.entityToCachedMessage(msg)
	}
	
	return mc.cache.Set(ctx, key, cachedMessages, mc.keyGenerator.GetTTL("message"))
}

// GetSearchResults retrieves cached search results
func (mc *MessageCache) GetSearchResults(ctx context.Context, conversationID conversationentities.ConversationID, queryHash string) (*MessageSearchResult, bool) {
	key := mc.keyGenerator.MessageSearchKey(conversationID, queryHash)
	
	var searchResult MessageSearchResult
	if !mc.cache.GetTyped(ctx, key, &searchResult) {
		return nil, false
	}
	
	// Check if results have expired
	if time.Now().UnixMilli() > searchResult.ExpiresAt {
		mc.cache.Delete(ctx, key)
		return nil, false
	}
	
	return &searchResult, true
}

// SetSearchResults stores search results in cache
func (mc *MessageCache) SetSearchResults(ctx context.Context, conversationID conversationentities.ConversationID, queryHash string, messages []*entities.Message, total int64, query string) error {
	key := mc.keyGenerator.MessageSearchKey(conversationID, queryHash)
	
	cachedMessages := make([]*CachedMessage, len(messages))
	for i, msg := range messages {
		cachedMessages[i] = mc.entityToCachedMessage(msg)
	}
	
	now := time.Now()
	searchResult := MessageSearchResult{
		Messages:  cachedMessages,
		Total:     total,
		Query:     query,
		CachedAt:  now.UnixMilli(),
		ExpiresAt: now.Add(mc.keyGenerator.GetTTL("search")).UnixMilli(),
	}
	
	return mc.cache.Set(ctx, key, searchResult, mc.keyGenerator.GetTTL("search"))
}

// InvalidateMessage removes a message from cache
func (mc *MessageCache) InvalidateMessage(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64) error {
	key := mc.keyGenerator.MessageKey(conversationID, sequence)
	return mc.cache.Delete(ctx, key)
}

// InvalidateConversationMessages removes all cached messages for a conversation
func (mc *MessageCache) InvalidateConversationMessages(ctx context.Context, conversationID conversationentities.ConversationID) error {
	tag := fmt.Sprintf("conversation:%s", conversationID.String())
	return mc.cache.DeleteByTags(ctx, []string{tag})
}

// InvalidateUserMessages removes all cached messages for a user
func (mc *MessageCache) InvalidateUserMessages(ctx context.Context, userID uuid.UUID) error {
	tag := fmt.Sprintf("user:%s", userID.String())
	return mc.cache.DeleteByTags(ctx, []string{tag})
}

// InvalidateSearchResults removes cached search results
func (mc *MessageCache) InvalidateSearchResults(ctx context.Context, conversationID conversationentities.ConversationID) error {
	prefix := fmt.Sprintf("MSG_SEARCH:%s:", conversationID.String())
	return mc.cache.DeleteByPrefix(ctx, prefix)
}

// GetUnreadCount retrieves cached unread count for a user
func (mc *MessageCache) GetUnreadCount(ctx context.Context, userID uuid.UUID) (int64, bool) {
	key := mc.keyGenerator.UnreadCountKey(userID)
	
	value, found := mc.cache.Get(ctx, key)
	if !found {
		return 0, false
	}
	
	if count, ok := value.(float64); ok {
		return int64(count), true
	}
	
	return 0, false
}

// SetUnreadCount stores unread count for a user
func (mc *MessageCache) SetUnreadCount(ctx context.Context, userID uuid.UUID, count int64) error {
	key := mc.keyGenerator.UnreadCountKey(userID)
	return mc.cache.Set(ctx, key, count, mc.keyGenerator.GetTTL("read_status"))
}

// IncrementUnreadCount atomically increments unread count
func (mc *MessageCache) IncrementUnreadCount(ctx context.Context, userID uuid.UUID, delta int64) (int64, error) {
	key := mc.keyGenerator.UnreadCountKey(userID)
	return mc.cache.Increment(ctx, key, delta, mc.keyGenerator.GetTTL("read_status"))
}

// Helper methods for conversion

// entityToCachedMessage converts a domain message entity to cached format
func (mc *MessageCache) entityToCachedMessage(message *entities.Message) *CachedMessage {
	cached := &CachedMessage{
		ID:             message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		Content:        mc.entityToCachedContent(message.Content()),
		MessageType:    string(message.MessageType()),
		Sequence:       message.Sequence(),
		Status:         string(message.Status()),
		Timestamp:      message.Timestamp().UnixMilli(),
		Metadata:       message.Metadata(),
		ReadBy:         mc.entityToCachedReadStatus(message.ReadBy()),
	}
	
	if message.ThreadID() != nil {
		threadID := message.ThreadID().String()
		cached.ThreadID = &threadID
	}
	
	if message.ReplyToID() != nil {
		replyToID := message.ReplyToID().String()
		cached.ReplyToID = &replyToID
	}
	
	if message.EditedAt() != nil {
		editedAt := message.EditedAt().UnixMilli()
		cached.EditedAt = &editedAt
	}
	
	if message.DeletedAt() != nil {
		deletedAt := message.DeletedAt().UnixMilli()
		cached.DeletedAt = &deletedAt
	}
	
	return cached
}

// cachedMessageToEntity converts cached format to domain message entity
func (mc *MessageCache) cachedMessageToEntity(cached *CachedMessage) *entities.Message {
	conversationID := conversationentities.ConversationID(uuid.MustParse(cached.ConversationID))
	senderID := uuid.MustParse(cached.SenderID)
	content := mc.cachedContentToEntity(cached.Content)
	readBy := mc.cachedReadStatusToEntity(cached.ReadBy)
	
	var threadID *entities.MessageID
	if cached.ThreadID != nil {
		tid := entities.MessageID(uuid.MustParse(*cached.ThreadID))
		threadID = &tid
	}
	
	var replyToID *entities.MessageID
	if cached.ReplyToID != nil {
		rid := entities.MessageID(uuid.MustParse(*cached.ReplyToID))
		replyToID = &rid
	}
	
	var editedAt *time.Time
	if cached.EditedAt != nil {
		t := time.UnixMilli(*cached.EditedAt)
		editedAt = &t
	}
	
	var deletedAt *time.Time
	if cached.DeletedAt != nil {
		t := time.UnixMilli(*cached.DeletedAt)
		deletedAt = &t
	}
	
	return entities.ReconstructMessage(
		entities.MessageID(uuid.MustParse(cached.ID)),
		conversationID,
		senderID,
		content,
		entities.MessageType(cached.MessageType),
		cached.Sequence,
		threadID,
		replyToID,
		entities.MessageStatus(cached.Status),
		time.UnixMilli(cached.Timestamp),
		editedAt,
		deletedAt,
		cached.Metadata,
		readBy,
	)
}

// entityToCachedContent converts message content to cached format
func (mc *MessageCache) entityToCachedContent(content valueobjects.MessageContent) CachedMessageContent {
	attachments := make([]CachedMessageAttachment, len(content.Attachments()))
	for i, att := range content.Attachments() {
		attachments[i] = CachedMessageAttachment{
			ID:       att.ID(),
			Name:     att.Name(),
			URL:      att.URL(),
			MimeType: att.MimeType(),
			Size:     att.Size(),
			Metadata: att.Metadata(),
		}
	}
	
	return CachedMessageContent{
		Text:        content.Text(),
		Attachments: attachments,
		Mentions:    content.Mentions(),
		Links:       content.Links(),
	}
}

// cachedContentToEntity converts cached content to domain value object
func (mc *MessageCache) cachedContentToEntity(cached CachedMessageContent) valueobjects.MessageContent {
	attachments := make([]valueobjects.MessageAttachment, len(cached.Attachments))
	for i, att := range cached.Attachments {
		attachment := valueobjects.NewMessageAttachment(
			att.ID,
			att.Name,
			att.URL,
			att.MimeType,
			att.Size,
		)
		for k, v := range att.Metadata {
			attachment.SetMetadata(k, v)
		}
		attachments[i] = attachment
	}
	
	var content valueobjects.MessageContent
	var err error
	
	if len(attachments) > 0 {
		content, err = valueobjects.NewAttachmentMessageContent(attachments, cached.Text)
	} else {
		content, err = valueobjects.NewTextMessageContent(cached.Text)
	}
	
	if err != nil {
		// Fallback to system message content
		content = valueobjects.NewSystemMessageContent(cached.Text)
	}
	
	for _, mention := range cached.Mentions {
		content.AddMention(mention)
	}
	
	for _, link := range cached.Links {
		content.AddLink(link)
	}
	
	return content
}

// entityToCachedReadStatus converts read status to cached format
func (mc *MessageCache) entityToCachedReadStatus(readStatus []entities.ReadStatus) []CachedReadStatus {
	cached := make([]CachedReadStatus, len(readStatus))
	for i, rs := range readStatus {
		cached[i] = CachedReadStatus{
			UserID: rs.UserID.String(),
			ReadAt: rs.ReadAt.UnixMilli(),
		}
	}
	return cached
}

// cachedReadStatusToEntity converts cached read status to domain format
func (mc *MessageCache) cachedReadStatusToEntity(cached []CachedReadStatus) []entities.ReadStatus {
	readStatus := make([]entities.ReadStatus, len(cached))
	for i, rs := range cached {
		readStatus[i] = entities.ReadStatus{
			UserID: uuid.MustParse(rs.UserID),
			ReadAt: time.UnixMilli(rs.ReadAt),
		}
	}
	return readStatus
}

// entityToCachedDocument converts message document to cached format
func (mc *MessageCache) entityToCachedDocument(document *entities.MessageDocument) *CachedMessageDocument {
	messages := make([]*CachedMessage, len(document.Messages()))
	for i, msgInfo := range document.Messages() {
		if msgInfo != nil && msgInfo.Message() != nil {
			messages[i] = mc.entityToCachedMessage(msgInfo.Message())
		}
	}
	
	return &CachedMessageDocument{
		ID:             fmt.Sprintf("%s:%d", document.ConversationID().String(), document.ShardIndex()),
		ConversationID: document.ConversationID().String(),
		ShardIndex:     document.ShardIndex(),
		Messages:       messages,
		CreatedAt:      document.CreatedAt().UnixMilli(),
		UpdatedAt:      document.UpdatedAt().UnixMilli(),
	}
}

// cachedDocumentToEntity converts cached document to domain entity
func (mc *MessageCache) cachedDocumentToEntity(cached *CachedMessageDocument) *entities.MessageDocument {
	conversationID := conversationentities.ConversationID(uuid.MustParse(cached.ConversationID))
	document := entities.NewMessageDocument(conversationID, cached.ShardIndex)
	
	for _, cachedMsg := range cached.Messages {
		if cachedMsg != nil {
			message := mc.cachedMessageToEntity(cachedMsg)
			document.AddMessage(message)
		}
	}
	
	return document
}