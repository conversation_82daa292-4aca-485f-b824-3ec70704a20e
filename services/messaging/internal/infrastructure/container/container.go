package container

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/swork-team/platform/pkg/database"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	conversationrepositories "github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	conversationservices "github.com/swork-team/platform/services/messaging/internal/domain/conversation/services"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messagerepositories "github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
	messageservices "github.com/swork-team/platform/services/messaging/internal/domain/message/services"
	realtimerepositories "github.com/swork-team/platform/services/messaging/internal/domain/realtime/repositories"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/cache"
	"github.com/swork-team/platform/pkg/config"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/events"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/health"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/persistence/mongodb"
	postgrespersistence "github.com/swork-team/platform/services/messaging/internal/infrastructure/persistence/postgres"
	redispersistence "github.com/swork-team/platform/services/messaging/internal/infrastructure/persistence/redis"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/queue"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/websocket"
)

type Container struct {
	config *config.MessagingServiceConfig
	
	// Database connections
	postgresDB *gorm.DB
	mongoDB    *mongo.Database
	redisClient *redis.Client
	
	// Repositories
	messageRepository      messagerepositories.MessageRepository
	conversationRepository conversationrepositories.ConversationRepository
	connectionRepository   realtimerepositories.ConnectionRepository
	
	// Services
	messageService      messageservices.MessageService
	conversationService conversationservices.ConversationService
	sequenceManager     *redispersistence.SequenceManager
	
	// Infrastructure
	messagingHub     *websocket.MessagingHub
	multiLevelCache  *cache.MultiLevelCache
	messageCache     *cache.MessageCache
	memoryQueue      *queue.MemoryQueue
	jobProcessor     *queue.MessageJobProcessor
	eventPublisher   messageservices.EventPublisher
	healthChecker    *health.DatabaseHealthChecker
}

func NewContainer(config *config.MessagingServiceConfig) *Container {
	return &Container{
		config: config,
	}
}

func (c *Container) Initialize() error {
	log.Println("Initializing messaging service container...")
	
	if err := c.initializeDatabases(); err != nil {
		return fmt.Errorf("failed to initialize databases: %w", err)
	}
	
	if err := c.initializeRepositories(); err != nil {
		return fmt.Errorf("failed to initialize repositories: %w", err)
	}
	
	if err := c.initializeServices(); err != nil {
		return fmt.Errorf("failed to initialize services: %w", err)
	}
	
	if err := c.initializeInfrastructure(); err != nil {
		return fmt.Errorf("failed to initialize infrastructure: %w", err)
	}
	
	log.Println("Messaging service container initialized successfully")
	return nil
}

func (c *Container) initializeDatabases() error {
	// Initialize PostgreSQL for conversations
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		c.config.Database.Host,
		c.config.Database.Username,
		c.config.Database.Password,
		c.config.Database.Database,
		c.config.Database.Port,
		c.config.Database.SSLMode,
	)
	
	var err error
	c.postgresDB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}
	
	// Initialize MongoDB for messages
	mongoClient, err := mongo.Connect(context.Background(), options.Client().ApplyURI(c.config.MongoDB.URI))
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %w", err)
	}
	
	c.mongoDB = mongoClient.Database(c.config.MongoDB.Database)
	
	// Test MongoDB connection
	if err := mongoClient.Ping(context.Background(), nil); err != nil {
		return fmt.Errorf("failed to ping MongoDB: %w", err)
	}
	
	// Initialize Redis
	c.redisClient = database.NewRedisClient(&c.config.Redis)
	
	// Test Redis connection
	if err := c.redisClient.Ping(context.Background()).Err(); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}
	
	log.Println("Database connections established")
	return nil
}

func (c *Container) initializeRepositories() error {
	// Initialize message repository with MongoDB
	c.messageRepository = mongodb.NewMongoMessageRepository(
		c.mongoDB.Client(),
		c.mongoDB.Name(),
		c.config,
	)
	
	// Initialize conversation repository with PostgreSQL
	c.conversationRepository = postgrespersistence.NewPostgresConversationRepository(c.postgresDB)
	
	// Initialize connection repository with Redis
	c.connectionRepository = redispersistence.NewRedisConnectionRepository(c.redisClient, c.config)
	
	log.Println("Repositories initialized")
	return nil
}

func (c *Container) initializeServices() error {
	// Initialize sequence manager
	c.sequenceManager = redispersistence.NewSequenceManager(c.redisClient, c.config)
	
	// Initialize conversation service first
	teamClient := &NoOpTeamClient{}
	conversationEventPublisher := &NoOpConversationEventPublisher{}
	c.conversationService = conversationservices.NewConversationService(
		c.conversationRepository,
		teamClient,
		conversationEventPublisher,
	)
	
	// Initialize message service (initially with no-op publisher)
	noOpPublisher := &NoOpEventPublisher{}
	c.messageService = messageservices.NewMessageService(
		c.messageRepository,
		c.conversationRepository,
		c.sequenceManager,
		noOpPublisher,
	)
	
	log.Println("Services initialized")
	return nil
}

func (c *Container) initializeInfrastructure() error {
	// Initialize multi-level cache
	c.multiLevelCache = cache.NewMultiLevelCache(c.redisClient, c.config)
	c.messageCache = cache.NewMessageCache(c.multiLevelCache)

	// Initialize messaging hub
	c.messagingHub = websocket.NewMessagingHub(c.config)

	// Initialize job processor
	c.jobProcessor = queue.NewMessageJobProcessor(
		c.messageRepository,
		c.conversationRepository,
		c.messagingHub,
		nil, // Will be set after event publisher is created
	)

	// Initialize memory queue system
	queueConfig := queue.QueueConfig{
		BufferSize:        c.config.Queue.BatchSize,
		MaxWorkers:        c.config.Queue.WorkerCount,
		PriorityQueues:    c.config.Queue.PriorityQueues,
		WorkerIdleTimeout: time.Duration(c.config.Queue.ProcessingTimeout) * time.Second,
		JobTimeout:        time.Duration(c.config.Queue.ProcessingTimeout) * time.Second,
		RetryDelay:        time.Second,
		EnableMetrics:     c.config.Queue.EnableMetrics,
	}
	c.memoryQueue = queue.NewMemoryQueue(queueConfig, c.jobProcessor)

	// Initialize message handler and connect to hub
	messageHandler := websocket.NewMessageHandler(c.messageService, c.conversationService, c.messagingHub)
	c.messagingHub.SetMessageHandler(messageHandler)

	// Initialize full event publisher with messaging hub, queue, and conversation repository
	c.eventPublisher = events.NewEventPublisher(c.messagingHub, c.memoryQueue, c.conversationRepository, events.DefaultEventConfig())

	// Recreate message service with WebSocket event publisher
	c.messageService = messageservices.NewMessageService(
		c.messageRepository,
		c.conversationRepository,
		c.sequenceManager,
		c.eventPublisher,
	)

	// Update message handler with new service
	messageHandler = websocket.NewMessageHandler(c.messageService, c.conversationService, c.messagingHub)
	c.messagingHub.SetMessageHandler(messageHandler)

	// Initialize health checker
	if err := c.initializeHealthChecker(); err != nil {
		return fmt.Errorf("failed to initialize health checker: %w", err)
	}

	// Start infrastructure components
	c.memoryQueue.Start()
	c.messagingHub.Start()

	log.Println("Infrastructure initialized")
	return nil
}

func (c *Container) initializeHealthChecker() error {
	// Get raw database connections for health checking
	rawDB, err := c.postgresDB.DB()
	if err != nil {
		return fmt.Errorf("failed to get raw PostgreSQL connection: %w", err)
	}

	// Create health checker with optimized intervals for messaging service
	c.healthChecker = health.NewDatabaseHealthChecker(
		rawDB,
		c.mongoDB.Client(),
		c.mongoDB,
		c.redisClient,
		1*time.Minute, // Check interval
		5*time.Second,  // Timeout per check
		1*time.Second,  // Degraded threshold
	)

	// Start health checking
	if err := c.healthChecker.Start(); err != nil {
		return fmt.Errorf("failed to start health checker: %w", err)
	}

	log.Println("Health checker initialized and started")
	return nil
}

func (c *Container) Close() {
	log.Println("Closing messaging service container...")
	
	if c.healthChecker != nil {
		c.healthChecker.Stop()
	}
	
	if c.memoryQueue != nil {
		c.memoryQueue.Stop()
	}
	
	if c.messagingHub != nil {
		c.messagingHub.Stop()
	}
	
	if c.redisClient != nil {
		c.redisClient.Close()
	}
	
	if c.mongoDB != nil {
		c.mongoDB.Client().Disconnect(context.Background())
	}
	
	if c.postgresDB != nil {
		if db, err := c.postgresDB.DB(); err == nil {
			db.Close()
		}
	}
	
	log.Println("Container closed")
}

// Getters for dependency injection
func (c *Container) Config() *config.MessagingServiceConfig {
	return c.config
}

func (c *Container) PostgresDB() *gorm.DB {
	return c.postgresDB
}

func (c *Container) MongoDB() *mongo.Database {
	return c.mongoDB
}

func (c *Container) RedisClient() *redis.Client {
	return c.redisClient
}

func (c *Container) MessageRepository() messagerepositories.MessageRepository {
	return c.messageRepository
}

func (c *Container) ConversationRepository() conversationrepositories.ConversationRepository {
	return c.conversationRepository
}

func (c *Container) MessageService() messageservices.MessageService {
	return c.messageService
}

func (c *Container) SequenceManager() *redispersistence.SequenceManager {
	return c.sequenceManager
}

func (c *Container) ConversationService() conversationservices.ConversationService {
	return c.conversationService
}

func (c *Container) GetMessageService() messageservices.MessageService {
	return c.messageService
}

func (c *Container) GetConversationService() conversationservices.ConversationService {
	return c.conversationService
}

func (c *Container) GetMessagingHub() *websocket.MessagingHub {
	return c.messagingHub
}

func (c *Container) MessagingHub() *websocket.MessagingHub {
	return c.messagingHub
}

func (c *Container) GetConfig() *config.MessagingServiceConfig {
	return c.config
}

func (c *Container) GetMultiLevelCache() *cache.MultiLevelCache {
	return c.multiLevelCache
}

func (c *Container) GetMessageCache() *cache.MessageCache {
	return c.messageCache
}

func (c *Container) ConnectionRepository() realtimerepositories.ConnectionRepository {
	return c.connectionRepository
}

func (c *Container) GetConnectionRepository() realtimerepositories.ConnectionRepository {
	return c.connectionRepository
}

func (c *Container) MemoryQueue() *queue.MemoryQueue {
	return c.memoryQueue
}

func (c *Container) GetMemoryQueue() *queue.MemoryQueue {
	return c.memoryQueue
}

func (c *Container) JobProcessor() *queue.MessageJobProcessor {
	return c.jobProcessor
}

func (c *Container) GetJobProcessor() *queue.MessageJobProcessor {
	return c.jobProcessor
}

func (c *Container) EventPublisher() messageservices.EventPublisher {
	return c.eventPublisher
}

func (c *Container) GetEventPublisher() messageservices.EventPublisher {
	return c.eventPublisher
}

func (c *Container) HealthChecker() *health.DatabaseHealthChecker {
	return c.healthChecker
}

func (c *Container) GetHealthChecker() *health.DatabaseHealthChecker {
	return c.healthChecker
}


// Placeholder implementation for team client
type NoOpEventPublisher struct{}

func (p *NoOpEventPublisher) PublishMessageSent(ctx context.Context, message *messageentities.Message, conversationID conversationentities.ConversationID) error {
	return nil
}

func (p *NoOpEventPublisher) PublishMessageEdited(ctx context.Context, message *messageentities.Message) error {
	return nil
}

func (p *NoOpEventPublisher) PublishMessageDeleted(ctx context.Context, messageID messageentities.MessageID, userID uuid.UUID) error {
	return nil
}

func (p *NoOpEventPublisher) PublishReadStatusUpdated(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64) error {
	return nil
}

type NoOpTeamClient struct{}

func (t *NoOpTeamClient) ValidateTeamAccess(ctx context.Context, teamID uuid.UUID, userID uuid.UUID) error {
	// TODO: Integrate with actual team service
	return nil
}

func (t *NoOpTeamClient) GetTeamMembers(ctx context.Context, teamID uuid.UUID) ([]uuid.UUID, error) {
	// TODO: Integrate with actual team service
	return []uuid.UUID{}, nil
}

type NoOpConversationEventPublisher struct{}

func (p *NoOpConversationEventPublisher) PublishConversationCreated(ctx context.Context, conversation *conversationentities.Conversation) error {
	// TODO: Integrate with WebSocket hub for real-time conversation events
	return nil
}

func (p *NoOpConversationEventPublisher) PublishConversationUpdated(ctx context.Context, conversation *conversationentities.Conversation) error {
	// TODO: Integrate with WebSocket hub for real-time conversation events
	return nil
}

func (p *NoOpConversationEventPublisher) PublishParticipantAdded(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID) error {
	// TODO: Integrate with WebSocket hub for real-time participant events
	return nil
}

func (p *NoOpConversationEventPublisher) PublishParticipantRemoved(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID) error {
	// TODO: Integrate with WebSocket hub for real-time participant events
	return nil
}