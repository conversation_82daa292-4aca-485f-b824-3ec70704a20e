package utils

import (
	"errors"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetUserID extracts user ID from the gin context and handles different formats
func GetUserID(c *gin.Context) (string, error) {
	userIDStr := c.GetString("user_id")
	if userIDStr == "" {
		return "", errors.New("user not authenticated")
	}
	return userIDStr, nil
}

// GetUserIDAsUUID extracts user ID from context and converts to UUID
// This handles both UUID strings and other formats by creating a deterministic UUID
func GetUserIDAsUUID(c *gin.Context) (uuid.UUID, error) {
	userIDStr, err := GetUserID(c)
	if err != nil {
		return uuid.UUID{}, err
	}
	
	// Try to parse as UUID first
	if id, err := uuid.Parse(userIDStr); err == nil {
		return id, nil
	}
	
	// If not a UUID, create a deterministic UUID from the string
	// This allows different user ID formats to work with UUID-based domain entities
	return GenerateUUIDFromString(userIDStr), nil
}

// GenerateUUIDFromString creates a deterministic UUID from a string
func GenerateUUIDFromString(str string) uuid.UUID {
	// Pad or truncate to 36 characters to create a valid UUID format
	normalized := normalizeToUUID(str)
	
	// Try to parse the normalized string as UUID
	if id, err := uuid.Parse(normalized); err == nil {
		return id
	}
	
	// If all else fails, create a UUID from the first 16 bytes of the string
	bytes := []byte(str)
	if len(bytes) < 16 {
		// Pad with zeros
		padded := make([]byte, 16)
		copy(padded, bytes)
		bytes = padded
	}
	
	return uuid.UUID(bytes[:16])
}

// normalizeToUUID converts a string to UUID format
func normalizeToUUID(str string) string {
	// Remove any non-alphanumeric characters
	clean := strings.ReplaceAll(str, "-", "")
	clean = strings.ReplaceAll(clean, " ", "")
	
	if len(clean) == 32 {
		// Already in hex format, add dashes
		return clean[:8] + "-" + clean[8:12] + "-" + clean[12:16] + "-" + clean[16:20] + "-" + clean[20:32]
	}
	
	if len(clean) == 36 {
		return clean
	}
	
	// For shorter strings (like 12-char MongoDB ObjectIds), pad with zeros
	if len(clean) < 32 {
		clean = clean + strings.Repeat("0", 32-len(clean))
	}
	
	// Take first 32 characters if longer
	if len(clean) > 32 {
		clean = clean[:32]
	}
	
	// Format as UUID
	return clean[:8] + "-" + clean[8:12] + "-" + clean[12:16] + "-" + clean[16:20] + "-" + clean[20:32]
}

// GetUserRole extracts user role from context
func GetUserRole(c *gin.Context) string {
	return c.GetString("role")
}

// GetUserEmail extracts user email from context
func GetUserEmail(c *gin.Context) string {
	return c.GetString("email")
}

// IsAdmin checks if the user has admin role
func IsAdmin(c *gin.Context) bool {
	role := GetUserRole(c)
	return role == "admin" || role == "moderator"
}

// ErrorResponse sends a standardized error response
func ErrorResponse(c *gin.Context, statusCode int, message string, err error) {
	response := gin.H{
		"success": false,
		"message": message,
	}
	
	if err != nil {
		response["error"] = err.Error()
	}
	
	c.JSON(statusCode, response)
}

// SuccessResponse sends a standardized success response
func SuccessResponse(c *gin.Context, data interface{}) {
	response := gin.H{
		"success": true,
		"data":    data,
	}
	
	c.JSON(http.StatusOK, response)
}