package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/container"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/http/handlers"
	localmiddleware "github.com/swork-team/platform/services/messaging/internal/infrastructure/http/middleware"
)

func SetupRoutes(router *gin.Engine, container *container.Container) {
	
	// API version group
	v1 := router.Group("/api/v1")
	
	// Public routes (no authentication required)
	publicGroup := v1.Group("/public")
	{
		publicGroup.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "healthy"})
		})
	}
	
	// Protected routes (authentication required) - using unified middleware
	protectedGroup := v1.Group("/")
	protectedGroup.Use(middleware.AuthMiddleware())
	
	// Message routes
	messageHandler := handlers.NewMessageHandler(container)
	messageGroup := protectedGroup.Group("/messages")
	{
		messageGroup.POST("/", messageHandler.SendMessage)
		messageGroup.GET("/:messageId", messageHandler.GetMessage)
		messageGroup.PUT("/:messageId", messageHandler.EditMessage)
		messageGroup.DELETE("/:messageId", messageHandler.DeleteMessage)
		messageGroup.POST("/:messageId/read", messageHandler.MarkAsRead)
	}
	
	// Conversation routes
	conversationHandler := handlers.NewConversationHandler(container)
	conversationGroup := protectedGroup.Group("/conversations")
	{
		conversationGroup.POST("/direct", conversationHandler.CreateDirectConversation)
		conversationGroup.POST("/group", conversationHandler.CreateGroupConversation)
		conversationGroup.POST("/team-channel", conversationHandler.CreateTeamChannel)
		conversationGroup.GET("/:conversationId", conversationHandler.GetConversation)
		conversationGroup.PUT("/:conversationId", conversationHandler.UpdateConversation)
		conversationGroup.GET("/", conversationHandler.GetUserConversations)
		conversationGroup.GET("/unread", conversationHandler.GetUnreadConversations)
	}
	
	// WebSocket route (special authentication for upgrade)
	wsHandler := handlers.NewWebSocketHandler(container)
	wsGroup := v1.Group("/ws")
	wsGroup.Use(middleware.WebSocketAuthMiddleware())
	{
		wsGroup.GET("/", wsHandler.HandleWebSocket)
	}
	
	// Real-time routes (admin only for some operations)
	realtimeGroup := protectedGroup.Group("/realtime")
	{
		realtimeGroup.GET("/users/:userId/status", wsHandler.GetConnectionStatus)
		
		// Admin-only routes
		adminGroup := realtimeGroup.Group("/")
		adminGroup.Use(localmiddleware.RequireAnyRole([]string{"admin", "moderator"}))
		{
			adminGroup.POST("/users/:userId/kick", wsHandler.KickUser)
			adminGroup.POST("/users/:userId/notify", wsHandler.SendNotification)
			adminGroup.GET("/stats", wsHandler.GetHubStats)
		}
	}
	
	// Search routes
	searchGroup := protectedGroup.Group("/search")
	{
		searchGroup.GET("/messages", messageHandler.SearchMessages)
	}
}