package handlers

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/swork-team/platform/services/messaging/internal/domain/realtime/entities"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/container"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/http/utils"
	websocketinfra "github.com/swork-team/platform/services/messaging/internal/infrastructure/websocket"
)

type WebSocketHandler struct {
	container *container.Container
	upgrader  websocket.Upgrader
}

func NewWebSocketHandler(container *container.Container) *WebSocketHandler {
	return &WebSocketHandler{
		container: container,
		upgrader: websocket.Upgrader{
			ReadBufferSize:  container.Config().WebSocket.BufferSize,
			WriteBufferSize: container.Config().WebSocket.BufferSize,
			CheckOrigin: func(r *http.Request) bool {
				// TODO: Implement proper origin validation for production
				return true
			},
			EnableCompression: container.Config().WebSocket.EnableCompression,
		},
	}
}

func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Get query parameters
	deviceID := c.Query("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device ID required"})
		return
	}
	
	platformStr := c.DefaultQuery("platform", "1") // Default to web
	platform, err := strconv.ParseInt(platformStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid platform"})
		return
	}
	
	// Get authentication token from header or query parameter
	token := c.GetHeader("Authorization")
	if token == "" {
		token = c.Query("token")
	}
	
	if token == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication token required"})
		return
	}
	
	// Remove "Bearer " prefix if present
	if len(token) > 7 && token[:7] == "Bearer " {
		token = token[7:]
	}
	
	// TODO: Validate token with auth service
	// For now, we'll use a temporary user ID from query parameter
	// In production, this would come from token validation
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User authentication required"})
		return
	}
	
	// TODO: Validate user permissions and access
	
	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	
	// Get configuration
	config := h.container.GetConfig()
	
	// Create connection entity
	connection := entities.NewConnection(
		userID,
		deviceID,
		entities.PlatformType(platform),
		conn,
		config.WebSocket.ChannelBufferSize,
	)
	
	// Get messaging hub
	hub := h.container.GetMessagingHub()
	
	// Create and start client
	client := websocketinfra.NewClient(connection, hub, config)
	
	// Register connection with hub
	if err := hub.RegisterConnection(connection); err != nil {
		log.Printf("Failed to register connection: %v", err)
		connection.Close()
		return
	}
	
	// Start client handlers
	client.Start()
	
	// Wait for client to finish (this will block until connection closes)
	<-connection.Context().Done()
	
	// Unregister connection
	hub.UnregisterConnection(connection)
	
	log.Printf("WebSocket connection closed for user %s, device %s", userID, deviceID)
}

func (h *WebSocketHandler) KickUser(c *gin.Context) {
	userIDStr := c.Param("userId")
	if userIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID required"})
		return
	}
	
	userID := utils.GenerateUUIDFromString(userIDStr)
	
	var request struct {
		DeviceID string `json:"device_id,omitempty"`
		Platform int32  `json:"platform,omitempty"`
		Reason   string `json:"reason"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	
	if request.Reason == "" {
		request.Reason = "Kicked by administrator"
	}
	
	hub := h.container.GetMessagingHub()
	hub.KickUser(userID, request.DeviceID, entities.PlatformType(request.Platform), request.Reason)
	
	c.JSON(http.StatusOK, gin.H{
		"message": "User kick initiated",
		"user_id": userID.String(),
	})
}

func (h *WebSocketHandler) SendNotification(c *gin.Context) {
	userIDStr := c.Param("userId")
	if userIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID required"})
		return
	}
	
	userID := utils.GenerateUUIDFromString(userIDStr)
	
	var notification map[string]interface{}
	if err := c.ShouldBindJSON(&notification); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification data"})
		return
	}
	
	hub := h.container.GetMessagingHub()
	hub.SendToUser(userID, notification, websocketinfra.PriorityNormal)
	
	c.JSON(http.StatusOK, gin.H{
		"message": "Notification sent",
		"user_id": userID.String(),
	})
}

func (h *WebSocketHandler) GetConnectionStatus(c *gin.Context) {
	userIDStr := c.Param("userId")
	if userIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID required"})
		return
	}
	
	userID := utils.GenerateUUIDFromString(userIDStr)
	
	hub := h.container.GetMessagingHub()
	if hub == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"message": "WebSocket hub not initialized",
			"error":   "service_unavailable",
		})
		return
	}
	
	connections, isOnline := hub.GetUserConnections(userID)
	
	response := gin.H{
		"user_id":     userID.String(),
		"is_online":   isOnline,
		"connections": len(connections),
	}
	
	if isOnline {
		connectionDetails := make([]gin.H, len(connections))
		for i, conn := range connections {
			connectionDetails[i] = gin.H{
				"id":           conn.ID().String(),
				"device_id":    conn.DeviceID(),
				"platform":     int(conn.PlatformType()),
				"status":       string(conn.Status()),
				"connected_at": conn.ConnectedAt(),
				"is_background": conn.IsBackground(),
			}
		}
		response["connection_details"] = connectionDetails
	}
	
	c.JSON(http.StatusOK, response)
}

func (h *WebSocketHandler) GetHubStats(c *gin.Context) {
	hub := h.container.GetMessagingHub()
	if hub == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"message": "WebSocket hub not initialized",
			"error":   "service_unavailable",
		})
		return
	}
	
	stats := hub.GetStats()
	
	c.JSON(http.StatusOK, gin.H{
		"active_connections": stats.ActiveConnections,
		"active_users":       stats.ActiveUsers,
		"max_connections":    stats.MaxConnections,
		"utilization":        float64(stats.ActiveConnections) / float64(stats.MaxConnections),
	})
}