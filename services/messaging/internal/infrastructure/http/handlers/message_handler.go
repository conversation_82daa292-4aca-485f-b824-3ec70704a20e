package handlers

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/http/utils"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messageservices "github.com/swork-team/platform/services/messaging/internal/domain/message/services"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/container"
)

type MessageHandler struct {
	container *container.Container
}

func NewMessageHandler(container *container.Container) *MessageHandler {
	return &MessageHandler{
		container: container,
	}
}

type SendMessageRequest struct {
	ConversationID string                 `json:"conversation_id" binding:"required"`
	Content        MessageContentRequest  `json:"content" binding:"required"`
	MessageType    string                 `json:"message_type"`
	ThreadID       *string                `json:"thread_id,omitempty"`
	ReplyToID      *string                `json:"reply_to_id,omitempty"`
}

type MessageContentRequest struct {
	Text        string                     `json:"text"`
	Attachments []MessageAttachmentRequest `json:"attachments,omitempty"`
	Mentions    []string                   `json:"mentions,omitempty"`
}

type MessageAttachmentRequest struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	URL      string `json:"url"`
	MimeType string `json:"mime_type"`
	Size     int64  `json:"size"`
}

type EditMessageRequest struct {
	Content MessageContentRequest `json:"content" binding:"required"`
}

type MessageResponse struct {
	ID             string                    `json:"id"`
	ConversationID string                    `json:"conversation_id"`
	SenderID       string                    `json:"sender_id"`
	Content        MessageContentResponse    `json:"content"`
	MessageType    string                    `json:"message_type"`
	Sequence       int64                     `json:"sequence"`
	ThreadID       *string                   `json:"thread_id,omitempty"`
	ReplyToID      *string                   `json:"reply_to_id,omitempty"`
	Status         string                    `json:"status"`
	Timestamp      string                    `json:"timestamp"`
	EditedAt       *string                   `json:"edited_at,omitempty"`
	DeletedAt      *string                   `json:"deleted_at,omitempty"`
	ReadBy         []ReadStatusResponse      `json:"read_by"`
}

type MessageContentResponse struct {
	Text        string                      `json:"text,omitempty"`
	Attachments []MessageAttachmentResponse `json:"attachments,omitempty"`
	Mentions    []string                    `json:"mentions,omitempty"`
	Links       []string                    `json:"links,omitempty"`
}

type MessageAttachmentResponse struct {
	ID       string                 `json:"id"`
	Name     string                 `json:"name"`
	URL      string                 `json:"url"`
	MimeType string                 `json:"mime_type"`
	Size     int64                  `json:"size"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

type ReadStatusResponse struct {
	UserID string `json:"user_id"`
	ReadAt string `json:"read_at"`
}

func (h *MessageHandler) SendMessage(c *gin.Context) {
	var req SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err)
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse conversation ID
	conversationID, err := uuid.Parse(req.ConversationID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid conversation ID", err)
		return
	}
	
	// Create message content
	content, err := h.requestToMessageContent(req.Content)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message content", err)
		return
	}
	
	// Determine message type
	messageType := messageTypeFromString(req.MessageType)
	
	// Create service request
	serviceRequest := &messageservices.SendMessageRequest{
		ConversationID: conversationentities.ConversationID(conversationID),
		SenderID:       userID,
		Content:        content,
		MessageType:    messageType,
	}
	
	// Parse optional thread and reply IDs
	if req.ThreadID != nil {
		threadID, err := uuid.Parse(*req.ThreadID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid thread ID", err)
			return
		}
		threadMessageID := messageentities.MessageID(threadID)
		serviceRequest.ThreadID = &threadMessageID
	}
	
	if req.ReplyToID != nil {
		replyID, err := uuid.Parse(*req.ReplyToID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid reply ID", err)
			return
		}
		replyMessageID := messageentities.MessageID(replyID)
		serviceRequest.ReplyToID = &replyMessageID
	}
	
	// Send message
	messageService := h.container.GetMessageService()
	if messageService == nil {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Message service not initialized", nil)
		return
	}
	
	message, err := messageService.SendMessage(c.Request.Context(), serviceRequest)
	if err != nil {
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to send message", err)
		return
	}
	
	// Convert to response
	response := h.messageToResponse(message)
	c.JSON(http.StatusCreated, response)
}

func (h *MessageHandler) GetMessage(c *gin.Context) {
	messageID := c.Param("messageId")
	if messageID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID", errors.New("message ID is required"))
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse message ID
	msgID, err := uuid.Parse(messageID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID format", err)
		return
	}
	
	// Get message
	messageService := h.container.GetMessageService()
	if messageService == nil {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Message service not initialized", nil)
		return
	}
	
	message, err := messageService.GetMessage(c.Request.Context(), messageentities.MessageID(msgID), userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			utils.ErrorResponse(c, http.StatusNotFound, "Message not found", err)
			return
		}
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get message", err)
		return
	}
	
	// Convert to response
	response := h.messageToResponse(message)
	c.JSON(http.StatusOK, response)
}

func (h *MessageHandler) EditMessage(c *gin.Context) {
	messageID := c.Param("messageId")
	if messageID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID", errors.New("message ID is required"))
		return
	}
	
	var req EditMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err)
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse message ID
	msgID, err := uuid.Parse(messageID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID format", err)
		return
	}
	
	// Create message content
	content, err := h.requestToMessageContent(req.Content)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message content", err)
		return
	}
	
	// Edit message
	messageService := h.container.GetMessageService()
	if messageService == nil {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Message service not initialized", nil)
		return
	}
	
	message, err := messageService.EditMessage(c.Request.Context(), messageentities.MessageID(msgID), userID, content)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			utils.ErrorResponse(c, http.StatusNotFound, "Message not found", err)
			return
		}
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		if strings.Contains(err.Error(), "too old") {
			utils.ErrorResponse(c, http.StatusBadRequest, "Message too old to edit", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to edit message", err)
		return
	}
	
	// Convert to response
	response := h.messageToResponse(message)
	c.JSON(http.StatusOK, response)
}

func (h *MessageHandler) DeleteMessage(c *gin.Context) {
	messageID := c.Param("messageId")
	if messageID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID", errors.New("message ID is required"))
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse message ID
	msgID, err := uuid.Parse(messageID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID format", err)
		return
	}
	
	// Delete message
	messageService := h.container.GetMessageService()
	if messageService == nil {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Message service not initialized", nil)
		return
	}
	
	err = messageService.DeleteMessage(c.Request.Context(), messageentities.MessageID(msgID), userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			utils.ErrorResponse(c, http.StatusNotFound, "Message not found", err)
			return
		}
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete message", err)
		return
	}
	
	c.JSON(http.StatusNoContent, nil)
}

func (h *MessageHandler) MarkAsRead(c *gin.Context) {
	messageID := c.Param("messageId")
	if messageID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID", errors.New("message ID is required"))
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse message ID
	msgID, err := uuid.Parse(messageID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid message ID format", err)
		return
	}
	
	// Mark message as read
	messageService := h.container.GetMessageService()
	if messageService == nil {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Message service not initialized", nil)
		return
	}
	
	err = messageService.MarkMessageAsRead(c.Request.Context(), messageentities.MessageID(msgID), userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			utils.ErrorResponse(c, http.StatusNotFound, "Message not found", err)
			return
		}
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to mark message as read", err)
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"status": "marked_as_read"})
}

func (h *MessageHandler) SearchMessages(c *gin.Context) {
	query := c.Query("q")
	fmt.Printf("DEBUG: Handler SearchMessages called with query '%s'\n", query)
	
	if query == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid query", errors.New("search query is required"))
		return
	}
	
	conversationIDStr := c.Query("conversation_id")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}
	
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	messageService := h.container.GetMessageService()
	if messageService == nil {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Message service not initialized", nil)
		return
	}
	
	var messages []*messageentities.Message
	
	// Conversation-specific search
	if conversationIDStr != "" {
		conversationID, err := uuid.Parse(conversationIDStr)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid conversation ID", err)
			return
		}
		
		messages, err = messageService.SearchMessages(
			c.Request.Context(),
			conversationentities.ConversationID(conversationID),
			userID,
			query,
			limit,
			offset,
		)
	} else {
		// Global search
		messages, err = messageService.SearchMessagesGlobal(c.Request.Context(), userID, query, limit, offset)
	}
	
	if err != nil {
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		if strings.Contains(err.Error(), "query must be") {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid search query", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Search failed", err)
		return
	}
	
	// Convert to responses
	responses := make([]MessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = h.messageToResponse(message)
	}
	
	c.JSON(http.StatusOK, gin.H{
		"messages": responses,
		"total":    len(responses),
		"limit":    limit,
		"offset":   offset,
	})
}

// Helper methods
func (h *MessageHandler) requestToMessageContent(req MessageContentRequest) (valueobjects.MessageContent, error) {
	// Convert attachments
	attachments := make([]valueobjects.MessageAttachment, len(req.Attachments))
	for i, att := range req.Attachments {
		attachments[i] = valueobjects.NewMessageAttachment(
			att.ID,
			att.Name,
			att.URL,
			att.MimeType,
			att.Size,
		)
	}
	
	// Create content based on whether there are attachments
	var content valueobjects.MessageContent
	var err error
	
	if len(attachments) > 0 {
		content, err = valueobjects.NewAttachmentMessageContent(attachments, req.Text)
	} else {
		content, err = valueobjects.NewTextMessageContent(req.Text)
	}
	
	if err != nil {
		return content, err
	}
	
	// Add mentions
	for _, mention := range req.Mentions {
		content.AddMention(mention)
	}
	
	return content, nil
}

func (h *MessageHandler) messageToResponse(message *messageentities.Message) MessageResponse {
	response := MessageResponse{
		ID:             message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		Content:        h.contentToResponse(message.Content()),
		MessageType:    string(message.MessageType()),
		Sequence:       message.Sequence(),
		Status:         string(message.Status()),
		Timestamp:      message.Timestamp().Format(time.RFC3339),
		ReadBy:         h.readStatusToResponse(message.ReadBy()),
	}
	
	if message.ThreadID() != nil {
		threadID := message.ThreadID().String()
		response.ThreadID = &threadID
	}
	
	if message.ReplyToID() != nil {
		replyToID := message.ReplyToID().String()
		response.ReplyToID = &replyToID
	}
	
	if message.EditedAt() != nil {
		editedAt := message.EditedAt().Format(time.RFC3339)
		response.EditedAt = &editedAt
	}
	
	if message.DeletedAt() != nil {
		deletedAt := message.DeletedAt().Format(time.RFC3339)
		response.DeletedAt = &deletedAt
	}
	
	return response
}

func (h *MessageHandler) contentToResponse(content valueobjects.MessageContent) MessageContentResponse {
	attachments := make([]MessageAttachmentResponse, len(content.Attachments()))
	for i, att := range content.Attachments() {
		attachments[i] = MessageAttachmentResponse{
			ID:       att.ID(),
			Name:     att.Name(),
			URL:      att.URL(),
			MimeType: att.MimeType(),
			Size:     att.Size(),
			Metadata: att.Metadata(),
		}
	}
	
	return MessageContentResponse{
		Text:        content.Text(),
		Attachments: attachments,
		Mentions:    content.Mentions(),
		Links:       content.Links(),
	}
}

func (h *MessageHandler) readStatusToResponse(readStatus []messageentities.ReadStatus) []ReadStatusResponse {
	responses := make([]ReadStatusResponse, len(readStatus))
	for i, rs := range readStatus {
		responses[i] = ReadStatusResponse{
			UserID: rs.UserID.String(),
			ReadAt: rs.ReadAt.Format(time.RFC3339),
		}
	}
	return responses
}

func messageTypeFromString(s string) messageentities.MessageType {
	switch s {
	case "text":
		return messageentities.MessageTypeText
	case "image":
		return messageentities.MessageTypeImage
	case "file":
		return messageentities.MessageTypeFile
	case "audio":
		return messageentities.MessageTypeAudio
	case "video":
		return messageentities.MessageTypeVideo
	case "location":
		return messageentities.MessageTypeLocation
	case "system":
		return messageentities.MessageTypeSystem
	default:
		return messageentities.MessageTypeText
	}
}