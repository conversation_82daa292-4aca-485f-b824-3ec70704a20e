package handlers

import (
	"errors"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/http/utils"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	conversationservices "github.com/swork-team/platform/services/messaging/internal/domain/conversation/services"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/container"
)

type ConversationHandler struct {
	container *container.Container
}

func NewConversationHandler(container *container.Container) *ConversationHandler {
	return &ConversationHandler{
		container: container,
	}
}

// Request/Response types
type CreateDirectConversationRequest struct {
	ParticipantID string `json:"participant_id" binding:"required"`
}

type CreateGroupConversationRequest struct {
	Name         string   `json:"name" binding:"required"`
	Description  *string  `json:"description,omitempty"`
	Participants []string `json:"participants" binding:"required"`
}

type CreateTeamChannelRequest struct {
	Name         string   `json:"name" binding:"required"`
	Description  *string  `json:"description,omitempty"`
	TeamID       string   `json:"team_id" binding:"required"`
	Participants []string `json:"participants,omitempty"`
}

type UpdateConversationRequest struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
}

type ConversationResponse struct {
	ID            string                          `json:"id"`
	Type          string                          `json:"type"`
	Name          *string                         `json:"name,omitempty"`
	Description   *string                         `json:"description,omitempty"`
	TeamID        *string                         `json:"team_id,omitempty"`
	CreatorID     string                          `json:"creator_id"`
	LastMessageID *string                         `json:"last_message_id,omitempty"`
	LastActivity  string                          `json:"last_activity"`
	MessageCount  int64                           `json:"message_count"`
	MaxSequence   int64                           `json:"max_sequence"`
	CreatedAt     string                          `json:"created_at"`
	UpdatedAt     string                          `json:"updated_at"`
	Participants  []ConversationParticipantResponse `json:"participants"`
}

type ConversationParticipantResponse struct {
	UserID      string `json:"user_id"`
	JoinedAt    string `json:"joined_at"`
	LastReadSeq int64  `json:"last_read_seq"`
	Role        string `json:"role"`
}

type ConversationUnreadResponse struct {
	ConversationID string  `json:"conversation_id"`
	UnreadCount    int64   `json:"unread_count"`
	LastMessageID  *string `json:"last_message_id,omitempty"`
	LastActivity   string  `json:"last_activity"`
}

// Handlers
func (h *ConversationHandler) CreateDirectConversation(c *gin.Context) {
	var req CreateDirectConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err)
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse participant ID
	participantID, err := uuid.Parse(req.ParticipantID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid participant ID", err)
		return
	}
	
	// Create direct conversation
	conversationService := h.container.GetConversationService()
	conversation, err := conversationService.CreateDirectConversation(c.Request.Context(), userID, participantID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create conversation", err)
		return
	}
	
	// Convert to response
	response := h.conversationToResponse(conversation)
	c.JSON(http.StatusCreated, response)
}

func (h *ConversationHandler) CreateGroupConversation(c *gin.Context) {
	var req CreateGroupConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err)
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse participant IDs
	participants := make([]uuid.UUID, len(req.Participants))
	for i, p := range req.Participants {
		participantID, err := uuid.Parse(p)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid participant ID: "+p, err)
			return
		}
		participants[i] = participantID
	}
	
	// Create service request
	serviceRequest := &conversationservices.CreateGroupConversationRequest{
		Name:         req.Name,
		Description:  req.Description,
		CreatorID:    userID,
		Participants: participants,
	}
	
	// Create group conversation
	conversationService := h.container.GetConversationService()
	conversation, err := conversationService.CreateGroupConversation(c.Request.Context(), serviceRequest)
	if err != nil {
		if strings.Contains(err.Error(), "validation") {
			utils.ErrorResponse(c, http.StatusBadRequest, "Validation error", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create conversation", err)
		return
	}
	
	// Convert to response
	response := h.conversationToResponse(conversation)
	c.JSON(http.StatusCreated, response)
}

func (h *ConversationHandler) CreateTeamChannel(c *gin.Context) {
	var req CreateTeamChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err)
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse team ID
	teamID, err := uuid.Parse(req.TeamID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}
	
	// Parse participant IDs
	participants := make([]uuid.UUID, len(req.Participants))
	for i, p := range req.Participants {
		participantID, err := uuid.Parse(p)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid participant ID: "+p, err)
			return
		}
		participants[i] = participantID
	}
	
	// Create service request
	serviceRequest := &conversationservices.CreateTeamChannelRequest{
		Name:         req.Name,
		Description:  req.Description,
		TeamID:       teamID,
		CreatorID:    userID,
		Participants: participants,
	}
	
	// Create team channel
	conversationService := h.container.GetConversationService()
	conversation, err := conversationService.CreateTeamChannel(c.Request.Context(), serviceRequest)
	if err != nil {
		if strings.Contains(err.Error(), "access") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		if strings.Contains(err.Error(), "validation") {
			utils.ErrorResponse(c, http.StatusBadRequest, "Validation error", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create team channel", err)
		return
	}
	
	// Convert to response
	response := h.conversationToResponse(conversation)
	c.JSON(http.StatusCreated, response)
}

func (h *ConversationHandler) GetConversation(c *gin.Context) {
	conversationID := c.Param("conversationId")
	if conversationID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid conversation ID", errors.New("conversation ID is required"))
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse conversation ID
	convID, err := uuid.Parse(conversationID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid conversation ID format", err)
		return
	}
	
	// Get conversation
	conversationService := h.container.GetConversationService()
	conversation, err := conversationService.GetConversation(c.Request.Context(), entities.ConversationID(convID), userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			utils.ErrorResponse(c, http.StatusNotFound, "Conversation not found", err)
			return
		}
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get conversation", err)
		return
	}
	
	// Convert to response
	response := h.conversationToResponse(conversation)
	c.JSON(http.StatusOK, response)
}

func (h *ConversationHandler) GetUserConversations(c *gin.Context) {
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}
	
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}
	
	// Get conversations
	conversationService := h.container.GetConversationService()
	if conversationService == nil {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "Conversation service not initialized", nil)
		return
	}
	
	conversations, err := conversationService.GetUserConversations(c.Request.Context(), userID, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get conversations", err)
		return
	}
	
	// Convert to responses
	responses := make([]ConversationResponse, len(conversations))
	for i, conversation := range conversations {
		responses[i] = h.conversationToResponse(conversation)
	}
	
	c.JSON(http.StatusOK, gin.H{
		"conversations": responses,
		"total":         len(responses),
		"limit":         limit,
		"offset":        offset,
	})
}

func (h *ConversationHandler) GetUnreadConversations(c *gin.Context) {
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Get unread conversations
	conversationService := h.container.GetConversationService()
	unreadInfos, err := conversationService.GetUnreadConversations(c.Request.Context(), userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get unread conversations", err)
		return
	}
	
	// Convert to responses
	responses := make([]ConversationUnreadResponse, len(unreadInfos))
	for i, info := range unreadInfos {
		responses[i] = ConversationUnreadResponse{
			ConversationID: info.ConversationID.String(),
			UnreadCount:    info.UnreadCount,
			LastMessageID:  info.LastMessageID,
			LastActivity:   info.LastActivity.Format(time.RFC3339),
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"unread_conversations": responses,
		"total":                len(responses),
	})
}

func (h *ConversationHandler) UpdateConversation(c *gin.Context) {
	conversationID := c.Param("conversationId")
	if conversationID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid conversation ID", errors.New("conversation ID is required"))
		return
	}
	
	var req UpdateConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err)
		return
	}
	
	// Get user ID from authentication context
	userID, err := utils.GetUserIDAsUUID(c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", err)
		return
	}
	
	// Parse conversation ID
	convID, err := uuid.Parse(conversationID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid conversation ID format", err)
		return
	}
	
	// Create service request
	serviceRequest := &conversationservices.UpdateConversationRequest{
		Name:        req.Name,
		Description: req.Description,
	}
	
	// Update conversation
	conversationService := h.container.GetConversationService()
	conversation, err := conversationService.UpdateConversation(c.Request.Context(), entities.ConversationID(convID), userID, serviceRequest)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			utils.ErrorResponse(c, http.StatusNotFound, "Conversation not found", err)
			return
		}
		if strings.Contains(err.Error(), "access denied") {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		if strings.Contains(err.Error(), "validation") {
			utils.ErrorResponse(c, http.StatusBadRequest, "Validation error", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update conversation", err)
		return
	}
	
	// Convert to response
	response := h.conversationToResponse(conversation)
	c.JSON(http.StatusOK, response)
}

// Helper methods
func (h *ConversationHandler) conversationToResponse(conversation *entities.Conversation) ConversationResponse {
	participants := make([]ConversationParticipantResponse, len(conversation.Participants()))
	for i, p := range conversation.Participants() {
		participants[i] = ConversationParticipantResponse{
			UserID:      p.UserID().String(),
			JoinedAt:    p.JoinedAt().Format(time.RFC3339),
			LastReadSeq: p.LastReadSeq(),
			Role:        string(p.Role()),
		}
	}
	
	response := ConversationResponse{
		ID:            conversation.ID().String(),
		Type:          string(conversation.Type()),
		Name:          conversation.Name(),
		Description:   conversation.Description(),
		CreatorID:     conversation.CreatorID().String(),
		LastMessageID: conversation.LastMessageID(),
		LastActivity:  conversation.LastActivity().Format(time.RFC3339),
		MessageCount:  conversation.MessageCount(),
		MaxSequence:   conversation.MaxSequence(),
		CreatedAt:     conversation.CreatedAt().Format(time.RFC3339),
		UpdatedAt:     conversation.UpdatedAt().Format(time.RFC3339),
		Participants:  participants,
	}
	
	if conversation.TeamID() != nil {
		teamID := conversation.TeamID().String()
		response.TeamID = &teamID
	}
	
	return response
}