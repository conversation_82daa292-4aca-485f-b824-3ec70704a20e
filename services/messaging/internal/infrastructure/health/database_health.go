package health

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"github.com/redis/go-redis/v9"
)

// HealthStatus represents the health status of a component
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusDegraded  HealthStatus = "degraded"
	HealthStatusUnknown   HealthStatus = "unknown"
)

// HealthCheck represents a health check result
type HealthCheck struct {
	Name        string                 `json:"name"`
	Status      HealthStatus           `json:"status"`
	Message     string                 `json:"message,omitempty"`
	Duration    time.Duration          `json:"duration"`
	Timestamp   time.Time              `json:"timestamp"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// DatabaseHealthChecker manages health checks for all database systems
type DatabaseHealthChecker struct {
	postgres    *sql.DB
	mongodb     *mongo.Client
	mongoDB     *mongo.Database
	redis       *redis.Client
	
	// Configuration
	checkInterval   time.Duration
	timeout         time.Duration
	degradedThreshold time.Duration
	
	// State management
	lastChecks      map[string]*HealthCheck
	mutex           sync.RWMutex
	stopChan        chan struct{}
	running         bool
}

// NewDatabaseHealthChecker creates a new health checker
func NewDatabaseHealthChecker(
	postgres *sql.DB,
	mongodb *mongo.Client,
	mongoDB *mongo.Database,
	redis *redis.Client,
	checkInterval, timeout, degradedThreshold time.Duration,
) *DatabaseHealthChecker {
	return &DatabaseHealthChecker{
		postgres:          postgres,
		mongodb:           mongodb,
		mongoDB:           mongoDB,
		redis:             redis,
		checkInterval:     checkInterval,
		timeout:           timeout,
		degradedThreshold: degradedThreshold,
		lastChecks:        make(map[string]*HealthCheck),
		stopChan:          make(chan struct{}),
	}
}

// Start begins periodic health checking
func (h *DatabaseHealthChecker) Start() error {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	
	if h.running {
		return fmt.Errorf("health checker is already running")
	}
	
	h.running = true
	
	// Run initial checks
	go h.runChecks()
	
	// Start periodic checking
	go h.periodicCheck()
	
	return nil
}

// Stop stops the health checker
func (h *DatabaseHealthChecker) Stop() {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	
	if !h.running {
		return
	}
	
	h.running = false
	close(h.stopChan)
}

// GetOverallHealth returns the overall health status
func (h *DatabaseHealthChecker) GetOverallHealth() *HealthCheck {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	
	overallStatus := HealthStatusHealthy
	var unhealthyServices []string
	var degradedServices []string
	totalDuration := time.Duration(0)
	
	for name, check := range h.lastChecks {
		totalDuration += check.Duration
		
		switch check.Status {
		case HealthStatusUnhealthy:
			overallStatus = HealthStatusUnhealthy
			unhealthyServices = append(unhealthyServices, name)
		case HealthStatusDegraded:
			if overallStatus == HealthStatusHealthy {
				overallStatus = HealthStatusDegraded
			}
			degradedServices = append(degradedServices, name)
		}
	}
	
	message := "All database services are healthy"
	if len(unhealthyServices) > 0 {
		message = fmt.Sprintf("Unhealthy services: %v", unhealthyServices)
	} else if len(degradedServices) > 0 {
		message = fmt.Sprintf("Degraded services: %v", degradedServices)
	}
	
	return &HealthCheck{
		Name:      "database_overall",
		Status:    overallStatus,
		Message:   message,
		Duration:  totalDuration,
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"services_checked":    len(h.lastChecks),
			"unhealthy_services": unhealthyServices,
			"degraded_services":  degradedServices,
		},
	}
}

// GetServiceHealth returns health for a specific service
func (h *DatabaseHealthChecker) GetServiceHealth(serviceName string) *HealthCheck {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	
	if check, exists := h.lastChecks[serviceName]; exists {
		return check
	}
	
	return &HealthCheck{
		Name:      serviceName,
		Status:    HealthStatusUnknown,
		Message:   "No health check data available",
		Timestamp: time.Now(),
	}
}

// GetAllHealthChecks returns all current health checks
func (h *DatabaseHealthChecker) GetAllHealthChecks() map[string]*HealthCheck {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	
	result := make(map[string]*HealthCheck)
	for name, check := range h.lastChecks {
		result[name] = check
	}
	
	return result
}

// periodicCheck runs health checks periodically
func (h *DatabaseHealthChecker) periodicCheck() {
	ticker := time.NewTicker(h.checkInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			h.runChecks()
		case <-h.stopChan:
			return
		}
	}
}

// runChecks executes all health checks
func (h *DatabaseHealthChecker) runChecks() {
	var wg sync.WaitGroup
	
	// PostgreSQL health check
	if h.postgres != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			check := h.checkPostgreSQL()
			h.updateCheck("postgresql", check)
		}()
	}
	
	// MongoDB health check
	if h.mongodb != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			check := h.checkMongoDB()
			h.updateCheck("mongodb", check)
		}()
	}
	
	// Redis health check
	if h.redis != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			check := h.checkRedis()
			h.updateCheck("redis", check)
		}()
	}
	
	wg.Wait()
}

// checkPostgreSQL performs PostgreSQL health check
func (h *DatabaseHealthChecker) checkPostgreSQL() *HealthCheck {
	start := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), h.timeout)
	defer cancel()
	
	check := &HealthCheck{
		Name:      "postgresql",
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}
	
	// Test basic connection
	if err := h.postgres.PingContext(ctx); err != nil {
		check.Status = HealthStatusUnhealthy
		check.Error = err.Error()
		check.Message = "Failed to ping PostgreSQL"
		check.Duration = time.Since(start)
		return check
	}
	
	// Test query execution
	var result int
	query := "SELECT 1"
	if err := h.postgres.QueryRowContext(ctx, query).Scan(&result); err != nil {
		check.Status = HealthStatusUnhealthy
		check.Error = err.Error()
		check.Message = "Failed to execute test query"
		check.Duration = time.Since(start)
		return check
	}
	
	// Get connection pool stats
	stats := h.postgres.Stats()
	check.Details["open_connections"] = stats.OpenConnections
	check.Details["in_use"] = stats.InUse
	check.Details["idle"] = stats.Idle
	check.Details["wait_count"] = stats.WaitCount
	check.Details["wait_duration"] = stats.WaitDuration.String()
	check.Details["max_idle_closed"] = stats.MaxIdleClosed
	check.Details["max_lifetime_closed"] = stats.MaxLifetimeClosed
	
	check.Duration = time.Since(start)
	
	// Determine status based on performance
	if check.Duration > h.degradedThreshold {
		check.Status = HealthStatusDegraded
		check.Message = fmt.Sprintf("PostgreSQL response time degraded: %v", check.Duration)
	} else {
		check.Status = HealthStatusHealthy
		check.Message = "PostgreSQL is healthy"
	}
	
	return check
}

// checkMongoDB performs MongoDB health check
func (h *DatabaseHealthChecker) checkMongoDB() *HealthCheck {
	start := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), h.timeout)
	defer cancel()
	
	check := &HealthCheck{
		Name:      "mongodb",
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}
	
	// Test connection with ping
	if err := h.mongodb.Ping(ctx, readpref.Primary()); err != nil {
		check.Status = HealthStatusUnhealthy
		check.Error = err.Error()
		check.Message = "Failed to ping MongoDB"
		check.Duration = time.Since(start)
		return check
	}
	
	// Test database operations
	coll := h.mongoDB.Collection("health_check")
	doc := map[string]interface{}{
		"_id":       "health_check",
		"timestamp": time.Now(),
	}
	
	// Test write operation
	if _, err := coll.ReplaceOne(ctx, map[string]interface{}{"_id": "health_check"}, doc); err != nil {
		// Try insert if replace fails
		if _, insertErr := coll.InsertOne(ctx, doc); insertErr != nil {
			check.Status = HealthStatusDegraded
			check.Error = insertErr.Error()
			check.Message = "Failed to write to MongoDB"
		}
	}
	
	// Test read operation
	var result map[string]interface{}
	if err := coll.FindOne(ctx, map[string]interface{}{"_id": "health_check"}).Decode(&result); err != nil {
		if check.Status != HealthStatusDegraded {
			check.Status = HealthStatusDegraded
			check.Error = err.Error()
			check.Message = "Failed to read from MongoDB"
		}
	}
	
	// Get server status (if available)
	var serverStatus map[string]interface{}
	if err := h.mongoDB.RunCommand(ctx, map[string]interface{}{"serverStatus": 1}).Decode(&serverStatus); err == nil {
		if connections, ok := serverStatus["connections"].(map[string]interface{}); ok {
			check.Details["current_connections"] = connections["current"]
			check.Details["available_connections"] = connections["available"]
		}
		if opcounters, ok := serverStatus["opcounters"].(map[string]interface{}); ok {
			check.Details["operations"] = opcounters
		}
	}
	
	check.Duration = time.Since(start)
	
	// Determine final status
	if check.Status == "" {
		if check.Duration > h.degradedThreshold {
			check.Status = HealthStatusDegraded
			check.Message = fmt.Sprintf("MongoDB response time degraded: %v", check.Duration)
		} else {
			check.Status = HealthStatusHealthy
			check.Message = "MongoDB is healthy"
		}
	}
	
	return check
}

// checkRedis performs Redis health check
func (h *DatabaseHealthChecker) checkRedis() *HealthCheck {
	start := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), h.timeout)
	defer cancel()
	
	check := &HealthCheck{
		Name:      "redis",
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}
	
	// Test basic ping
	if err := h.redis.Ping(ctx).Err(); err != nil {
		check.Status = HealthStatusUnhealthy
		check.Error = err.Error()
		check.Message = "Failed to ping Redis"
		check.Duration = time.Since(start)
		return check
	}
	
	// Test read/write operations
	testKey := "health_check"
	testValue := fmt.Sprintf("health_check_%d", time.Now().Unix())
	
	// Test write
	if err := h.redis.Set(ctx, testKey, testValue, time.Minute).Err(); err != nil {
		check.Status = HealthStatusDegraded
		check.Error = err.Error()
		check.Message = "Failed to write to Redis"
	}
	
	// Test read
	if value, err := h.redis.Get(ctx, testKey).Result(); err != nil {
		if check.Status == "" {
			check.Status = HealthStatusDegraded
			check.Error = err.Error()
			check.Message = "Failed to read from Redis"
		}
	} else if value != testValue {
		if check.Status == "" {
			check.Status = HealthStatusDegraded
			check.Message = "Redis read/write data mismatch"
		}
	}
	
	// Get Redis info
	if info, err := h.redis.Info(ctx).Result(); err == nil {
		// Parse basic info (simplified parsing)
		lines := strings.Split(info, "\r\n")
		for _, line := range lines {
			if strings.Contains(line, "connected_clients:") {
				if parts := strings.Split(line, ":"); len(parts) == 2 {
					check.Details["connected_clients"] = strings.TrimSpace(parts[1])
				}
			}
			if strings.Contains(line, "used_memory_human:") {
				if parts := strings.Split(line, ":"); len(parts) == 2 {
					check.Details["used_memory"] = strings.TrimSpace(parts[1])
				}
			}
		}
	}
	
	// Get pool stats
	stats := h.redis.PoolStats()
	check.Details["pool_hits"] = stats.Hits
	check.Details["pool_misses"] = stats.Misses
	check.Details["pool_timeouts"] = stats.Timeouts
	check.Details["pool_total_conns"] = stats.TotalConns
	check.Details["pool_idle_conns"] = stats.IdleConns
	check.Details["pool_stale_conns"] = stats.StaleConns
	
	check.Duration = time.Since(start)
	
	// Determine final status
	if check.Status == "" {
		if check.Duration > h.degradedThreshold {
			check.Status = HealthStatusDegraded
			check.Message = fmt.Sprintf("Redis response time degraded: %v", check.Duration)
		} else {
			check.Status = HealthStatusHealthy
			check.Message = "Redis is healthy"
		}
	}
	
	return check
}

// updateCheck updates the stored health check result
func (h *DatabaseHealthChecker) updateCheck(serviceName string, check *HealthCheck) {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	h.lastChecks[serviceName] = check
}

// Additional helper for parsing Redis info