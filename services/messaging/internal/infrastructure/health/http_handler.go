package health

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// HealthHandler provides HTTP endpoints for health checks
type HealthHandler struct {
	checker *DatabaseHealthChecker
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(checker *DatabaseHealthChecker) *HealthHandler {
	return &HealthHandler{
		checker: checker,
	}
}

// RegisterRoutes registers health check routes
func (h *HealthHandler) RegisterRoutes(router *gin.Engine) {
	health := router.Group("/health")
	{
		health.GET("", h.GetOverallHealth)
		health.GET("/", h.GetOverallHealth)
		health.GET("/database", h.GetDatabaseHealth)
		health.GET("/database/:service", h.GetServiceHealth)
		health.GET("/detailed", h.GetDetailedHealth)
		health.GET("/ready", h.GetReadinessCheck)
		health.GET("/live", h.GetLivenessCheck)
	}
}

// HealthResponse represents the structure of health check responses
type HealthResponse struct {
	Status    HealthStatus           `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Service   string                 `json:"service"`
	Version   string                 `json:"version,omitempty"`
	Uptime    string                 `json:"uptime,omitempty"`
	Checks    map[string]*HealthCheck `json:"checks,omitempty"`
	Summary   *HealthSummary         `json:"summary,omitempty"`
}

// HealthSummary provides aggregated health information
type HealthSummary struct {
	TotalChecks    int           `json:"total_checks"`
	HealthyChecks  int           `json:"healthy_checks"`
	DegradedChecks int           `json:"degraded_checks"`
	UnhealthyChecks int          `json:"unhealthy_checks"`
	TotalDuration  time.Duration `json:"total_duration"`
	AverageDuration time.Duration `json:"average_duration"`
}

// GetOverallHealth returns overall service health
func (h *HealthHandler) GetOverallHealth(c *gin.Context) {
	overall := h.checker.GetOverallHealth()
	
	response := HealthResponse{
		Status:    overall.Status,
		Timestamp: time.Now(),
		Service:   "messaging-service",
		Version:   getServiceVersion(),
		Uptime:    getServiceUptime(),
	}
	
	statusCode := h.getHTTPStatusCode(overall.Status)
	c.JSON(statusCode, response)
}

// GetDatabaseHealth returns health of all database services
func (h *HealthHandler) GetDatabaseHealth(c *gin.Context) {
	checks := h.checker.GetAllHealthChecks()
	overall := h.checker.GetOverallHealth()
	summary := h.calculateSummary(checks)
	
	response := HealthResponse{
		Status:    overall.Status,
		Timestamp: time.Now(),
		Service:   "messaging-service-database",
		Checks:    checks,
		Summary:   summary,
	}
	
	statusCode := h.getHTTPStatusCode(overall.Status)
	c.JSON(statusCode, response)
}

// GetServiceHealth returns health of a specific database service
func (h *HealthHandler) GetServiceHealth(c *gin.Context) {
	serviceName := c.Param("service")
	check := h.checker.GetServiceHealth(serviceName)
	
	if check.Status == HealthStatusUnknown {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Service not found",
			"service": serviceName,
		})
		return
	}
	
	response := HealthResponse{
		Status:    check.Status,
		Timestamp: time.Now(),
		Service:   serviceName,
		Checks:    map[string]*HealthCheck{serviceName: check},
	}
	
	statusCode := h.getHTTPStatusCode(check.Status)
	c.JSON(statusCode, response)
}

// GetDetailedHealth returns detailed health information
func (h *HealthHandler) GetDetailedHealth(c *gin.Context) {
	checks := h.checker.GetAllHealthChecks()
	overall := h.checker.GetOverallHealth()
	summary := h.calculateSummary(checks)
	
	response := HealthResponse{
		Status:    overall.Status,
		Timestamp: time.Now(),
		Service:   "messaging-service",
		Version:   getServiceVersion(),
		Uptime:    getServiceUptime(),
		Checks:    checks,
		Summary:   summary,
	}
	
	statusCode := h.getHTTPStatusCode(overall.Status)
	c.JSON(statusCode, response)
}

// GetReadinessCheck returns readiness status (for Kubernetes)
func (h *HealthHandler) GetReadinessCheck(c *gin.Context) {
	overall := h.checker.GetOverallHealth()
	
	// Service is ready if all critical services are healthy or degraded
	isReady := overall.Status == HealthStatusHealthy || overall.Status == HealthStatusDegraded
	
	if isReady {
		c.JSON(http.StatusOK, gin.H{
			"status": "ready",
			"timestamp": time.Now(),
		})
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "not_ready",
			"timestamp": time.Now(),
			"message": overall.Message,
		})
	}
}

// GetLivenessCheck returns liveness status (for Kubernetes)
func (h *HealthHandler) GetLivenessCheck(c *gin.Context) {
	// Simple liveness check - service is alive if it can respond
	c.JSON(http.StatusOK, gin.H{
		"status": "alive",
		"timestamp": time.Now(),
		"service": "messaging-service",
	})
}

// calculateSummary calculates health summary statistics
func (h *HealthHandler) calculateSummary(checks map[string]*HealthCheck) *HealthSummary {
	summary := &HealthSummary{
		TotalChecks: len(checks),
	}
	
	var totalDuration time.Duration
	
	for _, check := range checks {
		totalDuration += check.Duration
		
		switch check.Status {
		case HealthStatusHealthy:
			summary.HealthyChecks++
		case HealthStatusDegraded:
			summary.DegradedChecks++
		case HealthStatusUnhealthy:
			summary.UnhealthyChecks++
		}
	}
	
	summary.TotalDuration = totalDuration
	if len(checks) > 0 {
		summary.AverageDuration = totalDuration / time.Duration(len(checks))
	}
	
	return summary
}

// getHTTPStatusCode maps health status to HTTP status code
func (h *HealthHandler) getHTTPStatusCode(status HealthStatus) int {
	switch status {
	case HealthStatusHealthy:
		return http.StatusOK
	case HealthStatusDegraded:
		return http.StatusOK // Still operational
	case HealthStatusUnhealthy:
		return http.StatusServiceUnavailable
	default:
		return http.StatusServiceUnavailable
	}
}

// Service metadata helpers
var serviceStartTime = time.Now()

func getServiceVersion() string {
	// This could be set during build or from environment
	version := "1.0.0" // Default version
	// You could also read from VERSION file, build tags, or environment variable
	return version
}

func getServiceUptime() string {
	uptime := time.Since(serviceStartTime)
	return uptime.String()
}

// Custom middleware for health check logging
func (h *HealthHandler) LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// Custom log format for health check endpoints
		if param.Path == "/health" || 
		   param.Path == "/health/" || 
		   param.Path == "/health/live" {
			// Reduce verbosity for frequent health checks
			return ""
		}
		
		// Use default format for other health endpoints
		return gin.LoggerConfig{}.Formatter(param)
	})
}

// Middleware to add health check headers
func (h *HealthHandler) HeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add standard health check headers
		c.Header("Content-Type", "application/json")
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("X-Health-Check-Service", "messaging-service")
		c.Header("X-Health-Check-Version", getServiceVersion())
		
		c.Next()
	}
}

// Advanced health check with custom metrics
func (h *HealthHandler) GetAdvancedHealth(c *gin.Context) {
	checks := h.checker.GetAllHealthChecks()
	overall := h.checker.GetOverallHealth()
	summary := h.calculateSummary(checks)
	
	// Add additional system metrics
	systemMetrics := map[string]interface{}{
		"uptime":           getServiceUptime(),
		"timestamp":        time.Now(),
		"go_routines":      getGoroutineCount(),
		"memory_usage":     getMemoryUsage(),
		"cpu_usage":        getCPUUsage(),
	}
	
	response := map[string]interface{}{
		"status":          overall.Status,
		"timestamp":       time.Now(),
		"service":         "messaging-service",
		"version":         getServiceVersion(),
		"checks":          checks,
		"summary":         summary,
		"system_metrics":  systemMetrics,
	}
	
	statusCode := h.getHTTPStatusCode(overall.Status)
	c.JSON(statusCode, response)
}

// System metrics helpers (simplified implementations)
func getGoroutineCount() int {
	// Import runtime and return runtime.NumGoroutine()
	return 0 // Placeholder
}

func getMemoryUsage() map[string]interface{} {
	// Import runtime and return memory stats
	return map[string]interface{}{
		"alloc":      "0 MB",
		"total_alloc": "0 MB",
		"sys":        "0 MB",
		"num_gc":     0,
	}
}

func getCPUUsage() float64 {
	// Return CPU usage percentage
	return 0.0 // Placeholder
}