package websocket

import (
	"context"
	"encoding/json"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/domain/realtime/entities"
	"github.com/swork-team/platform/pkg/config"
)

type MessagingHub struct {
	// Core connection management
	userConnections sync.Map // map[uuid.UUID]*UserConnections
	register        chan *entities.Connection
	unregister      chan *entities.Connection
	kickHandler     chan *KickHandler
	
	// Message broadcasting
	broadcast     chan *BroadcastMessage
	messageQueue  chan *QueuedMessage
	
	// Connection limits and metrics
	maxConnections int64
	activeConns    atomic.Int64
	activeUsers    atomic.Int64
	
	// Configuration
	config *config.MessagingServiceConfig
	
	// Message handling
	messageHandler *MessageHandler
	
	// Object pooling for performance
	clientPool    sync.Pool
	messagePool   sync.Pool
	
	// Lifecycle management
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

type UserConnections struct {
	userID      uuid.UUID
	connections map[entities.ConnectionID]*entities.Connection
	mutex       sync.RWMutex
	lastSeen    time.Time
	isOnline    bool
}

type BroadcastMessage struct {
	ConversationID string
	Message        interface{}
	Recipients     []uuid.UUID
	ExcludeUserID  *uuid.UUID
	DeliveryMode   DeliveryMode
}

type DeliveryMode int

const (
	DeliveryModeRealTime DeliveryMode = iota
	DeliveryModeQueued
	DeliveryModeOffline
)

type QueuedMessage struct {
	UserID    uuid.UUID
	Message   interface{}
	Priority  MessagePriority
	Timestamp time.Time
}

type MessagePriority int

const (
	PriorityLow MessagePriority = iota
	PriorityNormal
	PriorityHigh
	PriorityUrgent
)

type KickHandler struct {
	UserID     uuid.UUID
	DeviceID   string
	PlatformID entities.PlatformType
	Reason     string
}

func NewMessagingHub(config *config.MessagingServiceConfig) *MessagingHub {
	ctx, cancel := context.WithCancel(context.Background())
	
	hub := &MessagingHub{
		register:       make(chan *entities.Connection, config.WebSocket.ChannelBufferSize),
		unregister:     make(chan *entities.Connection, config.WebSocket.ChannelBufferSize),
		kickHandler:    make(chan *KickHandler, config.WebSocket.ChannelBufferSize),
		broadcast:      make(chan *BroadcastMessage, config.WebSocket.ChannelBufferSize*2),
		messageQueue:   make(chan *QueuedMessage, config.WebSocket.ChannelBufferSize*4),
		maxConnections: config.WebSocket.MaxConnections,
		config:         config,
		ctx:            ctx,
		cancel:         cancel,
	}
	
	// Initialize object pools
	hub.clientPool = sync.Pool{
		New: func() interface{} {
			return make(map[string]interface{})
		},
	}
	
	hub.messagePool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 1024)
		},
	}
	
	return hub
}

func (h *MessagingHub) Start() {
	log.Println("Starting messaging hub...")
	
	// Start worker goroutines
	h.wg.Add(4)
	go h.handleRegistrations()
	go h.handleBroadcasts()
	go h.handleMessageQueue()
	go h.handleKicks()
	
	// Start periodic cleanup
	h.wg.Add(1)
	go h.periodicCleanup()
	
	log.Printf("Messaging hub started with max connections: %d", h.maxConnections)
}

func (h *MessagingHub) Stop() {
	log.Println("Stopping messaging hub...")
	
	h.cancel()
	h.wg.Wait()
	
	// Close all connections
	h.userConnections.Range(func(key, value interface{}) bool {
		userConns := value.(*UserConnections)
		userConns.mutex.Lock()
		for _, conn := range userConns.connections {
			conn.Close()
		}
		userConns.mutex.Unlock()
		return true
	})
	
	log.Println("Messaging hub stopped")
}

func (h *MessagingHub) RegisterConnection(conn *entities.Connection) error {
	if h.activeConns.Load() >= h.maxConnections {
		return ErrMaxConnectionsReached
	}
	
	select {
	case h.register <- conn:
		return nil
	case <-h.ctx.Done():
		return ErrHubClosed
	default:
		return ErrRegistrationFull
	}
}

func (h *MessagingHub) UnregisterConnection(conn *entities.Connection) {
	select {
	case h.unregister <- conn:
	case <-h.ctx.Done():
	default:
		// Best effort unregistration
		log.Printf("Failed to unregister connection %s - channel full", conn.ID())
	}
}

func (h *MessagingHub) BroadcastMessage(msg *BroadcastMessage) {
	select {
	case h.broadcast <- msg:
	case <-h.ctx.Done():
	default:
		log.Printf("Failed to broadcast message - channel full")
	}
}

func (h *MessagingHub) SendToUser(userID uuid.UUID, message interface{}, priority MessagePriority) {
	queuedMsg := &QueuedMessage{
		UserID:    userID,
		Message:   message,
		Priority:  priority,
		Timestamp: time.Now(),
	}
	
	select {
	case h.messageQueue <- queuedMsg:
	case <-h.ctx.Done():
	default:
		log.Printf("Failed to queue message for user %s - channel full", userID)
	}
}

func (h *MessagingHub) KickUser(userID uuid.UUID, deviceID string, platformID entities.PlatformType, reason string) {
	kick := &KickHandler{
		UserID:     userID,
		DeviceID:   deviceID,
		PlatformID: platformID,
		Reason:     reason,
	}
	
	select {
	case h.kickHandler <- kick:
	case <-h.ctx.Done():
	default:
		log.Printf("Failed to kick user %s - channel full", userID)
	}
}

func (h *MessagingHub) GetUserConnections(userID uuid.UUID) ([]*entities.Connection, bool) {
	if value, ok := h.userConnections.Load(userID); ok {
		userConns := value.(*UserConnections)
		userConns.mutex.RLock()
		defer userConns.mutex.RUnlock()
		
		connections := make([]*entities.Connection, 0, len(userConns.connections))
		for _, conn := range userConns.connections {
			if !conn.IsClosed() {
				connections = append(connections, conn)
			}
		}
		return connections, len(connections) > 0
	}
	return nil, false
}

func (h *MessagingHub) IsUserOnline(userID uuid.UUID) bool {
	if value, ok := h.userConnections.Load(userID); ok {
		userConns := value.(*UserConnections)
		userConns.mutex.RLock()
		defer userConns.mutex.RUnlock()
		return userConns.isOnline
	}
	return false
}

func (h *MessagingHub) GetStats() HubStats {
	return HubStats{
		ActiveConnections: h.activeConns.Load(),
		ActiveUsers:       h.activeUsers.Load(),
		MaxConnections:    h.maxConnections,
	}
}

func (h *MessagingHub) SetMessageHandler(handler *MessageHandler) {
	h.messageHandler = handler
}

func (h *MessagingHub) GetMessageHandler() *MessageHandler {
	return h.messageHandler
}

func (h *MessagingHub) handleRegistrations() {
	defer h.wg.Done()
	
	for {
		select {
		case conn := <-h.register:
			h.registerConnection(conn)
		case conn := <-h.unregister:
			h.unregisterConnection(conn)
		case <-h.ctx.Done():
			return
		}
	}
}

func (h *MessagingHub) registerConnection(conn *entities.Connection) {
	userID := conn.UserID()
	
	// Get or create user connections
	value, _ := h.userConnections.LoadOrStore(userID, &UserConnections{
		userID:      userID,
		connections: make(map[entities.ConnectionID]*entities.Connection),
		lastSeen:    time.Now(),
		isOnline:    false,
	})
	
	userConns := value.(*UserConnections)
	userConns.mutex.Lock()
	defer userConns.mutex.Unlock()
	
	// Add connection
	userConns.connections[conn.ID()] = conn
	userConns.isOnline = true
	userConns.lastSeen = time.Now()
	
	// Update metrics
	h.activeConns.Add(1)
	if len(userConns.connections) == 1 {
		h.activeUsers.Add(1)
	}
	
	// Set connection status
	conn.SetStatus(entities.ConnectionStatusConnected)
	
	log.Printf("Connection registered: user=%s, connection=%s, platform=%d, total_connections=%d",
		userID, conn.ID(), conn.PlatformType(), h.activeConns.Load())
}

func (h *MessagingHub) unregisterConnection(conn *entities.Connection) {
	userID := conn.UserID()
	
	if value, ok := h.userConnections.Load(userID); ok {
		userConns := value.(*UserConnections)
		userConns.mutex.Lock()
		defer userConns.mutex.Unlock()
		
		if _, exists := userConns.connections[conn.ID()]; exists {
			delete(userConns.connections, conn.ID())
			h.activeConns.Add(-1)
			
			// Update user online status
			if len(userConns.connections) == 0 {
				userConns.isOnline = false
				h.activeUsers.Add(-1)
			}
			
			userConns.lastSeen = time.Now()
		}
	}
	
	// Close connection
	conn.Close()
	
	log.Printf("Connection unregistered: user=%s, connection=%s, total_connections=%d",
		userID, conn.ID(), h.activeConns.Load())
}

func (h *MessagingHub) handleBroadcasts() {
	defer h.wg.Done()
	
	for {
		select {
		case msg := <-h.broadcast:
			h.processBroadcast(msg)
		case <-h.ctx.Done():
			return
		}
	}
}

func (h *MessagingHub) processBroadcast(msg *BroadcastMessage) {
	var wg sync.WaitGroup
	
	// If no specific recipients, broadcast to all users (simplified approach)
	if len(msg.Recipients) == 0 {
		h.userConnections.Range(func(key, value interface{}) bool {
			userID := key.(uuid.UUID)
			if msg.ExcludeUserID != nil && userID == *msg.ExcludeUserID {
				return true // continue
			}
			
			wg.Add(1)
			go func(uid uuid.UUID) {
				defer wg.Done()
				h.deliverToUser(uid, msg.Message)
			}(userID)
			return true
		})
	} else {
		// Broadcast to specific recipients
		for _, userID := range msg.Recipients {
			if msg.ExcludeUserID != nil && userID == *msg.ExcludeUserID {
				continue
			}
			
			wg.Add(1)
			go func(uid uuid.UUID) {
				defer wg.Done()
				h.deliverToUser(uid, msg.Message)
			}(userID)
		}
	}
	
	wg.Wait()
}

func (h *MessagingHub) handleMessageQueue() {
	defer h.wg.Done()
	
	for {
		select {
		case qMsg := <-h.messageQueue:
			h.deliverToUser(qMsg.UserID, qMsg.Message)
		case <-h.ctx.Done():
			return
		}
	}
}

func (h *MessagingHub) handleKicks() {
	defer h.wg.Done()
	
	for {
		select {
		case kick := <-h.kickHandler:
			h.processKick(kick)
		case <-h.ctx.Done():
			return
		}
	}
}

func (h *MessagingHub) processKick(kick *KickHandler) {
	if value, ok := h.userConnections.Load(kick.UserID); ok {
		userConns := value.(*UserConnections)
		userConns.mutex.Lock()
		defer userConns.mutex.Unlock()
		
		for _, conn := range userConns.connections {
			if kick.DeviceID != "" && conn.DeviceID() != kick.DeviceID {
				continue
			}
			if kick.PlatformID != 0 && conn.PlatformType() != kick.PlatformID {
				continue
			}
			
			// Send kick message before closing
			kickMsg := map[string]interface{}{
				"type":   "kick",
				"reason": kick.Reason,
			}
			
			if data, err := json.Marshal(kickMsg); err == nil {
				conn.SendMessage(data)
			}
			
			// Close connection after a short delay
			go func(c *entities.Connection) {
				time.Sleep(100 * time.Millisecond)
				c.Close()
			}(conn)
		}
	}
}

func (h *MessagingHub) deliverToUser(userID uuid.UUID, message interface{}) {
	connections, exists := h.GetUserConnections(userID)
	if !exists {
		return
	}
	
	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("Failed to marshal message for user %s: %v", userID, err)
		return
	}
	
	for _, conn := range connections {
		if !conn.ShouldReceivePush() {
			continue
		}
		
		if err := conn.SendMessage(data); err != nil {
			log.Printf("Failed to send message to connection %s: %v", conn.ID(), err)
		}
	}
}

func (h *MessagingHub) periodicCleanup() {
	defer h.wg.Done()
	
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			h.cleanupStaleConnections()
		case <-h.ctx.Done():
			return
		}
	}
}

func (h *MessagingHub) cleanupStaleConnections() {
	timeout := 5 * time.Minute
	
	h.userConnections.Range(func(key, value interface{}) bool {
		userConns := value.(*UserConnections)
		userConns.mutex.Lock()
		
		for connID, conn := range userConns.connections {
			if !conn.IsHealthy(timeout) {
				delete(userConns.connections, connID)
				h.activeConns.Add(-1)
				conn.Close()
				log.Printf("Cleaned up stale connection: %s", connID)
			}
		}
		
		// Update user online status
		if len(userConns.connections) == 0 && userConns.isOnline {
			userConns.isOnline = false
			h.activeUsers.Add(-1)
		}
		
		userConns.mutex.Unlock()
		return true
	})
}

type HubStats struct {
	ActiveConnections int64
	ActiveUsers       int64
	MaxConnections    int64
}

// Hub errors
var (
	ErrMaxConnectionsReached = NewHubError("maximum connections reached")
	ErrHubClosed            = NewHubError("hub is closed")
	ErrRegistrationFull     = NewHubError("registration channel full")
)

type HubError struct {
	message string
}

func NewHubError(message string) *HubError {
	return &HubError{message: message}
}

func (e *HubError) Error() string {
	return e.message
}