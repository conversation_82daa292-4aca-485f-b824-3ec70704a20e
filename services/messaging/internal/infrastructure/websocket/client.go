package websocket

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/swork-team/platform/pkg/config"
	"github.com/swork-team/platform/services/messaging/internal/domain/realtime/entities"
)

// Additional WebSocket message types specific to client
const (
	MessageTypeAuth         = "auth"
	MessageTypeNotification = "notification"
	MessageTypeKick         = "kick"
)

// AuthMessage represents authentication data
type AuthMessage struct {
	Token      string `json:"token"`
	DeviceID   string `json:"device_id"`
	Platform   int32  `json:"platform"`
	AppVersion string `json:"app_version,omitempty"`
}

// Client represents a WebSocket client connection handler
type Client struct {
	connection *entities.Connection
	hub        *MessagingHub
	config     *config.MessagingServiceConfig

	// Message handling
	readPump  sync.WaitGroup
	writePump sync.WaitGroup

	// Authentication
	authenticated bool
	authMutex     sync.RWMutex

	// Compression
	enableCompression bool
}

// NewClient creates a new WebSocket client
func NewClient(conn *entities.Connection, hub *MessagingHub, config *config.MessagingServiceConfig) *Client {
	client := &Client{
		connection:        conn,
		hub:               hub,
		config:            config,
		authenticated:     false,
		enableCompression: conn.SupportsCompression(),
	}

	return client
}

// Start begins the client's read and write pump goroutines
func (c *Client) Start() {
	log.Printf("Starting client for connection %s (user: %s)", c.connection.ID(), c.connection.UserID())

	c.readPump.Add(1)
	c.writePump.Add(1)

	go c.writePumpHandler()
	go c.readPumpHandler()
}

// Stop gracefully stops the client
func (c *Client) Stop() {
	log.Printf("Stopping client for connection %s", c.connection.ID())

	c.connection.Close()
	c.readPump.Wait()
	c.writePump.Wait()
}

// IsAuthenticated returns whether the client is authenticated
func (c *Client) IsAuthenticated() bool {
	c.authMutex.RLock()
	defer c.authMutex.RUnlock()
	return c.authenticated
}

// SetAuthenticated sets the authentication status
func (c *Client) SetAuthenticated(authenticated bool) {
	c.authMutex.Lock()
	defer c.authMutex.Unlock()
	c.authenticated = authenticated
}

// readPumpHandler handles reading messages from the WebSocket connection
func (c *Client) readPumpHandler() {
	defer c.readPump.Done()
	defer c.connection.Close()

	// Check for nil pointers
	if c.config == nil {
		log.Printf("Error: config is nil for connection %s", c.connection.ID())
		return
	}
	if c.connection == nil {
		log.Printf("Error: connection is nil")
		return
	}

	// Configure WebSocket settings
	c.configureWebSocket()

	// Start authentication timeout
	authTimer := time.NewTimer(c.config.WebSocket.ReadTimeout)
	defer authTimer.Stop()
	authTimeoutActive := true

	for {
		if authTimeoutActive {
			select {
			case <-c.connection.Context().Done():
				return
			case <-authTimer.C:
				if !c.IsAuthenticated() {
					log.Printf("Authentication timeout for connection %s", c.connection.ID())
					c.sendError("authentication_timeout", "Authentication timeout")
					return
				}
				authTimeoutActive = false
			default:
				// Continue with message reading
			}
		} else {
			select {
			case <-c.connection.Context().Done():
				return
			default:
				// Continue with message reading
			}
		}

		// Set read deadline
		c.connection.WebSocketConn().SetReadDeadline(time.Now().Add(c.config.WebSocket.ReadTimeout))

		// Read message
		_, messageBytes, err := c.connection.WebSocketConn().ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error for connection %s: %v", c.connection.ID(), err)
			}
			return
		}

		// Update activity
		c.connection.UpdateActivity()

		// Parse and handle message
		if err := c.handleMessage(messageBytes); err != nil {
			log.Printf("Error handling message for connection %s: %v", c.connection.ID(), err)
			c.sendError("message_error", err.Error())
		}

		// Disable auth timeout after first successful authentication
		if c.IsAuthenticated() && authTimeoutActive {
			authTimeoutActive = false
		}
	}
}

// writePumpHandler handles writing messages to the WebSocket connection
func (c *Client) writePumpHandler() {
	defer c.writePump.Done()
	defer c.connection.Close()

	// Heartbeat ticker for platforms that require it
	var heartbeatTicker *time.Ticker
	if c.connection.RequiresHeartbeat() {
		heartbeatTicker = time.NewTicker(c.config.WebSocket.PingPeriod)
		defer heartbeatTicker.Stop()
	}

	for {
		if heartbeatTicker != nil {
			select {
			case <-c.connection.Context().Done():
				return

			case message, ok := <-c.connection.SendChannel():
				// Set write deadline
				c.connection.WebSocketConn().SetWriteDeadline(time.Now().Add(c.config.WebSocket.WriteTimeout))

				if !ok {
					// Channel closed
					c.connection.WebSocketConn().WriteMessage(websocket.CloseMessage, []byte{})
					return
				}

				// Send message
				if err := c.writeMessage(message); err != nil {
					log.Printf("Error writing message for connection %s: %v", c.connection.ID(), err)
					return
				}

			case <-heartbeatTicker.C:
				// Send heartbeat
				c.connection.WebSocketConn().SetWriteDeadline(time.Now().Add(c.config.WebSocket.WriteTimeout))
				if err := c.connection.WebSocketConn().WriteMessage(websocket.PingMessage, nil); err != nil {
					log.Printf("Error sending ping for connection %s: %v", c.connection.ID(), err)
					return
				}
				c.connection.UpdateHeartbeat()
			}
		} else {
			select {
			case <-c.connection.Context().Done():
				return

			case message, ok := <-c.connection.SendChannel():
				// Set write deadline
				c.connection.WebSocketConn().SetWriteDeadline(time.Now().Add(c.config.WebSocket.WriteTimeout))

				if !ok {
					// Channel closed
					c.connection.WebSocketConn().WriteMessage(websocket.CloseMessage, []byte{})
					return
				}

				// Send message
				if err := c.writeMessage(message); err != nil {
					log.Printf("Error writing message for connection %s: %v", c.connection.ID(), err)
					return
				}
			}
		}
	}
}

// configureWebSocket sets up WebSocket connection parameters
func (c *Client) configureWebSocket() {
	// Set maximum message size
	c.connection.WebSocketConn().SetReadLimit(c.config.WebSocket.MaxMessageSize)

	// Set pong handler
	c.connection.WebSocketConn().SetPongHandler(func(string) error {
		c.connection.UpdateHeartbeat()
		c.connection.WebSocketConn().SetReadDeadline(time.Now().Add(c.config.WebSocket.PongTimeout))
		return nil
	})

	// Enable compression if supported
	if c.enableCompression && c.config.WebSocket.EnableCompression {
		c.connection.SetCompressed(true)
	}
}

// handleMessage processes incoming WebSocket messages
func (c *Client) handleMessage(messageBytes []byte) error {
	var wsMsg WebSocketMessage
	if err := json.Unmarshal(messageBytes, &wsMsg); err != nil {
		return err
	}

	// Handle message based on type
	switch wsMsg.Type {
	case MessageTypeAuth:
		return c.handleAuth(wsMsg)
	case MessageTypePing:
		return c.handlePing(wsMsg)
	case MessageTypeMessage:
		return c.handleIncomingMessage(wsMsg)
	case MessageTypeSubscribe:
		return c.handleSubscribe(wsMsg)
	case MessageTypeUnsubscribe:
		return c.handleUnsubscribe(wsMsg)
	case MessageTypeTyping:
		return c.handleTyping(wsMsg)
	case MessageTypeReadReceipt:
		return c.handleReadReceipt(wsMsg)
	case MessageTypeUserStatus:
		return c.handleUserStatus(wsMsg)
	case MessageTypeAck:
		return c.handleAck(wsMsg)
	default:
		return entities.ErrInvalidMessage
	}
}

// handleAuth processes authentication messages
func (c *Client) handleAuth(wsMsg WebSocketMessage) error {
	if c.IsAuthenticated() {
		return c.sendError("already_authenticated", "Already authenticated")
	}

	// Parse auth data
	authDataBytes, err := json.Marshal(wsMsg.Data)
	if err != nil {
		return err
	}

	var authMsg AuthMessage
	if err := json.Unmarshal(authDataBytes, &authMsg); err != nil {
		return err
	}

	// Validate token format and basic checks
	if authMsg.Token == "" {
		return c.sendError("invalid_token", "Token is required")
	}

	// Basic JWT format validation (Bearer token without 'Bearer ' prefix)
	tokenParts := strings.Split(authMsg.Token, ".")
	if len(tokenParts) != 3 {
		return c.sendError("invalid_token", "Invalid token format")
	}

	// For production, integrate with auth service
	// For now, accept tokens that look valid (start with 'eyJ' - JWT header)
	if !strings.HasPrefix(authMsg.Token, "eyJ") {
		return c.sendError("invalid_token", "Invalid token")
	}

	// Extract user ID from token (simplified - in production use proper JWT library)
	userID, err := c.extractUserIDFromToken(authMsg.Token)
	if err != nil {
		return c.sendError("invalid_token", "Failed to extract user information")
	}

	// Validate that token user ID matches connection user ID
	if userID != c.connection.UserID() {
		return c.sendError("invalid_token", "Token user ID does not match connection")
	}

	// Set platform type
	platformType := entities.PlatformType(authMsg.Platform)
	if platformType == 0 {
		platformType = entities.PlatformWeb // Default to web
	}

	// Mark as authenticated
	c.SetAuthenticated(true)
	c.connection.SetStatus(entities.ConnectionStatusConnected)

	// Send authentication success
	response := WebSocketMessage{
		Type:      MessageTypeAuth,
		ID:        wsMsg.ID,
		Data:      map[string]interface{}{"status": "authenticated"},
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(response)
}

// handlePing processes ping messages
func (c *Client) handlePing(wsMsg WebSocketMessage) error {
	response := WebSocketMessage{
		Type:      MessageTypePong,
		ID:        wsMsg.ID,
		Timestamp: time.Now().UnixMilli(),
	}

	c.connection.UpdateHeartbeat()
	return c.sendMessage(response)
}

// handleIncomingMessage processes incoming chat messages
func (c *Client) handleIncomingMessage(wsMsg WebSocketMessage) error {
	if !c.IsAuthenticated() {
		return c.sendError("not_authenticated", "Authentication required")
	}

	// Process the message through the message handler
	if handler := c.hub.GetMessageHandler(); handler != nil {
		return handler.ProcessIncomingMessage(context.Background(), c, wsMsg)
	}

	// Fallback: just acknowledge receipt
	response := WebSocketMessage{
		Type:      MessageTypeAck,
		ID:        wsMsg.ID,
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(response)
}

// handleSubscribe processes subscription requests
func (c *Client) handleSubscribe(wsMsg WebSocketMessage) error {
	if !c.IsAuthenticated() {
		return c.sendError("not_authenticated", "Authentication required")
	}

	// Parse subscription data
	if wsMsg.Data == nil {
		return c.sendError("invalid_data", "Subscription data required")
	}

	// Try to parse as SubscribeMessage first
	subscribeDataBytes, err := json.Marshal(wsMsg.Data)
	if err != nil {
		return c.sendError("invalid_data", "Invalid subscription data format")
	}

	var subscribeMsg SubscribeMessage
	if err := json.Unmarshal(subscribeDataBytes, &subscribeMsg); err != nil {
		// Fallback: try to parse as map with topic field for backward compatibility
		dataMap, ok := wsMsg.Data.(map[string]interface{})
		if !ok {
			return c.sendError("invalid_data", "Invalid subscription data")
		}

		// Support both "conversation_id" and "topic" fields for compatibility
		if conversationID, ok := dataMap["conversation_id"].(string); ok && conversationID != "" {
			subscribeMsg.ConversationID = conversationID
		} else if topic, ok := dataMap["topic"].(string); ok && topic != "" {
			subscribeMsg.ConversationID = topic
		} else {
			return c.sendError("invalid_data", "conversation_id required")
		}

		// Optional event types
		if eventTypes, ok := dataMap["event_types"].([]interface{}); ok {
			subscribeMsg.EventTypes = make([]string, len(eventTypes))
			for i, et := range eventTypes {
				if etStr, ok := et.(string); ok {
					subscribeMsg.EventTypes[i] = etStr
				}
			}
		}
	}

	if subscribeMsg.ConversationID == "" {
		return c.sendError("invalid_data", "conversation_id required")
	}

	// TODO: Validate user has access to the conversation
	// For now, subscribe to the conversation topic
	topic := fmt.Sprintf("conversation:%s", subscribeMsg.ConversationID)
	c.connection.Subscribe(topic)

	// Send confirmation
	response := WebSocketMessage{
		Type: MessageTypeAck,
		ID:   wsMsg.ID,
		Data: map[string]interface{}{
			"subscribed":      subscribeMsg.ConversationID,
			"conversation_id": subscribeMsg.ConversationID,
			"event_types":     subscribeMsg.EventTypes,
		},
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(response)
}

// handleUnsubscribe processes unsubscription requests
func (c *Client) handleUnsubscribe(wsMsg WebSocketMessage) error {
	if !c.IsAuthenticated() {
		return c.sendError("not_authenticated", "Authentication required")
	}

	// Parse unsubscription data
	if wsMsg.Data == nil {
		return c.sendError("invalid_data", "Unsubscription data required")
	}

	// Try to parse as UnsubscribeMessage first
	unsubscribeDataBytes, err := json.Marshal(wsMsg.Data)
	if err != nil {
		return c.sendError("invalid_data", "Invalid unsubscription data format")
	}

	var unsubscribeMsg UnsubscribeMessage
	if err := json.Unmarshal(unsubscribeDataBytes, &unsubscribeMsg); err != nil {
		// Fallback: try to parse as map with topic field for backward compatibility
		dataMap, ok := wsMsg.Data.(map[string]interface{})
		if !ok {
			return c.sendError("invalid_data", "Invalid unsubscription data")
		}

		// Support both "conversation_id" and "topic" fields for compatibility
		if conversationID, ok := dataMap["conversation_id"].(string); ok && conversationID != "" {
			unsubscribeMsg.ConversationID = conversationID
		} else if topic, ok := dataMap["topic"].(string); ok && topic != "" {
			unsubscribeMsg.ConversationID = topic
		} else {
			return c.sendError("invalid_data", "conversation_id required")
		}
	}

	if unsubscribeMsg.ConversationID == "" {
		return c.sendError("invalid_data", "conversation_id required")
	}

	// Unsubscribe from conversation topic
	topic := fmt.Sprintf("conversation:%s", unsubscribeMsg.ConversationID)
	c.connection.Unsubscribe(topic)

	// Send confirmation
	response := WebSocketMessage{
		Type: MessageTypeAck,
		ID:   wsMsg.ID,
		Data: map[string]interface{}{
			"unsubscribed":    unsubscribeMsg.ConversationID,
			"conversation_id": unsubscribeMsg.ConversationID,
		},
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(response)
}

// handleTyping processes typing indicator messages
func (c *Client) handleTyping(wsMsg WebSocketMessage) error {
	if !c.IsAuthenticated() {
		return c.sendError("not_authenticated", "Authentication required")
	}

	// Parse typing data
	if wsMsg.Data == nil {
		return c.sendError("invalid_data", "Typing data required")
	}

	dataMap, ok := wsMsg.Data.(map[string]interface{})
	if !ok {
		return c.sendError("invalid_data", "Invalid typing data")
	}

	conversationID, ok := dataMap["conversation_id"].(string)
	if !ok || conversationID == "" {
		return c.sendError("invalid_data", "conversation_id required")
	}

	isTyping, ok := dataMap["is_typing"].(bool)
	if !ok {
		return c.sendError("invalid_data", "is_typing required")
	}

	// TODO: Broadcast typing indicator to other participants in the conversation
	// For now, just acknowledge receipt
	response := WebSocketMessage{
		Type: MessageTypeAck,
		ID:   wsMsg.ID,
		Data: map[string]interface{}{
			"typing_received": true,
			"is_typing":       isTyping,
		},
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(response)
}

// handleReadReceipt processes read receipt messages
func (c *Client) handleReadReceipt(wsMsg WebSocketMessage) error {
	if !c.IsAuthenticated() {
		return c.sendError("not_authenticated", "Authentication required")
	}

	// Parse read receipt data
	if wsMsg.Data == nil {
		return c.sendError("invalid_data", "Read receipt data required")
	}

	dataMap, ok := wsMsg.Data.(map[string]interface{})
	if !ok {
		return c.sendError("invalid_data", "Invalid read receipt data")
	}

	conversationID, ok := dataMap["conversation_id"].(string)
	if !ok || conversationID == "" {
		return c.sendError("invalid_data", "conversation_id required")
	}

	// TODO: Update read status in database and broadcast to other participants
	// For now, just acknowledge receipt
	response := WebSocketMessage{
		Type:      MessageTypeAck,
		ID:        wsMsg.ID,
		Data:      map[string]interface{}{"read_receipt_received": true},
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(response)
}

// handleUserStatus processes user status update messages
func (c *Client) handleUserStatus(wsMsg WebSocketMessage) error {
	if !c.IsAuthenticated() {
		return c.sendError("not_authenticated", "Authentication required")
	}

	// Parse user status data
	if wsMsg.Data == nil {
		return c.sendError("invalid_data", "User status data required")
	}

	dataMap, ok := wsMsg.Data.(map[string]interface{})
	if !ok {
		return c.sendError("invalid_data", "Invalid user status data")
	}

	status, ok := dataMap["status"].(string)
	if !ok || status == "" {
		return c.sendError("invalid_data", "status required")
	}

	// Validate status values
	validStatuses := map[string]bool{
		"online":  true,
		"offline": true,
		"away":    true,
		"busy":    true,
	}

	if !validStatuses[status] {
		return c.sendError("invalid_data", "invalid status value")
	}

	// TODO: Update user status in cache and broadcast to relevant connections
	// For now, just acknowledge receipt
	response := WebSocketMessage{
		Type:      MessageTypeAck,
		ID:        wsMsg.ID,
		Data:      map[string]interface{}{"status_updated": true},
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(response)
}

// handleAck processes acknowledgment messages
func (c *Client) handleAck(wsMsg WebSocketMessage) error {
	// TODO: Handle message acknowledgments for delivery tracking
	return nil
}

// writeMessage writes a message to the WebSocket connection
func (c *Client) writeMessage(messageBytes []byte) error {
	if c.connection.IsCompressed() {
		// TODO: Implement compression
		return c.connection.WebSocketConn().WriteMessage(websocket.TextMessage, messageBytes)
	}

	return c.connection.WebSocketConn().WriteMessage(websocket.TextMessage, messageBytes)
}

// sendMessage sends a WebSocket message to the client
func (c *Client) sendMessage(wsMsg WebSocketMessage) error {
	messageBytes, err := json.Marshal(wsMsg)
	if err != nil {
		return err
	}

	return c.connection.SendMessage(messageBytes)
}

// sendError sends an error message to the client
func (c *Client) sendError(errorCode, errorMessage string) error {
	wsMsg := WebSocketMessage{
		Type: MessageTypeError,
		Data: map[string]interface{}{
			"code":    errorCode,
			"message": errorMessage,
		},
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(wsMsg)
}

// SendNotification sends a notification to the client
func (c *Client) SendNotification(notification interface{}) error {
	if !c.IsAuthenticated() {
		return entities.ErrAuthenticationFailed
	}

	wsMsg := WebSocketMessage{
		Type:      MessageTypeNotification,
		Data:      notification,
		Timestamp: time.Now().UnixMilli(),
	}

	return c.sendMessage(wsMsg)
}

// SendKick sends a kick message and closes the connection
func (c *Client) SendKick(reason string) error {
	wsMsg := WebSocketMessage{
		Type: MessageTypeKick,
		Data: map[string]interface{}{
			"reason": reason,
		},
		Timestamp: time.Now().UnixMilli(),
	}

	// Send kick message
	if err := c.sendMessage(wsMsg); err != nil {
		return err
	}

	// Close connection after a brief delay
	go func() {
		time.Sleep(100 * time.Millisecond)
		c.connection.Close()
	}()

	return nil
}

// extractUserIDFromToken extracts user ID from JWT token (simplified implementation)
func (c *Client) extractUserIDFromToken(token string) (uuid.UUID, error) {
	// Split JWT token into parts
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return uuid.UUID{}, fmt.Errorf("invalid token format")
	}

	// Decode payload (second part)
	payload, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return uuid.UUID{}, fmt.Errorf("failed to decode token payload: %w", err)
	}

	// Parse JSON payload
	var claims map[string]interface{}
	if err := json.Unmarshal(payload, &claims); err != nil {
		return uuid.UUID{}, fmt.Errorf("failed to parse token claims: %w", err)
	}

	// Extract user ID (try both 'user_id' and 'sub' fields)
	var userIDStr string
	if uid, ok := claims["user_id"].(string); ok {
		userIDStr = uid
	} else if sub, ok := claims["sub"].(string); ok {
		userIDStr = sub
	} else {
		return uuid.UUID{}, fmt.Errorf("user_id not found in token")
	}

	// Parse as UUID
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return uuid.UUID{}, fmt.Errorf("invalid user ID format: %w", err)
	}

	return userID, nil
}
