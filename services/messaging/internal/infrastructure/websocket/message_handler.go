package websocket

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	conversationservices "github.com/swork-team/platform/services/messaging/internal/domain/conversation/services"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/services"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
)

// MessageHandler handles WebSocket message operations
type MessageHandler struct {
	messageService      services.MessageService
	conversationService conversationservices.ConversationService
	hub                 *MessagingHub
}

// NewMessageHandler creates a new message handler
func NewMessageHandler(messageService services.MessageService, conversationService conversationservices.ConversationService, hub *MessagingHub) *MessageHandler {
	return &MessageHandler{
		messageService:      messageService,
		conversationService: conversationService,
		hub:                 hub,
	}
}

// IncomingMessageData represents the structure of incoming message data
type IncomingMessageData struct {
	ConversationID string                 `json:"conversation_id"`
	Content        MessageContentData     `json:"content"`
	MessageType    string                 `json:"message_type,omitempty"`
	ThreadID       *string                `json:"thread_id,omitempty"`
	ReplyToID      *string                `json:"reply_to_id,omitempty"`
	ClientID       string                 `json:"client_id,omitempty"` // Client-side message ID for deduplication
}

// MessageContentData represents message content from WebSocket
type MessageContentData struct {
	Text        string                     `json:"text"`
	Attachments []MessageAttachmentData    `json:"attachments,omitempty"`
	Mentions    []string                   `json:"mentions,omitempty"`
}

// MessageAttachmentData represents attachment data from WebSocket
type MessageAttachmentData struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	URL      string `json:"url"`
	MimeType string `json:"mime_type"`
	Size     int64  `json:"size"`
}

// OutgoingMessageData represents the structure of outgoing message data
type OutgoingMessageData struct {
	ID             string                 `json:"id"`
	ConversationID string                 `json:"conversation_id"`
	SenderID       string                 `json:"sender_id"`
	Content        MessageContentData     `json:"content"`
	MessageType    string                 `json:"message_type"`
	Sequence       int64                  `json:"sequence"`
	ThreadID       *string                `json:"thread_id,omitempty"`
	ReplyToID      *string                `json:"reply_to_id,omitempty"`
	Status         string                 `json:"status"`
	Timestamp      int64                  `json:"timestamp"`
	EditedAt       *int64                 `json:"edited_at,omitempty"`
	ClientID       string                 `json:"client_id,omitempty"`
}

// ProcessIncomingMessage processes an incoming message from WebSocket
func (mh *MessageHandler) ProcessIncomingMessage(ctx context.Context, client *Client, wsMsg WebSocketMessage) error {
	// Parse message data
	msgDataBytes, err := json.Marshal(wsMsg.Data)
	if err != nil {
		return err
	}
	
	var msgData IncomingMessageData
	if err := json.Unmarshal(msgDataBytes, &msgData); err != nil {
		return err
	}
	
	// Validate required fields
	if msgData.ConversationID == "" {
		return client.sendError("invalid_conversation", "Conversation ID is required")
	}
	
	if msgData.Content.Text == "" && len(msgData.Content.Attachments) == 0 {
		return client.sendError("empty_message", "Message content cannot be empty")
	}
	
	// Parse conversation ID
	conversationID, err := uuid.Parse(msgData.ConversationID)
	if err != nil {
		return client.sendError("invalid_conversation_id", "Invalid conversation ID format")
	}
	
	// Create message content
	content, err := mh.createMessageContent(msgData.Content)
	if err != nil {
		return client.sendError("invalid_content", err.Error())
	}
	
	// Determine message type
	messageType := mh.parseMessageType(msgData.MessageType)
	
	// Create service request
	serviceRequest := &services.SendMessageRequest{
		ConversationID: conversationentities.ConversationID(conversationID),
		SenderID:       client.connection.UserID(),
		Content:        content,
		MessageType:    messageType,
	}
	
	// Handle thread and reply IDs
	if msgData.ThreadID != nil {
		threadID, err := uuid.Parse(*msgData.ThreadID)
		if err != nil {
			return client.sendError("invalid_thread_id", "Invalid thread ID format")
		}
		threadMessageID := entities.MessageID(threadID)
		serviceRequest.ThreadID = &threadMessageID
	}
	
	if msgData.ReplyToID != nil {
		replyID, err := uuid.Parse(*msgData.ReplyToID)
		if err != nil {
			return client.sendError("invalid_reply_id", "Invalid reply ID format")
		}
		replyMessageID := entities.MessageID(replyID)
		serviceRequest.ReplyToID = &replyMessageID
	}
	
	// Send message through service
	message, err := mh.messageService.SendMessage(ctx, serviceRequest)
	if err != nil {
		log.Printf("Error sending message via service: %v", err)
		return client.sendError("send_failed", "Failed to send message")
	}
	
	// Send acknowledgment to sender
	ackData := OutgoingMessageData{
		ID:             message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		Content:        mh.convertMessageContent(message.Content()),
		MessageType:    string(message.MessageType()),
		Sequence:       message.Sequence(),
		Status:         string(message.Status()),
		Timestamp:      message.Timestamp().UnixMilli(),
		ClientID:       msgData.ClientID,
	}
	
	if message.ThreadID() != nil {
		threadID := message.ThreadID().String()
		ackData.ThreadID = &threadID
	}
	
	if message.ReplyToID() != nil {
		replyToID := message.ReplyToID().String()
		ackData.ReplyToID = &replyToID
	}
	
	ackResponse := WebSocketMessage{
		Type:      MessageTypeAck,
		ID:        wsMsg.ID,
		Data:      ackData,
		Timestamp: time.Now().UnixMilli(),
	}
	
	if err := client.sendMessage(ackResponse); err != nil {
		log.Printf("Error sending ack to client: %v", err)
	}
	
	// Broadcast message to conversation participants
	go mh.broadcastMessage(ctx, message, msgData.ClientID)
	
	return nil
}

// BroadcastMessage broadcasts a message to all relevant clients
func (mh *MessageHandler) BroadcastMessage(ctx context.Context, message *entities.Message) {
	mh.broadcastMessage(ctx, message, "")
}

// broadcastMessage handles the actual broadcasting logic
func (mh *MessageHandler) broadcastMessage(ctx context.Context, message *entities.Message, clientID string) {
	// Create broadcast message data
	msgData := OutgoingMessageData{
		ID:             message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		Content:        mh.convertMessageContent(message.Content()),
		MessageType:    string(message.MessageType()),
		Sequence:       message.Sequence(),
		Status:         string(message.Status()),
		Timestamp:      message.Timestamp().UnixMilli(),
		ClientID:       clientID,
	}
	
	if message.ThreadID() != nil {
		threadID := message.ThreadID().String()
		msgData.ThreadID = &threadID
	}
	
	if message.ReplyToID() != nil {
		replyToID := message.ReplyToID().String()
		msgData.ReplyToID = &replyToID
	}
	
	if message.EditedAt() != nil {
		editedAt := message.EditedAt().UnixMilli()
		msgData.EditedAt = &editedAt
	}
	
	// Create WebSocket message
	wsMsg := WebSocketMessage{
		Type:      MessageTypeMessage,
		Data:      msgData,
		Timestamp: time.Now().UnixMilli(),
		Metadata: map[string]interface{}{
			"conversation_id": message.ConversationID().String(),
			"message_type":    string(message.MessageType()),
		},
	}
	
	// Get conversation participants
	participantIDs, err := mh.conversationService.GetConversationParticipants(ctx, message.ConversationID())
	if err != nil {
		log.Printf("Error getting conversation participants: %v", err)
		// Fall back to broadcasting to all users
		participantIDs = []uuid.UUID{}
	}
	
	// Broadcast to hub
	senderID := message.SenderID()
	broadcastMsg := &BroadcastMessage{
		ConversationID: message.ConversationID().String(),
		Message:        wsMsg,
		Recipients:     participantIDs,
		ExcludeUserID:  &senderID, // Don't send back to sender
		DeliveryMode:   DeliveryModeRealTime,
	}
	
	mh.hub.BroadcastMessage(broadcastMsg)
}

// BroadcastMessageEdit broadcasts a message edit notification
func (mh *MessageHandler) BroadcastMessageEdit(ctx context.Context, message *entities.Message) {
	msgData := OutgoingMessageData{
		ID:             message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		Content:        mh.convertMessageContent(message.Content()),
		MessageType:    string(message.MessageType()),
		Sequence:       message.Sequence(),
		Status:         string(message.Status()),
		Timestamp:      message.Timestamp().UnixMilli(),
	}
	
	if message.EditedAt() != nil {
		editedAt := message.EditedAt().UnixMilli()
		msgData.EditedAt = &editedAt
	}
	
	wsMsg := WebSocketMessage{
		Type:      "message_edited",
		Data:      msgData,
		Timestamp: time.Now().UnixMilli(),
		Metadata: map[string]interface{}{
			"conversation_id": message.ConversationID().String(),
			"action":         "edit",
		},
	}
	
	broadcastMsg := &BroadcastMessage{
		ConversationID: message.ConversationID().String(),
		Message:        wsMsg,
		DeliveryMode:   DeliveryModeRealTime,
	}
	
	mh.hub.BroadcastMessage(broadcastMsg)
}

// BroadcastMessageDelete broadcasts a message deletion notification
func (mh *MessageHandler) BroadcastMessageDelete(ctx context.Context, messageID entities.MessageID, conversationID conversationentities.ConversationID, userID uuid.UUID) {
	wsMsg := WebSocketMessage{
		Type: "message_deleted",
		Data: map[string]interface{}{
			"id":              messageID.String(),
			"conversation_id": conversationID.String(),
			"deleted_by":      userID.String(),
		},
		Timestamp: time.Now().UnixMilli(),
		Metadata: map[string]interface{}{
			"conversation_id": conversationID.String(),
			"action":         "delete",
		},
	}
	
	broadcastMsg := &BroadcastMessage{
		ConversationID: conversationID.String(),
		Message:        wsMsg,
		DeliveryMode:   DeliveryModeRealTime,
	}
	
	mh.hub.BroadcastMessage(broadcastMsg)
}

// BroadcastReadStatus broadcasts read status updates
func (mh *MessageHandler) BroadcastReadStatus(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64) {
	wsMsg := WebSocketMessage{
		Type: "read_status",
		Data: map[string]interface{}{
			"conversation_id": conversationID.String(),
			"user_id":        userID.String(),
			"sequence":       sequence,
			"read_at":        time.Now().UnixMilli(),
		},
		Timestamp: time.Now().UnixMilli(),
		Metadata: map[string]interface{}{
			"conversation_id": conversationID.String(),
			"action":         "read",
		},
	}
	
	broadcastMsg := &BroadcastMessage{
		ConversationID: conversationID.String(),
		Message:        wsMsg,
		ExcludeUserID:  &userID, // Don't send back to the user who read it
		DeliveryMode:   DeliveryModeRealTime,
	}
	
	mh.hub.BroadcastMessage(broadcastMsg)
}

// Helper methods

// createMessageContent converts WebSocket message content to domain value object
func (mh *MessageHandler) createMessageContent(content MessageContentData) (valueobjects.MessageContent, error) {
	// Convert attachments
	attachments := make([]valueobjects.MessageAttachment, len(content.Attachments))
	for i, att := range content.Attachments {
		attachments[i] = valueobjects.NewMessageAttachment(
			att.ID,
			att.Name,
			att.URL,
			att.MimeType,
			att.Size,
		)
	}
	
	// Create content based on whether there are attachments
	var msgContent valueobjects.MessageContent
	var err error
	
	if len(attachments) > 0 {
		msgContent, err = valueobjects.NewAttachmentMessageContent(attachments, content.Text)
	} else {
		msgContent, err = valueobjects.NewTextMessageContent(content.Text)
	}
	
	if err != nil {
		return msgContent, err
	}
	
	// Add mentions
	for _, mention := range content.Mentions {
		msgContent.AddMention(mention)
	}
	
	return msgContent, nil
}

// convertMessageContent converts domain message content to WebSocket format
func (mh *MessageHandler) convertMessageContent(content valueobjects.MessageContent) MessageContentData {
	attachments := make([]MessageAttachmentData, len(content.Attachments()))
	for i, att := range content.Attachments() {
		attachments[i] = MessageAttachmentData{
			ID:       att.ID(),
			Name:     att.Name(),
			URL:      att.URL(),
			MimeType: att.MimeType(),
			Size:     att.Size(),
		}
	}
	
	return MessageContentData{
		Text:        content.Text(),
		Attachments: attachments,
		Mentions:    content.Mentions(),
	}
}

// parseMessageType converts string message type to domain enum
func (mh *MessageHandler) parseMessageType(msgType string) entities.MessageType {
	switch msgType {
	case "text":
		return entities.MessageTypeText
	case "image":
		return entities.MessageTypeImage
	case "file":
		return entities.MessageTypeFile
	case "audio":
		return entities.MessageTypeAudio
	case "video":
		return entities.MessageTypeVideo
	case "location":
		return entities.MessageTypeLocation
	case "system":
		return entities.MessageTypeSystem
	default:
		return entities.MessageTypeText
	}
}