package websocket

import (
	"time"
)

// WebSocket message types
const (
	MessageTypeMessage        = "message"
	MessageTypeAck           = "ack"
	MessageTypePing          = "ping"
	MessageTypePong          = "pong"
	MessageTypeError         = "error"
	MessageTypeSubscribe     = "subscribe"
	MessageTypeUnsubscribe   = "unsubscribe"
	MessageTypeTyping        = "typing"
	MessageTypeReadReceipt   = "read_receipt"
	MessageTypeUserStatus    = "user_status"
	MessageTypeJoinRoom      = "join_room"
	MessageTypeLeaveRoom     = "leave_room"
)

// WebSocketMessage represents a message sent over WebSocket
type WebSocketMessage struct {
	Type      string                 `json:"type"`
	ID        string                 `json:"id,omitempty"`
	Data      interface{}            `json:"data,omitempty"`
	Timestamp int64                  `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ErrorMessage represents an error response
type ErrorMessage struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// SubscribeMessage represents a subscription request
type SubscribeMessage struct {
	ConversationID string `json:"conversation_id"`
	EventTypes     []string `json:"event_types,omitempty"` // Optional: specific event types
}

// UnsubscribeMessage represents an unsubscription request
type UnsubscribeMessage struct {
	ConversationID string `json:"conversation_id"`
}

// TypingMessage represents typing indicator data
type TypingMessage struct {
	ConversationID string `json:"conversation_id"`
	UserID         string `json:"user_id"`
	IsTyping       bool   `json:"is_typing"`
	ExpiresAt      int64  `json:"expires_at"`
}

// ReadReceiptMessage represents read receipt data
type ReadReceiptMessage struct {
	ConversationID string `json:"conversation_id"`
	MessageID      string `json:"message_id,omitempty"`
	UpToSequence   *int64 `json:"up_to_sequence,omitempty"`
}

// UserStatusMessage represents user status updates
type UserStatusMessage struct {
	UserID   string `json:"user_id"`
	Status   string `json:"status"` // online, offline, away, busy
	DeviceID string `json:"device_id,omitempty"`
}

// PingMessage represents a ping message
type PingMessage struct {
	Timestamp int64 `json:"timestamp"`
}

// PongMessage represents a pong response
type PongMessage struct {
	Timestamp int64 `json:"timestamp"`
}

// Helper functions

// NewWebSocketMessage creates a new WebSocket message
func NewWebSocketMessage(msgType string, data interface{}) WebSocketMessage {
	return WebSocketMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}
}

// NewErrorMessage creates a new error message
func NewErrorMessage(code, message, details string) WebSocketMessage {
	return NewWebSocketMessage(MessageTypeError, ErrorMessage{
		Code:    code,
		Message: message,
		Details: details,
	})
}

// NewPingMessage creates a new ping message
func NewPingMessage() WebSocketMessage {
	return NewWebSocketMessage(MessageTypePing, PingMessage{
		Timestamp: time.Now().UnixMilli(),
	})
}

// NewPongMessage creates a new pong message
func NewPongMessage() WebSocketMessage {
	return NewWebSocketMessage(MessageTypePong, PongMessage{
		Timestamp: time.Now().UnixMilli(),
	})
}

// NewSubscribeMessage creates a new subscribe message
func NewSubscribeMessage(conversationID string, eventTypes []string) WebSocketMessage {
	return NewWebSocketMessage(MessageTypeSubscribe, SubscribeMessage{
		ConversationID: conversationID,
		EventTypes:     eventTypes,
	})
}

// NewUnsubscribeMessage creates a new unsubscribe message
func NewUnsubscribeMessage(conversationID string) WebSocketMessage {
	return NewWebSocketMessage(MessageTypeUnsubscribe, UnsubscribeMessage{
		ConversationID: conversationID,
	})
}

// NewTypingMessage creates a new typing indicator message
func NewTypingMessage(conversationID, userID string, isTyping bool) WebSocketMessage {
	expiresAt := time.Now().Add(5 * time.Second).UnixMilli() // Typing expires in 5 seconds
	return NewWebSocketMessage(MessageTypeTyping, TypingMessage{
		ConversationID: conversationID,
		UserID:         userID,
		IsTyping:       isTyping,
		ExpiresAt:      expiresAt,
	})
}

// NewReadReceiptMessage creates a new read receipt message
func NewReadReceiptMessage(conversationID string, messageID *string, upToSequence *int64) WebSocketMessage {
	receipt := ReadReceiptMessage{
		ConversationID: conversationID,
		UpToSequence:   upToSequence,
	}
	if messageID != nil {
		receipt.MessageID = *messageID
	}
	return NewWebSocketMessage(MessageTypeReadReceipt, receipt)
}

// NewUserStatusMessage creates a new user status message
func NewUserStatusMessage(userID, status, deviceID string) WebSocketMessage {
	return NewWebSocketMessage(MessageTypeUserStatus, UserStatusMessage{
		UserID:   userID,
		Status:   status,
		DeviceID: deviceID,
	})
}

// Constants for user status
const (
	UserStatusOnline  = "online"
	UserStatusOffline = "offline"
	UserStatusAway    = "away"
	UserStatusBusy    = "busy"
)