package queue

import (
	"sync"
	"time"
)

// QueueMetrics tracks queue performance and statistics
type QueueMetrics struct {
	// Job counters by type
	enqueued    map[string]int64
	processing  map[string]int64
	successful  map[string]int64
	failed      map[string]int64
	deadLetter  map[string]int64
	
	// Processing times by job type
	processingTimes map[string][]time.Duration
	
	// Queue state
	queueSizes   []int
	workerCount  int64
	
	// Performance metrics
	avgProcessingTime map[string]time.Duration
	peakQueueSize     int
	totalThroughput   int64
	
	// Time-based metrics
	startTime    time.Time
	lastUpdated  time.Time
	
	// Mutex for thread safety
	mu sync.RWMutex
}

// NewQueueMetrics creates a new metrics instance
func NewQueueMetrics() *QueueMetrics {
	return &QueueMetrics{
		enqueued:          make(map[string]int64),
		processing:        make(map[string]int64),
		successful:        make(map[string]int64),
		failed:            make(map[string]int64),
		deadLetter:        make(map[string]int64),
		processingTimes:   make(map[string][]time.Duration),
		avgProcessingTime: make(map[string]time.Duration),
		startTime:         time.Now(),
		lastUpdated:       time.Now(),
	}
}

// IncrementEnqueued increments the enqueued counter for a job type
func (m *QueueMetrics) IncrementEnqueued(jobType string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.enqueued[jobType]++
	m.totalThroughput++
	m.lastUpdated = time.Now()
}

// IncrementProcessing increments the processing counter for a job type
func (m *QueueMetrics) IncrementProcessing(jobType string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.processing[jobType]++
	m.lastUpdated = time.Now()
}

// IncrementSuccessful increments the successful counter for a job type
func (m *QueueMetrics) IncrementSuccessful(jobType string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.successful[jobType]++
	if m.processing[jobType] > 0 {
		m.processing[jobType]--
	}
	m.lastUpdated = time.Now()
}

// IncrementFailed increments the failed counter for a job type
func (m *QueueMetrics) IncrementFailed(jobType string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.failed[jobType]++
	if m.processing[jobType] > 0 {
		m.processing[jobType]--
	}
	m.lastUpdated = time.Now()
}

// IncrementDeadLetter increments the dead letter counter for a job type
func (m *QueueMetrics) IncrementDeadLetter(jobType string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.deadLetter[jobType]++
	m.lastUpdated = time.Now()
}

// RecordProcessingTime records the processing time for a job type
func (m *QueueMetrics) RecordProcessingTime(jobType string, duration time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Keep only the last 100 processing times for average calculation
	times := m.processingTimes[jobType]
	if len(times) >= 100 {
		times = times[1:]
	}
	times = append(times, duration)
	m.processingTimes[jobType] = times
	
	// Calculate new average
	var total time.Duration
	for _, t := range times {
		total += t
	}
	m.avgProcessingTime[jobType] = total / time.Duration(len(times))
	
	m.lastUpdated = time.Now()
}

// UpdateQueueSizes updates the current queue sizes
func (m *QueueMetrics) UpdateQueueSizes(sizes []int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.queueSizes = make([]int, len(sizes))
	copy(m.queueSizes, sizes)
	
	// Update peak queue size
	total := 0
	for _, size := range sizes {
		total += size
	}
	if total > m.peakQueueSize {
		m.peakQueueSize = total
	}
	
	m.lastUpdated = time.Now()
}

// UpdateWorkerCount updates the current worker count
func (m *QueueMetrics) UpdateWorkerCount(count int64) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.workerCount = count
	m.lastUpdated = time.Now()
}

// GetJobTypeMetrics returns metrics for a specific job type
func (m *QueueMetrics) GetJobTypeMetrics(jobType string) JobTypeMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	return JobTypeMetrics{
		JobType:           jobType,
		Enqueued:          m.enqueued[jobType],
		Processing:        m.processing[jobType],
		Successful:        m.successful[jobType],
		Failed:            m.failed[jobType],
		DeadLetter:        m.deadLetter[jobType],
		AvgProcessingTime: m.avgProcessingTime[jobType],
		SuccessRate:       m.calculateSuccessRate(jobType),
	}
}

// GetAllJobTypeMetrics returns metrics for all job types
func (m *QueueMetrics) GetAllJobTypeMetrics() map[string]JobTypeMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	metrics := make(map[string]JobTypeMetrics)
	
	// Collect all job types
	jobTypes := make(map[string]bool)
	for jobType := range m.enqueued {
		jobTypes[jobType] = true
	}
	for jobType := range m.processing {
		jobTypes[jobType] = true
	}
	for jobType := range m.successful {
		jobTypes[jobType] = true
	}
	for jobType := range m.failed {
		jobTypes[jobType] = true
	}
	
	// Create metrics for each job type
	for jobType := range jobTypes {
		metrics[jobType] = JobTypeMetrics{
			JobType:           jobType,
			Enqueued:          m.enqueued[jobType],
			Processing:        m.processing[jobType],
			Successful:        m.successful[jobType],
			Failed:            m.failed[jobType],
			DeadLetter:        m.deadLetter[jobType],
			AvgProcessingTime: m.avgProcessingTime[jobType],
			SuccessRate:       m.calculateSuccessRate(jobType),
		}
	}
	
	return metrics
}

// GetOverallMetrics returns overall queue metrics
func (m *QueueMetrics) GetOverallMetrics() OverallMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	totalEnqueued := int64(0)
	totalProcessing := int64(0)
	totalSuccessful := int64(0)
	totalFailed := int64(0)
	totalDeadLetter := int64(0)
	
	for _, count := range m.enqueued {
		totalEnqueued += count
	}
	for _, count := range m.processing {
		totalProcessing += count
	}
	for _, count := range m.successful {
		totalSuccessful += count
	}
	for _, count := range m.failed {
		totalFailed += count
	}
	for _, count := range m.deadLetter {
		totalDeadLetter += count
	}
	
	currentQueueSize := 0
	for _, size := range m.queueSizes {
		currentQueueSize += size
	}
	
	uptime := time.Since(m.startTime)
	throughputPerSecond := float64(m.totalThroughput) / uptime.Seconds()
	
	var overallSuccessRate float64
	totalCompleted := totalSuccessful + totalFailed
	if totalCompleted > 0 {
		overallSuccessRate = float64(totalSuccessful) / float64(totalCompleted)
	}
	
	return OverallMetrics{
		TotalEnqueued:       totalEnqueued,
		TotalProcessing:     totalProcessing,
		TotalSuccessful:     totalSuccessful,
		TotalFailed:         totalFailed,
		TotalDeadLetter:     totalDeadLetter,
		CurrentQueueSize:    currentQueueSize,
		PeakQueueSize:       m.peakQueueSize,
		WorkerCount:         m.workerCount,
		TotalThroughput:     m.totalThroughput,
		ThroughputPerSecond: throughputPerSecond,
		OverallSuccessRate:  overallSuccessRate,
		Uptime:              uptime,
		StartTime:           m.startTime,
		LastUpdated:         m.lastUpdated,
	}
}

// calculateSuccessRate calculates the success rate for a job type
func (m *QueueMetrics) calculateSuccessRate(jobType string) float64 {
	successful := m.successful[jobType]
	failed := m.failed[jobType]
	total := successful + failed
	
	if total == 0 {
		return 0.0
	}
	
	return float64(successful) / float64(total)
}

// Reset clears all metrics
func (m *QueueMetrics) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.enqueued = make(map[string]int64)
	m.processing = make(map[string]int64)
	m.successful = make(map[string]int64)
	m.failed = make(map[string]int64)
	m.deadLetter = make(map[string]int64)
	m.processingTimes = make(map[string][]time.Duration)
	m.avgProcessingTime = make(map[string]time.Duration)
	m.peakQueueSize = 0
	m.totalThroughput = 0
	m.startTime = time.Now()
	m.lastUpdated = time.Now()
}

// GetRecentProcessingTimes returns recent processing times for a job type
func (m *QueueMetrics) GetRecentProcessingTimes(jobType string, limit int) []time.Duration {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	times := m.processingTimes[jobType]
	if len(times) == 0 {
		return []time.Duration{}
	}
	
	if limit <= 0 || limit > len(times) {
		limit = len(times)
	}
	
	result := make([]time.Duration, limit)
	start := len(times) - limit
	copy(result, times[start:])
	
	return result
}

// GetProcessingTimePercentiles calculates percentiles for processing times
func (m *QueueMetrics) GetProcessingTimePercentiles(jobType string) ProcessingTimePercentiles {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	times := m.processingTimes[jobType]
	if len(times) == 0 {
		return ProcessingTimePercentiles{}
	}
	
	// Sort times for percentile calculation
	sortedTimes := make([]time.Duration, len(times))
	copy(sortedTimes, times)
	
	// Simple insertion sort for small arrays
	for i := 1; i < len(sortedTimes); i++ {
		key := sortedTimes[i]
		j := i - 1
		for j >= 0 && sortedTimes[j] > key {
			sortedTimes[j+1] = sortedTimes[j]
			j--
		}
		sortedTimes[j+1] = key
	}
	
	return ProcessingTimePercentiles{
		P50:  getPercentile(sortedTimes, 0.5),
		P90:  getPercentile(sortedTimes, 0.9),
		P95:  getPercentile(sortedTimes, 0.95),
		P99:  getPercentile(sortedTimes, 0.99),
		Min:  sortedTimes[0],
		Max:  sortedTimes[len(sortedTimes)-1],
		Mean: m.avgProcessingTime[jobType],
	}
}

// getPercentile calculates the percentile value from sorted durations
func getPercentile(sortedTimes []time.Duration, percentile float64) time.Duration {
	if len(sortedTimes) == 0 {
		return 0
	}
	
	index := int(float64(len(sortedTimes)) * percentile)
	if index >= len(sortedTimes) {
		index = len(sortedTimes) - 1
	}
	
	return sortedTimes[index]
}

// JobTypeMetrics represents metrics for a specific job type
type JobTypeMetrics struct {
	JobType           string        `json:"job_type"`
	Enqueued          int64         `json:"enqueued"`
	Processing        int64         `json:"processing"`
	Successful        int64         `json:"successful"`
	Failed            int64         `json:"failed"`
	DeadLetter        int64         `json:"dead_letter"`
	AvgProcessingTime time.Duration `json:"avg_processing_time"`
	SuccessRate       float64       `json:"success_rate"`
}

// OverallMetrics represents overall queue metrics
type OverallMetrics struct {
	TotalEnqueued       int64         `json:"total_enqueued"`
	TotalProcessing     int64         `json:"total_processing"`
	TotalSuccessful     int64         `json:"total_successful"`
	TotalFailed         int64         `json:"total_failed"`
	TotalDeadLetter     int64         `json:"total_dead_letter"`
	CurrentQueueSize    int           `json:"current_queue_size"`
	PeakQueueSize       int           `json:"peak_queue_size"`
	WorkerCount         int64         `json:"worker_count"`
	TotalThroughput     int64         `json:"total_throughput"`
	ThroughputPerSecond float64       `json:"throughput_per_second"`
	OverallSuccessRate  float64       `json:"overall_success_rate"`
	Uptime              time.Duration `json:"uptime"`
	StartTime           time.Time     `json:"start_time"`
	LastUpdated         time.Time     `json:"last_updated"`
}

// ProcessingTimePercentiles represents processing time percentiles
type ProcessingTimePercentiles struct {
	P50  time.Duration `json:"p50"`
	P90  time.Duration `json:"p90"`
	P95  time.Duration `json:"p95"`
	P99  time.Duration `json:"p99"`
	Min  time.Duration `json:"min"`
	Max  time.Duration `json:"max"`
	Mean time.Duration `json:"mean"`
}

// HealthStatus represents the health status of the queue
type HealthStatus struct {
	Healthy            bool    `json:"healthy"`
	QueueBacklog       int     `json:"queue_backlog"`
	ErrorRate          float64 `json:"error_rate"`
	AvgProcessingTime  time.Duration `json:"avg_processing_time"`
	WorkerUtilization  float64 `json:"worker_utilization"`
	Issues             []string `json:"issues,omitempty"`
}

// GetHealthStatus evaluates the health of the queue
func (m *QueueMetrics) GetHealthStatus() HealthStatus {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	overall := m.GetOverallMetrics()
	
	var issues []string
	healthy := true
	
	// Check queue backlog
	if overall.CurrentQueueSize > 1000 {
		issues = append(issues, "High queue backlog")
		healthy = false
	}
	
	// Check error rate
	errorRate := 1.0 - overall.OverallSuccessRate
	if errorRate > 0.1 { // More than 10% errors
		issues = append(issues, "High error rate")
		healthy = false
	}
	
	// Check worker utilization (estimate based on processing vs capacity)
	workerUtilization := float64(overall.TotalProcessing) / float64(overall.WorkerCount)
	if workerUtilization > 0.9 {
		issues = append(issues, "High worker utilization")
	}
	
	return HealthStatus{
		Healthy:           healthy,
		QueueBacklog:      overall.CurrentQueueSize,
		ErrorRate:         errorRate,
		AvgProcessingTime: time.Duration(0), // Would need to calculate overall average
		WorkerUtilization: workerUtilization,
		Issues:            issues,
	}
}