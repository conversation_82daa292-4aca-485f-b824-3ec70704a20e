package queue

import (
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
)

// Job priorities for queue processing
type JobPriority int

const (
	PriorityLow JobPriority = iota
	PriorityNormal
	PriorityHigh
	PriorityUrgent
)

// Job types for different async operations
const (
	JobTypeSendMessage        = "send_message"
	JobTypeDeliverMessage     = "deliver_message"
	JobTypeUpdateReadStatus   = "update_read_status"
	JobTypeNotifyOfflineUsers = "notify_offline_users"
	JobTypeIndexMessage       = "index_message"
	JobTypeSyncConversation   = "sync_conversation"
	JobTypeCleanupMessages    = "cleanup_messages"
	JobTypeGenerateThumbnails = "generate_thumbnails"
	JobTypeProcessAttachment  = "process_attachment"
	JobTypeUpdateUserStatus   = "update_user_status"
	JobTypeBroadcastMessage   = "broadcast_message"
	JobTypeMarkAsDelivered    = "mark_as_delivered"
)

// Job interface for all job types
type Job interface {
	GetID() string
	GetType() string
	GetPriority() JobPriority
	GetPayload() interface{}
	GetCreatedAt() time.Time
	GetScheduledAt() time.Time
	GetAttempts() int
	GetMaxRetries() int
	IncrementAttempts()
	SetError(error)
	GetError() error
}

// BaseJob provides common job functionality
type BaseJob struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Priority    JobPriority            `json:"priority"`
	Payload     map[string]interface{} `json:"payload"`
	CreatedAt   time.Time              `json:"created_at"`
	ScheduledAt time.Time              `json:"scheduled_at"`
	Attempts    int                    `json:"attempts"`
	MaxRetries  int                    `json:"max_retries"`
	Error       error                  `json:"error,omitempty"`
}

func NewBaseJob(jobType string, priority JobPriority, payload map[string]interface{}) *BaseJob {
	now := time.Now()
	return &BaseJob{
		ID:          uuid.New().String(),
		Type:        jobType,
		Priority:    priority,
		Payload:     payload,
		CreatedAt:   now,
		ScheduledAt: now,
		Attempts:    0,
		MaxRetries:  3,
	}
}

func (j *BaseJob) GetID() string             { return j.ID }
func (j *BaseJob) GetType() string           { return j.Type }
func (j *BaseJob) GetPriority() JobPriority  { return j.Priority }
func (j *BaseJob) GetPayload() interface{}   { return j.Payload }
func (j *BaseJob) GetCreatedAt() time.Time   { return j.CreatedAt }
func (j *BaseJob) GetScheduledAt() time.Time { return j.ScheduledAt }
func (j *BaseJob) GetAttempts() int          { return j.Attempts }
func (j *BaseJob) GetMaxRetries() int        { return j.MaxRetries }
func (j *BaseJob) GetError() error           { return j.Error }

func (j *BaseJob) IncrementAttempts() {
	j.Attempts++
}

func (j *BaseJob) SetError(err error) {
	j.Error = err
}

// SendMessageJob for processing message sends
type SendMessageJob struct {
	*BaseJob
	ConversationID conversationentities.ConversationID `json:"conversation_id"`
	MessageID      messageentities.MessageID           `json:"message_id"`
	SenderID       uuid.UUID                           `json:"sender_id"`
	Recipients     []uuid.UUID                         `json:"recipients"`
	MessageData    map[string]interface{}              `json:"message_data"`
}

func NewSendMessageJob(conversationID conversationentities.ConversationID, messageID messageentities.MessageID, senderID uuid.UUID, recipients []uuid.UUID, messageData map[string]interface{}) *SendMessageJob {
	payload := map[string]interface{}{
		"conversation_id": conversationID.String(),
		"message_id":      messageID.String(),
		"sender_id":       senderID.String(),
		"recipients":      recipients,
		"message_data":    messageData,
	}

	return &SendMessageJob{
		BaseJob:        NewBaseJob(JobTypeSendMessage, PriorityNormal, payload),
		ConversationID: conversationID,
		MessageID:      messageID,
		SenderID:       senderID,
		Recipients:     recipients,
		MessageData:    messageData,
	}
}

// DeliverMessageJob for message delivery to users
type DeliverMessageJob struct {
	*BaseJob
	MessageID      messageentities.MessageID `json:"message_id"`
	ConversationID conversationentities.ConversationID `json:"conversation_id"`
	Recipients     []uuid.UUID               `json:"recipients"`
	ExcludeUserID  *uuid.UUID                `json:"exclude_user_id,omitempty"`
	DeliveryMode   string                    `json:"delivery_mode"`
}

func NewDeliverMessageJob(messageID messageentities.MessageID, conversationID conversationentities.ConversationID, recipients []uuid.UUID, excludeUserID *uuid.UUID, deliveryMode string) *DeliverMessageJob {
	payload := map[string]interface{}{
		"message_id":      messageID.String(),
		"conversation_id": conversationID.String(),
		"recipients":      recipients,
		"delivery_mode":   deliveryMode,
	}
	
	if excludeUserID != nil {
		payload["exclude_user_id"] = excludeUserID.String()
	}

	return &DeliverMessageJob{
		BaseJob:        NewBaseJob(JobTypeDeliverMessage, PriorityHigh, payload),
		MessageID:      messageID,
		ConversationID: conversationID,
		Recipients:     recipients,
		ExcludeUserID:  excludeUserID,
		DeliveryMode:   deliveryMode,
	}
}

// UpdateReadStatusJob for marking messages as read
type UpdateReadStatusJob struct {
	*BaseJob
	ConversationID conversationentities.ConversationID `json:"conversation_id"`
	UserID         uuid.UUID                           `json:"user_id"`
	MessageID      *messageentities.MessageID          `json:"message_id,omitempty"`
	UpToSequence   *int64                              `json:"up_to_sequence,omitempty"`
}

func NewUpdateReadStatusJob(conversationID conversationentities.ConversationID, userID uuid.UUID, messageID *messageentities.MessageID, upToSequence *int64) *UpdateReadStatusJob {
	payload := map[string]interface{}{
		"conversation_id": conversationID.String(),
		"user_id":         userID.String(),
	}
	
	if messageID != nil {
		payload["message_id"] = messageID.String()
	}
	
	if upToSequence != nil {
		payload["up_to_sequence"] = *upToSequence
	}

	return &UpdateReadStatusJob{
		BaseJob:        NewBaseJob(JobTypeUpdateReadStatus, PriorityNormal, payload),
		ConversationID: conversationID,
		UserID:         userID,
		MessageID:      messageID,
		UpToSequence:   upToSequence,
	}
}

// NotifyOfflineUsersJob for sending notifications to offline users
type NotifyOfflineUsersJob struct {
	*BaseJob
	MessageID      messageentities.MessageID           `json:"message_id"`
	ConversationID conversationentities.ConversationID `json:"conversation_id"`
	OfflineUsers   []uuid.UUID                         `json:"offline_users"`
	NotificationData map[string]interface{}            `json:"notification_data"`
}

func NewNotifyOfflineUsersJob(messageID messageentities.MessageID, conversationID conversationentities.ConversationID, offlineUsers []uuid.UUID, notificationData map[string]interface{}) *NotifyOfflineUsersJob {
	payload := map[string]interface{}{
		"message_id":        messageID.String(),
		"conversation_id":   conversationID.String(),
		"offline_users":     offlineUsers,
		"notification_data": notificationData,
	}

	return &NotifyOfflineUsersJob{
		BaseJob:          NewBaseJob(JobTypeNotifyOfflineUsers, PriorityNormal, payload),
		MessageID:        messageID,
		ConversationID:   conversationID,
		OfflineUsers:     offlineUsers,
		NotificationData: notificationData,
	}
}

// IndexMessageJob for search indexing
type IndexMessageJob struct {
	*BaseJob
	MessageID      messageentities.MessageID           `json:"message_id"`
	ConversationID conversationentities.ConversationID `json:"conversation_id"`
	Content        string                              `json:"content"`
	Metadata       map[string]interface{}              `json:"metadata"`
}

func NewIndexMessageJob(messageID messageentities.MessageID, conversationID conversationentities.ConversationID, content string, metadata map[string]interface{}) *IndexMessageJob {
	payload := map[string]interface{}{
		"message_id":      messageID.String(),
		"conversation_id": conversationID.String(),
		"content":         content,
		"metadata":        metadata,
	}

	return &IndexMessageJob{
		BaseJob:        NewBaseJob(JobTypeIndexMessage, PriorityLow, payload),
		MessageID:      messageID,
		ConversationID: conversationID,
		Content:        content,
		Metadata:       metadata,
	}
}

// SyncConversationJob for conversation state synchronization
type SyncConversationJob struct {
	*BaseJob
	ConversationID conversationentities.ConversationID `json:"conversation_id"`
	UserID         uuid.UUID                           `json:"user_id"`
	SyncType       string                              `json:"sync_type"`
	SyncData       map[string]interface{}              `json:"sync_data"`
}

func NewSyncConversationJob(conversationID conversationentities.ConversationID, userID uuid.UUID, syncType string, syncData map[string]interface{}) *SyncConversationJob {
	payload := map[string]interface{}{
		"conversation_id": conversationID.String(),
		"user_id":         userID.String(),
		"sync_type":       syncType,
		"sync_data":       syncData,
	}

	return &SyncConversationJob{
		BaseJob:        NewBaseJob(JobTypeSyncConversation, PriorityNormal, payload),
		ConversationID: conversationID,
		UserID:         userID,
		SyncType:       syncType,
		SyncData:       syncData,
	}
}

// ProcessAttachmentJob for attachment processing (thumbnails, virus scanning, etc.)
type ProcessAttachmentJob struct {
	*BaseJob
	MessageID    messageentities.MessageID `json:"message_id"`
	AttachmentID string                    `json:"attachment_id"`
	AttachmentURL string                   `json:"attachment_url"`
	MimeType     string                    `json:"mime_type"`
	Size         int64                     `json:"size"`
	ProcessingType string                  `json:"processing_type"`
}

func NewProcessAttachmentJob(messageID messageentities.MessageID, attachmentID, attachmentURL, mimeType string, size int64, processingType string) *ProcessAttachmentJob {
	payload := map[string]interface{}{
		"message_id":       messageID.String(),
		"attachment_id":    attachmentID,
		"attachment_url":   attachmentURL,
		"mime_type":        mimeType,
		"size":             size,
		"processing_type":  processingType,
	}

	return &ProcessAttachmentJob{
		BaseJob:        NewBaseJob(JobTypeProcessAttachment, PriorityLow, payload),
		MessageID:      messageID,
		AttachmentID:   attachmentID,
		AttachmentURL:  attachmentURL,
		MimeType:       mimeType,
		Size:           size,
		ProcessingType: processingType,
	}
}

// BroadcastMessageJob for broadcasting messages to multiple conversations
type BroadcastMessageJob struct {
	*BaseJob
	MessageData     map[string]interface{}                `json:"message_data"`
	ConversationIDs []conversationentities.ConversationID `json:"conversation_ids"`
	SenderID        uuid.UUID                             `json:"sender_id"`
	BroadcastType   string                                `json:"broadcast_type"`
}

func NewBroadcastMessageJob(messageData map[string]interface{}, conversationIDs []conversationentities.ConversationID, senderID uuid.UUID, broadcastType string) *BroadcastMessageJob {
	payload := map[string]interface{}{
		"message_data":     messageData,
		"conversation_ids": conversationIDs,
		"sender_id":        senderID.String(),
		"broadcast_type":   broadcastType,
	}

	return &BroadcastMessageJob{
		BaseJob:         NewBaseJob(JobTypeBroadcastMessage, PriorityNormal, payload),
		MessageData:     messageData,
		ConversationIDs: conversationIDs,
		SenderID:        senderID,
		BroadcastType:   broadcastType,
	}
}

// UpdateUserStatusJob for updating user online/offline status
type UpdateUserStatusJob struct {
	*BaseJob
	UserID        uuid.UUID              `json:"user_id"`
	Status        string                 `json:"status"`
	DeviceID      string                 `json:"device_id"`
	PlatformType  int32                  `json:"platform_type"`
	LastSeen      time.Time              `json:"last_seen"`
	StatusData    map[string]interface{} `json:"status_data"`
}

func NewUpdateUserStatusJob(userID uuid.UUID, status, deviceID string, platformType int32, lastSeen time.Time, statusData map[string]interface{}) *UpdateUserStatusJob {
	payload := map[string]interface{}{
		"user_id":       userID.String(),
		"status":        status,
		"device_id":     deviceID,
		"platform_type": platformType,
		"last_seen":     lastSeen,
		"status_data":   statusData,
	}

	return &UpdateUserStatusJob{
		BaseJob:      NewBaseJob(JobTypeUpdateUserStatus, PriorityNormal, payload),
		UserID:       userID,
		Status:       status,
		DeviceID:     deviceID,
		PlatformType: platformType,
		LastSeen:     lastSeen,
		StatusData:   statusData,
	}
}

// JobFactory for creating jobs from payloads
type JobFactory struct{}

func NewJobFactory() *JobFactory {
	return &JobFactory{}
}

func (f *JobFactory) CreateJob(jobType string, payload map[string]interface{}) (Job, error) {
	switch jobType {
	case JobTypeSendMessage:
		// Parse payload and create SendMessageJob
		// Implementation would parse the payload map and create the appropriate job
		return NewBaseJob(jobType, PriorityNormal, payload), nil
	case JobTypeDeliverMessage:
		return NewBaseJob(jobType, PriorityHigh, payload), nil
	case JobTypeUpdateReadStatus:
		return NewBaseJob(jobType, PriorityNormal, payload), nil
	case JobTypeNotifyOfflineUsers:
		return NewBaseJob(jobType, PriorityNormal, payload), nil
	case JobTypeIndexMessage:
		return NewBaseJob(jobType, PriorityLow, payload), nil
	case JobTypeSyncConversation:
		return NewBaseJob(jobType, PriorityNormal, payload), nil
	case JobTypeProcessAttachment:
		return NewBaseJob(jobType, PriorityLow, payload), nil
	case JobTypeBroadcastMessage:
		return NewBaseJob(jobType, PriorityNormal, payload), nil
	case JobTypeUpdateUserStatus:
		return NewBaseJob(jobType, PriorityNormal, payload), nil
	default:
		return NewBaseJob(jobType, PriorityNormal, payload), nil
	}
}

// JobResult represents the result of job processing
type JobResult struct {
	JobID       string      `json:"job_id"`
	Success     bool        `json:"success"`
	Result      interface{} `json:"result,omitempty"`
	Error       error       `json:"error,omitempty"`
	ProcessedAt time.Time   `json:"processed_at"`
	Duration    time.Duration `json:"duration"`
}

func NewJobResult(jobID string, success bool, result interface{}, err error, duration time.Duration) *JobResult {
	return &JobResult{
		JobID:       jobID,
		Success:     success,
		Result:      result,
		Error:       err,
		ProcessedAt: time.Now(),
		Duration:    duration,
	}
}