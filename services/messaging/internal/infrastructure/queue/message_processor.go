package queue

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	conversationrepositories "github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messagerepositories "github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/websocket"
)

// MessageJobProcessor handles processing of message-related jobs
type MessageJobProcessor struct {
	messageRepo      messagerepositories.MessageRepository
	conversationRepo conversationrepositories.ConversationRepository
	messagingHub     *websocket.MessagingHub
	eventPublisher   EventPublisher
	
	// Job-specific timeout configurations
	timeouts map[string]time.Duration
}

// EventPublisher interface for publishing events
type EventPublisher interface {
	PublishMessageSent(ctx context.Context, message *messageentities.Message, conversationID conversationentities.ConversationID) error
	PublishMessageDelivered(ctx context.Context, messageID messageentities.MessageID, userID uuid.UUID) error
	PublishReadStatusUpdated(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64) error
	PublishUserStatusUpdated(ctx context.Context, userID uuid.UUID, status string, deviceID string) error
}

// NewMessageJobProcessor creates a new message job processor
func NewMessageJobProcessor(
	messageRepo messagerepositories.MessageRepository,
	conversationRepo conversationrepositories.ConversationRepository,
	messagingHub *websocket.MessagingHub,
	eventPublisher EventPublisher,
) *MessageJobProcessor {
	processor := &MessageJobProcessor{
		messageRepo:      messageRepo,
		conversationRepo: conversationRepo,
		messagingHub:     messagingHub,
		eventPublisher:   eventPublisher,
		timeouts:         make(map[string]time.Duration),
	}
	
	// Configure job-specific timeouts
	processor.setupTimeouts()
	
	return processor
}

// setupTimeouts configures timeout durations for different job types
func (p *MessageJobProcessor) setupTimeouts() {
	p.timeouts[JobTypeSendMessage] = 30 * time.Second
	p.timeouts[JobTypeDeliverMessage] = 10 * time.Second
	p.timeouts[JobTypeUpdateReadStatus] = 15 * time.Second
	p.timeouts[JobTypeNotifyOfflineUsers] = 60 * time.Second
	p.timeouts[JobTypeIndexMessage] = 2 * time.Minute
	p.timeouts[JobTypeSyncConversation] = 45 * time.Second
	p.timeouts[JobTypeProcessAttachment] = 5 * time.Minute
	p.timeouts[JobTypeBroadcastMessage] = 2 * time.Minute
	p.timeouts[JobTypeUpdateUserStatus] = 5 * time.Second
	p.timeouts[JobTypeCleanupMessages] = 10 * time.Minute
	p.timeouts[JobTypeGenerateThumbnails] = 3 * time.Minute
}

// ProcessJob processes a job based on its type
func (p *MessageJobProcessor) ProcessJob(ctx context.Context, job Job) *JobResult {
	startTime := time.Now()
	jobID := job.GetID()
	jobType := job.GetType()
	
	log.Printf("Processing job %s of type %s", jobID, jobType)
	
	var result interface{}
	var err error
	
	// Route to appropriate processor based on job type
	switch jobType {
	case JobTypeSendMessage:
		result, err = p.processSendMessage(ctx, job)
	case JobTypeDeliverMessage:
		result, err = p.processDeliverMessage(ctx, job)
	case JobTypeUpdateReadStatus:
		result, err = p.processUpdateReadStatus(ctx, job)
	case JobTypeNotifyOfflineUsers:
		result, err = p.processNotifyOfflineUsers(ctx, job)
	case JobTypeIndexMessage:
		result, err = p.processIndexMessage(ctx, job)
	case JobTypeSyncConversation:
		result, err = p.processSyncConversation(ctx, job)
	case JobTypeProcessAttachment:
		result, err = p.processAttachment(ctx, job)
	case JobTypeBroadcastMessage:
		result, err = p.processBroadcastMessage(ctx, job)
	case JobTypeUpdateUserStatus:
		result, err = p.processUpdateUserStatus(ctx, job)
	case JobTypeCleanupMessages:
		result, err = p.processCleanupMessages(ctx, job)
	case JobTypeGenerateThumbnails:
		result, err = p.processGenerateThumbnails(ctx, job)
	default:
		err = fmt.Errorf("unknown job type: %s", jobType)
	}
	
	duration := time.Since(startTime)
	success := err == nil
	
	if success {
		log.Printf("Job %s completed successfully in %v", jobID, duration)
	} else {
		log.Printf("Job %s failed after %v: %v", jobID, duration, err)
	}
	
	return NewJobResult(jobID, success, result, err, duration)
}

// CanProcess returns true if this processor can handle the job type
func (p *MessageJobProcessor) CanProcess(jobType string) bool {
	switch jobType {
	case JobTypeSendMessage, JobTypeDeliverMessage, JobTypeUpdateReadStatus,
		 JobTypeNotifyOfflineUsers, JobTypeIndexMessage, JobTypeSyncConversation,
		 JobTypeProcessAttachment, JobTypeBroadcastMessage, JobTypeUpdateUserStatus,
		 JobTypeCleanupMessages, JobTypeGenerateThumbnails:
		return true
	default:
		return false
	}
}

// GetProcessingTimeout returns the timeout for a specific job type
func (p *MessageJobProcessor) GetProcessingTimeout(jobType string) time.Duration {
	if timeout, exists := p.timeouts[jobType]; exists {
		return timeout
	}
	return 5 * time.Minute // Default timeout
}

// Job processing implementations

func (p *MessageJobProcessor) processSendMessage(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	// Extract job data
	conversationIDStr, ok := payload["conversation_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid conversation_id in payload")
	}
	
	messageIDStr, ok := payload["message_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid message_id in payload")
	}
	
	senderIDStr, ok := payload["sender_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid sender_id in payload")
	}
	
	// messageData, ok := payload["message_data"].(map[string]interface{})
	// if !ok {
	// 	return nil, fmt.Errorf("invalid message_data in payload")
	// }
	
	// Parse IDs
	conversationID := conversationentities.ConversationID(uuid.MustParse(conversationIDStr))
	messageID := messageentities.MessageID(uuid.MustParse(messageIDStr))
	senderID := uuid.MustParse(senderIDStr)
	
	// Retrieve the message from storage
	message, err := p.messageRepo.GetMessage(ctx, messageID)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve message: %w", err)
	}
	
	// Publish message sent event for real-time delivery
	if err := p.eventPublisher.PublishMessageSent(ctx, message, conversationID); err != nil {
		log.Printf("Warning: failed to publish message sent event: %v", err)
	}
	
	return map[string]interface{}{
		"message_id":      messageID.String(),
		"conversation_id": conversationID.String(),
		"sender_id":       senderID.String(),
		"processed_at":    time.Now(),
	}, nil
}

func (p *MessageJobProcessor) processDeliverMessage(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	messageIDStr, ok := payload["message_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid message_id in payload")
	}
	
	conversationIDStr, ok := payload["conversation_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid conversation_id in payload")
	}
	
	recipients, ok := payload["recipients"].([]uuid.UUID)
	if !ok {
		return nil, fmt.Errorf("invalid recipients in payload")
	}
	
	deliveryMode, ok := payload["delivery_mode"].(string)
	if !ok {
		deliveryMode = "real_time"
	}
	
	messageID := messageentities.MessageID(uuid.MustParse(messageIDStr))
	conversationID := conversationentities.ConversationID(uuid.MustParse(conversationIDStr))
	
	// Get the message
	message, err := p.messageRepo.GetMessage(ctx, messageID)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve message for delivery: %w", err)
	}
	
	// Create broadcast message for hub
	var excludeUserID *uuid.UUID
	if excludeUserIDStr, exists := payload["exclude_user_id"].(string); exists {
		userID := uuid.MustParse(excludeUserIDStr)
		excludeUserID = &userID
	}
	
	broadcastMsg := &websocket.BroadcastMessage{
		ConversationID: conversationID.String(),
		Message:        message,
		Recipients:     recipients,
		ExcludeUserID:  excludeUserID,
		DeliveryMode:   websocket.DeliveryMode(0), // Convert from string if needed
	}
	
	// Send through messaging hub
	p.messagingHub.BroadcastMessage(broadcastMsg)
	
	// Mark as delivered for online recipients
	deliveredCount := 0
	for _, userID := range recipients {
		if p.messagingHub.IsUserOnline(userID) {
			if err := p.eventPublisher.PublishMessageDelivered(ctx, messageID, userID); err != nil {
				log.Printf("Warning: failed to publish message delivered event for user %s: %v", userID, err)
			} else {
				deliveredCount++
			}
		}
	}
	
	return map[string]interface{}{
		"message_id":       messageID.String(),
		"recipients_count": len(recipients),
		"delivered_count":  deliveredCount,
		"delivery_mode":    deliveryMode,
	}, nil
}

func (p *MessageJobProcessor) processUpdateReadStatus(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	conversationIDStr, ok := payload["conversation_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid conversation_id in payload")
	}
	
	userIDStr, ok := payload["user_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid user_id in payload")
	}
	
	conversationID := conversationentities.ConversationID(uuid.MustParse(conversationIDStr))
	userID := uuid.MustParse(userIDStr)
	
	// Check if it's a single message or bulk update
	if messageIDStr, exists := payload["message_id"].(string); exists {
		// Single message read
		messageID := messageentities.MessageID(uuid.MustParse(messageIDStr))
		
		if err := p.messageRepo.MarkMessageAsRead(ctx, messageID, userID); err != nil {
			return nil, fmt.Errorf("failed to mark message as read: %w", err)
		}
		
		// Get the message to get its sequence for event publishing
		message, err := p.messageRepo.GetMessage(ctx, messageID)
		if err != nil {
			log.Printf("Warning: failed to get message for sequence: %v", err)
		} else {
			if err := p.eventPublisher.PublishReadStatusUpdated(ctx, conversationID, userID, message.Sequence()); err != nil {
				log.Printf("Warning: failed to publish read status event: %v", err)
			}
		}
		
		return map[string]interface{}{
			"message_id":      messageID.String(),
			"user_id":         userID.String(),
			"conversation_id": conversationID.String(),
		}, nil
	} else if upToSequence, exists := payload["up_to_sequence"].(int64); exists {
		// Bulk read update
		if err := p.messageRepo.MarkMessagesAsRead(ctx, conversationID, userID, upToSequence); err != nil {
			return nil, fmt.Errorf("failed to mark messages as read: %w", err)
		}
		
		if err := p.eventPublisher.PublishReadStatusUpdated(ctx, conversationID, userID, upToSequence); err != nil {
			log.Printf("Warning: failed to publish read status event: %v", err)
		}
		
		return map[string]interface{}{
			"user_id":         userID.String(),
			"conversation_id": conversationID.String(),
			"up_to_sequence":  upToSequence,
		}, nil
	}
	
	return nil, fmt.Errorf("neither message_id nor up_to_sequence provided")
}

func (p *MessageJobProcessor) processNotifyOfflineUsers(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	messageIDStr, ok := payload["message_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid message_id in payload")
	}
	
	offlineUsers, ok := payload["offline_users"].([]uuid.UUID)
	if !ok {
		return nil, fmt.Errorf("invalid offline_users in payload")
	}
	
	notificationData, ok := payload["notification_data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid notification_data in payload")
	}
	
	// This would integrate with the notification service
	// For now, we'll just log the notification attempt
	log.Printf("Sending notifications for message %s to %d offline users", messageIDStr, len(offlineUsers))
	
	// In a real implementation, this would:
	// 1. Format notification content
	// 2. Send push notifications via the notification service
	// 3. Send email notifications if configured
	// 4. Update notification delivery status
	
	notifiedCount := 0
	for _, userID := range offlineUsers {
		// Simulate notification sending
		log.Printf("Sending notification to offline user %s", userID.String())
		notifiedCount++
	}
	
	return map[string]interface{}{
		"message_id":      messageIDStr,
		"users_notified":  notifiedCount,
		"total_users":     len(offlineUsers),
		"notification_data": notificationData,
	}, nil
}

func (p *MessageJobProcessor) processIndexMessage(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	messageIDStr, ok := payload["message_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid message_id in payload")
	}
	
	content, ok := payload["content"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid content in payload")
	}
	
	metadata, ok := payload["metadata"].(map[string]interface{})
	if !ok {
		metadata = make(map[string]interface{})
	}
	
	// This would integrate with a search indexing service (Elasticsearch, etc.)
	log.Printf("Indexing message %s with content length %d", messageIDStr, len(content))
	
	// In a real implementation, this would:
	// 1. Extract searchable content
	// 2. Analyze text for keywords
	// 3. Index in search service
	// 4. Update search metadata
	
	return map[string]interface{}{
		"message_id":     messageIDStr,
		"content_length": len(content),
		"indexed_at":     time.Now(),
		"metadata":       metadata,
	}, nil
}

func (p *MessageJobProcessor) processSyncConversation(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	conversationIDStr, ok := payload["conversation_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid conversation_id in payload")
	}
	
	userIDStr, ok := payload["user_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid user_id in payload")
	}
	
	syncType, ok := payload["sync_type"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid sync_type in payload")
	}
	
	conversationID := conversationentities.ConversationID(uuid.MustParse(conversationIDStr))
	// userID := uuid.MustParse(userIDStr)
	
	// Get conversation info
	// conversation, err := p.conversationRepo.GetByID(ctx, conversationID)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to get conversation: %w", err)
	// }
	
	// Perform sync based on type
	switch syncType {
	case "participant_update":
		// Update participant information
		log.Printf("Syncing participant update for conversation %s", conversationID.String())
	case "last_read_update":
		// Update last read status
		log.Printf("Syncing last read update for conversation %s", conversationID.String())
	default:
		log.Printf("Unknown sync type %s for conversation %s", syncType, conversationID.String())
	}
	
	return map[string]interface{}{
		"conversation_id": conversationIDStr,
		"user_id":         userIDStr,
		"sync_type":       syncType,
		"synced_at":       time.Now(),
	}, nil
}

func (p *MessageJobProcessor) processAttachment(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	messageIDStr, ok := payload["message_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid message_id in payload")
	}
	
	attachmentID, ok := payload["attachment_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid attachment_id in payload")
	}
	
	processingType, ok := payload["processing_type"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid processing_type in payload")
	}
	
	// This would integrate with file processing services
	log.Printf("Processing attachment %s for message %s (type: %s)", attachmentID, messageIDStr, processingType)
	
	// In a real implementation, this would:
	// 1. Download the attachment
	// 2. Scan for viruses
	// 3. Generate thumbnails for images/videos
	// 4. Extract metadata
	// 5. Update attachment status
	
	return map[string]interface{}{
		"message_id":      messageIDStr,
		"attachment_id":   attachmentID,
		"processing_type": processingType,
		"processed_at":    time.Now(),
	}, nil
}

func (p *MessageJobProcessor) processBroadcastMessage(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	// messageData, ok := payload["message_data"].(map[string]interface{})
	// if !ok {
	// 	return nil, fmt.Errorf("invalid message_data in payload")
	// }
	
	conversationIDs, ok := payload["conversation_ids"].([]conversationentities.ConversationID)
	if !ok {
		return nil, fmt.Errorf("invalid conversation_ids in payload")
	}
	
	senderIDStr, ok := payload["sender_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid sender_id in payload")
	}
	
	// senderID := uuid.MustParse(senderIDStr)
	
	// Broadcast message to all conversations
	broadcastCount := 0
	for _, conversationID := range conversationIDs {
		// This would create individual messages for each conversation
		log.Printf("Broadcasting message to conversation %s", conversationID.String())
		broadcastCount++
	}
	
	return map[string]interface{}{
		"sender_id":           senderIDStr,
		"conversations_count": len(conversationIDs),
		"broadcast_count":     broadcastCount,
		"broadcast_at":        time.Now(),
	}, nil
}

func (p *MessageJobProcessor) processUpdateUserStatus(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	userIDStr, ok := payload["user_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid user_id in payload")
	}
	
	status, ok := payload["status"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid status in payload")
	}
	
	deviceID, ok := payload["device_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid device_id in payload")
	}
	
	userID := uuid.MustParse(userIDStr)
	
	// Update user status in the system
	log.Printf("Updating status for user %s to %s (device: %s)", userIDStr, status, deviceID)
	
	// Publish status update event
	if err := p.eventPublisher.PublishUserStatusUpdated(ctx, userID, status, deviceID); err != nil {
		log.Printf("Warning: failed to publish user status event: %v", err)
	}
	
	return map[string]interface{}{
		"user_id":    userIDStr,
		"status":     status,
		"device_id":  deviceID,
		"updated_at": time.Now(),
	}, nil
}

func (p *MessageJobProcessor) processCleanupMessages(ctx context.Context, job Job) (interface{}, error) {
	// This would implement message cleanup logic
	log.Printf("Processing message cleanup job %s", job.GetID())
	
	// In a real implementation, this would:
	// 1. Find old messages to clean up
	// 2. Archive or delete them based on retention policy
	// 3. Update storage statistics
	// 4. Clean up orphaned attachments
	
	return map[string]interface{}{
		"cleanup_type": "messages",
		"cleaned_at":   time.Now(),
	}, nil
}

func (p *MessageJobProcessor) processGenerateThumbnails(ctx context.Context, job Job) (interface{}, error) {
	payload := job.GetPayload().(map[string]interface{})
	
	messageIDStr, ok := payload["message_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid message_id in payload")
	}
	
	// This would implement thumbnail generation
	log.Printf("Generating thumbnails for message %s", messageIDStr)
	
	// In a real implementation, this would:
	// 1. Get message attachments
	// 2. Generate thumbnails for images/videos
	// 3. Store thumbnails in CDN
	// 4. Update attachment metadata
	
	return map[string]interface{}{
		"message_id":         messageIDStr,
		"thumbnails_generated": 1,
		"generated_at":       time.Now(),
	}, nil
}