package queue

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// QueueConfig holds configuration for the memory queue
type QueueConfig struct {
	BufferSize        int           `json:"buffer_size"`         // Channel buffer size
	MaxWorkers        int           `json:"max_workers"`         // Maximum worker goroutines
	PriorityQueues    int           `json:"priority_queues"`     // Number of priority levels
	WorkerIdleTimeout time.Duration `json:"worker_idle_timeout"` // How long workers wait before shutting down
	JobTimeout        time.Duration `json:"job_timeout"`         // Maximum time for job execution
	RetryDelay        time.Duration `json:"retry_delay"`         // Delay between job retries
	EnableMetrics     bool          `json:"enable_metrics"`      // Enable metrics collection
}

// DefaultQueueConfig returns a default configuration
func DefaultQueueConfig() QueueConfig {
	return QueueConfig{
		BufferSize:        512,
		MaxWorkers:        runtime.NumCPU() * 2,
		PriorityQueues:    4, // Low, Normal, High, Urgent
		WorkerIdleTimeout: 30 * time.Second,
		JobTimeout:        5 * time.Minute,
		RetryDelay:        time.Second,
		EnableMetrics:     true,
	}
}

// MemoryQueue implements an in-memory job queue with priority support
type MemoryQueue struct {
	config           QueueConfig
	priorityQueues   []chan Job
	workers          []chan struct{} // Worker control channels
	activeWorkers    int64
	totalWorkers     int64
	ctx              context.Context
	cancel           context.CancelFunc
	wg               sync.WaitGroup
	processor        JobProcessor
	metrics          *QueueMetrics
	mu               sync.RWMutex
	running          bool
	
	// Job routing
	jobRouter        map[string]int // Job type to priority mapping
	
	// Retry mechanism
	retryQueue       chan Job
	deadLetterQueue  []Job
	deadLetterMutex  sync.RWMutex
}

// JobProcessor interface for processing different job types
type JobProcessor interface {
	ProcessJob(ctx context.Context, job Job) *JobResult
	CanProcess(jobType string) bool
	GetProcessingTimeout(jobType string) time.Duration
}

// NewMemoryQueue creates a new memory queue with the given configuration
func NewMemoryQueue(config QueueConfig, processor JobProcessor) *MemoryQueue {
	ctx, cancel := context.WithCancel(context.Background())
	
	// Create priority queues
	priorityQueues := make([]chan Job, config.PriorityQueues)
	for i := range priorityQueues {
		priorityQueues[i] = make(chan Job, config.BufferSize)
	}
	
	// Initialize metrics if enabled
	var metrics *QueueMetrics
	if config.EnableMetrics {
		metrics = NewQueueMetrics()
	}
	
	queue := &MemoryQueue{
		config:         config,
		priorityQueues: priorityQueues,
		workers:        make([]chan struct{}, 0, config.MaxWorkers),
		ctx:            ctx,
		cancel:         cancel,
		processor:      processor,
		metrics:        metrics,
		jobRouter:      make(map[string]int),
		retryQueue:     make(chan Job, config.BufferSize),
		deadLetterQueue: make([]Job, 0),
		running:        false,
	}
	
	// Set up default job routing
	queue.setupJobRouting()
	
	return queue
}

// setupJobRouting configures default job type to priority mappings
func (mq *MemoryQueue) setupJobRouting() {
	// High priority jobs
	mq.jobRouter[JobTypeDeliverMessage] = int(PriorityHigh)
	mq.jobRouter[JobTypeUpdateReadStatus] = int(PriorityHigh)
	
	// Normal priority jobs
	mq.jobRouter[JobTypeSendMessage] = int(PriorityNormal)
	mq.jobRouter[JobTypeNotifyOfflineUsers] = int(PriorityNormal)
	mq.jobRouter[JobTypeSyncConversation] = int(PriorityNormal)
	mq.jobRouter[JobTypeBroadcastMessage] = int(PriorityNormal)
	mq.jobRouter[JobTypeUpdateUserStatus] = int(PriorityNormal)
	
	// Low priority jobs
	mq.jobRouter[JobTypeIndexMessage] = int(PriorityLow)
	mq.jobRouter[JobTypeProcessAttachment] = int(PriorityLow)
	mq.jobRouter[JobTypeCleanupMessages] = int(PriorityLow)
	mq.jobRouter[JobTypeGenerateThumbnails] = int(PriorityLow)
}

// Start begins processing jobs
func (mq *MemoryQueue) Start() error {
	mq.mu.Lock()
	defer mq.mu.Unlock()
	
	if mq.running {
		return fmt.Errorf("queue is already running")
	}
	
	// Start initial workers
	for i := 0; i < mq.config.MaxWorkers; i++ {
		mq.startWorker()
	}
	
	// Start retry processor
	mq.wg.Add(1)
	go mq.retryProcessor()
	
	// Start metrics collector if enabled
	if mq.metrics != nil {
		mq.wg.Add(1)
		go mq.metricsCollector()
	}
	
	mq.running = true
	log.Printf("Memory queue started with %d workers", mq.config.MaxWorkers)
	
	return nil
}

// Stop gracefully stops the queue
func (mq *MemoryQueue) Stop() error {
	mq.mu.Lock()
	defer mq.mu.Unlock()
	
	if !mq.running {
		return fmt.Errorf("queue is not running")
	}
	
	log.Println("Stopping memory queue...")
	
	// Cancel context to signal workers to stop
	mq.cancel()
	
	// Close all priority queues
	for _, queue := range mq.priorityQueues {
		close(queue)
	}
	close(mq.retryQueue)
	
	// Wait for all workers to finish
	mq.wg.Wait()
	
	mq.running = false
	log.Printf("Memory queue stopped. Dead letter queue size: %d", len(mq.deadLetterQueue))
	
	return nil
}

// Enqueue adds a job to the appropriate priority queue
func (mq *MemoryQueue) Enqueue(job Job) error {
	if !mq.running {
		return fmt.Errorf("queue is not running")
	}
	
	// Determine priority queue
	priority := mq.getJobPriority(job)
	queueIndex := mq.priorityToQueueIndex(priority)
	
	select {
	case mq.priorityQueues[queueIndex] <- job:
		if mq.metrics != nil {
			mq.metrics.IncrementEnqueued(job.GetType())
		}
		
		// Dynamically scale workers if needed
		mq.scaleWorkersIfNeeded()
		
		return nil
	case <-mq.ctx.Done():
		return fmt.Errorf("queue is shutting down")
	default:
		// Queue is full, try to add to retry queue or dead letter
		return mq.handleFullQueue(job)
	}
}

// EnqueueWithDelay adds a job to be processed after a delay
func (mq *MemoryQueue) EnqueueWithDelay(job Job, delay time.Duration) error {
	go func() {
		timer := time.NewTimer(delay)
		defer timer.Stop()
		
		select {
		case <-timer.C:
			if err := mq.Enqueue(job); err != nil {
				log.Printf("Failed to enqueue delayed job %s: %v", job.GetID(), err)
				mq.addToDeadLetter(job, err)
			}
		case <-mq.ctx.Done():
			return
		}
	}()
	
	return nil
}

// EnqueueBatch adds multiple jobs efficiently
func (mq *MemoryQueue) EnqueueBatch(jobs []Job) error {
	if !mq.running {
		return fmt.Errorf("queue is not running")
	}
	
	var errors []error
	
	for _, job := range jobs {
		if err := mq.Enqueue(job); err != nil {
			errors = append(errors, fmt.Errorf("job %s: %w", job.GetID(), err))
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("batch enqueue had %d errors: %v", len(errors), errors[0])
	}
	
	return nil
}

// startWorker creates and starts a new worker goroutine
func (mq *MemoryQueue) startWorker() {
	workerID := atomic.AddInt64(&mq.totalWorkers, 1)
	atomic.AddInt64(&mq.activeWorkers, 1)
	
	mq.wg.Add(1)
	go mq.worker(workerID)
}

// worker processes jobs from priority queues
func (mq *MemoryQueue) worker(id int64) {
	defer mq.wg.Done()
	defer atomic.AddInt64(&mq.activeWorkers, -1)
	
	log.Printf("Worker %d started", id)
	defer log.Printf("Worker %d stopped", id)
	
	idleTimer := time.NewTimer(mq.config.WorkerIdleTimeout)
	defer idleTimer.Stop()
	
	for {
		select {
		case <-mq.ctx.Done():
			return
			
		case <-idleTimer.C:
			// Worker has been idle too long, check if we should shut down
			if mq.shouldScaleDown() {
				return
			}
			idleTimer.Reset(mq.config.WorkerIdleTimeout)
			
		default:
			// Try to get a job from priority queues (highest priority first)
			job := mq.getNextJob()
			if job == nil {
				// No jobs available, wait briefly
				time.Sleep(10 * time.Millisecond)
				continue
			}
			
			// Reset idle timer since we got work
			if !idleTimer.Stop() {
				<-idleTimer.C
			}
			idleTimer.Reset(mq.config.WorkerIdleTimeout)
			
			// Process the job
			mq.processJob(job)
		}
	}
}

// getNextJob retrieves the next job from priority queues
func (mq *MemoryQueue) getNextJob() Job {
	// Check priority queues from highest to lowest priority
	for i := len(mq.priorityQueues) - 1; i >= 0; i-- {
		select {
		case job := <-mq.priorityQueues[i]:
			return job
		default:
			continue
		}
	}
	return nil
}

// processJob handles job execution with timeout and error handling
func (mq *MemoryQueue) processJob(job Job) {
	startTime := time.Now()
	jobID := job.GetID()
	jobType := job.GetType()
	
	// Create job context with timeout
	timeout := mq.config.JobTimeout
	if mq.processor != nil {
		if customTimeout := mq.processor.GetProcessingTimeout(jobType); customTimeout > 0 {
			timeout = customTimeout
		}
	}
	
	jobCtx, cancel := context.WithTimeout(mq.ctx, timeout)
	defer cancel()
	
	// Update metrics
	if mq.metrics != nil {
		mq.metrics.IncrementProcessing(jobType)
		defer func() {
			duration := time.Since(startTime)
			mq.metrics.RecordProcessingTime(jobType, duration)
		}()
	}
	
	// Process the job
	var result *JobResult
	if mq.processor == nil || !mq.processor.CanProcess(jobType) {
		result = NewJobResult(jobID, false, nil, fmt.Errorf("no processor available for job type %s", jobType), time.Since(startTime))
	} else {
		result = mq.processor.ProcessJob(jobCtx, job)
	}
	
	// Handle job result
	if result.Success {
		if mq.metrics != nil {
			mq.metrics.IncrementSuccessful(jobType)
		}
		log.Printf("Job %s (%s) completed successfully in %v", jobID, jobType, result.Duration)
	} else {
		if mq.metrics != nil {
			mq.metrics.IncrementFailed(jobType)
		}
		log.Printf("Job %s (%s) failed: %v", jobID, jobType, result.Error)
		
		// Handle job retry
		mq.handleJobFailure(job, result.Error)
	}
}

// handleJobFailure determines whether to retry a failed job
func (mq *MemoryQueue) handleJobFailure(job Job, err error) {
	job.IncrementAttempts()
	job.SetError(err)
	
	if job.GetAttempts() < job.GetMaxRetries() {
		// Retry the job
		select {
		case mq.retryQueue <- job:
			log.Printf("Job %s queued for retry (attempt %d/%d)", job.GetID(), job.GetAttempts(), job.GetMaxRetries())
		default:
			// Retry queue full, send to dead letter
			mq.addToDeadLetter(job, fmt.Errorf("retry queue full: %w", err))
		}
	} else {
		// Max retries exceeded, send to dead letter
		mq.addToDeadLetter(job, fmt.Errorf("max retries exceeded: %w", err))
	}
}

// retryProcessor handles job retries with exponential backoff
func (mq *MemoryQueue) retryProcessor() {
	defer mq.wg.Done()
	
	for {
		select {
		case <-mq.ctx.Done():
			return
			
		case job := <-mq.retryQueue:
			if job == nil {
				continue
			}
			
			// Calculate retry delay with exponential backoff
			delay := mq.config.RetryDelay * time.Duration(1<<uint(job.GetAttempts()-1))
			if delay > time.Minute {
				delay = time.Minute // Cap at 1 minute
			}
			
			log.Printf("Retrying job %s after %v", job.GetID(), delay)
			
			// Schedule retry
			time.AfterFunc(delay, func() {
				if err := mq.Enqueue(job); err != nil {
					log.Printf("Failed to re-enqueue job %s: %v", job.GetID(), err)
					mq.addToDeadLetter(job, err)
				}
			})
		}
	}
}

// addToDeadLetter moves a job to the dead letter queue
func (mq *MemoryQueue) addToDeadLetter(job Job, err error) {
	mq.deadLetterMutex.Lock()
	defer mq.deadLetterMutex.Unlock()
	
	job.SetError(err)
	mq.deadLetterQueue = append(mq.deadLetterQueue, job)
	
	if mq.metrics != nil {
		mq.metrics.IncrementDeadLetter(job.GetType())
	}
	
	log.Printf("Job %s moved to dead letter queue: %v", job.GetID(), err)
}

// Helper methods
func (mq *MemoryQueue) getJobPriority(job Job) JobPriority {
	// Check if job type has a specific priority mapping
	if queueIndex, exists := mq.jobRouter[job.GetType()]; exists {
		return JobPriority(queueIndex)
	}
	
	// Use job's own priority
	return job.GetPriority()
}

func (mq *MemoryQueue) priorityToQueueIndex(priority JobPriority) int {
	index := int(priority)
	if index < 0 {
		return 0
	}
	if index >= len(mq.priorityQueues) {
		return len(mq.priorityQueues) - 1
	}
	return index
}

func (mq *MemoryQueue) handleFullQueue(job Job) error {
	// Try retry queue first
	select {
	case mq.retryQueue <- job:
		log.Printf("Job %s added to retry queue (main queue full)", job.GetID())
		return nil
	default:
		// Both main and retry queues full, add to dead letter
		mq.addToDeadLetter(job, fmt.Errorf("all queues full"))
		return fmt.Errorf("queue capacity exceeded, job moved to dead letter queue")
	}
}

func (mq *MemoryQueue) scaleWorkersIfNeeded() {
	currentWorkers := atomic.LoadInt64(&mq.activeWorkers)
	
	// Simple scaling logic: add workers if queues are getting full
	totalQueuedJobs := mq.getTotalQueuedJobs()
	if totalQueuedJobs > int(currentWorkers)*2 && currentWorkers < int64(mq.config.MaxWorkers) {
		mq.startWorker()
	}
}

func (mq *MemoryQueue) shouldScaleDown() bool {
	currentWorkers := atomic.LoadInt64(&mq.activeWorkers)
	totalQueuedJobs := mq.getTotalQueuedJobs()
	
	// Scale down if we have more workers than jobs and above minimum
	minWorkers := int64(mq.config.MaxWorkers / 4) // Keep at least 25% of max workers
	if minWorkers < 1 {
		minWorkers = 1
	}
	
	return totalQueuedJobs < int(currentWorkers)/2 && currentWorkers > minWorkers
}

func (mq *MemoryQueue) getTotalQueuedJobs() int {
	total := 0
	for _, queue := range mq.priorityQueues {
		total += len(queue)
	}
	total += len(mq.retryQueue)
	return total
}

// Public status and management methods

// GetStatus returns the current queue status
func (mq *MemoryQueue) GetStatus() QueueStatus {
	mq.deadLetterMutex.RLock()
	deadLetterCount := len(mq.deadLetterQueue)
	mq.deadLetterMutex.RUnlock()
	
	queueSizes := make([]int, len(mq.priorityQueues))
	for i, queue := range mq.priorityQueues {
		queueSizes[i] = len(queue)
	}
	
	return QueueStatus{
		Running:          mq.running,
		ActiveWorkers:    atomic.LoadInt64(&mq.activeWorkers),
		TotalWorkers:     atomic.LoadInt64(&mq.totalWorkers),
		QueueSizes:       queueSizes,
		RetryQueueSize:   len(mq.retryQueue),
		DeadLetterCount:  deadLetterCount,
		TotalJobsQueued:  mq.getTotalQueuedJobs(),
	}
}

// GetDeadLetterJobs returns jobs in the dead letter queue
func (mq *MemoryQueue) GetDeadLetterJobs() []Job {
	mq.deadLetterMutex.RLock()
	defer mq.deadLetterMutex.RUnlock()
	
	jobs := make([]Job, len(mq.deadLetterQueue))
	copy(jobs, mq.deadLetterQueue)
	return jobs
}

// RequeueDeadLetterJob moves a job from dead letter back to processing
func (mq *MemoryQueue) RequeueDeadLetterJob(jobID string) error {
	mq.deadLetterMutex.Lock()
	defer mq.deadLetterMutex.Unlock()
	
	for i, job := range mq.deadLetterQueue {
		if job.GetID() == jobID {
			// Reset job attempts and error
			if baseJob, ok := job.(*BaseJob); ok {
				baseJob.Attempts = 0
				baseJob.Error = nil
			}
			
			// Remove from dead letter and re-enqueue
			mq.deadLetterQueue = append(mq.deadLetterQueue[:i], mq.deadLetterQueue[i+1:]...)
			return mq.Enqueue(job)
		}
	}
	
	return fmt.Errorf("job %s not found in dead letter queue", jobID)
}

// GetMetrics returns queue metrics if enabled
func (mq *MemoryQueue) GetMetrics() *QueueMetrics {
	return mq.metrics
}

// metricsCollector periodically updates queue metrics
func (mq *MemoryQueue) metricsCollector() {
	defer mq.wg.Done()
	
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-mq.ctx.Done():
			return
		case <-ticker.C:
			if mq.metrics != nil {
				status := mq.GetStatus()
				mq.metrics.UpdateQueueSizes(status.QueueSizes)
				mq.metrics.UpdateWorkerCount(status.ActiveWorkers)
			}
		}
	}
}

// QueueStatus represents the current state of the queue
type QueueStatus struct {
	Running          bool    `json:"running"`
	ActiveWorkers    int64   `json:"active_workers"`
	TotalWorkers     int64   `json:"total_workers"`
	QueueSizes       []int   `json:"queue_sizes"`
	RetryQueueSize   int     `json:"retry_queue_size"`
	DeadLetterCount  int     `json:"dead_letter_count"`
	TotalJobsQueued  int     `json:"total_jobs_queued"`
}