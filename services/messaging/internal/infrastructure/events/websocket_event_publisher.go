package events

import (
	"context"
	"log"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/services"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/websocket"
)

// WebSocketEventPublisher implements the EventPublisher interface
// to bridge domain services with WebSocket real-time delivery
type WebSocketEventPublisher struct {
	messageHandler *websocket.MessageHandler
}

// NewWebSocketEventPublisher creates a new WebSocket event publisher
func NewWebSocketEventPublisher(messageHandler *websocket.MessageHandler) services.EventPublisher {
	return &WebSocketEventPublisher{
		messageHandler: messageHandler,
	}
}

// PublishMessageSent publishes a message sent event via WebSocket
func (p *WebSocketEventPublisher) PublishMessageSent(ctx context.Context, message *entities.Message, conversationID conversationentities.ConversationID) error {
	if p.messageHandler == nil {
		log.Printf("Warning: messageHandler is nil, cannot broadcast message")
		return nil
	}

	// Broadcast the message to all conversation participants
	p.messageHandler.BroadcastMessage(ctx, message)
	
	log.Printf("Message broadcast sent: id=%s, conversation=%s, sender=%s", 
		message.ID().String(), 
		conversationID.String(), 
		message.SenderID().String())
	
	return nil
}

// PublishMessageEdited publishes a message edited event via WebSocket
func (p *WebSocketEventPublisher) PublishMessageEdited(ctx context.Context, message *entities.Message) error {
	if p.messageHandler == nil {
		log.Printf("Warning: messageHandler is nil, cannot broadcast message edit")
		return nil
	}

	// Broadcast the message edit to all conversation participants
	p.messageHandler.BroadcastMessageEdit(ctx, message)
	
	log.Printf("Message edit broadcast sent: id=%s, conversation=%s", 
		message.ID().String(), 
		message.ConversationID().String())
	
	return nil
}

// PublishMessageDeleted publishes a message deleted event via WebSocket
func (p *WebSocketEventPublisher) PublishMessageDeleted(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error {
	if p.messageHandler == nil {
		log.Printf("Warning: messageHandler is nil, cannot broadcast message deletion")
		return nil
	}

	// Note: We need the conversation ID to broadcast the deletion
	// In a real implementation, we might want to get this from the message
	// For now, we'll need to modify the interface or get it from context
	log.Printf("Message deletion published: id=%s, user=%s", 
		messageID.String(), 
		userID.String())
	
	// TODO: Implement when we have conversation ID available
	// p.messageHandler.BroadcastMessageDelete(ctx, messageID, conversationID, userID)
	
	return nil
}

// PublishReadStatusUpdated publishes a read status update event via WebSocket
func (p *WebSocketEventPublisher) PublishReadStatusUpdated(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64) error {
	if p.messageHandler == nil {
		log.Printf("Warning: messageHandler is nil, cannot broadcast read status")
		return nil
	}

	// Broadcast the read status update to conversation participants
	p.messageHandler.BroadcastReadStatus(ctx, conversationID, userID, sequence)
	
	log.Printf("Read status broadcast sent: conversation=%s, user=%s, sequence=%d", 
		conversationID.String(), 
		userID.String(), 
		sequence)
	
	return nil
}