package events

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	conversationrepositories "github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/queue"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/websocket"
)

// EventPublisher handles publishing of real-time events and async job queuing
type EventPublisher struct {
	messagingHub *websocket.MessagingHub
	messageQueue *queue.MemoryQueue
	conversationRepo conversationrepositories.ConversationRepository
	
	// Event configuration
	enableRealTime   bool
	enableAsync      bool
	enableNotifications bool
	
	// Event routing configuration
	eventConfig *EventConfig
}

// EventConfig holds configuration for event publishing
type EventConfig struct {
	// Real-time delivery settings
	RealTimeEnabled        bool          `json:"realtime_enabled"`
	RealTimeTimeout        time.Duration `json:"realtime_timeout"`
	
	// Async processing settings
	AsyncEnabled           bool          `json:"async_enabled"`
	AsyncRetries           int           `json:"async_retries"`
	
	// Notification settings
	NotificationsEnabled   bool          `json:"notifications_enabled"`
	OfflineNotificationDelay time.Duration `json:"offline_notification_delay"`
	
	// Event filtering
	FilterSystemMessages   bool          `json:"filter_system_messages"`
	FilterDeletedMessages  bool          `json:"filter_deleted_messages"`
}

// DefaultEventConfig returns default event configuration
func DefaultEventConfig() *EventConfig {
	return &EventConfig{
		RealTimeEnabled:        true,
		RealTimeTimeout:        5 * time.Second,
		AsyncEnabled:           true,
		AsyncRetries:           3,
		NotificationsEnabled:   true,
		OfflineNotificationDelay: 30 * time.Second,
		FilterSystemMessages:   false,
		FilterDeletedMessages:  true,
	}
}

// NewEventPublisher creates a new event publisher
func NewEventPublisher(
	messagingHub *websocket.MessagingHub,
	messageQueue *queue.MemoryQueue,
	conversationRepo conversationrepositories.ConversationRepository,
	config *EventConfig,
) *EventPublisher {
	if config == nil {
		config = DefaultEventConfig()
	}
	
	return &EventPublisher{
		messagingHub:        messagingHub,
		messageQueue:        messageQueue,
		conversationRepo:    conversationRepo,
		enableRealTime:      config.RealTimeEnabled,
		enableAsync:         config.AsyncEnabled,
		enableNotifications: config.NotificationsEnabled,
		eventConfig:         config,
	}
}

// Event types for WebSocket messaging
const (
	EventTypeMessageSent      = "message.sent"
	EventTypeMessageDelivered = "message.delivered"
	EventTypeMessageEdited    = "message.edited"
	EventTypeMessageDeleted   = "message.deleted"
	EventTypeReadStatusUpdated = "read_status.updated"
	EventTypeUserStatusUpdated = "user_status.updated"
	EventTypeTypingIndicator   = "typing.indicator"
	EventTypeConversationUpdated = "conversation.updated"
)

// Event payload structures

type MessageSentEvent struct {
	Type           string                              `json:"type"`
	MessageID      string                              `json:"message_id"`
	ConversationID string                              `json:"conversation_id"`
	SenderID       string                              `json:"sender_id"`
	Content        interface{}                         `json:"content"`
	MessageType    string                              `json:"message_type"`
	Sequence       int64                               `json:"sequence"`
	Timestamp      time.Time                           `json:"timestamp"`
	ThreadID       *string                             `json:"thread_id,omitempty"`
	ReplyToID      *string                             `json:"reply_to_id,omitempty"`
	Metadata       map[string]interface{}              `json:"metadata,omitempty"`
}

type MessageDeliveredEvent struct {
	Type           string    `json:"type"`
	MessageID      string    `json:"message_id"`
	ConversationID string    `json:"conversation_id"`
	UserID         string    `json:"user_id"`
	DeliveredAt    time.Time `json:"delivered_at"`
}

type MessageEditedEvent struct {
	Type           string                 `json:"type"`
	MessageID      string                 `json:"message_id"`
	ConversationID string                 `json:"conversation_id"`
	SenderID       string                 `json:"sender_id"`
	NewContent     interface{}            `json:"new_content"`
	EditedAt       time.Time              `json:"edited_at"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

type MessageDeletedEvent struct {
	Type           string    `json:"type"`
	MessageID      string    `json:"message_id"`
	ConversationID string    `json:"conversation_id"`
	DeletedBy      string    `json:"deleted_by"`
	DeletedAt      time.Time `json:"deleted_at"`
}

type ReadStatusUpdatedEvent struct {
	Type           string    `json:"type"`
	ConversationID string    `json:"conversation_id"`
	UserID         string    `json:"user_id"`
	LastReadSeq    int64     `json:"last_read_seq"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type UserStatusUpdatedEvent struct {
	Type       string                 `json:"type"`
	UserID     string                 `json:"user_id"`
	Status     string                 `json:"status"`
	DeviceID   string                 `json:"device_id"`
	UpdatedAt  time.Time              `json:"updated_at"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

type TypingIndicatorEvent struct {
	Type           string    `json:"type"`
	ConversationID string    `json:"conversation_id"`
	UserID         string    `json:"user_id"`
	IsTyping       bool      `json:"is_typing"`
	Timestamp      time.Time `json:"timestamp"`
}

// PublishMessageSent publishes a message sent event
func (ep *EventPublisher) PublishMessageSent(ctx context.Context, message *messageentities.Message, conversationID conversationentities.ConversationID) error {
	// Skip filtered messages
	if ep.shouldFilterMessage(message) {
		return nil
	}
	
	event := &MessageSentEvent{
		Type:           EventTypeMessageSent,
		MessageID:      message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		Content:        ep.serializeMessageContent(message.Content()),
		MessageType:    string(message.MessageType()),
		Sequence:       message.Sequence(),
		Timestamp:      message.Timestamp(),
		Metadata:       message.Metadata(),
	}
	
	if message.ThreadID() != nil {
		threadID := message.ThreadID().String()
		event.ThreadID = &threadID
	}
	
	if message.ReplyToID() != nil {
		replyToID := message.ReplyToID().String()
		event.ReplyToID = &replyToID
	}
	
	// Real-time delivery
	senderID := message.SenderID()
	if ep.enableRealTime {
		log.Printf("DEBUG: Real-time broadcasting enabled, broadcasting message %s", message.ID().String())
		if err := ep.broadcastEvent(ctx, message.ConversationID(), event, &senderID); err != nil {
			log.Printf("Warning: failed to broadcast message sent event: %v", err)
		}
	} else {
		log.Printf("DEBUG: Real-time broadcasting disabled")
	}
	
	// Async processing for delivery and notifications
	if ep.enableAsync {
		// Queue message delivery job
		deliveryJob := queue.NewDeliverMessageJob(
			message.ID(),
			message.ConversationID(),
			[]uuid.UUID{}, // Will be populated by job processor from conversation participants
			&senderID,
			"real_time",
		)
		
		if err := ep.messageQueue.Enqueue(deliveryJob); err != nil {
			log.Printf("Warning: failed to queue message delivery job: %v", err)
		}
		
		// Queue offline notification job (with delay)
		if ep.enableNotifications {
			notificationJob := queue.NewNotifyOfflineUsersJob(
				message.ID(),
				message.ConversationID(),
				[]uuid.UUID{}, // Will be populated by job processor
				ep.createNotificationData(message),
			)
			
			if err := ep.messageQueue.EnqueueWithDelay(notificationJob, ep.eventConfig.OfflineNotificationDelay); err != nil {
				log.Printf("Warning: failed to queue notification job: %v", err)
			}
		}
		
		// Queue message indexing job
		indexJob := queue.NewIndexMessageJob(
			message.ID(),
			message.ConversationID(),
			message.Content().Text(),
			message.Metadata(),
		)
		
		if err := ep.messageQueue.Enqueue(indexJob); err != nil {
			log.Printf("Warning: failed to queue indexing job: %v", err)
		}
	}
	
	return nil
}

// PublishMessageDelivered publishes a message delivered event
func (ep *EventPublisher) PublishMessageDelivered(ctx context.Context, messageID messageentities.MessageID, userID uuid.UUID) error {
	event := &MessageDeliveredEvent{
		Type:        EventTypeMessageDelivered,
		MessageID:   messageID.String(),
		UserID:      userID.String(),
		DeliveredAt: time.Now(),
	}
	
	// Send delivery confirmation to sender (if online)
	if ep.enableRealTime {
		if ep.messagingHub.IsUserOnline(userID) {
			ep.sendToUser(userID, event)
		}
	}
	
	return nil
}

// PublishMessageEdited publishes a message edited event
func (ep *EventPublisher) PublishMessageEdited(ctx context.Context, message *messageentities.Message) error {
	event := &MessageEditedEvent{
		Type:           EventTypeMessageEdited,
		MessageID:      message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		NewContent:     ep.serializeMessageContent(message.Content()),
		EditedAt:       time.Now(),
		Metadata:       message.Metadata(),
	}
	
	// Real-time delivery
	if ep.enableRealTime {
		senderID := message.SenderID()
		if err := ep.broadcastEvent(ctx, message.ConversationID(), event, &senderID); err != nil {
			log.Printf("Warning: failed to broadcast message edited event: %v", err)
		}
	}
	
	// Update search index
	if ep.enableAsync {
		indexJob := queue.NewIndexMessageJob(
			message.ID(),
			message.ConversationID(),
			message.Content().Text(),
			message.Metadata(),
		)
		
		if err := ep.messageQueue.Enqueue(indexJob); err != nil {
			log.Printf("Warning: failed to queue indexing job: %v", err)
		}
	}
	
	return nil
}

// PublishMessageDeleted publishes a message deleted event
func (ep *EventPublisher) PublishMessageDeleted(ctx context.Context, messageID messageentities.MessageID, userID uuid.UUID) error {
	// Log the deletion event
	log.Printf("Message deleted event for messageID: %s by user: %s", messageID.String(), userID.String())
	
	// Real-time delivery (without conversation ID for now)
	if ep.enableRealTime {
		// Would create and broadcast deletion event if conversation ID was available
		// event := &MessageDeletedEvent{...}
	}
	
	return nil
}

// PublishReadStatusUpdated publishes a read status updated event
func (ep *EventPublisher) PublishReadStatusUpdated(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64) error {
	event := &ReadStatusUpdatedEvent{
		Type:           EventTypeReadStatusUpdated,
		ConversationID: conversationID.String(),
		UserID:         userID.String(),
		LastReadSeq:    sequence,
		UpdatedAt:      time.Now(),
	}
	
	// Real-time delivery to conversation participants
	if ep.enableRealTime {
		if err := ep.broadcastEvent(ctx, conversationID, event, &userID); err != nil {
			log.Printf("Warning: failed to broadcast read status event: %v", err)
		}
	}
	
	return nil
}

// PublishUserStatusUpdated publishes a user status updated event
func (ep *EventPublisher) PublishUserStatusUpdated(ctx context.Context, userID uuid.UUID, status string, deviceID string) error {
	// event := &UserStatusUpdatedEvent{
	// 	Type:      EventTypeUserStatusUpdated,
	// 	UserID:    userID.String(),
	// 	Status:    status,
	// 	DeviceID:  deviceID,
	// 	UpdatedAt: time.Now(),
	// }
	
	// Real-time delivery to user's contacts (would need contact list)
	if ep.enableRealTime {
		log.Printf("Broadcasting user status update for user %s: %s", userID.String(), status)
		// In a real implementation, we'd get the user's contact list
		// and broadcast to all their contacts
	}
	
	return nil
}

// PublishTypingIndicator publishes a typing indicator event
func (ep *EventPublisher) PublishTypingIndicator(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, isTyping bool) error {
	event := &TypingIndicatorEvent{
		Type:           EventTypeTypingIndicator,
		ConversationID: conversationID.String(),
		UserID:         userID.String(),
		IsTyping:       isTyping,
		Timestamp:      time.Now(),
	}
	
	// Real-time delivery only (no persistence needed for typing indicators)
	if ep.enableRealTime {
		if err := ep.broadcastEvent(ctx, conversationID, event, &userID); err != nil {
			log.Printf("Warning: failed to broadcast typing indicator: %v", err)
		}
	}
	
	return nil
}

// PublishConversationCreated publishes a conversation created event
func (ep *EventPublisher) PublishConversationCreated(ctx context.Context, conversation *conversationentities.Conversation) error {
	event := map[string]interface{}{
		"type":            "conversation.created",
		"conversation_id": conversation.ID().String(),
		"conversation":    ep.serializeConversation(conversation),
		"created_by":      conversation.CreatorID().String(),
		"created_at":      conversation.CreatedAt(),
	}
	
	// Real-time delivery to conversation participants
	if ep.enableRealTime {
		if err := ep.broadcastEvent(ctx, conversation.ID(), event, nil); err != nil {
			log.Printf("Warning: failed to broadcast conversation created: %v", err)
		}
	}
	
	return nil
}

// PublishConversationUpdated publishes a conversation updated event
func (ep *EventPublisher) PublishConversationUpdated(ctx context.Context, conversationID conversationentities.ConversationID, updateType string, updatedBy uuid.UUID, data map[string]interface{}) error {
	event := map[string]interface{}{
		"type":            "conversation.updated",
		"conversation_id": conversationID.String(),
		"update_type":     updateType,
		"updated_by":      updatedBy.String(),
		"updated_at":      time.Now(),
		"data":            data,
	}
	
	// Real-time delivery to conversation participants
	if ep.enableRealTime {
		if err := ep.broadcastEvent(ctx, conversationID, event, &updatedBy); err != nil {
			log.Printf("Warning: failed to broadcast conversation update: %v", err)
		}
	}
	
	return nil
}

// Helper methods

// broadcastEvent broadcasts an event to all participants in a conversation
func (ep *EventPublisher) broadcastEvent(ctx context.Context, conversationID conversationentities.ConversationID, event interface{}, excludeUserID *uuid.UUID) error {
	// Resolve conversation participants for targeted broadcasting
	recipients, err := ep.resolveConversationParticipants(ctx, conversationID)
	if err != nil {
		log.Printf("Warning: failed to resolve conversation participants: %v", err)
		return nil // Don't fail the message send
	}
	
	log.Printf("DEBUG: Broadcasting to %d participants for conversation %s", len(recipients), conversationID.String())
	for i, recipient := range recipients {
		log.Printf("DEBUG: Participant %d: %s", i+1, recipient.String())
	}
	
	// Create broadcast message for the hub
	broadcastMsg := &websocket.BroadcastMessage{
		ConversationID: conversationID.String(),
		Message:        event,
		Recipients:     recipients,
		ExcludeUserID:  nil, // Temporarily disable exclusion for testing
		DeliveryMode:   websocket.DeliveryModeRealTime,
	}
	
	// Send through messaging hub
	ep.messagingHub.BroadcastMessage(broadcastMsg)
	
	return nil
}

// sendToUser sends an event to a specific user
func (ep *EventPublisher) sendToUser(userID uuid.UUID, event interface{}) error {
	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}
	
	// Send to user through hub
	ep.messagingHub.SendToUser(userID, eventData, websocket.PriorityHigh)
	
	return nil
}

// shouldFilterMessage determines if a message should be filtered from events
func (ep *EventPublisher) shouldFilterMessage(message *messageentities.Message) bool {
	if ep.eventConfig.FilterSystemMessages && message.IsSystemMessage() {
		return true
	}
	
	if ep.eventConfig.FilterDeletedMessages && message.IsDeleted() {
		return true
	}
	
	return false
}

// serializeMessageContent converts message content to a serializable format
func (ep *EventPublisher) serializeMessageContent(content valueobjects.MessageContent) interface{} {
	// Convert to a map structure suitable for JSON serialization
	contentMap := map[string]interface{}{
		"text":     content.Text(),
		"mentions": content.Mentions(),
		"links":    content.Links(),
	}
	
	// Add attachments if any
	if len(content.Attachments()) > 0 {
		attachments := make([]map[string]interface{}, len(content.Attachments()))
		for i, attachment := range content.Attachments() {
			attachments[i] = map[string]interface{}{
				"id":        attachment.ID(),
				"name":      attachment.Name(),
				"url":       attachment.URL(),
				"mime_type": attachment.MimeType(),
				"size":      attachment.Size(),
				"metadata":  attachment.Metadata(),
			}
		}
		contentMap["attachments"] = attachments
	}
	
	return contentMap
}

// resolveConversationParticipants resolves the participants of a conversation
func (ep *EventPublisher) resolveConversationParticipants(ctx context.Context, conversationID conversationentities.ConversationID) ([]uuid.UUID, error) {
	conversation, err := ep.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}
	
	participants := conversation.Participants()
	recipients := make([]uuid.UUID, len(participants))
	for i, participant := range participants {
		recipients[i] = participant.UserID()
	}
	
	return recipients, nil
}

// createNotificationData creates notification data for offline users
func (ep *EventPublisher) createNotificationData(message *messageentities.Message) map[string]interface{} {
	return map[string]interface{}{
		"message_id":      message.ID().String(),
		"conversation_id": message.ConversationID().String(),
		"sender_id":       message.SenderID().String(),
		"message_type":    string(message.MessageType()),
		"content_preview": ep.createContentPreview(message.Content()),
		"timestamp":       message.Timestamp(),
	}
}

// serializeConversation converts a conversation to a serializable format
func (ep *EventPublisher) serializeConversation(conversation *conversationentities.Conversation) interface{} {
	convMap := map[string]interface{}{
		"id":            conversation.ID().String(),
		"type":          string(conversation.Type()),
		"creator_id":    conversation.CreatorID().String(),
		"message_count": conversation.MessageCount(),
		"max_sequence":  conversation.MaxSequence(),
		"last_activity": conversation.LastActivity(),
		"created_at":    conversation.CreatedAt(),
		"updated_at":    conversation.UpdatedAt(),
	}
	
	if conversation.Name() != nil {
		convMap["name"] = *conversation.Name()
	}
	
	if conversation.Description() != nil {
		convMap["description"] = *conversation.Description()
	}
	
	if conversation.TeamID() != nil {
		convMap["team_id"] = conversation.TeamID().String()
	}
	
	if conversation.LastMessageID() != nil {
		convMap["last_message_id"] = *conversation.LastMessageID()
	}
	
	// Add participants
	participants := make([]map[string]interface{}, len(conversation.Participants()))
	for i, participant := range conversation.Participants() {
		participants[i] = map[string]interface{}{
			"user_id":       participant.UserID().String(),
			"role":          string(participant.Role()),
			"joined_at":     participant.JoinedAt(),
			"last_read_seq": participant.LastReadSeq(),
		}
	}
	convMap["participants"] = participants
	
	return convMap
}

// createContentPreview creates a preview of message content for notifications
func (ep *EventPublisher) createContentPreview(content valueobjects.MessageContent) string {
	text := content.Text()
	
	// Truncate long messages
	if len(text) > 100 {
		text = text[:97] + "..."
	}
	
	// Add attachment indicator
	if len(content.Attachments()) > 0 {
		attachmentCount := len(content.Attachments())
		if text == "" {
			if attachmentCount == 1 {
				text = "📎 Sent an attachment"
			} else {
				text = fmt.Sprintf("📎 Sent %d attachments", attachmentCount)
			}
		} else {
			text += fmt.Sprintf(" (📎 %d attachment%s)", attachmentCount, func() string {
				if attachmentCount > 1 {
					return "s"
				}
				return ""
			}())
		}
	}
	
	return text
}

// Event publisher factory methods

// NewRealtimeEventPublisher creates an event publisher for real-time only
func NewRealtimeEventPublisher(messagingHub *websocket.MessagingHub, conversationRepo conversationrepositories.ConversationRepository) *EventPublisher {
	config := &EventConfig{
		RealTimeEnabled:      true,
		AsyncEnabled:         false,
		NotificationsEnabled: false,
	}
	
	return NewEventPublisher(messagingHub, nil, conversationRepo, config)
}

// NewAsyncEventPublisher creates an event publisher for async processing only
func NewAsyncEventPublisher(messageQueue *queue.MemoryQueue, conversationRepo conversationrepositories.ConversationRepository) *EventPublisher {
	config := &EventConfig{
		RealTimeEnabled:      false,
		AsyncEnabled:         true,
		NotificationsEnabled: true,
	}
	
	return NewEventPublisher(nil, messageQueue, conversationRepo, config)
}

// NewFullEventPublisher creates an event publisher with all features enabled
func NewFullEventPublisher(messagingHub *websocket.MessagingHub, messageQueue *queue.MemoryQueue, conversationRepo conversationrepositories.ConversationRepository) *EventPublisher {
	return NewEventPublisher(messagingHub, messageQueue, conversationRepo, DefaultEventConfig())
}

// Event statistics and monitoring

// GetEventStats returns statistics about event publishing
func (ep *EventPublisher) GetEventStats() *EventStats {
	return &EventStats{
		RealTimeEnabled:      ep.enableRealTime,
		AsyncEnabled:         ep.enableAsync,
		NotificationsEnabled: ep.enableNotifications,
		EventsPublished:      0, // Would need tracking
		EventsQueued:         0, // Would need tracking
		EventsFailed:         0, // Would need tracking
		LastEventAt:          time.Now(),
	}
}

// EventStats represents event publishing statistics
type EventStats struct {
	RealTimeEnabled      bool      `json:"realtime_enabled"`
	AsyncEnabled         bool      `json:"async_enabled"`
	NotificationsEnabled bool      `json:"notifications_enabled"`
	EventsPublished      int64     `json:"events_published"`
	EventsQueued         int64     `json:"events_queued"`
	EventsFailed         int64     `json:"events_failed"`
	LastEventAt          time.Time `json:"last_event_at"`
}