package postgres

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
)

type postgresConversationRepository struct {
	db *gorm.DB
}

// PostgreSQL models
type ConversationModel struct {
	ID            uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Type          string    `gorm:"not null"`
	Name          *string   `gorm:"size:255"`
	Description   *string   `gorm:"size:1000"`
	TeamID        *uuid.UUID `gorm:"type:uuid"`
	CreatorID     uuid.UUID  `gorm:"type:uuid;not null"`
	LastMessageID *string    `gorm:"size:255"`
	LastActivity  time.Time  `gorm:"not null"`
	MessageCount  int64      `gorm:"default:0"`
	MaxSequence   int64      `gorm:"default:0"`
	CreatedAt     time.Time  `gorm:"not null"`
	UpdatedAt     time.Time  `gorm:"not null"`
	DeletedAt     *time.Time `gorm:"index"`
	
	Participants []ConversationParticipantModel `gorm:"foreignKey:ConversationID"`
}

type ConversationParticipantModel struct {
	ConversationID uuid.UUID `gorm:"type:uuid;primaryKey"`
	UserID         uuid.UUID `gorm:"type:uuid;primaryKey"`
	JoinedAt       time.Time `gorm:"not null"`
	LastReadSeq    int64     `gorm:"default:0"`
	Role           string    `gorm:"default:'member'"`
}

func NewPostgresConversationRepository(db *gorm.DB) repositories.ConversationRepository {
	repo := &postgresConversationRepository{db: db}
	
	// Auto migrate tables
	db.AutoMigrate(&ConversationModel{}, &ConversationParticipantModel{})
	
	return repo
}

func (r *postgresConversationRepository) Create(ctx context.Context, conversation *entities.Conversation) error {
	model := r.conversationToModel(conversation)
	
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create conversation
		if err := tx.Create(model).Error; err != nil {
			return err
		}
		
		// Create participants
		for _, participant := range conversation.Participants() {
			participantModel := ConversationParticipantModel{
				ConversationID: uuid.UUID(conversation.ID()),
				UserID:         participant.UserID(),
				JoinedAt:       participant.JoinedAt(),
				LastReadSeq:    participant.LastReadSeq(),
				Role:           string(participant.Role()),
			}
			
			if err := tx.Create(&participantModel).Error; err != nil {
				return err
			}
		}
		
		return nil
	})
}

func (r *postgresConversationRepository) GetByID(ctx context.Context, id entities.ConversationID) (*entities.Conversation, error) {
	var model ConversationModel
	
	err := r.db.WithContext(ctx).
		Preload("Participants").
		Where("id = ? AND deleted_at IS NULL", uuid.UUID(id)).
		First(&model).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repositories.ErrConversationNotFound
		}
		return nil, err
	}
	
	return r.modelToConversation(&model), nil
}

func (r *postgresConversationRepository) Update(ctx context.Context, conversation *entities.Conversation) error {
	model := r.conversationToModel(conversation)
	
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update conversation
		if err := tx.Model(&ConversationModel{}).
			Where("id = ?", uuid.UUID(conversation.ID())).
			Updates(model).Error; err != nil {
			return err
		}
		
		// Delete existing participants and recreate them
		if err := tx.Where("conversation_id = ?", uuid.UUID(conversation.ID())).
			Delete(&ConversationParticipantModel{}).Error; err != nil {
			return err
		}
		
		// Recreate participants
		for _, participant := range conversation.Participants() {
			participantModel := ConversationParticipantModel{
				ConversationID: uuid.UUID(conversation.ID()),
				UserID:         participant.UserID(),
				JoinedAt:       participant.JoinedAt(),
				LastReadSeq:    participant.LastReadSeq(),
				Role:           string(participant.Role()),
			}
			
			if err := tx.Create(&participantModel).Error; err != nil {
				return err
			}
		}
		
		return nil
	})
}

func (r *postgresConversationRepository) Delete(ctx context.Context, id entities.ConversationID) error {
	return r.db.WithContext(ctx).
		Model(&ConversationModel{}).
		Where("id = ?", uuid.UUID(id)).
		Update("deleted_at", time.Now()).Error
}

func (r *postgresConversationRepository) GetUserConversations(ctx context.Context, userID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error) {
	var models []ConversationModel
	
	err := r.db.WithContext(ctx).
		Preload("Participants").
		Joins("JOIN conversation_participant_models ON conversation_models.id = conversation_participant_models.conversation_id").
		Where("conversation_participant_models.user_id = ? AND conversation_models.deleted_at IS NULL", userID).
		Order("conversation_models.last_activity DESC").
		Limit(limit).
		Offset(offset).
		Find(&models).Error
	
	if err != nil {
		return nil, err
	}
	
	conversations := make([]*entities.Conversation, len(models))
	for i, model := range models {
		conversations[i] = r.modelToConversation(&model)
	}
	
	return conversations, nil
}

func (r *postgresConversationRepository) GetByParticipant(ctx context.Context, userID uuid.UUID, limit int, offset int64) ([]*entities.Conversation, error) {
	var models []ConversationModel
	
	err := r.db.WithContext(ctx).
		Preload("Participants").
		Joins("JOIN conversation_participant_models ON conversation_models.id = conversation_participant_models.conversation_id").
		Where("conversation_participant_models.user_id = ? AND conversation_models.deleted_at IS NULL", userID).
		Order("conversation_models.last_activity DESC").
		Limit(limit).
		Offset(int(offset)).
		Find(&models).Error
	
	if err != nil {
		return nil, err
	}
	
	conversations := make([]*entities.Conversation, len(models))
	for i, model := range models {
		conversations[i] = r.modelToConversation(&model)
	}
	
	return conversations, nil
}

func (r *postgresConversationRepository) GetTeamConversations(ctx context.Context, teamID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error) {
	var models []ConversationModel
	
	err := r.db.WithContext(ctx).
		Preload("Participants").
		Where("team_id = ? AND deleted_at IS NULL", teamID).
		Order("last_activity DESC").
		Limit(limit).
		Offset(offset).
		Find(&models).Error
	
	if err != nil {
		return nil, err
	}
	
	conversations := make([]*entities.Conversation, len(models))
	for i, model := range models {
		conversations[i] = r.modelToConversation(&model)
	}
	
	return conversations, nil
}

func (r *postgresConversationRepository) GetDirectConversation(ctx context.Context, user1ID, user2ID uuid.UUID) (*entities.Conversation, error) {
	var model ConversationModel
	
	err := r.db.WithContext(ctx).
		Preload("Participants").
		Joins("JOIN conversation_participant_models cp1 ON conversation_models.id = cp1.conversation_id").
		Joins("JOIN conversation_participant_models cp2 ON conversation_models.id = cp2.conversation_id").
		Where("conversation_models.type = ? AND conversation_models.deleted_at IS NULL", "direct").
		Where("cp1.user_id = ? AND cp2.user_id = ?", user1ID, user2ID).
		First(&model).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repositories.ErrConversationNotFound
		}
		return nil, err
	}
	
	return r.modelToConversation(&model), nil
}

func (r *postgresConversationRepository) AddParticipant(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, role entities.ParticipantRole) error {
	participant := ConversationParticipantModel{
		ConversationID: uuid.UUID(conversationID),
		UserID:         userID,
		JoinedAt:       time.Now(),
		LastReadSeq:    0,
		Role:           string(role),
	}
	
	return r.db.WithContext(ctx).Create(&participant).Error
}

func (r *postgresConversationRepository) RemoveParticipant(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Where("conversation_id = ? AND user_id = ?", uuid.UUID(conversationID), userID).
		Delete(&ConversationParticipantModel{}).Error
}

func (r *postgresConversationRepository) UpdateParticipantRole(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, role entities.ParticipantRole) error {
	return r.db.WithContext(ctx).
		Model(&ConversationParticipantModel{}).
		Where("conversation_id = ? AND user_id = ?", uuid.UUID(conversationID), userID).
		Update("role", string(role)).Error
}

func (r *postgresConversationRepository) GetParticipants(ctx context.Context, conversationID entities.ConversationID) ([]entities.ConversationParticipant, error) {
	var models []ConversationParticipantModel
	
	err := r.db.WithContext(ctx).
		Where("conversation_id = ?", uuid.UUID(conversationID)).
		Find(&models).Error
	
	if err != nil {
		return nil, err
	}
	
	participants := make([]entities.ConversationParticipant, len(models))
	for i, model := range models {
		participants[i] = entities.NewConversationParticipant(
			entities.ConversationID(model.ConversationID),
			model.UserID,
			entities.ParticipantRole(model.Role),
		)
	}
	
	return participants, nil
}

func (r *postgresConversationRepository) GetParticipantIDs(ctx context.Context, conversationID entities.ConversationID) ([]uuid.UUID, error) {
	var userIDs []uuid.UUID
	
	err := r.db.WithContext(ctx).
		Model(&ConversationParticipantModel{}).
		Where("conversation_id = ?", uuid.UUID(conversationID)).
		Pluck("user_id", &userIDs).Error
	
	if err != nil {
		return nil, err
	}
	
	return userIDs, nil
}

func (r *postgresConversationRepository) UpdateLastActivity(ctx context.Context, conversationID entities.ConversationID, lastMessageID string, sequence int64) error {
	updates := map[string]interface{}{
		"last_message_id": lastMessageID,
		"last_activity":   time.Now(),
		"message_count":   gorm.Expr("message_count + 1"),
		"updated_at":      time.Now(),
	}
	
	if sequence > 0 {
		updates["max_sequence"] = gorm.Expr("GREATEST(max_sequence, ?)", sequence)
	}
	
	return r.db.WithContext(ctx).
		Model(&ConversationModel{}).
		Where("id = ?", uuid.UUID(conversationID)).
		Updates(updates).Error
}

func (r *postgresConversationRepository) UpdateLastReadSequence(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, sequence int64) error {
	return r.db.WithContext(ctx).
		Model(&ConversationParticipantModel{}).
		Where("conversation_id = ? AND user_id = ?", uuid.UUID(conversationID), userID).
		Update("last_read_seq", sequence).Error
}

func (r *postgresConversationRepository) GetUnreadConversations(ctx context.Context, userID uuid.UUID) ([]*repositories.ConversationUnreadInfo, error) {
	fmt.Printf("================= NEW IMPLEMENTATION CALLED =================\n")
	
	var results []struct {
		ConversationID uuid.UUID
		UnreadCount    int64
		LastMessageID  *string
		LastActivity   time.Time
	}
	
	// Use raw SQL to avoid GORM issues with complex expressions
	rawSQL := `
		SELECT 
			conversation_models.id as conversation_id,
			GREATEST(0, conversation_models.max_sequence - COALESCE(conversation_participant_models.last_read_seq, 0)) as unread_count,
			conversation_models.last_message_id,
			conversation_models.last_activity
		FROM conversation_models
		JOIN conversation_participant_models ON conversation_models.id = conversation_participant_models.conversation_id
		WHERE conversation_participant_models.user_id = ? 
		  AND conversation_models.deleted_at IS NULL
		  AND conversation_models.max_sequence > COALESCE(conversation_participant_models.last_read_seq, 0)
		ORDER BY conversation_models.last_activity DESC
	`
	
	// Debug: Let's see what query is actually being executed
	fmt.Printf("DEBUG: Executing query: %s with userID: %s\n", rawSQL, userID.String())
	
	err := r.db.WithContext(ctx).Raw(rawSQL, userID).Scan(&results).Error
	
	if err != nil {
		return nil, err
	}
	
	unreadInfos := make([]*repositories.ConversationUnreadInfo, len(results))
	for i, result := range results {
		unreadInfos[i] = &repositories.ConversationUnreadInfo{
			ConversationID: entities.ConversationID(result.ConversationID),
			UnreadCount:    result.UnreadCount,
			LastMessageID:  result.LastMessageID,
			LastActivity:   result.LastActivity,
		}
	}
	
	return unreadInfos, nil
}

func (r *postgresConversationRepository) SearchConversations(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Conversation, error) {
	var models []ConversationModel
	
	searchQuery := fmt.Sprintf("%%%s%%", query)
	
	err := r.db.WithContext(ctx).
		Preload("Participants").
		Joins("JOIN conversation_participant_models ON conversation_models.id = conversation_participant_models.conversation_id").
		Where("conversation_participant_models.user_id = ? AND conversation_models.deleted_at IS NULL", userID).
		Where("conversation_models.name ILIKE ? OR conversation_models.description ILIKE ?", searchQuery, searchQuery).
		Order("conversation_models.last_activity DESC").
		Limit(limit).
		Offset(offset).
		Find(&models).Error
	
	if err != nil {
		return nil, err
	}
	
	conversations := make([]*entities.Conversation, len(models))
	for i, model := range models {
		conversations[i] = r.modelToConversation(&model)
	}
	
	return conversations, nil
}

func (r *postgresConversationRepository) GetRecentConversations(ctx context.Context, userID uuid.UUID, limit int) ([]*entities.Conversation, error) {
	return r.GetUserConversations(ctx, userID, limit, 0)
}

func (r *postgresConversationRepository) GetConversationsBatch(ctx context.Context, conversationIDs []entities.ConversationID) ([]*entities.Conversation, error) {
	if len(conversationIDs) == 0 {
		return []*entities.Conversation{}, nil
	}
	
	uuidIDs := make([]uuid.UUID, len(conversationIDs))
	for i, id := range conversationIDs {
		uuidIDs[i] = uuid.UUID(id)
	}
	
	var models []ConversationModel
	
	err := r.db.WithContext(ctx).
		Preload("Participants").
		Where("id IN ? AND deleted_at IS NULL", uuidIDs).
		Find(&models).Error
	
	if err != nil {
		return nil, err
	}
	
	conversations := make([]*entities.Conversation, len(models))
	for i, model := range models {
		conversations[i] = r.modelToConversation(&model)
	}
	
	return conversations, nil
}

func (r *postgresConversationRepository) UpdateLastActivityBatch(ctx context.Context, updates []repositories.ConversationActivityUpdate) error {
	if len(updates) == 0 {
		return nil
	}
	
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			updateData := map[string]interface{}{
				"last_message_id": update.LastMessageID,
				"last_activity":   update.Timestamp,
				"message_count":   gorm.Expr("message_count + 1"),
				"updated_at":      time.Now(),
			}
			
			if update.Sequence > 0 {
				updateData["max_sequence"] = gorm.Expr("GREATEST(max_sequence, ?)", update.Sequence)
			}
			
			if err := tx.Model(&ConversationModel{}).
				Where("id = ?", uuid.UUID(update.ConversationID)).
				Updates(updateData).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (r *postgresConversationRepository) GetConversationStats(ctx context.Context, conversationID entities.ConversationID, fromTime, toTime time.Time) (*repositories.ConversationStats, error) {
	var result struct {
		MessageCount       int64
		ActiveParticipants int64
		CreatedAt          time.Time
		LastActivity       time.Time
	}
	
	err := r.db.WithContext(ctx).
		Table("conversation_models").
		Select(`
			message_count,
			(SELECT COUNT(*) FROM conversation_participant_models WHERE conversation_id = conversation_models.id) as active_participants,
			created_at,
			last_activity
		`).
		Where("id = ?", uuid.UUID(conversationID)).
		Scan(&result).Error
	
	if err != nil {
		return nil, err
	}
	
	stats := &repositories.ConversationStats{
		TotalMessages:      result.MessageCount,
		ActiveParticipants: result.ActiveParticipants,
		CreatedAt:          result.CreatedAt,
		LastActivity:       result.LastActivity,
	}
	
	return stats, nil
}

func (r *postgresConversationRepository) GetUserConversationStats(ctx context.Context, userID uuid.UUID, fromTime, toTime time.Time) (*repositories.UserConversationStats, error) {
	var result struct {
		TotalConversations   int64
		DirectConversations  int64
		GroupConversations   int64
		TeamConversations    int64
		CreatedConversations int64
		FirstConversation    *time.Time
		LastActivity         *time.Time
	}
	
	err := r.db.WithContext(ctx).
		Table("conversation_models").
		Select(`
			COUNT(*) as total_conversations,
			SUM(CASE WHEN type = 'direct' THEN 1 ELSE 0 END) as direct_conversations,
			SUM(CASE WHEN type = 'group' THEN 1 ELSE 0 END) as group_conversations,
			SUM(CASE WHEN type = 'team_channel' THEN 1 ELSE 0 END) as team_conversations,
			SUM(CASE WHEN creator_id = ? THEN 1 ELSE 0 END) as created_conversations,
			MIN(created_at) as first_conversation,
			MAX(last_activity) as last_activity
		`, userID).
		Joins("JOIN conversation_participant_models ON conversation_models.id = conversation_participant_models.conversation_id").
		Where("conversation_participant_models.user_id = ? AND conversation_models.deleted_at IS NULL", userID).
		Where("conversation_models.created_at BETWEEN ? AND ?", fromTime, toTime).
		Scan(&result).Error
	
	if err != nil {
		return nil, err
	}
	
	stats := &repositories.UserConversationStats{
		TotalConversations:   result.TotalConversations,
		DirectConversations:  result.DirectConversations,
		GroupConversations:   result.GroupConversations,
		TeamConversations:    result.TeamConversations,
		CreatedConversations: result.CreatedConversations,
		FirstConversation:    result.FirstConversation,
		LastActivity:         result.LastActivity,
	}
	
	// Calculate active conversations (conversations with activity in last 30 days)
	var activeCount int64
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	
	r.db.WithContext(ctx).
		Table("conversation_models").
		Joins("JOIN conversation_participant_models ON conversation_models.id = conversation_participant_models.conversation_id").
		Where("conversation_participant_models.user_id = ? AND conversation_models.deleted_at IS NULL", userID).
		Where("conversation_models.last_activity > ?", thirtyDaysAgo).
		Count(&activeCount)
	
	stats.ActiveConversations = activeCount
	
	return stats, nil
}

// Conversion methods
func (r *postgresConversationRepository) conversationToModel(conversation *entities.Conversation) *ConversationModel {
	model := &ConversationModel{
		ID:            uuid.UUID(conversation.ID()),
		Type:          string(conversation.Type()),
		Name:          conversation.Name(),
		Description:   conversation.Description(),
		TeamID:        conversation.TeamID(),
		CreatorID:     conversation.CreatorID(),
		LastMessageID: conversation.LastMessageID(),
		LastActivity:  conversation.LastActivity(),
		MessageCount:  conversation.MessageCount(),
		MaxSequence:   conversation.MaxSequence(),
		CreatedAt:     conversation.CreatedAt(),
		UpdatedAt:     conversation.UpdatedAt(),
		DeletedAt:     conversation.DeletedAt(),
	}
	
	return model
}

func (r *postgresConversationRepository) modelToConversation(model *ConversationModel) *entities.Conversation {
	participants := make([]entities.ConversationParticipant, len(model.Participants))
	for i, p := range model.Participants {
		participants[i] = entities.NewConversationParticipant(
			entities.ConversationID(p.ConversationID),
			p.UserID,
			entities.ParticipantRole(p.Role),
		)
	}
	
	return entities.ReconstructConversation(
		entities.ConversationID(model.ID),
		entities.ConversationType(model.Type),
		model.Name,
		model.Description,
		model.TeamID,
		model.CreatorID,
		model.LastMessageID,
		model.LastActivity,
		model.MessageCount,
		model.MaxSequence,
		model.CreatedAt,
		model.UpdatedAt,
		model.DeletedAt,
		participants,
	)
}