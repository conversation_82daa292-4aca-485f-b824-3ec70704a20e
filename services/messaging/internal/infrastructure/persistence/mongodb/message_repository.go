package mongodb

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/swork-team/platform/pkg/config"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
)

type mongoMessageRepository struct {
	client              *mongo.Client
	database            *mongo.Database
	documentsCollection *mongo.Collection
	config              *config.MessagingServiceConfig
}

type MessageDocumentModel struct {
	ID             string                       `bson:"_id"`
	ConversationID string                       `bson:"conversation_id"`
	ShardIndex     int64                        `bson:"shard_index"`
	Messages       map[string]*MessageInfoModel `bson:"messages"`
	CreatedAt      time.Time                    `bson:"created_at"`
	UpdatedAt      time.Time                    `bson:"updated_at"`
}

type MessageInfoModel struct {
	Message   *MessageModel `bson:"msg"`
	DeletedBy []string      `bson:"deleted_by"`
	IsRead    bool          `bson:"is_read"`
}

type MessageModel struct {
	ID             string                 `bson:"_id"`
	ConversationID string                 `bson:"conversation_id"`
	SenderID       string                 `bson:"sender_id"`
	Content        MessageContentModel    `bson:"content"`
	MessageType    string                 `bson:"message_type"`
	Sequence       int64                  `bson:"sequence"`
	ThreadID       *string                `bson:"thread_id,omitempty"`
	ReplyToID      *string                `bson:"reply_to_id,omitempty"`
	Status         string                 `bson:"status"`
	Timestamp      time.Time              `bson:"timestamp"`
	EditedAt       *time.Time             `bson:"edited_at,omitempty"`
	DeletedAt      *time.Time             `bson:"deleted_at,omitempty"`
	Metadata       map[string]interface{} `bson:"metadata"`
	ReadBy         []ReadStatusModel      `bson:"read_by"`
}

type MessageContentModel struct {
	Text        string                   `bson:"text,omitempty"`
	Attachments []MessageAttachmentModel `bson:"attachments,omitempty"`
	Mentions    []string                 `bson:"mentions,omitempty"`
	Links       []string                 `bson:"links,omitempty"`
}

type MessageAttachmentModel struct {
	ID       string                 `bson:"id"`
	Name     string                 `bson:"name"`
	URL      string                 `bson:"url"`
	MimeType string                 `bson:"mime_type"`
	Size     int64                  `bson:"size"`
	Metadata map[string]interface{} `bson:"metadata"`
}

type ReadStatusModel struct {
	UserID string    `bson:"user_id"`
	ReadAt time.Time `bson:"read_at"`
}

func NewMongoMessageRepository(client *mongo.Client, databaseName string, config *config.MessagingServiceConfig) repositories.MessageRepository {
	database := client.Database(databaseName)

	repo := &mongoMessageRepository{
		client:              client,
		database:            database,
		documentsCollection: database.Collection("message_documents"),
		config:              config,
	}

	// Create indexes
	repo.createIndexes()

	return repo
}

func (r *mongoMessageRepository) createIndexes() {
	ctx := context.Background()

	// Message document indexes - optimized for document-based sharding
	documentIndexes := []mongo.IndexModel{
		// Primary document lookup - unique composite index
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "shard_index", Value: 1},
			},
			Options: options.Index().SetUnique(true).SetName("conversation_shard_unique"),
		},
		// Fast conversation-based queries
		{
			Keys:    bson.D{{Key: "conversation_id", Value: 1}},
			Options: options.Index().SetName("conversation_lookup"),
		},
		// Optimized shard range queries for pagination
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "shard_index", Value: -1},
				{Key: "updated_at", Value: -1},
			},
			Options: options.Index().SetName("conversation_shard_time"),
		},
		// Message ID lookup optimization - individual indexes for first 10 slots
		{
			Keys:    bson.D{{Key: "messages.0.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_0"),
		},
		{
			Keys:    bson.D{{Key: "messages.1.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_1"),
		},
		{
			Keys:    bson.D{{Key: "messages.2.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_2"),
		},
		{
			Keys:    bson.D{{Key: "messages.3.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_3"),
		},
		{
			Keys:    bson.D{{Key: "messages.4.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_4"),
		},
		{
			Keys:    bson.D{{Key: "messages.5.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_5"),
		},
		{
			Keys:    bson.D{{Key: "messages.6.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_6"),
		},
		{
			Keys:    bson.D{{Key: "messages.7.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_7"),
		},
		{
			Keys:    bson.D{{Key: "messages.8.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_8"),
		},
		{
			Keys:    bson.D{{Key: "messages.9.msg._id", Value: 1}},
			Options: options.Index().SetSparse(true).SetName("message_id_slot_9"),
		},
		// Thread-based queries optimization
		{
			Keys: bson.D{
				{Key: "messages.msg.thread_id", Value: 1},
				{Key: "messages.msg.timestamp", Value: 1},
			},
			Options: options.Index().SetSparse(true).SetName("thread_messages"),
		},
		// Read status queries optimization
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.read_by.user_id", Value: 1},
				{Key: "messages.msg.sequence", Value: 1},
			},
			Options: options.Index().SetSparse(true).SetName("read_status_lookup"),
		},
		// Sender-based queries within documents
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.sender_id", Value: 1},
				{Key: "messages.msg.timestamp", Value: -1},
			},
			Options: options.Index().SetSparse(true).SetName("sender_messages"),
		},
		// Optimized text search with conversation context
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.content.text", Value: "text"},
			},
			Options: options.Index().SetName("conversation_text_search"),
		},
		// Full-text search across all documents
		{
			Keys:    bson.D{{Key: "messages.msg.content.text", Value: "text"}},
			Options: options.Index().SetName("global_text_search"),
		},
		// Time-based queries for message history
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.timestamp", Value: -1},
			},
			Options: options.Index().SetSparse(true).SetName("message_history"),
		},
		// Sequence-based queries for pagination
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.sequence", Value: 1},
			},
			Options: options.Index().SetSparse(true).SetName("sequence_lookup"),
		},
		// Message type filtering
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.message_type", Value: 1},
				{Key: "messages.msg.timestamp", Value: -1},
			},
			Options: options.Index().SetSparse(true).SetName("message_type_filter"),
		},
		// Deleted message queries
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.deleted_at", Value: 1},
			},
			Options: options.Index().SetSparse(true).SetName("deleted_messages"),
		},
		// Message mentions optimization
		{
			Keys: bson.D{
				{Key: "messages.msg.content.mentions", Value: 1},
				{Key: "messages.msg.timestamp", Value: -1},
			},
			Options: options.Index().SetSparse(true).SetName("message_mentions"),
		},
		// Media/attachment queries
		{
			Keys: bson.D{
				{Key: "conversation_id", Value: 1},
				{Key: "messages.msg.content.attachments", Value: 1},
				{Key: "messages.msg.timestamp", Value: -1},
			},
			Options: options.Index().SetSparse(true).SetName("media_messages"),
		},
	}

	r.documentsCollection.Indexes().CreateMany(ctx, documentIndexes)
}

func (r *mongoMessageRepository) StoreMessage(ctx context.Context, message *entities.Message) error {
	// Store using document-based approach (OpenIM pattern)
	return r.storeMessageInDocument(ctx, message)
}

func (r *mongoMessageRepository) storeMessageInDocument(ctx context.Context, message *entities.Message) error {
	conversationID := message.ConversationID().String()
	sequence := message.Sequence()

	// Calculate document shard index
	shardIndex := entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument)
	messageIndex := entities.GetMessageIndex(sequence, r.config.Messaging.MessagesPerDocument)

	documentID := fmt.Sprintf("%s:%d", conversationID, shardIndex)

	// Convert message to model
	messageModel := r.messageToModel(message)
	messageInfo := &MessageInfoModel{
		Message:   messageModel,
		DeletedBy: make([]string, 0),
		IsRead:    false,
	}

	// Try to update existing document first
	filter := bson.M{"_id": documentID}
	update := bson.M{
		"$set": bson.M{
			fmt.Sprintf("messages.%d", messageIndex): messageInfo,
			"updated_at":                             time.Now(),
		},
		"$setOnInsert": bson.M{
			"conversation_id": conversationID,
			"shard_index":     shardIndex,
			"created_at":      time.Now(),
		},
	}

	// Add explicit timeout for MongoDB operation
	mongoCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	opts := options.Update().SetUpsert(true)
	_, err := r.documentsCollection.UpdateOne(mongoCtx, filter, update, opts)

	if err != nil {
		// If upsert fails, try inserting a new document
		if mongo.IsDuplicateKeyError(err) {
			// Document exists, retry the update with timeout
			retryCtx, retryCancel := context.WithTimeout(ctx, 3*time.Second)
			defer retryCancel()
			_, err = r.documentsCollection.UpdateOne(retryCtx, filter, update)
		}
	}

	return err
}

func (r *mongoMessageRepository) GetMessage(ctx context.Context, messageID entities.MessageID) (*entities.Message, error) {
	messageIDStr := messageID.String()

	// Use optimized query with compound index on message IDs
	// Try most common message slots first (0-9) with indexed query
	filter := bson.M{
		"$or": []bson.M{
			{"messages.0.msg._id": messageIDStr},
			{"messages.1.msg._id": messageIDStr},
			{"messages.2.msg._id": messageIDStr},
			{"messages.3.msg._id": messageIDStr},
			{"messages.4.msg._id": messageIDStr},
			{"messages.5.msg._id": messageIDStr},
			{"messages.6.msg._id": messageIDStr},
			{"messages.7.msg._id": messageIDStr},
			{"messages.8.msg._id": messageIDStr},
			{"messages.9.msg._id": messageIDStr},
		},
	}

	var document MessageDocumentModel
	err := r.documentsCollection.FindOne(ctx, filter).Decode(&document)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Fall back to broader search if not found in first 10 slots
			return r.getMessageFullScan(ctx, messageID)
		}
		return nil, err
	}

	// Find the specific message in the document
	for _, msgInfo := range document.Messages {
		if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.ID == messageIDStr {
			return r.modelToMessage(msgInfo.Message), nil
		}
	}

	return nil, repositories.ErrMessageNotFound
}

// getMessageFullScan performs a full scan as fallback for messages in higher slots
func (r *mongoMessageRepository) getMessageFullScan(ctx context.Context, messageID entities.MessageID) (*entities.Message, error) {
	messageIDStr := messageID.String()

	// Build filters for remaining message slots (10-99)
	orFilters := []bson.M{}
	for i := 10; i < 100; i++ {
		orFilters = append(orFilters, bson.M{
			fmt.Sprintf("messages.%d.msg._id", i): messageIDStr,
		})
	}

	filter := bson.M{"$or": orFilters}

	var document MessageDocumentModel
	err := r.documentsCollection.FindOne(ctx, filter).Decode(&document)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, repositories.ErrMessageNotFound
		}
		return nil, err
	}

	// Find the specific message in the document
	for _, msgInfo := range document.Messages {
		if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.ID == messageIDStr {
			return r.modelToMessage(msgInfo.Message), nil
		}
	}

	return nil, repositories.ErrMessageNotFound
}

func (r *mongoMessageRepository) GetMessageBySequence(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64) (*entities.Message, error) {
	shardIndex := entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument)
	messageIndex := entities.GetMessageIndex(sequence, r.config.Messaging.MessagesPerDocument)

	documentID := fmt.Sprintf("%s:%d", conversationID.String(), shardIndex)

	filter := bson.M{"_id": documentID}
	projection := bson.M{
		fmt.Sprintf("messages.%d", messageIndex): 1,
	}

	var document MessageDocumentModel
	opts := options.FindOne().SetProjection(projection)
	err := r.documentsCollection.FindOne(ctx, filter, opts).Decode(&document)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, repositories.ErrMessageNotFound
		}
		return nil, err
	}

	// Look for message at the specific index
	messageKey := fmt.Sprintf("%d", messageIndex)
	if msgInfo, exists := document.Messages[messageKey]; exists && msgInfo != nil && msgInfo.Message != nil {
		return r.modelToMessage(msgInfo.Message), nil
	}

	return nil, repositories.ErrMessageNotFound
}

func (r *mongoMessageRepository) GetMessages(ctx context.Context, conversationID conversationentities.ConversationID, limit int, offset int64) ([]*entities.Message, error) {
	filter := bson.M{"conversation_id": conversationID.String()}
	opts := options.Find().
		SetSort(bson.D{{Key: "shard_index", Value: -1}}).
		SetLimit(int64(limit))

	if offset > 0 {
		opts.SetSkip(offset)
	}

	cursor, err := r.documentsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message
	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		// Extract messages from document in reverse order (newest first)
		// First, get the message indices and sort them
		indices := make([]int, 0, len(document.Messages))
		for key := range document.Messages {
			if idx, err := strconv.Atoi(key); err == nil {
				indices = append(indices, idx)
			}
		}
		sort.Ints(indices)

		// Iterate in reverse order (newest first)
		for i := len(indices) - 1; i >= 0; i-- {
			key := fmt.Sprintf("%d", indices[i])
			if msgInfo := document.Messages[key]; msgInfo != nil && msgInfo.Message != nil {
				messages = append(messages, r.modelToMessage(msgInfo.Message))
			}
		}
	}

	return messages, nil
}

func (r *mongoMessageRepository) GetMessagesAfterSequence(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64, limit int) ([]*entities.Message, error) {
	startShardIndex := entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument)

	filter := bson.M{
		"conversation_id": conversationID.String(),
		"shard_index":     bson.M{"$gte": startShardIndex},
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "shard_index", Value: 1}}).
		SetLimit(int64(limit))

	cursor, err := r.documentsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message
	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		for _, msgInfo := range document.Messages {
			if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.Sequence > sequence {
				messages = append(messages, r.modelToMessage(msgInfo.Message))
			}
		}
	}

	return messages, nil
}

func (r *mongoMessageRepository) GetMessagesBeforeSequence(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64, limit int) ([]*entities.Message, error) {
	endShardIndex := entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument)

	filter := bson.M{
		"conversation_id": conversationID.String(),
		"shard_index":     bson.M{"$lte": endShardIndex},
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "shard_index", Value: -1}}).
		SetLimit(int64(limit))

	cursor, err := r.documentsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message
	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		// Extract messages in reverse order
		indices := make([]int, 0, len(document.Messages))
		for key := range document.Messages {
			if idx, err := strconv.Atoi(key); err == nil {
				indices = append(indices, idx)
			}
		}
		sort.Ints(indices)

		for i := len(indices) - 1; i >= 0; i-- {
			key := fmt.Sprintf("%d", indices[i])
			if msgInfo := document.Messages[key]; msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.Sequence < sequence {
				messages = append(messages, r.modelToMessage(msgInfo.Message))
			}
		}
	}

	return messages, nil
}

func (r *mongoMessageRepository) SearchMessages(ctx context.Context, conversationID conversationentities.ConversationID, query string, limit int, offset int) ([]*entities.Message, error) {
	// Add explicit timeout for MongoDB operation
	mongoCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	fmt.Printf("DEBUG: SearchMessages called with query='%s', conversationID='%s'\n", query, conversationID.String())

	// Use regex search for better compatibility with nested document structure
	// MongoDB text search requires complex aggregation for nested documents
	filter := bson.M{
		"conversation_id": conversationID.String(),
		"$or": []bson.M{
			{
				"messages.msg.content.text": bson.M{
					"$regex":   regexp.QuoteMeta(query),
					"$options": "i",
				},
			},
		},
	}

	fmt.Printf("DEBUG: MongoDB filter: %+v\n", filter)

	opts := options.Find().
		SetSort(bson.D{{Key: "updated_at", Value: -1}}).
		SetLimit(int64(limit * 2)) // Get more documents to filter properly

	if offset > 0 {
		opts.SetSkip(int64(offset))
	}

	cursor, err := r.documentsCollection.Find(mongoCtx, filter, opts)
	if err != nil {
		fmt.Printf("DEBUG: MongoDB find error: %v\n", err)
		return nil, fmt.Errorf("search query failed: %w", err)
	}
	defer cursor.Close(mongoCtx)

	var messages []*entities.Message
	processedCount := 0

	for cursor.Next(mongoCtx) && processedCount < limit {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			fmt.Printf("DEBUG: Document decode error: %v\n", err)
			continue
		}

		fmt.Printf("DEBUG: Processing document with %d messages\n", len(document.Messages))

		// Extract and sort message indices by sequence for proper ordering
		type messageWithSequence struct {
			message  *entities.Message
			sequence int64
		}

		var candidates []messageWithSequence

		for _, msgInfo := range document.Messages {
			if msgInfo == nil || msgInfo.Message == nil {
				continue
			}

			// Check if message content contains the search query (case-insensitive)
			if msgInfo.Message.Content.Text != "" {
				if matched, _ := regexp.MatchString("(?i)"+regexp.QuoteMeta(query), msgInfo.Message.Content.Text); matched {
					message := r.modelToMessage(msgInfo.Message)
					candidates = append(candidates, messageWithSequence{
						message:  message,
						sequence: msgInfo.Message.Sequence,
					})
					fmt.Printf("DEBUG: Found matching message: %s, sequence: %d\n", message.ID().String(), msgInfo.Message.Sequence)
				}
			}
		}

		// Sort by sequence descending (newest first)
		sort.Slice(candidates, func(i, j int) bool {
			return candidates[i].sequence > candidates[j].sequence
		})

		// Add sorted messages up to the limit
		for _, candidate := range candidates {
			if processedCount >= limit {
				break
			}
			messages = append(messages, candidate.message)
			processedCount++
		}
	}

	fmt.Printf("DEBUG: SearchMessages returning %d messages\n", len(messages))
	return messages, nil
}

// searchMessagesRegex is a fallback method using regex search
func (r *mongoMessageRepository) searchMessagesRegex(ctx context.Context, conversationID conversationentities.ConversationID, query string, limit int, offset int) ([]*entities.Message, error) {
	filter := bson.M{
		"conversation_id": conversationID.String(),
		"messages.msg.content.text": bson.M{
			"$regex":   regexp.QuoteMeta(query),
			"$options": "i",
		},
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "updated_at", Value: -1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := r.documentsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message

	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		// Extract matching messages
		for _, msgInfo := range document.Messages {
			if msgInfo != nil && msgInfo.Message != nil {
				if msgInfo.Message.Content.Text != "" {
					if matched, _ := regexp.MatchString("(?i)"+regexp.QuoteMeta(query), msgInfo.Message.Content.Text); matched {
						messages = append(messages, r.modelToMessage(msgInfo.Message))
					}
				}
			}
		}
	}

	return messages, nil
}

// Conversion methods
func (r *mongoMessageRepository) messageToModel(message *entities.Message) *MessageModel {
	model := &MessageModel{
		ID:             message.ID().String(),
		ConversationID: message.ConversationID().String(),
		SenderID:       message.SenderID().String(),
		Content:        r.contentToModel(message.Content()),
		MessageType:    string(message.MessageType()),
		Sequence:       message.Sequence(),
		Status:         string(message.Status()),
		Timestamp:      message.Timestamp(),
		EditedAt:       message.EditedAt(),
		DeletedAt:      message.DeletedAt(),
		Metadata:       message.Metadata(),
		ReadBy:         r.readStatusToModel(message.ReadBy()),
	}

	if message.ThreadID() != nil {
		threadID := message.ThreadID().String()
		model.ThreadID = &threadID
	}

	if message.ReplyToID() != nil {
		replyToID := message.ReplyToID().String()
		model.ReplyToID = &replyToID
	}

	return model
}

func (r *mongoMessageRepository) modelToMessage(model *MessageModel) *entities.Message {
	conversationID := conversationentities.ConversationID(uuid.MustParse(model.ConversationID))
	senderID := uuid.MustParse(model.SenderID)
	content := r.modelToContent(model.Content)
	readBy := r.modelToReadStatus(model.ReadBy)

	var threadID *entities.MessageID
	if model.ThreadID != nil {
		tid := entities.MessageID(uuid.MustParse(*model.ThreadID))
		threadID = &tid
	}

	var replyToID *entities.MessageID
	if model.ReplyToID != nil {
		rid := entities.MessageID(uuid.MustParse(*model.ReplyToID))
		replyToID = &rid
	}

	return entities.ReconstructMessage(
		entities.MessageID(uuid.MustParse(model.ID)),
		conversationID,
		senderID,
		content,
		entities.MessageType(model.MessageType),
		model.Sequence,
		threadID,
		replyToID,
		entities.MessageStatus(model.Status),
		model.Timestamp,
		model.EditedAt,
		model.DeletedAt,
		model.Metadata,
		readBy,
	)
}

func (r *mongoMessageRepository) contentToModel(content valueobjects.MessageContent) MessageContentModel {
	attachments := make([]MessageAttachmentModel, len(content.Attachments()))
	for i, attachment := range content.Attachments() {
		attachments[i] = MessageAttachmentModel{
			ID:       attachment.ID(),
			Name:     attachment.Name(),
			URL:      attachment.URL(),
			MimeType: attachment.MimeType(),
			Size:     attachment.Size(),
			Metadata: attachment.Metadata(),
		}
	}

	return MessageContentModel{
		Text:        content.Text(),
		Attachments: attachments,
		Mentions:    content.Mentions(),
		Links:       content.Links(),
	}
}

func (r *mongoMessageRepository) modelToContent(model MessageContentModel) valueobjects.MessageContent {
	attachments := make([]valueobjects.MessageAttachment, len(model.Attachments))
	for i, attachment := range model.Attachments {
		attachments[i] = valueobjects.NewMessageAttachment(
			attachment.ID,
			attachment.Name,
			attachment.URL,
			attachment.MimeType,
			attachment.Size,
		)
		for k, v := range attachment.Metadata {
			attachments[i].SetMetadata(k, v)
		}
	}

	if len(attachments) > 0 {
		content, _ := valueobjects.NewAttachmentMessageContent(attachments, model.Text)
		for _, mention := range model.Mentions {
			content.AddMention(mention)
		}
		for _, link := range model.Links {
			content.AddLink(link)
		}
		return content
	}

	content, _ := valueobjects.NewTextMessageContent(model.Text)
	for _, mention := range model.Mentions {
		content.AddMention(mention)
	}
	for _, link := range model.Links {
		content.AddLink(link)
	}
	return content
}

func (r *mongoMessageRepository) readStatusToModel(readStatus []entities.ReadStatus) []ReadStatusModel {
	models := make([]ReadStatusModel, len(readStatus))
	for i, rs := range readStatus {
		models[i] = ReadStatusModel{
			UserID: rs.UserID.String(),
			ReadAt: rs.ReadAt,
		}
	}
	return models
}

func (r *mongoMessageRepository) modelToReadStatus(models []ReadStatusModel) []entities.ReadStatus {
	readStatus := make([]entities.ReadStatus, len(models))
	for i, model := range models {
		readStatus[i] = entities.ReadStatus{
			UserID: uuid.MustParse(model.UserID),
			ReadAt: model.ReadAt,
		}
	}
	return readStatus
}

// UpdateMessage updates an existing message in its document
func (r *mongoMessageRepository) UpdateMessage(ctx context.Context, message *entities.Message) error {
	conversationID := message.ConversationID().String()
	sequence := message.Sequence()

	shardIndex := entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument)
	messageIndex := entities.GetMessageIndex(sequence, r.config.Messaging.MessagesPerDocument)

	documentID := fmt.Sprintf("%s:%d", conversationID, shardIndex)
	messageModel := r.messageToModel(message)

	messageInfo := &MessageInfoModel{
		Message:   messageModel,
		DeletedBy: make([]string, 0),
		IsRead:    false,
	}

	filter := bson.M{"_id": documentID}
	update := bson.M{
		"$set": bson.M{
			fmt.Sprintf("messages.%d", messageIndex): messageInfo,
			"updated_at":                             time.Now(),
		},
	}

	_, err := r.documentsCollection.UpdateOne(ctx, filter, update)
	return err
}

func (r *mongoMessageRepository) DeleteMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error {
	messageIDStr := messageID.String()

	// Use optimized query with compound index on message IDs
	filter := bson.M{
		"$or": []bson.M{
			{"messages.0.msg._id": messageIDStr},
			{"messages.1.msg._id": messageIDStr},
			{"messages.2.msg._id": messageIDStr},
			{"messages.3.msg._id": messageIDStr},
			{"messages.4.msg._id": messageIDStr},
			{"messages.5.msg._id": messageIDStr},
			{"messages.6.msg._id": messageIDStr},
			{"messages.7.msg._id": messageIDStr},
			{"messages.8.msg._id": messageIDStr},
			{"messages.9.msg._id": messageIDStr},
		},
	}

	var document MessageDocumentModel
	err := r.documentsCollection.FindOne(ctx, filter).Decode(&document)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Fall back to broader search if not found in first 10 slots
			return r.deleteMessageFullScan(ctx, messageID, userID)
		}
		return err
	}

	// Find the message index in the document
	var messageIndex = -1
	for key, msgInfo := range document.Messages {
		if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.ID == messageIDStr {
			if idx, err := strconv.Atoi(key); err == nil {
				messageIndex = idx
				break
			}
		}
	}

	if messageIndex == -1 {
		return repositories.ErrMessageNotFound
	}

	// Add user to deleted_by list and set deleted_at
	now := time.Now()
	updateFilter := bson.M{"_id": document.ID}
	update := bson.M{
		"$addToSet": bson.M{
			fmt.Sprintf("messages.%d.deleted_by", messageIndex): userID.String(),
		},
		"$set": bson.M{
			fmt.Sprintf("messages.%d.msg.deleted_at", messageIndex): now,
			"updated_at": now,
		},
	}

	_, err = r.documentsCollection.UpdateOne(ctx, updateFilter, update)
	return err
}

// deleteMessageFullScan performs a full scan as fallback for messages in higher slots
func (r *mongoMessageRepository) deleteMessageFullScan(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error {
	messageIDStr := messageID.String()

	// Build filters for remaining message slots (10-99)
	orFilters := []bson.M{}
	for i := 10; i < 100; i++ {
		orFilters = append(orFilters, bson.M{
			fmt.Sprintf("messages.%d.msg._id", i): messageIDStr,
		})
	}

	filter := bson.M{"$or": orFilters}

	var document MessageDocumentModel
	err := r.documentsCollection.FindOne(ctx, filter).Decode(&document)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return repositories.ErrMessageNotFound
		}
		return err
	}

	// Find the message index in the document
	var messageIndex = -1
	for key, msgInfo := range document.Messages {
		if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.ID == messageIDStr {
			if idx, err := strconv.Atoi(key); err == nil {
				messageIndex = idx
				break
			}
		}
	}

	if messageIndex == -1 {
		return repositories.ErrMessageNotFound
	}

	// Add user to deleted_by list and set deleted_at
	now := time.Now()
	updateFilter := bson.M{"_id": document.ID}
	update := bson.M{
		"$addToSet": bson.M{
			fmt.Sprintf("messages.%d.deleted_by", messageIndex): userID.String(),
		},
		"$set": bson.M{
			fmt.Sprintf("messages.%d.msg.deleted_at", messageIndex): now,
			"updated_at": now,
		},
	}

	_, err = r.documentsCollection.UpdateOne(ctx, updateFilter, update)
	return err
}

func (r *mongoMessageRepository) GetMessageHistory(ctx context.Context, conversationID conversationentities.ConversationID, fromTime, toTime time.Time, limit int) ([]*entities.Message, error) {
	filter := bson.M{
		"conversation_id": conversationID.String(),
		"messages.msg.timestamp": bson.M{
			"$gte": fromTime,
			"$lte": toTime,
		},
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "messages.msg.timestamp", Value: -1}}).
		SetLimit(int64(limit))

	cursor, err := r.documentsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message
	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		for _, msgInfo := range document.Messages {
			if msgInfo != nil && msgInfo.Message != nil {
				msgTime := msgInfo.Message.Timestamp
				if msgTime.After(fromTime) && msgTime.Before(toTime) {
					messages = append(messages, r.modelToMessage(msgInfo.Message))
				}
			}
		}
	}

	return messages, nil
}

func (r *mongoMessageRepository) SearchMessagesGlobal(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Message, error) {
	// Search across all conversations where user is a participant
	// This would typically require a separate index or denormalized data
	filter := bson.M{
		"$text": bson.M{"$search": query},
		// Note: In production, you'd need a way to filter by user access
		// This could be done by maintaining a separate collection of user-conversation mappings
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "score", Value: bson.M{"$meta": "textScore"}}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := r.documentsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message
	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		for _, msgInfo := range document.Messages {
			if msgInfo != nil && msgInfo.Message != nil {
				messages = append(messages, r.modelToMessage(msgInfo.Message))
			}
		}
	}

	return messages, nil
}

func (r *mongoMessageRepository) GetThreadMessages(ctx context.Context, threadID entities.MessageID, limit int, offset int) ([]*entities.Message, error) {
	filter := bson.M{"messages.msg.thread_id": threadID.String()}

	opts := options.Find().
		SetSort(bson.D{{Key: "messages.msg.timestamp", Value: 1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := r.documentsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message
	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		for _, msgInfo := range document.Messages {
			if msgInfo != nil && msgInfo.Message != nil &&
				msgInfo.Message.ThreadID != nil && *msgInfo.Message.ThreadID == threadID.String() {
				messages = append(messages, r.modelToMessage(msgInfo.Message))
			}
		}
	}

	return messages, nil
}

func (r *mongoMessageRepository) GetThreadRepliesCount(ctx context.Context, threadID entities.MessageID) (int64, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"messages.msg.thread_id": threadID.String(),
			},
		},
		{
			"$unwind": "$messages",
		},
		{
			"$match": bson.M{
				"messages.msg.thread_id": threadID.String(),
			},
		},
		{
			"$count": "total",
		},
	}

	cursor, err := r.documentsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var result struct {
		Total int64 `bson:"total"`
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		return result.Total, nil
	}

	return 0, nil
}

func (r *mongoMessageRepository) StoreMessagesBatch(ctx context.Context, messages []*entities.Message) error {
	if len(messages) == 0 {
		return nil
	}

	// Group messages by document
	documentGroups := make(map[string][]*entities.Message)

	for _, message := range messages {
		conversationID := message.ConversationID().String()
		sequence := message.Sequence()
		shardIndex := entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument)
		documentID := fmt.Sprintf("%s:%d", conversationID, shardIndex)

		documentGroups[documentID] = append(documentGroups[documentID], message)
	}

	// Use bulk operations for efficiency
	var operations []mongo.WriteModel

	for documentID, docMessages := range documentGroups {
		for _, message := range docMessages {
			sequence := message.Sequence()
			messageIndex := entities.GetMessageIndex(sequence, r.config.Messaging.MessagesPerDocument)

			messageModel := r.messageToModel(message)
			messageInfo := &MessageInfoModel{
				Message:   messageModel,
				DeletedBy: make([]string, 0),
				IsRead:    false,
			}

			filter := bson.M{"_id": documentID}
			update := bson.M{
				"$set": bson.M{
					fmt.Sprintf("messages.%d", messageIndex): messageInfo,
					"updated_at":                             time.Now(),
				},
				"$setOnInsert": bson.M{
					"conversation_id": message.ConversationID().String(),
					"shard_index":     entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument),
					"created_at":      time.Now(),
				},
			}

			updateModel := mongo.NewUpdateOneModel().
				SetFilter(filter).
				SetUpdate(update).
				SetUpsert(true)

			operations = append(operations, updateModel)
		}
	}

	if len(operations) > 0 {
		_, err := r.documentsCollection.BulkWrite(ctx, operations)
		return err
	}

	return nil
}

func (r *mongoMessageRepository) GetMessagesBatch(ctx context.Context, messageIDs []entities.MessageID) ([]*entities.Message, error) {
	if len(messageIDs) == 0 {
		return []*entities.Message{}, nil
	}

	stringIDs := make([]string, len(messageIDs))
	for i, id := range messageIDs {
		stringIDs[i] = id.String()
	}

	// Build query for batch message retrieval
	orFilters := []bson.M{}
	for i := 0; i < 100; i++ {
		orFilters = append(orFilters, bson.M{
			fmt.Sprintf("messages.%d.msg._id", i): bson.M{"$in": stringIDs},
		})
	}
	filter := bson.M{"$or": orFilters}

	cursor, err := r.documentsCollection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*entities.Message
	idSet := make(map[string]bool)
	for _, id := range stringIDs {
		idSet[id] = true
	}

	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		for _, msgInfo := range document.Messages {
			if msgInfo != nil && msgInfo.Message != nil && idSet[msgInfo.Message.ID] {
				messages = append(messages, r.modelToMessage(msgInfo.Message))
			}
		}
	}

	return messages, nil
}

func (r *mongoMessageRepository) StoreMessageDocument(ctx context.Context, document *entities.MessageDocument) error {
	// Convert domain document to MongoDB model
	model := &MessageDocumentModel{
		ID:             fmt.Sprintf("%s:%d", document.ConversationID().String(), document.ShardIndex()),
		ConversationID: document.ConversationID().String(),
		ShardIndex:     document.ShardIndex(),
		Messages:       make(map[string]*MessageInfoModel),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Convert messages
	for idx, msgInfo := range document.Messages() {
		if msgInfo != nil {
			messageModel := r.messageToModel(msgInfo.Message())
			model.Messages[fmt.Sprintf("%d", idx)] = &MessageInfoModel{
				Message:   messageModel,
				DeletedBy: make([]string, 0), // Convert from []uuid.UUID if needed
				IsRead:    false,
			}
		}
	}

	_, err := r.documentsCollection.InsertOne(ctx, model)
	return err
}

func (r *mongoMessageRepository) GetMessageDocument(ctx context.Context, conversationID conversationentities.ConversationID, shardIndex int64) (*entities.MessageDocument, error) {
	documentID := fmt.Sprintf("%s:%d", conversationID.String(), shardIndex)

	var model MessageDocumentModel
	err := r.documentsCollection.FindOne(ctx, bson.M{"_id": documentID}).Decode(&model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, repositories.ErrMessageNotFound
		}
		return nil, err
	}

	// Convert to domain entity
	document := entities.NewMessageDocument(conversationID, shardIndex)
	for _, msgInfo := range model.Messages {
		if msgInfo != nil && msgInfo.Message != nil {
			message := r.modelToMessage(msgInfo.Message)
			document.AddMessage(message)
		}
	}

	return document, nil
}

func (r *mongoMessageRepository) UpdateMessageInDocument(ctx context.Context, conversationID conversationentities.ConversationID, message *entities.Message) error {
	sequence := message.Sequence()
	shardIndex := entities.GetDocumentIndex(sequence, r.config.Messaging.MessagesPerDocument)
	messageIndex := entities.GetMessageIndex(sequence, r.config.Messaging.MessagesPerDocument)

	documentID := fmt.Sprintf("%s:%d", conversationID.String(), shardIndex)
	messageModel := r.messageToModel(message)

	filter := bson.M{"_id": documentID}
	update := bson.M{
		"$set": bson.M{
			fmt.Sprintf("messages.%d.msg", messageIndex): messageModel,
			"updated_at": time.Now(),
		},
	}

	_, err := r.documentsCollection.UpdateOne(ctx, filter, update)
	return err
}

func (r *mongoMessageRepository) MarkMessageAsRead(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error {
	messageIDStr := messageID.String()

	// Use optimized query with compound index on message IDs
	filter := bson.M{
		"$or": []bson.M{
			{"messages.0.msg._id": messageIDStr},
			{"messages.1.msg._id": messageIDStr},
			{"messages.2.msg._id": messageIDStr},
			{"messages.3.msg._id": messageIDStr},
			{"messages.4.msg._id": messageIDStr},
			{"messages.5.msg._id": messageIDStr},
			{"messages.6.msg._id": messageIDStr},
			{"messages.7.msg._id": messageIDStr},
			{"messages.8.msg._id": messageIDStr},
			{"messages.9.msg._id": messageIDStr},
		},
	}

	var document MessageDocumentModel
	err := r.documentsCollection.FindOne(ctx, filter).Decode(&document)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Fall back to broader search if not found in first 10 slots
			return r.markMessageAsReadFullScan(ctx, messageID, userID)
		}
		return err
	}

	// Find message index
	var messageIndex = -1
	for key, msgInfo := range document.Messages {
		if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.ID == messageIDStr {
			if idx, err := strconv.Atoi(key); err == nil {
				messageIndex = idx
				break
			}
		}
	}

	if messageIndex == -1 {
		return repositories.ErrMessageNotFound
	}

	// Add read status
	readStatus := ReadStatusModel{
		UserID: userID.String(),
		ReadAt: time.Now(),
	}

	updateFilter := bson.M{"_id": document.ID}
	update := bson.M{
		"$addToSet": bson.M{
			fmt.Sprintf("messages.%d.msg.read_by", messageIndex): readStatus,
		},
		"$set": bson.M{
			"updated_at": time.Now(),
		},
	}

	_, err = r.documentsCollection.UpdateOne(ctx, updateFilter, update)
	return err
}

// markMessageAsReadFullScan performs a full scan as fallback for messages in higher slots
func (r *mongoMessageRepository) markMessageAsReadFullScan(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error {
	messageIDStr := messageID.String()

	// Build filters for remaining message slots (10-99)
	orFilters := []bson.M{}
	for i := 10; i < 100; i++ {
		orFilters = append(orFilters, bson.M{
			fmt.Sprintf("messages.%d.msg._id", i): messageIDStr,
		})
	}

	filter := bson.M{"$or": orFilters}

	var document MessageDocumentModel
	err := r.documentsCollection.FindOne(ctx, filter).Decode(&document)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return repositories.ErrMessageNotFound
		}
		return err
	}

	// Find message index
	var messageIndex = -1
	for key, msgInfo := range document.Messages {
		if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.ID == messageIDStr {
			if idx, err := strconv.Atoi(key); err == nil {
				messageIndex = idx
				break
			}
		}
	}

	if messageIndex == -1 {
		return repositories.ErrMessageNotFound
	}

	// Add read status
	readStatus := ReadStatusModel{
		UserID: userID.String(),
		ReadAt: time.Now(),
	}

	updateFilter := bson.M{"_id": document.ID}
	update := bson.M{
		"$addToSet": bson.M{
			fmt.Sprintf("messages.%d.msg.read_by", messageIndex): readStatus,
		},
		"$set": bson.M{
			"updated_at": time.Now(),
		},
	}

	_, err = r.documentsCollection.UpdateOne(ctx, updateFilter, update)
	return err
}

func (r *mongoMessageRepository) MarkMessagesAsRead(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, upToSequence int64) error {
	// This is a complex operation that might be better handled at the service level
	// For now, we'll implement a basic version

	filter := bson.M{
		"conversation_id":       conversationID.String(),
		"messages.msg.sequence": bson.M{"$lte": upToSequence},
	}

	// This is a simplified implementation - in production you'd want to be more efficient
	cursor, err := r.documentsCollection.Find(ctx, filter)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var operations []mongo.WriteModel
	readStatus := ReadStatusModel{
		UserID: userID.String(),
		ReadAt: time.Now(),
	}

	for cursor.Next(ctx) {
		var document MessageDocumentModel
		if err := cursor.Decode(&document); err != nil {
			continue
		}

		for i, msgInfo := range document.Messages {
			if msgInfo != nil && msgInfo.Message != nil && msgInfo.Message.Sequence <= upToSequence {
				updateModel := mongo.NewUpdateOneModel().
					SetFilter(bson.M{"_id": document.ID}).
					SetUpdate(bson.M{
						"$addToSet": bson.M{
							fmt.Sprintf("messages.%d.msg.read_by", i): readStatus,
						},
						"$set": bson.M{
							"updated_at": time.Now(),
						},
					})

				operations = append(operations, updateModel)
			}
		}
	}

	if len(operations) > 0 {
		_, err = r.documentsCollection.BulkWrite(ctx, operations)
		return err
	}

	return nil
}

func (r *mongoMessageRepository) GetUnreadMessageCount(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID) (int64, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"conversation_id": conversationID.String(),
			},
		},
		{
			"$unwind": "$messages",
		},
		{
			"$match": bson.M{
				"messages.msg":                 bson.M{"$ne": nil},
				"messages.msg.read_by.user_id": bson.M{"$ne": userID.String()},
			},
		},
		{
			"$count": "unread_count",
		},
	}

	cursor, err := r.documentsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var result struct {
		UnreadCount int64 `bson:"unread_count"`
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		return result.UnreadCount, nil
	}

	return 0, nil
}

func (r *mongoMessageRepository) GetMessageStats(ctx context.Context, conversationID conversationentities.ConversationID, fromTime, toTime time.Time) (*repositories.MessageStats, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"conversation_id": conversationID.String(),
			},
		},
		{
			"$unwind": "$messages",
		},
		{
			"$match": bson.M{
				"messages.msg.timestamp": bson.M{
					"$gte": fromTime,
					"$lte": toTime,
				},
			},
		},
		{
			"$group": bson.M{
				"_id":            nil,
				"total_messages": bson.M{"$sum": 1},
				"unique_users":   bson.M{"$addToSet": "$messages.msg.sender_id"},
				"media_messages": bson.M{
					"$sum": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$ne": []interface{}{"$messages.msg.content.attachments", []interface{}{}}},
							"then": 1,
							"else": 0,
						},
					},
				},
				"deleted_messages": bson.M{
					"$sum": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$ne": []interface{}{"$messages.msg.deleted_at", nil}},
							"then": 1,
							"else": 0,
						},
					},
				},
				"edited_messages": bson.M{
					"$sum": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$ne": []interface{}{"$messages.msg.edited_at", nil}},
							"then": 1,
							"else": 0,
						},
					},
				},
			},
		},
		{
			"$project": bson.M{
				"total_messages":   1,
				"unique_users":     bson.M{"$size": "$unique_users"},
				"media_messages":   1,
				"deleted_messages": 1,
				"edited_messages":  1,
			},
		},
	}

	cursor, err := r.documentsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	stats := &repositories.MessageStats{}

	if cursor.Next(ctx) {
		var result struct {
			TotalMessages   int64 `bson:"total_messages"`
			UniqueUsers     int64 `bson:"unique_users"`
			MediaMessages   int64 `bson:"media_messages"`
			DeletedMessages int64 `bson:"deleted_messages"`
			EditedMessages  int64 `bson:"edited_messages"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}

		stats.TotalMessages = result.TotalMessages
		stats.UniqueUsers = result.UniqueUsers
		stats.MediaMessages = result.MediaMessages
		stats.DeletedMessages = result.DeletedMessages
		stats.EditedMessages = result.EditedMessages
		stats.TextMessages = result.TotalMessages - result.MediaMessages
	}

	return stats, nil
}

func (r *mongoMessageRepository) GetUserMessageStats(ctx context.Context, userID uuid.UUID, fromTime, toTime time.Time) (*repositories.UserMessageStats, error) {
	pipeline := []bson.M{
		{
			"$unwind": "$messages",
		},
		{
			"$match": bson.M{
				"messages.msg.timestamp": bson.M{
					"$gte": fromTime,
					"$lte": toTime,
				},
			},
		},
		{
			"$group": bson.M{
				"_id": nil,
				"messages_sent": bson.M{
					"$sum": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": []interface{}{"$messages.msg.sender_id", userID.String()}},
							"then": 1,
							"else": 0,
						},
					},
				},
				"messages_received": bson.M{
					"$sum": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$ne": []interface{}{"$messages.msg.sender_id", userID.String()}},
							"then": 1,
							"else": 0,
						},
					},
				},
				"media_shared": bson.M{
					"$sum": bson.M{
						"$cond": bson.M{
							"if": bson.M{
								"$and": []interface{}{
									bson.M{"$eq": []interface{}{"$messages.msg.sender_id", userID.String()}},
									bson.M{"$ne": []interface{}{"$messages.msg.content.attachments", []interface{}{}}},
								},
							},
							"then": 1,
							"else": 0,
						},
					},
				},
				"avg_message_length": bson.M{
					"$avg": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": []interface{}{"$messages.msg.sender_id", userID.String()}},
							"then": bson.M{"$strLenCP": "$messages.msg.content.text"},
							"else": 0,
						},
					},
				},
				"first_message": bson.M{"$min": "$messages.msg.timestamp"},
				"last_message":  bson.M{"$max": "$messages.msg.timestamp"},
			},
		},
	}

	cursor, err := r.documentsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	stats := &repositories.UserMessageStats{}

	if cursor.Next(ctx) {
		var result struct {
			MessagesSent     int64     `bson:"messages_sent"`
			MessagesReceived int64     `bson:"messages_received"`
			MediaShared      int64     `bson:"media_shared"`
			AvgMessageLength float64   `bson:"avg_message_length"`
			FirstMessage     time.Time `bson:"first_message"`
			LastMessage      time.Time `bson:"last_message"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}

		stats.MessagesSent = result.MessagesSent
		stats.MessagesReceived = result.MessagesReceived
		stats.MediaShared = result.MediaShared
		stats.AvgMessageLength = result.AvgMessageLength

		if !result.FirstMessage.IsZero() {
			stats.FirstMessage = &result.FirstMessage
		}
		if !result.LastMessage.IsZero() {
			stats.LastMessage = &result.LastMessage
		}
	}

	return stats, nil
}
