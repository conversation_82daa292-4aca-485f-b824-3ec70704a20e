package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/services/messaging/internal/domain/realtime/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/realtime/repositories"
	"github.com/swork-team/platform/pkg/config"
)

// redisConnectionRepository implements ConnectionRepository using Redis
type redisConnectionRepository struct {
	client *redis.Client
	config *config.MessagingServiceConfig
	
	// Cache TTL settings
	connectionTTL time.Duration
	presenceTTL   time.Duration
	metadataTTL   time.Duration
}

// NewRedisConnectionRepository creates a new Redis-based connection repository
func NewRedisConnectionRepository(client *redis.Client, config *config.MessagingServiceConfig) repositories.ConnectionRepository {
	return &redisConnectionRepository{
		client:        client,
		config:        config,
		connectionTTL: 30 * time.Minute, // Connections expire after 30 minutes of inactivity
		presenceTTL:   5 * time.Minute,   // Presence info expires after 5 minutes
		metadataTTL:   1 * time.Hour,     // Metadata expires after 1 hour
	}
}

// Redis key patterns
const (
	// Connection storage
	connectionKeyPattern       = "conn:%s"                    // conn:{connectionID}
	userConnectionsKeyPattern  = "user_conns:%s"              // user_conns:{userID}
	connectionMetaKeyPattern   = "conn_meta:%s"               // conn_meta:{connectionID}
	
	// User presence
	userOnlineKeyPattern       = "user_online:%s"             // user_online:{userID}
	userLastSeenKeyPattern     = "user_last_seen:%s"          // user_last_seen:{userID}
	userDevicesKeyPattern      = "user_devices:%s"            // user_devices:{userID}
	
	// Platform and device tracking
	platformConnectionsPattern = "platform_conns:%d"          // platform_conns:{platformType}
	deviceConnectionsPattern   = "device_conns:%s:%s"         // device_conns:{userID}:{deviceID}
	
	// Subscriptions
	connectionSubsKeyPattern   = "conn_subs:%s"               // conn_subs:{connectionID}
	topicSubscribersPattern    = "topic_subs:%s"              // topic_subs:{topic}
	
	// Statistics
	connectionStatsKey         = "conn_stats"
	connectionCountKey         = "conn_count"
	onlineUsersKey            = "online_users"
	
	// History and cleanup
	staleConnectionsKey       = "stale_connections"
	connectionHistoryPattern  = "conn_history:%s"             // conn_history:{userID}
)

// Connection storage operations

func (r *redisConnectionRepository) StoreConnection(ctx context.Context, connection *entities.Connection) error {
	connectionData, err := r.serializeConnection(connection)
	if err != nil {
		return fmt.Errorf("failed to serialize connection: %w", err)
	}
	
	connectionKey := fmt.Sprintf(connectionKeyPattern, connection.ID().String())
	userConnectionsKey := fmt.Sprintf(userConnectionsKeyPattern, connection.UserID().String())
	platformKey := fmt.Sprintf(platformConnectionsPattern, int(connection.PlatformType()))
	deviceKey := fmt.Sprintf(deviceConnectionsPattern, connection.UserID().String(), connection.DeviceID())
	
	pipe := r.client.Pipeline()
	
	// Store connection data
	pipe.Set(ctx, connectionKey, connectionData, r.connectionTTL)
	
	// Add to user's connections set
	pipe.SAdd(ctx, userConnectionsKey, connection.ID().String())
	pipe.Expire(ctx, userConnectionsKey, r.connectionTTL)
	
	// Add to platform connections
	pipe.SAdd(ctx, platformKey, connection.ID().String())
	pipe.Expire(ctx, platformKey, r.connectionTTL)
	
	// Store device connection mapping
	pipe.Set(ctx, deviceKey, connection.ID().String(), r.connectionTTL)
	
	// Update user online status
	pipe.Set(ctx, fmt.Sprintf(userOnlineKeyPattern, connection.UserID().String()), "true", r.presenceTTL)
	pipe.Set(ctx, fmt.Sprintf(userLastSeenKeyPattern, connection.UserID().String()), time.Now().Unix(), r.presenceTTL)
	
	// Update connection count
	pipe.Incr(ctx, connectionCountKey)
	pipe.SAdd(ctx, onlineUsersKey, connection.UserID().String())
	pipe.Expire(ctx, onlineUsersKey, r.presenceTTL)
	
	// Add to user devices
	deviceInfo := map[string]interface{}{
		"device_id":     connection.DeviceID(),
		"platform_type": int(connection.PlatformType()),
		"connected_at":  time.Now().Unix(),
		"connection_id": connection.ID().String(),
	}
	deviceInfoJSON, _ := json.Marshal(deviceInfo)
	pipe.HSet(ctx, fmt.Sprintf(userDevicesKeyPattern, connection.UserID().String()), connection.DeviceID(), deviceInfoJSON)
	pipe.Expire(ctx, fmt.Sprintf(userDevicesKeyPattern, connection.UserID().String()), r.connectionTTL)
	
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to store connection in Redis: %w", err)
	}
	
	log.Printf("Stored connection %s for user %s", connection.ID().String(), connection.UserID().String())
	return nil
}

func (r *redisConnectionRepository) GetConnection(ctx context.Context, connectionID entities.ConnectionID) (*entities.Connection, error) {
	connectionKey := fmt.Sprintf(connectionKeyPattern, connectionID.String())
	
	data, err := r.client.Get(ctx, connectionKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, repositories.ErrConnectionNotFound
		}
		return nil, fmt.Errorf("failed to get connection from Redis: %w", err)
	}
	
	connection, err := r.deserializeConnection(data)
	if err != nil {
		return nil, fmt.Errorf("failed to deserialize connection: %w", err)
	}
	
	return connection, nil
}

func (r *redisConnectionRepository) UpdateConnection(ctx context.Context, connection *entities.Connection) error {
	// Check if connection exists
	exists, err := r.client.Exists(ctx, fmt.Sprintf(connectionKeyPattern, connection.ID().String())).Result()
	if err != nil {
		return fmt.Errorf("failed to check connection existence: %w", err)
	}
	if exists == 0 {
		return repositories.ErrConnectionNotFound
	}
	
	// Update with new data
	return r.StoreConnection(ctx, connection)
}

func (r *redisConnectionRepository) RemoveConnection(ctx context.Context, connectionID entities.ConnectionID) error {
	// First get the connection to extract user info
	connection, err := r.GetConnection(ctx, connectionID)
	if err != nil {
		if err == repositories.ErrConnectionNotFound {
			return nil // Already removed
		}
		return err
	}
	
	connectionKey := fmt.Sprintf(connectionKeyPattern, connectionID.String())
	userConnectionsKey := fmt.Sprintf(userConnectionsKeyPattern, connection.UserID().String())
	platformKey := fmt.Sprintf(platformConnectionsPattern, int(connection.PlatformType()))
	deviceKey := fmt.Sprintf(deviceConnectionsPattern, connection.UserID().String(), connection.DeviceID())
	
	pipe := r.client.Pipeline()
	
	// Remove connection data
	pipe.Del(ctx, connectionKey)
	
	// Remove from user's connections set
	pipe.SRem(ctx, userConnectionsKey, connectionID.String())
	
	// Remove from platform connections
	pipe.SRem(ctx, platformKey, connectionID.String())
	
	// Remove device connection mapping
	pipe.Del(ctx, deviceKey)
	
	// Remove connection metadata
	pipe.Del(ctx, fmt.Sprintf(connectionMetaKeyPattern, connectionID.String()))
	pipe.Del(ctx, fmt.Sprintf(connectionSubsKeyPattern, connectionID.String()))
	
	// Update connection count
	pipe.Decr(ctx, connectionCountKey)
	
	// Remove from user devices
	pipe.HDel(ctx, fmt.Sprintf(userDevicesKeyPattern, connection.UserID().String()), connection.DeviceID())
	
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to remove connection from Redis: %w", err)
	}
	
	// Check if user has any remaining connections
	remainingConnections, err := r.client.SCard(ctx, userConnectionsKey).Result()
	if err == nil && remainingConnections == 0 {
		// User is now offline
		r.SetUserOnlineStatus(ctx, connection.UserID(), false, time.Now())
	}
	
	log.Printf("Removed connection %s for user %s", connectionID.String(), connection.UserID().String())
	return nil
}

// User connection management

func (r *redisConnectionRepository) GetUserConnections(ctx context.Context, userID uuid.UUID) ([]*entities.Connection, error) {
	userConnectionsKey := fmt.Sprintf(userConnectionsKeyPattern, userID.String())
	
	connectionIDs, err := r.client.SMembers(ctx, userConnectionsKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get user connections: %w", err)
	}
	
	var connections []*entities.Connection
	for _, connectionIDStr := range connectionIDs {
		connectionID := entities.ConnectionID(uuid.MustParse(connectionIDStr))
		connection, err := r.GetConnection(ctx, connectionID)
		if err != nil {
			if err == repositories.ErrConnectionNotFound {
				// Clean up stale reference
				r.client.SRem(ctx, userConnectionsKey, connectionIDStr)
				continue
			}
			log.Printf("Warning: failed to get connection %s: %v", connectionIDStr, err)
			continue
		}
		connections = append(connections, connection)
	}
	
	return connections, nil
}

func (r *redisConnectionRepository) GetUserConnectionsByPlatform(ctx context.Context, userID uuid.UUID, platformType entities.PlatformType) ([]*entities.Connection, error) {
	connections, err := r.GetUserConnections(ctx, userID)
	if err != nil {
		return nil, err
	}
	
	var platformConnections []*entities.Connection
	for _, conn := range connections {
		if conn.PlatformType() == platformType {
			platformConnections = append(platformConnections, conn)
		}
	}
	
	return platformConnections, nil
}

func (r *redisConnectionRepository) RemoveUserConnections(ctx context.Context, userID uuid.UUID) error {
	connections, err := r.GetUserConnections(ctx, userID)
	if err != nil {
		return err
	}
	
	for _, connection := range connections {
		if err := r.RemoveConnection(ctx, connection.ID()); err != nil {
			log.Printf("Warning: failed to remove connection %s: %v", connection.ID().String(), err)
		}
	}
	
	return nil
}

func (r *redisConnectionRepository) RemoveUserConnectionsByDevice(ctx context.Context, userID uuid.UUID, deviceID string) error {
	deviceKey := fmt.Sprintf(deviceConnectionsPattern, userID.String(), deviceID)
	
	connectionIDStr, err := r.client.Get(ctx, deviceKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil // No connection for this device
		}
		return fmt.Errorf("failed to get device connection: %w", err)
	}
	
	connectionID := entities.ConnectionID(uuid.MustParse(connectionIDStr))
	return r.RemoveConnection(ctx, connectionID)
}

// Connection state queries

func (r *redisConnectionRepository) IsUserOnline(ctx context.Context, userID uuid.UUID) (bool, error) {
	result, err := r.client.Get(ctx, fmt.Sprintf(userOnlineKeyPattern, userID.String())).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		return false, fmt.Errorf("failed to check user online status: %w", err)
	}
	
	return result == "true", nil
}

func (r *redisConnectionRepository) GetOnlineUsers(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error) {
	var onlineUsers []uuid.UUID
	
	if len(userIDs) == 0 {
		return onlineUsers, nil
	}
	
	pipe := r.client.Pipeline()
	cmds := make([]*redis.StringCmd, len(userIDs))
	
	for i, userID := range userIDs {
		cmds[i] = pipe.Get(ctx, fmt.Sprintf(userOnlineKeyPattern, userID.String()))
	}
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to check online users: %w", err)
	}
	
	for i, cmd := range cmds {
		if cmd.Err() == nil && cmd.Val() == "true" {
			onlineUsers = append(onlineUsers, userIDs[i])
		}
	}
	
	return onlineUsers, nil
}

func (r *redisConnectionRepository) GetConnectionCount(ctx context.Context) (int64, error) {
	count, err := r.client.Get(ctx, connectionCountKey).Int64()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		return 0, fmt.Errorf("failed to get connection count: %w", err)
	}
	
	return count, nil
}

func (r *redisConnectionRepository) GetUserConnectionCount(ctx context.Context, userID uuid.UUID) (int, error) {
	userConnectionsKey := fmt.Sprintf(userConnectionsKeyPattern, userID.String())
	
	count, err := r.client.SCard(ctx, userConnectionsKey).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get user connection count: %w", err)
	}
	
	return int(count), nil
}

// Connection metadata and status

func (r *redisConnectionRepository) UpdateConnectionStatus(ctx context.Context, connectionID entities.ConnectionID, status entities.ConnectionStatus) error {
	connection, err := r.GetConnection(ctx, connectionID)
	if err != nil {
		return err
	}
	
	// Update status and save back
	connection.SetStatus(status)
	return r.UpdateConnection(ctx, connection)
}

func (r *redisConnectionRepository) UpdateConnectionHeartbeat(ctx context.Context, connectionID entities.ConnectionID) error {
	connection, err := r.GetConnection(ctx, connectionID)
	if err != nil {
		return err
	}
	
	// Update heartbeat and save back
	connection.UpdateHeartbeat()
	return r.UpdateConnection(ctx, connection)
}

func (r *redisConnectionRepository) SetConnectionMetadata(ctx context.Context, connectionID entities.ConnectionID, key string, value interface{}) error {
	metaKey := fmt.Sprintf(connectionMetaKeyPattern, connectionID.String())
	
	valueJSON, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata value: %w", err)
	}
	
	return r.client.HSet(ctx, metaKey, key, valueJSON).Err()
}

func (r *redisConnectionRepository) GetConnectionMetadata(ctx context.Context, connectionID entities.ConnectionID, key string) (interface{}, error) {
	metaKey := fmt.Sprintf(connectionMetaKeyPattern, connectionID.String())
	
	valueJSON, err := r.client.HGet(ctx, metaKey, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get connection metadata: %w", err)
	}
	
	var value interface{}
	if err := json.Unmarshal([]byte(valueJSON), &value); err != nil {
		return nil, fmt.Errorf("failed to unmarshal metadata value: %w", err)
	}
	
	return value, nil
}

// Subscription management

func (r *redisConnectionRepository) AddSubscription(ctx context.Context, connectionID entities.ConnectionID, topic string) error {
	subscriptionsKey := fmt.Sprintf(connectionSubsKeyPattern, connectionID.String())
	topicKey := fmt.Sprintf(topicSubscribersPattern, topic)
	
	pipe := r.client.Pipeline()
	pipe.SAdd(ctx, subscriptionsKey, topic)
	pipe.Expire(ctx, subscriptionsKey, r.connectionTTL)
	pipe.SAdd(ctx, topicKey, connectionID.String())
	pipe.Expire(ctx, topicKey, r.connectionTTL)
	
	_, err := pipe.Exec(ctx)
	return err
}

func (r *redisConnectionRepository) RemoveSubscription(ctx context.Context, connectionID entities.ConnectionID, topic string) error {
	subscriptionsKey := fmt.Sprintf(connectionSubsKeyPattern, connectionID.String())
	topicKey := fmt.Sprintf(topicSubscribersPattern, topic)
	
	pipe := r.client.Pipeline()
	pipe.SRem(ctx, subscriptionsKey, topic)
	pipe.SRem(ctx, topicKey, connectionID.String())
	
	_, err := pipe.Exec(ctx)
	return err
}

func (r *redisConnectionRepository) GetSubscriptions(ctx context.Context, connectionID entities.ConnectionID) ([]string, error) {
	subscriptionsKey := fmt.Sprintf(connectionSubsKeyPattern, connectionID.String())
	
	topics, err := r.client.SMembers(ctx, subscriptionsKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get subscriptions: %w", err)
	}
	
	return topics, nil
}

func (r *redisConnectionRepository) GetSubscribersForTopic(ctx context.Context, topic string) ([]entities.ConnectionID, error) {
	topicKey := fmt.Sprintf(topicSubscribersPattern, topic)
	
	connectionIDStrs, err := r.client.SMembers(ctx, topicKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get topic subscribers: %w", err)
	}
	
	var connectionIDs []entities.ConnectionID
	for _, idStr := range connectionIDStrs {
		connectionIDs = append(connectionIDs, entities.ConnectionID(uuid.MustParse(idStr)))
	}
	
	return connectionIDs, nil
}

// Health and cleanup operations

func (r *redisConnectionRepository) GetStaleConnections(ctx context.Context, timeout time.Duration) ([]*entities.Connection, error) {
	cutoffTime := time.Now().Add(-timeout)
	
	// Get all connections and check their heartbeats
	connectionCount, err := r.GetConnectionCount(ctx)
	if err != nil || connectionCount == 0 {
		return []*entities.Connection{}, nil
	}
	
	// Use SCAN to iterate through all connection keys
	var staleConnections []*entities.Connection
	iter := r.client.Scan(ctx, 0, "conn:*", 100).Iterator()
	
	for iter.Next(ctx) {
		connectionKey := iter.Val()
		connectionIDStr := strings.TrimPrefix(connectionKey, "conn:")
		
		connectionID := entities.ConnectionID(uuid.MustParse(connectionIDStr))
		connection, err := r.GetConnection(ctx, connectionID)
		if err != nil {
			continue
		}
		
		if connection.LastHeartbeat().Before(cutoffTime) {
			staleConnections = append(staleConnections, connection)
		}
	}
	
	return staleConnections, iter.Err()
}

func (r *redisConnectionRepository) CleanupStaleConnections(ctx context.Context, timeout time.Duration) (int64, error) {
	staleConnections, err := r.GetStaleConnections(ctx, timeout)
	if err != nil {
		return 0, err
	}
	
	var cleaned int64
	for _, connection := range staleConnections {
		if err := r.RemoveConnection(ctx, connection.ID()); err != nil {
			log.Printf("Warning: failed to remove stale connection %s: %v", connection.ID().String(), err)
		} else {
			cleaned++
		}
	}
	
	log.Printf("Cleaned up %d stale connections", cleaned)
	return cleaned, nil
}

func (r *redisConnectionRepository) GetConnectionsByLastActivity(ctx context.Context, since time.Time) ([]*entities.Connection, error) {
	// Similar to GetStaleConnections but filters by activity time
	var activeConnections []*entities.Connection
	iter := r.client.Scan(ctx, 0, "conn:*", 100).Iterator()
	
	for iter.Next(ctx) {
		connectionKey := iter.Val()
		connectionIDStr := strings.TrimPrefix(connectionKey, "conn:")
		
		connectionID := entities.ConnectionID(uuid.MustParse(connectionIDStr))
		connection, err := r.GetConnection(ctx, connectionID)
		if err != nil {
			continue
		}
		
		if connection.LastActivity().After(since) {
			activeConnections = append(activeConnections, connection)
		}
	}
	
	return activeConnections, iter.Err()
}

// Batch operations

func (r *redisConnectionRepository) StoreConnectionsBatch(ctx context.Context, connections []*entities.Connection) error {
	if len(connections) == 0 {
		return nil
	}
	
	pipe := r.client.Pipeline()
	
	for _, connection := range connections {
		connectionData, err := r.serializeConnection(connection)
		if err != nil {
			log.Printf("Warning: failed to serialize connection %s: %v", connection.ID().String(), err)
			continue
		}
		
		connectionKey := fmt.Sprintf(connectionKeyPattern, connection.ID().String())
		pipe.Set(ctx, connectionKey, connectionData, r.connectionTTL)
		
		// Add other operations for each connection
		userConnectionsKey := fmt.Sprintf(userConnectionsKeyPattern, connection.UserID().String())
		pipe.SAdd(ctx, userConnectionsKey, connection.ID().String())
		pipe.Expire(ctx, userConnectionsKey, r.connectionTTL)
	}
	
	_, err := pipe.Exec(ctx)
	return err
}

func (r *redisConnectionRepository) RemoveConnectionsBatch(ctx context.Context, connectionIDs []entities.ConnectionID) error {
	if len(connectionIDs) == 0 {
		return nil
	}
	
	var keys []string
	for _, connectionID := range connectionIDs {
		keys = append(keys, fmt.Sprintf(connectionKeyPattern, connectionID.String()))
	}
	
	return r.client.Del(ctx, keys...).Err()
}

// User presence management

func (r *redisConnectionRepository) SetUserOnlineStatus(ctx context.Context, userID uuid.UUID, isOnline bool, lastSeen time.Time) error {
	onlineKey := fmt.Sprintf(userOnlineKeyPattern, userID.String())
	lastSeenKey := fmt.Sprintf(userLastSeenKeyPattern, userID.String())
	
	pipe := r.client.Pipeline()
	
	if isOnline {
		pipe.Set(ctx, onlineKey, "true", r.presenceTTL)
		pipe.SAdd(ctx, onlineUsersKey, userID.String())
		pipe.Expire(ctx, onlineUsersKey, r.presenceTTL)
	} else {
		pipe.Del(ctx, onlineKey)
		pipe.SRem(ctx, onlineUsersKey, userID.String())
	}
	
	pipe.Set(ctx, lastSeenKey, lastSeen.Unix(), r.presenceTTL)
	
	_, err := pipe.Exec(ctx)
	return err
}

func (r *redisConnectionRepository) GetUserOnlineStatus(ctx context.Context, userID uuid.UUID) (*repositories.UserOnlineStatus, error) {
	onlineKey := fmt.Sprintf(userOnlineKeyPattern, userID.String())
	lastSeenKey := fmt.Sprintf(userLastSeenKeyPattern, userID.String())
	userConnectionsKey := fmt.Sprintf(userConnectionsKeyPattern, userID.String())
	
	pipe := r.client.Pipeline()
	onlineCmd := pipe.Get(ctx, onlineKey)
	lastSeenCmd := pipe.Get(ctx, lastSeenKey)
	connectionsCmd := pipe.SMembers(ctx, userConnectionsKey)
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user online status: %w", err)
	}
	
	isOnline := onlineCmd.Err() == nil && onlineCmd.Val() == "true"
	
	var lastSeen time.Time
	if lastSeenCmd.Err() == nil {
		if timestamp, err := strconv.ParseInt(lastSeenCmd.Val(), 10, 64); err == nil {
			lastSeen = time.Unix(timestamp, 0)
		}
	}
	
	var connectionIDs []entities.ConnectionID
	if connectionsCmd.Err() == nil {
		for _, idStr := range connectionsCmd.Val() {
			connectionIDs = append(connectionIDs, entities.ConnectionID(uuid.MustParse(idStr)))
		}
	}
	
	// Get platforms from connections
	var platforms []entities.PlatformType
	platformMap := make(map[entities.PlatformType]bool)
	
	for _, connID := range connectionIDs {
		if connection, err := r.GetConnection(ctx, connID); err == nil {
			if !platformMap[connection.PlatformType()] {
				platforms = append(platforms, connection.PlatformType())
				platformMap[connection.PlatformType()] = true
			}
		}
	}
	
	return &repositories.UserOnlineStatus{
		UserID:      userID,
		IsOnline:    isOnline,
		LastSeen:    lastSeen,
		Connections: connectionIDs,
		Platforms:   platforms,
		DeviceCount: len(connectionIDs),
		UpdatedAt:   time.Now(),
	}, nil
}

func (r *redisConnectionRepository) GetUserLastSeen(ctx context.Context, userID uuid.UUID) (*time.Time, error) {
	lastSeenKey := fmt.Sprintf(userLastSeenKeyPattern, userID.String())
	
	timestampStr, err := r.client.Get(ctx, lastSeenKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user last seen: %w", err)
	}
	
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse last seen timestamp: %w", err)
	}
	
	lastSeen := time.Unix(timestamp, 0)
	return &lastSeen, nil
}

// Platform-specific operations

func (r *redisConnectionRepository) GetConnectionsByPlatform(ctx context.Context, platformType entities.PlatformType) ([]*entities.Connection, error) {
	platformKey := fmt.Sprintf(platformConnectionsPattern, int(platformType))
	
	connectionIDs, err := r.client.SMembers(ctx, platformKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get platform connections: %w", err)
	}
	
	var connections []*entities.Connection
	for _, connectionIDStr := range connectionIDs {
		connectionID := entities.ConnectionID(uuid.MustParse(connectionIDStr))
		connection, err := r.GetConnection(ctx, connectionID)
		if err != nil {
			if err == repositories.ErrConnectionNotFound {
				// Clean up stale reference
				r.client.SRem(ctx, platformKey, connectionIDStr)
				continue
			}
			log.Printf("Warning: failed to get connection %s: %v", connectionIDStr, err)
			continue
		}
		connections = append(connections, connection)
	}
	
	return connections, nil
}

func (r *redisConnectionRepository) GetUserDevices(ctx context.Context, userID uuid.UUID) ([]repositories.UserDevice, error) {
	userDevicesKey := fmt.Sprintf(userDevicesKeyPattern, userID.String())
	
	deviceData, err := r.client.HGetAll(ctx, userDevicesKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get user devices: %w", err)
	}
	
	var devices []repositories.UserDevice
	for deviceID, deviceInfoJSON := range deviceData {
		var deviceInfo map[string]interface{}
		if err := json.Unmarshal([]byte(deviceInfoJSON), &deviceInfo); err != nil {
			log.Printf("Warning: failed to unmarshal device info for %s: %v", deviceID, err)
			continue
		}
		
		device := repositories.UserDevice{
			UserID:   userID,
			DeviceID: deviceID,
		}
		
		if platformType, ok := deviceInfo["platform_type"].(float64); ok {
			device.PlatformType = entities.PlatformType(int32(platformType))
		}
		
		if connectedAt, ok := deviceInfo["connected_at"].(float64); ok {
			device.LastSeen = time.Unix(int64(connectedAt), 0)
		}
		
		if connectionIDStr, ok := deviceInfo["connection_id"].(string); ok {
			connectionID := entities.ConnectionID(uuid.MustParse(connectionIDStr))
			device.ConnectionID = &connectionID
			device.IsOnline = true // Has active connection
		}
		
		devices = append(devices, device)
	}
	
	return devices, nil
}

// Connection statistics

func (r *redisConnectionRepository) GetConnectionStats(ctx context.Context) (*repositories.ConnectionStats, error) {
	totalConnections, err := r.GetConnectionCount(ctx)
	if err != nil {
		return nil, err
	}
	
	onlineUsersCount, err := r.client.SCard(ctx, onlineUsersKey).Result()
	if err != nil {
		onlineUsersCount = 0
	}
	
	// Get platform distribution
	platformStats := make(map[string]int64)
	for platformType := entities.PlatformWeb; platformType <= entities.PlatformDesktop; platformType++ {
		platformKey := fmt.Sprintf(platformConnectionsPattern, int(platformType))
		count, err := r.client.SCard(ctx, platformKey).Result()
		if err == nil {
			platformStats[platformType.String()] = count
		}
	}
	
	return &repositories.ConnectionStats{
		TotalConnections:       totalConnections,
		OnlineUsers:           onlineUsersCount,
		ConnectionsByPlatform:  platformStats,
		ConnectionsByStatus:    make(map[string]int64), // Would need more complex tracking
		AverageConnectionTime:  time.Duration(0),      // Would need historical data
		PeakConnections:       totalConnections,        // Simplified for now
		ConnectionRate:        0,                       // Would need time-series data
		DisconnectionRate:     0,                       // Would need time-series data
		LastUpdated:           time.Now(),
	}, nil
}

func (r *redisConnectionRepository) GetUserConnectionHistory(ctx context.Context, userID uuid.UUID, limit int) ([]*repositories.ConnectionHistoryEntry, error) {
	// This would require maintaining connection history
	// For now, return empty slice
	return []*repositories.ConnectionHistoryEntry{}, nil
}

// Helper methods for serialization

func (r *redisConnectionRepository) serializeConnection(connection *entities.Connection) (string, error) {
	data := map[string]interface{}{
		"id":             connection.ID().String(),
		"user_id":        connection.UserID().String(),
		"device_id":      connection.DeviceID(),
		"platform_type":  int(connection.PlatformType()),
		"status":         string(connection.Status()),
		"is_background":  connection.IsBackground(),
		"is_compressed":  connection.IsCompressed(),
		"connected_at":   connection.ConnectedAt().Unix(),
		"last_heartbeat": connection.LastHeartbeat().Unix(),
		"last_activity":  connection.LastActivity().Unix(),
		"subscriptions":  connection.GetSubscriptions(),
		"metadata":       connection.GetAllMetadata(),
	}
	
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	
	return string(jsonData), nil
}

func (r *redisConnectionRepository) deserializeConnection(data string) (*entities.Connection, error) {
	var connData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &connData); err != nil {
		return nil, err
	}
	
	// This is a simplified deserialization - in a real implementation,
	// you'd need to reconstruct the full connection entity
	// For now, we'll create a basic connection structure
	userIDStr, ok := connData["user_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid user_id in connection data")
	}
	
	deviceID, ok := connData["device_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid device_id in connection data")
	}
	
	platformType, ok := connData["platform_type"].(float64)
	if !ok {
		return nil, fmt.Errorf("invalid platform_type in connection data")
	}
	
	userID := uuid.MustParse(userIDStr)
	
	// Note: This is simplified - you'd need to properly reconstruct the WebSocket connection
	// and all other fields from the stored data
	connection := entities.NewConnection(
		userID,
		deviceID,
		entities.PlatformType(int32(platformType)),
		nil, // WebSocket connection would be nil for stored connections
		256, // Buffer size
	)
	
	return connection, nil
}