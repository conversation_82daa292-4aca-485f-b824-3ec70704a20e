package redis

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/pkg/config"
)

type SequenceManager struct {
	client      *redis.Client
	config      *config.MessagingServiceConfig
	lockTimeout time.Duration
	dataTimeout time.Duration
}

func NewSequenceManager(client *redis.Client, config *config.MessagingServiceConfig) *SequenceManager {
	return &SequenceManager{
		client:      client,
		config:      config,
		lockTimeout: 10 * time.Second,
		dataTimeout: 24 * time.Hour,
	}
}

// AllocateSequence allocates a single sequence number for a conversation
func (s *SequenceManager) AllocateSequence(ctx context.Context, conversationID conversationentities.ConversationID) (int64, error) {
	sequences, err := s.AllocateSequences(ctx, conversationID, 1)
	if err != nil {
		return 0, err
	}
	return sequences[0], nil
}

// AllocateSequences allocates multiple sequence numbers atomically
func (s *SequenceManager) AllocateSequences(ctx context.Context, conversationID conversationentities.ConversationID, count int64) ([]int64, error) {
	return s.allocateSequencesWithRetry(ctx, conversationID, count, 0)
}

// allocateSequencesWithRetry allocates multiple sequence numbers with retry limit
func (s *SequenceManager) allocateSequencesWithRetry(ctx context.Context, conversationID conversationentities.ConversationID, count int64, retryCount int) ([]int64, error) {
	const maxRetries = 10 // Maximum number of retries to prevent infinite loops
	key := s.getSequenceKey(conversationID)
	
	// Determine batch size based on conversation type
	batchSize := s.config.Messaging.SequenceBatchSize
	if s.isGroupConversation(conversationID) {
		batchSize = s.config.Messaging.GroupSequenceBatch
	}
	
	// Redis Lua script for atomic sequence allocation (OpenIM-inspired)
	script := `
		local key = KEYS[1]
		local size = tonumber(ARGV[1])
		local lockSecond = tonumber(ARGV[2])
		local dataSecond = tonumber(ARGV[3])
		local batchSize = tonumber(ARGV[4])
		
		-- If size is 0, just return current values
		if size == 0 then
			redis.call("EXPIRE", key, dataSecond)
			local curr = redis.call("HGET", key, "CURR") or "0"
			local last = redis.call("HGET", key, "LAST") or "0"
			return {0, tonumber(curr), tonumber(last)}
		end
		
		-- Check if key exists
		if redis.call("EXISTS", key) == 0 then
			-- Initialize sequence from 1
			redis.call("HSET", key, "CURR", 0)
			redis.call("HSET", key, "LAST", batchSize)
			redis.call("EXPIRE", key, dataSecond)
		end
		
		-- Check if there's a lock
		if redis.call("HEXISTS", key, "LOCK") == 1 then
			return {2} -- Locked, retry later
		end
		
		local curr_seq = tonumber(redis.call("HGET", key, "CURR") or "0")
		local last_seq = tonumber(redis.call("HGET", key, "LAST") or "0")
		local max_seq = curr_seq + size
		
		-- Check if we need more sequences
		if max_seq > last_seq then
			-- Set lock for database allocation
			local lockValue = math.random(1, 999999999)
			redis.call("HSET", key, "LOCK", lockValue)
			redis.call("EXPIRE", key, lockSecond)
			return {3, curr_seq, last_seq, lockValue} -- Need database allocation
		end
		
		-- Successful allocation
		redis.call("HSET", key, "CURR", max_seq)
		redis.call("EXPIRE", key, dataSecond)
		return {0, curr_seq + 1, last_seq} -- Return starting sequence
	`
	
	result, err := s.client.Eval(ctx, script, []string{key}, count, s.lockTimeout.Seconds(), s.dataTimeout.Seconds(), batchSize).Result()
	if err != nil {
		return nil, fmt.Errorf("sequence allocation failed: %w", err)
	}
	
	resultSlice, ok := result.([]interface{})
	if !ok || len(resultSlice) < 1 {
		return nil, fmt.Errorf("invalid sequence allocation result")
	}
	
	status := resultSlice[0].(int64)
	
	switch status {
	case 0: // Successful allocation
		if len(resultSlice) < 3 {
			return nil, fmt.Errorf("invalid successful allocation result")
		}
		startSeq := resultSlice[1].(int64)
		return s.generateSequenceRange(startSeq, count), nil
		
	case 2: // Locked, retry
		if retryCount >= maxRetries {
			return nil, fmt.Errorf("max retries exceeded waiting for lock on conversation %s", conversationID)
		}
		time.Sleep(10 * time.Millisecond)
		return s.allocateSequencesWithRetry(ctx, conversationID, count, retryCount+1)
		
	case 3: // Need database allocation
		if len(resultSlice) < 4 {
			return nil, fmt.Errorf("invalid database allocation result")
		}
		currSeq := resultSlice[1].(int64)
		lastSeq := resultSlice[2].(int64)
		lockValue := resultSlice[3].(int64)
		
		return s.allocateFromDatabase(ctx, conversationID, count, currSeq, lastSeq, lockValue)
		
	default:
		return nil, fmt.Errorf("unknown sequence allocation status: %d", status)
	}
}

// SetSequence sets the current sequence for a conversation (used during initialization)
func (s *SequenceManager) SetSequence(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64) error {
	key := s.getSequenceKey(conversationID)
	batchSize := s.config.Messaging.SequenceBatchSize
	
	if s.isGroupConversation(conversationID) {
		batchSize = s.config.Messaging.GroupSequenceBatch
	}
	
	pipe := s.client.Pipeline()
	pipe.HSet(ctx, key, "CURR", sequence)
	pipe.HSet(ctx, key, "LAST", sequence+batchSize)
	pipe.Expire(ctx, key, s.dataTimeout)
	
	_, err := pipe.Exec(ctx)
	return err
}

// GetCurrentSequence returns the current sequence number for a conversation
func (s *SequenceManager) GetCurrentSequence(ctx context.Context, conversationID conversationentities.ConversationID) (int64, error) {
	key := s.getSequenceKey(conversationID)
	
	result, err := s.client.HGet(ctx, key, "CURR").Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil // No sequence allocated yet
		}
		return 0, err
	}
	
	sequence, err := strconv.ParseInt(result, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid sequence value: %w", err)
	}
	
	return sequence, nil
}

// IncrementSequence increments the sequence by a specific amount
func (s *SequenceManager) IncrementSequence(ctx context.Context, conversationID conversationentities.ConversationID, increment int64) (int64, error) {
	key := s.getSequenceKey(conversationID)
	
	result, err := s.client.HIncrBy(ctx, key, "CURR", increment).Result()
	if err != nil {
		return 0, err
	}
	
	// Extend TTL
	s.client.Expire(ctx, key, s.dataTimeout)
	
	return result, nil
}

// ClearSequence removes the sequence cache for a conversation
func (s *SequenceManager) ClearSequence(ctx context.Context, conversationID conversationentities.ConversationID) error {
	key := s.getSequenceKey(conversationID)
	return s.client.Del(ctx, key).Err()
}

// GetSequenceInfo returns detailed information about sequence allocation
func (s *SequenceManager) GetSequenceInfo(ctx context.Context, conversationID conversationentities.ConversationID) (*SequenceInfo, error) {
	key := s.getSequenceKey(conversationID)
	
	result, err := s.client.HMGet(ctx, key, "CURR", "LAST", "LOCK").Result()
	if err != nil {
		return nil, err
	}
	
	info := &SequenceInfo{
		ConversationID: conversationID,
	}
	
	if result[0] != nil {
		if curr, err := strconv.ParseInt(result[0].(string), 10, 64); err == nil {
			info.CurrentSequence = curr
		}
	}
	
	if result[1] != nil {
		if last, err := strconv.ParseInt(result[1].(string), 10, 64); err == nil {
			info.LastSequence = last
		}
	}
	
	info.IsLocked = result[2] != nil
	
	// Get TTL
	ttl, _ := s.client.TTL(ctx, key).Result()
	info.TTL = ttl
	
	return info, nil
}

// Helper methods
func (s *SequenceManager) getSequenceKey(conversationID conversationentities.ConversationID) string {
	return fmt.Sprintf("seq:%s", conversationID.String())
}

func (s *SequenceManager) isGroupConversation(conversationID conversationentities.ConversationID) bool {
	// This is a simplified check - in a real implementation, you would
	// query the conversation type from the database or cache
	// For now, assume all conversations are group conversations for higher batch size
	return true
}

func (s *SequenceManager) generateSequenceRange(start int64, count int64) []int64 {
	sequences := make([]int64, count)
	for i := int64(0); i < count; i++ {
		sequences[i] = start + i
	}
	return sequences
}

func (s *SequenceManager) allocateFromDatabase(ctx context.Context, conversationID conversationentities.ConversationID, count, currSeq, lastSeq, lockValue int64) ([]int64, error) {
	// In a real implementation, this would:
	// 1. Query the database for the actual max sequence
	// 2. Allocate a new batch from the database
	// 3. Update Redis with the new allocation
	// 4. Release the lock
	
	// For now, simulate database allocation
	key := s.getSequenceKey(conversationID)
	batchSize := s.config.Messaging.SequenceBatchSize
	
	if s.isGroupConversation(conversationID) {
		batchSize = s.config.Messaging.GroupSequenceBatch
	}
	
	// Simulate getting max sequence from database (would be actual DB query)
	dbMaxSequence := lastSeq // In real implementation, query from MongoDB
	
	// Allocate new batch
	newLastSeq := dbMaxSequence + batchSize
	newCurrSeq := dbMaxSequence + count
	
	// Update Redis with new allocation and release lock
	script := `
		local key = KEYS[1]
		local lockValue = ARGV[1]
		local newCurr = ARGV[2]
		local newLast = ARGV[3]
		local dataSecond = ARGV[4]
		
		-- Check if we still hold the lock
		local currentLock = redis.call("HGET", key, "LOCK")
		if currentLock ~= lockValue then
			return {1} -- Lock was stolen
		end
		
		-- Update sequences and remove lock
		redis.call("HSET", key, "CURR", newCurr)
		redis.call("HSET", key, "LAST", newLast)
		redis.call("HDEL", key, "LOCK")
		redis.call("EXPIRE", key, dataSecond)
		
		return {0, newCurr - tonumber(ARGV[5]) + 1} -- Return starting sequence
	`
	
	result, err := s.client.Eval(ctx, script, []string{key}, 
		strconv.FormatInt(lockValue, 10),
		strconv.FormatInt(newCurrSeq, 10),
		strconv.FormatInt(newLastSeq, 10),
		strconv.FormatFloat(s.dataTimeout.Seconds(), 'f', 0, 64),
		strconv.FormatInt(count, 10),
	).Result()
	
	if err != nil {
		return nil, fmt.Errorf("database allocation update failed: %w", err)
	}
	
	resultSlice := result.([]interface{})
	status := resultSlice[0].(int64)
	
	if status == 1 {
		// Lock was stolen, retry
		return s.AllocateSequences(ctx, conversationID, count)
	}
	
	startSeq := resultSlice[1].(int64)
	return s.generateSequenceRange(startSeq, count), nil
}

type SequenceInfo struct {
	ConversationID  conversationentities.ConversationID
	CurrentSequence int64
	LastSequence    int64
	IsLocked        bool
	TTL             time.Duration
}