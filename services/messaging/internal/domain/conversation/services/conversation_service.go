package services

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
)

type ConversationService interface {
	// Conversation CRUD operations
	CreateDirectConversation(ctx context.Context, user1ID, user2ID uuid.UUID) (*entities.Conversation, error)
	CreateGroupConversation(ctx context.Context, request *CreateGroupConversationRequest) (*entities.Conversation, error)
	CreateTeamChannel(ctx context.Context, request *CreateTeamChannelRequest) (*entities.Conversation, error)
	
	// Conversation retrieval
	GetConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) (*entities.Conversation, error)
	GetUserConversations(ctx context.Context, userID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error)
	GetTeamConversations(ctx context.Context, teamID uuid.UUID, userID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error)
	GetDirectConversation(ctx context.Context, user1ID, user2ID uuid.UUID) (*entities.Conversation, error)
	
	// Conversation modifications
	UpdateConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, request *UpdateConversationRequest) (*entities.Conversation, error)
	DeleteConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error
	
	// Participant management
	AddParticipant(ctx context.Context, conversationID entities.ConversationID, requestingUserID, targetUserID uuid.UUID, role entities.ParticipantRole) error
	RemoveParticipant(ctx context.Context, conversationID entities.ConversationID, requestingUserID, targetUserID uuid.UUID) error
	UpdateParticipantRole(ctx context.Context, conversationID entities.ConversationID, requestingUserID, targetUserID uuid.UUID, role entities.ParticipantRole) error
	LeaveConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error
	GetConversationParticipants(ctx context.Context, conversationID entities.ConversationID) ([]uuid.UUID, error)
	
	// Activity tracking
	UpdateLastActivity(ctx context.Context, conversationID entities.ConversationID, lastMessageID string, sequence int64) error
	UpdateLastReadSequence(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, sequence int64) error
	
	// Search and filtering
	SearchConversations(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Conversation, error)
	GetRecentConversations(ctx context.Context, userID uuid.UUID, limit int) ([]*entities.Conversation, error)
	GetUnreadConversations(ctx context.Context, userID uuid.UUID) ([]*ConversationUnreadInfo, error)
	
	// Access control
	ValidateAccess(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error
	IsParticipant(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) (bool, error)
	IsAdmin(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) (bool, error)
}

type CreateGroupConversationRequest struct {
	Name         string
	Description  *string
	CreatorID    uuid.UUID
	Participants []uuid.UUID
}

type CreateTeamChannelRequest struct {
	Name         string
	Description  *string
	TeamID       uuid.UUID
	CreatorID    uuid.UUID
	Participants []uuid.UUID
}

type UpdateConversationRequest struct {
	Name        *string
	Description *string
}

type ConversationUnreadInfo struct {
	ConversationID entities.ConversationID
	UnreadCount    int64
	LastMessageID  *string
	LastActivity   time.Time
}