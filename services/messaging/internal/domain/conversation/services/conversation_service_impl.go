package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
)

type conversationServiceImpl struct {
	conversationRepo repositories.ConversationRepository
	teamClient       TeamClient // Interface for team service integration
	eventPublisher   ConversationEventPublisher
}

// TeamClient interface for team service integration
type TeamClient interface {
	ValidateTeamAccess(ctx context.Context, teamID uuid.UUID, userID uuid.UUID) error
	GetTeamMembers(ctx context.Context, teamID uuid.UUID) ([]uuid.UUID, error)
}

// ConversationEventPublisher interface for events
type ConversationEventPublisher interface {
	PublishConversationCreated(ctx context.Context, conversation *entities.Conversation) error
	PublishConversationUpdated(ctx context.Context, conversation *entities.Conversation) error
	PublishParticipantAdded(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error
	PublishParticipantRemoved(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error
}

func NewConversationService(
	conversationRepo repositories.ConversationRepository,
	teamClient TeamClient,
	eventPublisher ConversationEventPublisher,
) ConversationService {
	return &conversationServiceImpl{
		conversationRepo: conversationRepo,
		teamClient:       teamClient,
		eventPublisher:   eventPublisher,
	}
}

func (s *conversationServiceImpl) CreateDirectConversation(ctx context.Context, user1ID, user2ID uuid.UUID) (*entities.Conversation, error) {
	// Check if direct conversation already exists
	existing, err := s.conversationRepo.GetDirectConversation(ctx, user1ID, user2ID)
	if err == nil {
		return existing, nil // Return existing conversation
	}
	
	// Create new direct conversation
	conversation := entities.NewDirectConversation(user1ID, user2ID)
	
	// Store conversation
	if err := s.conversationRepo.Create(ctx, conversation); err != nil {
		return nil, fmt.Errorf("failed to create direct conversation: %w", err)
	}
	
	// Publish event
	if err := s.eventPublisher.PublishConversationCreated(ctx, conversation); err != nil {
		fmt.Printf("Warning: failed to publish conversation created event: %v\n", err)
	}
	
	return conversation, nil
}

func (s *conversationServiceImpl) CreateGroupConversation(ctx context.Context, request *CreateGroupConversationRequest) (*entities.Conversation, error) {
	// Validate request
	if err := s.validateGroupConversationRequest(request); err != nil {
		return nil, err
	}
	
	// Create group conversation
	conversation := entities.NewGroupConversation(request.Name, request.CreatorID, nil)
	
	if request.Description != nil {
		conversation.UpdateDescription(*request.Description)
	}
	
	// Add participants
	for _, participantID := range request.Participants {
		if participantID != request.CreatorID {
			conversation.AddParticipant(participantID, entities.ParticipantRoleMember)
		}
	}
	
	// Store conversation
	if err := s.conversationRepo.Create(ctx, conversation); err != nil {
		return nil, fmt.Errorf("failed to create group conversation: %w", err)
	}
	
	// Publish event
	if err := s.eventPublisher.PublishConversationCreated(ctx, conversation); err != nil {
		fmt.Printf("Warning: failed to publish conversation created event: %v\n", err)
	}
	
	return conversation, nil
}

func (s *conversationServiceImpl) CreateTeamChannel(ctx context.Context, request *CreateTeamChannelRequest) (*entities.Conversation, error) {
	// Validate request
	if err := s.validateTeamChannelRequest(request); err != nil {
		return nil, err
	}
	
	// Validate team access
	if err := s.teamClient.ValidateTeamAccess(ctx, request.TeamID, request.CreatorID); err != nil {
		return nil, fmt.Errorf("creator does not have access to team: %w", err)
	}
	
	// Create team channel
	conversation := entities.NewGroupConversation(request.Name, request.CreatorID, &request.TeamID)
	
	if request.Description != nil {
		conversation.UpdateDescription(*request.Description)
	}
	
	// Add specified participants or all team members
	if len(request.Participants) > 0 {
		for _, participantID := range request.Participants {
			if participantID != request.CreatorID {
				// Validate each participant has team access
				if err := s.teamClient.ValidateTeamAccess(ctx, request.TeamID, participantID); err != nil {
					fmt.Printf("Warning: skipping participant %s - no team access: %v\n", participantID, err)
					continue
				}
				conversation.AddParticipant(participantID, entities.ParticipantRoleMember)
			}
		}
	} else {
		// Add all team members
		teamMembers, err := s.teamClient.GetTeamMembers(ctx, request.TeamID)
		if err != nil {
			return nil, fmt.Errorf("failed to get team members: %w", err)
		}
		
		for _, memberID := range teamMembers {
			if memberID != request.CreatorID {
				conversation.AddParticipant(memberID, entities.ParticipantRoleMember)
			}
		}
	}
	
	// Store conversation
	if err := s.conversationRepo.Create(ctx, conversation); err != nil {
		return nil, fmt.Errorf("failed to create team channel: %w", err)
	}
	
	// Publish event
	if err := s.eventPublisher.PublishConversationCreated(ctx, conversation); err != nil {
		fmt.Printf("Warning: failed to publish conversation created event: %v\n", err)
	}
	
	return conversation, nil
}

func (s *conversationServiceImpl) GetConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) (*entities.Conversation, error) {
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, err
	}
	
	// Check access
	if err := s.validateConversationAccess(ctx, conversation, userID); err != nil {
		return nil, err
	}
	
	return conversation, nil
}

func (s *conversationServiceImpl) GetUserConversations(ctx context.Context, userID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error) {
	return s.conversationRepo.GetUserConversations(ctx, userID, limit, offset)
}

func (s *conversationServiceImpl) GetTeamConversations(ctx context.Context, teamID uuid.UUID, userID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error) {
	// Validate team access
	if err := s.teamClient.ValidateTeamAccess(ctx, teamID, userID); err != nil {
		return nil, fmt.Errorf("user does not have access to team: %w", err)
	}
	
	return s.conversationRepo.GetTeamConversations(ctx, teamID, limit, offset)
}

func (s *conversationServiceImpl) GetDirectConversation(ctx context.Context, user1ID, user2ID uuid.UUID) (*entities.Conversation, error) {
	return s.conversationRepo.GetDirectConversation(ctx, user1ID, user2ID)
}

func (s *conversationServiceImpl) UpdateConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, request *UpdateConversationRequest) (*entities.Conversation, error) {
	// Get conversation
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, err
	}
	
	// Check if user can update (admin or creator)
	if !s.canModifyConversation(conversation, userID) {
		return nil, repositories.ErrAccessDenied
	}
	
	// Update fields
	if request.Name != nil {
		if err := s.validateConversationName(*request.Name); err != nil {
			return nil, err
		}
		conversation.UpdateName(*request.Name)
	}
	
	if request.Description != nil {
		conversation.UpdateDescription(*request.Description)
	}
	
	// Store updated conversation
	if err := s.conversationRepo.Update(ctx, conversation); err != nil {
		return nil, fmt.Errorf("failed to update conversation: %w", err)
	}
	
	// Publish event
	if err := s.eventPublisher.PublishConversationUpdated(ctx, conversation); err != nil {
		fmt.Printf("Warning: failed to publish conversation updated event: %v\n", err)
	}
	
	return conversation, nil
}

func (s *conversationServiceImpl) DeleteConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error {
	// Get conversation
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}
	
	// Check if user can delete (only creator or admin)
	if !s.canDeleteConversation(conversation, userID) {
		return repositories.ErrAccessDenied
	}
	
	// Cannot delete direct conversations
	if conversation.IsDirect() {
		return fmt.Errorf("cannot delete direct conversations")
	}
	
	// Delete conversation
	if err := s.conversationRepo.Delete(ctx, conversationID); err != nil {
		return fmt.Errorf("failed to delete conversation: %w", err)
	}
	
	return nil
}

func (s *conversationServiceImpl) AddParticipant(ctx context.Context, conversationID entities.ConversationID, requestingUserID, targetUserID uuid.UUID, role entities.ParticipantRole) error {
	// Get conversation
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}
	
	// Check if requesting user can add participants
	if !s.canModifyParticipants(conversation, requestingUserID) {
		return repositories.ErrAccessDenied
	}
	
	// Cannot add to direct conversations
	if conversation.IsDirect() {
		return fmt.Errorf("cannot add participants to direct conversations")
	}
	
	// For team channels, validate team access
	if conversation.IsTeamChannel() && conversation.TeamID() != nil {
		if err := s.teamClient.ValidateTeamAccess(ctx, *conversation.TeamID(), targetUserID); err != nil {
			return fmt.Errorf("target user does not have team access: %w", err)
		}
	}
	
	// Check if user is already a participant
	if conversation.HasParticipant(targetUserID) {
		return repositories.ErrDuplicateParticipant
	}
	
	// Add participant to repository
	if err := s.conversationRepo.AddParticipant(ctx, conversationID, targetUserID, role); err != nil {
		return fmt.Errorf("failed to add participant: %w", err)
	}
	
	// Publish event
	if err := s.eventPublisher.PublishParticipantAdded(ctx, conversationID, targetUserID); err != nil {
		fmt.Printf("Warning: failed to publish participant added event: %v\n", err)
	}
	
	return nil
}

func (s *conversationServiceImpl) RemoveParticipant(ctx context.Context, conversationID entities.ConversationID, requestingUserID, targetUserID uuid.UUID) error {
	// Get conversation
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}
	
	// Check if requesting user can remove participants
	if !s.canModifyParticipants(conversation, requestingUserID) && requestingUserID != targetUserID {
		return repositories.ErrAccessDenied
	}
	
	// Cannot remove from direct conversations
	if conversation.IsDirect() {
		return fmt.Errorf("cannot remove participants from direct conversations")
	}
	
	// Cannot remove creator
	if conversation.CreatorID() == targetUserID {
		return repositories.ErrCannotRemoveCreator
	}
	
	// Remove participant
	if err := s.conversationRepo.RemoveParticipant(ctx, conversationID, targetUserID); err != nil {
		return fmt.Errorf("failed to remove participant: %w", err)
	}
	
	// Publish event
	if err := s.eventPublisher.PublishParticipantRemoved(ctx, conversationID, targetUserID); err != nil {
		fmt.Printf("Warning: failed to publish participant removed event: %v\n", err)
	}
	
	return nil
}

func (s *conversationServiceImpl) UpdateParticipantRole(ctx context.Context, conversationID entities.ConversationID, requestingUserID, targetUserID uuid.UUID, role entities.ParticipantRole) error {
	// Get conversation
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}
	
	// Check if requesting user can modify roles (admin only)
	if !conversation.IsParticipantAdmin(requestingUserID) {
		return repositories.ErrAccessDenied
	}
	
	// Cannot change creator role
	if conversation.CreatorID() == targetUserID {
		return fmt.Errorf("cannot change creator role")
	}
	
	// Validate role
	if !s.isValidParticipantRole(role) {
		return repositories.ErrInvalidParticipantRole
	}
	
	// Update role
	if err := s.conversationRepo.UpdateParticipantRole(ctx, conversationID, targetUserID, role); err != nil {
		return fmt.Errorf("failed to update participant role: %w", err)
	}
	
	return nil
}

func (s *conversationServiceImpl) LeaveConversation(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error {
	return s.RemoveParticipant(ctx, conversationID, userID, userID)
}

func (s *conversationServiceImpl) UpdateLastActivity(ctx context.Context, conversationID entities.ConversationID, lastMessageID string, sequence int64) error {
	return s.conversationRepo.UpdateLastActivity(ctx, conversationID, lastMessageID, sequence)
}

func (s *conversationServiceImpl) UpdateLastReadSequence(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, sequence int64) error {
	// Validate access
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}
	
	if !conversation.HasParticipant(userID) {
		return repositories.ErrAccessDenied
	}
	
	return s.conversationRepo.UpdateLastReadSequence(ctx, conversationID, userID, sequence)
}

func (s *conversationServiceImpl) SearchConversations(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Conversation, error) {
	// Sanitize query
	query = strings.TrimSpace(query)
	if len(query) < 2 {
		return nil, fmt.Errorf("search query must be at least 2 characters")
	}
	
	return s.conversationRepo.SearchConversations(ctx, userID, query, limit, offset)
}

func (s *conversationServiceImpl) GetRecentConversations(ctx context.Context, userID uuid.UUID, limit int) ([]*entities.Conversation, error) {
	return s.conversationRepo.GetRecentConversations(ctx, userID, limit)
}

func (s *conversationServiceImpl) GetUnreadConversations(ctx context.Context, userID uuid.UUID) ([]*ConversationUnreadInfo, error) {
	unreadInfos, err := s.conversationRepo.GetUnreadConversations(ctx, userID)
	if err != nil {
		return nil, err
	}
	
	// Convert to service type
	result := make([]*ConversationUnreadInfo, len(unreadInfos))
	for i, info := range unreadInfos {
		result[i] = &ConversationUnreadInfo{
			ConversationID: info.ConversationID,
			UnreadCount:    info.UnreadCount,
			LastMessageID:  info.LastMessageID,
			LastActivity:   info.LastActivity,
		}
	}
	
	return result, nil
}

func (s *conversationServiceImpl) ValidateAccess(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error {
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}
	
	return s.validateConversationAccess(ctx, conversation, userID)
}

func (s *conversationServiceImpl) IsParticipant(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) (bool, error) {
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		if err == repositories.ErrConversationNotFound {
			return false, nil
		}
		return false, err
	}
	
	return conversation.HasParticipant(userID), nil
}

func (s *conversationServiceImpl) IsAdmin(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) (bool, error) {
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		if err == repositories.ErrConversationNotFound {
			return false, nil
		}
		return false, err
	}
	
	return conversation.IsParticipantAdmin(userID), nil
}

func (s *conversationServiceImpl) GetConversationParticipants(ctx context.Context, conversationID entities.ConversationID) ([]uuid.UUID, error) {
	return s.conversationRepo.GetParticipantIDs(ctx, conversationID)
}

// Helper methods
func (s *conversationServiceImpl) validateConversationAccess(ctx context.Context, conversation *entities.Conversation, userID uuid.UUID) error {
	// Check if user is participant
	if !conversation.HasParticipant(userID) {
		// For team channels, also check team access
		if conversation.IsTeamChannel() && conversation.TeamID() != nil {
			if err := s.teamClient.ValidateTeamAccess(ctx, *conversation.TeamID(), userID); err != nil {
				return repositories.ErrAccessDenied
			}
			return nil
		}
		return repositories.ErrAccessDenied
	}
	
	return nil
}

func (s *conversationServiceImpl) canModifyConversation(conversation *entities.Conversation, userID uuid.UUID) bool {
	return conversation.CreatorID() == userID || conversation.IsParticipantAdmin(userID)
}

func (s *conversationServiceImpl) canDeleteConversation(conversation *entities.Conversation, userID uuid.UUID) bool {
	return conversation.CreatorID() == userID
}

func (s *conversationServiceImpl) canModifyParticipants(conversation *entities.Conversation, userID uuid.UUID) bool {
	return conversation.CreatorID() == userID || conversation.IsParticipantAdmin(userID)
}

func (s *conversationServiceImpl) validateGroupConversationRequest(request *CreateGroupConversationRequest) error {
	if err := s.validateConversationName(request.Name); err != nil {
		return err
	}
	
	if len(request.Participants) == 0 {
		return fmt.Errorf("group conversation must have at least one participant")
	}
	
	if len(request.Participants) > 100 {
		return fmt.Errorf("group conversation cannot have more than 100 participants")
	}
	
	return nil
}

func (s *conversationServiceImpl) validateTeamChannelRequest(request *CreateTeamChannelRequest) error {
	if err := s.validateConversationName(request.Name); err != nil {
		return err
	}
	
	if request.TeamID == uuid.Nil {
		return fmt.Errorf("team ID is required for team channels")
	}
	
	return nil
}

func (s *conversationServiceImpl) validateConversationName(name string) error {
	name = strings.TrimSpace(name)
	if len(name) == 0 {
		return fmt.Errorf("conversation name cannot be empty")
	}
	
	if len(name) > 100 {
		return fmt.Errorf("conversation name cannot exceed 100 characters")
	}
	
	return nil
}

func (s *conversationServiceImpl) isValidParticipantRole(role entities.ParticipantRole) bool {
	switch role {
	case entities.ParticipantRoleAdmin, entities.ParticipantRoleMember, entities.ParticipantRoleObserver:
		return true
	default:
		return false
	}
}