package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
)

type ConversationRepository interface {
	// Conversation CRUD operations
	Create(ctx context.Context, conversation *entities.Conversation) error
	GetByID(ctx context.Context, id entities.ConversationID) (*entities.Conversation, error)
	Update(ctx context.Context, conversation *entities.Conversation) error
	Delete(ctx context.Context, id entities.ConversationID) error
	
	// Conversation queries
	GetUserConversations(ctx context.Context, userID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error)
	GetByParticipant(ctx context.Context, userID uuid.UUID, limit int, offset int64) ([]*entities.Conversation, error)
	GetTeamConversations(ctx context.Context, teamID uuid.UUID, limit int, offset int) ([]*entities.Conversation, error)
	GetDirectConversation(ctx context.Context, user1ID, user2ID uuid.UUID) (*entities.Conversation, error)
	
	// Participant operations
	AddParticipant(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, role entities.ParticipantRole) error
	RemoveParticipant(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID) error
	UpdateParticipantRole(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, role entities.ParticipantRole) error
	GetParticipants(ctx context.Context, conversationID entities.ConversationID) ([]entities.ConversationParticipant, error)
	GetParticipantIDs(ctx context.Context, conversationID entities.ConversationID) ([]uuid.UUID, error)
	
	// Activity and metrics
	UpdateLastActivity(ctx context.Context, conversationID entities.ConversationID, lastMessageID string, sequence int64) error
	UpdateLastReadSequence(ctx context.Context, conversationID entities.ConversationID, userID uuid.UUID, sequence int64) error
	GetUnreadConversations(ctx context.Context, userID uuid.UUID) ([]*ConversationUnreadInfo, error)
	
	// Search and filtering
	SearchConversations(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Conversation, error)
	GetRecentConversations(ctx context.Context, userID uuid.UUID, limit int) ([]*entities.Conversation, error)
	
	// Batch operations
	GetConversationsBatch(ctx context.Context, conversationIDs []entities.ConversationID) ([]*entities.Conversation, error)
	UpdateLastActivityBatch(ctx context.Context, updates []ConversationActivityUpdate) error
	
	// Analytics
	GetConversationStats(ctx context.Context, conversationID entities.ConversationID, fromTime, toTime time.Time) (*ConversationStats, error)
	GetUserConversationStats(ctx context.Context, userID uuid.UUID, fromTime, toTime time.Time) (*UserConversationStats, error)
}

type ConversationUnreadInfo struct {
	ConversationID entities.ConversationID
	UnreadCount    int64
	LastMessageID  *string
	LastActivity   time.Time
}

type ConversationActivityUpdate struct {
	ConversationID entities.ConversationID
	LastMessageID  string
	Sequence       int64
	Timestamp      time.Time
}

type ConversationStats struct {
	TotalMessages      int64
	ActiveParticipants int64
	MessagesThisWeek   int64
	MessagesThisMonth  int64
	PeakActivity       time.Time
	AvgResponseTime    time.Duration
	MostActiveUser     *uuid.UUID
	CreatedAt          time.Time
	LastActivity       time.Time
}

type UserConversationStats struct {
	TotalConversations   int64
	DirectConversations  int64
	GroupConversations   int64
	TeamConversations    int64
	ActiveConversations  int64 // Conversations with activity in last 30 days
	MostActiveConversation *entities.ConversationID
	AvgParticipants      float64
	CreatedConversations int64 // Conversations created by user
	FirstConversation    *time.Time
	LastActivity         *time.Time
}