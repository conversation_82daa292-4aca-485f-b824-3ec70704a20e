package repositories

import "errors"

// Repository errors
var (
	ErrConversationNotFound = errors.New("conversation not found")
	ErrParticipantNotFound  = errors.New("participant not found")
	ErrAccessDenied         = errors.New("access denied")
	ErrInvalidConversationType = errors.New("invalid conversation type")
	ErrDuplicateParticipant = errors.New("participant already exists")
	ErrCannotRemoveCreator  = errors.New("cannot remove conversation creator")
	ErrInvalidParticipantRole = errors.New("invalid participant role")
)