package entities

import (
	"time"

	"github.com/google/uuid"
)

type ConversationID uuid.UUID

func NewConversationID() ConversationID {
	return ConversationID(uuid.New())
}

func (id ConversationID) String() string {
	return uuid.UUID(id).String()
}

type ConversationType string

const (
	ConversationTypeDirect      ConversationType = "direct"
	ConversationTypeGroup       ConversationType = "group"
	ConversationTypeTeamChannel ConversationType = "team_channel"
)

type Conversation struct {
	id            ConversationID
	conversationType ConversationType
	name          *string
	description   *string
	teamID        *uuid.UUID
	creatorID     uuid.UUID
	lastMessageID *string
	lastActivity  time.Time
	messageCount  int64
	maxSequence   int64
	createdAt     time.Time
	updatedAt     time.Time
	deletedAt     *time.Time
	participants  []ConversationParticipant
}

type ConversationParticipant struct {
	conversationID ConversationID
	userID         uuid.UUID
	joinedAt       time.Time
	lastReadSeq    int64
	role           ParticipantRole
}

type ParticipantRole string

const (
	ParticipantRoleAdmin    ParticipantRole = "admin"
	ParticipantRoleMember   ParticipantRole = "member"
	ParticipantRoleObserver ParticipantRole = "observer"
)

func NewDirectConversation(participant1, participant2 uuid.UUID) *Conversation {
	now := time.Now()
	id := NewConversationID()
	
	return &Conversation{
		id:               id,
		conversationType: ConversationTypeDirect,
		creatorID:        participant1,
		lastActivity:     now,
		messageCount:     0,
		maxSequence:      0,
		createdAt:        now,
		updatedAt:        now,
		participants: []ConversationParticipant{
			{
				conversationID: id,
				userID:         participant1,
				joinedAt:       now,
				lastReadSeq:    0,
				role:           ParticipantRoleMember,
			},
			{
				conversationID: id,
				userID:         participant2,
				joinedAt:       now,
				lastReadSeq:    0,
				role:           ParticipantRoleMember,
			},
		},
	}
}

func NewGroupConversation(name string, creatorID uuid.UUID, teamID *uuid.UUID) *Conversation {
	now := time.Now()
	id := NewConversationID()
	
	conv := &Conversation{
		id:               id,
		conversationType: ConversationTypeGroup,
		name:             &name,
		teamID:           teamID,
		creatorID:        creatorID,
		lastActivity:     now,
		messageCount:     0,
		maxSequence:      0,
		createdAt:        now,
		updatedAt:        now,
		participants: []ConversationParticipant{
			{
				conversationID: id,
				userID:         creatorID,
				joinedAt:       now,
				lastReadSeq:    0,
				role:           ParticipantRoleAdmin,
			},
		},
	}
	
	if teamID != nil {
		conv.conversationType = ConversationTypeTeamChannel
	}
	
	return conv
}

// Getters
func (c *Conversation) ID() ConversationID {
	return c.id
}

func (c *Conversation) Type() ConversationType {
	return c.conversationType
}

func (c *Conversation) Name() *string {
	return c.name
}

func (c *Conversation) Description() *string {
	return c.description
}

func (c *Conversation) TeamID() *uuid.UUID {
	return c.teamID
}

func (c *Conversation) CreatorID() uuid.UUID {
	return c.creatorID
}

func (c *Conversation) LastMessageID() *string {
	return c.lastMessageID
}

func (c *Conversation) LastActivity() time.Time {
	return c.lastActivity
}

func (c *Conversation) MessageCount() int64 {
	return c.messageCount
}

func (c *Conversation) MaxSequence() int64 {
	return c.maxSequence
}

func (c *Conversation) CreatedAt() time.Time {
	return c.createdAt
}

func (c *Conversation) UpdatedAt() time.Time {
	return c.updatedAt
}

func (c *Conversation) DeletedAt() *time.Time {
	return c.deletedAt
}

func (c *Conversation) Participants() []ConversationParticipant {
	return c.participants
}

// Business logic methods
func (c *Conversation) IsDeleted() bool {
	return c.deletedAt != nil
}

func (c *Conversation) IsDirect() bool {
	return c.conversationType == ConversationTypeDirect
}

func (c *Conversation) IsGroup() bool {
	return c.conversationType == ConversationTypeGroup
}

func (c *Conversation) IsTeamChannel() bool {
	return c.conversationType == ConversationTypeTeamChannel
}

func (c *Conversation) HasParticipant(userID uuid.UUID) bool {
	for _, p := range c.participants {
		if p.userID == userID {
			return true
		}
	}
	return false
}

func (c *Conversation) GetParticipant(userID uuid.UUID) (*ConversationParticipant, bool) {
	for i, p := range c.participants {
		if p.userID == userID {
			return &c.participants[i], true
		}
	}
	return nil, false
}

func (c *Conversation) IsParticipantAdmin(userID uuid.UUID) bool {
	if participant, exists := c.GetParticipant(userID); exists {
		return participant.role == ParticipantRoleAdmin
	}
	return false
}

func (c *Conversation) AddParticipant(userID uuid.UUID, role ParticipantRole) {
	if !c.HasParticipant(userID) {
		participant := ConversationParticipant{
			conversationID: c.id,
			userID:         userID,
			joinedAt:       time.Now(),
			lastReadSeq:    c.maxSequence,
			role:           role,
		}
		c.participants = append(c.participants, participant)
		c.updatedAt = time.Now()
	}
}

func (c *Conversation) RemoveParticipant(userID uuid.UUID) {
	for i, p := range c.participants {
		if p.userID == userID {
			c.participants = append(c.participants[:i], c.participants[i+1:]...)
			c.updatedAt = time.Now()
			break
		}
	}
}

func (c *Conversation) UpdateParticipantRole(userID uuid.UUID, role ParticipantRole) {
	for i, p := range c.participants {
		if p.userID == userID {
			c.participants[i].role = role
			c.updatedAt = time.Now()
			break
		}
	}
}

func (c *Conversation) UpdateLastReadSequence(userID uuid.UUID, sequence int64) {
	for i, p := range c.participants {
		if p.userID == userID {
			c.participants[i].lastReadSeq = sequence
			break
		}
	}
}

func (c *Conversation) UpdateLastMessage(messageID string, sequence int64) {
	c.lastMessageID = &messageID
	c.lastActivity = time.Now()
	c.messageCount++
	if sequence > c.maxSequence {
		c.maxSequence = sequence
	}
	c.updatedAt = time.Now()
}

func (c *Conversation) UpdateName(name string) {
	c.name = &name
	c.updatedAt = time.Now()
}

func (c *Conversation) UpdateDescription(description string) {
	c.description = &description
	c.updatedAt = time.Now()
}

func (c *Conversation) Delete() {
	now := time.Now()
	c.deletedAt = &now
	c.updatedAt = now
}

func (c *Conversation) GetUnreadCount(userID uuid.UUID) int64 {
	if participant, exists := c.GetParticipant(userID); exists {
		return c.maxSequence - participant.lastReadSeq
	}
	return 0
}

func (c *Conversation) GetParticipantIDs() []uuid.UUID {
	ids := make([]uuid.UUID, len(c.participants))
	for i, p := range c.participants {
		ids[i] = p.userID
	}
	return ids
}

// ReconstructConversation creates a Conversation entity from database data
func ReconstructConversation(
	id ConversationID,
	conversationType ConversationType,
	name, description *string,
	teamID *uuid.UUID,
	creatorID uuid.UUID,
	lastMessageID *string,
	lastActivity time.Time,
	messageCount, maxSequence int64,
	createdAt, updatedAt time.Time,
	deletedAt *time.Time,
	participants []ConversationParticipant,
) *Conversation {
	return &Conversation{
		id:               id,
		conversationType: conversationType,
		name:             name,
		description:      description,
		teamID:           teamID,
		creatorID:        creatorID,
		lastMessageID:    lastMessageID,
		lastActivity:     lastActivity,
		messageCount:     messageCount,
		maxSequence:      maxSequence,
		createdAt:        createdAt,
		updatedAt:        updatedAt,
		deletedAt:        deletedAt,
		participants:     participants,
	}
}

// ConversationParticipant getter methods
func (p *ConversationParticipant) ConversationID() ConversationID {
	return p.conversationID
}

func (p *ConversationParticipant) UserID() uuid.UUID {
	return p.userID
}

func (p *ConversationParticipant) JoinedAt() time.Time {
	return p.joinedAt
}

func (p *ConversationParticipant) LastReadSeq() int64 {
	return p.lastReadSeq
}

func (p *ConversationParticipant) Role() ParticipantRole {
	return p.role
}

// NewConversationParticipant creates a new conversation participant
func NewConversationParticipant(conversationID ConversationID, userID uuid.UUID, role ParticipantRole) ConversationParticipant {
	return ConversationParticipant{
		conversationID: conversationID,
		userID:         userID,
		joinedAt:       time.Now(),
		lastReadSeq:    0,
		role:           role,
	}
}