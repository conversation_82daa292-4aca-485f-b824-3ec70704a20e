package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
)

type MessageRepository interface {
	// Message CRUD operations
	StoreMessage(ctx context.Context, message *entities.Message) error
	GetMessage(ctx context.Context, messageID entities.MessageID) (*entities.Message, error)
	GetMessageBySequence(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64) (*entities.Message, error)
	UpdateMessage(ctx context.Context, message *entities.Message) error
	DeleteMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error
	
	// Message queries
	GetMessages(ctx context.Context, conversationID conversationentities.ConversationID, limit int, offset int64) ([]*entities.Message, error)
	GetMessagesAfterSequence(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64, limit int) ([]*entities.Message, error)
	GetMessagesBeforeSequence(ctx context.Context, conversationID conversationentities.ConversationID, sequence int64, limit int) ([]*entities.Message, error)
	GetMessageHistory(ctx context.Context, conversationID conversationentities.ConversationID, fromTime, toTime time.Time, limit int) ([]*entities.Message, error)
	
	// Search operations
	SearchMessages(ctx context.Context, conversationID conversationentities.ConversationID, query string, limit int, offset int) ([]*entities.Message, error)
	SearchMessagesGlobal(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Message, error)
	
	// Thread operations
	GetThreadMessages(ctx context.Context, threadID entities.MessageID, limit int, offset int) ([]*entities.Message, error)
	GetThreadRepliesCount(ctx context.Context, threadID entities.MessageID) (int64, error)
	
	// Batch operations
	StoreMessagesBatch(ctx context.Context, messages []*entities.Message) error
	GetMessagesBatch(ctx context.Context, messageIDs []entities.MessageID) ([]*entities.Message, error)
	
	// Document-based operations (OpenIM-inspired)
	StoreMessageDocument(ctx context.Context, document *entities.MessageDocument) error
	GetMessageDocument(ctx context.Context, conversationID conversationentities.ConversationID, shardIndex int64) (*entities.MessageDocument, error)
	UpdateMessageInDocument(ctx context.Context, conversationID conversationentities.ConversationID, message *entities.Message) error
	
	// Read status operations
	MarkMessageAsRead(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error
	MarkMessagesAsRead(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, upToSequence int64) error
	GetUnreadMessageCount(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID) (int64, error)
	
	// Analytics and metrics
	GetMessageStats(ctx context.Context, conversationID conversationentities.ConversationID, fromTime, toTime time.Time) (*MessageStats, error)
	GetUserMessageStats(ctx context.Context, userID uuid.UUID, fromTime, toTime time.Time) (*UserMessageStats, error)
}

type MessageStats struct {
	TotalMessages   int64
	UniqueUsers     int64
	MediaMessages   int64
	TextMessages    int64
	DeletedMessages int64
	EditedMessages  int64
	ThreadsStarted  int64
	Replies         int64
	PeakActivity    time.Time
	AvgResponseTime time.Duration
}

type UserMessageStats struct {
	MessagesSent     int64
	MessagesReceived int64
	ThreadsStarted   int64
	RepliesSent      int64
	MediaShared      int64
	MostActiveHour   int
	AvgMessageLength float64
	FirstMessage     *time.Time
	LastMessage      *time.Time
}