package repositories

import "errors"

var (
	ErrMessageNotFound      = errors.New("message not found")
	ErrConversationNotFound = errors.New("conversation not found")
	ErrInvalidSequence      = errors.New("invalid sequence number")
	ErrDuplicateMessage     = errors.New("duplicate message")
	ErrAccessDenied         = errors.New("access denied")
	ErrMessageTooLarge      = errors.New("message content too large")
	ErrInvalidMessageType   = errors.New("invalid message type")
	ErrThreadNotFound       = errors.New("thread not found")
	ErrReplyToNotFound      = errors.New("reply target message not found")
)