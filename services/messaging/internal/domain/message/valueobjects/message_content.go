package valueobjects

import (
	"errors"
	"strings"
)

type MessageContent struct {
	text        string
	attachments []MessageAttachment
	mentions    []string
	links       []string
}

type MessageAttachment struct {
	id       string
	name     string
	url      string
	mimeType string
	size     int64
	metadata map[string]interface{}
}

func NewTextMessageContent(text string) (MessageContent, error) {
	if strings.TrimSpace(text) == "" {
		return MessageContent{}, errors.New("message text cannot be empty")
	}
	
	return MessageContent{
		text:        text,
		attachments: make([]MessageAttachment, 0),
		mentions:    make([]string, 0),
		links:       make([]string, 0),
	}, nil
}

func NewAttachmentMessageContent(attachments []MessageAttachment, text string) (MessageContent, error) {
	if len(attachments) == 0 {
		return MessageContent{}, errors.New("at least one attachment is required")
	}
	
	return MessageContent{
		text:        text,
		attachments: attachments,
		mentions:    make([]string, 0),
		links:       make([]string, 0),
	}, nil
}

func NewSystemMessageContent(text string) MessageContent {
	return MessageContent{
		text:        text,
		attachments: make([]MessageAttachment, 0),
		mentions:    make([]string, 0),
		links:       make([]string, 0),
	}
}

// Getters
func (mc MessageContent) Text() string {
	return mc.text
}

func (mc MessageContent) Attachments() []MessageAttachment {
	return mc.attachments
}

func (mc MessageContent) Mentions() []string {
	return mc.mentions
}

func (mc MessageContent) Links() []string {
	return mc.links
}

func (mc MessageContent) HasText() bool {
	return strings.TrimSpace(mc.text) != ""
}

func (mc MessageContent) HasAttachments() bool {
	return len(mc.attachments) > 0
}

func (mc MessageContent) HasMentions() bool {
	return len(mc.mentions) > 0
}

func (mc MessageContent) HasLinks() bool {
	return len(mc.links) > 0
}

func (mc MessageContent) IsEmpty() bool {
	return !mc.HasText() && !mc.HasAttachments()
}

// Business logic methods
func (mc *MessageContent) AddMention(userID string) {
	for _, mention := range mc.mentions {
		if mention == userID {
			return // Already mentioned
		}
	}
	mc.mentions = append(mc.mentions, userID)
}

func (mc *MessageContent) RemoveMention(userID string) {
	for i, mention := range mc.mentions {
		if mention == userID {
			mc.mentions = append(mc.mentions[:i], mc.mentions[i+1:]...)
			break
		}
	}
}

func (mc *MessageContent) AddLink(url string) {
	for _, link := range mc.links {
		if link == url {
			return // Already added
		}
	}
	mc.links = append(mc.links, url)
}

func (mc *MessageContent) SetText(text string) {
	mc.text = text
}

func (mc MessageContent) GetPreview(maxLength int) string {
	if mc.HasText() {
		if len(mc.text) <= maxLength {
			return mc.text
		}
		return mc.text[:maxLength-3] + "..."
	}
	
	if mc.HasAttachments() {
		if len(mc.attachments) == 1 {
			return "📎 " + mc.attachments[0].name
		}
		return "📎 " + string(rune(len(mc.attachments))) + " attachments"
	}
	
	return ""
}

func (mc MessageContent) GetSearchableText() string {
	var parts []string
	
	if mc.HasText() {
		parts = append(parts, mc.text)
	}
	
	for _, attachment := range mc.attachments {
		parts = append(parts, attachment.name)
	}
	
	return strings.Join(parts, " ")
}

// NewMessageAttachment creates a new message attachment
func NewMessageAttachment(id, name, url, mimeType string, size int64) MessageAttachment {
	return MessageAttachment{
		id:       id,
		name:     name,
		url:      url,
		mimeType: mimeType,
		size:     size,
		metadata: make(map[string]interface{}),
	}
}

// Attachment getters
func (ma MessageAttachment) ID() string {
	return ma.id
}

func (ma MessageAttachment) Name() string {
	return ma.name
}

func (ma MessageAttachment) URL() string {
	return ma.url
}

func (ma MessageAttachment) MimeType() string {
	return ma.mimeType
}

func (ma MessageAttachment) Size() int64 {
	return ma.size
}

func (ma MessageAttachment) Metadata() map[string]interface{} {
	return ma.metadata
}

func (ma *MessageAttachment) SetMetadata(key string, value interface{}) {
	ma.metadata[key] = value
}

func (ma MessageAttachment) GetMetadata(key string) (interface{}, bool) {
	value, exists := ma.metadata[key]
	return value, exists
}

func (ma MessageAttachment) IsImage() bool {
	return strings.HasPrefix(ma.mimeType, "image/")
}

func (ma MessageAttachment) IsVideo() bool {
	return strings.HasPrefix(ma.mimeType, "video/")
}

func (ma MessageAttachment) IsAudio() bool {
	return strings.HasPrefix(ma.mimeType, "audio/")
}

func (ma MessageAttachment) IsDocument() bool {
	return !ma.IsImage() && !ma.IsVideo() && !ma.IsAudio()
}