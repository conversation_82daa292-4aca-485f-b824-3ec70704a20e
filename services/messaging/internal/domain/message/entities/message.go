package entities

import (
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
)

type MessageID uuid.UUID

func NewMessageID() MessageID {
	return MessageID(uuid.New())
}

func (id MessageID) String() string {
	return uuid.UUID(id).String()
}

type MessageType string

const (
	MessageTypeText     MessageType = "text"
	MessageTypeImage    MessageType = "image"
	MessageTypeFile     MessageType = "file"
	MessageTypeAudio    MessageType = "audio"
	MessageTypeVideo    MessageType = "video"
	MessageTypeLocation MessageType = "location"
	MessageTypeSystem   MessageType = "system"
)

type MessageStatus string

const (
	MessageStatusSent      MessageStatus = "sent"
	MessageStatusDelivered MessageStatus = "delivered"
	MessageStatusRead      MessageStatus = "read"
	MessageStatusFailed    MessageStatus = "failed"
)

type Message struct {
	id             MessageID
	conversationID conversationentities.ConversationID
	senderID       uuid.UUID
	content        valueobjects.MessageContent
	messageType    MessageType
	sequence       int64
	threadID       *MessageID
	replyToID      *MessageID
	status         MessageStatus
	timestamp      time.Time
	editedAt       *time.Time
	deletedAt      *time.Time
	metadata       map[string]interface{}
	readBy         []ReadStatus
}

type ReadStatus struct {
	UserID uuid.UUID `json:"user_id"`
	ReadAt time.Time `json:"read_at"`
}

type MessageDocument struct {
	conversationID conversationentities.ConversationID
	shardIndex     int64
	messages       []*MessageInfo
	createdAt      time.Time
	updatedAt      time.Time
}

// Getters for MessageDocument
func (md *MessageDocument) ConversationID() conversationentities.ConversationID {
	return md.conversationID
}

func (md *MessageDocument) ShardIndex() int64 {
	return md.shardIndex
}

func (md *MessageDocument) Messages() []*MessageInfo {
	return md.messages
}

func (md *MessageDocument) CreatedAt() time.Time {
	return md.createdAt
}

func (md *MessageDocument) UpdatedAt() time.Time {
	return md.updatedAt
}

type MessageInfo struct {
	message   *Message
	deletedBy []uuid.UUID
	isRead    bool
}

// Getters for MessageInfo
func (mi *MessageInfo) Message() *Message {
	return mi.message
}

func (mi *MessageInfo) DeletedBy() []uuid.UUID {
	return mi.deletedBy
}

func (mi *MessageInfo) IsRead() bool {
	return mi.isRead
}

func NewMessage(
	conversationID conversationentities.ConversationID,
	senderID uuid.UUID,
	content valueobjects.MessageContent,
	messageType MessageType,
) *Message {
	return &Message{
		id:             NewMessageID(),
		conversationID: conversationID,
		senderID:       senderID,
		content:        content,
		messageType:    messageType,
		status:         MessageStatusSent,
		timestamp:      time.Now(),
		metadata:       make(map[string]interface{}),
		readBy:         make([]ReadStatus, 0),
	}
}

func NewSystemMessage(
	conversationID conversationentities.ConversationID,
	content valueobjects.MessageContent,
) *Message {
	systemUserID := uuid.Nil // Use nil UUID for system messages
	return &Message{
		id:             NewMessageID(),
		conversationID: conversationID,
		senderID:       systemUserID,
		content:        content,
		messageType:    MessageTypeSystem,
		status:         MessageStatusSent,
		timestamp:      time.Now(),
		metadata:       make(map[string]interface{}),
		readBy:         make([]ReadStatus, 0),
	}
}

// Getters
func (m *Message) ID() MessageID {
	return m.id
}

func (m *Message) ConversationID() conversationentities.ConversationID {
	return m.conversationID
}

func (m *Message) SenderID() uuid.UUID {
	return m.senderID
}

func (m *Message) Content() valueobjects.MessageContent {
	return m.content
}

func (m *Message) MessageType() MessageType {
	return m.messageType
}

func (m *Message) Sequence() int64 {
	return m.sequence
}

func (m *Message) ThreadID() *MessageID {
	return m.threadID
}

func (m *Message) ReplyToID() *MessageID {
	return m.replyToID
}

func (m *Message) Status() MessageStatus {
	return m.status
}

func (m *Message) Timestamp() time.Time {
	return m.timestamp
}

func (m *Message) EditedAt() *time.Time {
	return m.editedAt
}

func (m *Message) DeletedAt() *time.Time {
	return m.deletedAt
}

func (m *Message) Metadata() map[string]interface{} {
	return m.metadata
}

func (m *Message) ReadBy() []ReadStatus {
	return m.readBy
}

// Business logic methods
func (m *Message) IsDeleted() bool {
	return m.deletedAt != nil
}

func (m *Message) IsEdited() bool {
	return m.editedAt != nil
}

func (m *Message) IsSystemMessage() bool {
	return m.messageType == MessageTypeSystem
}

func (m *Message) IsThreadReply() bool {
	return m.threadID != nil
}

func (m *Message) IsReply() bool {
	return m.replyToID != nil
}

func (m *Message) SetSequence(sequence int64) {
	m.sequence = sequence
}

func (m *Message) SetStatus(status MessageStatus) {
	m.status = status
}

func (m *Message) SetThreadID(threadID MessageID) {
	m.threadID = &threadID
}

func (m *Message) SetReplyToID(replyToID MessageID) {
	m.replyToID = &replyToID
}

func (m *Message) EditContent(content valueobjects.MessageContent) {
	m.content = content
	now := time.Now()
	m.editedAt = &now
}

func (m *Message) Delete() {
	now := time.Now()
	m.deletedAt = &now
}

func (m *Message) SetMetadata(key string, value interface{}) {
	m.metadata[key] = value
}

func (m *Message) GetMetadata(key string) (interface{}, bool) {
	value, exists := m.metadata[key]
	return value, exists
}

func (m *Message) MarkReadBy(userID uuid.UUID) {
	// Check if user already read the message
	for _, read := range m.readBy {
		if read.UserID == userID {
			return // Already marked as read
		}
	}
	
	m.readBy = append(m.readBy, ReadStatus{
		UserID: userID,
		ReadAt: time.Now(),
	})
}

func (m *Message) IsReadBy(userID uuid.UUID) bool {
	for _, read := range m.readBy {
		if read.UserID == userID {
			return true
		}
	}
	return false
}

func (m *Message) GetReadTime(userID uuid.UUID) *time.Time {
	for _, read := range m.readBy {
		if read.UserID == userID {
			return &read.ReadAt
		}
	}
	return nil
}

func (m *Message) IsSentBy(userID uuid.UUID) bool {
	return m.senderID == userID
}

// Document sharding methods (OpenIM-inspired)
func GetDocumentIndex(sequence int64, messagesPerDocument int) int64 {
	if sequence <= 0 {
		return 0
	}
	return (sequence - 1) / int64(messagesPerDocument)
}

func GetMessageIndex(sequence int64, messagesPerDocument int) int64 {
	if sequence <= 0 {
		return 0
	}
	return (sequence - 1) % int64(messagesPerDocument)
}

func GetDocumentID(conversationID conversationentities.ConversationID, sequence int64, messagesPerDocument int) string {
	docIndex := GetDocumentIndex(sequence, messagesPerDocument)
	return conversationID.String() + ":" + string(rune(docIndex))
}

// NewMessageDocument creates a new message document for sharding
func NewMessageDocument(conversationID conversationentities.ConversationID, shardIndex int64) *MessageDocument {
	return &MessageDocument{
		conversationID: conversationID,
		shardIndex:     shardIndex,
		messages:       make([]*MessageInfo, 0),
		createdAt:      time.Now(),
		updatedAt:      time.Now(),
	}
}

// AddMessage adds a message to the document
func (md *MessageDocument) AddMessage(message *Message) {
	messageInfo := &MessageInfo{
		message:   message,
		deletedBy: make([]uuid.UUID, 0),
		isRead:    false,
	}
	md.messages = append(md.messages, messageInfo)
	md.updatedAt = time.Now()
}

// GetMessage retrieves a message by sequence number
func (md *MessageDocument) GetMessage(sequence int64, messagesPerDocument int) *MessageInfo {
	index := GetMessageIndex(sequence, messagesPerDocument)
	if index >= 0 && index < int64(len(md.messages)) {
		return md.messages[index]
	}
	return nil
}

// ReconstructMessage creates a Message entity from database data
func ReconstructMessage(
	id MessageID,
	conversationID conversationentities.ConversationID,
	senderID uuid.UUID,
	content valueobjects.MessageContent,
	messageType MessageType,
	sequence int64,
	threadID, replyToID *MessageID,
	status MessageStatus,
	timestamp time.Time,
	editedAt, deletedAt *time.Time,
	metadata map[string]interface{},
	readBy []ReadStatus,
) *Message {
	return &Message{
		id:             id,
		conversationID: conversationID,
		senderID:       senderID,
		content:        content,
		messageType:    messageType,
		sequence:       sequence,
		threadID:       threadID,
		replyToID:      replyToID,
		status:         status,
		timestamp:      timestamp,
		editedAt:       editedAt,
		deletedAt:      deletedAt,
		metadata:       metadata,
		readBy:         readBy,
	}
}