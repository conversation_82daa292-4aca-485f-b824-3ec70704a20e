package services

import (
	"context"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
)

type MessageService interface {
	// Send message operations
	SendMessage(ctx context.Context, request *SendMessageRequest) (*entities.Message, error)
	SendSystemMessage(ctx context.Context, conversationID conversationentities.ConversationID, content valueobjects.MessageContent) (*entities.Message, error)
	
	// Message retrieval
	GetMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) (*entities.Message, error)
	GetMessages(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, limit int, offset int64) ([]*entities.Message, error)
	GetMessagesAfterSequence(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64, limit int) ([]*entities.Message, error)
	GetMessagesBeforeSequence(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64, limit int) ([]*entities.Message, error)
	
	// Message modifications
	EditMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID, content valueobjects.MessageContent) (*entities.Message, error)
	DeleteMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error
	
	// Thread operations
	ReplyToMessage(ctx context.Context, request *ReplyToMessageRequest) (*entities.Message, error)
	GetThreadMessages(ctx context.Context, threadID entities.MessageID, userID uuid.UUID, limit int, offset int) ([]*entities.Message, error)
	
	// Read status operations
	MarkMessageAsRead(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error
	MarkMessagesAsRead(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, upToSequence int64) error
	GetUnreadCount(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID) (int64, error)
	
	// Search operations
	SearchMessages(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Message, error)
	SearchMessagesGlobal(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Message, error)
}

type SendMessageRequest struct {
	ConversationID conversationentities.ConversationID
	SenderID       uuid.UUID
	Content        valueobjects.MessageContent
	MessageType    entities.MessageType
	ThreadID       *entities.MessageID
	ReplyToID      *entities.MessageID
}

type ReplyToMessageRequest struct {
	ConversationID conversationentities.ConversationID
	SenderID       uuid.UUID
	Content        valueobjects.MessageContent
	MessageType    entities.MessageType
	ReplyToID      entities.MessageID
	ThreadID       *entities.MessageID // If replying within a thread
}