package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	conversationrepositories "github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/persistence/redis"
)

type messageServiceImpl struct {
	messageRepo      repositories.MessageRepository
	conversationRepo conversationrepositories.ConversationRepository
	sequenceManager  *redis.SequenceManager
	eventPublisher   EventPublisher // Interface for publishing events
}

// EventPublisher interface for decoupling from WebSocket implementation
type EventPublisher interface {
	PublishMessageSent(ctx context.Context, message *entities.Message, conversationID conversationentities.ConversationID) error
	PublishMessageEdited(ctx context.Context, message *entities.Message) error
	PublishMessageDeleted(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error
	PublishReadStatusUpdated(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64) error
}

func NewMessageService(
	messageRepo repositories.MessageRepository,
	conversationRepo conversationrepositories.ConversationRepository,
	sequenceManager *redis.SequenceManager,
	eventPublisher EventPublisher,
) MessageService {
	return &messageServiceImpl{
		messageRepo:      messageRepo,
		conversationRepo: conversationRepo,
		sequenceManager:  sequenceManager,
		eventPublisher:   eventPublisher,
	}
}

func (s *messageServiceImpl) SendMessage(ctx context.Context, request *SendMessageRequest) (*entities.Message, error) {
	// Add timeout for the entire operation
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	
	// 1. Validate conversation access
	conversation, err := s.conversationRepo.GetByID(ctx, request.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}
	
	if !conversation.HasParticipant(request.SenderID) {
		return nil, repositories.ErrAccessDenied
	}
	
	// 2. Validate thread/reply references if provided
	if request.ThreadID != nil {
		if err := s.validateThreadMessage(ctx, *request.ThreadID, request.ConversationID); err != nil {
			return nil, err
		}
	}
	
	if request.ReplyToID != nil {
		if err := s.validateReplyToMessage(ctx, *request.ReplyToID, request.ConversationID); err != nil {
			return nil, err
		}
	}
	
	// 3. Create message entity
	message := entities.NewMessage(
		request.ConversationID,
		request.SenderID,
		request.Content,
		request.MessageType,
	)
	
	if request.ThreadID != nil {
		message.SetThreadID(*request.ThreadID)
	}
	
	if request.ReplyToID != nil {
		message.SetReplyToID(*request.ReplyToID)
	}
	
	// 4. Allocate sequence number
	sequence, err := s.sequenceManager.AllocateSequence(ctx, request.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to allocate sequence: %w", err)
	}
	
	message.SetSequence(sequence)
	
	// 5. Store message
	if err := s.messageRepo.StoreMessage(ctx, message); err != nil {
		return nil, fmt.Errorf("failed to store message: %w", err)
	}
	
	// 6. Update conversation last activity
	if err := s.conversationRepo.UpdateLastActivity(ctx, request.ConversationID, message.ID().String(), sequence); err != nil {
		// Log error but don't fail the message send
		fmt.Printf("Warning: failed to update conversation activity: %v\n", err)
	}
	
	// 7. Publish event for real-time delivery
	fmt.Printf("DEBUG: About to call PublishMessageSent for message %s\n", message.ID().String())
	if err := s.eventPublisher.PublishMessageSent(ctx, message, request.ConversationID); err != nil {
		// Log error but don't fail the message send
		fmt.Printf("Warning: failed to publish message event: %v\n", err)
	} else {
		fmt.Printf("DEBUG: Successfully called PublishMessageSent for message %s\n", message.ID().String())
	}
	
	return message, nil
}

func (s *messageServiceImpl) SendSystemMessage(ctx context.Context, conversationID conversationentities.ConversationID, content valueobjects.MessageContent) (*entities.Message, error) {
	// Create system message
	message := entities.NewSystemMessage(conversationID, content)
	
	// Allocate sequence number
	sequence, err := s.sequenceManager.AllocateSequence(ctx, conversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to allocate sequence for system message: %w", err)
	}
	
	message.SetSequence(sequence)
	
	// Store message
	if err := s.messageRepo.StoreMessage(ctx, message); err != nil {
		return nil, fmt.Errorf("failed to store system message: %w", err)
	}
	
	// Update conversation activity
	if err := s.conversationRepo.UpdateLastActivity(ctx, conversationID, message.ID().String(), sequence); err != nil {
		fmt.Printf("Warning: failed to update conversation activity for system message: %v\n", err)
	}
	
	// Publish event
	if err := s.eventPublisher.PublishMessageSent(ctx, message, conversationID); err != nil {
		fmt.Printf("Warning: failed to publish system message event: %v\n", err)
	}
	
	return message, nil
}

func (s *messageServiceImpl) GetMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) (*entities.Message, error) {
	// Get the message
	message, err := s.messageRepo.GetMessage(ctx, messageID)
	if err != nil {
		return nil, err
	}
	
	// Check if user has access to the conversation
	conversation, err := s.conversationRepo.GetByID(ctx, message.ConversationID())
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}
	
	if !conversation.HasParticipant(userID) {
		return nil, repositories.ErrAccessDenied
	}
	
	return message, nil
}

func (s *messageServiceImpl) GetMessages(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, limit int, offset int64) ([]*entities.Message, error) {
	// Check conversation access
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}
	
	if !conversation.HasParticipant(userID) {
		return nil, repositories.ErrAccessDenied
	}
	
	// Get messages
	messages, err := s.messageRepo.GetMessages(ctx, conversationID, limit, offset)
	if err != nil {
		return nil, err
	}
	
	// Filter out messages deleted by this user
	filteredMessages := make([]*entities.Message, 0, len(messages))
	for _, message := range messages {
		if !message.IsDeleted() {
			filteredMessages = append(filteredMessages, message)
		}
	}
	
	return filteredMessages, nil
}

func (s *messageServiceImpl) GetMessagesAfterSequence(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64, limit int) ([]*entities.Message, error) {
	// Check conversation access
	if err := s.validateConversationAccess(ctx, conversationID, userID); err != nil {
		return nil, err
	}
	
	return s.messageRepo.GetMessagesAfterSequence(ctx, conversationID, sequence, limit)
}

func (s *messageServiceImpl) GetMessagesBeforeSequence(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, sequence int64, limit int) ([]*entities.Message, error) {
	// Check conversation access
	if err := s.validateConversationAccess(ctx, conversationID, userID); err != nil {
		return nil, err
	}
	
	return s.messageRepo.GetMessagesBeforeSequence(ctx, conversationID, sequence, limit)
}

func (s *messageServiceImpl) EditMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID, content valueobjects.MessageContent) (*entities.Message, error) {
	// Get the original message
	message, err := s.messageRepo.GetMessage(ctx, messageID)
	if err != nil {
		return nil, err
	}
	
	// Check if user can edit (must be sender)
	if !message.IsSentBy(userID) {
		return nil, repositories.ErrAccessDenied
	}
	
	// Check if message is too old to edit (e.g., 15 minutes)
	if time.Since(message.Timestamp()) > 15*time.Minute {
		return nil, fmt.Errorf("message is too old to edit")
	}
	
	// Check if message is already deleted
	if message.IsDeleted() {
		return nil, fmt.Errorf("cannot edit deleted message")
	}
	
	// Update content
	message.EditContent(content)
	
	// Store updated message
	if err := s.messageRepo.UpdateMessage(ctx, message); err != nil {
		return nil, fmt.Errorf("failed to update message: %w", err)
	}
	
	// Publish edit event
	if err := s.eventPublisher.PublishMessageEdited(ctx, message); err != nil {
		fmt.Printf("Warning: failed to publish message edit event: %v\n", err)
	}
	
	return message, nil
}

func (s *messageServiceImpl) DeleteMessage(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error {
	// Get the message
	message, err := s.messageRepo.GetMessage(ctx, messageID)
	if err != nil {
		return err
	}
	
	// Check conversation access
	conversation, err := s.conversationRepo.GetByID(ctx, message.ConversationID())
	if err != nil {
		return fmt.Errorf("failed to get conversation: %w", err)
	}
	
	// Check if user can delete (sender or admin)
	canDelete := message.IsSentBy(userID) || conversation.IsParticipantAdmin(userID)
	if !canDelete {
		return repositories.ErrAccessDenied
	}
	
	// Perform soft delete
	if err := s.messageRepo.DeleteMessage(ctx, messageID, userID); err != nil {
		return fmt.Errorf("failed to delete message: %w", err)
	}
	
	// Publish delete event
	if err := s.eventPublisher.PublishMessageDeleted(ctx, messageID, userID); err != nil {
		fmt.Printf("Warning: failed to publish message delete event: %v\n", err)
	}
	
	return nil
}

func (s *messageServiceImpl) ReplyToMessage(ctx context.Context, request *ReplyToMessageRequest) (*entities.Message, error) {
	// Validate the message being replied to exists
	replyToMessage, err := s.messageRepo.GetMessage(ctx, request.ReplyToID)
	if err != nil {
		return nil, fmt.Errorf("reply target message not found: %w", err)
	}
	
	// Ensure the reply is in the same conversation
	if replyToMessage.ConversationID() != request.ConversationID {
		return nil, fmt.Errorf("cannot reply to message from different conversation")
	}
	
	// Convert to SendMessageRequest
	sendRequest := &SendMessageRequest{
		ConversationID: request.ConversationID,
		SenderID:       request.SenderID,
		Content:        request.Content,
		MessageType:    request.MessageType,
		ReplyToID:      &request.ReplyToID,
		ThreadID:       request.ThreadID,
	}
	
	// If no thread specified, create thread from the replied message
	if request.ThreadID == nil && !replyToMessage.IsThreadReply() {
		threadID := replyToMessage.ID()
		sendRequest.ThreadID = &threadID
	}
	
	return s.SendMessage(ctx, sendRequest)
}

func (s *messageServiceImpl) GetThreadMessages(ctx context.Context, threadID entities.MessageID, userID uuid.UUID, limit int, offset int) ([]*entities.Message, error) {
	// Get the thread root message to check conversation access
	rootMessage, err := s.messageRepo.GetMessage(ctx, threadID)
	if err != nil {
		return nil, fmt.Errorf("thread root message not found: %w", err)
	}
	
	// Check conversation access
	if err := s.validateConversationAccess(ctx, rootMessage.ConversationID(), userID); err != nil {
		return nil, err
	}
	
	return s.messageRepo.GetThreadMessages(ctx, threadID, limit, offset)
}

func (s *messageServiceImpl) MarkMessageAsRead(ctx context.Context, messageID entities.MessageID, userID uuid.UUID) error {
	// Get message to check conversation access
	message, err := s.messageRepo.GetMessage(ctx, messageID)
	if err != nil {
		return err
	}
	
	// Check conversation access
	if err := s.validateConversationAccess(ctx, message.ConversationID(), userID); err != nil {
		return err
	}
	
	// Mark as read
	if err := s.messageRepo.MarkMessageAsRead(ctx, messageID, userID); err != nil {
		return fmt.Errorf("failed to mark message as read: %w", err)
	}
	
	// Update user's last read sequence in conversation
	if err := s.conversationRepo.UpdateLastReadSequence(ctx, message.ConversationID(), userID, message.Sequence()); err != nil {
		fmt.Printf("Warning: failed to update last read sequence: %v\n", err)
	}
	
	// Publish read status event
	if err := s.eventPublisher.PublishReadStatusUpdated(ctx, message.ConversationID(), userID, message.Sequence()); err != nil {
		fmt.Printf("Warning: failed to publish read status event: %v\n", err)
	}
	
	return nil
}

func (s *messageServiceImpl) MarkMessagesAsRead(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, upToSequence int64) error {
	// Check conversation access
	if err := s.validateConversationAccess(ctx, conversationID, userID); err != nil {
		return err
	}
	
	// Mark messages as read
	if err := s.messageRepo.MarkMessagesAsRead(ctx, conversationID, userID, upToSequence); err != nil {
		return fmt.Errorf("failed to mark messages as read: %w", err)
	}
	
	// Update user's last read sequence
	if err := s.conversationRepo.UpdateLastReadSequence(ctx, conversationID, userID, upToSequence); err != nil {
		return fmt.Errorf("failed to update last read sequence: %w", err)
	}
	
	// Publish read status event
	if err := s.eventPublisher.PublishReadStatusUpdated(ctx, conversationID, userID, upToSequence); err != nil {
		fmt.Printf("Warning: failed to publish read status event: %v\n", err)
	}
	
	return nil
}

func (s *messageServiceImpl) GetUnreadCount(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID) (int64, error) {
	// Check conversation access
	if err := s.validateConversationAccess(ctx, conversationID, userID); err != nil {
		return 0, err
	}
	
	return s.messageRepo.GetUnreadMessageCount(ctx, conversationID, userID)
}

func (s *messageServiceImpl) SearchMessages(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Message, error) {
	fmt.Printf("DEBUG: Service SearchMessages called with query '%s', conversationID %s, userID %s\n", query, conversationID.String(), userID.String())
	
	// Check conversation access
	if err := s.validateConversationAccess(ctx, conversationID, userID); err != nil {
		fmt.Printf("DEBUG: Conversation access denied: %v\n", err)
		return nil, err
	}
	
	// Sanitize search query
	query = strings.TrimSpace(query)
	if len(query) < 2 {
		return nil, fmt.Errorf("search query must be at least 2 characters")
	}
	
	fmt.Printf("DEBUG: Calling repository SearchMessages\n")
	return s.messageRepo.SearchMessages(ctx, conversationID, query, limit, offset)
}

func (s *messageServiceImpl) SearchMessagesGlobal(ctx context.Context, userID uuid.UUID, query string, limit int, offset int) ([]*entities.Message, error) {
	// Sanitize search query
	query = strings.TrimSpace(query)
	if len(query) < 2 {
		return nil, fmt.Errorf("search query must be at least 2 characters")
	}
	
	return s.messageRepo.SearchMessagesGlobal(ctx, userID, query, limit, offset)
}

// Helper methods
func (s *messageServiceImpl) validateConversationAccess(ctx context.Context, conversationID conversationentities.ConversationID, userID uuid.UUID) error {
	conversation, err := s.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return fmt.Errorf("failed to get conversation: %w", err)
	}
	
	if !conversation.HasParticipant(userID) {
		return repositories.ErrAccessDenied
	}
	
	return nil
}

func (s *messageServiceImpl) validateThreadMessage(ctx context.Context, threadID entities.MessageID, conversationID conversationentities.ConversationID) error {
	threadMessage, err := s.messageRepo.GetMessage(ctx, threadID)
	if err != nil {
		return repositories.ErrThreadNotFound
	}
	
	if threadMessage.ConversationID() != conversationID {
		return fmt.Errorf("thread message is not in the same conversation")
	}
	
	return nil
}

func (s *messageServiceImpl) validateReplyToMessage(ctx context.Context, replyToID entities.MessageID, conversationID conversationentities.ConversationID) error {
	replyToMessage, err := s.messageRepo.GetMessage(ctx, replyToID)
	if err != nil {
		return repositories.ErrReplyToNotFound
	}
	
	if replyToMessage.ConversationID() != conversationID {
		return fmt.Errorf("reply target message is not in the same conversation")
	}
	
	return nil
}