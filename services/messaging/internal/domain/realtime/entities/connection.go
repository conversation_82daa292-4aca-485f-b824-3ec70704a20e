package entities

import (
	"context"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

type ConnectionID uuid.UUID

func NewConnectionID() ConnectionID {
	return ConnectionID(uuid.New())
}

func (id ConnectionID) String() string {
	return uuid.UUID(id).String()
}

type PlatformType int32

const (
	PlatformWeb     PlatformType = 1
	PlatformIOS     PlatformType = 2
	PlatformAndroid PlatformType = 3
	PlatformDesktop PlatformType = 4
)

func (p PlatformType) String() string {
	switch p {
	case PlatformWeb:
		return "web"
	case PlatformIOS:
		return "ios"
	case PlatformAndroid:
		return "android"
	case PlatformDesktop:
		return "desktop"
	default:
		return "unknown"
	}
}

type ConnectionStatus string

const (
	ConnectionStatusConnecting ConnectionStatus = "connecting"
	ConnectionStatusConnected  ConnectionStatus = "connected"
	ConnectionStatusBackground ConnectionStatus = "background"
	ConnectionStatusDisconnected ConnectionStatus = "disconnected"
)

type Connection struct {
	id            ConnectionID
	userID        uuid.UUID
	deviceID      string
	platformType  PlatformType
	status        ConnectionStatus
	conn          *websocket.Conn
	send<PERSON>han      chan []byte
	ctx           context.Context
	cancel        context.CancelFunc
	isBackground  bool
	isCompressed  bool
	lastHeartbeat atomic.Value // time.Time
	lastActivity  atomic.Value // time.Time
	connectedAt   time.Time
	subscriptions map[string]bool
	metadata      map[string]interface{}
	mu            sync.RWMutex
	closed        atomic.Bool
}

func NewConnection(
	userID uuid.UUID,
	deviceID string,
	platformType PlatformType,
	conn *websocket.Conn,
	bufferSize int,
) *Connection {
	ctx, cancel := context.WithCancel(context.Background())
	connectionID := NewConnectionID()
	now := time.Now()
	
	connection := &Connection{
		id:            connectionID,
		userID:        userID,
		deviceID:      deviceID,
		platformType:  platformType,
		status:        ConnectionStatusConnecting,
		conn:          conn,
		sendChan:      make(chan []byte, bufferSize),
		ctx:           ctx,
		cancel:        cancel,
		isBackground:  false,
		isCompressed:  false,
		connectedAt:   now,
		subscriptions: make(map[string]bool),
		metadata:      make(map[string]interface{}),
	}
	
	connection.lastHeartbeat.Store(now)
	connection.lastActivity.Store(now)
	
	return connection
}

// Getters
func (c *Connection) ID() ConnectionID {
	return c.id
}

func (c *Connection) UserID() uuid.UUID {
	return c.userID
}

func (c *Connection) DeviceID() string {
	return c.deviceID
}

func (c *Connection) PlatformType() PlatformType {
	return c.platformType
}

func (c *Connection) Status() ConnectionStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.status
}

func (c *Connection) IsBackground() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.isBackground
}

func (c *Connection) IsCompressed() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.isCompressed
}

func (c *Connection) ConnectedAt() time.Time {
	return c.connectedAt
}

func (c *Connection) LastHeartbeat() time.Time {
	if hb := c.lastHeartbeat.Load(); hb != nil {
		return hb.(time.Time)
	}
	return c.connectedAt
}

func (c *Connection) LastActivity() time.Time {
	if activity := c.lastActivity.Load(); activity != nil {
		return activity.(time.Time)
	}
	return c.connectedAt
}

func (c *Connection) IsClosed() bool {
	return c.closed.Load()
}

func (c *Connection) Context() context.Context {
	return c.ctx
}

func (c *Connection) SendChannel() chan []byte {
	return c.sendChan
}

func (c *Connection) WebSocketConn() *websocket.Conn {
	return c.conn
}

// Status management
func (c *Connection) SetStatus(status ConnectionStatus) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.status = status
}

func (c *Connection) SetBackground(background bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.isBackground = background
	
	if background {
		c.status = ConnectionStatusBackground
	} else {
		c.status = ConnectionStatusConnected
	}
}

func (c *Connection) SetCompressed(compressed bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.isCompressed = compressed
}

func (c *Connection) UpdateHeartbeat() {
	c.lastHeartbeat.Store(time.Now())
}

func (c *Connection) UpdateActivity() {
	c.lastActivity.Store(time.Now())
}

// Subscription management
func (c *Connection) Subscribe(topic string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.subscriptions[topic] = true
}

func (c *Connection) Unsubscribe(topic string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.subscriptions, topic)
}

func (c *Connection) IsSubscribed(topic string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.subscriptions[topic]
}

func (c *Connection) GetSubscriptions() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	topics := make([]string, 0, len(c.subscriptions))
	for topic := range c.subscriptions {
		topics = append(topics, topic)
	}
	return topics
}

// Metadata management
func (c *Connection) SetMetadata(key string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.metadata[key] = value
}

func (c *Connection) GetMetadata(key string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	value, exists := c.metadata[key]
	return value, exists
}

func (c *Connection) GetAllMetadata() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	metadata := make(map[string]interface{})
	for k, v := range c.metadata {
		metadata[k] = v
	}
	return metadata
}

// Connection lifecycle
func (c *Connection) Close() error {
	if c.closed.CompareAndSwap(false, true) {
		c.cancel()
		close(c.sendChan)
		c.SetStatus(ConnectionStatusDisconnected)
		return c.conn.Close()
	}
	return nil
}

// Message sending
func (c *Connection) SendMessage(message []byte) error {
	if c.IsClosed() {
		return ErrConnectionClosed
	}
	
	select {
	case c.sendChan <- message:
		c.UpdateActivity()
		return nil
	case <-c.ctx.Done():
		return ErrConnectionClosed
	default:
		return ErrSendBufferFull
	}
}

// Health checks
func (c *Connection) IsHealthy(heartbeatTimeout time.Duration) bool {
	if c.IsClosed() {
		return false
	}
	
	lastHB := c.LastHeartbeat()
	return time.Since(lastHB) <= heartbeatTimeout
}

func (c *Connection) IsIdle(idleTimeout time.Duration) bool {
	lastActivity := c.LastActivity()
	return time.Since(lastActivity) > idleTimeout
}

func (c *Connection) GetConnectionDuration() time.Duration {
	return time.Since(c.connectedAt)
}

// Platform-specific methods
func (c *Connection) ShouldReceivePush() bool {
	// Background iOS connections should not receive real-time pushes
	return !(c.isBackground && c.platformType == PlatformIOS)
}

func (c *Connection) SupportsCompression() bool {
	// Only web and desktop platforms support compression
	return c.platformType == PlatformWeb || c.platformType == PlatformDesktop
}

func (c *Connection) RequiresHeartbeat() bool {
	// Mobile platforms don't need server-initiated heartbeats
	return c.platformType == PlatformWeb || c.platformType == PlatformDesktop
}

// Connection errors
var (
	ErrConnectionClosed  = NewConnectionError("connection closed")
	ErrSendBufferFull    = NewConnectionError("send buffer full")
	ErrInvalidMessage    = NewConnectionError("invalid message")
	ErrAuthenticationFailed = NewConnectionError("authentication failed")
)

type ConnectionError struct {
	message string
}

func NewConnectionError(message string) *ConnectionError {
	return &ConnectionError{message: message}
}

func (e *ConnectionError) Error() string {
	return e.message
}