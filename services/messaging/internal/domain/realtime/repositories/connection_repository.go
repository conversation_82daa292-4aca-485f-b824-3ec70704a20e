package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/domain/realtime/entities"
)

// ConnectionRepository manages WebSocket connection persistence and state
type ConnectionRepository interface {
	// Connection CRUD operations
	StoreConnection(ctx context.Context, connection *entities.Connection) error
	GetConnection(ctx context.Context, connectionID entities.ConnectionID) (*entities.Connection, error)
	UpdateConnection(ctx context.Context, connection *entities.Connection) error
	RemoveConnection(ctx context.Context, connectionID entities.ConnectionID) error
	
	// User connection management
	GetUserConnections(ctx context.Context, userID uuid.UUID) ([]*entities.Connection, error)
	GetUserConnectionsByPlatform(ctx context.Context, userID uuid.UUID, platformType entities.PlatformType) ([]*entities.Connection, error)
	RemoveUserConnections(ctx context.Context, userID uuid.UUID) error
	RemoveUserConnectionsByDevice(ctx context.Context, userID uuid.UUID, deviceID string) error
	
	// Connection state queries
	IsUserOnline(ctx context.Context, userID uuid.UUID) (bool, error)
	GetOnlineUsers(ctx context.Context, userIDs []uuid.UUID) ([]uuid.UUID, error)
	GetConnectionCount(ctx context.Context) (int64, error)
	GetUserConnectionCount(ctx context.Context, userID uuid.UUID) (int, error)
	
	// Connection metadata and status
	UpdateConnectionStatus(ctx context.Context, connectionID entities.ConnectionID, status entities.ConnectionStatus) error
	UpdateConnectionHeartbeat(ctx context.Context, connectionID entities.ConnectionID) error
	SetConnectionMetadata(ctx context.Context, connectionID entities.ConnectionID, key string, value interface{}) error
	GetConnectionMetadata(ctx context.Context, connectionID entities.ConnectionID, key string) (interface{}, error)
	
	// Subscription management
	AddSubscription(ctx context.Context, connectionID entities.ConnectionID, topic string) error
	RemoveSubscription(ctx context.Context, connectionID entities.ConnectionID, topic string) error
	GetSubscriptions(ctx context.Context, connectionID entities.ConnectionID) ([]string, error)
	GetSubscribersForTopic(ctx context.Context, topic string) ([]entities.ConnectionID, error)
	
	// Health and cleanup operations
	GetStaleConnections(ctx context.Context, timeout time.Duration) ([]*entities.Connection, error)
	CleanupStaleConnections(ctx context.Context, timeout time.Duration) (int64, error)
	GetConnectionsByLastActivity(ctx context.Context, since time.Time) ([]*entities.Connection, error)
	
	// Batch operations
	StoreConnectionsBatch(ctx context.Context, connections []*entities.Connection) error
	RemoveConnectionsBatch(ctx context.Context, connectionIDs []entities.ConnectionID) error
	
	// User presence management
	SetUserOnlineStatus(ctx context.Context, userID uuid.UUID, isOnline bool, lastSeen time.Time) error
	GetUserOnlineStatus(ctx context.Context, userID uuid.UUID) (*UserOnlineStatus, error)
	GetUserLastSeen(ctx context.Context, userID uuid.UUID) (*time.Time, error)
	
	// Platform-specific operations
	GetConnectionsByPlatform(ctx context.Context, platformType entities.PlatformType) ([]*entities.Connection, error)
	GetUserDevices(ctx context.Context, userID uuid.UUID) ([]UserDevice, error)
	
	// Connection statistics
	GetConnectionStats(ctx context.Context) (*ConnectionStats, error)
	GetUserConnectionHistory(ctx context.Context, userID uuid.UUID, limit int) ([]*ConnectionHistoryEntry, error)
}

// UserOnlineStatus represents a user's online presence
type UserOnlineStatus struct {
	UserID      uuid.UUID                  `json:"user_id"`
	IsOnline    bool                       `json:"is_online"`
	LastSeen    time.Time                  `json:"last_seen"`
	Connections []entities.ConnectionID    `json:"connections"`
	Platforms   []entities.PlatformType    `json:"platforms"`
	DeviceCount int                        `json:"device_count"`
	UpdatedAt   time.Time                  `json:"updated_at"`
}

// UserDevice represents a user's device information
type UserDevice struct {
	UserID       uuid.UUID               `json:"user_id"`
	DeviceID     string                  `json:"device_id"`
	PlatformType entities.PlatformType   `json:"platform_type"`
	LastSeen     time.Time               `json:"last_seen"`
	IsOnline     bool                    `json:"is_online"`
	ConnectionID *entities.ConnectionID  `json:"connection_id,omitempty"`
}

// ConnectionStats represents connection statistics
type ConnectionStats struct {
	TotalConnections       int64               `json:"total_connections"`
	OnlineUsers           int64               `json:"online_users"`
	ConnectionsByPlatform map[string]int64    `json:"connections_by_platform"`
	ConnectionsByStatus   map[string]int64    `json:"connections_by_status"`
	AverageConnectionTime time.Duration       `json:"average_connection_time"`
	PeakConnections       int64               `json:"peak_connections"`
	ConnectionRate        float64             `json:"connection_rate"` // connections per minute
	DisconnectionRate     float64             `json:"disconnection_rate"`
	LastUpdated           time.Time           `json:"last_updated"`
}

// ConnectionHistoryEntry represents a connection history record
type ConnectionHistoryEntry struct {
	ConnectionID   entities.ConnectionID   `json:"connection_id"`
	UserID         uuid.UUID               `json:"user_id"`
	DeviceID       string                  `json:"device_id"`
	PlatformType   entities.PlatformType   `json:"platform_type"`
	ConnectedAt    time.Time               `json:"connected_at"`
	DisconnectedAt *time.Time              `json:"disconnected_at,omitempty"`
	Duration       *time.Duration          `json:"duration,omitempty"`
	Reason         string                  `json:"reason,omitempty"`
}

// Repository errors
var (
	ErrConnectionNotFound     = NewConnectionRepositoryError("connection not found")
	ErrConnectionExists       = NewConnectionRepositoryError("connection already exists")
	ErrUserNotOnline         = NewConnectionRepositoryError("user is not online")
	ErrInvalidConnectionData = NewConnectionRepositoryError("invalid connection data")
	ErrConnectionTimeout     = NewConnectionRepositoryError("connection operation timeout")
)

// ConnectionRepositoryError represents repository-specific errors
type ConnectionRepositoryError struct {
	message string
}

func NewConnectionRepositoryError(message string) *ConnectionRepositoryError {
	return &ConnectionRepositoryError{message: message}
}

func (e *ConnectionRepositoryError) Error() string {
	return e.message
}