package queries

import (
	"context"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
)

type SearchMessagesQuery struct {
	UserID          uuid.UUID                            `json:"user_id" validate:"required"`
	SearchText      string                               `json:"search_text" validate:"required,min=2,max=256"`
	ConversationIDs []conversationentities.ConversationID `json:"conversation_ids,omitempty"`
	MessageTypes    []string                             `json:"message_types,omitempty"`
	SenderIDs       []uuid.UUID                          `json:"sender_ids,omitempty"`
	FromDate        *time.Time                           `json:"from_date,omitempty"`
	ToDate          *time.Time                           `json:"to_date,omitempty"`
	Limit           int                                  `json:"limit" validate:"min=1,max=50"`
	Offset          int                                  `json:"offset" validate:"min=0"`
}

type SearchMessagesResult struct {
	Messages    []*MessageSearchResult `json:"messages"`
	TotalCount  int64                  `json:"total_count"`
	Has<PERSON>ore     bool                   `json:"has_more"`
	NextOffset  *int                   `json:"next_offset,omitempty"`
	SearchedAt  time.Time              `json:"searched_at"`
	SearchQuery string                 `json:"search_query"`
}

type MessageSearchResult struct {
	Message        *messageentities.Message `json:"message"`
	ConversationID string                   `json:"conversation_id"`
	MatchScore     float64                  `json:"match_score"`
	MatchedText    []string                 `json:"matched_text"` // Highlighted text snippets
}

type SearchMessagesHandler interface {
	Handle(ctx context.Context, query *SearchMessagesQuery) (*SearchMessagesResult, error)
}

func NewSearchMessagesQuery(userID uuid.UUID, searchText string) *SearchMessagesQuery {
	return &SearchMessagesQuery{
		UserID:     userID,
		SearchText: searchText,
		Limit:      20, // Default limit for search
		Offset:     0,
	}
}

func (q *SearchMessagesQuery) WithConversations(conversationIDs []conversationentities.ConversationID) *SearchMessagesQuery {
	q.ConversationIDs = conversationIDs
	return q
}

func (q *SearchMessagesQuery) WithMessageTypes(messageTypes []string) *SearchMessagesQuery {
	q.MessageTypes = messageTypes
	return q
}

func (q *SearchMessagesQuery) WithSenders(senderIDs []uuid.UUID) *SearchMessagesQuery {
	q.SenderIDs = senderIDs
	return q
}

func (q *SearchMessagesQuery) WithDateRange(fromDate, toDate *time.Time) *SearchMessagesQuery {
	q.FromDate = fromDate
	q.ToDate = toDate
	return q
}

func (q *SearchMessagesQuery) WithPagination(limit, offset int) *SearchMessagesQuery {
	q.Limit = limit
	q.Offset = offset
	return q
}

func (q *SearchMessagesQuery) Validate() error {
	if q.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	if len(q.SearchText) < 2 {
		return ErrSearchTextTooShort
	}
	if len(q.SearchText) > 256 {
		return ErrSearchTextTooLong
	}
	if q.Limit < 1 || q.Limit > 50 {
		return ErrInvalidSearchLimit
	}
	if q.Offset < 0 {
		return ErrInvalidOffset
	}
	if q.FromDate != nil && q.ToDate != nil && q.FromDate.After(*q.ToDate) {
		return ErrInvalidDateRange
	}
	return nil
}

var (
	ErrSearchTextTooShort   = NewQueryError("search text must be at least 2 characters")
	ErrSearchTextTooLong    = NewQueryError("search text cannot exceed 256 characters")
	ErrInvalidSearchLimit   = NewQueryError("search limit must be between 1 and 50")
	ErrInvalidDateRange     = NewQueryError("from_date cannot be after to_date")
)