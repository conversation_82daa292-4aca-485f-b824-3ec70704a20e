package queries

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messagerepositories "github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
)

type GetMessagesQuery struct {
	ConversationID conversationentities.ConversationID `json:"conversation_id" validate:"required"`
	UserID         uuid.UUID                           `json:"user_id" validate:"required"`
	Limit          int                                 `json:"limit" validate:"min=1,max=100"`
	Offset         int64                               `json:"offset" validate:"min=0"`
	BeforeSequence *int64                              `json:"before_sequence,omitempty"`
	AfterSequence  *int64                              `json:"after_sequence,omitempty"`
}

type GetMessagesResult struct {
	Messages    []*messageentities.Message `json:"messages"`
	TotalCount  int                        `json:"total_count"`
	Has<PERSON><PERSON>     bool                       `json:"has_more"`
	NextOffset  *int64                     `json:"next_offset,omitempty"`
}

type GetMessagesHandler struct {
	messageRepo      messagerepositories.MessageRepository
	conversationRepo repositories.ConversationRepository
}

func NewGetMessagesHandler(
	messageRepo messagerepositories.MessageRepository,
	conversationRepo repositories.ConversationRepository,
) *GetMessagesHandler {
	return &GetMessagesHandler{
		messageRepo:      messageRepo,
		conversationRepo: conversationRepo,
	}
}

func (h *GetMessagesHandler) Handle(ctx context.Context, query *GetMessagesQuery) (*GetMessagesResult, error) {
	// Validate query
	if err := h.validateQuery(query); err != nil {
		return nil, fmt.Errorf("invalid query: %w", err)
	}

	// Get conversation to verify access
	conversation, err := h.conversationRepo.GetByID(ctx, query.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	// Verify user is a participant
	if !conversation.HasParticipant(query.UserID) {
		return nil, fmt.Errorf("user is not a participant in conversation")
	}

	var messages []*messageentities.Message

	// Execute query based on parameters
	if query.BeforeSequence != nil {
		// Get messages before a specific sequence (pagination backwards)
		messages, err = h.messageRepo.GetMessagesBeforeSequence(
			ctx,
			query.ConversationID,
			*query.BeforeSequence,
			query.Limit,
		)
	} else if query.AfterSequence != nil {
		// Get messages after a specific sequence (pagination forwards)
		messages, err = h.messageRepo.GetMessagesAfterSequence(
			ctx,
			query.ConversationID,
			*query.AfterSequence,
			query.Limit,
		)
	} else {
		// Get recent messages with offset
		messages, err = h.messageRepo.GetMessages(
			ctx,
			query.ConversationID,
			query.Limit,
			query.Offset,
		)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get messages: %w", err)
	}

	// Filter out messages deleted by this user
	filteredMessages := make([]*messageentities.Message, 0, len(messages))
	for _, msg := range messages {
		// Skip messages that are deleted and this user deleted them
		if msg.IsDeleted() {
			// For now, show deleted messages as placeholder
			// In a more sophisticated implementation, you'd check if this specific user deleted it
			filteredMessages = append(filteredMessages, msg)
		} else {
			filteredMessages = append(filteredMessages, msg)
		}
	}

	// Determine if there are more messages
	hasMore := len(filteredMessages) == query.Limit
	var nextOffset *int64
	if hasMore && len(filteredMessages) > 0 {
		// Calculate next offset based on the last message
		if query.BeforeSequence != nil || query.AfterSequence != nil {
			// For sequence-based pagination, use the last message's sequence
			lastSequence := filteredMessages[len(filteredMessages)-1].Sequence()
			nextOffset = &lastSequence
		} else {
			// For offset-based pagination
			next := query.Offset + int64(len(filteredMessages))
			nextOffset = &next
		}
	}

	return &GetMessagesResult{
		Messages:   filteredMessages,
		TotalCount: len(filteredMessages),
		HasMore:    hasMore,
		NextOffset: nextOffset,
	}, nil
}

func (h *GetMessagesHandler) validateQuery(query *GetMessagesQuery) error {
	if query.ConversationID == "" {
		return fmt.Errorf("conversation ID is required")
	}
	if query.UserID == uuid.Nil {
		return fmt.Errorf("user ID is required")
	}
	if query.Limit <= 0 {
		query.Limit = 20 // Default limit
	}
	if query.Limit > 100 {
		query.Limit = 100 // Max limit
	}
	if query.Offset < 0 {
		query.Offset = 0
	}
	return nil
}