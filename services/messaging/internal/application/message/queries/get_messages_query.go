package queries

import (
	"context"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
)

type GetMessagesQuery struct {
	ConversationID conversationentities.ConversationID `json:"conversation_id" validate:"required"`
	UserID         uuid.UUID                           `json:"user_id" validate:"required"`
	Limit          int                                  `json:"limit" validate:"min=1,max=100"`
	Offset         int                                  `json:"offset" validate:"min=0"`
	BeforeSeq      *int64                               `json:"before_seq,omitempty"`
	AfterSeq       *int64                               `json:"after_seq,omitempty"`
	SortOrder      SortOrder                            `json:"sort_order"`
}

type SortOrder string

const (
	SortOrderAsc  SortOrder = "asc"
	SortOrderDesc SortOrder = "desc"
)

type GetMessagesResult struct {
	Messages    []*messageentities.Message `json:"messages"`
	TotalCount  int64                      `json:"total_count"`
	HasMore     bool                       `json:"has_more"`
	NextOffset  *int                       `json:"next_offset,omitempty"`
	RetrievedAt time.Time                  `json:"retrieved_at"`
}

type GetMessagesHandler interface {
	Handle(ctx context.Context, query *GetMessagesQuery) (*GetMessagesResult, error)
}

func NewGetMessagesQuery(conversationID conversationentities.ConversationID, userID uuid.UUID) *GetMessagesQuery {
	return &GetMessagesQuery{
		ConversationID: conversationID,
		UserID:         userID,
		Limit:          50, // Default limit
		Offset:         0,
		SortOrder:      SortOrderDesc, // Most recent first
	}
}

func (q *GetMessagesQuery) WithPagination(limit, offset int) *GetMessagesQuery {
	q.Limit = limit
	q.Offset = offset
	return q
}

func (q *GetMessagesQuery) WithSequenceRange(beforeSeq, afterSeq *int64) *GetMessagesQuery {
	q.BeforeSeq = beforeSeq
	q.AfterSeq = afterSeq
	return q
}

func (q *GetMessagesQuery) WithSortOrder(order SortOrder) *GetMessagesQuery {
	q.SortOrder = order
	return q
}

func (q *GetMessagesQuery) Validate() error {
	if q.ConversationID == "" {
		return ErrInvalidConversationID
	}
	if q.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	if q.Limit < 1 || q.Limit > 100 {
		return ErrInvalidLimit
	}
	if q.Offset < 0 {
		return ErrInvalidOffset
	}
	if q.SortOrder != SortOrderAsc && q.SortOrder != SortOrderDesc && q.SortOrder != "" {
		return ErrInvalidSortOrder
	}
	if q.SortOrder == "" {
		q.SortOrder = SortOrderDesc
	}
	return nil
}

var (
	ErrInvalidConversationID = NewQueryError("invalid conversation ID")
	ErrInvalidUserID        = NewQueryError("invalid user ID")
	ErrInvalidLimit         = NewQueryError("limit must be between 1 and 100")
	ErrInvalidOffset        = NewQueryError("offset must be non-negative")
	ErrInvalidSortOrder     = NewQueryError("sort order must be 'asc' or 'desc'")
)

type QueryError struct {
	message string
}

func NewQueryError(message string) *QueryError {
	return &QueryError{message: message}
}

func (e *QueryError) Error() string {
	return e.message
}