package queries

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messagerepositories "github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
)

type SearchMessagesQuery struct {
	ConversationID *conversationentities.ConversationID `json:"conversation_id,omitempty"` // Optional: search in specific conversation
	UserID         uuid.UUID                            `json:"user_id" validate:"required"`
	Query          string                               `json:"query" validate:"required,min=1"`
	Limit          int                                  `json:"limit" validate:"min=1,max=50"`
	Offset         int                                  `json:"offset" validate:"min=0"`
}

type SearchMessagesResult struct {
	Messages   []*messageentities.Message `json:"messages"`
	TotalCount int                        `json:"total_count"`
	Has<PERSON><PERSON>    bool                       `json:"has_more"`
	NextOffset *int                       `json:"next_offset,omitempty"`
}

type SearchMessagesHandler struct {
	messageRepo      messagerepositories.MessageRepository
	conversationRepo repositories.ConversationRepository
}

func NewSearchMessagesHandler(
	messageRepo messagerepositories.MessageRepository,
	conversationRepo repositories.ConversationRepository,
) *SearchMessagesHandler {
	return &SearchMessagesHandler{
		messageRepo:      messageRepo,
		conversationRepo: conversationRepo,
	}
}

func (h *SearchMessagesHandler) Handle(ctx context.Context, query *SearchMessagesQuery) (*SearchMessagesResult, error) {
	// Validate query
	if err := h.validateQuery(query); err != nil {
		return nil, fmt.Errorf("invalid query: %w", err)
	}

	var messages []*messageentities.Message
	var err error

	if query.ConversationID != nil {
		// Search within a specific conversation
		// Verify user access to the conversation
		conversation, err := h.conversationRepo.GetByID(ctx, *query.ConversationID)
		if err != nil {
			return nil, fmt.Errorf("failed to get conversation: %w", err)
		}

		if !conversation.HasParticipant(query.UserID) {
			return nil, fmt.Errorf("user is not a participant in conversation")
		}

		// Search messages in the specific conversation
		messages, err = h.messageRepo.SearchMessages(
			ctx,
			*query.ConversationID,
			query.Query,
			query.Limit,
			query.Offset,
		)
	} else {
		// Global search across all conversations user has access to
		// This requires a more complex implementation that checks user's conversations
		messages, err = h.messageRepo.SearchMessagesGlobal(
			ctx,
			query.UserID,
			query.Query,
			query.Limit,
			query.Offset,
		)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to search messages: %w", err)
	}

	// Filter results based on user access (additional security layer)
	filteredMessages := make([]*messageentities.Message, 0, len(messages))
	for _, msg := range messages {
		// Verify user has access to this message's conversation
		if query.ConversationID == nil {
			// For global search, verify access to each conversation
			conversation, err := h.conversationRepo.GetByID(ctx, msg.ConversationID())
			if err != nil {
				continue // Skip messages from inaccessible conversations
			}
			if !conversation.HasParticipant(query.UserID) {
				continue // Skip messages from conversations user isn't part of
			}
		}

		// Skip deleted messages (or handle based on business rules)
		if !msg.IsDeleted() {
			filteredMessages = append(filteredMessages, msg)
		}
	}

	// Determine if there are more results
	hasMore := len(filteredMessages) == query.Limit
	var nextOffset *int
	if hasMore {
		next := query.Offset + len(filteredMessages)
		nextOffset = &next
	}

	return &SearchMessagesResult{
		Messages:   filteredMessages,
		TotalCount: len(filteredMessages),
		HasMore:    hasMore,
		NextOffset: nextOffset,
	}, nil
}

func (h *SearchMessagesHandler) validateQuery(query *SearchMessagesQuery) error {
	if query.UserID == uuid.Nil {
		return fmt.Errorf("user ID is required")
	}

	// Clean and validate search query
	query.Query = strings.TrimSpace(query.Query)
	if query.Query == "" {
		return fmt.Errorf("search query cannot be empty")
	}
	if len(query.Query) < 1 {
		return fmt.Errorf("search query must be at least 1 character")
	}
	if len(query.Query) > 500 {
		return fmt.Errorf("search query too long")
	}

	// Set defaults
	if query.Limit <= 0 {
		query.Limit = 20
	}
	if query.Limit > 50 {
		query.Limit = 50 // Lower limit for search to prevent performance issues
	}
	if query.Offset < 0 {
		query.Offset = 0
	}

	return nil
}