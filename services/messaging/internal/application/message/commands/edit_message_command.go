package commands

import (
	"context"
	"time"

	"github.com/google/uuid"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
)

type EditMessageCommand struct {
	MessageID messageentities.MessageID `json:"message_id" validate:"required"`
	UserID    uuid.UUID                 `json:"user_id" validate:"required"`
	NewText   string                    `json:"new_text" validate:"required,max=4096"`
	EditedAt  time.Time                 `json:"edited_at"`
}

type EditMessageResult struct {
	Message  *messageentities.Message `json:"message"`
	EditedAt time.Time                `json:"edited_at"`
}

type EditMessageHandler interface {
	Handle(ctx context.Context, cmd *EditMessageCommand) (*EditMessageResult, error)
}

func NewEditMessageCommand(messageID messageentities.MessageID, userID uuid.UUID, newText string) *EditMessageCommand {
	return &EditMessageCommand{
		MessageID: messageID,
		UserID:    userID,
		NewText:   newText,
		EditedAt:  time.Now(),
	}
}

func (cmd *EditMessageCommand) Validate() error {
	if cmd.MessageID == "" {
		return ErrInvalidMessageID
	}
	if cmd.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	if cmd.NewText == "" {
		return ErrEmptyNewText
	}
	if len(cmd.NewText) > 4096 {
		return ErrTextTooLong
	}
	return nil
}

var (
	ErrInvalidMessageID = NewCommandError("invalid message ID")
	ErrInvalidUserID   = NewCommandError("invalid user ID")
	ErrEmptyNewText    = NewCommandError("new text cannot be empty")
	ErrTextTooLong     = NewCommandError("text exceeds maximum length")
)