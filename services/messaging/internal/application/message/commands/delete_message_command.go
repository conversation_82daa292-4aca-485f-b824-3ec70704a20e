package commands

import (
	"context"
	"time"

	"github.com/google/uuid"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
)

type DeleteMessageCommand struct {
	MessageID  messageentities.MessageID `json:"message_id" validate:"required"`
	UserID     uuid.UUID                 `json:"user_id" validate:"required"`
	DeleteType DeleteType                `json:"delete_type" validate:"required"`
	DeletedAt  time.Time                 `json:"deleted_at"`
}

type DeleteType string

const (
	DeleteTypeForMe       DeleteType = "for_me"       // Delete only for the user
	DeleteTypeForEveryone DeleteType = "for_everyone" // Delete for all participants
)

type DeleteMessageResult struct {
	MessageID  messageentities.MessageID `json:"message_id"`
	DeleteType DeleteType                `json:"delete_type"`
	DeletedAt  time.Time                 `json:"deleted_at"`
	Success    bool                      `json:"success"`
}

type DeleteMessageHandler interface {
	Handle(ctx context.Context, cmd *DeleteMessageCommand) (*DeleteMessageResult, error)
}

func NewDeleteMessageCommand(messageID messageentities.MessageID, userID uuid.UUID, deleteType DeleteType) *DeleteMessageCommand {
	return &DeleteMessageCommand{
		MessageID:  messageID,
		UserID:     userID,
		DeleteType: deleteType,
		DeletedAt:  time.Now(),
	}
}

func (cmd *DeleteMessageCommand) Validate() error {
	if cmd.MessageID == "" {
		return ErrInvalidMessageID
	}
	if cmd.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	if cmd.DeleteType != DeleteTypeForMe && cmd.DeleteType != DeleteTypeForEveryone {
		return ErrInvalidDeleteType
	}
	return nil
}

func (dt DeleteType) IsValid() bool {
	return dt == DeleteTypeForMe || dt == DeleteTypeForEveryone
}

var (
	ErrInvalidDeleteType = NewCommandError("invalid delete type")
)