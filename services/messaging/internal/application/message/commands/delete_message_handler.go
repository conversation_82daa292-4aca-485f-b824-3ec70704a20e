package commands

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messagerepositories "github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/events"
)

type DeleteMessageCommand struct {
	MessageID      messageentities.MessageID          `json:"message_id" validate:"required"`
	ConversationID conversationentities.ConversationID `json:"conversation_id" validate:"required"`
	DeleterID      uuid.UUID                           `json:"deleter_id" validate:"required"`
}

type DeleteMessageResult struct {
	MessageID messageentities.MessageID `json:"message_id"`
	DeletedAt time.Time                 `json:"deleted_at"`
}

type DeleteMessageHandler struct {
	messageRepo        messagerepositories.MessageRepository
	conversationRepo   repositories.ConversationRepository
	eventPublisher     events.EventPublisher
}

func NewDeleteMessageHandler(
	messageRepo messagerepositories.MessageRepository,
	conversationRepo repositories.ConversationRepository,
	eventPublisher events.EventPublisher,
) *DeleteMessageHandler {
	return &DeleteMessageHandler{
		messageRepo:      messageRepo,
		conversationRepo: conversationRepo,
		eventPublisher:   eventPublisher,
	}
}

func (h *DeleteMessageHandler) Handle(ctx context.Context, cmd *DeleteMessageCommand) (*DeleteMessageResult, error) {
	// Get the message
	message, err := h.messageRepo.GetMessage(ctx, cmd.MessageID)
	if err != nil {
		return nil, fmt.Errorf("failed to get message: %w", err)
	}

	// Verify the message belongs to the specified conversation
	if message.ConversationID() != cmd.ConversationID {
		return nil, fmt.Errorf("message does not belong to specified conversation")
	}

	// Check if message is already deleted
	if message.IsDeleted() {
		return nil, fmt.Errorf("message is already deleted")
	}

	// Get conversation to verify access and permissions
	conversation, err := h.conversationRepo.GetByID(ctx, cmd.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	// Verify deleter is a participant
	if !conversation.HasParticipant(cmd.DeleterID) {
		return nil, fmt.Errorf("deleter is not a participant in conversation")
	}

	// Check permissions: either the message sender or conversation admin can delete
	canDelete := false
	if message.SenderID() == cmd.DeleterID {
		// Message sender can delete their own message
		canDelete = true
	} else if conversation.IsParticipantAdmin(cmd.DeleterID) {
		// Conversation admin can delete any message
		canDelete = true
	}

	if !canDelete {
		return nil, fmt.Errorf("insufficient permissions to delete message")
	}

	// Delete the message (soft delete)
	if err := h.messageRepo.DeleteMessage(ctx, cmd.MessageID, cmd.DeleterID); err != nil {
		return nil, fmt.Errorf("failed to delete message: %w", err)
	}

	// Get updated message to get deletion timestamp
	updatedMessage, err := h.messageRepo.GetMessage(ctx, cmd.MessageID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated message: %w", err)
	}

	// Publish message deleted event
	if err := h.eventPublisher.PublishMessageDeleted(ctx, updatedMessage, conversation.GetParticipantIDs()); err != nil {
		// Log error but don't fail the delete
		fmt.Printf("Warning: failed to publish message deleted event: %v\n", err)
	}

	return &DeleteMessageResult{
		MessageID: cmd.MessageID,
		DeletedAt: *updatedMessage.DeletedAt(),
	}, nil
}