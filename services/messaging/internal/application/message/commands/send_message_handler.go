package commands

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messagerepositories "github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/events"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/persistence/redis"
)

type SendMessageHandler struct {
	messageRepo        messagerepositories.MessageRepository
	conversationRepo   repositories.ConversationRepository
	sequenceManager    *redis.SequenceManager
	eventPublisher     events.EventPublisher
}

func NewSendMessageHandler(
	messageRepo messagerepositories.MessageRepository,
	conversationRepo repositories.ConversationRepository,
	sequenceManager *redis.SequenceManager,
	eventPublisher events.EventPublisher,
) SendMessageHandler {
	return &SendMessageHandler{
		messageRepo:      messageRepo,
		conversationRepo: conversationRepo,
		sequenceManager:  sequenceManager,
		eventPublisher:   eventPublisher,
	}
}

func (h *SendMessageHandler) Handle(ctx context.Context, cmd *SendMessageCommand) (*SendMessageResult, error) {
	// Validate command
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("invalid command: %w", err)
	}

	// Get conversation to verify access and participants
	conversation, err := h.conversationRepo.GetByID(ctx, cmd.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	// Verify sender is a participant
	if !conversation.HasParticipant(cmd.SenderID) {
		return nil, fmt.Errorf("sender is not a participant in conversation")
	}

	// Convert command content to value object
	content, err := cmd.ToValueObject()
	if err != nil {
		return nil, fmt.Errorf("failed to convert content: %w", err)
	}

	// Create message entity
	message := messageentities.NewMessage(
		cmd.ConversationID,
		cmd.SenderID,
		*content,
		messageentities.MessageType(cmd.MessageType),
	)

	// Set optional fields
	if cmd.ReplyToID != nil {
		message.SetReplyToID(*cmd.ReplyToID)
	}
	if cmd.ThreadID != nil {
		message.SetThreadID(*cmd.ThreadID)
	}
	if cmd.Metadata != nil {
		for key, value := range cmd.Metadata {
			message.SetMetadata(key, value)
		}
	}

	// Allocate sequence number
	sequence, err := h.sequenceManager.AllocateSequence(ctx, cmd.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to allocate sequence: %w", err)
	}
	message.SetSequence(sequence)

	// Store message
	if err := h.messageRepo.StoreMessage(ctx, message); err != nil {
		return nil, fmt.Errorf("failed to store message: %w", err)
	}

	// Update conversation with last message
	conversation.UpdateLastMessage(message.ID().String(), sequence)
	if err := h.conversationRepo.Update(ctx, conversation); err != nil {
		// Log error but don't fail the message send
		fmt.Printf("Warning: failed to update conversation last message: %v\n", err)
	}

	// Publish message sent event for real-time delivery
	if err := h.eventPublisher.PublishMessageSent(ctx, message, conversation.GetParticipantIDs()); err != nil {
		// Log error but don't fail the message send
		fmt.Printf("Warning: failed to publish message sent event: %v\n", err)
	}

	return &SendMessageResult{
		Message:   message,
		CreatedAt: message.Timestamp(),
	}, nil
}