package commands

import (
	"context"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
)

type SendMessageCommand struct {
	ConversationID conversationentities.ConversationID `json:"conversation_id" validate:"required"`
	SenderID       uuid.UUID                           `json:"sender_id" validate:"required"`
	Content        SendMessageContent                  `json:"content" validate:"required"`
	MessageType    string                              `json:"message_type" validate:"required,oneof=text image file audio video location"`
	ReplyToID      *messageentities.MessageID          `json:"reply_to_id,omitempty"`
	ThreadID       *messageentities.MessageID          `json:"thread_id,omitempty"`
	Metadata       map[string]interface{}              `json:"metadata,omitempty"`
}

type SendMessageContent struct {
	Text        string              `json:"text,omitempty"`
	Attachments []MessageAttachment `json:"attachments,omitempty"`
	Mentions    []uuid.UUID         `json:"mentions,omitempty"`
}

type MessageAttachment struct {
	ID       string `json:"id" validate:"required"`
	Type     string `json:"type" validate:"required,oneof=image file audio video document"`
	Name     string `json:"name" validate:"required"`
	Size     int64  `json:"size" validate:"min=0"`
	MimeType string `json:"mime_type"`
	URL      string `json:"url"`
}

type SendMessageResult struct {
	Message   *messageentities.Message `json:"message"`
	CreatedAt time.Time                `json:"created_at"`
}

type SendMessageHandler interface {
	Handle(ctx context.Context, cmd *SendMessageCommand) (*SendMessageResult, error)
}

func (cmd *SendMessageCommand) ToValueObject() (*valueobjects.MessageContent, error) {
	var attachments []valueobjects.MessageAttachment
	for _, att := range cmd.Content.Attachments {
		attachments = append(attachments, valueobjects.MessageAttachment{
			ID:       att.ID,
			Type:     att.Type,
			Name:     att.Name,
			Size:     att.Size,
			MimeType: att.MimeType,
			URL:      att.URL,
		})
	}

	var mentions []string
	for _, mention := range cmd.Content.Mentions {
		mentions = append(mentions, mention.String())
	}

	var replyToID, threadID string
	if cmd.ReplyToID != nil {
		replyToID = cmd.ReplyToID.String()
	}
	if cmd.ThreadID != nil {
		threadID = cmd.ThreadID.String()
	}

	return valueobjects.NewMessageContent(
		cmd.Content.Text,
		attachments,
		mentions,
		replyToID,
		threadID,
	)
}

func (cmd *SendMessageCommand) Validate() error {
	if cmd.ConversationID == "" {
		return ErrInvalidConversationID
	}
	if cmd.SenderID == uuid.Nil {
		return ErrInvalidSenderID
	}
	if cmd.MessageType == "" {
		return ErrInvalidMessageType
	}
	if cmd.Content.Text == "" && len(cmd.Content.Attachments) == 0 {
		return ErrEmptyMessageContent
	}
	return nil
}

var (
	ErrInvalidConversationID = NewCommandError("invalid conversation ID")
	ErrInvalidSenderID      = NewCommandError("invalid sender ID")
	ErrInvalidMessageType   = NewCommandError("invalid message type")
	ErrEmptyMessageContent  = NewCommandError("message content cannot be empty")
)

type CommandError struct {
	message string
}

func NewCommandError(message string) *CommandError {
	return &CommandError{message: message}
}

func (e *CommandError) Error() string {
	return e.message
}