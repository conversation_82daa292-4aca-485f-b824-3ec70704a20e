package commands

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	messageentities "github.com/swork-team/platform/services/messaging/internal/domain/message/entities"
	messagerepositories "github.com/swork-team/platform/services/messaging/internal/domain/message/repositories"
	"github.com/swork-team/platform/services/messaging/internal/domain/message/valueobjects"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/events"
)

type EditMessageCommand struct {
	MessageID      messageentities.MessageID          `json:"message_id" validate:"required"`
	ConversationID conversationentities.ConversationID `json:"conversation_id" validate:"required"`
	EditorID       uuid.UUID                           `json:"editor_id" validate:"required"`
	NewContent     SendMessageContent                  `json:"new_content" validate:"required"`
}

type EditMessageResult struct {
	Message   *messageentities.Message `json:"message"`
	EditedAt  time.Time                `json:"edited_at"`
}

type EditMessageHandler struct {
	messageRepo        messagerepositories.MessageRepository
	conversationRepo   repositories.ConversationRepository
	eventPublisher     events.EventPublisher
}

func NewEditMessageHandler(
	messageRepo messagerepositories.MessageRepository,
	conversationRepo repositories.ConversationRepository,
	eventPublisher events.EventPublisher,
) *EditMessageHandler {
	return &EditMessageHandler{
		messageRepo:      messageRepo,
		conversationRepo: conversationRepo,
		eventPublisher:   eventPublisher,
	}
}

func (h *EditMessageHandler) Handle(ctx context.Context, cmd *EditMessageCommand) (*EditMessageResult, error) {
	// Get the original message
	message, err := h.messageRepo.GetMessage(ctx, cmd.MessageID)
	if err != nil {
		return nil, fmt.Errorf("failed to get message: %w", err)
	}

	// Verify the message belongs to the specified conversation
	if message.ConversationID() != cmd.ConversationID {
		return nil, fmt.Errorf("message does not belong to specified conversation")
	}

	// Verify the editor is the original sender (only sender can edit their own messages)
	if message.SenderID() != cmd.EditorID {
		return nil, fmt.Errorf("only the message sender can edit the message")
	}

	// Check if message is already deleted
	if message.IsDeleted() {
		return nil, fmt.Errorf("cannot edit deleted message")
	}

	// Get conversation to verify access
	conversation, err := h.conversationRepo.GetByID(ctx, cmd.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	// Verify editor is still a participant
	if !conversation.HasParticipant(cmd.EditorID) {
		return nil, fmt.Errorf("editor is not a participant in conversation")
	}

	// Convert new content to value object
	content, err := h.convertToValueObject(cmd.NewContent)
	if err != nil {
		return nil, fmt.Errorf("failed to convert content: %w", err)
	}

	// Edit the message
	message.EditContent(*content)

	// Update message in repository
	if err := h.messageRepo.UpdateMessage(ctx, message); err != nil {
		return nil, fmt.Errorf("failed to update message: %w", err)
	}

	// Publish message edited event
	if err := h.eventPublisher.PublishMessageEdited(ctx, message, conversation.GetParticipantIDs()); err != nil {
		// Log error but don't fail the edit
		fmt.Printf("Warning: failed to publish message edited event: %v\n", err)
	}

	return &EditMessageResult{
		Message:  message,
		EditedAt: *message.EditedAt(),
	}, nil
}

func (h *EditMessageHandler) convertToValueObject(content SendMessageContent) (*valueobjects.MessageContent, error) {
	var attachments []valueobjects.MessageAttachment
	for _, att := range content.Attachments {
		attachments = append(attachments, valueobjects.MessageAttachment{
			ID:       att.ID,
			Type:     att.Type,
			Name:     att.Name,
			Size:     att.Size,
			MimeType: att.MimeType,
			URL:      att.URL,
		})
	}

	var mentions []string
	for _, mention := range content.Mentions {
		mentions = append(mentions, mention.String())
	}

	return valueobjects.NewMessageContent(
		content.Text,
		attachments,
		mentions,
		"", // replyToID not used in edit
		"", // threadID not used in edit
	)
}