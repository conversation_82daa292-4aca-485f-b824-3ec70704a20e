package commands

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
	"github.com/swork-team/platform/services/messaging/internal/infrastructure/events"
)

type CreateConversationCommand struct {
	CreatorID    uuid.UUID     `json:"creator_id" validate:"required"`
	Type         string        `json:"type" validate:"required,oneof=direct group team_channel"`
	Name         *string       `json:"name,omitempty"`
	Description  *string       `json:"description,omitempty"`
	TeamID       *uuid.UUID    `json:"team_id,omitempty"`
	Participants []uuid.UUID   `json:"participants" validate:"min=1"`
}

type CreateConversationResult struct {
	Conversation *entities.Conversation `json:"conversation"`
	CreatedAt    time.Time              `json:"created_at"`
}

type CreateConversationHandler struct {
	conversationRepo repositories.ConversationRepository
	eventPublisher   events.EventPublisher
}

func NewCreateConversationHandler(
	conversationRepo repositories.ConversationRepository,
	eventPublisher events.EventPublisher,
) *CreateConversationHandler {
	return &CreateConversationHandler{
		conversationRepo: conversationRepo,
		eventPublisher:   eventPublisher,
	}
}

func (h *CreateConversationHandler) Handle(ctx context.Context, cmd *CreateConversationCommand) (*CreateConversationResult, error) {
	// Validate command
	if err := h.validateCommand(cmd); err != nil {
		return nil, fmt.Errorf("invalid command: %w", err)
	}

	var conversation *entities.Conversation

	switch cmd.Type {
	case "direct":
		// Direct conversation must have exactly 2 participants
		if len(cmd.Participants) != 2 {
			return nil, fmt.Errorf("direct conversation must have exactly 2 participants")
		}

		// Check if direct conversation already exists between these users
		existing, err := h.conversationRepo.GetDirectConversation(ctx, cmd.Participants[0], cmd.Participants[1])
		if err == nil && existing != nil {
			return nil, fmt.Errorf("direct conversation already exists between these users")
		}

		conversation = entities.NewDirectConversation(cmd.Participants[0], cmd.Participants[1])

	case "group", "team_channel":
		// Group and team channel conversations
		if cmd.Name == nil || *cmd.Name == "" {
			return nil, fmt.Errorf("group and team channel conversations must have a name")
		}

		conversation = entities.NewGroupConversation(*cmd.Name, cmd.CreatorID, cmd.TeamID)

		// Add additional participants
		for _, participantID := range cmd.Participants {
			if participantID != cmd.CreatorID {
				conversation.AddParticipant(participantID, entities.ParticipantRoleMember)
			}
		}

		// Set description if provided
		if cmd.Description != nil && *cmd.Description != "" {
			conversation.UpdateDescription(*cmd.Description)
		}

	default:
		return nil, fmt.Errorf("invalid conversation type: %s", cmd.Type)
	}

	// Store conversation
	if err := h.conversationRepo.Create(ctx, conversation); err != nil {
		return nil, fmt.Errorf("failed to create conversation: %w", err)
	}

	// Publish conversation created event
	if err := h.eventPublisher.PublishConversationCreated(ctx, conversation); err != nil {
		// Log error but don't fail the creation
		fmt.Printf("Warning: failed to publish conversation created event: %v\n", err)
	}

	return &CreateConversationResult{
		Conversation: conversation,
		CreatedAt:    conversation.CreatedAt(),
	}, nil
}

func (h *CreateConversationHandler) validateCommand(cmd *CreateConversationCommand) error {
	if cmd.CreatorID == uuid.Nil {
		return fmt.Errorf("creator ID is required")
	}

	if len(cmd.Participants) == 0 {
		return fmt.Errorf("at least one participant is required")
	}

	// Ensure creator is in participants list
	creatorFound := false
	for _, participantID := range cmd.Participants {
		if participantID == cmd.CreatorID {
			creatorFound = true
			break
		}
	}
	if !creatorFound {
		cmd.Participants = append(cmd.Participants, cmd.CreatorID)
	}

	// Validate team channel requirements
	if cmd.Type == "team_channel" {
		if cmd.TeamID == nil {
			return fmt.Errorf("team ID is required for team channel conversations")
		}
	}

	return nil
}