package commands

import (
	"context"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
)

type CreateConversationCommand struct {
	Type         conversationentities.ConversationType `json:"type" validate:"required"`
	Name         *string                                `json:"name,omitempty"`
	Description  *string                                `json:"description,omitempty"`
	CreatorID    uuid.UUID                              `json:"creator_id" validate:"required"`
	TeamID       *uuid.UUID                             `json:"team_id,omitempty"`
	Participants []ConversationParticipant              `json:"participants" validate:"required,min=1"`
	IsPrivate    bool                                   `json:"is_private"`
	Settings     map[string]interface{}                 `json:"settings,omitempty"`
}

type ConversationParticipant struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
	Role   string    `json:"role" validate:"required,oneof=admin member observer"`
}

type CreateConversationResult struct {
	Conversation *conversationentities.Conversation `json:"conversation"`
	CreatedAt    time.Time                          `json:"created_at"`
}

type CreateConversationHandler interface {
	Handle(ctx context.Context, cmd *CreateConversationCommand) (*CreateConversationResult, error)
}

func NewDirectConversationCommand(creatorID, participantID uuid.UUID) *CreateConversationCommand {
	return &CreateConversationCommand{
		Type:      conversationentities.ConversationTypeDirect,
		CreatorID: creatorID,
		Participants: []ConversationParticipant{
			{UserID: creatorID, Role: "member"},
			{UserID: participantID, Role: "member"},
		},
		IsPrivate: true,
	}
}

func NewGroupConversationCommand(creatorID uuid.UUID, name string, participantIDs []uuid.UUID) *CreateConversationCommand {
	participants := []ConversationParticipant{
		{UserID: creatorID, Role: "admin"},
	}
	
	for _, participantID := range participantIDs {
		if participantID != creatorID {
			participants = append(participants, ConversationParticipant{
				UserID: participantID,
				Role:   "member",
			})
		}
	}

	return &CreateConversationCommand{
		Type:         conversationentities.ConversationTypeGroup,
		Name:         &name,
		CreatorID:    creatorID,
		Participants: participants,
		IsPrivate:    false,
	}
}

func NewTeamChannelCommand(creatorID, teamID uuid.UUID, name string, isPrivate bool) *CreateConversationCommand {
	return &CreateConversationCommand{
		Type:      conversationentities.ConversationTypeTeamChannel,
		Name:      &name,
		CreatorID: creatorID,
		TeamID:    &teamID,
		Participants: []ConversationParticipant{
			{UserID: creatorID, Role: "admin"},
		},
		IsPrivate: isPrivate,
	}
}

func (cmd *CreateConversationCommand) Validate() error {
	if cmd.CreatorID == uuid.Nil {
		return ErrInvalidCreatorID
	}
	
	if !cmd.Type.IsValid() {
		return ErrInvalidConversationType
	}

	if len(cmd.Participants) == 0 {
		return ErrNoParticipants
	}

	// Validate based on conversation type
	switch cmd.Type {
	case conversationentities.ConversationTypeDirect:
		if len(cmd.Participants) != 2 {
			return ErrDirectConversationMustHaveTwoParticipants
		}
	case conversationentities.ConversationTypeGroup:
		if cmd.Name == nil || *cmd.Name == "" {
			return ErrGroupConversationMustHaveName
		}
		if len(cmd.Participants) < 2 {
			return ErrGroupConversationMustHaveMultipleParticipants
		}
	case conversationentities.ConversationTypeTeamChannel:
		if cmd.TeamID == nil {
			return ErrTeamChannelMustHaveTeamID
		}
		if cmd.Name == nil || *cmd.Name == "" {
			return ErrTeamChannelMustHaveName
		}
	}

	// Validate participants
	creatorFound := false
	userIDs := make(map[uuid.UUID]bool)
	
	for _, participant := range cmd.Participants {
		if participant.UserID == uuid.Nil {
			return ErrInvalidParticipantID
		}
		if userIDs[participant.UserID] {
			return ErrDuplicateParticipant
		}
		userIDs[participant.UserID] = true
		
		if participant.UserID == cmd.CreatorID {
			creatorFound = true
		}
		
		if !isValidRole(participant.Role) {
			return ErrInvalidParticipantRole
		}
	}

	if !creatorFound {
		return ErrCreatorMustBeParticipant
	}

	return nil
}

func isValidRole(role string) bool {
	return role == "admin" || role == "member" || role == "observer"
}

var (
	ErrInvalidCreatorID                            = NewCommandError("invalid creator ID")
	ErrInvalidConversationType                     = NewCommandError("invalid conversation type")
	ErrNoParticipants                             = NewCommandError("conversation must have participants")
	ErrDirectConversationMustHaveTwoParticipants   = NewCommandError("direct conversation must have exactly two participants")
	ErrGroupConversationMustHaveName              = NewCommandError("group conversation must have a name")
	ErrGroupConversationMustHaveMultipleParticipants = NewCommandError("group conversation must have multiple participants")
	ErrTeamChannelMustHaveTeamID                  = NewCommandError("team channel must have team ID")
	ErrTeamChannelMustHaveName                    = NewCommandError("team channel must have a name")
	ErrInvalidParticipantID                       = NewCommandError("invalid participant ID")
	ErrDuplicateParticipant                       = NewCommandError("duplicate participant")
	ErrInvalidParticipantRole                     = NewCommandError("invalid participant role")
	ErrCreatorMustBeParticipant                   = NewCommandError("creator must be a participant")
)

type CommandError struct {
	message string
}

func NewCommandError(message string) *CommandError {
	return &CommandError{message: message}
}

func (e *CommandError) Error() string {
	return e.message
}