package queries

import (
	"context"
	"time"

	"github.com/google/uuid"
	conversationentities "github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
)

type GetConversationQuery struct {
	ConversationID conversationentities.ConversationID `json:"conversation_id" validate:"required"`
	UserID         uuid.UUID                           `json:"user_id" validate:"required"`
	IncludeStats   bool                                 `json:"include_stats"`
}

type GetConversationResult struct {
	Conversation    *conversationentities.Conversation `json:"conversation"`
	UserRole        string                              `json:"user_role"`
	UnreadCount     int64                               `json:"unread_count,omitempty"`
	LastReadSeq     int64                               `json:"last_read_seq,omitempty"`
	ParticipantInfo *ParticipantInfo                    `json:"participant_info,omitempty"`
	RetrievedAt     time.Time                           `json:"retrieved_at"`
}

type ParticipantInfo struct {
	TotalCount    int `json:"total_count"`
	ActiveCount   int `json:"active_count"`
	AdminCount    int `json:"admin_count"`
	ObserverCount int `json:"observer_count"`
}

type GetConversationHandler interface {
	Handle(ctx context.Context, query *GetConversationQuery) (*GetConversationResult, error)
}

type GetUserConversationsQuery struct {
	UserID     uuid.UUID  `json:"user_id" validate:"required"`
	TeamID     *uuid.UUID `json:"team_id,omitempty"`
	Type       *string    `json:"type,omitempty" validate:"omitempty,oneof=direct group team_channel"`
	Limit      int        `json:"limit" validate:"min=1,max=100"`
	Offset     int        `json:"offset" validate:"min=0"`
	SortBy     string     `json:"sort_by" validate:"omitempty,oneof=last_activity created_at name"`
	SortOrder  SortOrder  `json:"sort_order"`
	OnlyUnread bool       `json:"only_unread"`
}

type SortOrder string

const (
	SortOrderAsc  SortOrder = "asc"
	SortOrderDesc SortOrder = "desc"
)

type GetUserConversationsResult struct {
	Conversations []*ConversationSummary `json:"conversations"`
	TotalCount    int64                  `json:"total_count"`
	HasMore       bool                   `json:"has_more"`
	NextOffset    *int                   `json:"next_offset,omitempty"`
	RetrievedAt   time.Time              `json:"retrieved_at"`
}

type ConversationSummary struct {
	Conversation  *conversationentities.Conversation `json:"conversation"`
	UnreadCount   int64                               `json:"unread_count"`
	LastReadSeq   int64                               `json:"last_read_seq"`
	UserRole      string                              `json:"user_role"`
	LastMessage   *LastMessageInfo                    `json:"last_message,omitempty"`
	IsMuted       bool                                `json:"is_muted"`
	IsPinned      bool                                `json:"is_pinned"`
}

type LastMessageInfo struct {
	ID        string    `json:"id"`
	Text      string    `json:"text"`
	SenderID  string    `json:"sender_id"`
	Type      string    `json:"type"`
	Timestamp time.Time `json:"timestamp"`
}

type GetUserConversationsHandler interface {
	Handle(ctx context.Context, query *GetUserConversationsQuery) (*GetUserConversationsResult, error)
}

func NewGetConversationQuery(conversationID conversationentities.ConversationID, userID uuid.UUID) *GetConversationQuery {
	return &GetConversationQuery{
		ConversationID: conversationID,
		UserID:         userID,
		IncludeStats:   false,
	}
}

func (q *GetConversationQuery) WithStats() *GetConversationQuery {
	q.IncludeStats = true
	return q
}

func (q *GetConversationQuery) Validate() error {
	if q.ConversationID == "" {
		return ErrInvalidConversationID
	}
	if q.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	return nil
}

func NewGetUserConversationsQuery(userID uuid.UUID) *GetUserConversationsQuery {
	return &GetUserConversationsQuery{
		UserID:    userID,
		Limit:     50, // Default limit
		Offset:    0,
		SortBy:    "last_activity",
		SortOrder: SortOrderDesc,
	}
}

func (q *GetUserConversationsQuery) WithTeam(teamID uuid.UUID) *GetUserConversationsQuery {
	q.TeamID = &teamID
	return q
}

func (q *GetUserConversationsQuery) WithType(conversationType string) *GetUserConversationsQuery {
	q.Type = &conversationType
	return q
}

func (q *GetUserConversationsQuery) WithPagination(limit, offset int) *GetUserConversationsQuery {
	q.Limit = limit
	q.Offset = offset
	return q
}

func (q *GetUserConversationsQuery) WithSorting(sortBy string, sortOrder SortOrder) *GetUserConversationsQuery {
	q.SortBy = sortBy
	q.SortOrder = sortOrder
	return q
}

func (q *GetUserConversationsQuery) OnlyUnreadConversations() *GetUserConversationsQuery {
	q.OnlyUnread = true
	return q
}

func (q *GetUserConversationsQuery) Validate() error {
	if q.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	if q.Limit < 1 || q.Limit > 100 {
		return ErrInvalidLimit
	}
	if q.Offset < 0 {
		return ErrInvalidOffset
	}
	if q.Type != nil && !isValidConversationType(*q.Type) {
		return ErrInvalidConversationType
	}
	if q.SortBy != "" && !isValidSortBy(q.SortBy) {
		return ErrInvalidSortBy
	}
	if q.SortOrder != SortOrderAsc && q.SortOrder != SortOrderDesc && q.SortOrder != "" {
		return ErrInvalidSortOrder
	}
	if q.SortOrder == "" {
		q.SortOrder = SortOrderDesc
	}
	return nil
}

func isValidConversationType(convType string) bool {
	return convType == "direct" || convType == "group" || convType == "team_channel"
}

func isValidSortBy(sortBy string) bool {
	return sortBy == "last_activity" || sortBy == "created_at" || sortBy == "name"
}

var (
	ErrInvalidConversationID   = NewQueryError("invalid conversation ID")
	ErrInvalidUserID          = NewQueryError("invalid user ID")
	ErrInvalidLimit           = NewQueryError("limit must be between 1 and 100")
	ErrInvalidOffset          = NewQueryError("offset must be non-negative")
	ErrInvalidConversationType = NewQueryError("invalid conversation type")
	ErrInvalidSortBy          = NewQueryError("invalid sort field")
	ErrInvalidSortOrder       = NewQueryError("sort order must be 'asc' or 'desc'")
)

type QueryError struct {
	message string
}

func NewQueryError(message string) *QueryError {
	return &QueryError{message: message}
}

func (e *QueryError) Error() string {
	return e.message
}