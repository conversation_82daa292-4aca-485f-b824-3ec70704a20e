package queries

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/entities"
	"github.com/swork-team/platform/services/messaging/internal/domain/conversation/repositories"
)

type GetConversationQuery struct {
	ConversationID entities.ConversationID `json:"conversation_id" validate:"required"`
	UserID         uuid.UUID               `json:"user_id" validate:"required"`
}

type GetConversationResult struct {
	Conversation    *entities.Conversation `json:"conversation"`
	UnreadCount     int64                  `json:"unread_count"`
	LastReadSequence int64                 `json:"last_read_sequence"`
}

type GetConversationHandler struct {
	conversationRepo repositories.ConversationRepository
}

func NewGetConversationHandler(
	conversationRepo repositories.ConversationRepository,
) *GetConversationHandler {
	return &GetConversationHandler{
		conversationRepo: conversationRepo,
	}
}

func (h *GetConversationHandler) Handle(ctx context.Context, query *GetConversationQuery) (*GetConversationResult, error) {
	// Validate query
	if err := h.validateQuery(query); err != nil {
		return nil, fmt.Errorf("invalid query: %w", err)
	}

	// Get conversation
	conversation, err := h.conversationRepo.GetByID(ctx, query.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	// Verify user is a participant
	if !conversation.HasParticipant(query.UserID) {
		return nil, fmt.Errorf("user is not a participant in conversation")
	}

	// Get user's participation details
	participant, exists := conversation.GetParticipant(query.UserID)
	if !exists {
		return nil, fmt.Errorf("user participation details not found")
	}

	// Calculate unread count
	unreadCount := conversation.GetUnreadCount(query.UserID)

	return &GetConversationResult{
		Conversation:     conversation,
		UnreadCount:      unreadCount,
		LastReadSequence: participant.LastReadSeq(),
	}, nil
}

func (h *GetConversationHandler) validateQuery(query *GetConversationQuery) error {
	if query.ConversationID == "" {
		return fmt.Errorf("conversation ID is required")
	}
	if query.UserID == uuid.Nil {
		return fmt.Errorf("user ID is required")
	}
	return nil
}

// List conversations query
type ListConversationsQuery struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
	Limit  int       `json:"limit" validate:"min=1,max=100"`
	Offset int64     `json:"offset" validate:"min=0"`
}

type ListConversationsResult struct {
	Conversations []*ConversationWithMetadata `json:"conversations"`
	TotalCount    int                         `json:"total_count"`
	HasMore       bool                        `json:"has_more"`
	NextOffset    *int64                      `json:"next_offset,omitempty"`
}

type ConversationWithMetadata struct {
	Conversation     *entities.Conversation `json:"conversation"`
	UnreadCount      int64                  `json:"unread_count"`
	LastReadSequence int64                  `json:"last_read_sequence"`
	LastActivity     time.Time              `json:"last_activity"`
}

type ListConversationsHandler struct {
	conversationRepo repositories.ConversationRepository
}

func NewListConversationsHandler(
	conversationRepo repositories.ConversationRepository,
) *ListConversationsHandler {
	return &ListConversationsHandler{
		conversationRepo: conversationRepo,
	}
}

func (h *ListConversationsHandler) Handle(ctx context.Context, query *ListConversationsQuery) (*ListConversationsResult, error) {
	// Validate query
	if err := h.validateListQuery(query); err != nil {
		return nil, fmt.Errorf("invalid query: %w", err)
	}

	// Get user's conversations
	conversations, err := h.conversationRepo.GetByParticipant(ctx, query.UserID, query.Limit, query.Offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversations: %w", err)
	}

	// Build result with metadata
	result := make([]*ConversationWithMetadata, 0, len(conversations))
	for _, conv := range conversations {
		participant, exists := conv.GetParticipant(query.UserID)
		if !exists {
			continue // Skip conversations where user is not found (shouldn't happen)
		}

		metadata := &ConversationWithMetadata{
			Conversation:     conv,
			UnreadCount:      conv.GetUnreadCount(query.UserID),
			LastReadSequence: participant.LastReadSeq(),
			LastActivity:     conv.LastActivity(),
		}
		result = append(result, metadata)
	}

	// Determine if there are more conversations
	hasMore := len(result) == query.Limit
	var nextOffset *int64
	if hasMore {
		next := query.Offset + int64(len(result))
		nextOffset = &next
	}

	return &ListConversationsResult{
		Conversations: result,
		TotalCount:    len(result),
		HasMore:       hasMore,
		NextOffset:    nextOffset,
	}, nil
}

func (h *ListConversationsHandler) validateListQuery(query *ListConversationsQuery) error {
	if query.UserID == uuid.Nil {
		return fmt.Errorf("user ID is required")
	}
	if query.Limit <= 0 {
		query.Limit = 20 // Default limit
	}
	if query.Limit > 100 {
		query.Limit = 100 // Max limit
	}
	if query.Offset < 0 {
		query.Offset = 0
	}
	return nil
}