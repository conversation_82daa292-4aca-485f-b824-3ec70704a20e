#!/bin/bash

# Development database seeding script for messaging service
# This script seeds the databases with test data for development and testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
POSTGRES_URL=""
MONGODB_URI=""
REDIS_URL=""
SEED_TYPE="full" # full, basic, minimal
CLEAN_FIRST=false

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] ✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ✗${NC} $1"
}

# Usage information
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Development database seeding script for messaging service.

OPTIONS:
    -t, --type TYPE         Seed type: full, basic, minimal (default: full)
    -c, --clean             Clean existing data before seeding
    -p, --postgres-url URL  PostgreSQL connection URL
    -m, --mongo-url URL     MongoDB connection URL
    -r, --redis-url URL     Redis connection URL
    -h, --help              Show this help message

SEED TYPES:
    minimal     Create basic test users and 1 conversation
    basic       Create test users, several conversations, and some messages
    full        Create comprehensive test data including all message types

EXAMPLES:
    # Full development seed
    $0 --type full

    # Clean database and seed with basic data
    $0 --type basic --clean

    # Minimal seed with custom database URLs
    $0 --type minimal -p "postgres://..." -m "mongodb://..."

ENVIRONMENT VARIABLES:
    POSTGRES_URL      PostgreSQL connection URL
    MONGODB_URI       MongoDB connection URL
    REDIS_URL         Redis connection URL

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            SEED_TYPE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_FIRST=true
            shift
            ;;
        -p|--postgres-url)
            POSTGRES_URL="$2"
            shift 2
            ;;
        -m|--mongo-url)
            MONGODB_URI="$2"
            shift 2
            ;;
        -r|--redis-url)
            REDIS_URL="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate seed type
if [[ "$SEED_TYPE" != "minimal" && "$SEED_TYPE" != "basic" && "$SEED_TYPE" != "full" ]]; then
    log_error "Invalid seed type: $SEED_TYPE. Must be 'minimal', 'basic', or 'full'"
    exit 1
fi

# Load environment variables if not provided
if [[ -z "$POSTGRES_URL" ]]; then
    if [[ -n "$POSTGRES_HOST" ]]; then
        POSTGRES_URL="postgres://${POSTGRES_USER:-swork}:${POSTGRES_PASSWORD}@${POSTGRES_HOST:-localhost}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-swork_messaging}?sslmode=${POSTGRES_SSL_MODE:-disable}"
    else
        POSTGRES_URL="${POSTGRES_URL:-postgres://swork:messaging_password@localhost:5432/swork_messaging?sslmode=disable}"
    fi
fi

if [[ -z "$MONGODB_URI" ]]; then
    MONGODB_URI="${MONGODB_URI:-mongodb://localhost:27017/swork_messaging}"
fi

if [[ -z "$REDIS_URL" ]]; then
    REDIS_URL="${REDIS_URL:-redis://localhost:6379}"
fi

# Test database connections
test_connections() {
    log "Testing database connections..."
    
    # Test PostgreSQL
    if ! PGPASSWORD="${POSTGRES_PASSWORD}" psql "$POSTGRES_URL" -c "SELECT 1;" -q >/dev/null 2>&1; then
        log_error "Cannot connect to PostgreSQL: $POSTGRES_URL"
        return 1
    fi
    
    # Test MongoDB
    if command -v mongosh &> /dev/null; then
        if ! mongosh "$MONGODB_URI" --eval "db.adminCommand('ping')" --quiet >/dev/null 2>&1; then
            log_error "Cannot connect to MongoDB: $MONGODB_URI"
            return 1
        fi
    else
        log_warning "mongosh not available, skipping MongoDB connection test"
    fi
    
    # Test Redis
    if command -v redis-cli &> /dev/null; then
        if ! redis-cli -u "$REDIS_URL" ping >/dev/null 2>&1; then
            log_error "Cannot connect to Redis: $REDIS_URL"
            return 1
        fi
    else
        log_warning "redis-cli not available, skipping Redis connection test"
    fi
    
    log_success "All database connections successful"
}

# Clean existing data
clean_data() {
    if [[ "$CLEAN_FIRST" != true ]]; then
        return 0
    fi
    
    log "Cleaning existing data..."
    
    # Clean PostgreSQL tables
    PGPASSWORD="${POSTGRES_PASSWORD}" psql "$POSTGRES_URL" << 'EOF'
    -- Clean conversation data
    TRUNCATE TABLE conversation_participant_models CASCADE;
    TRUNCATE TABLE conversation_models CASCADE;
    
    -- Reset sequences if they exist
    SELECT setval(pg_get_serial_sequence('conversation_models', 'id'), 1, false);
EOF
    
    # Clean MongoDB collections
    if command -v mongosh &> /dev/null; then
        mongosh "$MONGODB_URI" --eval "
            use swork_messaging;
            db.message_documents.deleteMany({});
            db.message_cache.deleteMany({});
            db.sequence_allocation.deleteMany({});
            db.message_analytics.deleteMany({});
            db.connection_tracking.deleteMany({});
            print('MongoDB collections cleaned');
        " --quiet
    fi
    
    # Clean Redis cache
    redis-cli -u "$REDIS_URL" FLUSHDB >/dev/null 2>&1 || true
    
    log_success "Data cleaned successfully"
}

# Seed PostgreSQL with conversations
seed_conversations() {
    log "Seeding conversations..."
    
    case $SEED_TYPE in
        minimal)
            seed_minimal_conversations
            ;;
        basic)
            seed_basic_conversations
            ;;
        full)
            seed_full_conversations
            ;;
    esac
    
    log_success "Conversations seeded"
}

# Minimal conversation seeding
seed_minimal_conversations() {
    PGPASSWORD="${POSTGRES_PASSWORD}" psql "$POSTGRES_URL" << 'EOF'
-- Create test conversations
INSERT INTO conversation_models (id, type, name, creator_id, last_activity, created_at, updated_at) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'direct', NULL, '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW());

-- Create participants
INSERT INTO conversation_participant_models (conversation_id, user_id, joined_at, role) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-************', NOW(), 'member');
EOF
}

# Basic conversation seeding
seed_basic_conversations() {
    PGPASSWORD="${POSTGRES_PASSWORD}" psql "$POSTGRES_URL" << 'EOF'
-- Create test conversations
INSERT INTO conversation_models (id, type, name, creator_id, last_activity, created_at, updated_at) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'direct', NULL, '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440002', 'group', 'Development Team', '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440003', 'team_channel', 'General Discussion', '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440004', 'direct', NULL, '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW());

-- Create participants
INSERT INTO conversation_participant_models (conversation_id, user_id, joined_at, role) VALUES
    -- Direct conversation 1
    ('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    -- Group conversation
    ('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-************', NOW(), 'admin'),
    ('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    -- Team channel
    ('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', NOW(), 'admin'),
    ('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    -- Direct conversation 2
    ('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-************', NOW(), 'member');
EOF
}

# Full conversation seeding
seed_full_conversations() {
    seed_basic_conversations
    
    PGPASSWORD="${POSTGRES_PASSWORD}" psql "$POSTGRES_URL" << 'EOF'
-- Add more conversations for full seeding
INSERT INTO conversation_models (id, type, name, description, creator_id, last_activity, created_at, updated_at) VALUES
    ('550e8400-e29b-41d4-a716-446655440005', 'group', 'QA Team', 'Quality Assurance Team Chat', '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440006', 'group', 'Project Alpha', 'Project Alpha Development Discussion', '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440007', 'direct', NULL, NULL, '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440008', 'team_channel', 'Announcements', 'Company-wide announcements', '550e8400-e29b-41d4-a716-************', NOW(), NOW(), NOW());

-- Add participants for new conversations
INSERT INTO conversation_participant_models (conversation_id, user_id, joined_at, role) VALUES
    -- QA Team
    ('550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-************', NOW(), 'admin'),
    ('550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    -- Project Alpha
    ('550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-************', NOW(), 'admin'),
    ('550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    -- Direct conversation 3
    ('550e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    -- Announcements
    ('550e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-************', NOW(), 'admin'),
    ('550e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-************', NOW(), 'member'),
    ('550e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-************', NOW(), 'member');
EOF
}

# Seed MongoDB with messages
seed_messages() {
    if ! command -v mongosh &> /dev/null; then
        log_warning "mongosh not available, skipping MongoDB seeding"
        return 0
    fi
    
    log "Seeding messages..."
    
    case $SEED_TYPE in
        minimal)
            seed_minimal_messages
            ;;
        basic)
            seed_basic_messages
            ;;
        full)
            seed_full_messages
            ;;
    esac
    
    log_success "Messages seeded"
}

# Minimal message seeding
seed_minimal_messages() {
    mongosh "$MONGODB_URI" --eval "
        use swork_messaging;
        
        // Seed basic sequence allocation
        db.sequence_allocation.insertMany([
            {
                conversation_id: '550e8400-e29b-41d4-a716-446655440001',
                current_sequence: 2,
                last_sequence: 100,
                batch_size: 50,
                updated_at: new Date()
            }
        ]);
        
        // Seed basic messages
        db.message_documents.insertOne({
            doc_id: '550e8400-e29b-41d4-a716-446655440001:0',
            conversation_id: '550e8400-e29b-41d4-a716-446655440001',
            shard_index: 0,
            messages: [
                {
                    id: '660e8400-e29b-41d4-a716-446655440001',
                    sequence: 1,
                    sender_id: '550e8400-e29b-41d4-a716-************',
                    content: {
                        text: 'Hello! This is a test message.',
                        attachments: [],
                        mentions: [],
                        thread_id: null
                    },
                    message_type: 'text',
                    timestamp: new Date(),
                    metadata: {},
                    read_by: []
                },
                {
                    id: '660e8400-e29b-41d4-a716-446655440002',
                    sequence: 2,
                    sender_id: '550e8400-e29b-41d4-a716-************',
                    content: {
                        text: 'Hi there! Nice to see the messaging system working.',
                        attachments: [],
                        mentions: ['550e8400-e29b-41d4-a716-************'],
                        thread_id: null
                    },
                    message_type: 'text',
                    timestamp: new Date(),
                    metadata: {},
                    read_by: []
                }
            ],
            created_at: new Date(),
            updated_at: new Date()
        });
        
        print('Minimal messages seeded successfully');
    " --quiet
}

# Basic message seeding
seed_basic_messages() {
    seed_minimal_messages
    
    mongosh "$MONGODB_URI" --eval "
        use swork_messaging;
        
        // Add sequence allocation for more conversations
        db.sequence_allocation.insertMany([
            {
                conversation_id: '550e8400-e29b-41d4-a716-446655440002',
                current_sequence: 5,
                last_sequence: 100,
                batch_size: 100,
                updated_at: new Date()
            },
            {
                conversation_id: '550e8400-e29b-41d4-a716-446655440003',
                current_sequence: 3,
                last_sequence: 100,
                batch_size: 100,
                updated_at: new Date()
            }
        ]);
        
        // Seed group conversation messages
        db.message_documents.insertOne({
            doc_id: '550e8400-e29b-41d4-a716-446655440002:0',
            conversation_id: '550e8400-e29b-41d4-a716-446655440002',
            shard_index: 0,
            messages: [
                {
                    id: '660e8400-e29b-41d4-a716-446655440010',
                    sequence: 1,
                    sender_id: '550e8400-e29b-41d4-a716-************',
                    content: {
                        text: 'Welcome to the Development Team chat!',
                        attachments: [],
                        mentions: [],
                        thread_id: null
                    },
                    message_type: 'text',
                    timestamp: new Date(),
                    metadata: {},
                    read_by: []
                },
                {
                    id: '660e8400-e29b-41d4-a716-446655440011',
                    sequence: 2,
                    sender_id: '550e8400-e29b-41d4-a716-************',
                    content: {
                        text: 'Thanks for adding me to the team!',
                        attachments: [],
                        mentions: [],
                        thread_id: null
                    },
                    message_type: 'text',
                    timestamp: new Date(),
                    metadata: {},
                    read_by: []
                },
                {
                    id: '660e8400-e29b-41d4-a716-446655440012',
                    sequence: 3,
                    sender_id: '550e8400-e29b-41d4-a716-************',
                    content: {
                        text: 'Looking forward to working together!',
                        attachments: [],
                        mentions: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
                        thread_id: null
                    },
                    message_type: 'text',
                    timestamp: new Date(),
                    metadata: {},
                    read_by: []
                }
            ],
            created_at: new Date(),
            updated_at: new Date()
        });
        
        // Seed team channel messages
        db.message_documents.insertOne({
            doc_id: '550e8400-e29b-41d4-a716-446655440003:0',
            conversation_id: '550e8400-e29b-41d4-a716-446655440003',
            shard_index: 0,
            messages: [
                {
                    id: '660e8400-e29b-41d4-a716-446655440020',
                    sequence: 1,
                    sender_id: '550e8400-e29b-41d4-a716-************',
                    content: {
                        text: 'Welcome to the General Discussion channel! Please introduce yourselves.',
                        attachments: [],
                        mentions: [],
                        thread_id: null
                    },
                    message_type: 'text',
                    timestamp: new Date(),
                    metadata: {},
                    read_by: []
                },
                {
                    id: '660e8400-e29b-41d4-a716-446655440021',
                    sequence: 2,
                    sender_id: '550e8400-e29b-41d4-a716-************',
                    content: {
                        text: 'Hi everyone! I\\'m excited to be part of this team.',
                        attachments: [],
                        mentions: [],
                        thread_id: null
                    },
                    message_type: 'text',
                    timestamp: new Date(),
                    metadata: {},
                    read_by: []
                }
            ],
            created_at: new Date(),
            updated_at: new Date()
        });
        
        print('Basic messages seeded successfully');
    " --quiet
}

# Full message seeding
seed_full_messages() {
    seed_basic_messages
    
    mongosh "$MONGODB_URI" --eval "
        use swork_messaging;
        
        // Add more sequence allocations
        db.sequence_allocation.insertMany([
            {
                conversation_id: '550e8400-e29b-41d4-a716-446655440004',
                current_sequence: 4,
                last_sequence: 100,
                batch_size: 50,
                updated_at: new Date()
            },
            {
                conversation_id: '550e8400-e29b-41d4-a716-446655440005',
                current_sequence: 6,
                last_sequence: 100,
                batch_size: 100,
                updated_at: new Date()
            }
        ]);
        
        // Add various message types
        db.message_documents.insertMany([
            {
                doc_id: '550e8400-e29b-41d4-a716-446655440004:0',
                conversation_id: '550e8400-e29b-41d4-a716-446655440004',
                shard_index: 0,
                messages: [
                    {
                        id: '660e8400-e29b-41d4-a716-446655440030',
                        sequence: 1,
                        sender_id: '550e8400-e29b-41d4-a716-************',
                        content: {
                            text: 'Check out this image!',
                            attachments: [
                                {
                                    id: '770e8400-e29b-41d4-a716-446655440001',
                                    type: 'image',
                                    name: 'screenshot.png',
                                    size: 1024000,
                                    url: '/files/screenshot.png',
                                    thumbnail_url: '/files/screenshot_thumb.png'
                                }
                            ],
                            mentions: [],
                            thread_id: null
                        },
                        message_type: 'image',
                        timestamp: new Date(),
                        metadata: {
                            image_width: 1920,
                            image_height: 1080
                        },
                        read_by: []
                    },
                    {
                        id: '660e8400-e29b-41d4-a716-446655440031',
                        sequence: 2,
                        sender_id: '550e8400-e29b-41d4-a716-************',
                        content: {
                            text: 'Nice! Here\\'s a document for review.',
                            attachments: [
                                {
                                    id: '770e8400-e29b-41d4-a716-446655440002',
                                    type: 'file',
                                    name: 'project_spec.pdf',
                                    size: 2048000,
                                    url: '/files/project_spec.pdf',
                                    mime_type: 'application/pdf'
                                }
                            ],
                            mentions: ['550e8400-e29b-41d4-a716-************'],
                            thread_id: null
                        },
                        message_type: 'file',
                        timestamp: new Date(),
                        metadata: {},
                        read_by: []
                    }
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                doc_id: '550e8400-e29b-41d4-a716-446655440005:0',
                conversation_id: '550e8400-e29b-41d4-a716-446655440005',
                shard_index: 0,
                messages: [
                    {
                        id: '660e8400-e29b-41d4-a716-446655440040',
                        sequence: 1,
                        sender_id: '550e8400-e29b-41d4-a716-************',
                        content: {
                            text: 'QA Team, let\\'s discuss our testing strategy.',
                            attachments: [],
                            mentions: [],
                            thread_id: null
                        },
                        message_type: 'text',
                        timestamp: new Date(),
                        metadata: {},
                        read_by: []
                    },
                    {
                        id: '660e8400-e29b-41d4-a716-446655440041',
                        sequence: 2,
                        sender_id: '550e8400-e29b-41d4-a716-************',
                        content: {
                            text: 'This message was edited to include more details.',
                            attachments: [],
                            mentions: [],
                            thread_id: null
                        },
                        message_type: 'text',
                        timestamp: new Date(),
                        edited_at: new Date(),
                        metadata: {
                            edited: true,
                            edit_count: 1
                        },
                        read_by: []
                    }
                ],
                created_at: new Date(),
                updated_at: new Date()
            }
        ]);
        
        // Add some analytics data
        db.message_analytics.insertMany([
            {
                conversation_id: '550e8400-e29b-41d4-a716-446655440001',
                date: new Date(),
                message_count: 2,
                user_count: 2,
                message_types: {
                    text: 2,
                    image: 0,
                    file: 0
                },
                updated_at: new Date()
            },
            {
                conversation_id: '550e8400-e29b-41d4-a716-446655440002',
                date: new Date(),
                message_count: 3,
                user_count: 3,
                message_types: {
                    text: 3,
                    image: 0,
                    file: 0
                },
                updated_at: new Date()
            }
        ]);
        
        print('Full messages seeded successfully');
    " --quiet
}

# Seed Redis with cache data
seed_redis() {
    log "Seeding Redis cache..."
    
    # Add some sample cache entries
    redis-cli -u "$REDIS_URL" << 'EOF'
# Set some sequence cache entries
SET seq:550e8400-e29b-41d4-a716-446655440001 "2"
SET seq:550e8400-e29b-41d4-a716-446655440002 "5"
SET seq:550e8400-e29b-41d4-a716-446655440003 "3"

# Set conversation cache
HSET conv:550e8400-e29b-41d4-a716-446655440001 "name" "" "type" "direct" "participants" "2"
HSET conv:550e8400-e29b-41d4-a716-446655440002 "name" "Development Team" "type" "group" "participants" "3"

# Set some user online status
SETEX user_online:550e8400-e29b-41d4-a716-************ 300 "1"
SETEX user_online:550e8400-e29b-41d4-a716-************ 300 "1"
EOF
    
    log_success "Redis cache seeded"
}

# Print summary
print_summary() {
    log_success "Database seeding completed!"
    echo
    log "Seed Summary:"
    log "  Type: $SEED_TYPE"
    log "  Clean first: $CLEAN_FIRST"
    echo
    
    case $SEED_TYPE in
        minimal)
            log "  Conversations: 1 direct conversation"
            log "  Users: 2 test users"
            log "  Messages: 2 text messages"
            ;;
        basic)
            log "  Conversations: 4 conversations (1 direct, 1 group, 1 team channel, 1 direct)"
            log "  Users: 6 test users"
            log "  Messages: ~7 text messages"
            ;;
        full)
            log "  Conversations: 8 conversations (various types)"
            log "  Users: 6 test users"
            log "  Messages: ~15 messages (text, image, file, edited)"
            log "  Analytics: Sample analytics data"
            ;;
    esac
    
    echo
    log "Test Users (UUIDs):"
    log "  User 1: 550e8400-e29b-41d4-a716-************"
    log "  User 2: 550e8400-e29b-41d4-a716-************"
    log "  User 3: 550e8400-e29b-41d4-a716-************"
    log "  User 4: 550e8400-e29b-41d4-a716-************"
    log "  User 5: 550e8400-e29b-41d4-a716-************"
    log "  User 6: 550e8400-e29b-41d4-a716-************"
    echo
    log "Use these UUIDs in your API requests for testing."
}

# Main execution
main() {
    log "Starting messaging service database seeding..."
    log "Seed type: $SEED_TYPE"
    echo
    
    test_connections
    clean_data
    seed_conversations
    seed_messages
    seed_redis
    print_summary
}

# Handle script interruption
trap 'log_error "Seeding interrupted"; exit 1' INT TERM

# Run main function
main "$@"