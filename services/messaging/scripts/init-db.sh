#!/bin/bash

# Database initialization script for messaging service
# This script sets up PostgreSQL, MongoDB, and Redis for the messaging service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
POSTGRES_HOST=${POSTGRES_HOST:-localhost}
POSTGRES_PORT=${POSTGRES_PORT:-5432}
POSTGRES_DB=${POSTGRES_DB:-swork_messaging}
POSTGRES_USER=${POSTGRES_USER:-swork_messaging}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-messaging_password}

MONGODB_HOST=${MONGODB_HOST:-localhost}
MONGODB_PORT=${MONGODB_PORT:-27017}
MONGODB_DB=${MONGODB_DB:-swork_messaging}

REDIS_HOST=${REDIS_HOST:-localhost}
REDIS_PORT=${REDIS_PORT:-6379}

# Directory paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MIGRATIONS_DIR="$(dirname "$SCRIPT_DIR")/migrations"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v psql &> /dev/null; then
        missing_deps+=("postgresql-client")
    fi
    
    if ! command -v mongosh &> /dev/null && ! command -v mongo &> /dev/null; then
        missing_deps+=("mongodb shell (mongosh or mongo)")
    fi
    
    if ! command -v redis-cli &> /dev/null; then
        missing_deps+=("redis-cli")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_error "Please install the missing dependencies and try again."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

# Wait for service to be ready
wait_for_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local max_attempts=${4:-30}
    local attempt=1
    
    log "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            log_success "$service_name is ready"
            return 0
        fi
        
        log "Attempt $attempt/$max_attempts: $service_name not ready, waiting..."
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name is not ready after $max_attempts attempts"
    return 1
}

# Initialize PostgreSQL
init_postgres() {
    log "Initializing PostgreSQL..."
    
    # Wait for PostgreSQL to be ready
    wait_for_service "PostgreSQL" "$POSTGRES_HOST" "$POSTGRES_PORT"
    
    # Check if database exists, create if not
    log "Checking if database '$POSTGRES_DB' exists..."
    
    if PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -lqt | cut -d \| -f 1 | grep -qw "$POSTGRES_DB"; then
        log_warning "Database '$POSTGRES_DB' already exists"
    else
        log "Creating database '$POSTGRES_DB'..."
        PGPASSWORD="$POSTGRES_PASSWORD" createdb -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" "$POSTGRES_DB"
        log_success "Database '$POSTGRES_DB' created successfully"
    fi
    
    # Run migrations
    log "Running database migrations..."
    
    for migration_file in "$MIGRATIONS_DIR"/*.up.sql; do
        if [ -f "$migration_file" ]; then
            local migration_name=$(basename "$migration_file" .up.sql)
            log "Applying migration: $migration_name"
            
            PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f "$migration_file"
            
            if [ $? -eq 0 ]; then
                log_success "Migration $migration_name applied successfully"
            else
                log_error "Failed to apply migration $migration_name"
                exit 1
            fi
        fi
    done
    
    log_success "PostgreSQL initialization completed"
}

# Initialize MongoDB
init_mongodb() {
    log "Initializing MongoDB..."
    
    # Wait for MongoDB to be ready
    wait_for_service "MongoDB" "$MONGODB_HOST" "$MONGODB_PORT"
    
    # Use mongosh if available, otherwise fall back to mongo
    local mongo_cmd="mongosh"
    if ! command -v mongosh &> /dev/null; then
        mongo_cmd="mongo"
    fi
    
    # Create database and collections with indexes
    log "Setting up MongoDB database and indexes..."
    
    $mongo_cmd --host "$MONGODB_HOST:$MONGODB_PORT" --eval "
        use $MONGODB_DB;
        
        // Create message documents collection
        db.createCollection('message_documents');
        
        // Create indexes for message documents
        db.message_documents.createIndex({ 'conversation_id': 1, 'doc_id': 1 }, { unique: true });
        db.message_documents.createIndex({ 'conversation_id': 1, 'shard_index': 1 });
        db.message_documents.createIndex({ 'conversation_id': 1, 'created_at': -1 });
        db.message_documents.createIndex({ 'updated_at': -1 });
        
        // Create message cache collection (TTL collection for temporary caching)
        db.createCollection('message_cache');
        db.message_cache.createIndex({ 'created_at': 1 }, { expireAfterSeconds: 86400 }); // 24 hours TTL
        db.message_cache.createIndex({ 'conversation_id': 1, 'sequence': 1 }, { unique: true });
        
        // Create sequence allocation collection
        db.createCollection('sequence_allocation');
        db.sequence_allocation.createIndex({ 'conversation_id': 1 }, { unique: true });
        
        print('MongoDB indexes created successfully');
    "
    
    if [ $? -eq 0 ]; then
        log_success "MongoDB initialization completed"
    else
        log_error "Failed to initialize MongoDB"
        exit 1
    fi
}

# Initialize Redis
init_redis() {
    log "Initializing Redis..."
    
    # Wait for Redis to be ready
    wait_for_service "Redis" "$REDIS_HOST" "$REDIS_PORT"
    
    # Test Redis connection and set up any required configurations
    log "Testing Redis connection..."
    
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null
    
    if [ $? -eq 0 ]; then
        log_success "Redis connection successful"
        
        # Set up Redis configuration for messaging service
        log "Configuring Redis for messaging service..."
        
        # Set memory policy for cache eviction
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" CONFIG SET maxmemory-policy allkeys-lru
        
        # Set reasonable memory limit (adjust based on available memory)
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" CONFIG SET maxmemory 256mb
        
        # Enable keyspace notifications for cache invalidation
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" CONFIG SET notify-keyspace-events Ex
        
        log_success "Redis configuration completed"
    else
        log_error "Failed to connect to Redis"
        exit 1
    fi
}

# Create database users and permissions
setup_permissions() {
    log "Setting up database permissions..."
    
    # PostgreSQL permissions
    log "Setting up PostgreSQL permissions..."
    PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "
        -- Grant necessary permissions
        GRANT USAGE ON SCHEMA public TO $POSTGRES_USER;
        GRANT CREATE ON SCHEMA public TO $POSTGRES_USER;
        GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $POSTGRES_USER;
        GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $POSTGRES_USER;
        
        -- Set default privileges for future tables
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $POSTGRES_USER;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $POSTGRES_USER;
    "
    
    log_success "Database permissions configured"
}

# Verify setup
verify_setup() {
    log "Verifying setup..."
    
    # Verify PostgreSQL
    log "Verifying PostgreSQL setup..."
    local pg_table_count=$(PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
    
    if [ "$pg_table_count" -ge 2 ]; then
        log_success "PostgreSQL setup verified ($pg_table_count tables found)"
    else
        log_error "PostgreSQL setup verification failed"
        exit 1
    fi
    
    # Verify MongoDB
    log "Verifying MongoDB setup..."
    local mongo_cmd="mongosh"
    if ! command -v mongosh &> /dev/null; then
        mongo_cmd="mongo"
    fi
    
    local mongo_collections=$($mongo_cmd --host "$MONGODB_HOST:$MONGODB_PORT" --quiet --eval "use $MONGODB_DB; db.getCollectionNames().length")
    
    if [ "$mongo_collections" -ge 3 ]; then
        log_success "MongoDB setup verified ($mongo_collections collections found)"
    else
        log_error "MongoDB setup verification failed"
        exit 1
    fi
    
    # Verify Redis
    log "Verifying Redis setup..."
    local redis_info=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" INFO server | grep "redis_version")
    
    if [ -n "$redis_info" ]; then
        log_success "Redis setup verified ($redis_info)"
    else
        log_error "Redis setup verification failed"
        exit 1
    fi
    
    log_success "All database services verified successfully!"
}

# Main execution
main() {
    log "Starting messaging service database initialization..."
    log "Configuration:"
    log "  PostgreSQL: $POSTGRES_HOST:$POSTGRES_PORT (DB: $POSTGRES_DB, User: $POSTGRES_USER)"
    log "  MongoDB: $MONGODB_HOST:$MONGODB_PORT (DB: $MONGODB_DB)"
    log "  Redis: $REDIS_HOST:$REDIS_PORT"
    echo
    
    check_dependencies
    init_postgres
    init_mongodb
    init_redis
    setup_permissions
    verify_setup
    
    log_success "Database initialization completed successfully!"
    log "The messaging service databases are ready for use."
}

# Handle script interruption
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"