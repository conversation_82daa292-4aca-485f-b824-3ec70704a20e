#!/bin/bash

# Migration script for messaging service
# This script provides a convenient interface for running database migrations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MIGRATE_CMD="$PROJECT_ROOT/cmd/migrate"

# Default values
DIRECTION="up"
TARGET=""
POSTGRES_URL=""
MONGODB_URI=""
MONGODB_DB="swork_messaging"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] ✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ✗${NC} $1"
}

# Usage information
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Database migration tool for messaging service.

OPTIONS:
    -d, --direction DIRECTION   Migration direction: up or down (default: up)
    -t, --target VERSION        Target migration version (optional)
    -p, --postgres-url URL      PostgreSQL connection URL
    -m, --mongo-url URL         MongoDB connection URL
    -b, --mongo-db NAME         MongoDB database name (default: swork_messaging)
    -h, --help                  Show this help message

EXAMPLES:
    # Run all pending migrations
    $0

    # Run migrations up to version 3
    $0 -d up -t 3

    # Rollback to version 1
    $0 -d down -t 1

    # Use custom database URLs
    $0 -p "postgres://user:pass@localhost:5432/dbname" -m "mongodb://localhost:27017"

ENVIRONMENT VARIABLES:
    POSTGRES_URL      PostgreSQL connection URL
    MONGODB_URI       MongoDB connection URL
    MONGODB_DB        MongoDB database name

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--direction)
            DIRECTION="$2"
            shift 2
            ;;
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -p|--postgres-url)
            POSTGRES_URL="$2"
            shift 2
            ;;
        -m|--mongo-url)
            MONGODB_URI="$2"
            shift 2
            ;;
        -b|--mongo-db)
            MONGODB_DB="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate direction
if [[ "$DIRECTION" != "up" && "$DIRECTION" != "down" ]]; then
    log_error "Invalid direction: $DIRECTION. Must be 'up' or 'down'"
    exit 1
fi

# Load environment variables if not provided
if [[ -z "$POSTGRES_URL" ]]; then
    if [[ -n "$POSTGRES_HOST" ]]; then
        # Build URL from individual components
        POSTGRES_URL="postgres://${POSTGRES_USER:-swork}:${POSTGRES_PASSWORD}@${POSTGRES_HOST:-localhost}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-swork_messaging}?sslmode=${POSTGRES_SSL_MODE:-disable}"
    else
        POSTGRES_URL="${POSTGRES_URL:-}"
    fi
fi

if [[ -z "$MONGODB_URI" ]]; then
    MONGODB_URI="${MONGODB_URI:-}"
fi

if [[ -z "$MONGODB_DB" ]]; then
    MONGODB_DB="${MONGODB_DB:-swork_messaging}"
fi

# Check if database URLs are provided
if [[ -z "$POSTGRES_URL" ]]; then
    log_error "PostgreSQL URL not provided. Set POSTGRES_URL environment variable or use -p flag"
    exit 1
fi

if [[ -z "$MONGODB_URI" ]]; then
    log_error "MongoDB URI not provided. Set MONGODB_URI environment variable or use -m flag"
    exit 1
fi

# Build migration command
build_migration_tool() {
    log "Building migration tool..."
    
    cd "$MIGRATE_CMD"
    
    if ! go mod tidy; then
        log_error "Failed to tidy go modules"
        exit 1
    fi
    
    if ! go build -o migrate main.go; then
        log_error "Failed to build migration tool"
        exit 1
    fi
    
    log_success "Migration tool built successfully"
}

# Run migration
run_migration() {
    log "Running migration..."
    log "Direction: $DIRECTION"
    log "Target: ${TARGET:-latest}"
    log "PostgreSQL: ${POSTGRES_URL%@*}@[HIDDEN]"
    log "MongoDB: ${MONGODB_URI%@*}@[HIDDEN]"
    echo

    cd "$MIGRATE_CMD"

    # Build migration command arguments
    ARGS=()
    ARGS+=("-direction" "$DIRECTION")
    
    if [[ -n "$TARGET" ]]; then
        ARGS+=("-target" "$TARGET")
    fi
    
    ARGS+=("-db-url" "$POSTGRES_URL")
    ARGS+=("-mongo-url" "$MONGODB_URI")
    ARGS+=("-mongo-db" "$MONGODB_DB")

    # Run migration
    if ./migrate "${ARGS[@]}"; then
        log_success "Migration completed successfully"
    else
        log_error "Migration failed"
        exit 1
    fi
}

# Verify migration status
verify_migration() {
    log "Verifying migration status..."
    
    # Check PostgreSQL tables
    PGPASSWORD="${POSTGRES_PASSWORD}" psql "$POSTGRES_URL" -c "\dt" -q >/dev/null 2>&1
    if [[ $? -eq 0 ]]; then
        log_success "PostgreSQL tables verified"
    else
        log_warning "Could not verify PostgreSQL tables"
    fi
    
    # Check MongoDB collections (if mongosh is available)
    if command -v mongosh &> /dev/null; then
        mongosh "$MONGODB_URI" --eval "use $MONGODB_DB; db.getCollectionNames()" --quiet >/dev/null 2>&1
        if [[ $? -eq 0 ]]; then
            log_success "MongoDB collections verified"
        else
            log_warning "Could not verify MongoDB collections"
        fi
    else
        log_warning "mongosh not available, skipping MongoDB verification"
    fi
}

# Main execution
main() {
    log "Starting messaging service migration..."
    
    build_migration_tool
    run_migration
    verify_migration
    
    log_success "Migration process completed!"
}

# Handle script interruption
trap 'log_error "Migration interrupted"; exit 1' INT TERM

# Run main function
main "$@"