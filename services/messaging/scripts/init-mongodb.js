// MongoDB initialization script for messaging service
// This script sets up collections, indexes, and sharding configuration

// Configuration
const DB_NAME = process.env.MONGODB_DB || 'swork_messaging';

// Switch to messaging database
use(DB_NAME);

print('Initializing MongoDB for messaging service...');
print('Database: ' + DB_NAME);

// Drop existing collections if they exist (for clean setup)
// Comment out these lines for production to avoid data loss
// db.message_documents.drop();
// db.message_cache.drop();
// db.sequence_allocation.drop();

// 1. Create message documents collection with sharding support
print('Creating message_documents collection...');
db.createCollection('message_documents', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["doc_id", "conversation_id", "shard_index", "messages", "created_at", "updated_at"],
            properties: {
                doc_id: {
                    bsonType: "string",
                    description: "Unique document ID in format conversationID:shardIndex"
                },
                conversation_id: {
                    bsonType: "string",
                    description: "Conversation ID this document belongs to"
                },
                shard_index: {
                    bsonType: "long",
                    minimum: 0,
                    description: "Shard index for this document (sequence / 100)"
                },
                messages: {
                    bsonType: "array",
                    maxItems: 100,
                    description: "Array of messages (max 100 per document)",
                    items: {
                        bsonType: "object",
                        properties: {
                            id: { bsonType: "string" },
                            sequence: { bsonType: "long" },
                            sender_id: { bsonType: "string" },
                            content: { bsonType: "object" },
                            message_type: { bsonType: "string" },
                            timestamp: { bsonType: "date" },
                            edited_at: { bsonType: ["date", "null"] },
                            deleted_at: { bsonType: ["date", "null"] },
                            metadata: { bsonType: "object" },
                            read_by: { bsonType: "array" }
                        }
                    }
                },
                created_at: {
                    bsonType: "date",
                    description: "Document creation timestamp"
                },
                updated_at: {
                    bsonType: "date",
                    description: "Document last update timestamp"
                }
            }
        }
    }
});

// Create indexes for message documents
print('Creating indexes for message_documents...');

// Primary lookup index (conversation + document)
db.message_documents.createIndex(
    { "doc_id": 1 }, 
    { 
        unique: true, 
        name: "idx_doc_id_unique",
        background: true 
    }
);

// Conversation-based queries
db.message_documents.createIndex(
    { "conversation_id": 1, "shard_index": 1 }, 
    { 
        unique: true, 
        name: "idx_conversation_shard",
        background: true 
    }
);

// Time-based queries for recent messages
db.message_documents.createIndex(
    { "conversation_id": 1, "created_at": -1 }, 
    { 
        name: "idx_conversation_time",
        background: true 
    }
);

// Message sequence lookup within conversation
db.message_documents.createIndex(
    { "conversation_id": 1, "messages.sequence": 1 }, 
    { 
        name: "idx_conversation_sequence",
        background: true 
    }
);

// Sender-based queries
db.message_documents.createIndex(
    { "messages.sender_id": 1, "messages.timestamp": -1 }, 
    { 
        name: "idx_sender_time",
        background: true 
    }
);

// Message type filtering
db.message_documents.createIndex(
    { "conversation_id": 1, "messages.message_type": 1, "messages.timestamp": -1 }, 
    { 
        name: "idx_conversation_type_time",
        background: true 
    }
);

// Cleanup queries (for deleted messages)
db.message_documents.createIndex(
    { "updated_at": -1 }, 
    { 
        name: "idx_updated_time",
        background: true 
    }
);

// 2. Create message cache collection (TTL for temporary caching)
print('Creating message_cache collection...');
db.createCollection('message_cache', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["cache_key", "conversation_id", "data", "created_at"],
            properties: {
                cache_key: {
                    bsonType: "string",
                    description: "Unique cache key"
                },
                conversation_id: {
                    bsonType: "string",
                    description: "Conversation ID for cache invalidation"
                },
                sequence: {
                    bsonType: ["long", "null"],
                    description: "Message sequence number if applicable"
                },
                data: {
                    bsonType: "object",
                    description: "Cached data"
                },
                created_at: {
                    bsonType: "date",
                    description: "Cache entry creation time"
                },
                expires_at: {
                    bsonType: "date",
                    description: "Cache expiration time"
                }
            }
        }
    }
});

// Create TTL index for automatic cleanup (24 hours)
db.message_cache.createIndex(
    { "expires_at": 1 }, 
    { 
        expireAfterSeconds: 0,
        name: "idx_ttl_expires",
        background: true 
    }
);

// Cache lookup indexes
db.message_cache.createIndex(
    { "cache_key": 1 }, 
    { 
        unique: true,
        name: "idx_cache_key",
        background: true 
    }
);

db.message_cache.createIndex(
    { "conversation_id": 1, "sequence": 1 }, 
    { 
        name: "idx_cache_conversation_sequence",
        background: true 
    }
);

// Conversation-based cache invalidation
db.message_cache.createIndex(
    { "conversation_id": 1, "created_at": -1 }, 
    { 
        name: "idx_cache_conversation_time",
        background: true 
    }
);

// 3. Create sequence allocation collection
print('Creating sequence_allocation collection...');
db.createCollection('sequence_allocation', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["conversation_id", "current_sequence", "last_sequence", "updated_at"],
            properties: {
                conversation_id: {
                    bsonType: "string",
                    description: "Conversation ID"
                },
                current_sequence: {
                    bsonType: "long",
                    minimum: 0,
                    description: "Current allocated sequence number"
                },
                last_sequence: {
                    bsonType: "long",
                    minimum: 0,
                    description: "Last available sequence number"
                },
                batch_size: {
                    bsonType: "int",
                    minimum: 1,
                    maximum: 1000,
                    description: "Batch size for sequence allocation"
                },
                updated_at: {
                    bsonType: "date",
                    description: "Last update timestamp"
                },
                lock_token: {
                    bsonType: ["string", "null"],
                    description: "Lock token for atomic operations"
                },
                lock_expires_at: {
                    bsonType: ["date", "null"],
                    description: "Lock expiration time"
                }
            }
        }
    }
});

// Sequence allocation indexes
db.sequence_allocation.createIndex(
    { "conversation_id": 1 }, 
    { 
        unique: true,
        name: "idx_sequence_conversation",
        background: true 
    }
);

// Lock cleanup index
db.sequence_allocation.createIndex(
    { "lock_expires_at": 1 }, 
    { 
        expireAfterSeconds: 0,
        name: "idx_sequence_lock_ttl",
        background: true,
        partialFilterExpression: { "lock_expires_at": { $exists: true } }
    }
);

// 4. Create analytics collection for message statistics
print('Creating message_analytics collection...');
db.createCollection('message_analytics', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["conversation_id", "date", "message_count", "user_count"],
            properties: {
                conversation_id: {
                    bsonType: "string",
                    description: "Conversation ID"
                },
                date: {
                    bsonType: "date",
                    description: "Date for analytics (day granularity)"
                },
                message_count: {
                    bsonType: "long",
                    minimum: 0,
                    description: "Number of messages sent on this date"
                },
                user_count: {
                    bsonType: "int",
                    minimum: 0,
                    description: "Number of unique users who sent messages"
                },
                message_types: {
                    bsonType: "object",
                    description: "Count by message type"
                },
                updated_at: {
                    bsonType: "date",
                    description: "Last update timestamp"
                }
            }
        }
    }
});

// Analytics indexes
db.message_analytics.createIndex(
    { "conversation_id": 1, "date": -1 }, 
    { 
        unique: true,
        name: "idx_analytics_conversation_date",
        background: true 
    }
);

db.message_analytics.createIndex(
    { "date": -1 }, 
    { 
        name: "idx_analytics_date",
        background: true 
    }
);

// 5. Create connection tracking collection for WebSocket connections
print('Creating connection_tracking collection...');
db.createCollection('connection_tracking', {
    validator: {
        $jsonSchema: {
            bsonType: "object",
            required: ["connection_id", "user_id", "connected_at"],
            properties: {
                connection_id: {
                    bsonType: "string",
                    description: "Unique connection ID"
                },
                user_id: {
                    bsonType: "string",
                    description: "User ID"
                },
                device_id: {
                    bsonType: ["string", "null"],
                    description: "Device identifier"
                },
                platform: {
                    bsonType: ["string", "null"],
                    description: "Platform (web, ios, android, etc.)"
                },
                connected_at: {
                    bsonType: "date",
                    description: "Connection timestamp"
                },
                last_heartbeat: {
                    bsonType: ["date", "null"],
                    description: "Last heartbeat timestamp"
                },
                disconnected_at: {
                    bsonType: ["date", "null"],
                    description: "Disconnection timestamp"
                },
                is_active: {
                    bsonType: "bool",
                    description: "Whether connection is currently active"
                }
            }
        }
    }
});

// Connection tracking indexes
db.connection_tracking.createIndex(
    { "connection_id": 1 }, 
    { 
        unique: true,
        name: "idx_connection_id",
        background: true 
    }
);

db.connection_tracking.createIndex(
    { "user_id": 1, "is_active": 1 }, 
    { 
        name: "idx_user_active_connections",
        background: true 
    }
);

db.connection_tracking.createIndex(
    { "last_heartbeat": 1 }, 
    { 
        name: "idx_heartbeat_cleanup",
        background: true 
    }
);

// TTL index for automatic cleanup of old connection records (7 days)
db.connection_tracking.createIndex(
    { "disconnected_at": 1 }, 
    { 
        expireAfterSeconds: 604800, // 7 days
        name: "idx_connection_cleanup",
        background: true,
        partialFilterExpression: { "disconnected_at": { $exists: true } }
    }
);

// 6. Create sample data for testing (optional)
print('Creating sample sequence allocation records...');

// Initialize sequence allocation for a test conversation
db.sequence_allocation.insertOne({
    conversation_id: "test-conversation-1",
    current_sequence: 0,
    last_sequence: 100,
    batch_size: 50,
    updated_at: new Date(),
    lock_token: null,
    lock_expires_at: null
});

// 7. Create database-level configurations
print('Setting up database configurations...');

// Enable journaling for durability (usually enabled by default)
db.adminCommand({ "setParameter": 1, "journalCommitInterval": 100 });

// Set write concern for consistency
db.runCommand({
    "setDefaultRWConcern": 1,
    "defaultWriteConcern": {
        "w": 1,
        "j": true
    }
});

print('MongoDB initialization completed successfully!');
print('Collections created:');
print('  - message_documents (with sharding support)');
print('  - message_cache (with TTL)');
print('  - sequence_allocation (with locking)');
print('  - message_analytics (for statistics)');
print('  - connection_tracking (for WebSocket connections)');
print('');
print('Total indexes created: ' + db.stats().indexes);
print('Database size: ' + (db.stats().dataSize / 1024 / 1024).toFixed(2) + ' MB');

// Verify collections and indexes
print('\nVerification:');
db.getCollectionNames().forEach(function(collection) {
    var indexCount = db.getCollection(collection).getIndexes().length;
    print('  ' + collection + ': ' + indexCount + ' indexes');
});

print('\nMongoDB setup verification completed!');