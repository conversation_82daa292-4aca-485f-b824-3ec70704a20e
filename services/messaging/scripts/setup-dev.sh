#!/bin/bash

# Development environment setup script for messaging service
# This script sets up the complete development environment including databases and dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.dev.yml"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] ✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ✗${NC} $1"
}

# Check if Docker is available
check_docker() {
    log "Checking Docker availability..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not available. Please install Docker Compose and try again."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker and try again."
        exit 1
    fi
    
    log_success "Docker is available and running"
}

# Create development docker-compose file
create_dev_compose() {
    log "Creating development docker-compose configuration..."
    
    cat > "$COMPOSE_FILE" << 'EOF'
version: '3.8'

services:
  # PostgreSQL for conversation storage
  postgres-messaging:
    image: postgres:15-alpine
    container_name: swork-messaging-postgres
    environment:
      POSTGRES_DB: swork_messaging
      POSTGRES_USER: swork_messaging
      POSTGRES_PASSWORD: messaging_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_messaging_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d/
    ports:
      - "5433:5432"  # Use different port to avoid conflicts
    networks:
      - messaging-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U swork_messaging -d swork_messaging"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB for message storage
  mongodb-messaging:
    image: mongo:7.0
    container_name: swork-messaging-mongodb
    environment:
      MONGO_INITDB_DATABASE: swork_messaging
    volumes:
      - mongodb_messaging_data:/data/db
      - ./scripts/init-mongodb.js:/docker-entrypoint-initdb.d/init-mongodb.js:ro
    ports:
      - "27018:27017"  # Use different port to avoid conflicts
    networks:
      - messaging-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: ["mongod", "--replSet", "rs0", "--bind_ip_all"]

  # Redis for caching and real-time features
  redis-messaging:
    image: redis:7.2-alpine
    container_name: swork-messaging-redis
    volumes:
      - redis_messaging_data:/data
    ports:
      - "6380:6379"  # Use different port to avoid conflicts
    networks:
      - messaging-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # MongoDB replica set initialization
  mongodb-setup:
    image: mongo:7.0
    container_name: swork-messaging-mongodb-setup
    depends_on:
      - mongodb-messaging
    networks:
      - messaging-network
    volumes:
      - ./scripts/init-mongodb.js:/scripts/init-mongodb.js:ro
    command: >
      bash -c "
        sleep 10 &&
        mongosh --host mongodb-messaging:27017 --eval 'rs.initiate({_id: \"rs0\", members: [{_id: 0, host: \"mongodb-messaging:27017\"}]})' &&
        sleep 5 &&
        mongosh --host mongodb-messaging:27017 /scripts/init-mongodb.js
      "
    restart: "no"

  # Development database admin tools
  adminer:
    image: adminer:4.8.1
    container_name: swork-messaging-adminer
    ports:
      - "8081:8080"
    networks:
      - messaging-network
    environment:
      ADMINER_DEFAULT_SERVER: postgres-messaging
    depends_on:
      - postgres-messaging

  # MongoDB admin interface
  mongo-express:
    image: mongo-express:1.0.0
    container_name: swork-messaging-mongo-express
    ports:
      - "8082:8081"
    networks:
      - messaging-network
    environment:
      ME_CONFIG_MONGODB_URL: mongodb://mongodb-messaging:27017/swork_messaging
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      - mongodb-messaging

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: swork-messaging-redis-commander
    ports:
      - "8083:8081"
    networks:
      - messaging-network
    environment:
      REDIS_HOSTS: redis-messaging:redis-messaging:6379
    depends_on:
      - redis-messaging

volumes:
  postgres_messaging_data:
    driver: local
  mongodb_messaging_data:
    driver: local
  redis_messaging_data:
    driver: local

networks:
  messaging-network:
    driver: bridge
    name: swork-messaging-network
EOF

    log_success "Development docker-compose file created"
}

# Create environment file
create_env_file() {
    log "Creating environment configuration file..."
    
    cat > "$PROJECT_ROOT/.env.dev" << 'EOF'
# Messaging Service Development Environment Configuration

# Service Configuration
SERVICE_NAME=messaging
SERVICE_PORT=8005
SERVICE_ENV=development
LOG_LEVEL=debug

# Database Configuration
# PostgreSQL (Conversations)
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=swork_messaging
POSTGRES_USER=swork_messaging
POSTGRES_PASSWORD=messaging_password
POSTGRES_SSL_MODE=disable
POSTGRES_MAX_CONNECTIONS=25
POSTGRES_MAX_IDLE_CONNECTIONS=5
POSTGRES_CONNECTION_LIFETIME=300s

# MongoDB (Messages)
MONGODB_URI=mongodb://localhost:27018/swork_messaging
MONGODB_DATABASE=swork_messaging
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=2
MONGODB_TIMEOUT=10s

# Redis (Cache & Real-time)
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20
REDIS_MAX_IDLE_CONNECTIONS=5
REDIS_CONNECTION_TIMEOUT=5s

# Cache Configuration
CACHE_TTL_MESSAGES=24h
CACHE_TTL_CONVERSATIONS=12h
CACHE_TTL_CONNECTIONS=5m
CACHE_TTL_SEQUENCES=365d
LOCAL_CACHE_SIZE=20000
LOCAL_CACHE_SLOTS=500

# WebSocket Configuration
WEBSOCKET_MAX_CONNECTIONS=10000
WEBSOCKET_BUFFER_SIZE=512
WEBSOCKET_MAX_WORKERS=16
WEBSOCKET_HEARTBEAT_INTERVAL=30s
WEBSOCKET_CONNECTION_TIMEOUT=60s

# Message Processing
MESSAGE_BATCH_SIZE=100
MESSAGE_QUEUE_SIZE=1000
MESSAGE_WORKER_COUNT=4
MESSAGE_PROCESSING_TIMEOUT=30s

# Sequence Allocation
SEQUENCE_BATCH_SIZE_DIRECT=50
SEQUENCE_BATCH_SIZE_GROUP=100
SEQUENCE_LOCK_TIMEOUT=10s
SEQUENCE_DATA_TIMEOUT=3600s

# Security Configuration
JWT_SECRET=dev-jwt-secret-key-change-in-production
JWT_EXPIRATION=24h
RATE_LIMIT_MESSAGES_PER_MINUTE=60
RATE_LIMIT_CONNECTIONS_PER_IP=10

# External Service URLs (for development)
TEAM_SERVICE_URL=http://localhost:8001
NOTIFICATION_SERVICE_URL=http://localhost:8003
DRIVE_SERVICE_URL=http://localhost:8004

# File Upload Configuration
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt,mp4,mp3,wav
UPLOAD_PATH=/tmp/messaging-uploads

# Development Features
ENABLE_CORS=true
ENABLE_DEBUG_LOGS=true
ENABLE_METRICS=true
ENABLE_PROFILING=true
AUTO_MIGRATE=true
EOF

    log_success "Environment configuration file created"
}

# Create development scripts
create_dev_scripts() {
    log "Creating development helper scripts..."
    
    # Create start script
    cat > "$PROJECT_ROOT/scripts/start-dev.sh" << 'EOF'
#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Starting messaging service development environment..."

# Load environment variables
export $(cat "$PROJECT_ROOT/.env.dev" | grep -v '^#' | xargs)

# Start databases
docker-compose -f "$PROJECT_ROOT/docker-compose.dev.yml" up -d

# Wait for databases to be ready
echo "Waiting for databases to be ready..."
sleep 10

# Initialize databases
"$SCRIPT_DIR/init-db.sh"

echo "Starting messaging service..."
cd "$PROJECT_ROOT"
go run cmd/main.go
EOF

    # Create stop script
    cat > "$PROJECT_ROOT/scripts/stop-dev.sh" << 'EOF'
#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Stopping messaging service development environment..."

# Stop all services
docker-compose -f "$PROJECT_ROOT/docker-compose.dev.yml" down

echo "Development environment stopped."
EOF

    # Create reset script
    cat > "$PROJECT_ROOT/scripts/reset-dev.sh" << 'EOF'
#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Resetting messaging service development environment..."
echo "WARNING: This will delete all data!"
read -p "Are you sure? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Reset cancelled."
    exit 1
fi

# Stop and remove containers and volumes
docker-compose -f "$PROJECT_ROOT/docker-compose.dev.yml" down -v

# Remove any orphaned containers
docker-compose -f "$PROJECT_ROOT/docker-compose.dev.yml" rm -f

echo "Development environment reset completed."
echo "Run './scripts/setup-dev.sh' to set up again."
EOF

    # Create logs script
    cat > "$PROJECT_ROOT/scripts/logs-dev.sh" << 'EOF'
#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Show logs for specific service or all services
SERVICE=${1:-}

if [ -n "$SERVICE" ]; then
    echo "Showing logs for $SERVICE..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.dev.yml" logs -f "$SERVICE"
else
    echo "Showing logs for all services..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.dev.yml" logs -f
fi
EOF

    # Make scripts executable
    chmod +x "$PROJECT_ROOT/scripts/start-dev.sh"
    chmod +x "$PROJECT_ROOT/scripts/stop-dev.sh"
    chmod +x "$PROJECT_ROOT/scripts/reset-dev.sh"
    chmod +x "$PROJECT_ROOT/scripts/logs-dev.sh"
    
    log_success "Development helper scripts created"
}

# Create development configuration
create_dev_config() {
    log "Creating development configuration files..."
    
    # Create go module tidy script
    cat > "$PROJECT_ROOT/scripts/tidy.sh" << 'EOF'
#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Tidying Go modules..."
cd "$PROJECT_ROOT"
go mod tidy
go mod download

echo "Go modules tidied successfully."
EOF

    chmod +x "$PROJECT_ROOT/scripts/tidy.sh"
    
    # Create test script
    cat > "$PROJECT_ROOT/scripts/test.sh" << 'EOF'
#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Running tests..."
cd "$PROJECT_ROOT"

# Load test environment
export $(cat "$PROJECT_ROOT/.env.dev" | grep -v '^#' | xargs)

# Run unit tests
echo "Running unit tests..."
go test -v ./internal/...

# Run integration tests if databases are available
if docker-compose -f "$PROJECT_ROOT/docker-compose.dev.yml" ps | grep -q "Up"; then
    echo "Running integration tests..."
    go test -v ./test/...
else
    echo "Skipping integration tests (databases not running)"
fi

echo "Tests completed."
EOF

    chmod +x "$PROJECT_ROOT/scripts/test.sh"
    
    log_success "Development configuration files created"
}

# Create README for development
create_dev_readme() {
    log "Creating development README..."
    
    cat > "$PROJECT_ROOT/README.dev.md" << 'EOF'
# Messaging Service - Development Environment

This guide explains how to set up and run the messaging service in development mode.

## Prerequisites

- Docker and Docker Compose
- Go 1.21+
- Make (optional)

## Quick Start

1. **Set up development environment:**
   ```bash
   ./scripts/setup-dev.sh
   ```

2. **Start the development environment:**
   ```bash
   ./scripts/start-dev.sh
   ```

3. **Access admin interfaces:**
   - PostgreSQL Admin (Adminer): http://localhost:8081
   - MongoDB Admin (Mongo Express): http://localhost:8082 (admin/admin123)
   - Redis Admin (Redis Commander): http://localhost:8083

## Development Commands

### Environment Management
```bash
# Start development environment
./scripts/start-dev.sh

# Stop development environment
./scripts/stop-dev.sh

# Reset environment (deletes all data)
./scripts/reset-dev.sh

# View logs
./scripts/logs-dev.sh [service-name]
```

### Testing
```bash
# Run all tests
./scripts/test.sh

# Run specific test
go test -v ./internal/domain/message/...

# Run integration tests
go test -v ./test/integration_test.go
```

### Development Tools
```bash
# Tidy Go modules
./scripts/tidy.sh

# Initialize databases manually
./scripts/init-db.sh

# Run MongoDB initialization
mongosh --host localhost:27018 < ./scripts/init-mongodb.js
```

## Database Connections

### PostgreSQL (Conversations)
- Host: localhost:5433
- Database: swork_messaging
- User: swork_messaging
- Password: messaging_password

### MongoDB (Messages)
- Host: localhost:27018
- Database: swork_messaging
- No authentication in development

### Redis (Cache)
- Host: localhost:6380
- No authentication in development

## Configuration

Development configuration is stored in `.env.dev`. Key settings:

- Service runs on port 8005
- All databases use non-standard ports to avoid conflicts
- Debug logging enabled
- CORS enabled for frontend development
- Auto-migration enabled

## Project Structure

```
messaging/
├── cmd/main.go                 # Service entry point
├── internal/                   # Internal packages
│   ├── application/           # Application layer (CQRS)
│   ├── domain/               # Domain layer (DDD)
│   └── infrastructure/       # Infrastructure layer
├── migrations/               # Database migrations
├── scripts/                  # Development scripts
├── test/                     # Integration tests
├── docker-compose.dev.yml    # Development services
└── .env.dev                  # Development environment
```

## Common Issues

### Port Conflicts
If you get port conflicts, check what's running:
```bash
# Check PostgreSQL
lsof -i :5433

# Check MongoDB  
lsof -i :27018

# Check Redis
lsof -i :6380
```

### Database Connection Issues
1. Ensure Docker services are running:
   ```bash
   docker-compose -f docker-compose.dev.yml ps
   ```

2. Check service health:
   ```bash
   docker-compose -f docker-compose.dev.yml logs [service-name]
   ```

3. Restart services:
   ```bash
   docker-compose -f docker-compose.dev.yml restart
   ```

### MongoDB Replica Set Issues
If MongoDB replica set is not initialized:
```bash
docker exec -it swork-messaging-mongodb mongosh --eval "rs.initiate()"
```

## API Testing

Use the included integration tests or tools like curl:

```bash
# Health check
curl http://localhost:8005/health

# WebSocket connection
wscat -c ws://localhost:8005/ws?token=<jwt-token>
```

## Performance Testing

For load testing WebSocket connections:
```bash
# Install artillery (if not installed)
npm install -g artillery

# Run WebSocket load test (create artillery config as needed)
artillery run websocket-load-test.yml
```

## Troubleshooting

1. **Database migrations not applied:**
   - Run `./scripts/init-db.sh` manually

2. **WebSocket connections failing:**
   - Check JWT token validity
   - Verify CORS settings

3. **High memory usage:**
   - Adjust cache sizes in `.env.dev`
   - Monitor with `docker stats`

For more help, check the main project documentation or create an issue.
EOF

    log_success "Development README created"
}

# Main execution
main() {
    log "Setting up messaging service development environment..."
    
    check_docker
    create_dev_compose
    create_env_file
    create_dev_scripts
    create_dev_config
    create_dev_readme
    
    log_success "Development environment setup completed!"
    echo
    log "Next steps:"
    log "1. Review and customize .env.dev if needed"
    log "2. Run './scripts/start-dev.sh' to start the development environment"
    log "3. Access admin interfaces:"
    log "   - PostgreSQL: http://localhost:8081"
    log "   - MongoDB: http://localhost:8082 (admin/admin123)"
    log "   - Redis: http://localhost:8083"
    echo
    log "See README.dev.md for detailed development instructions."
}

# Handle script interruption
trap 'log_error "Setup interrupted"; exit 1' INT TERM

# Run main function
main "$@"