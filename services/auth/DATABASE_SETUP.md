# Database Setup Guide for Auth Service

This guide documents the database schema setup and fixes required for the auth service to handle both email and phone number registrations correctly.

## Problem Summary

The auth service supports two registration methods:
1. **Email-only registration**: Users register with email, phone_number should be NULL
2. **Phone-only registration**: Users register with phone, email should be NULL

**Issue**: <PERSON><PERSON><PERSON> was converting `nil` values to empty strings (`""`) instead of NULL, causing unique constraint violations when multiple users registered using the same method.

## Database Schema Fixes Required

### 1. Create Normalization Functions

```sql
-- Function to normalize phone numbers (convert empty strings to NULL)
CREATE OR REPLACE FUNCTION normalize_phone_number(phone_number TEXT) 
RETURNS TEXT AS $$
BEGIN
    IF phone_number IS NULL OR phone_number = '' THEN
        RETURN NULL;
    ELSE
        RETURN phone_number;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to normalize email addresses (convert empty strings to NULL)
CREATE OR REPLACE FUNCTION normalize_email(email TEXT) 
R<PERSON>URNS TEXT AS $$
BEGIN
    IF email IS NULL OR email = '' THEN
        RETURN NULL;
    ELSE
        RETURN email;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
```

### 2. Update Unique Indexes

```sql
-- Drop existing unique indexes
DROP INDEX IF EXISTS idx_users_phone_number;
DROP INDEX IF EXISTS idx_users_email;

-- Create partial unique indexes that ignore NULL values
CREATE UNIQUE INDEX idx_users_phone_number_normalized 
ON users (normalize_phone_number(phone_number)) 
WHERE normalize_phone_number(phone_number) IS NOT NULL;

CREATE UNIQUE INDEX idx_users_email_normalized 
ON users (normalize_email(email)) 
WHERE normalize_email(email) IS NOT NULL;
```

### 3. Create Database Triggers

```sql
-- Create trigger function to normalize both email and phone before insert/update
CREATE OR REPLACE FUNCTION normalize_user_fields_before_insert()
RETURNS TRIGGER AS $$
BEGIN
    -- Normalize phone number
    IF NEW.phone_number = '' THEN
        NEW.phone_number = NULL;
    END IF;
    
    -- Normalize email
    IF NEW.email = '' THEN
        NEW.email = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for both INSERT and UPDATE
CREATE TRIGGER normalize_user_fields_insert 
    BEFORE INSERT ON users 
    FOR EACH ROW EXECUTE FUNCTION normalize_user_fields_before_insert();

CREATE TRIGGER normalize_user_fields_update 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION normalize_user_fields_before_insert();
```

### 4. Clean Existing Data

```sql
-- Convert any existing empty strings to NULL
UPDATE users SET email = NULL WHERE email = '';
UPDATE users SET phone_number = NULL WHERE phone_number = '';
```

## Application Code Requirements

### 1. User Model (models/user.go)

The User model must use `sql.NullString` for both email and phone fields:

```go
import (
    "database/sql"
    "time"
    "github.com/google/uuid"
    "gorm.io/gorm"
)

type User struct {
    ID            uuid.UUID      `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
    Email         sql.NullString `json:"email" gorm:"uniqueIndex"`
    PhoneNumber   sql.NullString `json:"phone_number" gorm:"uniqueIndex"`
    PasswordHash  string         `json:"-"`
    EmailVerified bool           `json:"email_verified" gorm:"default:false"`
    PhoneVerified bool           `json:"phone_verified" gorm:"default:false"`
    // ... other fields
}
```

### 2. Auth Service User Creation

```go
// Create user with proper NULL handling
user := &models.User{
    FirstName:   req.FirstName,
    LastName:    req.LastName,
    Username:    req.Username,
    PasswordHash: string(hashedPassword),
    Email:       sql.NullString{Valid: false}, // Default to NULL
    PhoneNumber: sql.NullString{Valid: false}, // Default to NULL
}

if contactType == "email" {
    user.Email = sql.NullString{String: contact, Valid: true}
    user.EmailVerified = true
    user.PhoneNumber = sql.NullString{Valid: false} // Ensure NULL for email registration
} else {
    user.PhoneNumber = sql.NullString{String: contact, Valid: true}
    user.PhoneVerified = true
    user.Email = sql.NullString{Valid: false} // Ensure NULL for phone registration
}
```

### 3. API Response Conversion

```go
func (s *AuthService) convertToAPIUser(user *models.User) *APIUser {
    email := ""
    if user.Email.Valid {
        email = user.Email.String
    }
    
    phoneNumber := ""
    if user.PhoneNumber.Valid {
        phoneNumber = user.PhoneNumber.String
    }
    
    return &APIUser{
        ID:            user.ID.String(),
        Email:         email,
        PhoneNumber:   phoneNumber,
        EmailVerified: user.EmailVerified,
        PhoneVerified: user.PhoneVerified,
        // ... other fields
    }
}
```

## Complete Setup Script

Run this script when recreating the database:

```sql
-- Connect to your database
-- psql -U swork -d swork_auth

-- 1. Create normalization functions
CREATE OR REPLACE FUNCTION normalize_phone_number(phone_number TEXT) 
RETURNS TEXT AS $$
BEGIN
    IF phone_number IS NULL OR phone_number = '' THEN
        RETURN NULL;
    ELSE
        RETURN phone_number;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION normalize_email(email TEXT) 
RETURNS TEXT AS $$
BEGIN
    IF email IS NULL OR email = '' THEN
        RETURN NULL;
    ELSE
        RETURN email;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 2. Drop existing indexes
DROP INDEX IF EXISTS idx_users_phone_number;
DROP INDEX IF EXISTS idx_users_email;

-- 3. Create partial unique indexes
CREATE UNIQUE INDEX idx_users_phone_number_normalized 
ON users (normalize_phone_number(phone_number)) 
WHERE normalize_phone_number(phone_number) IS NOT NULL;

CREATE UNIQUE INDEX idx_users_email_normalized 
ON users (normalize_email(email)) 
WHERE normalize_email(email) IS NOT NULL;

-- 4. Create trigger function
CREATE OR REPLACE FUNCTION normalize_user_fields_before_insert()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.phone_number = '' THEN
        NEW.phone_number = NULL;
    END IF;
    
    IF NEW.email = '' THEN
        NEW.email = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create triggers
DROP TRIGGER IF EXISTS normalize_user_fields_insert ON users;
DROP TRIGGER IF EXISTS normalize_user_fields_update ON users;

CREATE TRIGGER normalize_user_fields_insert 
    BEFORE INSERT ON users 
    FOR EACH ROW EXECUTE FUNCTION normalize_user_fields_before_insert();

CREATE TRIGGER normalize_user_fields_update 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION normalize_user_fields_before_insert();

-- 6. Clean existing data
UPDATE users SET email = NULL WHERE email = '';
UPDATE users SET phone_number = NULL WHERE phone_number = '';
```

## Quick Setup Commands

For Docker environment:

```bash
# Apply the database setup
docker exec swork-postgres psql -U swork -d swork_auth -f /path/to/setup.sql

# Or run commands directly
docker exec swork-postgres psql -U swork -d swork_auth -c "
-- Paste the complete setup script here
"

# Rebuild auth service
make rebuild-auth
```

## Testing the Setup

After applying the fixes, test both registration methods:

```bash
# Test email registration
curl -X POST http://localhost:8001/auth/send-otp \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

curl -X POST http://localhost:8001/auth/complete-registration \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "123456",
    "first_name": "Test",
    "last_name": "User",
    "username": "testuser",
    "password": "SecurePass123@"
  }'

# Test phone registration
curl -X POST http://localhost:8001/auth/send-otp \
  -H "Content-Type: application/json" \
  -d '{"phone_number": "+1234567890"}'

curl -X POST http://localhost:8001/auth/complete-registration \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "+1234567890",
    "otp": "123456",
    "first_name": "Phone",
    "last_name": "User",
    "username": "phoneuser",
    "password": "SecurePass123@"
  }'
```

## Verification

Check the database state after testing:

```sql
-- Verify data is stored correctly
SELECT 
  CASE WHEN email IS NULL THEN 'NULL' ELSE email END as email_display,
  CASE WHEN phone_number IS NULL THEN 'NULL' ELSE phone_number END as phone_display,
  username,
  email_verified,
  phone_verified
FROM users 
ORDER BY created_at;
```

Expected result:
- Email registrations: email = actual email, phone_number = NULL
- Phone registrations: phone_number = actual phone, email = NULL
- No duplicate constraint violations

## Troubleshooting

### Common Issues

1. **Still getting duplicate key violations**: 
   - Check if triggers are properly created
   - Verify existing data is cleaned (no empty strings)
   - Ensure application code uses `sql.NullString`

2. **GORM migrations failing**:
   - Apply database fixes after GORM creates the initial schema
   - Or modify the GORM migration to include the fixes

3. **Application crashes**:
   - Ensure all code that accesses Email/PhoneNumber fields handles `sql.NullString`
   - Check JWT token generation functions
   - Verify API response conversion functions

### Debug Commands

```sql
-- Check current indexes
\d users

-- Check triggers
SELECT trigger_name, event_manipulation, action_statement 
FROM information_schema.triggers 
WHERE event_object_table = 'users';

-- Check for empty strings vs NULL
SELECT 
  COUNT(*) as total_users,
  COUNT(CASE WHEN email IS NULL THEN 1 END) as null_emails,
  COUNT(CASE WHEN email = '' THEN 1 END) as empty_emails,
  COUNT(CASE WHEN phone_number IS NULL THEN 1 END) as null_phones,
  COUNT(CASE WHEN phone_number = '' THEN 1 END) as empty_phones
FROM users;
```

---

**Note**: This setup is essential for supporting both email-only and phone-only registration flows without conflicts. Always apply these fixes when recreating the database.