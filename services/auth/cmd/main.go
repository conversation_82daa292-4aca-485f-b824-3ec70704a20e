package main

import (
	"os"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/clients"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	"github.com/swork-team/platform/services/auth/internal/config"
	"github.com/swork-team/platform/services/auth/internal/handlers"
	"github.com/swork-team/platform/services/auth/internal/models"
	"github.com/swork-team/platform/services/auth/internal/repositories"
	"github.com/swork-team/platform/services/auth/internal/services"
)

func main() {
	cfg := config.LoadConfig()

	// Create config adapter for bootstrap
	configAdapter := server.NewConfigAdapter(cfg.Server, cfg.Database, cfg.Redis)

	// Use the new ServiceBootstrapper
	bootstrapper := server.NewServiceBootstrapper(server.ServiceBootstrapConfig{
		ServiceName:      "auth",
		ConfigAdapter:    configAdapter,
		ModelsProvider:   getAuthModels,
		RouterSetup:      createAuthRouterSetup(),
		EnableExtensions: false,
		DatabaseType:     server.PostgreSQL,
		CustomStartupLog: authCustomStartupLog,
	})

	bootstrapper.Bootstrap()
}

// getAuthModels returns models for migration
func getAuthModels() []interface{} {
	return []interface{}{
		&models.AuthUser{},
		&models.Session{},
		&models.EmailVerification{},
		&models.PasswordReset{},
		&models.OTPVerification{},
	}
}

// createAuthRouterSetup creates the router setup function for auth service
func createAuthRouterSetup() server.RouterSetupFunction {
	return func(components *server.ServiceComponents) *gin.Engine {
		cfg := config.LoadConfig()

		// Setup User Service client
		userServiceClient := clients.NewUserServiceClient(cfg.Services.User, components.RedisClient)

		userRepo := repositories.NewUserRepository(components.Database)
		authService := services.NewAuthService(userRepo, cfg, userServiceClient)
		authHandler := handlers.NewAuthHandler(authService)

		// Start background cleanup jobs for expired records
		authService.StartBackgroundCleanup()

		// Get logger manager
		loggerManager := logger.NewServiceLoggerManager("auth")

		return setupRouter(authHandler, components.RedisClient, cfg, loggerManager)
	}
}

// authCustomStartupLog provides custom startup logging for auth service
func authCustomStartupLog(serviceLogger logger.ServiceLogger) {
	cfg := config.LoadConfig()
	serviceLogger.Info("Auth service starting up",
		logger.F("version", cfg.Server.Version),
		logger.F("environment", cfg.Server.Environment),
		logger.F("port", cfg.Server.Port),
		logger.F("database_type", "postgresql"),
	)
}

func setupRouter(authHandler *handlers.AuthHandler, redisClient interface{}, cfg *config.Config, loggerManager *logger.ServiceLoggerManager) *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	serviceLogger := loggerManager.GetLogger()

	// Add standardized logging middleware
	router.Use(logger.RequestLoggingMiddleware(serviceLogger))
	router.Use(logger.UserContextMiddleware())
	router.Use(logger.ErrorLoggingMiddleware())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// Health check
	server.AddStandardHealthCheck(router, "auth-service")

	// Auth routes - Modern OTP-based authentication
	auth := router.Group("/auth")
	{
		// Registration flow: OTP-based only
		auth.POST("/send-otp", authHandler.SendOTP)
		auth.POST("/verify-otp", authHandler.VerifyOTP)
		auth.POST("/complete-registration", authHandler.CompleteRegistration)

		// Login: Email/phone + password
		auth.POST("/login", authHandler.LoginWithContact)

		// Token management
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/logout", authHandler.Logout)
		auth.GET("/validate", authHandler.ValidateToken)

		// Password reset
		auth.POST("/forgot-password", authHandler.ForgotPassword)
		auth.POST("/reset-password", authHandler.ResetPassword)

		// Debug endpoints (development only)
		auth.GET("/debug/otp-status", authHandler.DebugGetOTPStatus)
		auth.DELETE("/debug/clear-otp", authHandler.DebugClearOTP)
	}

	return router
}
