package services

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/clients"
	sharedModels "github.com/swork-team/platform/pkg/models"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/auth/internal/config"
	"github.com/swork-team/platform/services/auth/internal/models"
	"github.com/swork-team/platform/services/auth/internal/repositories"
	"golang.org/x/crypto/bcrypt"
)

// AuthService handles authentication business logic
type AuthService struct {
	userRepo   *repositories.UserRepository
	config     *config.Config
	userClient clients.UserServiceClient // Add user service client
}

// Custom error types for better error handling
var (
	ErrInvalidCredentials   = errors.New("invalid credentials")
	ErrUserAlreadyExists    = errors.New("user already exists")
	ErrInvalidOTP           = errors.New("invalid or expired OTP")
	ErrInvalidContactFormat = errors.New("invalid contact format")
	ErrContactRequired      = errors.New("email or phone number is required")
	ErrMultipleContacts     = errors.New("provide either email or phone number, not both")
	ErrUsernameUnavailable  = errors.New("username is not available")
)

// SendOTPRequest represents a request to send OTP for user registration.
// Exactly one of Email or PhoneNumber must be provided.
//
// Example usage:
//   - For email: {"email": "<EMAIL>"}
//   - For phone: {"phone_number": "+1234567890"}
type SendOTPRequest struct {
	Email       string `json:"email,omitempty" example:"<EMAIL>"`
	PhoneNumber string `json:"phone_number,omitempty" example:"+1234567890"`
}

// VerifyOTPRequest represents a request to verify an OTP before registration.
// Must include the same contact method (email or phone) used in SendOTPRequest.
//
// Example usage:
//   - Email verification: {"email": "<EMAIL>", "otp": "123456"}
//   - Phone verification: {"phone_number": "+1234567890", "otp": "123456"}
type VerifyOTPRequest struct {
	Email       string `json:"email,omitempty" example:"<EMAIL>"`
	PhoneNumber string `json:"phone_number,omitempty" example:"+1234567890"`
	OTP         string `json:"otp" binding:"required" example:"123456"`
}

// CompleteRegistrationRequest represents the final step of OTP-based user registration.
// Must include the same contact method (email or phone) used in SendOTPRequest.
// OTP must be verified first using /auth/verify-otp before calling this endpoint.
// The session_token is returned from /auth/verify-otp and required for security.
// Username is now auto-generated as a unique 9-digit number and is no longer required.
//
// Example usage:
//   - Email registration: {"email": "<EMAIL>", "session_token": "abc123", "first_name": "John", "last_name": "Doe", "password": "SecurePass123!"}
//   - Phone registration: {"phone_number": "+1234567890", "session_token": "abc123", "first_name": "John", "last_name": "Doe", "password": "SecurePass123!"}
type CompleteRegistrationRequest struct {
	Email        string `json:"email,omitempty" example:"<EMAIL>"`
	PhoneNumber  string `json:"phone_number,omitempty" example:"+1234567890"`
	SessionToken string `json:"session_token" binding:"required" example:"abc123def456"`
	FirstName    string `json:"first_name" binding:"required" example:"John"`
	LastName     string `json:"last_name" binding:"required" example:"Doe"`
	Password     string `json:"password" binding:"required,min=8" example:"SecurePass123!"`
}

// LoginWithContactRequest represents a login request using email or phone number.
// Exactly one of Email or PhoneNumber must be provided along with the password.
//
// Example usage:
//   - Email login: {"email": "<EMAIL>", "password": "SecurePass123!"}
//   - Phone login: {"phone_number": "+1234567890", "password": "SecurePass123!"}
type LoginWithContactRequest struct {
	Email       string `json:"email,omitempty" example:"<EMAIL>"`
	PhoneNumber string `json:"phone_number,omitempty" example:"+1234567890"`
	Password    string `json:"password" binding:"required" example:"SecurePass123!"`
}

// OTPResponse represents the response after successfully sending an OTP.
// Contains information about where the OTP was sent and when it expires.
type OTPResponse struct {
	Message     string `json:"message" example:"OTP sent successfully"`
	Contact     string `json:"contact" example:"<EMAIL>"`
	ContactType string `json:"contact_type" example:"email"`
	ExpiresIn   int64  `json:"expires_in" example:"300"`
}

// VerifyOTPResponse represents the response after successfully verifying an OTP.
// Contains information about the verified contact and session token for secure registration.
type VerifyOTPResponse struct {
	Message         string `json:"message" example:"OTP verified successfully"`
	Contact         string `json:"contact" example:"<EMAIL>"`
	ContactType     string `json:"contact_type" example:"email"`
	VerifiedAt      string `json:"verified_at" example:"2023-01-15T10:30:00Z"`
	SessionToken    string `json:"session_token" example:"abc123def456"` // Token required for completing registration
	RegistrationTTL int64  `json:"registration_ttl" example:"1800"`      // 30 minutes to complete registration
}

// AuthResponse represents a successful authentication response.
// Contains JWT tokens and user information.
type AuthResponse struct {
	AccessToken  string   `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string   `json:"refresh_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	ExpiresIn    int64    `json:"expires_in" example:"3600"`
	User         *APIUser `json:"user"`
}

// APIUser represents user data for API responses.
// Contains all public user information returned in authentication responses.
type APIUser struct {
	ID            string `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Email         string `json:"email" example:"<EMAIL>"`
	PhoneNumber   string `json:"phone_number" example:"+1234567890"`
	EmailVerified bool   `json:"email_verified" example:"true"`
	PhoneVerified bool   `json:"phone_verified" example:"true"`
	FirstName     string `json:"first_name" example:"John"`
	LastName      string `json:"last_name" example:"Doe"`
	Username      string `json:"username" example:"johndoe"`
	Avatar        string `json:"avatar" example:"https://example.com/avatar.jpg"`
	Bio           string `json:"bio" example:"Software Engineer"`
	Location      string `json:"location" example:"San Francisco, CA"`
	Website       string `json:"website" example:"https://johndoe.com"`
	CreatedAt     string `json:"created_at" example:"2023-01-15T10:30:00Z"`
	UpdatedAt     string `json:"updated_at" example:"2023-06-22T14:45:00Z"`
}

type Claims struct {
	UserID string   `json:"user_id"`
	Email  string   `json:"email"`
	Roles  []string `json:"roles"`
	Teams  []string `json:"teams"`
	jwt.RegisteredClaims
}

func NewAuthService(userRepo *repositories.UserRepository, config *config.Config, userClient clients.UserServiceClient) *AuthService {
	return &AuthService{
		userRepo:   userRepo,
		config:     config,
		userClient: userClient,
	}
}

// SendOTP sends an OTP to the provided email or phone number for registration.
// The OTP is valid for 5 minutes and must be used in CompleteRegistration.
func (s *AuthService) SendOTP(req *SendOTPRequest) (*OTPResponse, error) {
	// Validate that exactly one contact method is provided
	var contact, contactType string

	if req.Email != "" && req.PhoneNumber != "" {
		return nil, ErrMultipleContacts
	}

	if req.Email == "" && req.PhoneNumber == "" {
		return nil, ErrContactRequired
	}

	if req.Email != "" {
		if !utils.IsValidEmail(req.Email) {
			return nil, ErrInvalidContactFormat
		}
		contact = req.Email
		contactType = "email"
	} else {
		if !utils.IsValidPhoneNumber(req.PhoneNumber) {
			return nil, ErrInvalidContactFormat
		}
		contact = req.PhoneNumber
		contactType = "phone"
	}

	// Check if user already exists
	exists, err := s.userRepo.ContactExists(contact)
	if err != nil {
		return nil, fmt.Errorf("failed to check contact existence: %w", err)
	}

	if exists {
		return nil, errors.New("user already registered with this contact")
	}

	// Always invalidate any existing OTP for this contact to allow fresh start
	// This handles the case where user verified OTP but abandoned registration
	err = s.userRepo.InvalidateExistingOTPs(contact, "registration")
	if err != nil {
		fmt.Printf("Warning: Failed to invalidate existing OTPs for %s: %v\n", contact, err)
		// Continue anyway - don't fail the request
	}

	// Generate OTP
	otp := s.generateOTP()
	expiresAt := time.Now().Add(5 * time.Minute) // 5 minutes expiry

	// Store OTP
	otpVerification := &models.OTPVerification{
		Contact:     contact,
		ContactType: contactType,
		OTP:         otp,
		ExpiresAt:   expiresAt,
		Purpose:     "registration",
	}

	if err := s.userRepo.CreateOTPVerification(otpVerification); err != nil {
		return nil, fmt.Errorf("failed to create OTP verification: %w", err)
	}

	// Send OTP (mock for now)
	if err := s.sendOTP(contact, contactType, otp); err != nil {
		return nil, fmt.Errorf("failed to send OTP: %w", err)
	}

	return &OTPResponse{
		Message:     "OTP sent successfully",
		Contact:     contact,
		ContactType: contactType,
		ExpiresIn:   300, // 5 minutes
	}, nil
}

// VerifyOTP verifies an OTP without completing registration.
// This allows users to verify their contact information before providing full registration details.
func (s *AuthService) VerifyOTP(req *VerifyOTPRequest) (*VerifyOTPResponse, error) {
	// Validate input
	contact, contactType, err := s.validateContactInput(req.Email, req.PhoneNumber)
	if err != nil {
		return nil, err
	}

	// Get OTP verification record
	otpVerification, err := s.userRepo.GetOTPVerification(contact, "registration")
	if err != nil {
		return nil, fmt.Errorf("failed to get OTP verification: %w", err)
	}
	if otpVerification == nil {
		return nil, errors.New("invalid or expired OTP")
	}

	// Check if already verified
	if otpVerification.Verified {
		return nil, errors.New("OTP already verified")
	}

	// Validate OTP (allow default "123456" for testing)
	if req.OTP != "123456" && otpVerification.OTP != req.OTP {
		return nil, errors.New("invalid OTP")
	}

	// Generate session token for secure registration completion
	sessionToken, err := generateSecureToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate session token: %w", err)
	}

	// Mark OTP as verified and add session token
	if err := s.userRepo.MarkOTPVerifiedWithToken(otpVerification.ID, sessionToken); err != nil {
		return nil, fmt.Errorf("failed to mark OTP as verified: %w", err)
	}

	// Extend OTP expiry to 30 minutes from verification for registration completion
	newExpiry := time.Now().Add(30 * time.Minute)
	if err := s.userRepo.ExtendOTPExpiry(otpVerification.ID, newExpiry); err != nil {
		fmt.Printf("Warning: Failed to extend OTP expiry: %v\n", err)
	}

	// Calculate remaining time for registration (30 minutes from verification)
	registrationTTL := int64(1800) // 30 minutes in seconds

	return &VerifyOTPResponse{
		Message:         "OTP verified successfully",
		Contact:         contact,
		ContactType:     contactType,
		VerifiedAt:      time.Now().Format(time.RFC3339),
		SessionToken:    sessionToken,
		RegistrationTTL: registrationTTL,
	}, nil
}

func (s *AuthService) CompleteRegistration(req *CompleteRegistrationRequest) (*AuthResponse, error) {
	// Validate input
	contact, contactType, err := s.validateContactInput(req.Email, req.PhoneNumber)
	if err != nil {
		return nil, err
	}

	// Check for verified OTP with matching session token
	otpVerification, err := s.userRepo.GetVerifiedOTPWithToken(contact, "registration", req.SessionToken)
	if err != nil {
		return nil, fmt.Errorf("failed to get verified OTP: %w", err)
	}
	if otpVerification == nil {
		return nil, errors.New("invalid session token or no verified OTP found - please verify your OTP first using /auth/verify-otp")
	}

	// Validate password
	if !utils.IsValidPassword(req.Password) {
		return nil, errors.New("password must be at least 8 characters with uppercase, lowercase, number and special character")
	}

	// Generate unique 9-digit username with retry logic
	var username string
	maxRetries := 10
	for i := 0; i < maxRetries; i++ {
		generatedUsername, err := utils.GenerateUniqueUsername()
		if err != nil {
			return nil, fmt.Errorf("failed to generate username: %w", err)
		}

		// Validate that the generated username is unique
		ctx, cancel := context.WithTimeout(context.Background(), s.config.Timeouts.HTTP)
		err = s.userClient.ValidateUsername(ctx, generatedUsername)
		cancel()

		if err == nil {
			// Username is available
			username = generatedUsername
			break
		}

		if !strings.Contains(err.Error(), "username already taken") {
			// Unexpected error, not a collision
			return nil, fmt.Errorf("username validation failed: %w", err)
		}

		// Username collision, try again
		fmt.Printf("Username collision for %s, retrying... (attempt %d/%d)\n", generatedUsername, i+1, maxRetries)
	}

	if username == "" {
		return nil, errors.New("failed to generate unique username after multiple attempts")
	}

	// Pre-validate email with user service to avoid creating auth user if conflicts exist
	ctx, cancel := context.WithTimeout(context.Background(), s.config.Timeouts.HTTP)
	defer cancel()

	if contactType == "email" {
		if err := s.userClient.ValidateEmail(ctx, contact); err != nil {
			if strings.Contains(err.Error(), "email already exists") {
				return nil, errors.New("an account with this email already exists")
			}
			return nil, fmt.Errorf("email validation failed: %w", err)
		}
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create auth user with auto-generated username
	authUser := &models.AuthUser{
		User: sharedModels.User{
			FirstName: req.FirstName,
			LastName:  req.LastName,
			Username:  username, // Use the auto-generated username
		},
		PasswordHash: string(hashedPassword),
	}

	if contactType == "email" {
		authUser.Email = contact
		authUser.EmailVerified = true
	} else {
		authUser.PhoneNumber = contact
		authUser.PhoneVerified = true
	}

	// Step 1: Create auth user
	if err := s.userRepo.Create(authUser); err != nil {
		return nil, fmt.Errorf("failed to create authentication record: %w", err)
	}

	// Step 2: Create user profile
	userProfile := clients.CreateUserProfileRequest{
		UserID:        authUser.ID,
		Email:         authUser.Email,
		PhoneNumber:   authUser.PhoneNumber,
		FirstName:     authUser.FirstName,
		LastName:      authUser.LastName,
		Username:      authUser.Username,
		EmailVerified: authUser.EmailVerified,
		PhoneVerified: authUser.PhoneVerified,
	}

	ctx, cancel = context.WithTimeout(context.Background(), s.config.Timeouts.HTTP)
	defer cancel()

	err = s.userClient.CreateUserProfile(ctx, userProfile)
	if err != nil {
		// ROLLBACK: Delete auth user if profile creation fails
		if deleteErr := s.userRepo.Delete(authUser.ID); deleteErr != nil {
			fmt.Printf("CRITICAL: Failed to rollback auth user %s after profile creation failure: %v\n", authUser.ID, deleteErr)
		}

		// Return user-friendly error based on error type
		if strings.Contains(err.Error(), "temporarily unavailable") {
			return nil, errors.New("registration service is temporarily unavailable, please try again")
		}
		if strings.Contains(err.Error(), "username already taken") {
			return nil, errors.New("username is already taken")
		}
		if strings.Contains(err.Error(), "email already exists") {
			return nil, errors.New("an account with this email already exists")
		}
		if strings.Contains(err.Error(), "conflict") {
			return nil, errors.New("registration data conflict, please try again")
		}

		fmt.Printf("Registration failed for user %s: %v\n", authUser.ID, err)
		return nil, errors.New("registration failed, please try again")
	}

	// Step 3: Mark OTP as used (best effort)
	if err := s.userRepo.MarkOTPUsed(otpVerification.ID); err != nil {
		fmt.Printf("Warning: Failed to mark OTP as used for user %s: %v\n", authUser.ID, err)
	}

	// Success: Generate auth response
	fmt.Printf("METRIC: registration_success user_id=%s username=%s contact_type=%s\n",
		authUser.ID, authUser.Username, contactType)

	return s.generateAuthResponse(authUser)
}

func (s *AuthService) LoginWithContact(req *LoginWithContactRequest) (*AuthResponse, error) {
	// Validate that exactly one contact method is provided
	if req.Email != "" && req.PhoneNumber != "" {
		return nil, errors.New("provide either email or phone number, not both")
	}

	if req.Email == "" && req.PhoneNumber == "" {
		return nil, errors.New("email or phone number is required")
	}

	var user *models.AuthUser
	var err error

	if req.Email != "" {
		if !utils.IsValidEmail(req.Email) {
			return nil, ErrInvalidContactFormat
		}
		user, err = s.userRepo.GetByEmail(req.Email)
	} else {
		if !utils.IsValidPhoneNumber(req.PhoneNumber) {
			return nil, ErrInvalidContactFormat
		}
		user, err = s.userRepo.GetByPhoneNumber(req.PhoneNumber)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("invalid credentials")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, errors.New("invalid credentials")
	}

	return s.generateAuthResponse(user)
}

// generateOTP creates a 6-digit OTP - returns "123456" for easy testing
func (s *AuthService) generateOTP() string {
	return "123456"
}

func (s *AuthService) sendOTP(contact, contactType, otp string) error {
	// Mock implementation - always use "123456" for easy testing
	fmt.Printf("🔐 OTP sent to %s (%s): %s\n", contact, contactType, otp)
	fmt.Printf("💡 You can use OTP '123456' for testing\n")

	// TODO: Integrate with actual SMS/Email services
	// For email: Use email service
	// For SMS: Use SMS service from notification service

	return nil
}

func (s *AuthService) RefreshToken(refreshToken string) (*AuthResponse, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(refreshToken, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil || !token.Valid {
		return nil, errors.New("invalid refresh token")
	}

	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, errors.New("invalid user ID in token")
	}

	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	return s.generateAuthResponse(user)
}

func (s *AuthService) Logout(tokenHash string) error {
	return s.userRepo.DeleteSession(tokenHash)
}

func (s *AuthService) VerifyEmail(token string) error {
	verification, err := s.userRepo.GetEmailVerificationByToken(token)
	if err != nil {
		return fmt.Errorf("failed to get email verification: %w", err)
	}
	if verification == nil {
		return errors.New("invalid or expired verification token")
	}

	if err := s.userRepo.SetEmailVerified(verification.UserID); err != nil {
		return fmt.Errorf("failed to set email verified: %w", err)
	}

	if err := s.userRepo.DeleteEmailVerification(token); err != nil {
		// Log error but don't fail the verification
		fmt.Printf("Failed to delete email verification: %v\n", err)
	}

	return nil
}

func (s *AuthService) ForgotPassword(email string) error {
	user, err := s.userRepo.GetByEmail(email)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		// Don't reveal if email exists
		return nil
	}

	token, err := generateSecureToken()
	if err != nil {
		return fmt.Errorf("failed to generate token: %w", err)
	}

	reset := &models.PasswordReset{
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: time.Now().Add(s.config.JWT.PasswordResetDuration),
	}

	if err := s.userRepo.CreatePasswordReset(reset); err != nil {
		return fmt.Errorf("failed to create password reset: %w", err)
	}

	// TODO: Send password reset email
	fmt.Printf("Password reset token for %s: %s\n", email, token)

	return nil
}

func (s *AuthService) ResetPassword(token, newPassword string) error {
	if !utils.IsValidPassword(newPassword) {
		return errors.New("password must be at least 8 characters with uppercase, lowercase, number and special character")
	}

	reset, err := s.userRepo.GetPasswordResetByToken(token)
	if err != nil {
		return fmt.Errorf("failed to get password reset: %w", err)
	}
	if reset == nil {
		return errors.New("invalid or expired reset token")
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	reset.User.PasswordHash = string(hashedPassword)
	if err := s.userRepo.Update(&reset.User); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	if err := s.userRepo.MarkPasswordResetUsed(token); err != nil {
		// Log error but don't fail the reset
		fmt.Printf("Failed to mark password reset as used: %v\n", err)
	}

	// Invalidate all user sessions
	if err := s.userRepo.DeleteUserSessions(reset.UserID); err != nil {
		fmt.Printf("Failed to delete user sessions: %v\n", err)
	}

	return nil
}

func (s *AuthService) ValidateToken(tokenString string) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil || !token.Valid {
		return nil, errors.New("invalid token")
	}

	return claims, nil
}

func (s *AuthService) generateAuthResponse(user *models.AuthUser) (*AuthResponse, error) {
	accessToken, err := s.generateAccessToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := s.generateRefreshToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Store session
	tokenHash := hashToken(accessToken)
	session := &models.Session{
		UserID:    user.ID,
		TokenHash: tokenHash,
		ExpiresAt: time.Now().Add(s.config.JWT.AccessTokenDuration),
	}

	if err := s.userRepo.CreateSession(session); err != nil {
		// Log error but don't fail auth
		fmt.Printf("Failed to create session: %v\n", err)
	}

	return &AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.config.JWT.AccessTokenDuration.Seconds()),
		User:         s.convertToAPIUser(user),
	}, nil
}

// Helper function to extract email
func (s *AuthService) extractEmail(email string) string {
	return email
}

// Helper function to extract phone number
func (s *AuthService) extractPhoneNumber(phoneNumber string) string {
	return phoneNumber
}

// convertToAPIUser converts models.AuthUser to APIUser for API responses
func (s *AuthService) convertToAPIUser(user *models.AuthUser) *APIUser {
	return &APIUser{
		ID:            user.ID.String(),
		Email:         s.extractEmail(user.Email),
		PhoneNumber:   s.extractPhoneNumber(user.PhoneNumber),
		EmailVerified: user.EmailVerified,
		PhoneVerified: user.PhoneVerified,
		FirstName:     user.FirstName,
		LastName:      user.LastName,
		Username:      user.Username,
		Avatar:        user.ProfileImage,
		Bio:           user.Bio,
		Location:      user.Location,
		Website:       user.Website,
		CreatedAt:     user.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     user.UpdatedAt.Format(time.RFC3339),
	}
}

func (s *AuthService) generateAccessToken(user *models.AuthUser) (string, error) {
	// Determine user roles
	roles := []string{"user"} // Default role
	if s.extractEmail(user.Email) == "<EMAIL>" {
		roles = []string{"user", "admin"}
	}

	claims := &Claims{
		UserID: user.ID.String(),
		Email:  s.extractEmail(user.Email),
		Roles:  roles,
		Teams:  []string{}, // Will be populated by team service
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.config.JWT.AccessTokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "swork-auth",
			Subject:   user.ID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.JWT.Secret))
}

func (s *AuthService) generateRefreshToken(user *models.AuthUser) (string, error) {
	claims := &Claims{
		UserID: user.ID.String(),
		Email:  s.extractEmail(user.Email),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.config.JWT.RefreshTokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "swork-auth",
			Subject:   user.ID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.JWT.Secret))
}

func (s *AuthService) sendEmailVerification(user *models.AuthUser) error {
	token, err := generateSecureToken()
	if err != nil {
		return fmt.Errorf("failed to generate token: %w", err)
	}

	verification := &models.EmailVerification{
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: time.Now().Add(s.config.JWT.EmailVerificationDuration),
	}

	if err := s.userRepo.CreateEmailVerification(verification); err != nil {
		return fmt.Errorf("failed to create email verification: %w", err)
	}

	// TODO: Send actual email
	fmt.Printf("Email verification token for %s: %s\n", user.Email, token)

	return nil
}

func generateSecureToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// secureCompareStrings performs a time-constant comparison to prevent timing attacks
func secureCompareStrings(a, b string) bool {
	return subtle.ConstantTimeCompare([]byte(a), []byte(b)) == 1
}

// validateContactInput validates and extracts contact information from request
func (s *AuthService) validateContactInput(email, phoneNumber string) (contact, contactType string, err error) {
	if email != "" && phoneNumber != "" {
		return "", "", ErrMultipleContacts
	}

	if email == "" && phoneNumber == "" {
		return "", "", ErrContactRequired
	}

	if email != "" {
		if !utils.IsValidEmail(email) {
			return "", "", ErrInvalidContactFormat
		}
		return email, "email", nil
	}

	if !utils.IsValidPhoneNumber(phoneNumber) {
		return "", "", ErrInvalidContactFormat
	}
	return phoneNumber, "phone", nil
}

// StartBackgroundCleanup starts background jobs to clean up expired records
func (s *AuthService) StartBackgroundCleanup() {
	ticker := time.NewTicker(1 * time.Hour) // Run every hour
	go func() {
		for {
			select {
			case <-ticker.C:
				s.performCleanup()
			}
		}
	}()
}

// performCleanup removes expired records from the database
func (s *AuthService) performCleanup() {
	// Clean up expired OTPs
	if err := s.userRepo.CleanupExpiredOTPs(); err != nil {
		fmt.Printf("Failed to cleanup expired OTPs: %v\n", err)
	}

	// Clean up expired sessions
	if err := s.userRepo.CleanupExpiredSessions(); err != nil {
		fmt.Printf("Failed to cleanup expired sessions: %v\n", err)
	}

	// Clean up expired email verifications
	if err := s.userRepo.CleanupExpiredEmailVerifications(); err != nil {
		fmt.Printf("Failed to cleanup expired email verifications: %v\n", err)
	}

	// Clean up expired password resets
	if err := s.userRepo.CleanupExpiredPasswordResets(); err != nil {
		fmt.Printf("Failed to cleanup expired password resets: %v\n", err)
	}
}

// DEBUG METHODS - Only for development
// GetOTPVerification gets OTP verification for debugging
func (s *AuthService) GetOTPVerification(contact, purpose string) (*models.OTPVerification, error) {
	return s.userRepo.GetOTPVerification(contact, purpose)
}

// ClearOTPVerification clears OTP verification for debugging
func (s *AuthService) ClearOTPVerification(contact, purpose string) error {
	return s.userRepo.InvalidateExistingOTPs(contact, purpose)
}
