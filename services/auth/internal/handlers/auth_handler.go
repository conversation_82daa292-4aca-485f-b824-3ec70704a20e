package handlers

import (
	"crypto/sha256"
	"encoding/hex"
	"math"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/auth/internal/services"
)

type AuthHandler struct {
	authService *services.AuthService
}

func NewAuthHandler(authService *services.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// @Summary Refresh access token
// @Description Generate a new access token using a valid refresh token. Use this when the access token expires.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body object{refresh_token=string} true "Refresh token"
// @Success 200 {object} object{data=services.AuthResponse} "Token refreshed successfully"
// @Failure 400 {object} object "Invalid request format"
// @Failure 401 {object} object "Invalid or expired refresh token"
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	authResponse, err := h.authService.RefreshToken(req.RefreshToken)
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid refresh token", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Token refreshed successfully", authResponse)
}

// @Summary User logout
// @Description Invalidate the current user session and access token. Requires valid Bearer token in Authorization header.
// @Tags Authentication
// @Security BearerAuth
// @Success 200 {object} object "Logout successful"
// @Failure 401 {object} object "Invalid or missing authorization token"
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Authorization header required", nil)
		return
	}

	bearerToken := strings.Split(authHeader, " ")
	if len(bearerToken) != 2 {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid authorization format", nil)
		return
	}

	tokenHash := hashToken(bearerToken[1])
	if err := h.authService.Logout(tokenHash); err != nil {
		utils.ErrorResponseWithLog(c, http.StatusInternalServerError, "Failed to logout", err, "logout")
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Logout successful", nil, "logout")
	utils.LogSecurityEvent(c, "user_logout", true)
}

// NOTE: Email verification removed - now handled by OTP flow in registration

// @Summary Request password reset
// @Description Send a password reset link to the user's email address. If the email exists, a reset link will be sent.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body object{email=string} true "User email address"
// @Success 200 {object} object "Password reset link sent (if email exists)"
// @Failure 400 {object} object "Invalid request format or email"
// @Router /auth/forgot-password [post]
func (h *AuthHandler) ForgotPassword(c *gin.Context) {
	var req struct {
		Email string `json:"email" binding:"required,email"`
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	if err := h.authService.ForgotPassword(req.Email); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to process request", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "If the email exists, a password reset link has been sent", nil)
}

// @Summary Reset password
// @Description Reset user password using a valid reset token received via email. The token expires after a certain period.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body object{token=string,new_password=string} true "Reset token and new password"
// @Success 200 {object} object "Password reset successfully"
// @Failure 400 {object} object "Invalid request, token, or password requirements"
// @Router /auth/reset-password [post]
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	var req struct {
		Token       string `json:"token" binding:"required"`
		NewPassword string `json:"new_password" binding:"required,min=8"`
	}

	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	if err := h.authService.ResetPassword(req.Token, req.NewPassword); err != nil {
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, err.Error(), err, "password_reset")
		utils.LogSecurityEvent(c, "password_reset_attempt", false,
			logger.F("failure_reason", err.Error()))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Password reset successfully", nil, "password_reset")
	utils.LogSecurityEvent(c, "password_reset_attempt", true)
}

// @Summary Validate JWT token
// @Description Validate the provided JWT access token and return token claims. Use this to verify token validity and get user information.
// @Tags Authentication
// @Security BearerAuth
// @Success 200 {object} object{data=object} "Token is valid"
// @Failure 401 {object} object "Invalid or expired token"
// @Router /auth/validate [get]
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Authorization header required", nil)
		return
	}

	bearerToken := strings.Split(authHeader, " ")
	if len(bearerToken) != 2 {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid authorization format", nil)
		return
	}

	claims, err := h.authService.ValidateToken(bearerToken[1])
	if err != nil {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid token", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Token is valid", claims)
}

// @Summary Send OTP for user registration
// @Description Send a one-time password (OTP) to email or phone number to begin user registration. The OTP expires after 5 minutes.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body services.SendOTPRequest true "Contact information (email or phone number)"
// @Success 200 {object} object{data=services.OTPResponse} "OTP sent successfully"
// @Failure 400 {object} object "Invalid request format or contact information"
// @Failure 409 {object} object "User already exists with this contact"
// @Router /auth/send-otp [post]
func (h *AuthHandler) SendOTP(c *gin.Context) {
	var req services.SendOTPRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	otpResponse, err := h.authService.SendOTP(&req)
	if err != nil {
		if strings.Contains(err.Error(), "already registered") {
			utils.ErrorResponse(c, http.StatusConflict, err.Error(), nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "OTP sent successfully", otpResponse)
}

// @Summary Verify OTP for registration
// @Description Verify a one-time password (OTP) before completing registration. This step must be completed before calling complete-registration.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body services.VerifyOTPRequest true "OTP verification data (email or phone number + OTP)"
// @Success 200 {object} object{data=services.VerifyOTPResponse} "OTP verified successfully"
// @Failure 400 {object} object "Invalid request format, contact information, or OTP"
// @Router /auth/verify-otp [post]
func (h *AuthHandler) VerifyOTP(c *gin.Context) {
	var req services.VerifyOTPRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	verifyResponse, err := h.authService.VerifyOTP(&req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "OTP verified successfully", verifyResponse)
}

// @Summary Complete user registration
// @Description Complete user registration with user details. OTP must be verified first using /auth/verify-otp. Username is auto-generated as a unique 9-digit number. Returns authentication tokens upon success.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body services.CompleteRegistrationRequest true "User registration details (OTP must be verified first, username auto-generated)"
// @Success 201 {object} object{data=services.AuthResponse} "Registration completed successfully"
// @Failure 400 {object} object "Invalid request format, unverified OTP, or user data"
// @Failure 500 {object} object "Failed to generate unique username"
// @Router /auth/complete-registration [post]
func (h *AuthHandler) CompleteRegistration(c *gin.Context) {
	var req services.CompleteRegistrationRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	authResponse, err := h.authService.CompleteRegistration(&req)
	if err != nil {
		contact := req.Email
		if contact == "" {
			contact = req.PhoneNumber
		}
		if strings.Contains(err.Error(), "failed to generate unique username") {
			utils.ErrorResponseWithLog(c, http.StatusInternalServerError, err.Error(), err,
				"registration", logger.F("contact", contact))
			return
		}
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, err.Error(), err,
			"registration", logger.F("contact", contact))
		return
	}

	contact := req.Email
	if contact == "" {
		contact = req.PhoneNumber
	}
	utils.SuccessResponseWithLog(c, http.StatusCreated, "Registration completed successfully", authResponse,
		"registration",
		logger.F("user_id", authResponse.User.ID),
		logger.F("contact", contact),
		logger.F("first_name", req.FirstName),
		logger.F("last_name", req.LastName))
	utils.LogSecurityEvent(c, "user_registration", true,
		logger.F("user_id", authResponse.User.ID), logger.F("contact", contact))
}

// @Summary User login
// @Description Authenticate user with email or phone number and password. Returns JWT tokens upon successful authentication.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body services.LoginWithContactRequest true "Login credentials (email or phone + password)"
// @Success 200 {object} object{data=services.AuthResponse} "Login successful"
// @Failure 400 {object} object "Invalid request format or credentials"
// @Failure 401 {object} object "Authentication failed"
// @Router /auth/login [post]
func (h *AuthHandler) LoginWithContact(c *gin.Context) {
	var req services.LoginWithContactRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	authResponse, err := h.authService.LoginWithContact(&req)
	if err != nil {
		contact := req.Email
		if contact == "" {
			contact = req.PhoneNumber
		}
		if strings.Contains(err.Error(), "invalid credentials") {
			utils.ErrorResponseWithLog(c, http.StatusUnauthorized, "Invalid credentials", nil,
				"login", logger.F("contact", contact))
			utils.LogSecurityEvent(c, "login_attempt", false,
				logger.F("contact", contact), logger.F("failure_reason", "invalid_credentials"))
			return
		}
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, err.Error(), err,
			"login", logger.F("contact", contact))
		return
	}

	contact := req.Email
	if contact == "" {
		contact = req.PhoneNumber
	}
	utils.SuccessResponseWithLog(c, http.StatusOK, "Login successful", authResponse,
		"login", logger.F("user_id", authResponse.User.ID), logger.F("contact", contact))
	utils.LogSecurityEvent(c, "login_attempt", true,
		logger.F("user_id", authResponse.User.ID), logger.F("contact", contact))
}

func hashToken(token string) string {
	// SECURITY: Properly hash tokens to prevent exposure in logs
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// DEBUG ENDPOINTS - Only for development
// @Summary Debug: Get OTP status
// @Description Get current OTP verification status for a contact (development only)
// @Tags Debug
// @Param contact query string true "Email or phone number"
// @Success 200 {object} object
// @Router /auth/debug/otp-status [get]
func (h *AuthHandler) DebugGetOTPStatus(c *gin.Context) {
	// Only allow in development environment
	if os.Getenv("APP_ENV") != "development" {
		utils.ErrorResponse(c, http.StatusForbidden, "Debug endpoints only available in development", nil)
		return
	}

	contact := c.Query("contact")
	if contact == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "contact parameter required", nil)
		return
	}

	otpVerification, err := h.authService.GetOTPVerification(contact, "registration")
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get OTP status", err)
		return
	}

	if otpVerification == nil {
		utils.SuccessResponse(c, http.StatusOK, "No OTP found", map[string]interface{}{
			"contact": contact,
			"status":  "no_otp_found",
		})
		return
	}

	remainingTime := otpVerification.ExpiresAt.Sub(time.Now()).Seconds()
	status := "unknown"

	if remainingTime <= 0 {
		status = "expired"
	} else if otpVerification.Used {
		status = "used"
	} else if otpVerification.Verified {
		status = "verified_unused"
	} else {
		status = "sent_unverified"
	}

	utils.SuccessResponse(c, http.StatusOK, "OTP status retrieved", map[string]interface{}{
		"contact":           contact,
		"status":            status,
		"verified":          otpVerification.Verified,
		"used":              otpVerification.Used,
		"expires_at":        otpVerification.ExpiresAt,
		"remaining_seconds": math.Max(0, remainingTime),
		"has_session_token": otpVerification.SessionToken != "",
	})
}

// @Summary Debug: Clear OTP
// @Description Clear OTP verification for a contact (development only)
// @Tags Debug
// @Param contact query string true "Email or phone number"
// @Success 200 {object} object
// @Router /auth/debug/clear-otp [delete]
func (h *AuthHandler) DebugClearOTP(c *gin.Context) {
	// Only allow in development environment
	if os.Getenv("APP_ENV") != "development" {
		utils.ErrorResponse(c, http.StatusForbidden, "Debug endpoints only available in development", nil)
		return
	}

	contact := c.Query("contact")
	if contact == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "contact parameter required", nil)
		return
	}

	err := h.authService.ClearOTPVerification(contact, "registration")
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to clear OTP", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "OTP cleared successfully", map[string]interface{}{
		"contact": contact,
		"action":  "cleared",
	})
}
