package config

import (
	"errors"
	"log"
	"time"

	"github.com/swork-team/platform/pkg/config"
)

// Config extends the base configuration with Auth-specific settings
type Config struct {
	config.BaseServiceConfig
	JWT      JWTConfig
	Email    EmailConfig
	Timeouts config.TimeoutConfig
	Cache    config.CacheTTLConfig
}

type JWTConfig struct {
	Secret                    string
	AccessTokenDuration       time.Duration
	RefreshTokenDuration      time.Duration
	EmailVerificationDuration time.Duration
	PasswordResetDuration     time.Duration
}

type EmailConfig struct {
	SMTPHost     string
	SMTPPort     int
	SMTPUser     string
	SMTPPassword string
	FromEmail    string
	FromName     string
}

func LoadConfig() *Config {
	// Load base configuration
	baseConfig := config.LoadBaseConfig("AUTH")
	
	cfg := &Config{
		BaseServiceConfig: *baseConfig,
		JWT: JWTConfig{
			Secret:                    config.GetEnv("JWT_SECRET", ""),
			AccessTokenDuration:       config.GetDurationEnv("JWT_ACCESS_DURATION", 15*time.Minute),
			RefreshTokenDuration:      config.GetDurationEnv("JWT_REFRESH_DURATION", 7*24*time.Hour),
			EmailVerificationDuration: config.GetDurationEnv("JWT_EMAIL_VERIFICATION_DURATION", 24*time.Hour),
			PasswordResetDuration:     config.GetDurationEnv("JWT_PASSWORD_RESET_DURATION", time.Hour),
		},
		Email: EmailConfig{
			SMTPHost:     config.GetEnv("SMTP_HOST", "smtp.gmail.com"),
			SMTPPort:     config.GetIntEnv("SMTP_PORT", 587),
			SMTPUser:     config.GetEnv("SMTP_USER", ""),
			SMTPPassword: config.GetEnv("SMTP_PASS", ""),
			FromEmail:    config.GetEnv("FROM_EMAIL", "<EMAIL>"),
			FromName:     config.GetEnv("FROM_NAME", "Swork Team"),
		},
		Timeouts: config.NewTimeoutConfig(),
		Cache:    config.NewCacheTTLConfig(),
	}
	
	// Validate required configuration
	if err := validateConfig(cfg); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}
	
	return cfg
}

// validateConfig checks that all required configuration is present
func validateConfig(cfg *Config) error {
	if cfg.JWT.Secret == "" {
		return errors.New("JWT_SECRET is required but not set")
	}
	if len(cfg.JWT.Secret) < 32 {
		return errors.New("JWT_SECRET must be at least 32 characters long")
	}
	return nil
}
