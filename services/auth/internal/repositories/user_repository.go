package repositories

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/auth/internal/models"
	"gorm.io/gorm"
)

type UserRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

func (r *UserRepository) Create(user *models.AuthUser) error {
	return r.db.Create(user).Error
}

func (r *UserRepository) GetByID(id uuid.UUID) (*models.AuthUser, error) {
	var user models.AuthUser
	err := r.db.Where("id = ? AND deleted_at IS NULL", id).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetByEmail(email string) (*models.AuthUser, error) {
	var user models.AuthUser
	err := r.db.Where("email = ? AND deleted_at IS NULL", email).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetByUsername(username string) (*models.AuthUser, error) {
	var user models.AuthUser
	err := r.db.Where("username = ? AND deleted_at IS NULL", username).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) Update(user *models.AuthUser) error {
	return r.db.Save(user).Error
}

func (r *UserRepository) Delete(id uuid.UUID) error {
	return r.db.Delete(&models.AuthUser{}, id).Error
}

// EmailExists checks if an email address is already registered.
// Uses EXISTS query for better performance than COUNT.
func (r *UserRepository) EmailExists(email string) (bool, error) {
	var exists bool
	err := r.db.Model(&models.AuthUser{}).
		Select("1").
		Where("email = ? AND deleted_at IS NULL", email).
		Limit(1).
		Find(&exists).Error
	return exists, err
}

// UsernameExists checks if a username is already taken.
// Uses EXISTS query for better performance than COUNT.
func (r *UserRepository) UsernameExists(username string) (bool, error) {
	var exists bool
	err := r.db.Model(&models.AuthUser{}).
		Select("1").
		Where("username = ? AND deleted_at IS NULL", username).
		Limit(1).
		Find(&exists).Error
	return exists, err
}

func (r *UserRepository) GetByPhoneNumber(phoneNumber string) (*models.AuthUser, error) {
	var user models.AuthUser
	err := r.db.Where("phone_number = ? AND deleted_at IS NULL", phoneNumber).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) PhoneNumberExists(phoneNumber string) (bool, error) {
	var count int64
	err := r.db.Model(&models.AuthUser{}).Where("phone_number = ?", phoneNumber).Count(&count).Error
	return count > 0, err
}

func (r *UserRepository) ContactExists(contact string) (bool, error) {
	var count int64
	err := r.db.Model(&models.AuthUser{}).Where("email = ? OR phone_number = ?", contact, contact).Count(&count).Error
	return count > 0, err
}

func (r *UserRepository) SetPhoneVerified(userID uuid.UUID) error {
	return r.db.Model(&models.AuthUser{}).Where("id = ?", userID).Update("phone_verified", true).Error
}

func (r *UserRepository) SetEmailVerified(userID uuid.UUID) error {
	return r.db.Model(&models.AuthUser{}).Where("id = ?", userID).Update("email_verified", true).Error
}

// Session methods
func (r *UserRepository) CreateSession(session *models.Session) error {
	return r.db.Create(session).Error
}

func (r *UserRepository) GetSessionByTokenHash(tokenHash string) (*models.Session, error) {
	var session models.Session
	err := r.db.Preload("User").Where("token_hash = ? AND expires_at > ?", tokenHash, time.Now()).First(&session).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

func (r *UserRepository) DeleteSession(tokenHash string) error {
	return r.db.Where("token_hash = ?", tokenHash).Delete(&models.Session{}).Error
}

func (r *UserRepository) DeleteUserSessions(userID uuid.UUID) error {
	return r.db.Where("user_id = ?", userID).Delete(&models.Session{}).Error
}

func (r *UserRepository) CleanupExpiredSessions() error {
	return r.db.Where("expires_at < ?", time.Now()).Delete(&models.Session{}).Error
}

// Email verification methods
func (r *UserRepository) CreateEmailVerification(verification *models.EmailVerification) error {
	return r.db.Create(verification).Error
}

func (r *UserRepository) GetEmailVerificationByToken(token string) (*models.EmailVerification, error) {
	var verification models.EmailVerification
	err := r.db.Preload("User").Where("token = ? AND expires_at > ?", token, time.Now()).First(&verification).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &verification, nil
}

func (r *UserRepository) DeleteEmailVerification(token string) error {
	return r.db.Where("token = ?", token).Delete(&models.EmailVerification{}).Error
}

func (r *UserRepository) CleanupExpiredEmailVerifications() error {
	return r.db.Where("expires_at < ?", time.Now()).Delete(&models.EmailVerification{}).Error
}

// Password reset methods
func (r *UserRepository) CreatePasswordReset(reset *models.PasswordReset) error {
	return r.db.Create(reset).Error
}

func (r *UserRepository) GetPasswordResetByToken(token string) (*models.PasswordReset, error) {
	var reset models.PasswordReset
	err := r.db.Preload("User").Where("token = ? AND expires_at > ? AND used = false", token, time.Now()).First(&reset).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &reset, nil
}

func (r *UserRepository) MarkPasswordResetUsed(token string) error {
	return r.db.Model(&models.PasswordReset{}).Where("token = ?", token).Update("used", true).Error
}

func (r *UserRepository) CleanupExpiredPasswordResets() error {
	return r.db.Where("expires_at < ?", time.Now()).Delete(&models.PasswordReset{}).Error
}

// OTP verification methods
func (r *UserRepository) CreateOTPVerification(otp *models.OTPVerification) error {
	return r.db.Create(otp).Error
}

func (r *UserRepository) GetOTPVerification(contact, purpose string) (*models.OTPVerification, error) {
	var otp models.OTPVerification
	err := r.db.Where("contact = ? AND purpose = ? AND expires_at > ? AND used = false", contact, purpose, time.Now()).First(&otp).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &otp, nil
}

func (r *UserRepository) MarkOTPUsed(id uuid.UUID) error {
	return r.db.Model(&models.OTPVerification{}).Where("id = ?", id).Update("used", true).Error
}

// MarkOTPVerified marks an OTP as successfully verified
func (r *UserRepository) MarkOTPVerified(id uuid.UUID) error {
	now := time.Now()
	return r.db.Model(&models.OTPVerification{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"verified":    true,
			"verified_at": &now,
		}).Error
}

// MarkOTPVerifiedWithToken marks an OTP as verified and adds session token
func (r *UserRepository) MarkOTPVerifiedWithToken(id uuid.UUID, sessionToken string) error {
	now := time.Now()
	return r.db.Model(&models.OTPVerification{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"verified":      true,
			"verified_at":   &now,
			"session_token": sessionToken,
		}).Error
}

// GetVerifiedOTP gets a verified OTP that hasn't been used yet
func (r *UserRepository) GetVerifiedOTP(contact, purpose string) (*models.OTPVerification, error) {
	var otp models.OTPVerification
	err := r.db.Where("contact = ? AND purpose = ? AND verified = true AND used = false AND expires_at > ?",
		contact, purpose, time.Now()).First(&otp).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &otp, nil
}

// GetVerifiedOTPWithToken gets a verified OTP with matching session token
func (r *UserRepository) GetVerifiedOTPWithToken(contact, purpose, sessionToken string) (*models.OTPVerification, error) {
	var otp models.OTPVerification
	err := r.db.Where("contact = ? AND purpose = ? AND session_token = ? AND verified = true AND used = false AND expires_at > ?",
		contact, purpose, sessionToken, time.Now()).First(&otp).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &otp, nil
}

// ExtendOTPExpiry extends the expiry time of an OTP (used after verification)
func (r *UserRepository) ExtendOTPExpiry(id uuid.UUID, newExpiry time.Time) error {
	return r.db.Model(&models.OTPVerification{}).
		Where("id = ?", id).
		Update("expires_at", newExpiry).Error
}

// DeleteOTPVerification deletes a specific OTP verification record
func (r *UserRepository) DeleteOTPVerification(id uuid.UUID) error {
	return r.db.Delete(&models.OTPVerification{}, id).Error
}

// InvalidateExistingOTPs invalidates all existing OTPs for a contact and purpose
// This allows users to restart the flow even if they have verified but unused OTPs
func (r *UserRepository) InvalidateExistingOTPs(contact, purpose string) error {
	return r.db.Where("contact = ? AND purpose = ?", contact, purpose).
		Delete(&models.OTPVerification{}).Error
}

func (r *UserRepository) CleanupExpiredOTPs() error {
	return r.db.Where("expires_at < ?", time.Now()).Delete(&models.OTPVerification{}).Error
}
