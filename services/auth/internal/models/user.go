package models

import (
	"time"

	"github.com/google/uuid"
	sharedModels "github.com/swork-team/platform/pkg/models"
	"gorm.io/gorm"
)

// AuthUser embeds the shared User model and adds auth-specific fields
type AuthUser struct {
	sharedModels.User
	PasswordHash string `json:"-"`
}

// TableName is inherited from embedded User model - no need to override

type Session struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primaryKey"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	User      AuthUser  `json:"user" gorm:"foreignKey:UserID"`
	TokenHash string    `json:"-" gorm:"not null"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// BeforeCreate hook for UUID generation
func (s *Session) BeforeCreate(tx *gorm.DB) (err error) {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return
}

type EmailVerification struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primaryKey"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	User      AuthUser  `json:"user" gorm:"foreignKey:UserID"`
	Token     string    `json:"token" gorm:"not null;index"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// BeforeCreate hook for UUID generation
func (e *EmailVerification) BeforeCreate(tx *gorm.DB) (err error) {
	if e.ID == uuid.Nil {
		e.ID = uuid.New()
	}
	return
}

type PasswordReset struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primaryKey"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	User      AuthUser  `json:"user" gorm:"foreignKey:UserID"`
	Token     string    `json:"token" gorm:"not null;index"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	Used      bool      `json:"used" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// BeforeCreate hook for UUID generation
func (p *PasswordReset) BeforeCreate(tx *gorm.DB) (err error) {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return
}

type OTPVerification struct {
	ID           uuid.UUID  `json:"id" gorm:"type:uuid;primaryKey"`
	Contact      string     `json:"contact" gorm:"not null;index"` // email or phone number
	ContactType  string     `json:"contact_type" gorm:"not null"`  // "email" or "phone"
	OTP          string     `json:"otp" gorm:"not null"`
	ExpiresAt    time.Time  `json:"expires_at" gorm:"not null"`
	Used         bool       `json:"used" gorm:"default:false"`
	Verified     bool       `json:"verified" gorm:"default:false"` // NEW: tracks if OTP was successfully verified
	VerifiedAt   *time.Time `json:"verified_at"`                   // NEW: when OTP was verified
	SessionToken string     `json:"session_token"`                 // NEW: random token for completing registration
	Purpose      string     `json:"purpose" gorm:"not null"`       // "registration" or "login"
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// BeforeCreate hook for UUID generation
func (o *OTPVerification) BeforeCreate(tx *gorm.DB) (err error) {
	if o.ID == uuid.Nil {
		o.ID = uuid.New()
	}
	return
}
