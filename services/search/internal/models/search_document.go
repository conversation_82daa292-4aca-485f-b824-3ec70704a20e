package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SearchDocumentType represents the type of content being indexed
type SearchDocumentType string

const (
	DocumentTypeUser         SearchDocumentType = "user"
	DocumentTypeTeam         SearchDocumentType = "team"
	DocumentTypePost         SearchDocumentType = "post"
	DocumentTypeComment      SearchDocumentType = "comment"
	DocumentTypeMessage      SearchDocumentType = "message"
	DocumentTypeFile         SearchDocumentType = "file"
	DocumentTypeFolder       SearchDocumentType = "folder"
	DocumentTypeEvent        SearchDocumentType = "event"
	DocumentTypeNotification SearchDocumentType = "notification"
)

// SearchVisibility represents who can see this content in search results
type SearchVisibility string

const (
	VisibilityPublic    SearchVisibility = "public"
	VisibilityPrivate   SearchVisibility = "private"
	VisibilityTeam      SearchVisibility = "team"
	VisibilityFriends   SearchVisibility = "friends"
	VisibilityRestricted SearchVisibility = "restricted"
)

// Metadata represents flexible metadata storage
type Metadata map[string]interface{}

// Value implements driver.Valuer for database storage
func (m Metadata) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

// Scan implements sql.Scanner for database retrieval
func (m *Metadata) Scan(value interface{}) error {
	if value == nil {
		*m = nil
		return nil
	}
	
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into Metadata", value)
	}
	
	return json.Unmarshal(bytes, m)
}

// Vector represents embedding vector storage
type Vector []float32

// Value implements driver.Valuer for database storage
func (v Vector) Value() (driver.Value, error) {
	if v == nil {
		return nil, nil
	}
	return json.Marshal(v)
}

// Scan implements sql.Scanner for database retrieval
func (v *Vector) Scan(value interface{}) error {
	if value == nil {
		*v = nil
		return nil
	}
	
	var bytes []byte
	switch val := value.(type) {
	case []byte:
		bytes = val
	case string:
		bytes = []byte(val)
	default:
		return fmt.Errorf("cannot scan %T into Vector", value)
	}
	
	return json.Unmarshal(bytes, v)
}

// SearchDocument represents a unified document in the search index
type SearchDocument struct {
	ID           uuid.UUID            `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	Type         SearchDocumentType   `json:"type" gorm:"not null;index"`
	Title        string               `json:"title" gorm:"not null"`
	Content      string               `json:"content" gorm:"type:text"`
	Summary      string               `json:"summary" gorm:"type:text"`
	Tags         []string             `json:"tags" gorm:"type:text[]"`
	Metadata     Metadata             `json:"metadata" gorm:"type:jsonb"`
	
	// Source information
	SourceID     string               `json:"source_id" gorm:"not null;index"`
	SourceType   string               `json:"source_type" gorm:"not null;index"`
	SourceURL    string               `json:"source_url"`
	
	// Access control
	UserID       uuid.UUID            `json:"user_id" gorm:"type:uuid;not null;index"`
	TeamID       *uuid.UUID           `json:"team_id,omitempty" gorm:"type:uuid;index"`
	Visibility   SearchVisibility     `json:"visibility" gorm:"not null;index"`
	
	// Search optimization
	Vector       Vector               `json:"vector,omitempty" gorm:"type:jsonb"`
	SearchScore  float64              `json:"search_score,omitempty" gorm:"-"`
	Highlights   []string             `json:"highlights,omitempty" gorm:"-"`
	
	// Timestamps
	CreatedAt    time.Time            `json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`
	IndexedAt    time.Time            `json:"indexed_at"`
	DeletedAt    gorm.DeletedAt       `json:"-" gorm:"index"`
}

// BeforeCreate hook for UUID generation
func (sd *SearchDocument) BeforeCreate(tx *gorm.DB) (err error) {
	if sd.ID == uuid.Nil {
		sd.ID = uuid.New()
	}
	sd.IndexedAt = time.Now()
	return
}

// BeforeUpdate hook
func (sd *SearchDocument) BeforeUpdate(tx *gorm.DB) (err error) {
	sd.IndexedAt = time.Now()
	return
}

// TableName specifies the table name for GORM
func (SearchDocument) TableName() string {
	return "search_documents"
}

// GetContentForEmbedding returns the text content that should be used for generating embeddings
func (sd *SearchDocument) GetContentForEmbedding() string {
	content := sd.Title
	if sd.Content != "" {
		content += " " + sd.Content
	}
	if sd.Summary != "" {
		content += " " + sd.Summary
	}
	for _, tag := range sd.Tags {
		content += " " + tag
	}
	return content
}

// IsAccessibleBy checks if the document is accessible by the given user
func (sd *SearchDocument) IsAccessibleBy(userID uuid.UUID, userTeams []uuid.UUID) bool {
	switch sd.Visibility {
	case VisibilityPublic:
		return true
	case VisibilityPrivate:
		return sd.UserID == userID
	case VisibilityTeam:
		if sd.TeamID == nil {
			return false
		}
		for _, teamID := range userTeams {
			if teamID == *sd.TeamID {
				return true
			}
		}
		return false
	case VisibilityFriends:
		// TODO: Implement friends check
		return sd.UserID == userID
	case VisibilityRestricted:
		// TODO: Implement restricted access logic
		return sd.UserID == userID
	default:
		return false
	}
}
