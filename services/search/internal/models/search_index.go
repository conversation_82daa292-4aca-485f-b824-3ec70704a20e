package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// IndexStatus represents the status of an index operation
type IndexStatus string

const (
	IndexStatusPending    IndexStatus = "pending"
	IndexStatusProcessing IndexStatus = "processing"
	IndexStatusCompleted  IndexStatus = "completed"
	IndexStatusFailed     IndexStatus = "failed"
	IndexStatusSkipped    IndexStatus = "skipped"
)

// SearchIndex tracks indexing operations and status
type SearchIndex struct {
	ID           uuid.UUID   `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	DocumentID   uuid.UUID   `json:"document_id" gorm:"type:uuid;not null;index"`
	SourceType   string      `json:"source_type" gorm:"not null;index"`
	SourceID     string      `json:"source_id" gorm:"not null;index"`
	Status       IndexStatus `json:"status" gorm:"not null;index"`
	ErrorMessage string      `json:"error_message,omitempty"`
	RetryCount   int         `json:"retry_count" gorm:"default:0"`
	ProcessedAt  *time.Time  `json:"processed_at,omitempty"`
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`

	// Relationship
	Document SearchDocument `json:"document,omitempty" gorm:"foreignKey:DocumentID"`
}

// BeforeCreate hook for UUID generation
func (si *SearchIndex) BeforeCreate(tx *gorm.DB) (err error) {
	if si.ID == uuid.Nil {
		si.ID = uuid.New()
	}
	return
}

// TableName specifies the table name for GORM
func (SearchIndex) TableName() string {
	return "search_indexes"
}

// MarkAsProcessing updates the status to processing
func (si *SearchIndex) MarkAsProcessing() {
	si.Status = IndexStatusProcessing
	now := time.Now()
	si.ProcessedAt = &now
}

// MarkAsCompleted updates the status to completed
func (si *SearchIndex) MarkAsCompleted() {
	si.Status = IndexStatusCompleted
	now := time.Now()
	si.ProcessedAt = &now
}

// MarkAsFailed updates the status to failed with error message
func (si *SearchIndex) MarkAsFailed(errorMsg string) {
	si.Status = IndexStatusFailed
	si.ErrorMessage = errorMsg
	si.RetryCount++
	now := time.Now()
	si.ProcessedAt = &now
}

// CanRetry checks if the index operation can be retried
func (si *SearchIndex) CanRetry() bool {
	return si.RetryCount < 3 && si.Status == IndexStatusFailed
}
