package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SearchQuery represents a search query for analytics and caching
type SearchQuery struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	UserID       uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Query        string    `json:"query" gorm:"not null"`
	QueryHash    string    `json:"query_hash" gorm:"not null;index"` // For deduplication
	Filters      Metadata  `json:"filters" gorm:"type:jsonb"`
	ResultCount  int       `json:"result_count" gorm:"default:0"`
	ResponseTime int64     `json:"response_time" gorm:"default:0"` // in milliseconds
	CreatedAt    time.Time `json:"created_at"`
}

// BeforeCreate hook for UUID generation
func (sq *SearchQuery) BeforeCreate(tx *gorm.DB) (err error) {
	if sq.ID == uuid.Nil {
		sq.ID = uuid.New()
	}
	return
}

// TableName specifies the table name for GORM
func (SearchQuery) TableName() string {
	return "search_queries"
}
