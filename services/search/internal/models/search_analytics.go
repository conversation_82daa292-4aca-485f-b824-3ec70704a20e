package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SearchAnalytics represents search analytics data
type SearchAnalytics struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primaryKey;default:gen_random_uuid()"`
	Date            time.Time `json:"date" gorm:"not null;index"`
	TotalQueries    int64     `json:"total_queries" gorm:"default:0"`
	UniqueUsers     int64     `json:"unique_users" gorm:"default:0"`
	AvgResponseTime float64   `json:"avg_response_time" gorm:"default:0"`
	TopQueries      Metadata  `json:"top_queries" gorm:"type:jsonb"`
	TopTypes        Metadata  `json:"top_types" gorm:"type:jsonb"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// BeforeCreate hook for UUID generation
func (sa *SearchAnalytics) BeforeCreate(tx *gorm.DB) (err error) {
	if sa.ID == uuid.Nil {
		sa.ID = uuid.New()
	}
	return
}

// TableName specifies the table name for GORM
func (SearchAnalytics) TableName() string {
	return "search_analytics"
}
