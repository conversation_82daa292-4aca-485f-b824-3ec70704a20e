package domain

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/search/internal/models"
)

// SearchRequest represents a unified search request
type SearchRequest struct {
	Query      string                      `json:"query" binding:"required"`
	Types      []models.SearchDocumentType `json:"types,omitempty"`
	Filters    SearchFilters               `json:"filters,omitempty"`
	Sort       SearchSort                  `json:"sort,omitempty"`
	Pagination SearchPagination            `json:"pagination,omitempty"`
	Options    SearchOptions               `json:"options,omitempty"`
}

// SearchFilters represents search filtering options
type SearchFilters struct {
	UserID     *uuid.UUID                 `json:"user_id,omitempty"`
	TeamID     *uuid.UUID                 `json:"team_id,omitempty"`
	DateRange  *DateRange                 `json:"date_range,omitempty"`
	Visibility []models.SearchVisibility  `json:"visibility,omitempty"`
	Tags       []string                   `json:"tags,omitempty"`
	Metadata   map[string]interface{}     `json:"metadata,omitempty"`
}

// DateRange represents a date range filter
type DateRange struct {
	Start *time.Time `json:"start,omitempty"`
	End   *time.Time `json:"end,omitempty"`
}

// SearchSort represents sorting options
type SearchSort struct {
	Field string `json:"field,omitempty"` // "relevance", "date", "title"
	Order string `json:"order,omitempty"` // "asc", "desc"
}

// SearchPagination represents pagination options
type SearchPagination struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// SearchOptions represents additional search options
type SearchOptions struct {
	IncludeHighlights bool    `json:"include_highlights,omitempty"`
	IncludeFacets     bool    `json:"include_facets,omitempty"`
	MinScore          float64 `json:"min_score,omitempty"`
	SemanticSearch    bool    `json:"semantic_search,omitempty"`
	HybridSearch      bool    `json:"hybrid_search,omitempty"`
}

// SearchResponse represents a unified search response
type SearchResponse struct {
	Results    []SearchResult     `json:"results"`
	Total      int64              `json:"total"`
	Facets     map[string]Facet   `json:"facets,omitempty"`
	Query      string             `json:"query"`
	Took       int64              `json:"took"` // milliseconds
	Pagination SearchPagination   `json:"pagination"`
}

// SearchResult represents a single search result
type SearchResult struct {
	ID         string                    `json:"id"`
	Type       models.SearchDocumentType `json:"type"`
	Title      string                    `json:"title"`
	Content    string                    `json:"content"`
	Summary    string                    `json:"summary"`
	Tags       []string                  `json:"tags"`
	Source     SearchResultSource        `json:"source"`
	Score      float64                   `json:"score"`
	Highlights []string                  `json:"highlights,omitempty"`
	Metadata   map[string]interface{}    `json:"metadata,omitempty"`
	CreatedAt  time.Time                 `json:"created_at"`
	UpdatedAt  time.Time                 `json:"updated_at"`
}

// SearchResultSource represents the source of a search result
type SearchResultSource struct {
	Service string `json:"service"`
	ID      string `json:"id"`
	URL     string `json:"url"`
}

// Facet represents search facets for filtering
type Facet struct {
	Values []FacetValue `json:"values"`
}

// FacetValue represents a facet value with count
type FacetValue struct {
	Value string `json:"value"`
	Count int64  `json:"count"`
}

// Validate validates the search request
func (sr *SearchRequest) Validate() error {
	if strings.TrimSpace(sr.Query) == "" {
		return fmt.Errorf("query cannot be empty")
	}

	if len(sr.Query) > 1000 {
		return fmt.Errorf("query too long (max 1000 characters)")
	}

	// Validate pagination
	if sr.Pagination.Limit < 0 {
		return fmt.Errorf("limit cannot be negative")
	}
	if sr.Pagination.Offset < 0 {
		return fmt.Errorf("offset cannot be negative")
	}
	if sr.Pagination.Limit > 100 {
		return fmt.Errorf("limit cannot exceed 100")
	}

	// Validate sort
	if sr.Sort.Field != "" {
		validFields := []string{"relevance", "date", "title"}
		valid := false
		for _, field := range validFields {
			if sr.Sort.Field == field {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid sort field: %s", sr.Sort.Field)
		}
	}

	if sr.Sort.Order != "" && sr.Sort.Order != "asc" && sr.Sort.Order != "desc" {
		return fmt.Errorf("invalid sort order: %s", sr.Sort.Order)
	}

	return nil
}

// SetDefaults sets default values for the search request
func (sr *SearchRequest) SetDefaults() {
	if sr.Pagination.Limit == 0 {
		sr.Pagination.Limit = 20
	}
	if sr.Sort.Field == "" {
		sr.Sort.Field = "relevance"
	}
	if sr.Sort.Order == "" {
		sr.Sort.Order = "desc"
	}
	if sr.Options.SemanticSearch && !sr.Options.HybridSearch {
		sr.Options.HybridSearch = true // Enable hybrid by default for semantic search
	}
}
