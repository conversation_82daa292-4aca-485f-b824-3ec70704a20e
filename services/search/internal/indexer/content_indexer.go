package indexer

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/search/internal/embedding"
	"github.com/swork-team/platform/services/search/internal/models"
	"github.com/swork-team/platform/services/search/internal/repository"
	"github.com/swork-team/platform/services/search/internal/vector"
)

// ContentIndexer handles indexing of content from various services
type ContentIndexer struct {
	repo            *repository.SearchRepository
	embeddingService *embedding.EmbeddingService
	vectorService   *vector.VectorService
	batchSize       int
}

// NewContentIndexer creates a new content indexer
func NewContentIndexer(
	repo *repository.SearchRepository,
	embeddingService *embedding.EmbeddingService,
	vectorService *vector.VectorService,
	batchSize int,
) *ContentIndexer {
	if batchSize <= 0 {
		batchSize = 100
	}
	
	return &ContentIndexer{
		repo:            repo,
		embeddingService: embeddingService,
		vectorService:   vectorService,
		batchSize:       batchSize,
	}
}

// IndexDocument indexes a single document
func (ci *ContentIndexer) IndexDocument(ctx context.Context, doc *models.SearchDocument) error {
	// Generate embedding if not present
	if len(doc.Vector) == 0 {
		content := doc.GetContentForEmbedding()
		if content == "" {
			return fmt.Errorf("no content to index for document %s", doc.ID)
		}
		
		vector, err := ci.embeddingService.GenerateEmbedding(ctx, content)
		if err != nil {
			return fmt.Errorf("failed to generate embedding: %w", err)
		}
		doc.Vector = vector
	}
	
	// Save to database
	if err := ci.repo.UpdateDocument(ctx, doc); err != nil {
		return fmt.Errorf("failed to save document to database: %w", err)
	}
	
	// Index in vector store
	if err := ci.vectorService.UpsertDocument(ctx, doc); err != nil {
		return fmt.Errorf("failed to index document in vector store: %w", err)
	}
	
	log.Printf("Successfully indexed document %s (type: %s)", doc.ID, doc.Type)
	return nil
}

// IndexDocuments indexes multiple documents in batches
func (ci *ContentIndexer) IndexDocuments(ctx context.Context, docs []*models.SearchDocument) error {
	if len(docs) == 0 {
		return nil
	}
	
	// Process in batches
	for i := 0; i < len(docs); i += ci.batchSize {
		end := i + ci.batchSize
		if end > len(docs) {
			end = len(docs)
		}
		
		batch := docs[i:end]
		if err := ci.indexBatch(ctx, batch); err != nil {
			return fmt.Errorf("failed to index batch %d-%d: %w", i, end, err)
		}
		
		log.Printf("Indexed batch %d-%d (%d documents)", i, end, len(batch))
	}
	
	return nil
}

// indexBatch indexes a batch of documents
func (ci *ContentIndexer) indexBatch(ctx context.Context, docs []*models.SearchDocument) error {
	// Prepare texts for embedding generation
	var textsToEmbed []string
	var docsNeedingEmbedding []*models.SearchDocument
	
	for _, doc := range docs {
		if len(doc.Vector) == 0 {
			content := doc.GetContentForEmbedding()
			if content != "" {
				textsToEmbed = append(textsToEmbed, content)
				docsNeedingEmbedding = append(docsNeedingEmbedding, doc)
			}
		}
	}
	
	// Generate embeddings in batch
	if len(textsToEmbed) > 0 {
		embeddings, err := ci.embeddingService.GenerateEmbeddings(ctx, textsToEmbed)
		if err != nil {
			return fmt.Errorf("failed to generate embeddings: %w", err)
		}
		
		// Assign embeddings to documents
		for i, doc := range docsNeedingEmbedding {
			if i < len(embeddings) {
				doc.Vector = embeddings[i]
			}
		}
	}
	
	// Update documents in database
	for _, doc := range docs {
		if err := ci.repo.UpdateDocument(ctx, doc); err != nil {
			log.Printf("Failed to update document %s in database: %v", doc.ID, err)
			continue
		}
	}
	
	// Index in vector store
	if err := ci.vectorService.UpsertDocuments(ctx, docs); err != nil {
		return fmt.Errorf("failed to index documents in vector store: %w", err)
	}
	
	return nil
}

// ProcessPendingIndexes processes pending index operations
func (ci *ContentIndexer) ProcessPendingIndexes(ctx context.Context) error {
	indexes, err := ci.repo.GetPendingIndexes(ctx, ci.batchSize)
	if err != nil {
		return fmt.Errorf("failed to get pending indexes: %w", err)
	}
	
	if len(indexes) == 0 {
		return nil
	}
	
	log.Printf("Processing %d pending indexes", len(indexes))
	
	for _, index := range indexes {
		if err := ci.processIndex(ctx, index); err != nil {
			log.Printf("Failed to process index %s: %v", index.ID, err)
			index.MarkAsFailed(err.Error())
		} else {
			index.MarkAsCompleted()
		}
		
		// Update index status
		if err := ci.repo.UpdateIndex(ctx, index); err != nil {
			log.Printf("Failed to update index status %s: %v", index.ID, err)
		}
	}
	
	return nil
}

// processIndex processes a single index operation
func (ci *ContentIndexer) processIndex(ctx context.Context, index *models.SearchIndex) error {
	index.MarkAsProcessing()
	
	// Get the document
	doc, err := ci.repo.GetDocumentByID(ctx, index.DocumentID)
	if err != nil {
		return fmt.Errorf("failed to get document: %w", err)
	}
	
	// Index the document
	return ci.IndexDocument(ctx, doc)
}

// ReindexAll reindexes all documents
func (ci *ContentIndexer) ReindexAll(ctx context.Context) error {
	log.Println("Starting full reindex...")
	
	offset := 0
	limit := ci.batchSize
	
	for {
		// Get documents that need indexing
		docs, err := ci.repo.GetDocumentsForIndexing(ctx, limit)
		if err != nil {
			return fmt.Errorf("failed to get documents for indexing: %w", err)
		}
		
		if len(docs) == 0 {
			break
		}
		
		// Index the batch
		if err := ci.IndexDocuments(ctx, docs); err != nil {
			return fmt.Errorf("failed to index documents: %w", err)
		}
		
		offset += len(docs)
		log.Printf("Reindexed %d documents so far", offset)
		
		// If we got fewer documents than the limit, we're done
		if len(docs) < limit {
			break
		}
	}
	
	log.Printf("Full reindex completed. Total documents: %d", offset)
	return nil
}

// DeleteDocument removes a document from the index
func (ci *ContentIndexer) DeleteDocument(ctx context.Context, documentID uuid.UUID) error {
	// Delete from vector store
	if err := ci.vectorService.DeleteDocument(ctx, documentID.String()); err != nil {
		log.Printf("Failed to delete document %s from vector store: %v", documentID, err)
	}
	
	// Delete from database
	if err := ci.repo.DeleteDocument(ctx, documentID); err != nil {
		return fmt.Errorf("failed to delete document from database: %w", err)
	}
	
	log.Printf("Successfully deleted document %s", documentID)
	return nil
}

// DeleteDocumentBySource removes a document by source type and ID
func (ci *ContentIndexer) DeleteDocumentBySource(ctx context.Context, sourceType, sourceID string) error {
	// Get the document first to get its UUID
	doc, err := ci.repo.GetDocumentBySource(ctx, sourceType, sourceID)
	if err != nil {
		return fmt.Errorf("failed to get document by source: %w", err)
	}
	
	return ci.DeleteDocument(ctx, doc.ID)
}

// GetIndexingStats returns indexing statistics
func (ci *ContentIndexer) GetIndexingStats(ctx context.Context) (IndexingStats, error) {
	pendingIndexes, err := ci.repo.GetPendingIndexes(ctx, 1000) // Get up to 1000 pending
	if err != nil {
		return IndexingStats{}, fmt.Errorf("failed to get pending indexes: %w", err)
	}
	
	return IndexingStats{
		PendingIndexes: len(pendingIndexes),
		LastProcessed:  time.Now(), // TODO: Track this properly
	}, nil
}

// IndexingStats represents indexing statistics
type IndexingStats struct {
	PendingIndexes int       `json:"pending_indexes"`
	LastProcessed  time.Time `json:"last_processed"`
}
