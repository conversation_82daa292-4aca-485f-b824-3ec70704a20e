package indexer

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/search/internal/models"
)

// DocumentFactory creates search documents from various source types
type DocumentFactory struct{}

// NewDocumentFactory creates a new document factory
func NewDocumentFactory() *DocumentFactory {
	return &DocumentFactory{}
}

// CreateUserDocument creates a search document from user data
func (df *DocumentFactory) CreateUserDocument(userID uuid.UUID, data map[string]interface{}) (*models.SearchDocument, error) {
	firstName, _ := data["first_name"].(string)
	lastName, _ := data["last_name"].(string)
	username, _ := data["username"].(string)
	email, _ := data["email"].(string)
	bio, _ := data["bio"].(string)
	location, _ := data["location"].(string)
	
	title := strings.TrimSpace(firstName + " " + lastName)
	if title == "" {
		title = username
	}
	
	content := fmt.Sprintf("%s %s %s", username, email, bio)
	summary := bio
	if len(summary) > 200 {
		summary = summary[:200] + "..."
	}
	
	tags := []string{"user", "profile"}
	if location != "" {
		tags = append(tags, "location:"+location)
	}
	
	return &models.SearchDocument{
		Type:       models.DocumentTypeUser,
		Title:      title,
		Content:    content,
		Summary:    summary,
		Tags:       tags,
		SourceID:   userID.String(),
		SourceType: "user",
		SourceURL:  fmt.Sprintf("/api/v1/users/%s", userID),
		UserID:     userID,
		Visibility: models.VisibilityPublic, // Users are generally public
		Metadata:   data,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}, nil
}

// CreateTeamDocument creates a search document from team data
func (df *DocumentFactory) CreateTeamDocument(teamID uuid.UUID, ownerID uuid.UUID, data map[string]interface{}) (*models.SearchDocument, error) {
	name, _ := data["name"].(string)
	description, _ := data["description"].(string)
	category, _ := data["category"].(string)
	visibility, _ := data["visibility"].(string)
	
	content := fmt.Sprintf("%s %s", name, description)
	summary := description
	if len(summary) > 200 {
		summary = summary[:200] + "..."
	}
	
	tags := []string{"team", "workspace"}
	if category != "" {
		tags = append(tags, "category:"+category)
	}
	
	searchVisibility := models.VisibilityPublic
	if visibility == "private" {
		searchVisibility = models.VisibilityTeam
	}
	
	return &models.SearchDocument{
		Type:       models.DocumentTypeTeam,
		Title:      name,
		Content:    content,
		Summary:    summary,
		Tags:       tags,
		SourceID:   teamID.String(),
		SourceType: "team",
		SourceURL:  fmt.Sprintf("/api/v1/teams/%s", teamID),
		UserID:     ownerID,
		TeamID:     &teamID,
		Visibility: searchVisibility,
		Metadata:   data,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}, nil
}

// CreatePostDocument creates a search document from social post data
func (df *DocumentFactory) CreatePostDocument(postID, userID string, teamID *string, data map[string]interface{}) (*models.SearchDocument, error) {
	content, _ := data["content"].(string)
	postType, _ := data["type"].(string)
	visibility, _ := data["visibility"].(string)
	location, _ := data["location"].(string)
	
	title := "Social Post"
	if postType != "" {
		title = fmt.Sprintf("%s Post", strings.Title(postType))
	}
	
	summary := content
	if len(summary) > 200 {
		summary = summary[:200] + "..."
	}
	
	tags := []string{"post", "social"}
	if postType != "" {
		tags = append(tags, "type:"+postType)
	}
	if location != "" {
		tags = append(tags, "location:"+location)
	}
	
	searchVisibility := models.VisibilityPublic
	switch visibility {
	case "private":
		searchVisibility = models.VisibilityPrivate
	case "friends":
		searchVisibility = models.VisibilityFriends
	case "team":
		searchVisibility = models.VisibilityTeam
	}
	
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	
	doc := &models.SearchDocument{
		Type:       models.DocumentTypePost,
		Title:      title,
		Content:    content,
		Summary:    summary,
		Tags:       tags,
		SourceID:   postID,
		SourceType: "social_post",
		SourceURL:  fmt.Sprintf("/api/v1/social/posts/%s", postID),
		UserID:     userUUID,
		Visibility: searchVisibility,
		Metadata:   data,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
	
	if teamID != nil && *teamID != "" {
		teamUUID, err := uuid.Parse(*teamID)
		if err == nil {
			doc.TeamID = &teamUUID
		}
	}
	
	return doc, nil
}

// CreateMessageDocument creates a search document from message data
func (df *DocumentFactory) CreateMessageDocument(messageID, userID string, data map[string]interface{}) (*models.SearchDocument, error) {
	content, _ := data["content"].(string)
	conversationID, _ := data["conversation_id"].(string)
	messageType, _ := data["type"].(string)
	
	title := "Message"
	if messageType != "" {
		title = fmt.Sprintf("%s Message", strings.Title(messageType))
	}
	
	summary := content
	if len(summary) > 200 {
		summary = summary[:200] + "..."
	}
	
	tags := []string{"message", "chat"}
	if messageType != "" {
		tags = append(tags, "type:"+messageType)
	}
	
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	
	return &models.SearchDocument{
		Type:       models.DocumentTypeMessage,
		Title:      title,
		Content:    content,
		Summary:    summary,
		Tags:       tags,
		SourceID:   messageID,
		SourceType: "message",
		SourceURL:  fmt.Sprintf("/api/v1/messaging/messages/%s", messageID),
		UserID:     userUUID,
		Visibility: models.VisibilityPrivate, // Messages are private by default
		Metadata:   data,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}, nil
}

// CreateFileDocument creates a search document from file data
func (df *DocumentFactory) CreateFileDocument(fileID, userID string, teamID *string, data map[string]interface{}) (*models.SearchDocument, error) {
	name, _ := data["name"].(string)
	path, _ := data["path"].(string)
	mimeType, _ := data["mime_type"].(string)
	size, _ := data["size"].(float64)
	visibility, _ := data["visibility"].(string)
	
	content := fmt.Sprintf("%s %s", name, path)
	summary := fmt.Sprintf("File: %s (%.2f KB)", name, size/1024)
	
	tags := []string{"file", "document"}
	if mimeType != "" {
		tags = append(tags, "type:"+mimeType)
	}
	
	searchVisibility := models.VisibilityPrivate
	switch visibility {
	case "public":
		searchVisibility = models.VisibilityPublic
	case "team":
		searchVisibility = models.VisibilityTeam
	}
	
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	
	doc := &models.SearchDocument{
		Type:       models.DocumentTypeFile,
		Title:      name,
		Content:    content,
		Summary:    summary,
		Tags:       tags,
		SourceID:   fileID,
		SourceType: "file",
		SourceURL:  fmt.Sprintf("/api/v1/drive/files/%s", fileID),
		UserID:     userUUID,
		Visibility: searchVisibility,
		Metadata:   data,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
	
	if teamID != nil && *teamID != "" {
		teamUUID, err := uuid.Parse(*teamID)
		if err == nil {
			doc.TeamID = &teamUUID
		}
	}
	
	return doc, nil
}

// CreateEventDocument creates a search document from calendar event data
func (df *DocumentFactory) CreateEventDocument(eventID, userID string, data map[string]interface{}) (*models.SearchDocument, error) {
	title, _ := data["title"].(string)
	description, _ := data["description"].(string)
	location, _ := data["location"].(string)
	eventType, _ := data["event_type"].(string)
	
	content := fmt.Sprintf("%s %s %s", title, description, location)
	summary := description
	if len(summary) > 200 {
		summary = summary[:200] + "..."
	}
	
	tags := []string{"event", "calendar"}
	if eventType != "" {
		tags = append(tags, "type:"+eventType)
	}
	if location != "" {
		tags = append(tags, "location:"+location)
	}
	
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	
	return &models.SearchDocument{
		Type:       models.DocumentTypeEvent,
		Title:      title,
		Content:    content,
		Summary:    summary,
		Tags:       tags,
		SourceID:   eventID,
		SourceType: "event",
		SourceURL:  fmt.Sprintf("/api/v1/calendar/events/%s", eventID),
		UserID:     userUUID,
		Visibility: models.VisibilityPrivate, // Events are private by default
		Metadata:   data,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}, nil
}
