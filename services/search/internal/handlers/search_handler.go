package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/search/internal/domain"
	"github.com/swork-team/platform/services/search/internal/models"
	"github.com/swork-team/platform/services/search/internal/service"
)

// SearchHandler handles search-related HTTP requests
type SearchHandler struct {
	searchService *service.SearchService
}

// NewSearchHandler creates a new search handler
func NewSearchHandler(searchService *service.SearchService) *SearchHandler {
	return &SearchHandler{
		searchService: searchService,
	}
}

// Search handles unified search requests
// @Summary Unified search across all content types
// @Description Performs semantic and text-based search across users, teams, posts, messages, files, events, and notifications
// @Tags search
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body domain.SearchRequest true "Search request"
// @Success 200 {object} domain.SearchResponse
// @Failure 400 {object} utils.ErrorResponse
// @Failure 401 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /search [post]
func (h *SearchHandler) Search(c *gin.Context) {
	// Get authenticated user
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}
	
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}
	
	// Parse request
	var req domain.SearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}
	
	// Get user teams (from context or service call)
	userTeams := h.getUserTeams(c, userID)
	
	// Perform search
	response, err := h.searchService.Search(c.Request.Context(), &req, userID, userTeams)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Search failed", err)
		return
	}
	
	utils.SuccessResponse(c, http.StatusOK, "Search completed successfully", response)
}

// QuickSearch handles quick search requests with query parameters
// @Summary Quick search with query parameters
// @Description Performs a quick search using query parameters instead of JSON body
// @Tags search
// @Security BearerAuth
// @Param q query string true "Search query"
// @Param types query string false "Comma-separated list of content types to search"
// @Param limit query int false "Number of results to return" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Param semantic query bool false "Enable semantic search" default(false)
// @Param highlights query bool false "Include highlights in results" default(true)
// @Success 200 {object} domain.SearchResponse
// @Failure 400 {object} utils.ErrorResponse
// @Failure 401 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /search/quick [get]
func (h *SearchHandler) QuickSearch(c *gin.Context) {
	// Get authenticated user
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}
	
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", err)
		return
	}
	
	// Parse query parameters
	query := c.Query("q")
	if query == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Query parameter 'q' is required", nil)
		return
	}
	
	// Build search request from query parameters
	req := domain.SearchRequest{
		Query: query,
		Pagination: domain.SearchPagination{
			Limit:  h.getIntParam(c, "limit", 20),
			Offset: h.getIntParam(c, "offset", 0),
		},
		Options: domain.SearchOptions{
			SemanticSearch:    h.getBoolParam(c, "semantic", false),
			IncludeHighlights: h.getBoolParam(c, "highlights", true),
			IncludeFacets:     h.getBoolParam(c, "facets", false),
		},
	}
	
	// Parse types filter
	if typesStr := c.Query("types"); typesStr != "" {
		typeStrs := strings.Split(typesStr, ",")
		req.Types = make([]models.SearchDocumentType, 0, len(typeStrs))
		for _, typeStr := range typeStrs {
			req.Types = append(req.Types, models.SearchDocumentType(strings.TrimSpace(typeStr)))
		}
	}
	
	// Get user teams
	userTeams := h.getUserTeams(c, userID)
	
	// Perform search
	response, err := h.searchService.Search(c.Request.Context(), &req, userID, userTeams)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Search failed", err)
		return
	}
	
	utils.SuccessResponse(c, http.StatusOK, "Search completed successfully", response)
}

// Suggest handles search suggestions/autocomplete
// @Summary Get search suggestions
// @Description Get search suggestions based on partial query
// @Tags search
// @Security BearerAuth
// @Param q query string true "Partial search query"
// @Param limit query int false "Number of suggestions to return" default(10)
// @Success 200 {object} []string
// @Failure 400 {object} utils.ErrorResponse
// @Failure 401 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /search/suggest [get]
func (h *SearchHandler) Suggest(c *gin.Context) {
	// Get authenticated user
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}
	
	query := c.Query("q")
	if query == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Query parameter 'q' is required", nil)
		return
	}
	
	limit := h.getIntParam(c, "limit", 10)
	
	// TODO: Implement search suggestions
	// This could be based on:
	// - Popular search queries
	// - User's search history
	// - Content titles/tags that match the partial query
	
	suggestions := []string{
		query + " documents",
		query + " messages",
		query + " events",
		query + " in team",
		query + " from last week",
	}
	
	if len(suggestions) > limit {
		suggestions = suggestions[:limit]
	}
	
	utils.SuccessResponse(c, http.StatusOK, "Suggestions retrieved successfully", suggestions)
}

// GetSearchStats handles search statistics requests
// @Summary Get search statistics
// @Description Get search analytics and statistics
// @Tags search
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /search/stats [get]
func (h *SearchHandler) GetSearchStats(c *gin.Context) {
	// Get authenticated user
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}
	
	// TODO: Implement search statistics
	// This could include:
	// - Total searches performed
	// - Popular search terms
	// - Search performance metrics
	// - Content type distribution
	
	stats := map[string]interface{}{
		"total_searches":    1000,
		"avg_response_time": 150, // milliseconds
		"popular_terms":     []string{"meeting", "project", "document", "team"},
		"content_types": map[string]int{
			"files":    450,
			"messages": 300,
			"events":   150,
			"posts":    100,
		},
	}
	
	utils.SuccessResponse(c, http.StatusOK, "Search statistics retrieved successfully", stats)
}

// Health handles health check requests
// @Summary Health check
// @Description Check if the search service is healthy
// @Tags search
// @Success 200 {object} map[string]string
// @Router /search/health [get]
func (h *SearchHandler) Health(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Search service is healthy", map[string]string{
		"service": "search-service",
		"status":  "healthy",
	})
}

// Helper methods

func (h *SearchHandler) getUserTeams(c *gin.Context, userID uuid.UUID) []uuid.UUID {
	// Try to get teams from context (set by middleware)
	if teams, exists := c.Get("user_teams"); exists {
		if teamSlice, ok := teams.([]string); ok {
			userTeams := make([]uuid.UUID, 0, len(teamSlice))
			for _, teamStr := range teamSlice {
				if teamID, err := uuid.Parse(teamStr); err == nil {
					userTeams = append(userTeams, teamID)
				}
			}
			return userTeams
		}
	}
	
	// TODO: If not in context, fetch from team service
	// For now, return empty slice
	return []uuid.UUID{}
}

func (h *SearchHandler) getIntParam(c *gin.Context, key string, defaultValue int) int {
	if str := c.Query(key); str != "" {
		if val, err := strconv.Atoi(str); err == nil {
			return val
		}
	}
	return defaultValue
}

func (h *SearchHandler) getBoolParam(c *gin.Context, key string, defaultValue bool) bool {
	if str := c.Query(key); str != "" {
		if val, err := strconv.ParseBool(str); err == nil {
			return val
		}
	}
	return defaultValue
}
