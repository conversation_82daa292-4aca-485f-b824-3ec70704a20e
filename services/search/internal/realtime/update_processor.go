package realtime

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/services/search/internal/indexer"
	"github.com/swork-team/platform/services/search/internal/models"
	"github.com/swork-team/platform/services/search/internal/repository"
)

// UpdateProcessor handles real-time search index updates
type UpdateProcessor struct {
	redisClient     *redis.Client
	repo            *repository.SearchRepository
	indexer         *indexer.ContentIndexer
	documentFactory *indexer.DocumentFactory

	// Processing configuration
	batchSize     int
	flushInterval time.Duration
	maxRetries    int

	// Internal state
	updateQueue chan UpdateEvent
	batchQueue  []UpdateEvent
	batchMutex  sync.Mutex
	stopChan    chan struct{}
	wg          sync.WaitGroup
}

// UpdateEvent represents a real-time update event
type UpdateEvent struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`      // "create", "update", "delete"
	Service   string                 `json:"service"`   // Source service
	Entity    string                 `json:"entity"`    // Entity type
	EntityID  string                 `json:"entity_id"` // Entity ID
	UserID    string                 `json:"user_id"`   // User who made the change
	TeamID    *string                `json:"team_id,omitempty"`
	Data      map[string]interface{} `json:"data"`      // Entity data
	Timestamp int64                  `json:"timestamp"` // Unix timestamp
	Priority  int                    `json:"priority"`  // Processing priority (1-10)
}

// NewUpdateProcessor creates a new update processor
func NewUpdateProcessor(
	redisClient *redis.Client,
	repo *repository.SearchRepository,
	indexer *indexer.ContentIndexer,
	documentFactory *indexer.DocumentFactory,
) *UpdateProcessor {
	return &UpdateProcessor{
		redisClient:     redisClient,
		repo:            repo,
		indexer:         indexer,
		documentFactory: documentFactory,
		batchSize:       50,
		flushInterval:   5 * time.Second,
		maxRetries:      3,
		updateQueue:     make(chan UpdateEvent, 1000),
		batchQueue:      make([]UpdateEvent, 0, 50),
		stopChan:        make(chan struct{}),
	}
}

// Start starts the update processor
func (up *UpdateProcessor) Start(ctx context.Context) error {
	log.Println("Starting real-time update processor...")

	// Start Redis subscriber
	up.wg.Add(1)
	go up.subscribeToUpdates(ctx)

	// Start batch processor
	up.wg.Add(1)
	go up.processBatches(ctx)

	// Start periodic flush
	up.wg.Add(1)
	go up.periodicFlush(ctx)

	log.Println("Real-time update processor started successfully")
	return nil
}

// Stop stops the update processor
func (up *UpdateProcessor) Stop() {
	log.Println("Stopping real-time update processor...")
	close(up.stopChan)
	up.wg.Wait()
	log.Println("Real-time update processor stopped")
}

// subscribeToUpdates subscribes to Redis updates
func (up *UpdateProcessor) subscribeToUpdates(ctx context.Context) {
	defer up.wg.Done()

	// Subscribe to update channels
	pubsub := up.redisClient.Subscribe(ctx,
		"search:updates:user",
		"search:updates:team",
		"search:updates:social",
		"search:updates:messaging",
		"search:updates:drive",
		"search:updates:calendar",
		"search:updates:notification",
	)
	defer pubsub.Close()

	ch := pubsub.Channel()

	for {
		select {
		case <-ctx.Done():
			return
		case <-up.stopChan:
			return
		case msg := <-ch:
			if err := up.handleRedisMessage(msg); err != nil {
				log.Printf("Failed to handle Redis message: %v", err)
			}
		}
	}
}

// handleRedisMessage handles incoming Redis messages
func (up *UpdateProcessor) handleRedisMessage(msg *redis.Message) error {
	var event UpdateEvent
	if err := json.Unmarshal([]byte(msg.Payload), &event); err != nil {
		return fmt.Errorf("failed to unmarshal update event: %w", err)
	}

	// Add to processing queue
	select {
	case up.updateQueue <- event:
		return nil
	default:
		log.Printf("Update queue is full, dropping event: %s", event.ID)
		return fmt.Errorf("update queue is full")
	}
}

// processBatches processes update events in batches
func (up *UpdateProcessor) processBatches(ctx context.Context) {
	defer up.wg.Done()

	for {
		select {
		case <-ctx.Done():
			up.flushBatch(ctx)
			return
		case <-up.stopChan:
			up.flushBatch(ctx)
			return
		case event := <-up.updateQueue:
			up.addToBatch(event)
			if len(up.batchQueue) >= up.batchSize {
				up.flushBatch(ctx)
			}
		}
	}
}

// periodicFlush flushes batches periodically
func (up *UpdateProcessor) periodicFlush(ctx context.Context) {
	defer up.wg.Done()

	ticker := time.NewTicker(up.flushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-up.stopChan:
			return
		case <-ticker.C:
			if len(up.batchQueue) > 0 {
				up.flushBatch(ctx)
			}
		}
	}
}

// addToBatch adds an event to the batch queue
func (up *UpdateProcessor) addToBatch(event UpdateEvent) {
	up.batchMutex.Lock()
	defer up.batchMutex.Unlock()

	up.batchQueue = append(up.batchQueue, event)
}

// flushBatch processes the current batch
func (up *UpdateProcessor) flushBatch(ctx context.Context) {
	up.batchMutex.Lock()
	batch := make([]UpdateEvent, len(up.batchQueue))
	copy(batch, up.batchQueue)
	up.batchQueue = up.batchQueue[:0] // Clear the batch
	up.batchMutex.Unlock()

	if len(batch) == 0 {
		return
	}

	log.Printf("Processing batch of %d update events", len(batch))

	// Sort batch by priority (higher priority first)
	up.sortBatchByPriority(batch)

	// Process events
	for _, event := range batch {
		if err := up.processUpdateEvent(ctx, event); err != nil {
			log.Printf("Failed to process update event %s: %v", event.ID, err)
			// TODO: Add to retry queue
		}
	}
}

// processUpdateEvent processes a single update event
func (up *UpdateProcessor) processUpdateEvent(ctx context.Context, event UpdateEvent) error {
	switch event.Type {
	case "create", "update":
		return up.handleCreateOrUpdate(ctx, event)
	case "delete":
		return up.handleDelete(ctx, event)
	default:
		return fmt.Errorf("unsupported event type: %s", event.Type)
	}
}

// handleCreateOrUpdate handles create and update events
func (up *UpdateProcessor) handleCreateOrUpdate(ctx context.Context, event UpdateEvent) error {
	// Create search document from event
	doc, err := up.createSearchDocumentFromEvent(event)
	if err != nil {
		return fmt.Errorf("failed to create search document: %w", err)
	}

	// Check if document already exists
	existingDoc, err := up.repo.GetDocumentBySource(ctx, doc.SourceType, doc.SourceID)
	if err == nil {
		// Update existing document
		doc.ID = existingDoc.ID
		doc.CreatedAt = existingDoc.CreatedAt
	}

	// Index the document
	if err := up.indexer.IndexDocument(ctx, doc); err != nil {
		return fmt.Errorf("failed to index document: %w", err)
	}

	// Create index record
	indexRecord := &models.SearchIndex{
		DocumentID: doc.ID,
		SourceType: doc.SourceType,
		SourceID:   doc.SourceID,
		Status:     models.IndexStatusCompleted,
	}

	if err := up.repo.CreateIndex(ctx, indexRecord); err != nil {
		log.Printf("Failed to create index record: %v", err)
	}

	return nil
}

// handleDelete handles delete events
func (up *UpdateProcessor) handleDelete(ctx context.Context, event UpdateEvent) error {
	sourceType := fmt.Sprintf("%s_%s", event.Service, event.Entity)

	if err := up.indexer.DeleteDocumentBySource(ctx, sourceType, event.EntityID); err != nil {
		return fmt.Errorf("failed to delete document: %w", err)
	}

	return nil
}

// createSearchDocumentFromEvent creates a search document from an update event
func (up *UpdateProcessor) createSearchDocumentFromEvent(event UpdateEvent) (*models.SearchDocument, error) {
	// This is similar to the webhook handler logic
	// We could refactor this into a shared utility

	switch event.Service {
	case "user":
		userID := event.EntityID
		if userUUID, err := parseUUID(userID); err == nil {
			return up.documentFactory.CreateUserDocument(userUUID, event.Data)
		}
	case "team":
		teamID := event.EntityID
		userID := event.UserID
		if teamUUID, err := parseUUID(teamID); err == nil {
			if userUUID, err := parseUUID(userID); err == nil {
				return up.documentFactory.CreateTeamDocument(teamUUID, userUUID, event.Data)
			}
		}
	case "social":
		switch event.Entity {
		case "post":
			return up.documentFactory.CreatePostDocument(event.EntityID, event.UserID, event.TeamID, event.Data)
		}
	case "messaging":
		switch event.Entity {
		case "message":
			return up.documentFactory.CreateMessageDocument(event.EntityID, event.UserID, event.Data)
		}
	case "drive":
		switch event.Entity {
		case "file":
			return up.documentFactory.CreateFileDocument(event.EntityID, event.UserID, event.TeamID, event.Data)
		}
	case "calendar":
		switch event.Entity {
		case "event":
			return up.documentFactory.CreateEventDocument(event.EntityID, event.UserID, event.Data)
		}
	}

	return nil, fmt.Errorf("unsupported service/entity: %s/%s", event.Service, event.Entity)
}

// sortBatchByPriority sorts events by priority (higher first)
func (up *UpdateProcessor) sortBatchByPriority(batch []UpdateEvent) {
	for i := 0; i < len(batch)-1; i++ {
		for j := i + 1; j < len(batch); j++ {
			if batch[i].Priority < batch[j].Priority {
				batch[i], batch[j] = batch[j], batch[i]
			}
		}
	}
}

// PublishUpdate publishes an update event to Redis
func (up *UpdateProcessor) PublishUpdate(ctx context.Context, event UpdateEvent) error {
	channel := fmt.Sprintf("search:updates:%s", event.Service)

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal update event: %w", err)
	}

	return up.redisClient.Publish(ctx, channel, data).Err()
}

// GetQueueStats returns statistics about the update queue
func (up *UpdateProcessor) GetQueueStats() map[string]interface{} {
	up.batchMutex.Lock()
	batchSize := len(up.batchQueue)
	up.batchMutex.Unlock()

	return map[string]interface{}{
		"queue_size":      len(up.updateQueue),
		"batch_size":      batchSize,
		"max_queue_size":  cap(up.updateQueue),
		"batch_threshold": up.batchSize,
		"flush_interval":  up.flushInterval.String(),
	}
}

// Helper function to parse UUID
func parseUUID(s string) (uuid.UUID, error) {
	// This would use the actual UUID parsing logic
	// For now, we'll assume it's implemented elsewhere
	return uuid.Parse(s)
}
