package webhooks

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/search/internal/indexer"
	"github.com/swork-team/platform/services/search/internal/models"
)

// ContentWebhookHandler handles webhooks from other services for content indexing
type ContentWebhookHandler struct {
	indexer         *indexer.ContentIndexer
	documentFactory *indexer.DocumentFactory
}

// NewContentWebhookHandler creates a new content webhook handler
func NewContentWebhookHandler(indexer *indexer.ContentIndexer, documentFactory *indexer.DocumentFactory) *ContentWebhookHandler {
	return &ContentWebhookHandler{
		indexer:         indexer,
		documentFactory: documentFactory,
	}
}

// WebhookEvent represents a webhook event from other services
type WebhookEvent struct {
	Type      string                 `json:"type"`      // "created", "updated", "deleted"
	Service   string                 `json:"service"`   // "user", "team", "social", etc.
	Entity    string                 `json:"entity"`    // "user", "post", "file", etc.
	EntityID  string                 `json:"entity_id"` // ID of the entity
	UserID    string                 `json:"user_id"`   // User who performed the action
	TeamID    *string                `json:"team_id,omitempty"`
	Data      map[string]interface{} `json:"data"`      // Entity data
	Timestamp int64                  `json:"timestamp"` // Unix timestamp
}

// HandleWebhook handles incoming webhook events
// @Summary Handle content webhook
// @Description Handle webhook events from other services for content indexing
// @Tags webhooks
// @Accept json
// @Produce json
// @Param event body WebhookEvent true "Webhook event"
// @Success 200 {object} utils.SuccessResponse
// @Failure 400 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /webhooks/content [post]
func (h *ContentWebhookHandler) HandleWebhook(c *gin.Context) {
	var event WebhookEvent
	if err := c.ShouldBindJSON(&event); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid webhook payload", err)
		return
	}

	// Validate webhook event
	if err := h.validateWebhookEvent(&event); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid webhook event", err)
		return
	}

	// Process the webhook event asynchronously
	go func() {
		if err := h.processWebhookEvent(context.Background(), &event); err != nil {
			log.Printf("Failed to process webhook event: %v", err)
		}
	}()

	utils.SuccessResponse(c, http.StatusOK, "Webhook processed successfully", nil)
}

// validateWebhookEvent validates the webhook event
func (h *ContentWebhookHandler) validateWebhookEvent(event *WebhookEvent) error {
	if event.Type == "" {
		return fmt.Errorf("event type is required")
	}
	if event.Service == "" {
		return fmt.Errorf("service is required")
	}
	if event.Entity == "" {
		return fmt.Errorf("entity is required")
	}
	if event.EntityID == "" {
		return fmt.Errorf("entity ID is required")
	}
	if event.UserID == "" {
		return fmt.Errorf("user ID is required")
	}

	// Validate event type
	validTypes := []string{"created", "updated", "deleted"}
	valid := false
	for _, validType := range validTypes {
		if event.Type == validType {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid event type: %s", event.Type)
	}

	return nil
}

// processWebhookEvent processes the webhook event
func (h *ContentWebhookHandler) processWebhookEvent(ctx context.Context, event *WebhookEvent) error {
	log.Printf("Processing webhook event: %s %s %s %s", event.Type, event.Service, event.Entity, event.EntityID)

	switch event.Type {
	case "created", "updated":
		return h.handleCreateOrUpdate(ctx, event)
	case "deleted":
		return h.handleDelete(ctx, event)
	default:
		return fmt.Errorf("unsupported event type: %s", event.Type)
	}
}

// handleCreateOrUpdate handles create and update events
func (h *ContentWebhookHandler) handleCreateOrUpdate(ctx context.Context, event *WebhookEvent) error {
	// Create search document from webhook data
	doc, err := h.createSearchDocument(event)
	if err != nil {
		return fmt.Errorf("failed to create search document: %w", err)
	}

	// Index the document
	if err := h.indexer.IndexDocument(ctx, doc); err != nil {
		return fmt.Errorf("failed to index document: %w", err)
	}

	log.Printf("Successfully indexed %s %s %s", event.Service, event.Entity, event.EntityID)
	return nil
}

// handleDelete handles delete events
func (h *ContentWebhookHandler) handleDelete(ctx context.Context, event *WebhookEvent) error {
	sourceType := fmt.Sprintf("%s_%s", event.Service, event.Entity)
	
	if err := h.indexer.DeleteDocumentBySource(ctx, sourceType, event.EntityID); err != nil {
		return fmt.Errorf("failed to delete document: %w", err)
	}

	log.Printf("Successfully deleted %s %s %s from index", event.Service, event.Entity, event.EntityID)
	return nil
}

// createSearchDocument creates a search document from webhook event
func (h *ContentWebhookHandler) createSearchDocument(event *WebhookEvent) (*models.SearchDocument, error) {
	userID, err := uuid.Parse(event.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	switch event.Service {
	case "user":
		return h.documentFactory.CreateUserDocument(userID, event.Data)
	case "team":
		teamID, err := uuid.Parse(event.EntityID)
		if err != nil {
			return nil, fmt.Errorf("invalid team ID: %w", err)
		}
		return h.documentFactory.CreateTeamDocument(teamID, userID, event.Data)
	case "social":
		switch event.Entity {
		case "post":
			return h.documentFactory.CreatePostDocument(event.EntityID, event.UserID, event.TeamID, event.Data)
		default:
			return nil, fmt.Errorf("unsupported social entity: %s", event.Entity)
		}
	case "messaging":
		switch event.Entity {
		case "message":
			return h.documentFactory.CreateMessageDocument(event.EntityID, event.UserID, event.Data)
		default:
			return nil, fmt.Errorf("unsupported messaging entity: %s", event.Entity)
		}
	case "drive":
		switch event.Entity {
		case "file":
			return h.documentFactory.CreateFileDocument(event.EntityID, event.UserID, event.TeamID, event.Data)
		default:
			return nil, fmt.Errorf("unsupported drive entity: %s", event.Entity)
		}
	case "calendar":
		switch event.Entity {
		case "event":
			return h.documentFactory.CreateEventDocument(event.EntityID, event.UserID, event.Data)
		default:
			return nil, fmt.Errorf("unsupported calendar entity: %s", event.Entity)
		}
	default:
		return nil, fmt.Errorf("unsupported service: %s", event.Service)
	}
}

// HandleBulkWebhook handles bulk webhook events
// @Summary Handle bulk content webhook
// @Description Handle multiple webhook events in a single request
// @Tags webhooks
// @Accept json
// @Produce json
// @Param events body []WebhookEvent true "Webhook events"
// @Success 200 {object} utils.SuccessResponse
// @Failure 400 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /webhooks/content/bulk [post]
func (h *ContentWebhookHandler) HandleBulkWebhook(c *gin.Context) {
	var events []WebhookEvent
	if err := c.ShouldBindJSON(&events); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid webhook payload", err)
		return
	}

	if len(events) == 0 {
		utils.ErrorResponse(c, http.StatusBadRequest, "No events provided", nil)
		return
	}

	if len(events) > 100 {
		utils.ErrorResponse(c, http.StatusBadRequest, "Too many events (max 100)", nil)
		return
	}

	// Validate all events first
	for i, event := range events {
		if err := h.validateWebhookEvent(&event); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Invalid event at index %d: %v", i, err), nil)
			return
		}
	}

	// Process events asynchronously
	go func() {
		for _, event := range events {
			if err := h.processWebhookEvent(context.Background(), &event); err != nil {
				log.Printf("Failed to process bulk webhook event: %v", err)
			}
		}
	}()

	utils.SuccessResponse(c, http.StatusOK, fmt.Sprintf("Processed %d webhook events", len(events)), nil)
}

// Health check for webhook endpoint
func (h *ContentWebhookHandler) Health(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Webhook service is healthy", map[string]string{
		"service": "search-webhook",
		"status":  "healthy",
	})
}
