package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/swork-team/platform/services/search/internal/models"
)

// SearchRepository handles database operations for search documents
type SearchRepository struct {
	db *gorm.DB
}

// NewSearchRepository creates a new search repository
func NewSearchRepository(db *gorm.DB) *SearchRepository {
	return &SearchRepository{
		db: db,
	}
}

// CreateDocument creates a new search document
func (r *SearchRepository) CreateDocument(ctx context.Context, doc *models.SearchDocument) error {
	return r.db.WithContext(ctx).Create(doc).Error
}

// UpdateDocument updates an existing search document
func (r *SearchRepository) UpdateDocument(ctx context.Context, doc *models.SearchDocument) error {
	return r.db.WithContext(ctx).Save(doc).Error
}

// GetDocumentByID retrieves a document by ID
func (r *SearchRepository) GetDocumentByID(ctx context.Context, id uuid.UUID) (*models.SearchDocument, error) {
	var doc models.SearchDocument
	err := r.db.WithContext(ctx).First(&doc, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &doc, nil
}

// GetDocumentBySource retrieves a document by source type and ID
func (r *SearchRepository) GetDocumentBySource(ctx context.Context, sourceType, sourceID string) (*models.SearchDocument, error) {
	var doc models.SearchDocument
	err := r.db.WithContext(ctx).First(&doc, "source_type = ? AND source_id = ?", sourceType, sourceID).Error
	if err != nil {
		return nil, err
	}
	return &doc, nil
}

// DeleteDocument deletes a document by ID
func (r *SearchRepository) DeleteDocument(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.SearchDocument{}, "id = ?", id).Error
}

// DeleteDocumentBySource deletes a document by source type and ID
func (r *SearchRepository) DeleteDocumentBySource(ctx context.Context, sourceType, sourceID string) error {
	return r.db.WithContext(ctx).Delete(&models.SearchDocument{}, "source_type = ? AND source_id = ?", sourceType, sourceID).Error
}

// SearchDocuments performs a text-based search on documents
func (r *SearchRepository) SearchDocuments(ctx context.Context, query string, filters SearchFilters, limit, offset int) ([]*models.SearchDocument, int64, error) {
	var docs []*models.SearchDocument
	var total int64
	
	db := r.db.WithContext(ctx).Model(&models.SearchDocument{})
	
	// Apply filters
	db = r.applyFilters(db, filters)
	
	// Apply text search
	if query != "" {
		// Use PostgreSQL full-text search
		searchQuery := fmt.Sprintf("to_tsvector('english', title || ' ' || content || ' ' || summary) @@ plainto_tsquery('english', '%s')", query)
		db = db.Where(searchQuery)
	}
	
	// Get total count
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// Get documents with pagination
	err := db.Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&docs).Error
	
	return docs, total, err
}

// GetDocumentsByType retrieves documents by type
func (r *SearchRepository) GetDocumentsByType(ctx context.Context, docType models.SearchDocumentType, limit, offset int) ([]*models.SearchDocument, error) {
	var docs []*models.SearchDocument
	err := r.db.WithContext(ctx).
		Where("type = ?", docType).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&docs).Error
	return docs, err
}

// GetDocumentsByUser retrieves documents by user ID
func (r *SearchRepository) GetDocumentsByUser(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*models.SearchDocument, error) {
	var docs []*models.SearchDocument
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&docs).Error
	return docs, err
}

// GetDocumentsByTeam retrieves documents by team ID
func (r *SearchRepository) GetDocumentsByTeam(ctx context.Context, teamID uuid.UUID, limit, offset int) ([]*models.SearchDocument, error) {
	var docs []*models.SearchDocument
	err := r.db.WithContext(ctx).
		Where("team_id = ?", teamID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&docs).Error
	return docs, err
}

// GetRecentDocuments retrieves recently created/updated documents
func (r *SearchRepository) GetRecentDocuments(ctx context.Context, since time.Time, limit int) ([]*models.SearchDocument, error) {
	var docs []*models.SearchDocument
	err := r.db.WithContext(ctx).
		Where("updated_at > ?", since).
		Order("updated_at DESC").
		Limit(limit).
		Find(&docs).Error
	return docs, err
}

// GetDocumentsForIndexing retrieves documents that need to be indexed
func (r *SearchRepository) GetDocumentsForIndexing(ctx context.Context, limit int) ([]*models.SearchDocument, error) {
	var docs []*models.SearchDocument
	err := r.db.WithContext(ctx).
		Where("vector IS NULL OR vector = '[]'").
		Order("created_at ASC").
		Limit(limit).
		Find(&docs).Error
	return docs, err
}

// SearchFilters represents search filtering options
type SearchFilters struct {
	Types      []models.SearchDocumentType
	UserID     *uuid.UUID
	TeamID     *uuid.UUID
	Visibility []models.SearchVisibility
	DateRange  *DateRange
	Tags       []string
}

// DateRange represents a date range filter
type DateRange struct {
	Start *time.Time
	End   *time.Time
}

// applyFilters applies search filters to the query
func (r *SearchRepository) applyFilters(db *gorm.DB, filters SearchFilters) *gorm.DB {
	if len(filters.Types) > 0 {
		db = db.Where("type IN ?", filters.Types)
	}
	
	if filters.UserID != nil {
		db = db.Where("user_id = ?", *filters.UserID)
	}
	
	if filters.TeamID != nil {
		db = db.Where("team_id = ?", *filters.TeamID)
	}
	
	if len(filters.Visibility) > 0 {
		db = db.Where("visibility IN ?", filters.Visibility)
	}
	
	if filters.DateRange != nil {
		if filters.DateRange.Start != nil {
			db = db.Where("created_at >= ?", *filters.DateRange.Start)
		}
		if filters.DateRange.End != nil {
			db = db.Where("created_at <= ?", *filters.DateRange.End)
		}
	}
	
	if len(filters.Tags) > 0 {
		for _, tag := range filters.Tags {
			db = db.Where("? = ANY(tags)", tag)
		}
	}
	
	return db
}

// CreateIndex creates a search index record
func (r *SearchRepository) CreateIndex(ctx context.Context, index *models.SearchIndex) error {
	return r.db.WithContext(ctx).Create(index).Error
}

// UpdateIndex updates a search index record
func (r *SearchRepository) UpdateIndex(ctx context.Context, index *models.SearchIndex) error {
	return r.db.WithContext(ctx).Save(index).Error
}

// GetPendingIndexes retrieves pending index operations
func (r *SearchRepository) GetPendingIndexes(ctx context.Context, limit int) ([]*models.SearchIndex, error) {
	var indexes []*models.SearchIndex
	err := r.db.WithContext(ctx).
		Where("status IN ?", []models.IndexStatus{models.IndexStatusPending, models.IndexStatusFailed}).
		Where("retry_count < ?", 3).
		Order("created_at ASC").
		Limit(limit).
		Find(&indexes).Error
	return indexes, err
}

// RecordQuery records a search query for analytics
func (r *SearchRepository) RecordQuery(ctx context.Context, query *models.SearchQuery) error {
	return r.db.WithContext(ctx).Create(query).Error
}

// GetSearchAnalytics retrieves search analytics for a date range
func (r *SearchRepository) GetSearchAnalytics(ctx context.Context, start, end time.Time) ([]*models.SearchAnalytics, error) {
	var analytics []*models.SearchAnalytics
	err := r.db.WithContext(ctx).
		Where("date BETWEEN ? AND ?", start, end).
		Order("date ASC").
		Find(&analytics).Error
	return analytics, err
}
