package vector

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/search/internal/config"
	"github.com/swork-team/platform/services/search/internal/models"
)

// QdrantClient implements the VectorStore interface using Qdrant
type QdrantClient struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
	config     config.SearchEngineConfig
}

// QdrantPoint represents a point in Qdrant
type QdrantPoint struct {
	ID      string                 `json:"id"`
	Vector  []float32              `json:"vector"`
	Payload map[string]interface{} `json:"payload"`
}

// QdrantSearchRequest represents a search request to Qdrant
type QdrantSearchRequest struct {
	Vector      []float32              `json:"vector"`
	Limit       int                    `json:"limit"`
	Offset      int                    `json:"offset,omitempty"`
	Filter      map[string]interface{} `json:"filter,omitempty"`
	WithPayload bool                   `json:"with_payload"`
	WithVector  bool                   `json:"with_vector"`
	ScoreThreshold *float64            `json:"score_threshold,omitempty"`
}

// QdrantSearchResponse represents a search response from Qdrant
type QdrantSearchResponse struct {
	Result []QdrantSearchResult `json:"result"`
	Status string               `json:"status"`
	Time   float64              `json:"time"`
}

// QdrantSearchResult represents a single search result from Qdrant
type QdrantSearchResult struct {
	ID      string                 `json:"id"`
	Version int                    `json:"version"`
	Score   float64                `json:"score"`
	Payload map[string]interface{} `json:"payload"`
	Vector  []float32              `json:"vector,omitempty"`
}

// QdrantCollectionInfo represents collection information
type QdrantCollectionInfo struct {
	Status string `json:"status"`
	Result struct {
		Status string `json:"status"`
		Config struct {
			Params struct {
				VectorSize int    `json:"vector_size"`
				Distance   string `json:"distance"`
			} `json:"params"`
		} `json:"config"`
	} `json:"result"`
}

// NewQdrantClient creates a new Qdrant client
func NewQdrantClient(cfg config.SearchEngineConfig) *QdrantClient {
	baseURL := fmt.Sprintf("http://%s:%d", cfg.Host, cfg.Port)
	
	return &QdrantClient{
		baseURL: baseURL,
		apiKey:  cfg.APIKey,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		config: cfg,
	}
}

// CreateCollection creates a new collection in Qdrant
func (q *QdrantClient) CreateCollection(ctx context.Context, collectionName string, vectorSize int) error {
	url := fmt.Sprintf("%s/collections/%s", q.baseURL, collectionName)
	
	payload := map[string]interface{}{
		"vectors": map[string]interface{}{
			"size":     vectorSize,
			"distance": "Cosine",
		},
	}
	
	body, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	q.setHeaders(req)
	
	resp, err := q.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusConflict {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to create collection: %s", string(body))
	}
	
	return nil
}

// CollectionExists checks if a collection exists
func (q *QdrantClient) CollectionExists(ctx context.Context, collectionName string) (bool, error) {
	url := fmt.Sprintf("%s/collections/%s", q.baseURL, collectionName)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}
	
	q.setHeaders(req)
	
	resp, err := q.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == http.StatusOK, nil
}

// UpsertPoints upserts points into the collection
func (q *QdrantClient) UpsertPoints(ctx context.Context, collectionName string, points []QdrantPoint) error {
	url := fmt.Sprintf("%s/collections/%s/points", q.baseURL, collectionName)
	
	payload := map[string]interface{}{
		"points": points,
	}
	
	body, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	q.setHeaders(req)
	
	resp, err := q.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to upsert points: %s", string(body))
	}
	
	return nil
}

// SearchSimilar searches for similar vectors
func (q *QdrantClient) SearchSimilar(ctx context.Context, collectionName string, vector []float32, limit int, filter map[string]interface{}) ([]QdrantSearchResult, error) {
	url := fmt.Sprintf("%s/collections/%s/points/search", q.baseURL, collectionName)
	
	searchReq := QdrantSearchRequest{
		Vector:      vector,
		Limit:       limit,
		Filter:      filter,
		WithPayload: true,
		WithVector:  false,
	}
	
	body, err := json.Marshal(searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal search request: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	q.setHeaders(req)
	
	resp, err := q.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("search failed: %s", string(body))
	}
	
	var searchResp QdrantSearchResponse
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}
	
	return searchResp.Result, nil
}

// DeletePoints deletes points from the collection
func (q *QdrantClient) DeletePoints(ctx context.Context, collectionName string, pointIDs []string) error {
	url := fmt.Sprintf("%s/collections/%s/points/delete", q.baseURL, collectionName)
	
	payload := map[string]interface{}{
		"points": pointIDs,
	}
	
	body, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	q.setHeaders(req)
	
	resp, err := q.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to delete points: %s", string(body))
	}
	
	return nil
}

// setHeaders sets common headers for requests
func (q *QdrantClient) setHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	if q.apiKey != "" {
		req.Header.Set("api-key", q.apiKey)
	}
}

// DocumentToQdrantPoint converts a search document to a Qdrant point
func DocumentToQdrantPoint(doc *models.SearchDocument) QdrantPoint {
	payload := map[string]interface{}{
		"type":        string(doc.Type),
		"title":       doc.Title,
		"content":     doc.Content,
		"summary":     doc.Summary,
		"tags":        doc.Tags,
		"source_id":   doc.SourceID,
		"source_type": doc.SourceType,
		"source_url":  doc.SourceURL,
		"user_id":     doc.UserID.String(),
		"visibility":  string(doc.Visibility),
		"created_at":  doc.CreatedAt.Unix(),
		"updated_at":  doc.UpdatedAt.Unix(),
	}
	
	if doc.TeamID != nil {
		payload["team_id"] = doc.TeamID.String()
	}
	
	if doc.Metadata != nil {
		payload["metadata"] = doc.Metadata
	}
	
	return QdrantPoint{
		ID:      doc.ID.String(),
		Vector:  doc.Vector,
		Payload: payload,
	}
}
