package vector

import (
	"context"
	"fmt"

	"github.com/swork-team/platform/services/search/internal/config"
	"github.com/swork-team/platform/services/search/internal/models"
)

// VectorStore interface defines the contract for vector database operations
type VectorStore interface {
	CreateCollection(ctx context.Context, collectionName string, vectorSize int) error
	CollectionExists(ctx context.Context, collectionName string) (bool, error)
	UpsertDocument(ctx context.Context, collectionName string, doc *models.SearchDocument) error
	UpsertDocuments(ctx context.Context, collectionName string, docs []*models.SearchDocument) error
	SearchSimilar(ctx context.Context, collectionName string, vector []float32, limit int, filter map[string]interface{}) ([]VectorSearchResult, error)
	DeleteDocument(ctx context.Context, collectionName string, documentID string) error
	DeleteDocuments(ctx context.Context, collectionName string, documentIDs []string) error
}

// VectorSearchResult represents a search result from the vector store
type VectorSearchResult struct {
	ID       string                 `json:"id"`
	Score    float64                `json:"score"`
	Metadata map[string]interface{} `json:"metadata"`
}

// VectorService provides vector database functionality
type VectorService struct {
	store  VectorStore
	config config.SearchEngineConfig
}

// NewVectorService creates a new vector service
func NewVectorService(cfg config.SearchEngineConfig) (*VectorService, error) {
	var store VectorStore
	
	switch cfg.Provider {
	case "qdrant":
		store = NewQdrantClient(cfg)
	case "weaviate":
		// TODO: Implement Weaviate client
		return nil, fmt.Errorf("Weaviate client not implemented yet")
	case "elasticsearch":
		// TODO: Implement Elasticsearch client
		return nil, fmt.Errorf("Elasticsearch client not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported vector store provider: %s", cfg.Provider)
	}
	
	return &VectorService{
		store:  store,
		config: cfg,
	}, nil
}

// InitializeCollection initializes the search collection
func (vs *VectorService) InitializeCollection(ctx context.Context, vectorSize int) error {
	exists, err := vs.store.CollectionExists(ctx, vs.config.IndexName)
	if err != nil {
		return fmt.Errorf("failed to check collection existence: %w", err)
	}
	
	if !exists {
		err = vs.store.CreateCollection(ctx, vs.config.IndexName, vectorSize)
		if err != nil {
			return fmt.Errorf("failed to create collection: %w", err)
		}
	}
	
	return nil
}

// UpsertDocument upserts a single document
func (vs *VectorService) UpsertDocument(ctx context.Context, doc *models.SearchDocument) error {
	return vs.store.UpsertDocument(ctx, vs.config.IndexName, doc)
}

// UpsertDocuments upserts multiple documents
func (vs *VectorService) UpsertDocuments(ctx context.Context, docs []*models.SearchDocument) error {
	return vs.store.UpsertDocuments(ctx, vs.config.IndexName, docs)
}

// SearchSimilar searches for similar documents
func (vs *VectorService) SearchSimilar(ctx context.Context, vector []float32, limit int, filter map[string]interface{}) ([]VectorSearchResult, error) {
	return vs.store.SearchSimilar(ctx, vs.config.IndexName, vector, limit, filter)
}

// DeleteDocument deletes a single document
func (vs *VectorService) DeleteDocument(ctx context.Context, documentID string) error {
	return vs.store.DeleteDocument(ctx, vs.config.IndexName, documentID)
}

// DeleteDocuments deletes multiple documents
func (vs *VectorService) DeleteDocuments(ctx context.Context, documentIDs []string) error {
	return vs.store.DeleteDocuments(ctx, vs.config.IndexName, documentIDs)
}

// GetConfig returns the search engine configuration
func (vs *VectorService) GetConfig() config.SearchEngineConfig {
	return vs.config
}

// QdrantVectorStore implements VectorStore for Qdrant
type QdrantVectorStore struct {
	client *QdrantClient
}

// NewQdrantVectorStore creates a new Qdrant vector store
func NewQdrantVectorStore(cfg config.SearchEngineConfig) *QdrantVectorStore {
	return &QdrantVectorStore{
		client: NewQdrantClient(cfg),
	}
}

// CreateCollection creates a new collection
func (q *QdrantVectorStore) CreateCollection(ctx context.Context, collectionName string, vectorSize int) error {
	return q.client.CreateCollection(ctx, collectionName, vectorSize)
}

// CollectionExists checks if a collection exists
func (q *QdrantVectorStore) CollectionExists(ctx context.Context, collectionName string) (bool, error) {
	return q.client.CollectionExists(ctx, collectionName)
}

// UpsertDocument upserts a single document
func (q *QdrantVectorStore) UpsertDocument(ctx context.Context, collectionName string, doc *models.SearchDocument) error {
	point := DocumentToQdrantPoint(doc)
	return q.client.UpsertPoints(ctx, collectionName, []QdrantPoint{point})
}

// UpsertDocuments upserts multiple documents
func (q *QdrantVectorStore) UpsertDocuments(ctx context.Context, collectionName string, docs []*models.SearchDocument) error {
	points := make([]QdrantPoint, len(docs))
	for i, doc := range docs {
		points[i] = DocumentToQdrantPoint(doc)
	}
	return q.client.UpsertPoints(ctx, collectionName, points)
}

// SearchSimilar searches for similar documents
func (q *QdrantVectorStore) SearchSimilar(ctx context.Context, collectionName string, vector []float32, limit int, filter map[string]interface{}) ([]VectorSearchResult, error) {
	results, err := q.client.SearchSimilar(ctx, collectionName, vector, limit, filter)
	if err != nil {
		return nil, err
	}
	
	vectorResults := make([]VectorSearchResult, len(results))
	for i, result := range results {
		vectorResults[i] = VectorSearchResult{
			ID:       result.ID,
			Score:    result.Score,
			Metadata: result.Payload,
		}
	}
	
	return vectorResults, nil
}

// DeleteDocument deletes a single document
func (q *QdrantVectorStore) DeleteDocument(ctx context.Context, collectionName string, documentID string) error {
	return q.client.DeletePoints(ctx, collectionName, []string{documentID})
}

// DeleteDocuments deletes multiple documents
func (q *QdrantVectorStore) DeleteDocuments(ctx context.Context, collectionName string, documentIDs []string) error {
	return q.client.DeletePoints(ctx, collectionName, documentIDs)
}
