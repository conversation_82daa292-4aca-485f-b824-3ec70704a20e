package ranking

import (
	"math"
	"sort"
	"strings"
	"time"

	"github.com/swork-team/platform/services/search/internal/domain"
	"github.com/swork-team/platform/services/search/internal/models"
)

// ResultRanker handles ranking and scoring of search results
type ResultRanker struct {
	weights RankingWeights
}

// RankingWeights defines the weights for different ranking factors
type RankingWeights struct {
	SemanticSimilarity float64 // Weight for vector similarity score
	TextRelevance      float64 // Weight for text-based relevance
	Recency           float64 // Weight for content recency
	UserRelevance     float64 // Weight for user-specific relevance
	ContentType       float64 // Weight for content type preference
	Engagement        float64 // Weight for engagement metrics
}

// DefaultRankingWeights returns default ranking weights
func DefaultRankingWeights() RankingWeights {
	return RankingWeights{
		SemanticSimilarity: 0.4,
		TextRelevance:      0.3,
		Recency:           0.1,
		UserRelevance:     0.1,
		ContentType:       0.05,
		Engagement:        0.05,
	}
}

// NewResultRanker creates a new result ranker
func NewResultRanker(weights RankingWeights) *ResultRanker {
	return &ResultRanker{
		weights: weights,
	}
}

// RankResults ranks search results based on multiple factors
func (r *ResultRanker) RankResults(results []domain.SearchResult, query string, userContext UserContext) []domain.SearchResult {
	if len(results) == 0 {
		return results
	}

	// Calculate scores for each result
	for i := range results {
		results[i].Score = r.calculateScore(&results[i], query, userContext)
	}

	// Sort by score (descending)
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	return results
}

// UserContext provides context about the user for personalized ranking
type UserContext struct {
	UserID           string
	TeamIDs          []string
	PreferredTypes   []models.SearchDocumentType
	RecentSearches   []string
	InteractionHistory map[string]float64 // Content ID -> interaction score
}

// calculateScore calculates the overall score for a search result
func (r *ResultRanker) calculateScore(result *domain.SearchResult, query string, userContext UserContext) float64 {
	var totalScore float64

	// Semantic similarity score (from vector search)
	semanticScore := result.Score
	if semanticScore > 1.0 {
		semanticScore = 1.0 // Normalize if needed
	}
	totalScore += semanticScore * r.weights.SemanticSimilarity

	// Text relevance score
	textScore := r.calculateTextRelevance(result, query)
	totalScore += textScore * r.weights.TextRelevance

	// Recency score
	recencyScore := r.calculateRecencyScore(result)
	totalScore += recencyScore * r.weights.Recency

	// User relevance score
	userScore := r.calculateUserRelevance(result, userContext)
	totalScore += userScore * r.weights.UserRelevance

	// Content type preference score
	typeScore := r.calculateContentTypeScore(result, userContext)
	totalScore += typeScore * r.weights.ContentType

	// Engagement score
	engagementScore := r.calculateEngagementScore(result)
	totalScore += engagementScore * r.weights.Engagement

	return totalScore
}

// calculateTextRelevance calculates text-based relevance score
func (r *ResultRanker) calculateTextRelevance(result *domain.SearchResult, query string) float64 {
	if query == "" {
		return 0.0
	}

	queryTerms := strings.Fields(strings.ToLower(query))
	if len(queryTerms) == 0 {
		return 0.0
	}

	// Combine title and content for analysis
	text := strings.ToLower(result.Title + " " + result.Content + " " + result.Summary)
	
	var score float64
	var totalTerms float64

	for _, term := range queryTerms {
		totalTerms++
		
		// Exact match in title (highest weight)
		if strings.Contains(strings.ToLower(result.Title), term) {
			score += 1.0
		}
		// Exact match in content
		if strings.Contains(text, term) {
			score += 0.7
		}
		// Partial match
		if r.containsPartialMatch(text, term) {
			score += 0.3
		}
		// Tag match
		for _, tag := range result.Tags {
			if strings.Contains(strings.ToLower(tag), term) {
				score += 0.5
				break
			}
		}
	}

	if totalTerms == 0 {
		return 0.0
	}

	return math.Min(score/totalTerms, 1.0)
}

// calculateRecencyScore calculates score based on content recency
func (r *ResultRanker) calculateRecencyScore(result *domain.SearchResult) float64 {
	now := time.Now()
	age := now.Sub(result.UpdatedAt)
	
	// Score decreases with age
	// Recent content (< 1 day) gets full score
	// Content older than 30 days gets minimal score
	daysSinceUpdate := age.Hours() / 24
	
	if daysSinceUpdate <= 1 {
		return 1.0
	} else if daysSinceUpdate <= 7 {
		return 0.8
	} else if daysSinceUpdate <= 30 {
		return 0.5
	} else if daysSinceUpdate <= 90 {
		return 0.2
	} else {
		return 0.1
	}
}

// calculateUserRelevance calculates user-specific relevance
func (r *ResultRanker) calculateUserRelevance(result *domain.SearchResult, userContext UserContext) float64 {
	var score float64

	// Content created by the user gets higher score
	if result.Source.ID == userContext.UserID {
		score += 0.5
	}

	// Content from user's teams gets higher score
	if teamID := r.extractTeamID(result); teamID != "" {
		for _, userTeamID := range userContext.TeamIDs {
			if teamID == userTeamID {
				score += 0.3
				break
			}
		}
	}

	// Content the user has interacted with before
	if interactionScore, exists := userContext.InteractionHistory[result.ID]; exists {
		score += interactionScore * 0.2
	}

	return math.Min(score, 1.0)
}

// calculateContentTypeScore calculates score based on content type preferences
func (r *ResultRanker) calculateContentTypeScore(result *domain.SearchResult, userContext UserContext) float64 {
	if len(userContext.PreferredTypes) == 0 {
		return 0.5 // Neutral score if no preferences
	}

	for _, preferredType := range userContext.PreferredTypes {
		if result.Type == preferredType {
			return 1.0
		}
	}

	return 0.2 // Lower score for non-preferred types
}

// calculateEngagementScore calculates score based on engagement metrics
func (r *ResultRanker) calculateEngagementScore(result *domain.SearchResult) float64 {
	// This would typically use engagement metrics from the metadata
	// For now, we'll use some heuristics based on content type and metadata
	
	metadata := result.Metadata
	if metadata == nil {
		return 0.5
	}

	var score float64

	// Check for engagement indicators in metadata
	if likes, ok := metadata["likes"].(float64); ok && likes > 0 {
		score += math.Min(likes/100.0, 0.5) // Normalize likes
	}

	if comments, ok := metadata["comments"].(float64); ok && comments > 0 {
		score += math.Min(comments/50.0, 0.3) // Normalize comments
	}

	if views, ok := metadata["views"].(float64); ok && views > 0 {
		score += math.Min(views/1000.0, 0.2) // Normalize views
	}

	// Content type specific scoring
	switch result.Type {
	case models.DocumentTypePost:
		score += 0.1 // Posts are generally more engaging
	case models.DocumentTypeFile:
		score += 0.05 // Files are moderately engaging
	case models.DocumentTypeEvent:
		score += 0.15 // Events are highly engaging
	}

	return math.Min(score, 1.0)
}

// Helper functions

func (r *ResultRanker) containsPartialMatch(text, term string) bool {
	if len(term) < 3 {
		return false
	}
	
	// Check for partial matches (substring of 3+ characters)
	for i := 0; i <= len(term)-3; i++ {
		substring := term[i : i+3]
		if strings.Contains(text, substring) {
			return true
		}
	}
	
	return false
}

func (r *ResultRanker) extractTeamID(result *domain.SearchResult) string {
	if result.Metadata == nil {
		return ""
	}
	
	if teamID, ok := result.Metadata["team_id"].(string); ok {
		return teamID
	}
	
	return ""
}

// DiversifyResults ensures diversity in search results
func (r *ResultRanker) DiversifyResults(results []domain.SearchResult, maxPerType int) []domain.SearchResult {
	if len(results) == 0 || maxPerType <= 0 {
		return results
	}

	typeCounts := make(map[models.SearchDocumentType]int)
	var diversified []domain.SearchResult

	for _, result := range results {
		if typeCounts[result.Type] < maxPerType {
			diversified = append(diversified, result)
			typeCounts[result.Type]++
		}
	}

	return diversified
}

// BoostResults applies additional boosting based on specific criteria
func (r *ResultRanker) BoostResults(results []domain.SearchResult, boostCriteria BoostCriteria) []domain.SearchResult {
	for i := range results {
		boost := r.calculateBoost(&results[i], boostCriteria)
		results[i].Score *= (1.0 + boost)
	}

	// Re-sort after boosting
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	return results
}

// BoostCriteria defines criteria for boosting search results
type BoostCriteria struct {
	FavoriteContentTypes []models.SearchDocumentType
	RecentContentBoost   float64
	OwnContentBoost      float64
	TeamContentBoost     float64
}

func (r *ResultRanker) calculateBoost(result *domain.SearchResult, criteria BoostCriteria) float64 {
	var boost float64

	// Boost favorite content types
	for _, favoriteType := range criteria.FavoriteContentTypes {
		if result.Type == favoriteType {
			boost += 0.2
			break
		}
	}

	// Boost recent content
	if time.Since(result.UpdatedAt).Hours() < 24 {
		boost += criteria.RecentContentBoost
	}

	return boost
}
