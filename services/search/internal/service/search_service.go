package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/search/internal/domain"
	"github.com/swork-team/platform/services/search/internal/embedding"
	"github.com/swork-team/platform/services/search/internal/indexer"
	"github.com/swork-team/platform/services/search/internal/models"
	"github.com/swork-team/platform/services/search/internal/repository"
	"github.com/swork-team/platform/services/search/internal/vector"
)

// SearchService provides unified search functionality
type SearchService struct {
	repo             *repository.SearchRepository
	embeddingService *embedding.EmbeddingService
	vectorService    *vector.VectorService
	indexer          *indexer.ContentIndexer
}

// NewSearchService creates a new search service
func NewSearchService(
	repo *repository.SearchRepository,
	embeddingService *embedding.EmbeddingService,
	vectorService *vector.VectorService,
	indexer *indexer.ContentIndexer,
) *SearchService {
	return &SearchService{
		repo:             repo,
		embeddingService: embeddingService,
		vectorService:    vectorService,
		indexer:          indexer,
	}
}

// Search performs a unified search across all content types
func (s *SearchService) Search(ctx context.Context, req *domain.SearchRequest, userID uuid.UUID, userTeams []uuid.UUID) (*domain.SearchResponse, error) {
	startTime := time.Now()
	
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid search request: %w", err)
	}
	
	// Set defaults
	req.SetDefaults()
	
	var results []domain.SearchResult
	var total int64
	var facets map[string]domain.Facet
	
	// Perform search based on options
	if req.Options.SemanticSearch || req.Options.HybridSearch {
		// Semantic/hybrid search using vector similarity
		vectorResults, err := s.performVectorSearch(ctx, req, userID, userTeams)
		if err != nil {
			log.Printf("Vector search failed: %v", err)
			// Fall back to text search
			return s.performTextSearch(ctx, req, userID, userTeams, startTime)
		}
		
		if req.Options.HybridSearch {
			// Combine with text search results
			textResults, err := s.performTextSearch(ctx, req, userID, userTeams, startTime)
			if err != nil {
				log.Printf("Text search failed in hybrid mode: %v", err)
			} else {
				// Merge and re-rank results
				results = s.mergeResults(vectorResults.Results, textResults.Results)
				total = vectorResults.Total + textResults.Total
			}
		} else {
			results = vectorResults.Results
			total = vectorResults.Total
		}
	} else {
		// Traditional text search
		textResults, err := s.performTextSearch(ctx, req, userID, userTeams, startTime)
		if err != nil {
			return nil, err
		}
		results = textResults.Results
		total = textResults.Total
		facets = textResults.Facets
	}
	
	// Generate facets if requested and not already generated
	if req.Options.IncludeFacets && facets == nil {
		facets = s.generateFacets(results)
	}
	
	// Record query for analytics
	s.recordQuery(ctx, req, userID, len(results), time.Since(startTime))
	
	return &domain.SearchResponse{
		Results:    results,
		Total:      total,
		Facets:     facets,
		Query:      req.Query,
		Took:       time.Since(startTime).Milliseconds(),
		Pagination: req.Pagination,
	}, nil
}

// performVectorSearch performs semantic search using vector similarity
func (s *SearchService) performVectorSearch(ctx context.Context, req *domain.SearchRequest, userID uuid.UUID, userTeams []uuid.UUID) (*domain.SearchResponse, error) {
	// Generate query embedding
	queryVector, err := s.embeddingService.GenerateEmbedding(ctx, req.Query)
	if err != nil {
		return nil, fmt.Errorf("failed to generate query embedding: %w", err)
	}
	
	// Build vector search filter
	filter := s.buildVectorFilter(req, userID, userTeams)
	
	// Perform vector search
	vectorResults, err := s.vectorService.SearchSimilar(ctx, queryVector, req.Pagination.Limit, filter)
	if err != nil {
		return nil, fmt.Errorf("vector search failed: %w", err)
	}
	
	// Convert vector results to search results
	results := make([]domain.SearchResult, 0, len(vectorResults))
	for _, vr := range vectorResults {
		if vr.Score < req.Options.MinScore {
			continue
		}
		
		result := s.vectorResultToSearchResult(vr)
		if req.Options.IncludeHighlights {
			result.Highlights = s.generateHighlights(req.Query, result.Content)
		}
		results = append(results, result)
	}
	
	return &domain.SearchResponse{
		Results: results,
		Total:   int64(len(results)),
	}, nil
}

// performTextSearch performs traditional text-based search
func (s *SearchService) performTextSearch(ctx context.Context, req *domain.SearchRequest, userID uuid.UUID, userTeams []uuid.UUID, startTime time.Time) (*domain.SearchResponse, error) {
	// Build database filters
	filters := s.buildDatabaseFilters(req, userID, userTeams)
	
	// Perform database search
	docs, total, err := s.repo.SearchDocuments(ctx, req.Query, filters, req.Pagination.Limit, req.Pagination.Offset)
	if err != nil {
		return nil, fmt.Errorf("database search failed: %w", err)
	}
	
	// Convert documents to search results
	results := make([]domain.SearchResult, len(docs))
	for i, doc := range docs {
		results[i] = s.documentToSearchResult(doc)
		if req.Options.IncludeHighlights {
			results[i].Highlights = s.generateHighlights(req.Query, doc.Content)
		}
	}
	
	return &domain.SearchResponse{
		Results: results,
		Total:   total,
	}, nil
}

// buildVectorFilter builds filter for vector search
func (s *SearchService) buildVectorFilter(req *domain.SearchRequest, userID uuid.UUID, userTeams []uuid.UUID) map[string]interface{} {
	filter := make(map[string]interface{})
	
	// Access control filter
	accessFilter := []map[string]interface{}{
		{"visibility": "public"},
		{"user_id": userID.String()},
	}
	
	// Add team access
	for _, teamID := range userTeams {
		accessFilter = append(accessFilter, map[string]interface{}{
			"team_id":    teamID.String(),
			"visibility": "team",
		})
	}
	
	filter["should"] = accessFilter
	
	// Type filter
	if len(req.Types) > 0 {
		types := make([]string, len(req.Types))
		for i, t := range req.Types {
			types[i] = string(t)
		}
		filter["type"] = map[string]interface{}{"any": types}
	}
	
	// Date range filter
	if req.Filters.DateRange != nil {
		dateFilter := make(map[string]interface{})
		if req.Filters.DateRange.Start != nil {
			dateFilter["gte"] = req.Filters.DateRange.Start.Unix()
		}
		if req.Filters.DateRange.End != nil {
			dateFilter["lte"] = req.Filters.DateRange.End.Unix()
		}
		if len(dateFilter) > 0 {
			filter["created_at"] = dateFilter
		}
	}
	
	return filter
}

// buildDatabaseFilters builds filters for database search
func (s *SearchService) buildDatabaseFilters(req *domain.SearchRequest, userID uuid.UUID, userTeams []uuid.UUID) repository.SearchFilters {
	filters := repository.SearchFilters{
		Types: req.Types,
	}
	
	// Access control
	visibilityFilter := []models.SearchVisibility{models.VisibilityPublic}
	
	// Add user's private content
	filters.UserID = &userID
	
	// Add team content if user is in teams
	if len(userTeams) > 0 {
		visibilityFilter = append(visibilityFilter, models.VisibilityTeam)
		// Note: Team filtering would need to be handled in the repository
	}
	
	filters.Visibility = visibilityFilter
	
	// Date range filter
	if req.Filters.DateRange != nil {
		filters.DateRange = &repository.DateRange{
			Start: req.Filters.DateRange.Start,
			End:   req.Filters.DateRange.End,
		}
	}
	
	// Tags filter
	filters.Tags = req.Filters.Tags
	
	return filters
}

// vectorResultToSearchResult converts a vector search result to a search result
func (s *SearchService) vectorResultToSearchResult(vr vector.VectorSearchResult) domain.SearchResult {
	metadata := vr.Metadata
	
	return domain.SearchResult{
		ID:      vr.ID,
		Type:    models.SearchDocumentType(getString(metadata, "type")),
		Title:   getString(metadata, "title"),
		Content: getString(metadata, "content"),
		Summary: getString(metadata, "summary"),
		Tags:    getStringSlice(metadata, "tags"),
		Source: domain.SearchResultSource{
			Service: getString(metadata, "source_type"),
			ID:      getString(metadata, "source_id"),
			URL:     getString(metadata, "source_url"),
		},
		Score:     vr.Score,
		Metadata:  metadata,
		CreatedAt: time.Unix(getInt64(metadata, "created_at"), 0),
		UpdatedAt: time.Unix(getInt64(metadata, "updated_at"), 0),
	}
}

// documentToSearchResult converts a search document to a search result
func (s *SearchService) documentToSearchResult(doc *models.SearchDocument) domain.SearchResult {
	return domain.SearchResult{
		ID:      doc.ID.String(),
		Type:    doc.Type,
		Title:   doc.Title,
		Content: doc.Content,
		Summary: doc.Summary,
		Tags:    doc.Tags,
		Source: domain.SearchResultSource{
			Service: doc.SourceType,
			ID:      doc.SourceID,
			URL:     doc.SourceURL,
		},
		Score:     doc.SearchScore,
		Metadata:  doc.Metadata,
		CreatedAt: doc.CreatedAt,
		UpdatedAt: doc.UpdatedAt,
	}
}

// mergeResults merges and re-ranks results from different search methods
func (s *SearchService) mergeResults(vectorResults, textResults []domain.SearchResult) []domain.SearchResult {
	// Simple merge strategy: combine and sort by score
	// In production, you might want more sophisticated ranking
	
	resultMap := make(map[string]domain.SearchResult)
	
	// Add vector results with higher weight
	for _, result := range vectorResults {
		result.Score = result.Score * 1.2 // Boost semantic relevance
		resultMap[result.ID] = result
	}
	
	// Add text results, combining scores if already present
	for _, result := range textResults {
		if existing, exists := resultMap[result.ID]; exists {
			existing.Score = (existing.Score + result.Score) / 2
			resultMap[result.ID] = existing
		} else {
			resultMap[result.ID] = result
		}
	}
	
	// Convert back to slice and sort by score
	merged := make([]domain.SearchResult, 0, len(resultMap))
	for _, result := range resultMap {
		merged = append(merged, result)
	}
	
	// Sort by score descending
	for i := 0; i < len(merged)-1; i++ {
		for j := i + 1; j < len(merged); j++ {
			if merged[i].Score < merged[j].Score {
				merged[i], merged[j] = merged[j], merged[i]
			}
		}
	}
	
	return merged
}

// generateHighlights generates highlighted text snippets
func (s *SearchService) generateHighlights(query, content string) []string {
	if query == "" || content == "" {
		return nil
	}
	
	queryTerms := strings.Fields(strings.ToLower(query))
	contentLower := strings.ToLower(content)
	
	var highlights []string
	for _, term := range queryTerms {
		if strings.Contains(contentLower, term) {
			// Find the term in the original content and create a snippet
			start := strings.Index(contentLower, term)
			if start >= 0 {
				snippetStart := start - 50
				if snippetStart < 0 {
					snippetStart = 0
				}
				snippetEnd := start + len(term) + 50
				if snippetEnd > len(content) {
					snippetEnd = len(content)
				}
				
				snippet := content[snippetStart:snippetEnd]
				highlights = append(highlights, "..."+snippet+"...")
			}
		}
	}
	
	return highlights
}

// generateFacets generates search facets from results
func (s *SearchService) generateFacets(results []domain.SearchResult) map[string]domain.Facet {
	facets := make(map[string]domain.Facet)
	
	// Type facets
	typeCounts := make(map[string]int64)
	for _, result := range results {
		typeCounts[string(result.Type)]++
	}
	
	typeValues := make([]domain.FacetValue, 0, len(typeCounts))
	for t, count := range typeCounts {
		typeValues = append(typeValues, domain.FacetValue{
			Value: t,
			Count: count,
		})
	}
	facets["types"] = domain.Facet{Values: typeValues}
	
	return facets
}

// recordQuery records a search query for analytics
func (s *SearchService) recordQuery(ctx context.Context, req *domain.SearchRequest, userID uuid.UUID, resultCount int, responseTime time.Duration) {
	// Create query hash for deduplication
	hash := md5.Sum([]byte(req.Query + userID.String()))
	queryHash := fmt.Sprintf("%x", hash)
	
	query := &models.SearchQuery{
		UserID:       userID,
		Query:        req.Query,
		QueryHash:    queryHash,
		Filters:      req.Filters.Metadata,
		ResultCount:  resultCount,
		ResponseTime: responseTime.Milliseconds(),
	}
	
	// Record asynchronously to not slow down search
	go func() {
		if err := s.repo.RecordQuery(context.Background(), query); err != nil {
			log.Printf("Failed to record search query: %v", err)
		}
	}()
}

// Helper functions for extracting values from metadata
func getString(metadata map[string]interface{}, key string) string {
	if val, ok := metadata[key].(string); ok {
		return val
	}
	return ""
}

func getStringSlice(metadata map[string]interface{}, key string) []string {
	if val, ok := metadata[key].([]interface{}); ok {
		result := make([]string, len(val))
		for i, v := range val {
			if s, ok := v.(string); ok {
				result[i] = s
			}
		}
		return result
	}
	return nil
}

func getInt64(metadata map[string]interface{}, key string) int64 {
	if val, ok := metadata[key].(float64); ok {
		return int64(val)
	}
	if val, ok := metadata[key].(int64); ok {
		return val
	}
	return 0
}
