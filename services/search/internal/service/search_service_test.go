package service

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/swork-team/platform/services/search/internal/domain"
	"github.com/swork-team/platform/services/search/internal/models"
)

// Mock implementations for testing
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) SearchDocuments(ctx context.Context, query string, filters interface{}, limit, offset int) ([]*models.SearchDocument, int64, error) {
	args := m.Called(ctx, query, filters, limit, offset)
	return args.Get(0).([]*models.SearchDocument), args.Get(1).(int64), args.Error(2)
}

func (m *MockRepository) RecordQuery(ctx context.Context, query *models.SearchQuery) error {
	args := m.Called(ctx, query)
	return args.Error(0)
}

type MockEmbeddingService struct {
	mock.Mock
}

func (m *MockEmbeddingService) GenerateEmbedding(ctx context.Context, text string) ([]float32, error) {
	args := m.Called(ctx, text)
	return args.Get(0).([]float32), args.Error(1)
}

type MockVectorService struct {
	mock.Mock
}

func (m *MockVectorService) SearchSimilar(ctx context.Context, vector []float32, limit int, filter map[string]interface{}) ([]interface{}, error) {
	args := m.Called(ctx, vector, limit, filter)
	return args.Get(0).([]interface{}), args.Error(1)
}

type MockIndexer struct {
	mock.Mock
}

func TestSearchService_Search(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockRepository)
	mockEmbedding := new(MockEmbeddingService)
	mockVector := new(MockVectorService)
	mockIndexer := new(MockIndexer)

	// Create service
	service := NewSearchService(mockRepo, mockEmbedding, mockVector, mockIndexer)

	// Test data
	userID := uuid.New()
	userTeams := []uuid.UUID{uuid.New()}
	
	searchReq := &domain.SearchRequest{
		Query: "test search",
		Types: []models.SearchDocumentType{models.DocumentTypeUser},
		Pagination: domain.SearchPagination{
			Limit:  10,
			Offset: 0,
		},
		Options: domain.SearchOptions{
			SemanticSearch:    false,
			IncludeHighlights: true,
		},
	}

	// Mock expectations
	mockDocs := []*models.SearchDocument{
		{
			ID:      uuid.New(),
			Type:    models.DocumentTypeUser,
			Title:   "Test User",
			Content: "Test user content",
			UserID:  userID,
		},
	}

	mockRepo.On("SearchDocuments", mock.Anything, "test search", mock.Anything, 10, 0).
		Return(mockDocs, int64(1), nil)

	// Execute test
	ctx := context.Background()
	result, err := service.Search(ctx, searchReq, userID, userTeams)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.Total)
	assert.Len(t, result.Results, 1)
	assert.Equal(t, "Test User", result.Results[0].Title)

	// Verify mock calls
	mockRepo.AssertExpectations(t)
}

func TestSearchService_SemanticSearch(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockRepository)
	mockEmbedding := new(MockEmbeddingService)
	mockVector := new(MockVectorService)
	mockIndexer := new(MockIndexer)

	// Create service
	service := NewSearchService(mockRepo, mockEmbedding, mockVector, mockIndexer)

	// Test data
	userID := uuid.New()
	userTeams := []uuid.UUID{}
	
	searchReq := &domain.SearchRequest{
		Query: "machine learning",
		Pagination: domain.SearchPagination{
			Limit:  5,
			Offset: 0,
		},
		Options: domain.SearchOptions{
			SemanticSearch: true,
			MinScore:       0.7,
		},
	}

	// Mock expectations
	mockVector := []float32{0.1, 0.2, 0.3}
	mockEmbedding.On("GenerateEmbedding", mock.Anything, "machine learning").
		Return(mockVector, nil)

	mockVectorResults := []interface{}{
		map[string]interface{}{
			"id":    "doc1",
			"score": 0.85,
			"metadata": map[string]interface{}{
				"title":   "ML Tutorial",
				"content": "Machine learning basics",
				"type":    "post",
			},
		},
	}

	mockVector.On("SearchSimilar", mock.Anything, mockVector, 5, mock.Anything).
		Return(mockVectorResults, nil)

	// Execute test
	ctx := context.Background()
	result, err := service.Search(ctx, searchReq, userID, userTeams)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Results, 1)
	assert.Equal(t, "ML Tutorial", result.Results[0].Title)
	assert.True(t, result.Results[0].Score >= 0.7)

	// Verify mock calls
	mockEmbedding.AssertExpectations(t)
	mockVector.AssertExpectations(t)
}

func TestSearchRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		request domain.SearchRequest
		wantErr bool
	}{
		{
			name: "valid request",
			request: domain.SearchRequest{
				Query: "test query",
				Pagination: domain.SearchPagination{
					Limit:  20,
					Offset: 0,
				},
			},
			wantErr: false,
		},
		{
			name: "empty query",
			request: domain.SearchRequest{
				Query: "",
			},
			wantErr: true,
		},
		{
			name: "query too long",
			request: domain.SearchRequest{
				Query: string(make([]byte, 1001)), // 1001 characters
			},
			wantErr: true,
		},
		{
			name: "negative limit",
			request: domain.SearchRequest{
				Query: "test",
				Pagination: domain.SearchPagination{
					Limit: -1,
				},
			},
			wantErr: true,
		},
		{
			name: "limit too high",
			request: domain.SearchRequest{
				Query: "test",
				Pagination: domain.SearchPagination{
					Limit: 101,
				},
			},
			wantErr: true,
		},
		{
			name: "invalid sort field",
			request: domain.SearchRequest{
				Query: "test",
				Sort: domain.SearchSort{
					Field: "invalid_field",
				},
			},
			wantErr: true,
		},
		{
			name: "invalid sort order",
			request: domain.SearchRequest{
				Query: "test",
				Sort: domain.SearchSort{
					Field: "relevance",
					Order: "invalid_order",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestSearchRequest_SetDefaults(t *testing.T) {
	req := &domain.SearchRequest{
		Query: "test",
	}

	req.SetDefaults()

	assert.Equal(t, 20, req.Pagination.Limit)
	assert.Equal(t, "relevance", req.Sort.Field)
	assert.Equal(t, "desc", req.Sort.Order)
	assert.True(t, req.Options.HybridSearch) // Should be enabled when semantic search is enabled
}

func TestSearchDocument_IsAccessibleBy(t *testing.T) {
	userID := uuid.New()
	teamID := uuid.New()
	otherUserID := uuid.New()
	otherTeamID := uuid.New()

	tests := []struct {
		name       string
		doc        models.SearchDocument
		userID     uuid.UUID
		userTeams  []uuid.UUID
		accessible bool
	}{
		{
			name: "public document",
			doc: models.SearchDocument{
				Visibility: models.VisibilityPublic,
			},
			userID:     userID,
			userTeams:  []uuid.UUID{},
			accessible: true,
		},
		{
			name: "private document - owner",
			doc: models.SearchDocument{
				UserID:     userID,
				Visibility: models.VisibilityPrivate,
			},
			userID:     userID,
			userTeams:  []uuid.UUID{},
			accessible: true,
		},
		{
			name: "private document - not owner",
			doc: models.SearchDocument{
				UserID:     otherUserID,
				Visibility: models.VisibilityPrivate,
			},
			userID:     userID,
			userTeams:  []uuid.UUID{},
			accessible: false,
		},
		{
			name: "team document - member",
			doc: models.SearchDocument{
				TeamID:     &teamID,
				Visibility: models.VisibilityTeam,
			},
			userID:     userID,
			userTeams:  []uuid.UUID{teamID},
			accessible: true,
		},
		{
			name: "team document - not member",
			doc: models.SearchDocument{
				TeamID:     &otherTeamID,
				Visibility: models.VisibilityTeam,
			},
			userID:     userID,
			userTeams:  []uuid.UUID{teamID},
			accessible: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			accessible := tt.doc.IsAccessibleBy(tt.userID, tt.userTeams)
			assert.Equal(t, tt.accessible, accessible)
		})
	}
}

func TestSearchDocument_GetContentForEmbedding(t *testing.T) {
	doc := &models.SearchDocument{
		Title:   "Test Title",
		Content: "Test content",
		Summary: "Test summary",
		Tags:    []string{"tag1", "tag2"},
	}

	content := doc.GetContentForEmbedding()
	expected := "Test Title Test content Test summary tag1 tag2"
	assert.Equal(t, expected, content)
}

// Benchmark tests
func BenchmarkSearchService_Search(b *testing.B) {
	// Setup mocks
	mockRepo := new(MockRepository)
	mockEmbedding := new(MockEmbeddingService)
	mockVector := new(MockVectorService)
	mockIndexer := new(MockIndexer)

	service := NewSearchService(mockRepo, mockEmbedding, mockVector, mockIndexer)

	userID := uuid.New()
	userTeams := []uuid.UUID{}
	
	searchReq := &domain.SearchRequest{
		Query: "benchmark test",
		Pagination: domain.SearchPagination{
			Limit:  10,
			Offset: 0,
		},
	}

	// Mock setup
	mockDocs := []*models.SearchDocument{
		{
			ID:      uuid.New(),
			Type:    models.DocumentTypeUser,
			Title:   "Benchmark User",
			Content: "Benchmark content",
			UserID:  userID,
		},
	}

	mockRepo.On("SearchDocuments", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(mockDocs, int64(1), nil)

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.Search(ctx, searchReq, userID, userTeams)
		if err != nil {
			b.Fatal(err)
		}
	}
}
