package embedding

import (
	"context"
	"fmt"

	"github.com/swork-team/platform/services/search/internal/config"
)

// Embedder interface defines the contract for embedding generation
type Embedder interface {
	GenerateEmbedding(ctx context.Context, text string) ([]float32, error)
	GenerateEmbeddings(ctx context.Context, texts []string) ([][]float32, error)
	BatchGenerateEmbeddings(ctx context.Context, texts []string, batchSize int) ([][]float32, error)
	GetDimensions() int
	GetModel() string
}

// EmbeddingService provides embedding functionality
type EmbeddingService struct {
	embedder Embedder
	config   config.AIConfig
}

// NewEmbeddingService creates a new embedding service
func NewEmbeddingService(cfg config.AIConfig) (*EmbeddingService, error) {
	var embedder Embedder
	
	switch cfg.Provider {
	case "openai":
		if cfg.APIKey == "" {
			return nil, fmt.Errorf("OpenAI API key is required")
		}
		embedder = NewOpenAIEmbedder(cfg.<PERSON>Key, cfg.Model)
	case "huggingface":
		// TODO: Implement HuggingFace embedder
		return nil, fmt.Errorf("HuggingFace embedder not implemented yet")
	case "local":
		// TODO: Implement local embedder (e.g., using sentence-transformers)
		return nil, fmt.Errorf("local embedder not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported embedding provider: %s", cfg.Provider)
	}
	
	return &EmbeddingService{
		embedder: embedder,
		config:   cfg,
	}, nil
}

// GenerateEmbedding generates an embedding for a single text
func (es *EmbeddingService) GenerateEmbedding(ctx context.Context, text string) ([]float32, error) {
	return es.embedder.GenerateEmbedding(ctx, text)
}

// GenerateEmbeddings generates embeddings for multiple texts
func (es *EmbeddingService) GenerateEmbeddings(ctx context.Context, texts []string) ([][]float32, error) {
	return es.embedder.GenerateEmbeddings(ctx, texts)
}

// BatchGenerateEmbeddings generates embeddings in batches
func (es *EmbeddingService) BatchGenerateEmbeddings(ctx context.Context, texts []string, batchSize int) ([][]float32, error) {
	return es.embedder.BatchGenerateEmbeddings(ctx, texts, batchSize)
}

// GetDimensions returns the dimension size of the embeddings
func (es *EmbeddingService) GetDimensions() int {
	return es.embedder.GetDimensions()
}

// GetModel returns the model name
func (es *EmbeddingService) GetModel() string {
	return es.embedder.GetModel()
}

// GetConfig returns the AI configuration
func (es *EmbeddingService) GetConfig() config.AIConfig {
	return es.config
}
