package config

import (
	"os"
	"strconv"

	"github.com/swork-team/platform/pkg/config"
)

// SearchConfig holds search service specific configuration
type SearchConfig struct {
	Server   config.ServerConfig
	Database config.DatabaseConfig
	Redis    config.RedisConfig
	Search   SearchEngineConfig
	AI       AIConfig
}

// SearchEngineConfig holds search engine configuration
type SearchEngineConfig struct {
	Provider     string // "qdrant", "weaviate", "elasticsearch"
	Host         string
	Port         int
	APIKey       string
	IndexName    string
	VectorSize   int
	BatchSize    int
	MaxResults   int
}

// AIConfig holds AI service configuration
type AIConfig struct {
	Provider    string // "openai", "huggingface", "local"
	APIKey      string
	Model       string
	MaxTokens   int
	Temperature float64
}

// LoadConfig loads the search service configuration
func LoadConfig() *SearchConfig {
	config.LoadEnvFile()

	return &SearchConfig{
		Server:   config.NewServerConfig("SEARCH_PORT", "SEARCH_HOST", "8009", "0.0.0.0"),
		Database: config.NewDatabaseConfig("SEARCH_"),
		Redis:    config.NewRedisConfig(),
		Search:   loadSearchEngineConfig(),
		AI:       loadAIConfig(),
	}
}

func loadSearchEngineConfig() SearchEngineConfig {
	port, _ := strconv.Atoi(getEnv("SEARCH_ENGINE_PORT", "6333"))
	vectorSize, _ := strconv.Atoi(getEnv("SEARCH_VECTOR_SIZE", "1536"))
	batchSize, _ := strconv.Atoi(getEnv("SEARCH_BATCH_SIZE", "100"))
	maxResults, _ := strconv.Atoi(getEnv("SEARCH_MAX_RESULTS", "100"))

	return SearchEngineConfig{
		Provider:   getEnv("SEARCH_ENGINE_PROVIDER", "qdrant"),
		Host:       getEnv("SEARCH_ENGINE_HOST", "localhost"),
		Port:       port,
		APIKey:     getEnv("SEARCH_ENGINE_API_KEY", ""),
		IndexName:  getEnv("SEARCH_INDEX_NAME", "swork_search"),
		VectorSize: vectorSize,
		BatchSize:  batchSize,
		MaxResults: maxResults,
	}
}

func loadAIConfig() AIConfig {
	maxTokens, _ := strconv.Atoi(getEnv("AI_MAX_TOKENS", "8192"))
	temperature, _ := strconv.ParseFloat(getEnv("AI_TEMPERATURE", "0.0"), 64)

	return AIConfig{
		Provider:    getEnv("AI_PROVIDER", "openai"),
		APIKey:      getEnv("AI_API_KEY", ""),
		Model:       getEnv("AI_MODEL", "text-embedding-3-small"),
		MaxTokens:   maxTokens,
		Temperature: temperature,
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
