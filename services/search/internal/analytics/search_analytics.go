package analytics

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/services/search/internal/models"
	"github.com/swork-team/platform/services/search/internal/repository"
)

// SearchAnalyticsService handles search analytics and monitoring
type SearchAnalyticsService struct {
	repo        *repository.SearchRepository
	redisClient *redis.Client
}

// NewSearchAnalyticsService creates a new search analytics service
func NewSearchAnalyticsService(repo *repository.SearchRepository, redisClient *redis.Client) *SearchAnalyticsService {
	return &SearchAnalyticsService{
		repo:        repo,
		redisClient: redisClient,
	}
}

// SearchMetrics represents search performance metrics
type SearchMetrics struct {
	TotalQueries      int64                  `json:"total_queries"`
	UniqueUsers       int64                  `json:"unique_users"`
	AvgResponseTime   float64                `json:"avg_response_time"`
	SuccessRate       float64                `json:"success_rate"`
	PopularQueries    []QueryFrequency       `json:"popular_queries"`
	ContentTypeStats  map[string]int64       `json:"content_type_stats"`
	HourlyDistribution map[int]int64         `json:"hourly_distribution"`
	DailyTrends       []DailyMetric          `json:"daily_trends"`
}

// QueryFrequency represents query frequency data
type QueryFrequency struct {
	Query     string `json:"query"`
	Count     int64  `json:"count"`
	AvgScore  float64 `json:"avg_score"`
}

// DailyMetric represents daily search metrics
type DailyMetric struct {
	Date         time.Time `json:"date"`
	QueryCount   int64     `json:"query_count"`
	UniqueUsers  int64     `json:"unique_users"`
	AvgResponse  float64   `json:"avg_response"`
}

// UserSearchBehavior represents user search behavior analytics
type UserSearchBehavior struct {
	UserID              uuid.UUID                      `json:"user_id"`
	TotalSearches       int64                          `json:"total_searches"`
	PreferredTypes      []models.SearchDocumentType    `json:"preferred_types"`
	SearchPatterns      []SearchPattern                `json:"search_patterns"`
	AvgResponseTime     float64                        `json:"avg_response_time"`
	LastSearchTime      time.Time                      `json:"last_search_time"`
	PopularQueries      []string                       `json:"popular_queries"`
}

// SearchPattern represents search behavior patterns
type SearchPattern struct {
	Pattern     string    `json:"pattern"`
	Frequency   int64     `json:"frequency"`
	LastUsed    time.Time `json:"last_used"`
}

// RecordSearchQuery records a search query for analytics
func (s *SearchAnalyticsService) RecordSearchQuery(ctx context.Context, userID uuid.UUID, query string, resultCount int, responseTime time.Duration, success bool) error {
	// Record in database
	searchQuery := &models.SearchQuery{
		UserID:       userID,
		Query:        query,
		QueryHash:    s.generateQueryHash(query),
		ResultCount:  resultCount,
		ResponseTime: responseTime.Milliseconds(),
	}

	if err := s.repo.RecordQuery(ctx, searchQuery); err != nil {
		log.Printf("Failed to record search query in database: %v", err)
	}

	// Record in Redis for real-time analytics
	go func() {
		if err := s.recordInRedis(context.Background(), userID, query, resultCount, responseTime, success); err != nil {
			log.Printf("Failed to record search query in Redis: %v", err)
		}
	}()

	return nil
}

// recordInRedis records search metrics in Redis for real-time analytics
func (s *SearchAnalyticsService) recordInRedis(ctx context.Context, userID uuid.UUID, query string, resultCount int, responseTime time.Duration, success bool) error {
	now := time.Now()
	date := now.Format("2006-01-02")
	hour := now.Hour()

	pipe := s.redisClient.Pipeline()

	// Daily metrics
	dailyKey := fmt.Sprintf("search:analytics:daily:%s", date)
	pipe.HIncrBy(ctx, dailyKey, "total_queries", 1)
	pipe.SAdd(ctx, fmt.Sprintf("search:analytics:users:%s", date), userID.String())
	pipe.Expire(ctx, dailyKey, 30*24*time.Hour) // Keep for 30 days

	// Hourly distribution
	hourlyKey := fmt.Sprintf("search:analytics:hourly:%s", date)
	pipe.HIncrBy(ctx, hourlyKey, fmt.Sprintf("hour_%d", hour), 1)
	pipe.Expire(ctx, hourlyKey, 30*24*time.Hour)

	// Response time tracking
	responseKey := fmt.Sprintf("search:analytics:response:%s", date)
	pipe.LPush(ctx, responseKey, responseTime.Milliseconds())
	pipe.LTrim(ctx, responseKey, 0, 9999) // Keep last 10k response times
	pipe.Expire(ctx, responseKey, 30*24*time.Hour)

	// Query frequency
	queryKey := "search:analytics:queries"
	pipe.ZIncrBy(ctx, queryKey, 1, query)
	pipe.Expire(ctx, queryKey, 30*24*time.Hour)

	// Success rate
	if success {
		pipe.HIncrBy(ctx, dailyKey, "successful_queries", 1)
	}

	// User behavior
	userKey := fmt.Sprintf("search:analytics:user:%s", userID.String())
	pipe.HIncrBy(ctx, userKey, "total_searches", 1)
	pipe.HSet(ctx, userKey, "last_search", now.Unix())
	pipe.Expire(ctx, userKey, 90*24*time.Hour) // Keep for 90 days

	_, err := pipe.Exec(ctx)
	return err
}

// GetSearchMetrics retrieves search metrics for a date range
func (s *SearchAnalyticsService) GetSearchMetrics(ctx context.Context, startDate, endDate time.Time) (*SearchMetrics, error) {
	metrics := &SearchMetrics{
		ContentTypeStats:   make(map[string]int64),
		HourlyDistribution: make(map[int]int64),
	}

	// Get database analytics
	analytics, err := s.repo.GetSearchAnalytics(ctx, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get analytics from database: %w", err)
	}

	// Aggregate database metrics
	for _, analytic := range analytics {
		metrics.TotalQueries += analytic.TotalQueries
		metrics.UniqueUsers += analytic.UniqueUsers
		metrics.AvgResponseTime += analytic.AvgResponseTime

		// Add to daily trends
		metrics.DailyTrends = append(metrics.DailyTrends, DailyMetric{
			Date:        analytic.Date,
			QueryCount:  analytic.TotalQueries,
			UniqueUsers: analytic.UniqueUsers,
			AvgResponse: analytic.AvgResponseTime,
		})
	}

	// Calculate averages
	if len(analytics) > 0 {
		metrics.AvgResponseTime /= float64(len(analytics))
	}

	// Get Redis metrics for real-time data
	if err := s.enrichWithRedisMetrics(ctx, metrics, startDate, endDate); err != nil {
		log.Printf("Failed to enrich with Redis metrics: %v", err)
	}

	return metrics, nil
}

// enrichWithRedisMetrics enriches metrics with Redis data
func (s *SearchAnalyticsService) enrichWithRedisMetrics(ctx context.Context, metrics *SearchMetrics, startDate, endDate time.Time) error {
	// Get popular queries
	queries, err := s.redisClient.ZRevRangeWithScores(ctx, "search:analytics:queries", 0, 9).Result()
	if err == nil {
		for _, query := range queries {
			metrics.PopularQueries = append(metrics.PopularQueries, QueryFrequency{
				Query: query.Member.(string),
				Count: int64(query.Score),
			})
		}
	}

	// Get hourly distribution for the date range
	current := startDate
	for current.Before(endDate) || current.Equal(endDate) {
		dateStr := current.Format("2006-01-02")
		hourlyKey := fmt.Sprintf("search:analytics:hourly:%s", dateStr)
		
		hourlyData, err := s.redisClient.HGetAll(ctx, hourlyKey).Result()
		if err == nil {
			for hourStr, countStr := range hourlyData {
				var hour int
				if _, err := fmt.Sscanf(hourStr, "hour_%d", &hour); err == nil {
					var count int64
					if _, err := fmt.Sscanf(countStr, "%d", &count); err == nil {
						metrics.HourlyDistribution[hour] += count
					}
				}
			}
		}
		
		current = current.AddDate(0, 0, 1)
	}

	return nil
}

// GetUserSearchBehavior retrieves search behavior for a specific user
func (s *SearchAnalyticsService) GetUserSearchBehavior(ctx context.Context, userID uuid.UUID) (*UserSearchBehavior, error) {
	behavior := &UserSearchBehavior{
		UserID: userID,
	}

	// Get user data from Redis
	userKey := fmt.Sprintf("search:analytics:user:%s", userID.String())
	userData, err := s.redisClient.HGetAll(ctx, userKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get user data from Redis: %w", err)
	}

	// Parse user data
	if totalSearches, exists := userData["total_searches"]; exists {
		fmt.Sscanf(totalSearches, "%d", &behavior.TotalSearches)
	}

	if lastSearch, exists := userData["last_search"]; exists {
		var timestamp int64
		if fmt.Sscanf(lastSearch, "%d", &timestamp) == 1 {
			behavior.LastSearchTime = time.Unix(timestamp, 0)
		}
	}

	// Get user's search patterns from database
	// This would require additional queries to analyze user's search history
	// For now, we'll return the basic data

	return behavior, nil
}

// GetTopSearchTerms retrieves the most popular search terms
func (s *SearchAnalyticsService) GetTopSearchTerms(ctx context.Context, limit int) ([]QueryFrequency, error) {
	queries, err := s.redisClient.ZRevRangeWithScores(ctx, "search:analytics:queries", 0, int64(limit-1)).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get top search terms: %w", err)
	}

	var terms []QueryFrequency
	for _, query := range queries {
		terms = append(terms, QueryFrequency{
			Query: query.Member.(string),
			Count: int64(query.Score),
		})
	}

	return terms, nil
}

// GetSearchTrends retrieves search trends over time
func (s *SearchAnalyticsService) GetSearchTrends(ctx context.Context, days int) ([]DailyMetric, error) {
	var trends []DailyMetric
	
	for i := days - 1; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")
		
		dailyKey := fmt.Sprintf("search:analytics:daily:%s", dateStr)
		usersKey := fmt.Sprintf("search:analytics:users:%s", dateStr)
		responseKey := fmt.Sprintf("search:analytics:response:%s", dateStr)
		
		// Get daily metrics
		dailyData, err := s.redisClient.HGetAll(ctx, dailyKey).Result()
		if err != nil {
			continue
		}
		
		metric := DailyMetric{Date: date}
		
		if totalQueries, exists := dailyData["total_queries"]; exists {
			fmt.Sscanf(totalQueries, "%d", &metric.QueryCount)
		}
		
		// Get unique users count
		userCount, err := s.redisClient.SCard(ctx, usersKey).Result()
		if err == nil {
			metric.UniqueUsers = userCount
		}
		
		// Get average response time
		responseTimes, err := s.redisClient.LRange(ctx, responseKey, 0, -1).Result()
		if err == nil && len(responseTimes) > 0 {
			var total int64
			for _, timeStr := range responseTimes {
				var responseTime int64
				if fmt.Sscanf(timeStr, "%d", &responseTime) == 1 {
					total += responseTime
				}
			}
			if len(responseTimes) > 0 {
				metric.AvgResponse = float64(total) / float64(len(responseTimes))
			}
		}
		
		trends = append(trends, metric)
	}
	
	return trends, nil
}

// GenerateSearchReport generates a comprehensive search report
func (s *SearchAnalyticsService) GenerateSearchReport(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	metrics, err := s.GetSearchMetrics(ctx, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get search metrics: %w", err)
	}

	topTerms, err := s.GetTopSearchTerms(ctx, 10)
	if err != nil {
		log.Printf("Failed to get top search terms: %v", err)
		topTerms = []QueryFrequency{}
	}

	trends, err := s.GetSearchTrends(ctx, 30)
	if err != nil {
		log.Printf("Failed to get search trends: %v", err)
		trends = []DailyMetric{}
	}

	report := map[string]interface{}{
		"period": map[string]interface{}{
			"start_date": startDate.Format("2006-01-02"),
			"end_date":   endDate.Format("2006-01-02"),
		},
		"summary": map[string]interface{}{
			"total_queries":    metrics.TotalQueries,
			"unique_users":     metrics.UniqueUsers,
			"avg_response_ms":  metrics.AvgResponseTime,
			"success_rate":     metrics.SuccessRate,
		},
		"top_search_terms":     topTerms,
		"hourly_distribution":  metrics.HourlyDistribution,
		"daily_trends":         trends,
		"content_type_stats":   metrics.ContentTypeStats,
		"generated_at":         time.Now().Format(time.RFC3339),
	}

	return report, nil
}

// Helper functions

func (s *SearchAnalyticsService) generateQueryHash(query string) string {
	// Simple hash generation - in production, use a proper hash function
	return fmt.Sprintf("%x", []byte(query))
}

// CleanupOldData removes old analytics data
func (s *SearchAnalyticsService) CleanupOldData(ctx context.Context, retentionDays int) error {
	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
	
	// Clean up Redis data
	pattern := "search:analytics:*"
	keys, err := s.redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get Redis keys: %w", err)
	}
	
	for _, key := range keys {
		// Check if key contains a date and if it's older than cutoff
		// This is a simplified cleanup - in production, you'd want more sophisticated logic
		if s.isOldKey(key, cutoffDate) {
			s.redisClient.Del(ctx, key)
		}
	}
	
	return nil
}

func (s *SearchAnalyticsService) isOldKey(key string, cutoffDate time.Time) bool {
	// Simple date extraction from key - improve this in production
	// For now, just return false to avoid accidental deletion
	return false
}
