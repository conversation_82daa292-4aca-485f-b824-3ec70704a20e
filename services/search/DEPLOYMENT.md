# Search Service Deployment Guide

This guide covers deploying the Search Service in various environments.

## Prerequisites

### Required Services
- **PostgreSQL 14+** - For metadata and analytics storage
- **Qdrant** - Vector database for semantic search
- **Redis** - For caching and real-time updates
- **OpenAI API** - For embedding generation (or alternative)

### System Requirements
- **CPU**: 2+ cores (4+ recommended for production)
- **Memory**: 4GB+ (8GB+ recommended for production)
- **Storage**: 20GB+ (depends on index size)
- **Network**: Stable internet connection for OpenAI API

## Environment Configuration

### Environment Variables

Create a `.env` file with the following configuration:

```bash
# Service Configuration
APP_ENV=production
SEARCH_PORT=8009
SEARCH_HOST=0.0.0.0

# Database Configuration
SEARCH_DATABASE_URL=postgres://search_user:password@localhost:5432/search_db
SEARCH_DATABASE_MAX_OPEN_CONNS=25
SEARCH_DATABASE_MAX_IDLE_CONNS=5
SEARCH_DATABASE_CONN_MAX_LIFETIME=1h

# Vector Database (Qdrant)
SEARCH_ENGINE_PROVIDER=qdrant
SEARCH_ENGINE_HOST=localhost
SEARCH_ENGINE_PORT=6333
SEARCH_ENGINE_API_KEY=your_qdrant_api_key
SEARCH_INDEX_NAME=swork_search
SEARCH_VECTOR_SIZE=1536
SEARCH_BATCH_SIZE=100
SEARCH_MAX_RESULTS=100

# AI Configuration (OpenAI)
AI_PROVIDER=openai
AI_API_KEY=sk-your-openai-api-key
AI_MODEL=text-embedding-3-small
AI_MAX_TOKENS=8192
AI_TEMPERATURE=0.0

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

## Docker Deployment

### Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  search-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8009:8009"
    environment:
      - APP_ENV=production
      - SEARCH_DATABASE_URL=************************************************/search_db
      - SEARCH_ENGINE_HOST=qdrant
      - REDIS_URL=redis://redis:6379/0
      - AI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - qdrant
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8009/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: search_db
      POSTGRES_USER: search_user
      POSTGRES_PASSWORD: search_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  postgres_data:
  qdrant_data:
  redis_data:
```

### Dockerfile

Create `Dockerfile`:

```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git ca-certificates

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o search-service ./cmd/main.go

FROM alpine:latest

RUN apk --no-cache add ca-certificates curl
WORKDIR /root/

# Copy the binary
COPY --from=builder /app/search-service .

# Expose port
EXPOSE 8009

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8009/health || exit 1

# Run the application
CMD ["./search-service"]
```

## Kubernetes Deployment

### Namespace

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: search-service
```

### ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: search-config
  namespace: search-service
data:
  APP_ENV: "production"
  SEARCH_PORT: "8009"
  SEARCH_HOST: "0.0.0.0"
  SEARCH_ENGINE_PROVIDER: "qdrant"
  SEARCH_ENGINE_HOST: "qdrant-service"
  SEARCH_ENGINE_PORT: "6333"
  SEARCH_INDEX_NAME: "swork_search"
  SEARCH_VECTOR_SIZE: "1536"
  SEARCH_BATCH_SIZE: "100"
  AI_PROVIDER: "openai"
  AI_MODEL: "text-embedding-3-small"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
```

### Secret

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: search-secrets
  namespace: search-service
type: Opaque
stringData:
  SEARCH_DATABASE_URL: "*****************************************************/search_db"
  AI_API_KEY: "sk-your-openai-api-key"
  REDIS_PASSWORD: ""
```

### Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-service
  namespace: search-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: search-service
  template:
    metadata:
      labels:
        app: search-service
    spec:
      containers:
      - name: search-service
        image: your-registry/search-service:latest
        ports:
        - containerPort: 8009
        envFrom:
        - configMapRef:
            name: search-config
        - secretRef:
            name: search-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8009
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8009
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: search-service
  namespace: search-service
spec:
  selector:
    app: search-service
  ports:
  - protocol: TCP
    port: 8009
    targetPort: 8009
  type: ClusterIP
```

## Production Considerations

### Performance Tuning

1. **Database Optimization**
   - Use connection pooling
   - Optimize PostgreSQL configuration
   - Regular VACUUM and ANALYZE

2. **Vector Database Tuning**
   - Configure Qdrant memory settings
   - Optimize collection parameters
   - Use appropriate distance metrics

3. **Caching Strategy**
   - Redis memory optimization
   - Cache frequently accessed data
   - Implement cache warming

### Security

1. **Network Security**
   - Use TLS for all connections
   - Implement network policies
   - Restrict database access

2. **API Security**
   - Rate limiting
   - Authentication middleware
   - Input validation

3. **Secrets Management**
   - Use Kubernetes secrets or vault
   - Rotate API keys regularly
   - Monitor for leaked credentials

### Monitoring

1. **Health Checks**
   - Service health endpoints
   - Database connectivity
   - External service availability

2. **Metrics**
   - Search performance metrics
   - Indexing throughput
   - Error rates and latency

3. **Logging**
   - Structured logging
   - Log aggregation
   - Alert on errors

### Backup and Recovery

1. **Database Backups**
   - Regular PostgreSQL backups
   - Point-in-time recovery
   - Test restore procedures

2. **Vector Index Backups**
   - Qdrant snapshots
   - Index reconstruction procedures
   - Disaster recovery plan

### Scaling

1. **Horizontal Scaling**
   - Multiple service instances
   - Load balancing
   - Database read replicas

2. **Vertical Scaling**
   - Resource allocation
   - Performance monitoring
   - Capacity planning

## Troubleshooting

### Common Issues

1. **Service Won't Start**
   - Check environment variables
   - Verify database connectivity
   - Review logs for errors

2. **Search Performance Issues**
   - Monitor database queries
   - Check vector database performance
   - Review indexing status

3. **Memory Issues**
   - Monitor memory usage
   - Optimize batch sizes
   - Check for memory leaks

### Debug Commands

```bash
# Check service health
curl http://localhost:8009/health

# View logs
docker logs search-service

# Check database connection
psql $SEARCH_DATABASE_URL -c "SELECT 1"

# Test Qdrant connection
curl http://localhost:6333/collections

# Check Redis connection
redis-cli ping
```

## Migration Guide

### From Development to Production

1. Update environment variables
2. Configure production databases
3. Set up monitoring and alerting
4. Implement backup procedures
5. Test disaster recovery

### Version Updates

1. Review changelog
2. Test in staging environment
3. Plan maintenance window
4. Backup current state
5. Deploy with rollback plan
