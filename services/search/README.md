# Search Service

The Search Service provides unified semantic search capabilities across all content types in the SWork platform. It enables users to search for users, teams, posts, messages, files, events, and notifications using natural language queries with semantic understanding.

## Features

### 🔍 **Unified Search**
- Search across all content types in a single query
- Cross-service search with unified results
- Consistent search API across the platform

### 🧠 **Semantic Search**
- Natural language query understanding
- Vector embeddings for semantic similarity
- Context-aware search results
- Hybrid search combining semantic and keyword matching

### ⚡ **Performance**
- Real-time search with sub-second response times
- Efficient vector similarity search using Qdrant
- Optimized database queries with full-text search
- Intelligent result ranking and scoring

### 🔐 **Security & Privacy**
- Access control based on user permissions
- Team-based content filtering
- Visibility-aware search results
- Secure content indexing

### 📊 **Analytics**
- Search query analytics
- Performance monitoring
- Popular search terms tracking
- User search behavior insights

## Architecture

### Technology Stack
- **Vector Database**: Qdrant for semantic search
- **Embeddings**: OpenAI text-embedding-3-small
- **Database**: PostgreSQL for metadata and analytics
- **Search Engine**: Hybrid approach (vector + full-text)

### Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Search API    │    │  Content Index  │    │ Vector Database │
│                 │    │                 │    │    (Qdrant)     │
│ • Unified Query │────│ • Document Mgmt │────│ • Embeddings    │
│ • Result Rank   │    │ • Real-time Idx │    │ • Similarity    │
│ • Access Ctrl   │    │ • Batch Process │    │ • Filtering     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │  Embedding Gen  │              │
         │              │                 │              │
         └──────────────│ • OpenAI API    │──────────────┘
                        │ • Text Process  │
                        │ • Batch Embed   │
                        └─────────────────┘
```

## API Endpoints

### Search Endpoints

#### POST /search
Unified search across all content types.

```json
{
  "query": "team meeting next week",
  "types": ["events", "messages", "files"],
  "filters": {
    "date_range": {"start": "2024-01-01", "end": "2024-12-31"},
    "team_id": "uuid",
    "visibility": ["public", "team"]
  },
  "pagination": {"limit": 20, "offset": 0},
  "options": {
    "semantic_search": true,
    "include_highlights": true,
    "include_facets": true,
    "min_score": 0.7
  }
}
```

#### GET /search/quick
Quick search with query parameters.

```
GET /search/quick?q=project+update&types=files,messages&limit=10&semantic=true
```

#### GET /search/suggest
Search suggestions and autocomplete.

```
GET /search/suggest?q=team+meet&limit=5
```

#### GET /search/stats
Search analytics and statistics.

### Webhook Endpoints

#### POST /webhooks/content
Handle content updates from other services.

```json
{
  "type": "created",
  "service": "social",
  "entity": "post",
  "entity_id": "post_123",
  "user_id": "user_456",
  "team_id": "team_789",
  "data": {
    "content": "Project update meeting scheduled",
    "visibility": "team"
  },
  "timestamp": **********
}
```

#### POST /webhooks/content/bulk
Handle multiple content updates in batch.

## Configuration

### Environment Variables

```bash
# Server Configuration
SEARCH_PORT=8009
SEARCH_HOST=0.0.0.0

# Database Configuration
SEARCH_DATABASE_URL=postgres://user:pass@localhost:5432/search
SEARCH_DATABASE_MAX_OPEN_CONNS=25
SEARCH_DATABASE_MAX_IDLE_CONNS=5

# Vector Database (Qdrant)
SEARCH_ENGINE_PROVIDER=qdrant
SEARCH_ENGINE_HOST=localhost
SEARCH_ENGINE_PORT=6333
SEARCH_ENGINE_API_KEY=your_qdrant_api_key
SEARCH_INDEX_NAME=swork_search
SEARCH_VECTOR_SIZE=1536
SEARCH_BATCH_SIZE=100
SEARCH_MAX_RESULTS=100

# AI Configuration (OpenAI)
AI_PROVIDER=openai
AI_API_KEY=your_openai_api_key
AI_MODEL=text-embedding-3-small
AI_MAX_TOKENS=8192
AI_TEMPERATURE=0.0

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

## Content Types

The search service indexes the following content types:

### Users
- Names, usernames, emails
- Bio, location, profile information
- Public visibility by default

### Teams
- Team names, descriptions
- Categories, member information
- Visibility based on team settings

### Social Posts
- Text content, hashtags
- Location, media metadata
- Visibility: public, private, friends, team

### Messages
- Chat content, conversation context
- Private visibility (user access only)
- Real-time indexing

### Files & Documents
- File names, paths, content
- Metadata, file types
- Visibility: private, team, public

### Calendar Events
- Titles, descriptions, locations
- Attendee information
- Private visibility by default

### Notifications
- Titles, messages, metadata
- User-specific content
- Private visibility

## Development

### Setup

1. **Install Dependencies**
```bash
go mod download
```

2. **Setup Qdrant**
```bash
docker run -p 6333:6333 qdrant/qdrant
```

3. **Setup PostgreSQL**
```bash
# Database will be created automatically via migrations
```

4. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run Service**
```bash
go run cmd/main.go
```

### Testing

```bash
# Run unit tests
go test ./...

# Run integration tests
go test -tags=integration ./...

# Test search functionality
curl -X POST http://localhost:8009/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{"query": "test search", "types": ["users", "posts"]}'
```

### Indexing Content

Content is indexed automatically via webhooks from other services. To manually trigger indexing:

```bash
# Reindex all content
curl -X POST http://localhost:8009/admin/reindex \
  -H "Authorization: Bearer admin_token"

# Index specific content type
curl -X POST http://localhost:8009/admin/index \
  -H "Content-Type: application/json" \
  -d '{"type": "users", "batch_size": 100}'
```

## Monitoring

### Health Checks
- `/health` - Service health status
- `/webhooks/health` - Webhook service status

### Metrics
- Search query performance
- Indexing throughput
- Vector database status
- Embedding generation metrics

### Logging
- Structured logging with request tracing
- Search query logging for analytics
- Error tracking and alerting

## Integration

### Service Integration

Other services integrate with the search service by:

1. **Sending Webhooks** when content is created/updated/deleted
2. **Using Search API** to provide search functionality
3. **Configuring Access Control** for content visibility

### Example Webhook Integration

```go
// In your service
func (s *YourService) CreatePost(post *Post) error {
    // Create the post
    if err := s.repo.Create(post); err != nil {
        return err
    }
    
    // Send webhook to search service
    webhook := WebhookEvent{
        Type:     "created",
        Service:  "social",
        Entity:   "post",
        EntityID: post.ID,
        UserID:   post.UserID,
        TeamID:   post.TeamID,
        Data:     post.ToMap(),
        Timestamp: time.Now().Unix(),
    }
    
    return s.webhookClient.Send("search", "/webhooks/content", webhook)
}
```

## Performance Optimization

### Indexing Performance
- Batch processing for bulk operations
- Asynchronous webhook processing
- Intelligent retry mechanisms
- Rate limiting for external APIs

### Search Performance
- Vector similarity caching
- Query result caching
- Optimized database indexes
- Parallel processing for hybrid search

### Scaling
- Horizontal scaling support
- Load balancing for search queries
- Distributed vector database
- Microservice architecture
