package main

import (
	"context"
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	searchConfig "github.com/swork-team/platform/services/search/internal/config"
	"github.com/swork-team/platform/services/search/internal/embedding"
	"github.com/swork-team/platform/services/search/internal/handlers"
	"github.com/swork-team/platform/services/search/internal/indexer"
	"github.com/swork-team/platform/services/search/internal/models"
	"github.com/swork-team/platform/services/search/internal/repository"
	"github.com/swork-team/platform/services/search/internal/service"
	"github.com/swork-team/platform/services/search/internal/vector"
	"github.com/swork-team/platform/services/search/internal/webhooks"
)

func main() {
	// Load search-specific configuration
	searchCfg := searchConfig.LoadConfig()

	// Initialize standardized logger
	loggerManager := logger.NewServiceLoggerManager("search")
	serviceLogger := loggerManager.GetLogger()

	// Log service startup
	serviceLogger.Info("Search service starting up",
		logger.F("environment", os.Getenv("APP_ENV")),
		logger.F("port", searchCfg.Server.Port),
	)

	// Create config adapter for bootstrap
	configAdapter := server.NewConfigAdapter(searchCfg.Server, searchCfg.Database, searchCfg.Redis)

	// Define models to migrate
	searchModels := []interface{}{
		&models.SearchDocument{},
		&models.SearchIndex{},
		&models.SearchQuery{},
		&models.SearchAnalytics{},
	}

	// Use the new ServiceBootstrapper
	bootstrapper := server.NewServiceBootstrapper(server.ServiceBootstrapConfig{
		ServiceName:      "search",
		ConfigAdapter:    configAdapter,
		ModelsProvider:   func() []interface{} { return searchModels },
		RouterSetup:      createSearchRouterSetup(searchCfg),
		EnableExtensions: true,
		DatabaseType:     server.PostgreSQL,
	})

	bootstrapper.Bootstrap()
}

// createSearchRouterSetup returns the router setup function for search service
func createSearchRouterSetup(cfg *searchConfig.SearchConfig) server.RouterSetupFunction {
	return func(components *server.ServiceComponents) *gin.Engine {
		// Get database connection from components
		db := components.Database

		// Initialize services
		repo := repository.NewSearchRepository(db)

		// Initialize embedding service
		embeddingService, err := embedding.NewEmbeddingService(cfg.AI)
		if err != nil {
			log.Fatalf("Failed to initialize embedding service: %v", err)
		}

		// Initialize vector service
		vectorService, err := vector.NewVectorService(cfg.Search)
		if err != nil {
			log.Fatalf("Failed to initialize vector service: %v", err)
		}

		// Initialize vector collection
		ctx := context.Background()
		if err := vectorService.InitializeCollection(ctx, embeddingService.GetDimensions()); err != nil {
			log.Printf("Warning: Failed to initialize vector collection: %v", err)
		}

		// Initialize indexer
		contentIndexer := indexer.NewContentIndexer(repo, embeddingService, vectorService, cfg.Search.BatchSize)

		// Initialize search service
		searchService := service.NewSearchService(repo, embeddingService, vectorService, contentIndexer)

		// Initialize handlers
		searchHandler := handlers.NewSearchHandler(searchService)

		// Initialize webhook handlers
		documentFactory := indexer.NewDocumentFactory()
		webhookHandler := webhooks.NewContentWebhookHandler(contentIndexer, documentFactory)
 
		// Setup router
		return setupSearchRouter(searchHandler, webhookHandler, cfg)
	}
}

func setupSearchRouter(handler *handlers.SearchHandler, webhookHandler *webhooks.ContentWebhookHandler, cfg *searchConfig.SearchConfig) *gin.Engine {
	// Set Gin mode based on environment
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Add standard middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// Health check (no auth required)
	router.GET("/health", handler.Health)

	// Search routes
	searchGroup := router.Group("/search")
	{
		// Authenticated routes
		authGroup := searchGroup.Group("")
		authGroup.Use(middleware.AuthMiddleware())
		{
			// Main search endpoints
			authGroup.POST("", handler.Search)
			authGroup.GET("/quick", handler.QuickSearch)
			authGroup.GET("/suggest", handler.Suggest)
			authGroup.GET("/stats", handler.GetSearchStats)
		}
	}

	// Webhook routes (no auth required for service-to-service communication)
	webhookGroup := router.Group("/webhooks")
	{
		webhookGroup.GET("/health", webhookHandler.Health)
		webhookGroup.POST("/content", webhookHandler.HandleWebhook)
		webhookGroup.POST("/content/bulk", webhookHandler.HandleBulkWebhook)
	}

	return router
}
