package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/async"
	"github.com/swork-team/platform/pkg/cache"
	"github.com/swork-team/platform/pkg/clients"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/pkg/webhook"
	"github.com/swork-team/platform/services/team/internal/authorization"
	"github.com/swork-team/platform/services/team/internal/config"
	"github.com/swork-team/platform/services/team/internal/models"
	"github.com/swork-team/platform/services/team/internal/validation"
)

type TeamRepositoryInterface interface {
	CreateTeam(ctx context.Context, team *models.Team) error
	GetTeamByID(ctx context.Context, teamID uuid.UUID) (*models.Team, error)
	GetTeamBySlug(ctx context.Context, slug string) (*models.Team, error)
	UpdateTeam(ctx context.Context, team *models.Team) error
	DeleteTeam(ctx context.Context, teamID uuid.UUID) error
	IsSlugTaken(ctx context.Context, slug string, excludeTeamID *uuid.UUID) (bool, error)
	GetTeamsByMember(ctx context.Context, userID string, limit, offset int) ([]models.Team, error)
	GetTeamsByMemberWithCount(ctx context.Context, userID string, limit, offset int) ([]models.Team, int64, error)
	SearchTeams(ctx context.Context, query string, visibility models.TeamVisibility, limit, offset int) ([]models.Team, error)
	SearchTeamsWithCount(ctx context.Context, query string, visibility models.TeamVisibility, limit, offset int) ([]models.Team, int64, error)
	AddMember(ctx context.Context, member *models.TeamMember) error
	GetMember(ctx context.Context, teamID uuid.UUID, userID string) (*models.TeamMember, error)
	UpdateMember(ctx context.Context, member *models.TeamMember) error
	RemoveMember(ctx context.Context, teamID uuid.UUID, userID string) error
	GetMembers(ctx context.Context, teamID uuid.UUID, limit, offset int) ([]models.TeamMember, error)
	GetMembersWithCount(ctx context.Context, teamID uuid.UUID, limit, offset int) ([]models.TeamMember, int64, error)
	CreateInvitation(ctx context.Context, invitation *models.TeamInvitation) error
	GetInvitationByToken(ctx context.Context, token string) (*models.TeamInvitation, error)
	UpdateInvitation(ctx context.Context, invitation *models.TeamInvitation) error
	GetSettings(ctx context.Context, teamID uuid.UUID) (*models.TeamSettings, error)
	UpdateSettings(ctx context.Context, settings *models.TeamSettings) error
	LogActivity(ctx context.Context, activity *models.TeamActivity) error
	GetUserContext(ctx context.Context, userID string) (*models.UserContext, error)
	SetUserContext(ctx context.Context, context *models.UserContext) error
	IsUserMember(ctx context.Context, teamID uuid.UUID, userID string) (bool, error)
	GetUserTeamMemberships(ctx context.Context, userID string) ([]models.TeamMember, error)
	GetTeamsByCategory(ctx context.Context, category string, limit, offset int) ([]models.Team, error)
	GetTeamsByCategoryWithCount(ctx context.Context, category string, limit, offset int) ([]models.Team, int64, error)
}

type TeamService struct {
	teamRepo       TeamRepositoryInterface
	config         *config.Config
	userClient     clients.UserServiceClient
	cacheManager   *cache.DirectCacheManager
	asyncJobQueue  *async.AsyncJobQueue
	webhookService webhook.WebhookService
	batchPopulator *utils.BatchPopulator
	permChecker    *authorization.PermissionChecker
}

func NewTeamService(teamRepo TeamRepositoryInterface, config *config.Config, redisClient *redis.Client, cacheManager *cache.DirectCacheManager, asyncJobQueue *async.AsyncJobQueue, webhookService webhook.WebhookService) *TeamService {
	userClient := clients.NewUserServiceClient(config.Services.User, redisClient)
	teamClient := clients.NewTeamServiceClient(config.Services.Team, redisClient)

	return &TeamService{
		teamRepo:       teamRepo,
		config:         config,
		userClient:     userClient,
		cacheManager:   cacheManager,
		asyncJobQueue:  asyncJobQueue,
		webhookService: webhookService,
		batchPopulator: utils.NewBatchPopulator(userClient, teamClient),
		permChecker:    authorization.NewPermissionChecker(),
	}
}

// Helper function to parse and validate user ID
func (s *TeamService) parseUserID(userID string) (uuid.UUID, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return uuid.Nil, fmt.Errorf("invalid user ID format: %w", err)
	}
	return userUUID, nil
}

// Team operations
func (s *TeamService) CreateTeam(ctx context.Context, userID string, req *CreateTeamRequest) (*models.Team, error) {
	// Sanitize input data
	req.Name, req.Description, req.Website, req.Location = validation.SanitizeTeamInput(
		req.Name, req.Description, req.Website, req.Location)

	// Validate team data
	if validationErrors := validation.ValidateTeamCreation(
		req.Name, req.Description, req.Website, req.Location, req.Category); len(validationErrors) > 0 {
		return nil, validationErrors
	}

	// Validate enums
	if req.Visibility != "" && !validation.ValidateTeamVisibility(req.Visibility) {
		return nil, errors.New("invalid team visibility")
	}
	if req.JoinPolicy != "" && !validation.ValidateJoinPolicy(req.JoinPolicy) {
		return nil, errors.New("invalid join policy")
	}

	// Generate slug if not provided
	slug := req.Slug
	if slug == "" {
		slug = s.generateSlug(req.Name)
	}

	// Validate slug format
	if !validation.ValidateSlug(slug) {
		return nil, errors.New("invalid slug format")
	}

	// Skip pre-check to avoid race condition - rely on database constraint for enforcement

	// Parse userID to UUID
	userUUID, err := s.parseUserID(userID)
	if err != nil {
		return nil, err
	}

	team := &models.Team{
		Name:        req.Name,
		Slug:        slug,
		Description: req.Description,
		Category:    req.Category,
		Avatar:      req.Avatar,
		Cover:       req.Cover,
		Website:     req.Website,
		Location:    req.Location,
		Visibility:  req.Visibility,
		JoinPolicy:  req.JoinPolicy,
		MaxMembers:  req.MaxMembers,
		OwnerID:     userUUID,
		MemberCount: 1, // Owner is the first member
		IsActive:    true,
		IsVerified:  false,
	}

	if err := s.teamRepo.CreateTeam(ctx, team); err != nil {
		// Check if error is due to slug uniqueness constraint
		if strings.Contains(err.Error(), "slug") && (strings.Contains(err.Error(), "unique") || strings.Contains(err.Error(), "duplicate")) {
			return nil, errors.New("team slug is already taken")
		}
		return nil, fmt.Errorf("failed to create team: %w", err)
	}

	// Log activity
	s.logActivity(ctx, team.ID, userID, "team_created", "team", team.ID.String(), nil)

	return team, nil
}

func (s *TeamService) GetTeam(ctx context.Context, teamID uuid.UUID, userID string) (*models.Team, error) {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return nil, errors.New("team not found")
	}

	// Check if user can view the team
	userUUID, err := s.parseUserID(userID)
	if err != nil {
		return nil, err
	}

	// For public teams, allow access immediately
	if team.Visibility == models.TeamVisibilityPublic {
		return team, nil
	}

	// For private/restricted teams, check membership
	member, err := s.teamRepo.GetMember(ctx, teamID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check membership: %w", err)
	}

	if !s.permChecker.CanUserViewTeam(team, userUUID, member) {
		return nil, errors.New("access denied")
	}

	return team, nil
}

func (s *TeamService) GetTeamBySlug(ctx context.Context, slug string, userID string) (*models.Team, error) {
	team, err := s.teamRepo.GetTeamBySlug(ctx, slug)
	if err != nil {
		return nil, fmt.Errorf("failed to get team by slug: %w", err)
	}
	if team == nil {
		return nil, errors.New("team not found")
	}

	// Check if user can view the team
	userUUID, err := s.parseUserID(userID)
	if err != nil {
		return nil, err
	}

	// For public teams, allow access immediately
	if team.Visibility == models.TeamVisibilityPublic {
		return team, nil
	}

	// For private/restricted teams, check membership
	member, err := s.teamRepo.GetMember(ctx, team.ID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check membership: %w", err)
	}

	if !s.permChecker.CanUserViewTeam(team, userUUID, member) {
		return nil, errors.New("access denied")
	}

	return team, nil
}

func (s *TeamService) GetUserTeams(ctx context.Context, userID string, limit, offset int) ([]models.Team, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	teams, err := s.teamRepo.GetTeamsByMember(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get user teams: %w", err)
	}

	// TODO: Set user roles for each team when GetUserRole method is implemented
	// For now, we'll skip this functionality to make the service build
	// for i := range teams {
	//	if role := teams[i].GetUserRole(userID); role != nil {
	//		teams[i].UserRole = role
	//	}
	// }

	return teams, nil
}

// GetUserTeamsWithCount returns user teams with total count for pagination
func (s *TeamService) GetUserTeamsWithCount(ctx context.Context, userID string, limit, offset int) ([]models.Team, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	teams, total, err := s.teamRepo.GetTeamsByMemberWithCount(ctx, userID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get user teams: %w", err)
	}

	return teams, total, nil
}

func (s *TeamService) SearchTeams(ctx context.Context, query string, visibility models.TeamVisibility, limit, offset int) ([]models.Team, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	teams, err := s.teamRepo.SearchTeams(ctx, query, visibility, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to search teams: %w", err)
	}

	return teams, nil
}

// SearchTeamsWithCount searches teams and returns total count for pagination
func (s *TeamService) SearchTeamsWithCount(ctx context.Context, query string, visibility models.TeamVisibility, limit, offset int) ([]models.Team, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	teams, total, err := s.teamRepo.SearchTeamsWithCount(ctx, query, visibility, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search teams: %w", err)
	}

	return teams, total, nil
}

func (s *TeamService) UpdateTeam(ctx context.Context, teamID uuid.UUID, userID string, req *UpdateTeamRequest) (*models.Team, error) {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return nil, errors.New("team not found")
	}

	// Check permissions - allow both owners and admins
	_, err = uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Get user's membership to check role
	member, err := s.teamRepo.GetMember(ctx, teamID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get member info: %w", err)
	}

	// Check if user can modify team
	if !s.permChecker.CanUserPerformAction(team, member, authorization.PermissionWriteTeam) {
		return nil, errors.New("access denied")
	}

	// Update fields
	if req.Name != "" {
		if err := s.validateTeamName(req.Name); err != nil {
			return nil, err
		}
		team.Name = req.Name
	}

	if req.Slug != "" {
		if err := s.validateTeamSlug(req.Slug); err != nil {
			return nil, err
		}

		// Check if new slug is available
		if taken, err := s.teamRepo.IsSlugTaken(ctx, req.Slug, &teamID); err != nil {
			return nil, fmt.Errorf("failed to check slug availability: %w", err)
		} else if taken {
			return nil, errors.New("team slug is already taken")
		}

		team.Slug = req.Slug
	}

	if req.Description != nil {
		team.Description = *req.Description
	}

	if req.Avatar != nil {
		team.Avatar = *req.Avatar
	}

	if req.Cover != nil {
		team.Cover = *req.Cover
	}

	if req.Website != nil {
		team.Website = *req.Website
	}

	if req.Location != nil {
		team.Location = *req.Location
	}

	if req.Category != nil {
		team.Category = *req.Category
	}

	if req.Visibility != "" {
		team.Visibility = req.Visibility
	}

	if req.JoinPolicy != "" {
		team.JoinPolicy = req.JoinPolicy
	}

	if req.MaxMembers > 0 {
		team.MaxMembers = req.MaxMembers
	}

	if err := s.teamRepo.UpdateTeam(ctx, team); err != nil {
		return nil, fmt.Errorf("failed to update team: %w", err)
	}

	// Direct cache invalidation (immediate) - retry once on failure
	if err := s.cacheManager.InvalidateTeam(ctx, teamID.String()); err != nil {
		// Log the first failure and attempt one retry
		fmt.Printf("Cache invalidation failed for team %s, retrying: %v\n", teamID, err)
		
		// Single retry with shorter timeout
		retryCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
		defer cancel()
		
		if retryErr := s.cacheManager.InvalidateTeam(retryCtx, teamID.String()); retryErr != nil {
			// Log retry failure but don't fail the entire operation
			fmt.Printf("Cache invalidation retry failed for team %s: %v\n", teamID, retryErr)
		}
	}

	// Async notification for team updates
	go s.sendTeamUpdateNotifications(team, []string{"team_data"})

	// Log activity
	s.logActivity(ctx, teamID, userID, "team_updated", "team", teamID.String(), nil)

	return team, nil
}

func (s *TeamService) DeleteTeam(ctx context.Context, teamID uuid.UUID, userID string) error {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return errors.New("team not found")
	}

	// Parse userID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	// Only owner can delete team
	if team.OwnerID != userUUID {
		return errors.New("access denied")
	}

	if err := s.teamRepo.DeleteTeam(ctx, teamID); err != nil {
		return fmt.Errorf("failed to delete team: %w", err)
	}

	// Log activity
	s.logActivity(ctx, teamID, userID, "team_deleted", "team", teamID.String(), nil)

	return nil
}

// Member operations
func (s *TeamService) InviteMember(ctx context.Context, teamID uuid.UUID, userID string, req *InviteMemberRequest) (*models.TeamInvitation, error) {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return nil, errors.New("team not found")
	}

	// Check permissions using RBAC
	member, err := s.teamRepo.GetMember(ctx, teamID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get member info: %w", err)
	}

	if !s.permChecker.CanUserPerformAction(team, member, authorization.PermissionWriteInvitations) {
		return nil, errors.New("access denied")
	}

	// Check if team is at capacity
	if team.MemberCount >= team.MaxMembers {
		return nil, errors.New("team is at maximum capacity")
	}

	// Check if user is already a member
	if req.UserID != "" {
		if existingMember, err := s.teamRepo.GetMember(ctx, teamID, req.UserID); err == nil && existingMember != nil {
			return nil, errors.New("user is already a member")
		}
	}

	// Generate invitation token
	token, err := s.generateInvitationToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate invitation token: %w", err)
	}

	// Parse inviter userID to UUID
	inviterUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid inviter user ID")
	}

	// Validate that the inviter exists in the user service
	if _, err := s.userClient.GetUser(ctx, userID); err != nil {
		return nil, fmt.Errorf("invalid inviter: user not found")
	}

	var inviteeUUID *uuid.UUID
	if req.UserID != "" {
		parsedInviteeID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, errors.New("invalid invitee user ID")
		}
		
		// Validate that the invitee exists in the user service
		if _, err := s.userClient.GetUser(ctx, req.UserID); err != nil {
			return nil, fmt.Errorf("invalid invitee: user not found")
		}
		
		inviteeUUID = &parsedInviteeID
	}

	invitation := &models.TeamInvitation{
		TeamID:    teamID,
		InviterID: inviterUUID,
		InviteeID: inviteeUUID,
		Email:     req.Email,
		Role:      req.Role,
		Status:    models.InvitationStatusPending,
		Token:     token,
		Message:   req.Message,
		ExpiresAt: time.Now().Add(7 * 24 * time.Hour), // 7 days
	}

	if err := s.teamRepo.CreateInvitation(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to create invitation: %w", err)
	}

	// Log activity
	s.logActivity(ctx, teamID, userID, "member_invited", "user", req.UserID, map[string]interface{}{
		"role": req.Role,
	})

	// TODO: Send invitation email/notification

	return invitation, nil
}

func (s *TeamService) AcceptInvitation(ctx context.Context, token string, userID string) (*models.Team, error) {
	invitation, err := s.teamRepo.GetInvitationByToken(ctx, token)
	if err != nil {
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}
	if invitation == nil {
		return nil, errors.New("invitation not found")
	}

	// Validate invitation
	if !invitation.CanRespond() {
		return nil, errors.New("invitation is no longer valid")
	}

	// Convert userID string to UUID for comparison
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if invitation is for this user
	if invitation.InviteeID != nil {
		// User-specific invitation
		if *invitation.InviteeID != userUUID {
			return nil, errors.New("invitation is not for this user")
		}
	} else if invitation.Email != "" {
		// Email-based invitation - need to verify user owns the email
		// For now, we'll require that email invitations are first converted to user-specific invitations
		// This prevents hijacking while maintaining functionality
		return nil, errors.New("email invitations must be claimed through the email verification process first")
	} else {
		// Invalid invitation - should have either InviteeID or Email
		return nil, errors.New("invalid invitation: missing recipient information")
	}

	// Check if user is already a member
	if existingMember, err := s.teamRepo.GetMember(ctx, invitation.TeamID, userID); err == nil && existingMember != nil {
		return nil, errors.New("user is already a member")
	}

	// Check team capacity
	team := &invitation.Team
	if team.MemberCount >= team.MaxMembers {
		return nil, errors.New("team is at maximum capacity")
	}

	// Add user as member
	member := &models.TeamMember{
		TeamID:      invitation.TeamID,
		UserID:      userUUID,
		Role:        invitation.Role,
		Permissions: models.GetDefaultPermissionsForRole(invitation.Role),
		IsActive:    true,
		IsPrimary:   false, // Will be set based on user's first team
		JoinedAt:    time.Now(),
	}

	if err := s.teamRepo.AddMember(ctx, member); err != nil {
		return nil, fmt.Errorf("failed to add member: %w", err)
	}

	// Update invitation status
	invitation.Status = models.InvitationStatusAccepted
	respondedTime := time.Now()
	invitation.RespondedAt = &respondedTime
	if err := s.teamRepo.UpdateInvitation(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to update invitation: %w", err)
	}

	// Log activity
	s.logActivity(ctx, invitation.TeamID, userID, "invitation_accepted", "team", invitation.TeamID.String(), nil)

	return &invitation.Team, nil
}

func (s *TeamService) DeclineInvitation(ctx context.Context, token string, userID string) error {
	invitation, err := s.teamRepo.GetInvitationByToken(ctx, token)
	if err != nil {
		return fmt.Errorf("failed to get invitation: %w", err)
	}
	if invitation == nil {
		return errors.New("invitation not found")
	}

	// Validate invitation
	if !invitation.CanRespond() {
		return errors.New("invitation is no longer valid")
	}

	// Convert userID string to UUID for comparison
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Check if invitation is for this user
	if invitation.InviteeID != nil && *invitation.InviteeID != userUUID {
		return errors.New("invitation is not for this user")
	}

	// Update invitation status
	invitation.Status = models.TeamInvitationRejected
	respondedTime := time.Now()
	invitation.RespondedAt = &respondedTime
	if err := s.teamRepo.UpdateInvitation(ctx, invitation); err != nil {
		return fmt.Errorf("failed to update invitation: %w", err)
	}

	// Log activity
	s.logActivity(ctx, invitation.TeamID, userID, "invitation_declined", "team", invitation.TeamID.String(), nil)

	return nil
}

func (s *TeamService) GetMembers(ctx context.Context, teamID uuid.UUID, userID string, limit, offset int) ([]models.TeamMember, error) {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return nil, errors.New("team not found")
	}

	// Check permissions using proper RBAC
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// For public teams, allow access immediately
	if team.Visibility == models.TeamVisibilityPublic {
		// Continue to member listing
	} else {
		// For private/restricted teams, check membership
		member, err := s.teamRepo.GetMember(ctx, teamID, userID)
		if err != nil {
			return nil, fmt.Errorf("failed to check membership: %w", err)
		}

		if !s.permChecker.CanUserViewTeam(team, userUUID, member) {
			return nil, errors.New("access denied")
		}
	}

	if limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200
	}

	members, err := s.teamRepo.GetMembers(ctx, teamID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get members: %w", err)
	}

	// Populate user info for all members in batch
	s.populateMembersBatch(ctx, members)

	return members, nil
}

// GetMembersWithCount returns team members with total count for pagination
func (s *TeamService) GetMembersWithCount(ctx context.Context, teamID uuid.UUID, userID string, limit, offset int) ([]models.TeamMember, int64, error) {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return nil, 0, errors.New("team not found")
	}

	// Check permissions using proper RBAC
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, 0, errors.New("invalid user ID")
	}

	// For public teams, allow access immediately
	if team.Visibility == models.TeamVisibilityPublic {
		// Continue to member listing
	} else {
		// For private/restricted teams, check membership
		member, err := s.teamRepo.GetMember(ctx, teamID, userID)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to check membership: %w", err)
		}

		if !s.permChecker.CanUserViewTeam(team, userUUID, member) {
			return nil, 0, errors.New("access denied")
		}
	}

	if limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200
	}

	members, total, err := s.teamRepo.GetMembersWithCount(ctx, teamID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get members: %w", err)
	}

	// Populate user info for all members in batch
	s.populateMembersBatch(ctx, members)

	return members, total, nil
}

func (s *TeamService) UpdateMemberRole(ctx context.Context, teamID uuid.UUID, userID string, targetUserID string, newRole models.TeamRole) error {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return errors.New("team not found")
	}

	// Get current user's member record
	currentMember, err := s.teamRepo.GetMember(ctx, teamID, userID)
	if err != nil {
		return fmt.Errorf("failed to get current member: %w", err)
	}
	if currentMember == nil {
		return errors.New("access denied")
	}

	// Get target member
	targetMember, err := s.teamRepo.GetMember(ctx, teamID, targetUserID)
	if err != nil {
		return fmt.Errorf("failed to get target member: %w", err)
	}
	if targetMember == nil {
		return errors.New("member not found")
	}

	// Check permissions - only owners and admins can manage members
	if currentMember.Role != models.TeamRoleOwner && currentMember.Role != models.TeamRoleAdmin {
		return errors.New("access denied")
	}

	// Validate role change - only owners can manage all roles, admins can manage members
	if currentMember.Role == models.TeamRoleOwner {
		// Owners can manage all roles (no restrictions)
	} else if currentMember.Role == models.TeamRoleAdmin {
		// Admins can manage members and promote them to admin, but cannot manage other admins or owners
		if targetMember.Role == models.TeamRoleOwner {
			return errors.New("cannot manage owner role")
		}
		if targetMember.Role == models.TeamRoleAdmin && targetMember.UserID != currentMember.UserID {
			return errors.New("cannot manage other admin roles")
		}
		if newRole == models.TeamRoleOwner {
			return errors.New("cannot promote to owner role")
		}
	} else {
		return errors.New("insufficient permissions to manage roles")
	}

	// Cannot change owner role
	if targetMember.Role == models.TeamRoleOwner {
		return errors.New("cannot change owner role")
	}

	// Update member role
	targetMember.Role = newRole
	if err := s.teamRepo.UpdateMember(ctx, targetMember); err != nil {
		return fmt.Errorf("failed to update member role: %w", err)
	}

	// Log activity
	s.logActivity(ctx, teamID, userID, "member_role_updated", "user", targetUserID, map[string]interface{}{
		"new_role": newRole,
		"old_role": targetMember.Role,
	})

	return nil
}

func (s *TeamService) RemoveMember(ctx context.Context, teamID uuid.UUID, userID string, targetUserID string) error {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return errors.New("team not found")
	}

	// Get current user's member record
	currentMember, err := s.teamRepo.GetMember(ctx, teamID, userID)
	if err != nil {
		return fmt.Errorf("failed to get current member: %w", err)
	}
	if currentMember == nil {
		return errors.New("access denied")
	}

	// Get target member
	targetMember, err := s.teamRepo.GetMember(ctx, teamID, targetUserID)
	if err != nil {
		return fmt.Errorf("failed to get target member: %w", err)
	}
	if targetMember == nil {
		return errors.New("member not found")
	}

	// Check permissions
	if userID != targetUserID && currentMember.Role != models.TeamRoleOwner && currentMember.Role != models.TeamRoleAdmin {
		return errors.New("access denied")
	}

	// Cannot remove owner
	if targetMember.Role == models.TeamRoleOwner {
		return errors.New("cannot remove team owner")
	}

	// Remove member
	if err := s.teamRepo.RemoveMember(ctx, teamID, targetUserID); err != nil {
		return fmt.Errorf("failed to remove member: %w", err)
	}

	// Log activity
	action := "member_removed"
	if userID == targetUserID {
		action = "member_left"
	}
	s.logActivity(ctx, teamID, userID, action, "user", targetUserID, nil)

	return nil
}

// Settings operations
func (s *TeamService) GetSettings(ctx context.Context, teamID uuid.UUID, userID string) (*models.TeamSettings, error) {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return nil, errors.New("team not found")
	}

	// Check permissions using RBAC
	member, err := s.teamRepo.GetMember(ctx, teamID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get member info: %w", err)
	}

	if !s.permChecker.CanUserPerformAction(team, member, authorization.PermissionReadSettings) {
		return nil, errors.New("access denied")
	}

	settings, err := s.teamRepo.GetSettings(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get settings: %w", err)
	}

	return settings, nil
}

func (s *TeamService) UpdateSettings(ctx context.Context, teamID uuid.UUID, userID string, req *UpdateSettingsRequest) (*models.TeamSettings, error) {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return nil, errors.New("team not found")
	}

	// Check permissions using RBAC
	member, err := s.teamRepo.GetMember(ctx, teamID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get member info: %w", err)
	}

	if !s.permChecker.CanUserPerformAction(team, member, authorization.PermissionWriteSettings) {
		return nil, errors.New("access denied")
	}

	settings, err := s.teamRepo.GetSettings(ctx, teamID)
	if err != nil {
		return nil, fmt.Errorf("failed to get settings: %w", err)
	}

	// Update settings
	if req.AllowMemberInvites != nil {
		settings.AllowMemberInvites = *req.AllowMemberInvites
	}
	if req.RequireApproval != nil {
		settings.RequireApproval = *req.RequireApproval
	}
	if req.ShowMemberList != nil {
		settings.ShowMemberList = *req.ShowMemberList
	}
	if req.ShowMemberProfiles != nil {
		settings.ShowMemberProfiles = *req.ShowMemberProfiles
	}
	if req.AllowDirectMessages != nil {
		settings.AllowDirectMessages = *req.AllowDirectMessages
	}
	if req.AllowPublicChannels != nil {
		settings.AllowPublicChannels = *req.AllowPublicChannels
	}
	if req.AllowPrivateChannels != nil {
		settings.AllowPrivateChannels = *req.AllowPrivateChannels
	}
	if req.AllowFileSharing != nil {
		settings.AllowFileSharing = *req.AllowFileSharing
	}
	if req.AllowExternalLinks != nil {
		settings.AllowExternalLinks = *req.AllowExternalLinks
	}
	if req.ModerateContent != nil {
		settings.ModerateContent = *req.ModerateContent
	}
	if req.EnableCalendar != nil {
		settings.EnableCalendar = *req.EnableCalendar
	}
	if req.EnableDrive != nil {
		settings.EnableDrive = *req.EnableDrive
	}
	if req.EnableNotifications != nil {
		settings.EnableNotifications = *req.EnableNotifications
	}

	if err := s.teamRepo.UpdateSettings(ctx, settings); err != nil {
		return nil, fmt.Errorf("failed to update settings: %w", err)
	}

	// Log activity
	s.logActivity(ctx, teamID, userID, "team_settings_updated", "team", teamID.String(), nil)

	return settings, nil
}

// Helper methods
func (s *TeamService) validateTeamData(name, slug, description string) error {
	if err := s.validateTeamName(name); err != nil {
		return err
	}

	if slug != "" {
		if err := s.validateTeamSlug(slug); err != nil {
			return err
		}
	}

	if len(description) > 500 {
		return errors.New("description is too long (max 500 characters)")
	}

	return nil
}

func (s *TeamService) validateTeamName(name string) error {
	name = strings.TrimSpace(name)
	if name == "" {
		return errors.New("team name is required")
	}

	if len(name) < 2 {
		return errors.New("team name must be at least 2 characters")
	}

	if len(name) > 100 {
		return errors.New("team name is too long (max 100 characters)")
	}

	return nil
}

func (s *TeamService) validateTeamSlug(slug string) error {
	if slug == "" {
		return errors.New("team slug is required")
	}

	if len(slug) < 2 {
		return errors.New("team slug must be at least 2 characters")
	}

	if len(slug) > 100 {
		return errors.New("team slug is too long (max 100 characters)")
	}

	// Validate slug format
	slugRegex := regexp.MustCompile(`^[a-z0-9-]+$`)
	if !slugRegex.MatchString(slug) {
		return errors.New("team slug can only contain lowercase letters, numbers, and hyphens")
	}

	return nil
}

func (s *TeamService) generateSlug(name string) string {
	// Convert to lowercase and replace spaces with hyphens
	slug := strings.ToLower(name)
	slug = regexp.MustCompile(`[^a-z0-9]+`).ReplaceAllString(slug, "-")
	slug = strings.Trim(slug, "-")

	// Truncate if too long
	if len(slug) > 100 {
		slug = slug[:100]
	}

	return slug
}

func (s *TeamService) generateInvitationToken(ctx context.Context) (string, error) {
	// Generate unique token with collision detection
	maxAttempts := 5
	for attempt := 0; attempt < maxAttempts; attempt++ {
		// Use 256 bits (32 bytes) for cryptographically secure tokens
		bytes := make([]byte, 32)
		if _, err := rand.Read(bytes); err != nil {
			return "", fmt.Errorf("failed to generate secure token: %w", err)
		}
		
		token := hex.EncodeToString(bytes)
		
		// Check if token already exists
		existing, err := s.teamRepo.GetInvitationByToken(ctx, token)
		if err != nil {
			return "", fmt.Errorf("failed to check token uniqueness: %w", err)
		}
		
		if existing == nil {
			return token, nil
		}
		
		// Token collision detected, try again
	}
	
	return "", errors.New("failed to generate unique invitation token after multiple attempts")
}

// populateMembersBatch efficiently populates user data for multiple team members
func (s *TeamService) populateMembersBatch(ctx context.Context, members []models.TeamMember) {
	if len(members) == 0 {
		return
	}

	// Extract all user IDs
	var userIDs []string
	for _, member := range members {
		userIDs = append(userIDs, member.UserID.String())
	}

	// Fetch users in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, userIDs)
	if err != nil {
		fmt.Printf("Failed to batch populate users for team members: %v\n", err)
		return
	}

	// TODO: Populate members with user data when User field is available
	// For now, we'll skip this functionality to make the service build
	// The user data can be fetched separately when needed
	_ = userMap // Prevent unused variable error
}

func (s *TeamService) logActivity(ctx context.Context, teamID uuid.UUID, userID, action, targetType, targetID string, metadata map[string]interface{}) {
	// Convert userID string to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Invalid user ID format in logActivity: %v\n", err)
		return
	}

	// Convert metadata to JSON bytes, use empty object for nil metadata
	var metadataBytes []byte
	if metadata != nil && len(metadata) > 0 {
		if jsonBytes, err := json.Marshal(metadata); err == nil {
			metadataBytes = jsonBytes
		} else {
			fmt.Printf("Failed to marshal metadata in logActivity: %v\n", err)
			metadataBytes = []byte("{}")
		}
	} else {
		metadataBytes = []byte("{}") // Always use empty JSON object for JSONB compatibility
	}

	activity := &models.TeamActivity{
		TeamID:     teamID,
		UserID:     userUUID,
		Action:     action,
		TargetType: targetType,
		TargetID:   targetID,
		Metadata:   metadataBytes,
		IPAddress:  "", // TODO: Extract from context
		UserAgent:  "", // TODO: Extract from context
	}

	// Log activity asynchronously with error handling
	go func() {
		activityCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		if err := s.teamRepo.LogActivity(activityCtx, activity); err != nil {
			fmt.Printf("Failed to log team activity: %v\n", err)
		}
	}()
}

// Request/Response types
type CreateTeamRequest struct {
	Name        string                `json:"name" binding:"required"`
	Slug        string                `json:"slug,omitempty"`
	Description string                `json:"description,omitempty"`
	Category    string                `json:"category,omitempty"`
	Avatar      string                `json:"avatar,omitempty"`
	Cover       string                `json:"cover,omitempty"`
	Website     string                `json:"website,omitempty"`
	Location    string                `json:"location,omitempty"`
	Visibility  models.TeamVisibility `json:"visibility"`
	JoinPolicy  models.JoinPolicy     `json:"join_policy"`
	MaxMembers  int                   `json:"max_members"`
}

type UpdateTeamRequest struct {
	Name        string                `json:"name,omitempty"`
	Slug        string                `json:"slug,omitempty"`
	Description *string               `json:"description,omitempty"`
	Category    *string               `json:"category,omitempty"`
	Avatar      *string               `json:"avatar,omitempty"`
	Cover       *string               `json:"cover,omitempty"`
	Website     *string               `json:"website,omitempty"`
	Location    *string               `json:"location,omitempty"`
	Visibility  models.TeamVisibility `json:"visibility,omitempty"`
	JoinPolicy  models.JoinPolicy     `json:"join_policy,omitempty"`
	MaxMembers  int                   `json:"max_members,omitempty"`
}

type InviteMemberRequest struct {
	UserID  string          `json:"user_id,omitempty"`
	Email   string          `json:"email,omitempty"`
	Role    models.TeamRole `json:"role" binding:"required"`
	Message string          `json:"message,omitempty"`
}

type UpdateSettingsRequest struct {
	AllowMemberInvites   *bool `json:"allow_member_invites,omitempty"`
	RequireApproval      *bool `json:"require_approval,omitempty"`
	ShowMemberList       *bool `json:"show_member_list,omitempty"`
	ShowMemberProfiles   *bool `json:"show_member_profiles,omitempty"`
	AllowDirectMessages  *bool `json:"allow_direct_messages,omitempty"`
	AllowPublicChannels  *bool `json:"allow_public_channels,omitempty"`
	AllowPrivateChannels *bool `json:"allow_private_channels,omitempty"`
	AllowFileSharing     *bool `json:"allow_file_sharing,omitempty"`
	AllowExternalLinks   *bool `json:"allow_external_links,omitempty"`
	ModerateContent      *bool `json:"moderate_content,omitempty"`
	EnableCalendar       *bool `json:"enable_calendar,omitempty"`
	EnableDrive          *bool `json:"enable_drive,omitempty"`
	EnableNotifications  *bool `json:"enable_notifications,omitempty"`
}

// User Context Management
type SetUserContextRequest struct {
	ContextType string     `json:"context_type" binding:"required"`
	TeamID      *uuid.UUID `json:"team_id,omitempty"`
}

type UserContextResponse struct {
	ContextType string       `json:"context_type"`
	TeamID      *uuid.UUID   `json:"team_id,omitempty"`
	Team        *models.Team `json:"team,omitempty"`
}

type UserTeamsResponse struct {
	Memberships []models.TeamMember `json:"memberships"`
	PrimaryTeam *models.Team        `json:"primary_team,omitempty"`
}

// User Context Services
func (s *TeamService) GetUserContext(ctx context.Context, userID string) (*UserContextResponse, error) {
	userContext, err := s.teamRepo.GetUserContext(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user context: %w", err)
	}

	response := &UserContextResponse{
		ContextType: userContext.ContextType,
		TeamID:      userContext.TeamID,
	}

	// If user context is team-specific, populate team info
	if userContext.TeamID != nil {
		team, err := s.teamRepo.GetTeamByID(ctx, *userContext.TeamID)
		if err != nil {
			// If team not found, reset to global context
			response.ContextType = "global"
			response.TeamID = nil
		} else {
			response.Team = team
		}
	}

	return response, nil
}

func (s *TeamService) SetUserContext(ctx context.Context, userID string, req *SetUserContextRequest) error {
	// Validate context request
	if req.ContextType == "team" && req.TeamID == nil {
		return errors.New("team_id is required for team context")
	}

	// If setting team context, verify user is a member
	if req.TeamID != nil {
		isMember, err := s.teamRepo.IsUserMember(ctx, *req.TeamID, userID)
		if err != nil {
			return fmt.Errorf("failed to check team membership: %w", err)
		}
		if !isMember {
			return errors.New("user is not a member of the specified team")
		}
	}

	// Parse userID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Create or update user context
	userContext := &models.UserContext{
		UserID:      userUUID,
		ContextType: req.ContextType,
		TeamID:      req.TeamID,
	}

	if err := s.teamRepo.SetUserContext(ctx, userContext); err != nil {
		return fmt.Errorf("failed to set user context: %w", err)
	}

	return nil
}

func (s *TeamService) GetUserTeamMemberships(ctx context.Context, userID string) (*UserTeamsResponse, error) {
	memberships, err := s.teamRepo.GetUserTeamMemberships(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user team memberships: %w", err)
	}

	response := &UserTeamsResponse{
		Memberships: memberships,
	}

	// Find primary team
	for _, membership := range memberships {
		if membership.IsPrimary {
			team, err := s.teamRepo.GetTeamByID(ctx, membership.TeamID)
			if err != nil {
				continue // Log this error in production
			}
			response.PrimaryTeam = team
			break
		}
	}

	return response, nil
}

// Team Categories Services
func (s *TeamService) GetTeamCategories(ctx context.Context) ([]string, error) {
	// For now, return default categories
	// In future, this could be configurable or stored in database
	return models.DefaultTeamCategories, nil
}

func (s *TeamService) GetTeamsByCategory(ctx context.Context, category string, userID string, limit, offset int) ([]models.Team, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	teams, err := s.teamRepo.GetTeamsByCategory(ctx, category, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get teams by category: %w", err)
	}

	// Filter teams based on visibility and user access
	var filteredTeams []models.Team
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Apply same filtering logic as GetTeamsByCategoryWithCount
	for _, team := range teams {
		// Public teams are always visible
		if team.Visibility == models.TeamVisibilityPublic {
			filteredTeams = append(filteredTeams, team)
			continue
		}
		
		// For private/restricted teams, check membership
		member, err := s.teamRepo.GetMember(ctx, team.ID, userID)
		if err != nil {
			// Skip this team if we can't check membership
			continue
		}
		
		if s.permChecker.CanUserViewTeam(&team, userUUID, member) {
			filteredTeams = append(filteredTeams, team)
		}
	}

	return filteredTeams, nil
}

// GetTeamsByCategoryWithCount returns teams by category with total count for pagination
func (s *TeamService) GetTeamsByCategoryWithCount(ctx context.Context, category string, userID string, limit, offset int) ([]models.Team, int64, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	teams, total, err := s.teamRepo.GetTeamsByCategoryWithCount(ctx, category, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get teams by category: %w", err)
	}

	// Filter teams based on visibility and user access
	var filteredTeams []models.Team
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, 0, fmt.Errorf("invalid user ID: %w", err)
	}
	
	for _, team := range teams {
		// Public teams are always visible
		if team.Visibility == models.TeamVisibilityPublic {
			filteredTeams = append(filteredTeams, team)
			continue
		}
		
		// For private/restricted teams, check membership
		member, err := s.teamRepo.GetMember(ctx, team.ID, userID)
		if err != nil {
			// Skip this team if we can't check membership
			continue
		}
		
		if s.permChecker.CanUserViewTeam(&team, userUUID, member) {
			filteredTeams = append(filteredTeams, team)
		}
	}

	return filteredTeams, total, nil
}

// Permission Management Services
func (s *TeamService) UpdateMemberPermissions(ctx context.Context, teamID uuid.UUID, targetUserID string, permissions []string, managerUserID string) error {
	team, err := s.teamRepo.GetTeamByID(ctx, teamID)
	if err != nil {
		return fmt.Errorf("failed to get team: %w", err)
	}
	if team == nil {
		return errors.New("team not found")
	}

	// Check if manager has permission to manage members
	managerMember, err := s.teamRepo.GetMember(ctx, teamID, managerUserID)
	if err != nil {
		return fmt.Errorf("failed to get manager member info: %w", err)
	}

	if !s.permChecker.CanUserPerformAction(team, managerMember, authorization.PermissionWriteMembers) {
		return errors.New("access denied: insufficient permissions")
	}

	// Get target member
	member, err := s.teamRepo.GetMember(ctx, teamID, targetUserID)
	if err != nil {
		return fmt.Errorf("failed to get member: %w", err)
	}
	if member == nil {
		return errors.New("member not found")
	}

	// Update permissions
	member.Permissions = permissions
	if err := s.teamRepo.UpdateMember(ctx, member); err != nil {
		return fmt.Errorf("failed to update member permissions: %w", err)
	}

	// Log activity
	s.logActivity(ctx, teamID, managerUserID, "member_permissions_updated", "user", targetUserID, map[string]interface{}{
		"permissions": permissions,
	})

	return nil
}

// Team notification methods (replace Kafka events)
func (s *TeamService) sendTeamUpdateNotifications(team *models.Team, changedFields []string) {
	// Use background context with timeout for notifications
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	// Enqueue async job for notifications
	job := &async.AsyncJob{
		Type:   "team_updated",
		UserID: "", // Team-level event
		Data: map[string]interface{}{
			"team_id":        team.ID.String(),
			"team_name":      team.Name,
			"team_slug":      team.Slug,
			"changed_fields": changedFields,
			"timestamp":      time.Now().Format(time.RFC3339),
		},
	}

	if err := s.asyncJobQueue.EnqueueJob(ctx, job); err != nil {
		fmt.Printf("Failed to enqueue team update notification: %v\n", err)
	}

	// Send webhook for external integrations
	webhookReq := &webhook.WebhookRequest{
		Event:  "team_updated",
		UserID: team.ID.String(),
		Data: map[string]interface{}{
			"team_id":        team.ID.String(),
			"team_name":      team.Name,
			"changed_fields": changedFields,
			"timestamp":      time.Now().Format(time.RFC3339),
		},
	}

	if err := s.webhookService.SendWebhook(webhookReq); err != nil {
		fmt.Printf("Failed to send team update webhook: %v\n", err)
	}
}

func (s *TeamService) Close() error {
	if s.webhookService != nil {
		return s.webhookService.Close()
	}
	return nil
}

// Legacy close method compatibility
func (s *TeamService) LegacyClose() error {
	// Event publisher removed - no cleanup needed
	return nil
}
