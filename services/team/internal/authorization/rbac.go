package authorization

import (
	"github.com/google/uuid"
	"github.com/swork-team/platform/services/team/internal/models"
)

// Permission represents a specific permission
type Permission string

const (
	// Team permissions
	PermissionReadTeam   Permission = "team.read"
	PermissionWriteTeam  Permission = "team.write"
	PermissionDeleteTeam Permission = "team.delete"

	// Member permissions
	PermissionReadMembers   Permission = "members.read"
	PermissionWriteMembers  Permission = "members.write"
	PermissionRemoveMembers Permission = "members.remove"

	// Settings permissions
	PermissionReadSettings  Permission = "settings.read"
	PermissionWriteSettings Permission = "settings.write"

	// Invitation permissions
	PermissionReadInvitations   Permission = "invitations.read"
	PermissionWriteInvitations  Permission = "invitations.write"
	PermissionDeleteInvitations Permission = "invitations.delete"

	// Content permissions
	PermissionReadContent     Permission = "content.read"
	PermissionWriteContent    Permission = "content.write"
	PermissionDeleteContent   Permission = "content.delete"
	PermissionModerateContent Permission = "content.moderate"
)

// Permission<PERSON>hecker provides methods to check user permissions
type Permission<PERSON>he<PERSON> struct{}

// NewPermissionChecker creates a new permission checker
func NewPermissionChecker() *PermissionChecker {
	return &PermissionChecker{}
}

// CanUserPerformAction checks if a user can perform a specific action on a team
func (pc *PermissionChecker) CanUserPerformAction(
	team *models.Team,
	member *models.TeamMember,
	permission Permission,
) bool {
	if team == nil {
		return false
	}

	// Team owner has all permissions
	if member != nil && team.OwnerID == member.UserID {
		return true
	}

	// If no member record, user is not a team member
	if member == nil {
		// Only allow read access to public teams for non-members
		if team.Visibility == models.TeamVisibilityPublic && permission == PermissionReadTeam {
			return true
		}
		return false
	}

	// Check if member is active
	if !member.IsActive {
		return false
	}

	// Check role-based permissions
	return pc.hasRolePermission(member.Role, permission)
}

// hasRolePermission checks if a role has a specific permission
func (pc *PermissionChecker) hasRolePermission(role models.TeamRole, permission Permission) bool {
	switch role {
	case models.TeamRoleOwner:
		return true // Owners have all permissions

	case models.TeamRoleAdmin:
		// Admins can do everything except delete team and manage owner
		adminPermissions := map[Permission]bool{
			PermissionReadTeam:          true,
			PermissionWriteTeam:         true,
			PermissionReadMembers:       true,
			PermissionWriteMembers:      true,
			PermissionRemoveMembers:     true,
			PermissionReadSettings:      true,
			PermissionWriteSettings:     true,
			PermissionReadInvitations:   true,
			PermissionWriteInvitations:  true,
			PermissionDeleteInvitations: true,
			PermissionReadContent:       true,
			PermissionWriteContent:      true,
			PermissionDeleteContent:     true,
			PermissionModerateContent:   true,
		}
		return adminPermissions[permission]

	case models.TeamRoleMember:
		// Members have limited permissions
		memberPermissions := map[Permission]bool{
			PermissionReadTeam:      true,
			PermissionReadMembers:   true,
			PermissionReadContent:   true,
			PermissionWriteContent:  true,
		}
		return memberPermissions[permission]

	default:
		return false
	}
}

// CanUserModifyMember checks if a user can modify another member's role/permissions
func (pc *PermissionChecker) CanUserModifyMember(
	team *models.Team,
	actor *models.TeamMember,
	target *models.TeamMember,
) bool {
	if team == nil || actor == nil || target == nil {
		return false
	}

	// Cannot modify yourself
	if actor.UserID == target.UserID {
		return false
	}

	// Owner can modify anyone except other owners
	if actor.Role == models.TeamRoleOwner {
		return target.Role != models.TeamRoleOwner
	}

	// Admin can modify members only
	if actor.Role == models.TeamRoleAdmin {
		return target.Role == models.TeamRoleMember
	}

	// Members cannot modify anyone
	return false
}

// CanUserRemoveMember checks if a user can remove another member
func (pc *PermissionChecker) CanUserRemoveMember(
	team *models.Team,
	actor *models.TeamMember,
	target *models.TeamMember,
) bool {
	if team == nil || actor == nil || target == nil {
		return false
	}

	// Users can remove themselves (leave team)
	if actor.UserID == target.UserID {
		return target.Role != models.TeamRoleOwner // Owner cannot leave
	}

	// Owner can remove anyone except other owners
	if actor.Role == models.TeamRoleOwner {
		return target.Role != models.TeamRoleOwner
	}

	// Admin can remove members only
	if actor.Role == models.TeamRoleAdmin {
		return target.Role == models.TeamRoleMember
	}

	// Members cannot remove anyone else
	return false
}

// CanUserViewTeam checks if a user can view a team
func (pc *PermissionChecker) CanUserViewTeam(
	team *models.Team,
	userID uuid.UUID,
	member *models.TeamMember,
) bool {
	if team == nil {
		return false
	}

	// Public teams can be viewed by anyone
	if team.Visibility == models.TeamVisibilityPublic {
		return true
	}

	// Private teams can only be viewed by members
	if team.Visibility == models.TeamVisibilityPrivate {
		return member != nil && member.IsActive
	}

	// Restricted teams can be viewed by members
	if team.Visibility == models.TeamVisibilityRestricted {
		return member != nil && member.IsActive
	}

	return false
}

// IsTeamOwner checks if a user is the team owner
func (pc *PermissionChecker) IsTeamOwner(team *models.Team, userID uuid.UUID) bool {
	return team != nil && team.OwnerID == userID
}

// IsTeamAdmin checks if a user is a team admin (including owner)
func (pc *PermissionChecker) IsTeamAdmin(team *models.Team, member *models.TeamMember) bool {
	if team == nil || member == nil || !member.IsActive {
		return false
	}
	return member.Role == models.TeamRoleOwner || member.Role == models.TeamRoleAdmin
}