package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/team/internal/models"
	"github.com/swork-team/platform/services/team/internal/services"
	paginationMiddleware "github.com/swork-team/platform/services/team/internal/middleware"
)

type TeamHandler struct {
	teamService *services.TeamService
}

func NewTeamHandler(teamService *services.TeamService) *TeamHandler {
	return &TeamHandler{
		teamService: teamService,
	}
}

// Team management handlers

// @Summary Create a new team
// @Description Create a new team with the authenticated user as owner
// @Tags teams
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.CreateTeamRequest true "Team data"
// @Success 201 {object} utils.APIResponse{data=models.Team}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /teams [post]
func (h *TeamHandler) CreateTeam(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return // Error already handled by MustGetAuthenticatedUser
	}

	var req services.CreateTeamRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by BindJSONRequest
	}

	team, err := h.teamService.CreateTeam(c.Request.Context(), userID, &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, err.Error(), err,
			"create_team", 
			logger.F("user_id", userID),
			logger.F("team_name", req.Name),
			logger.F("team_category", req.Category))
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusCreated, "Team created successfully", team,
		"create_team",
		logger.F("user_id", userID),
		logger.F("team_id", team.ID.String()),
		logger.F("team_name", team.Name),
		logger.F("team_slug", team.Slug),
		logger.F("team_category", team.Category))
}

// @Summary Get team by ID
// @Description Get team details by ID
// @Tags teams
// @Security BearerAuth
// @Param id path string true "Team ID"
// @Success 200 {object} utils.APIResponse{data=models.Team}
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /teams/{id} [get]
func (h *TeamHandler) GetTeam(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	team, err := h.teamService.GetTeam(c.Request.Context(), teamID, userID)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get team", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Team retrieved successfully", team)
}

// @Summary Get team by slug
// @Description Get team details by slug
// @Tags teams
// @Security BearerAuth
// @Param slug path string true "Team slug"
// @Success 200 {object} utils.APIResponse{data=models.Team}
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /teams/slug/{slug} [get]
func (h *TeamHandler) GetTeamBySlug(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	slug := c.Param("slug")
	if slug == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Team slug is required", nil)
		return
	}

	team, err := h.teamService.GetTeamBySlug(c.Request.Context(), slug, userIDStr)
	if err != nil {
		if middleware.HandleStandardError(c, err, "Team") {
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get team", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Team retrieved successfully", team)
}

// @Summary Get user's teams
// @Description Get list of teams for the authenticated user
// @Tags teams
// @Security BearerAuth
// @Param limit query int false "Number of teams per page" default(20)
// @Param offset query int false "Number of teams to skip" default(0)
// @Success 200 {object} utils.APIResponse{data=[]models.Team}
// @Failure 401 {object} utils.APIResponse
// @Router /teams/me [get]
func (h *TeamHandler) GetUserTeams(c *gin.Context) {
	userID := middleware.MustGetAuthenticatedUser(c)
	if userID == "" {
		return
	}

	page, limit, offset := paginationMiddleware.GetPaginationParams(c)

	teams, total, err := h.teamService.GetUserTeamsWithCount(c.Request.Context(), userID, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get teams", err)
		return
	}

	response := paginationMiddleware.CreatePaginatedResponse(teams, page, limit, total)
	utils.SuccessResponse(c, http.StatusOK, "Teams retrieved successfully", response)
}

// @Summary Search teams
// @Description Search for teams by name or description
// @Tags teams
// @Security BearerAuth
// @Param q query string false "Search query"
// @Param visibility query string false "Team visibility" Enums(public, private)
// @Param limit query int false "Number of teams per page" default(20)
// @Param offset query int false "Number of teams to skip" default(0)
// @Success 200 {object} utils.APIResponse{data=[]models.Team}
// @Failure 401 {object} utils.APIResponse
// @Router /teams/search [get]
func (h *TeamHandler) SearchTeams(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	query := c.Query("q")
	visibility := models.TeamVisibility(c.Query("visibility"))
	page, limit, offset := paginationMiddleware.GetPaginationParams(c)

	teams, total, err := h.teamService.SearchTeamsWithCount(c.Request.Context(), query, visibility, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to search teams", err)
		return
	}

	response := paginationMiddleware.CreatePaginatedResponse(teams, page, limit, total)
	utils.SuccessResponse(c, http.StatusOK, "Search completed successfully", response)
}

// @Summary Update team
// @Description Update team information
// @Tags teams
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Team ID"
// @Param request body services.UpdateTeamRequest true "Updated team data"
// @Success 200 {object} utils.APIResponse{data=models.Team}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Router /teams/{id} [put]
func (h *TeamHandler) UpdateTeam(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	var req services.UpdateTeamRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	team, err := h.teamService.UpdateTeam(c.Request.Context(), teamID, userIDStr, &req)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Team updated successfully", team)
}

// @Summary Delete team
// @Description Delete a team (owner only)
// @Tags teams
// @Security BearerAuth
// @Param id path string true "Team ID"
// @Success 200 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /teams/{id} [delete]
func (h *TeamHandler) DeleteTeam(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	err = h.teamService.DeleteTeam(c.Request.Context(), teamID, userIDStr)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete team", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Team deleted successfully", nil)
}

// Member management handlers

// @Summary Invite member to team
// @Description Send an invitation to join the team
// @Tags members
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Team ID"
// @Param request body services.InviteMemberRequest true "Invitation data"
// @Success 201 {object} utils.APIResponse{data=models.TeamInvitation}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Router /teams/{id}/invite [post]
func (h *TeamHandler) InviteMember(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	var req services.InviteMemberRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	invitation, err := h.teamService.InviteMember(c.Request.Context(), teamID, userIDStr, &req)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Invitation sent successfully", invitation)
}

// @Summary Accept team invitation
// @Description Accept a team invitation using the invitation token
// @Tags invitations
// @Security BearerAuth
// @Param token path string true "Invitation token"
// @Success 200 {object} utils.APIResponse{data=models.Team}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /invitations/{token}/accept [post]
func (h *TeamHandler) AcceptInvitation(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	token := c.Param("token")
	if token == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invitation token is required", nil)
		return
	}

	team, err := h.teamService.AcceptInvitation(c.Request.Context(), token, userIDStr)
	if err != nil {
		if err.Error() == "invitation not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invitation not found", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Invitation accepted successfully", team)
}

// @Summary Decline team invitation
// @Description Decline a team invitation using the invitation token
// @Tags invitations
// @Security BearerAuth
// @Param token path string true "Invitation token"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /invitations/{token}/decline [post]
func (h *TeamHandler) DeclineInvitation(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	token := c.Param("token")
	if token == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invitation token is required", nil)
		return
	}

	err := h.teamService.DeclineInvitation(c.Request.Context(), token, userIDStr)
	if err != nil {
		if err.Error() == "invitation not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invitation not found", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Invitation declined successfully", nil)
}

// @Summary Get team members
// @Description Get list of team members
// @Tags members
// @Security BearerAuth
// @Param id path string true "Team ID"
// @Param limit query int false "Number of members per page" default(50)
// @Param offset query int false "Number of members to skip" default(0)
// @Success 200 {object} utils.APIResponse{data=[]models.TeamMember}
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /teams/{id}/members [get]
func (h *TeamHandler) GetMembers(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	page, limit, offset := paginationMiddleware.GetPaginationParams(c)
	if limit == 0 {
		limit = 50
	}

	members, total, err := h.teamService.GetMembersWithCount(c.Request.Context(), teamID, userIDStr, limit, offset)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get members", err)
		return
	}

	response := paginationMiddleware.CreatePaginatedResponse(members, page, limit, total)
	utils.SuccessResponse(c, http.StatusOK, "Members retrieved successfully", response)
}

// @Summary Update member role
// @Description Update a team member's role
// @Tags members
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Team ID"
// @Param user_id path string true "User ID"
// @Param request body map[string]string true "Role data"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Router /teams/{id}/members/{user_id}/role [put]
func (h *TeamHandler) UpdateMemberRole(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	targetUserID := c.Param("user_id")
	if targetUserID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "User ID is required", nil)
		return
	}

	var req struct {
		Role models.TeamRole `json:"role" binding:"required"`
	}
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	err = h.teamService.UpdateMemberRole(c.Request.Context(), teamID, userIDStr, targetUserID, req.Role)
	if err != nil {
		if err.Error() == "team not found" || err.Error() == "member not found" {
			utils.ErrorResponse(c, http.StatusNotFound, err.Error(), nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Member role updated successfully", nil)
}

// @Summary Remove team member
// @Description Remove a member from the team
// @Tags members
// @Security BearerAuth
// @Param id path string true "Team ID"
// @Param user_id path string true "User ID"
// @Success 200 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /teams/{id}/members/{user_id} [delete]
func (h *TeamHandler) RemoveMember(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	targetUserID := c.Param("user_id")
	if targetUserID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "User ID is required", nil)
		return
	}

	err = h.teamService.RemoveMember(c.Request.Context(), teamID, userIDStr, targetUserID)
	if err != nil {
		if err.Error() == "team not found" || err.Error() == "member not found" {
			utils.ErrorResponse(c, http.StatusNotFound, err.Error(), nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Member removed successfully", nil)
}

// Settings handlers

// @Summary Get team settings
// @Description Get team settings (admin only)
// @Tags settings
// @Security BearerAuth
// @Param id path string true "Team ID"
// @Success 200 {object} utils.APIResponse{data=models.TeamSettings}
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /teams/{id}/settings [get]
func (h *TeamHandler) GetSettings(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	settings, err := h.teamService.GetSettings(c.Request.Context(), teamID, userIDStr)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get settings", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Settings retrieved successfully", settings)
}

// @Summary Update team settings
// @Description Update team settings (admin only)
// @Tags settings
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Team ID"
// @Param request body services.UpdateSettingsRequest true "Settings data"
// @Success 200 {object} utils.APIResponse{data=models.TeamSettings}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Router /teams/{id}/settings [put]
func (h *TeamHandler) UpdateSettings(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	var req services.UpdateSettingsRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	settings, err := h.teamService.UpdateSettings(c.Request.Context(), teamID, userIDStr, &req)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Settings updated successfully", settings)
}

// User Context Management endpoints

// @Summary Get user context
// @Description Get the current user's context (global or team-specific)
// @Tags user
// @Security BearerAuth
// @Success 200 {object} utils.APIResponse{data=services.UserContextResponse}
// @Failure 401 {object} utils.APIResponse
// @Router /user/context [get]
func (h *TeamHandler) GetUserContext(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	context, err := h.teamService.GetUserContext(c.Request.Context(), userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user context", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "User context retrieved successfully", context)
}

// @Summary Set user context
// @Description Set the user's current context (switch to global or team context)
// @Tags user
// @Security BearerAuth
// @Accept json
// @Param request body services.SetUserContextRequest true "Context data"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /user/context [post]
func (h *TeamHandler) SetUserContext(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	var req services.SetUserContextRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	err := h.teamService.SetUserContext(c.Request.Context(), userIDStr, &req)
	if err != nil {
		if err.Error() == "user is not a member of the specified team" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "User context updated successfully", nil)
}

// @Summary Get user teams
// @Description Get all teams the user is a member of
// @Tags user
// @Security BearerAuth
// @Success 200 {object} utils.APIResponse{data=services.UserTeamsResponse}
// @Failure 401 {object} utils.APIResponse
// @Router /user/teams [get]
func (h *TeamHandler) GetUserTeamMemberships(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	userTeams, err := h.teamService.GetUserTeamMemberships(c.Request.Context(), userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get user teams", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "User teams retrieved successfully", userTeams)
}

// Team Categories endpoints

// @Summary Get team categories
// @Description Get list of available team categories
// @Tags teams
// @Success 200 {object} utils.APIResponse{data=[]string}
// @Router /teams/categories [get]
func (h *TeamHandler) GetTeamCategories(c *gin.Context) {
	categories, err := h.teamService.GetTeamCategories(c.Request.Context())
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get team categories", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Team categories retrieved successfully", categories)
}

// @Summary Get teams by category
// @Description Get teams filtered by category
// @Tags teams
// @Security BearerAuth
// @Param category query string true "Team category"
// @Param limit query int false "Number of teams per page" default(20)
// @Param offset query int false "Number of teams to skip" default(0)
// @Success 200 {object} utils.APIResponse{data=[]models.Team}
// @Failure 401 {object} utils.APIResponse
// @Router /teams [get]
func (h *TeamHandler) GetTeamsByCategory(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	category := c.Query("category")
	page, limit, offset := paginationMiddleware.GetPaginationParams(c)

	teams, total, err := h.teamService.GetTeamsByCategoryWithCount(c.Request.Context(), category, userIDStr, limit, offset)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get teams", err)
		return
	}

	response := paginationMiddleware.CreatePaginatedResponse(teams, page, limit, total)
	utils.SuccessResponse(c, http.StatusOK, "Teams retrieved successfully", response)
}

// Permission Management endpoints

// @Summary Update member permissions
// @Description Update specific permissions for a team member
// @Tags members
// @Security BearerAuth
// @Param id path string true "Team ID"
// @Param user_id path string true "User ID"
// @Accept json
// @Param request body map[string][]string true "Permissions data" example({"permissions":["VIEW","EDIT","INVITE"]})
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 403 {object} utils.APIResponse
// @Router /teams/{id}/members/{user_id}/permissions [put]
func (h *TeamHandler) UpdateMemberPermissions(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamIDStr := c.Param("id")
	teamID, err := uuid.Parse(teamIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid team ID", err)
		return
	}

	targetUserID := c.Param("user_id")
	if targetUserID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "User ID is required", nil)
		return
	}

	var req map[string][]string
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	permissions, ok := req["permissions"]
	if !ok {
		utils.ErrorResponse(c, http.StatusBadRequest, "Permissions array is required", nil)
		return
	}

	err = h.teamService.UpdateMemberPermissions(c.Request.Context(), teamID, targetUserID, permissions, userIDStr)
	if err != nil {
		if err.Error() == "team not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Team not found", nil)
			return
		}
		if err.Error() == "member not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Member not found", nil)
			return
		}
		if err.Error() == "access denied: insufficient permissions" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Member permissions updated successfully", nil)
}

// Health check endpoint
func (h *TeamHandler) HealthCheck(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Team service is healthy", map[string]string{
		"service": "team-service",
		"status":  "healthy",
	})
}
