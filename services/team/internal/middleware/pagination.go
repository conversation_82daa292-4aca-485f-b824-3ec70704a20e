package middleware

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// PaginationParams represents offset-based pagination parameters
type PaginationParams struct {
	Page   int `json:"page"`
	Limit  int `json:"limit"`
	Offset int `json:"offset"`
}

// PaginationMeta represents pagination metadata for API responses
type PaginationMeta struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	Data       interface{}     `json:"data"`
	Pagination *PaginationMeta `json:"pagination"`
}

// ParsePagination extracts and validates pagination parameters from request
func ParsePagination(c *gin.Context) *PaginationParams {
	// Default values
	page := 1
	limit := 20

	// Parse page parameter
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// Parse limit parameter
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// Validate and normalize limit (1-100)
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	// Calculate offset
	offset := (page - 1) * limit

	return &PaginationParams{
		Page:   page,
		Limit:  limit,
		Offset: offset,
	}
}

// CreatePaginationMeta creates pagination metadata from parameters and total count
func CreatePaginationMeta(page, limit int, total int64) *PaginationMeta {
	totalPages := int((total + int64(limit) - 1) / int64(limit)) // Ceiling division
	if totalPages < 1 {
		totalPages = 1
	}

	return &PaginationMeta{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// CreatePaginatedResponse creates a standardized paginated response
func CreatePaginatedResponse(data interface{}, page, limit int, total int64) *PaginatedResponse {
	return &PaginatedResponse{
		Data:       data,
		Pagination: CreatePaginationMeta(page, limit, total),
	}
}

// GetPaginationParams is a helper function to extract pagination params
func GetPaginationParams(c *gin.Context) (page, limit, offset int) {
	params := ParsePagination(c)
	return params.Page, params.Limit, params.Offset
}