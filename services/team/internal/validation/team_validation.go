package validation

import (
	"regexp"
	"strings"

	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/team/internal/models"
)

var (
	slugRegex = regexp.MustCompile(`^[a-z0-9]+(?:-[a-z0-9]+)*$`)
	nameRegex = regexp.MustCompile(`^[a-zA-Z0-9\s\-_\.]+$`)
)

// ValidateTeamCreation validates team creation request
func ValidateTeamCreation(name, description, website, location, category string) utils.ValidationErrors {
	var errors utils.ValidationErrors

	// Validate name
	if strings.TrimSpace(name) == "" {
		errors = append(errors, utils.ValidationError{
			Field:   "name",
			Message: "Team name is required",
		})
	} else {
		// Sanitize and validate
		sanitizedName := utils.SanitizeString(name)
		if !utils.ValidateStringLength(sanitizedName, 2, 100) {
			errors = append(errors, utils.ValidationError{
				Field:   "name",
				Message: "Team name must be between 2 and 100 characters",
			})
		}
		if !nameRegex.MatchString(sanitizedName) {
			errors = append(errors, utils.ValidationError{
				Field:   "name",
				Message: "Team name contains invalid characters",
			})
		}
	}

	// Validate description
	if description != "" {
		sanitizedDesc := utils.SanitizeHTML(description)
		if !utils.ValidateStringLength(sanitizedDesc, 0, 500) {
			errors = append(errors, utils.ValidationError{
				Field:   "description",
				Message: "Team description must be less than 500 characters",
			})
		}
	}

	// Validate website URL
	if website != "" && !utils.ValidateURL(website) {
		errors = append(errors, utils.ValidationError{
			Field:   "website",
			Message: "Invalid website URL",
		})
	}

	// Validate location
	if location != "" {
		sanitizedLocation := utils.SanitizeString(location)
		if !utils.ValidateStringLength(sanitizedLocation, 0, 100) {
			errors = append(errors, utils.ValidationError{
				Field:   "location",
				Message: "Location must be less than 100 characters",
			})
		}
	}

	// Validate category
	if category != "" {
		if !isValidCategory(category) {
			errors = append(errors, utils.ValidationError{
				Field:   "category",
				Message: "Invalid team category",
			})
		}
	}

	return errors
}

// ValidateSlug validates team slug format
func ValidateSlug(slug string) bool {
	if !utils.ValidateStringLength(slug, 2, 100) {
		return false
	}
	
	// Convert to lowercase for validation
	normalizedSlug := strings.ToLower(strings.TrimSpace(slug))
	
	// Check format: alphanumeric with hyphens, no leading/trailing hyphens
	if !slugRegex.MatchString(normalizedSlug) {
		return false
	}
	
	// Additional checks for reserved words
	reservedSlugs := map[string]bool{
		"api": true, "admin": true, "www": true, "help": true,
		"support": true, "settings": true, "profile": true,
		"teams": true, "users": true, "invitations": true,
	}
	
	return !reservedSlugs[normalizedSlug]
}

// ValidateTeamRole validates team role
func ValidateTeamRole(role models.TeamRole) bool {
	switch role {
	case models.TeamRoleOwner, models.TeamRoleAdmin, models.TeamRoleMember:
		return true
	default:
		return false
	}
}

// ValidateTeamVisibility validates team visibility
func ValidateTeamVisibility(visibility models.TeamVisibility) bool {
	switch visibility {
	case models.TeamVisibilityPublic, models.TeamVisibilityPrivate, models.TeamVisibilityRestricted:
		return true
	default:
		return false
	}
}

// ValidateJoinPolicy validates join policy
func ValidateJoinPolicy(policy models.JoinPolicy) bool {
	switch policy {
	case models.JoinPolicyOpen, models.JoinPolicyApproval, models.JoinPolicyInviteOnly:
		return true
	default:
		return false
	}
}

// ValidatePermissions validates team member permissions
func ValidatePermissions(permissions []string) bool {
	validPermissions := map[string]bool{
		"team.read":           true,
		"team.write":          true,
		"team.delete":         true,
		"members.read":        true,
		"members.write":       true,
		"members.remove":      true,
		"settings.read":       true,
		"settings.write":      true,
		"invitations.read":    true,
		"invitations.write":   true,
		"invitations.delete":  true,
		"content.read":        true,
		"content.write":       true,
		"content.delete":      true,
		"content.moderate":    true,
	}

	for _, permission := range permissions {
		if !validPermissions[permission] {
			return false
		}
	}
	return true
}

// ValidateInvitationMessage validates invitation message
func ValidateInvitationMessage(message string) utils.ValidationErrors {
	var errors utils.ValidationErrors

	if message != "" {
		sanitizedMessage := utils.SanitizeHTML(message)
		if !utils.ValidateStringLength(sanitizedMessage, 0, 500) {
			errors = append(errors, utils.ValidationError{
				Field:   "message",
				Message: "Invitation message must be less than 500 characters",
			})
		}
	}

	return errors
}

// isValidCategory checks if category is in allowed list
func isValidCategory(category string) bool {
	// Use the dynamic list from models to ensure consistency
	for _, validCategory := range models.DefaultTeamCategories {
		if strings.EqualFold(category, validCategory) {
			return true
		}
	}
	return false
}

// SanitizeTeamInput sanitizes team input data
func SanitizeTeamInput(name, description, website, location string) (string, string, string, string) {
	return utils.SanitizeString(name),
		utils.SanitizeHTML(description),
		strings.TrimSpace(website),
		utils.SanitizeString(location)
}