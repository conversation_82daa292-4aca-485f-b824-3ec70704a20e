package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/clients"
	sharedModels "github.com/swork-team/platform/pkg/models"
	"gorm.io/gorm"
)

// Use shared models - no duplication
type User = clients.User
type Team = sharedModels.Team
type TeamMember = sharedModels.TeamMember
type TeamVisibility = sharedModels.TeamVisibility
type JoinPolicy = sharedModels.JoinPolicy
type TeamRole = sharedModels.TeamRole
type Permissions = sharedModels.Permissions
type TeamSettings = sharedModels.TeamSettings

// Use shared enum constants - fixes case inconsistency
const (
	TeamVisibilityPublic     = sharedModels.TeamVisibilityPublic     // "public" not "PUBLIC"
	TeamVisibilityPrivate    = sharedModels.TeamVisibilityPrivate    // "private" not "PRIVATE"
	TeamVisibilityRestricted = sharedModels.TeamVisibilityRestricted // "restricted" not "RESTRICTED"

	JoinPolicyOpen       = sharedModels.JoinPolicyOpen
	JoinPolicyApproval   = sharedModels.JoinPolicyApproval
	JoinPolicyInviteOnly = sharedModels.JoinPolicyInviteOnly

	TeamRoleOwner  = sharedModels.TeamRoleOwner
	TeamRoleAdmin  = sharedModels.TeamRoleAdmin
	TeamRoleMember = sharedModels.TeamRoleMember
)

// Additional types needed by the team service
type InvitationStatus = TeamInvitationStatus
type RequestStatus = TeamJoinRequestStatus
type UserTeamMembership = TeamMember

// TeamMemberWithUser extends the shared TeamMember with populated user data
type TeamMemberWithUser struct {
	sharedModels.TeamMember
	User *User `json:"user,omitempty" gorm:"-"` // Populated field, not stored in DB
}

// UserContext represents user context for teams
type UserContext struct {
	UserID      uuid.UUID  `json:"user_id" gorm:"type:uuid;primaryKey"`
	ContextType string     `json:"context_type" gorm:"not null"`
	TeamID      *uuid.UUID `json:"team_id" gorm:"type:uuid;index"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// GetDefaultPermissionsForRole returns default permissions for a team role
func GetDefaultPermissionsForRole(role TeamRole) Permissions {
	switch role {
	case TeamRoleOwner:
		return Permissions{
			"team.read", "team.write", "team.delete",
			"members.read", "members.write", "members.remove",
			"settings.read", "settings.write",
			"invitations.read", "invitations.write", "invitations.delete",
			"content.read", "content.write", "content.delete", "content.moderate",
		}
	case TeamRoleAdmin:
		return Permissions{
			"team.read", "team.write",
			"members.read", "members.write",
			"settings.read", "settings.write",
			"invitations.read", "invitations.write",
			"content.read", "content.write", "content.moderate",
		}
	case TeamRoleMember:
		return Permissions{
			"team.read",
			"members.read",
			"content.read", "content.write",
		}
	default:
		return Permissions{"team.read", "members.read", "content.read"}
	}
}

// Team-service specific models (not duplicated in shared models)
type TeamInvitation struct {
	ID          uuid.UUID            `json:"id" gorm:"type:uuid;primaryKey"`
	TeamID      uuid.UUID            `json:"team_id" gorm:"type:uuid;not null;index"`
	InviterID   uuid.UUID            `json:"inviter_id" gorm:"type:uuid;not null"`
	InviteeID   *uuid.UUID           `json:"invitee_id" gorm:"type:uuid;index"` // null if invite by email
	Email       string               `json:"email" gorm:"size:255;index"`       // for email invites
	Token       string               `json:"token" gorm:"uniqueIndex;not null"`
	Role        TeamRole             `json:"role" gorm:"not null"`
	Status      TeamInvitationStatus `json:"status" gorm:"default:'pending'"`
	Message     string               `json:"message" gorm:"type:text"`
	ExpiresAt   time.Time            `json:"expires_at" gorm:"not null"`
	RespondedAt *time.Time           `json:"responded_at,omitempty"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`

	// Relationships
	Team    Team  `json:"team" gorm:"foreignKey:TeamID"`
	Inviter User  `json:"inviter" gorm:"foreignKey:InviterID"`
	Invitee *User `json:"invitee,omitempty" gorm:"foreignKey:InviteeID"`
}

type TeamJoinRequest struct {
	ID        uuid.UUID             `json:"id" gorm:"type:uuid;primaryKey"`
	TeamID    uuid.UUID             `json:"team_id" gorm:"type:uuid;not null;index"`
	UserID    uuid.UUID             `json:"user_id" gorm:"type:uuid;not null;index"`
	Status    TeamJoinRequestStatus `json:"status" gorm:"default:'pending'"`
	Message   string                `json:"message" gorm:"type:text"`
	CreatedAt time.Time             `json:"created_at"`
	UpdatedAt time.Time             `json:"updated_at"`

	// Relationships
	Team Team `json:"team" gorm:"foreignKey:TeamID"`
	User User `json:"user" gorm:"foreignKey:UserID"`
}


type TeamActivity struct {
	ID          uuid.UUID       `json:"id" gorm:"type:uuid;primaryKey"`
	TeamID      uuid.UUID       `json:"team_id" gorm:"type:uuid;not null;index"`
	UserID      uuid.UUID       `json:"user_id" gorm:"type:uuid;not null;index"`
	Action      string          `json:"action" gorm:"not null"` // Changed from TeamActionType to string for flexibility
	TargetType  string          `json:"target_type" gorm:"size:100"`
	TargetID    string          `json:"target_id" gorm:"size:255"`
	Description string          `json:"description" gorm:"type:text"`
	Metadata    json.RawMessage `json:"metadata" gorm:"type:jsonb;default:'{}'"` // JSON data
	IPAddress   string          `json:"ip_address" gorm:"size:45"`
	UserAgent   string          `json:"user_agent" gorm:"size:500"`
	CreatedAt   time.Time       `json:"created_at"`

	// Relationships - removed foreign key constraints for audit independence
	Team Team `json:"team,omitempty" gorm:"foreignKey:TeamID"`
	// User relationship removed to avoid foreign key constraint issues
}

// Enums for team-specific models
type TeamInvitationStatus string

const (
	TeamInvitationPending  TeamInvitationStatus = "pending"
	TeamInvitationAccepted TeamInvitationStatus = "accepted"
	TeamInvitationRejected TeamInvitationStatus = "rejected"
	TeamInvitationExpired  TeamInvitationStatus = "expired"

	// Backward compatibility aliases
	InvitationStatusPending  = TeamInvitationPending
	InvitationStatusAccepted = TeamInvitationAccepted
)

// Default team categories
var DefaultTeamCategories = []string{
	"Technology",
	"Business",
	"Education",
	"Health",
	"Sports",
	"Entertainment",
	"Art",
	"Science",
	"Social",
	"Other",
}

type TeamJoinRequestStatus string

const (
	TeamJoinRequestPending  TeamJoinRequestStatus = "pending"
	TeamJoinRequestApproved TeamJoinRequestStatus = "approved"
	TeamJoinRequestRejected TeamJoinRequestStatus = "rejected"
)

type TeamActionType string

const (
	TeamActionMemberJoined    TeamActionType = "member_joined"
	TeamActionMemberLeft      TeamActionType = "member_left"
	TeamActionMemberPromoted  TeamActionType = "member_promoted"
	TeamActionMemberDemoted   TeamActionType = "member_demoted"
	TeamActionTeamUpdated     TeamActionType = "team_updated"
	TeamActionSettingsChanged TeamActionType = "settings_changed"
)

// BeforeCreate hooks for UUID generation
func (ti *TeamInvitation) BeforeCreate(tx *gorm.DB) (err error) {
	if ti.ID == uuid.Nil {
		ti.ID = uuid.New()
	}
	return
}

func (tjr *TeamJoinRequest) BeforeCreate(tx *gorm.DB) (err error) {
	if tjr.ID == uuid.Nil {
		tjr.ID = uuid.New()
	}
	return
}


func (ta *TeamActivity) BeforeCreate(tx *gorm.DB) (err error) {
	if ta.ID == uuid.Nil {
		ta.ID = uuid.New()
	}
	return
}

// Team visibility and access methods are defined in the shared models
// CanUserView and IsUserOwner are available via the shared Team model


// Helper methods for invitations
func (ti *TeamInvitation) IsExpired() bool {
	return time.Now().After(ti.ExpiresAt)
}

func (ti *TeamInvitation) CanRespond() bool {
	return ti.Status == TeamInvitationPending && !ti.IsExpired()
}

// Table names
func (TeamInvitation) TableName() string {
	return "team_invitations"
}

func (TeamJoinRequest) TableName() string {
	return "team_join_requests"
}


func (TeamActivity) TableName() string {
	return "team_activities"
}
