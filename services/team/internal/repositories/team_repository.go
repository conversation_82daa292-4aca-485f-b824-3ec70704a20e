package repositories

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/team/internal/models"
	"gorm.io/gorm"
)

type TeamRepository struct {
	db *gorm.DB
}

func NewTeamRepository(db *gorm.DB) *TeamRepository {
	return &TeamRepository{
		db: db,
	}
}

// Team operations
func (r *TeamRepository) CreateTeam(ctx context.Context, team *models.Team) error {
	tx := r.db.WithContext(ctx).Begin()
	
	// Ensure rollback on any panic
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// Create team
	if err := tx.Create(team).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create team: %w", err)
	}

	// Create team owner as member
	member := &models.TeamMember{
		TeamID:      team.ID,
		UserID:      team.OwnerID,
		Role:        models.TeamRoleOwner,
		Permissions: models.GetDefaultPermissionsForRole(models.TeamRoleOwner),
		IsActive:    true,
		IsPrimary:   true, // Owner's first team is primary
		JoinedAt:    time.Now(),
	}

	if err := tx.Create(member).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create team owner membership: %w", err)
	}

	// Create default team settings
	settings := &models.TeamSettings{
		TeamID:               team.ID,
		AllowMemberInvites:   true,
		RequireApproval:      false,
		ShowMemberList:       true,
		ShowMemberProfiles:   true,
		AllowDirectMessages:  true,
		AllowPublicChannels:  true,
		AllowPrivateChannels: true,
		AllowFileSharing:     true,
		AllowExternalLinks:   true,
		ModerateContent:      false,
		EnableCalendar:       true,
		EnableDrive:          true,
		EnableNotifications:  true,
	}

	if err := tx.Create(settings).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create team settings: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit team creation transaction: %w", err)
	}

	return nil
}

func (r *TeamRepository) GetTeamByID(ctx context.Context, id uuid.UUID) (*models.Team, error) {
	var team models.Team
	result := r.db.WithContext(ctx).
		Preload("Members", "is_active = ?", true).
		Preload("Settings").
		Where("id = ?", id).
		First(&team)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get team: %w", result.Error)
	}

	return &team, nil
}

func (r *TeamRepository) GetTeamBySlug(ctx context.Context, slug string) (*models.Team, error) {
	var team models.Team
	result := r.db.WithContext(ctx).
		Preload("Members", "is_active = ?", true).
		Preload("Settings").
		Where("slug = ?", slug).
		First(&team)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get team by slug: %w", result.Error)
	}

	return &team, nil
}

func (r *TeamRepository) GetTeamsByOwner(ctx context.Context, ownerID string, limit, offset int) ([]models.Team, error) {
	var teams []models.Team
	result := r.db.WithContext(ctx).
		Where("owner_id = ? AND is_active = ?", ownerID, true).
		Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&teams)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get teams by owner: %w", result.Error)
	}

	return teams, nil
}

func (r *TeamRepository) GetTeamsByMember(ctx context.Context, userID string, limit, offset int) ([]models.Team, error) {
	var teams []models.Team
	result := r.db.WithContext(ctx).
		Joins("JOIN team_members ON teams.id = team_members.team_id").
		Where("team_members.user_id = ? AND team_members.is_active = ? AND teams.is_active = ?", userID, true, true).
		Preload("Members", "is_active = ?", true).
		Order("team_members.joined_at DESC").
		Limit(limit).Offset(offset).
		Find(&teams)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get teams by member: %w", result.Error)
	}

	return teams, nil
}

// GetTeamsByMemberWithCount returns teams by member with total count for pagination
func (r *TeamRepository) GetTeamsByMemberWithCount(ctx context.Context, userID string, limit, offset int) ([]models.Team, int64, error) {
	var teams []models.Team
	var total int64

	// Get total count
	countQuery := r.db.WithContext(ctx).Model(&models.Team{}).
		Joins("JOIN team_members ON teams.id = team_members.team_id").
		Where("team_members.user_id = ? AND team_members.is_active = ? AND teams.is_active = ? AND teams.deleted_at IS NULL", userID, true, true)

	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count teams by member: %w", err)
	}

	// Get paginated results
	result := r.db.WithContext(ctx).
		Joins("JOIN team_members ON teams.id = team_members.team_id").
		Where("team_members.user_id = ? AND team_members.is_active = ? AND teams.is_active = ? AND teams.deleted_at IS NULL", userID, true, true).
		Preload("Members", "is_active = ?", true).
		Order("team_members.joined_at DESC").
		Limit(limit).Offset(offset).
		Find(&teams)

	if result.Error != nil {
		return nil, 0, fmt.Errorf("failed to get teams by member: %w", result.Error)
	}

	return teams, total, nil
}

func (r *TeamRepository) SearchTeams(ctx context.Context, query string, visibility models.TeamVisibility, limit, offset int) ([]models.Team, error) {
	var teams []models.Team

	searchQuery := r.db.WithContext(ctx).Where("is_active = ?", true)

	if visibility != "" {
		searchQuery = searchQuery.Where("visibility = ?", visibility)
	}

	if query != "" {
		// Sanitize search query to prevent SQL injection via LIKE patterns
		sanitizedQuery := sanitizeLikePattern(strings.ToLower(query))
		searchPattern := "%" + sanitizedQuery + "%"
		searchQuery = searchQuery.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchPattern, searchPattern)
	}

	result := searchQuery.Order("member_count DESC, created_at DESC").
		Limit(limit).Offset(offset).
		Find(&teams)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to search teams: %w", result.Error)
	}

	return teams, nil
}

// SearchTeamsWithCount searches teams and returns total count for pagination
func (r *TeamRepository) SearchTeamsWithCount(ctx context.Context, query string, visibility models.TeamVisibility, limit, offset int) ([]models.Team, int64, error) {
	var teams []models.Team
	var total int64

	// Build base query
	baseQuery := r.db.WithContext(ctx).Model(&models.Team{}).
		Where("is_active = ? AND deleted_at IS NULL", true)

	if visibility != "" {
		baseQuery = baseQuery.Where("visibility = ?", visibility)
	}

	if query != "" {
		// Sanitize search query to prevent SQL injection via LIKE patterns
		sanitizedQuery := sanitizeLikePattern(strings.ToLower(query))
		searchPattern := "%" + sanitizedQuery + "%"
		baseQuery = baseQuery.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchPattern, searchPattern)
	}

	// Get total count
	if err := baseQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count search results: %w", err)
	}

	// Get paginated results
	err := baseQuery.Order("member_count DESC, created_at DESC").
		Limit(limit).Offset(offset).
		Find(&teams).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to search teams: %w", err)
	}

	return teams, total, nil
}

func (r *TeamRepository) UpdateTeam(ctx context.Context, team *models.Team) error {
	result := r.db.WithContext(ctx).Save(team)
	if result.Error != nil {
		return fmt.Errorf("failed to update team: %w", result.Error)
	}
	return nil
}

func (r *TeamRepository) DeleteTeam(ctx context.Context, id uuid.UUID) error {
	// Use transaction to ensure all operations succeed or fail together
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	// Delete team invitations first (they have foreign key to team)
	if err := tx.Where("team_id = ?", id).Delete(&models.TeamInvitation{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete team invitations: %w", err)
	}

	// Delete team join requests
	if err := tx.Where("team_id = ?", id).Delete(&models.TeamJoinRequest{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete team join requests: %w", err)
	}

	// Delete team members
	if err := tx.Where("team_id = ?", id).Delete(&models.TeamMember{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete team members: %w", err)
	}

	// Delete team settings
	if err := tx.Where("team_id = ?", id).Delete(&models.TeamSettings{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete team settings: %w", err)
	}

	// Finally, delete the team itself (soft delete)
	result := tx.Delete(&models.Team{}, id)
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete team: %w", result.Error)
	}
	
	// Check if any row was affected (team existed)
	if result.RowsAffected == 0 {
		tx.Rollback()
		return fmt.Errorf("team not found")
	}

	// Keep team_activities for audit purposes - don't delete them
	
	return tx.Commit().Error
}

func (r *TeamRepository) IsSlugTaken(ctx context.Context, slug string, excludeTeamID *uuid.UUID) (bool, error) {
	query := r.db.WithContext(ctx).Model(&models.Team{}).Where("slug = ?", slug)

	if excludeTeamID != nil {
		query = query.Where("id != ?", *excludeTeamID)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check slug: %w", err)
	}

	return count > 0, nil
}

// Team member operations
func (r *TeamRepository) AddMember(ctx context.Context, member *models.TeamMember) error {
	tx := r.db.WithContext(ctx).Begin()

	// Check for existing membership first to prevent duplicates
	var existingCount int64
	if err := tx.Model(&models.TeamMember{}).
		Where("team_id = ? AND user_id = ? AND is_active = ?", member.TeamID, member.UserID, true).
		Count(&existingCount).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to check existing membership: %w", err)
	}

	if existingCount > 0 {
		tx.Rollback()
		return errors.New("user is already a member")
	}

	// Add member
	if err := tx.Create(member).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to add member: %w", err)
	}

	// Update member count atomically with row locking
	result := tx.Model(&models.Team{}).
		Where("id = ?", member.TeamID).
		Update("member_count", gorm.Expr("member_count + 1"))

	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update member count: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return errors.New("team not found for member count update")
	}

	return tx.Commit().Error
}

func (r *TeamRepository) GetMember(ctx context.Context, teamID uuid.UUID, userID string) (*models.TeamMember, error) {
	var member models.TeamMember
	result := r.db.WithContext(ctx).Where("team_id = ? AND user_id = ? AND is_active = ?", teamID, userID, true).First(&member)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get member: %w", result.Error)
	}

	return &member, nil
}

func (r *TeamRepository) GetMembers(ctx context.Context, teamID uuid.UUID, limit, offset int) ([]models.TeamMember, error) {
	var members []models.TeamMember
	result := r.db.WithContext(ctx).
		Where("team_id = ? AND is_active = ?", teamID, true).
		Order("role DESC, joined_at ASC").
		Limit(limit).Offset(offset).
		Find(&members)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get members: %w", result.Error)
	}

	return members, nil
}

// GetMembersWithCount returns team members with total count for pagination
func (r *TeamRepository) GetMembersWithCount(ctx context.Context, teamID uuid.UUID, limit, offset int) ([]models.TeamMember, int64, error) {
	var members []models.TeamMember
	var total int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.TeamMember{}).
		Where("team_id = ? AND is_active = ?", teamID, true).
		Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count members: %w", err)
	}

	// Get paginated results
	result := r.db.WithContext(ctx).
		Where("team_id = ? AND is_active = ?", teamID, true).
		Order("role DESC, joined_at ASC").
		Limit(limit).Offset(offset).
		Find(&members)

	if result.Error != nil {
		return nil, 0, fmt.Errorf("failed to get members: %w", result.Error)
	}

	return members, total, nil
}

func (r *TeamRepository) UpdateMember(ctx context.Context, member *models.TeamMember) error {
	result := r.db.WithContext(ctx).Save(member)
	if result.Error != nil {
		return fmt.Errorf("failed to update member: %w", result.Error)
	}
	return nil
}

func (r *TeamRepository) RemoveMember(ctx context.Context, teamID uuid.UUID, userID string) error {
	tx := r.db.WithContext(ctx).Begin()

	// Check if member exists and is active
	var memberCount int64
	if err := tx.Model(&models.TeamMember{}).
		Where("team_id = ? AND user_id = ? AND is_active = ?", teamID, userID, true).
		Count(&memberCount).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to check member existence: %w", err)
	}

	if memberCount == 0 {
		tx.Rollback()
		return errors.New("member not found or already inactive")
	}

	// Deactivate member
	result := tx.Model(&models.TeamMember{}).
		Where("team_id = ? AND user_id = ? AND is_active = ?", teamID, userID, true).
		Update("is_active", false)

	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to remove member: %w", result.Error)
	}

	// Update member count atomically with row locking
	updateResult := tx.Model(&models.Team{}).
		Where("id = ? AND member_count > 0", teamID).
		Update("member_count", gorm.Expr("member_count - 1"))

	if updateResult.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update member count: %w", updateResult.Error)
	}

	if updateResult.RowsAffected == 0 {
		tx.Rollback()
		return errors.New("team not found or member count already at zero")
	}

	return tx.Commit().Error
}

func (r *TeamRepository) TransferOwnership(ctx context.Context, teamID uuid.UUID, currentOwnerID, newOwnerID string) error {
	tx := r.db.WithContext(ctx).Begin()

	// Update team owner
	if err := tx.Model(&models.Team{}).Where("id = ?", teamID).Update("owner_id", newOwnerID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update team owner: %w", err)
	}

	// Update current owner role to admin
	if err := tx.Model(&models.TeamMember{}).
		Where("team_id = ? AND user_id = ?", teamID, currentOwnerID).
		Update("role", models.TeamRoleAdmin).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update current owner role: %w", err)
	}

	// Update new owner role
	if err := tx.Model(&models.TeamMember{}).
		Where("team_id = ? AND user_id = ?", teamID, newOwnerID).
		Update("role", models.TeamRoleOwner).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update new owner role: %w", err)
	}

	return tx.Commit().Error
}

// Invitation operations
func (r *TeamRepository) CreateInvitation(ctx context.Context, invitation *models.TeamInvitation) error {
	result := r.db.WithContext(ctx).Create(invitation)
	if result.Error != nil {
		return fmt.Errorf("failed to create invitation: %w", result.Error)
	}
	return nil
}

func (r *TeamRepository) GetInvitationByToken(ctx context.Context, token string) (*models.TeamInvitation, error) {
	var invitation models.TeamInvitation
	result := r.db.WithContext(ctx).Preload("Team").Where("token = ?", token).First(&invitation)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get invitation: %w", result.Error)
	}

	return &invitation, nil
}

func (r *TeamRepository) GetInvitations(ctx context.Context, teamID uuid.UUID, status models.InvitationStatus) ([]models.TeamInvitation, error) {
	var invitations []models.TeamInvitation
	query := r.db.WithContext(ctx).Where("team_id = ?", teamID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	result := query.Order("created_at DESC").Find(&invitations)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get invitations: %w", result.Error)
	}

	return invitations, nil
}

func (r *TeamRepository) GetUserInvitations(ctx context.Context, userID string, status models.InvitationStatus) ([]models.TeamInvitation, error) {
	var invitations []models.TeamInvitation
	query := r.db.WithContext(ctx).Preload("Team").Where("invitee_id = ?", userID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	result := query.Order("created_at DESC").Find(&invitations)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user invitations: %w", result.Error)
	}

	return invitations, nil
}

func (r *TeamRepository) UpdateInvitation(ctx context.Context, invitation *models.TeamInvitation) error {
	result := r.db.WithContext(ctx).Save(invitation)
	if result.Error != nil {
		return fmt.Errorf("failed to update invitation: %w", result.Error)
	}
	return nil
}

func (r *TeamRepository) DeleteInvitation(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Delete(&models.TeamInvitation{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete invitation: %w", result.Error)
	}
	return nil
}

// Join request operations
func (r *TeamRepository) CreateJoinRequest(ctx context.Context, request *models.TeamJoinRequest) error {
	result := r.db.WithContext(ctx).Create(request)
	if result.Error != nil {
		return fmt.Errorf("failed to create join request: %w", result.Error)
	}
	return nil
}

func (r *TeamRepository) GetJoinRequest(ctx context.Context, teamID uuid.UUID, userID string) (*models.TeamJoinRequest, error) {
	var request models.TeamJoinRequest
	result := r.db.WithContext(ctx).Where("team_id = ? AND user_id = ?", teamID, userID).First(&request)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get join request: %w", result.Error)
	}

	return &request, nil
}

func (r *TeamRepository) GetJoinRequests(ctx context.Context, teamID uuid.UUID, status models.RequestStatus) ([]models.TeamJoinRequest, error) {
	var requests []models.TeamJoinRequest
	query := r.db.WithContext(ctx).Where("team_id = ?", teamID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	result := query.Order("created_at DESC").Find(&requests)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get join requests: %w", result.Error)
	}

	return requests, nil
}

func (r *TeamRepository) UpdateJoinRequest(ctx context.Context, request *models.TeamJoinRequest) error {
	result := r.db.WithContext(ctx).Save(request)
	if result.Error != nil {
		return fmt.Errorf("failed to update join request: %w", result.Error)
	}
	return nil
}

// Settings operations
func (r *TeamRepository) GetSettings(ctx context.Context, teamID uuid.UUID) (*models.TeamSettings, error) {
	var settings models.TeamSettings
	result := r.db.WithContext(ctx).Where("team_id = ?", teamID).First(&settings)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get team settings: %w", result.Error)
	}

	return &settings, nil
}

func (r *TeamRepository) UpdateSettings(ctx context.Context, settings *models.TeamSettings) error {
	result := r.db.WithContext(ctx).Save(settings)
	if result.Error != nil {
		return fmt.Errorf("failed to update team settings: %w", result.Error)
	}
	return nil
}

// Activity operations
func (r *TeamRepository) LogActivity(ctx context.Context, activity *models.TeamActivity) error {
	result := r.db.WithContext(ctx).Create(activity)
	if result.Error != nil {
		return fmt.Errorf("failed to log activity: %w", result.Error)
	}
	return nil
}

func (r *TeamRepository) GetActivities(ctx context.Context, teamID uuid.UUID, limit, offset int) ([]models.TeamActivity, error) {
	var activities []models.TeamActivity
	result := r.db.WithContext(ctx).
		Where("team_id = ?", teamID).
		Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&activities)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get activities: %w", result.Error)
	}

	return activities, nil
}

// Utility operations
func (r *TeamRepository) GetTeamStats(ctx context.Context, teamID uuid.UUID) (*TeamStats, error) {
	var stats TeamStats

	// Get member count by role
	err := r.db.WithContext(ctx).
		Model(&models.TeamMember{}).
		Select("role, COUNT(*) as count").
		Where("team_id = ? AND is_active = ?", teamID, true).
		Group("role").
		Scan(&stats.MembersByRole).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get team stats: %w", err)
	}

	// Get total member count
	err = r.db.WithContext(ctx).
		Model(&models.TeamMember{}).
		Where("team_id = ? AND is_active = ?", teamID, true).
		Count(&stats.TotalMembers).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get total member count: %w", err)
	}

	// Get pending invitations count
	err = r.db.WithContext(ctx).
		Model(&models.TeamInvitation{}).
		Where("team_id = ? AND status = ?", teamID, models.InvitationStatusPending).
		Count(&stats.PendingInvitations).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get pending invitations count: %w", err)
	}

	return &stats, nil
}

type TeamStats struct {
	TotalMembers       int64                    `json:"total_members"`
	MembersByRole      []map[string]interface{} `json:"members_by_role"`
	PendingInvitations int64                    `json:"pending_invitations"`
}

// User Context Repository Methods
func (r *TeamRepository) GetUserContext(ctx context.Context, userID string) (*models.UserContext, error) {
	var context models.UserContext
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&context).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Parse userID string to UUID
			userUUID, parseErr := uuid.Parse(userID)
			if parseErr != nil {
				return nil, fmt.Errorf("invalid user ID format: %w", parseErr)
			}
			// Return default global context if none exists
			return &models.UserContext{
				UserID:      userUUID,
				ContextType: "global",
			}, nil
		}
		return nil, fmt.Errorf("failed to get user context: %w", err)
	}

	return &context, nil
}

func (r *TeamRepository) SetUserContext(ctx context.Context, userContext *models.UserContext) error {
	// Use UPSERT (INSERT ... ON CONFLICT UPDATE)
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userContext.UserID).
		Assign(models.UserContext{
			ContextType: userContext.ContextType,
			TeamID:      userContext.TeamID,
		}).
		FirstOrCreate(userContext).Error

	if err != nil {
		return fmt.Errorf("failed to set user context: %w", err)
	}

	return nil
}

func (r *TeamRepository) GetUserTeamMemberships(ctx context.Context, userID string) ([]models.UserTeamMembership, error) {
	var memberships []models.UserTeamMembership

	// Parse userID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	err = r.db.WithContext(ctx).
		Where("user_id = ? AND is_active = true", userUUID).
		Order("is_primary DESC, joined_at ASC").
		Find(&memberships).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user team memberships: %w", err)
	}

	return memberships, nil
}

func (r *TeamRepository) IsUserMember(ctx context.Context, teamID uuid.UUID, userID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TeamMember{}).
		Where("team_id = ? AND user_id = ? AND is_active = true", teamID, userID).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check team membership: %w", err)
	}

	return count > 0, nil
}

// Team Categories Repository Methods
func (r *TeamRepository) GetTeamsByCategory(ctx context.Context, category string, limit, offset int) ([]models.Team, error) {
	var teams []models.Team

	query := r.db.WithContext(ctx).
		Where("deleted_at IS NULL")

	if category != "" {
		query = query.Where("category = ?", category)
	}

	err := query.
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&teams).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get teams by category: %w", err)
	}

	return teams, nil
}

// GetTeamsByCategoryWithCount returns teams by category with total count for pagination
func (r *TeamRepository) GetTeamsByCategoryWithCount(ctx context.Context, category string, limit, offset int) ([]models.Team, int64, error) {
	var teams []models.Team
	var total int64

	// Build base query
	baseQuery := r.db.WithContext(ctx).Model(&models.Team{}).
		Where("deleted_at IS NULL")

	if category != "" {
		baseQuery = baseQuery.Where("category = ?", category)
	}

	// Get total count
	if err := baseQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count teams by category: %w", err)
	}

	// Get paginated results
	err := baseQuery.
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&teams).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get teams by category: %w", err)
	}

	return teams, total, nil
}

// sanitizeLikePattern escapes special characters in LIKE patterns to prevent SQL injection
func sanitizeLikePattern(input string) string {
	// Escape SQL LIKE special characters: %, _, \
	replacer := strings.NewReplacer(
		"\\", "\\\\",  // Escape backslashes first
		"%", "\\%",    // Escape percentage signs
		"_", "\\_",    // Escape underscores
	)
	return replacer.Replace(input)
}
