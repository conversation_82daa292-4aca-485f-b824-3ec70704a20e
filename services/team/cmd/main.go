package main

import (
	"os"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	"github.com/swork-team/platform/services/team/internal/config"
	"github.com/swork-team/platform/services/team/internal/handlers"
	"github.com/swork-team/platform/services/team/internal/models"
	"github.com/swork-team/platform/services/team/internal/repositories"
	"github.com/swork-team/platform/services/team/internal/services"
)

func main() {
	cfg := config.LoadConfig()

	// Create config adapter for bootstrap
	configAdapter := server.NewConfigAdapter(cfg.Server, cfg.Database, cfg.Redis)

	// Use the new ServiceBootstrapper
	bootstrapper := server.NewServiceBootstrapper(server.ServiceBootstrapConfig{
		ServiceName:      "team",
		ConfigAdapter:    configAdapter,
		ModelsProvider:   getTeamModels,
		RouterSetup:      createTeamRouterSetup(),
		EnableExtensions: false,
		DatabaseType:     server.PostgreSQL,
	})

	bootstrapper.Bootstrap()
}

// getTeamModels returns models for migration
func getTeamModels() []interface{} {
	return []interface{}{
		&models.Team{},
		&models.TeamMember{},
		&models.TeamInvitation{},
		&models.TeamJoinRequest{},
		&models.TeamSettings{},
		&models.TeamActivity{},
		&models.UserContext{},
	}
}

// createTeamRouterSetup creates the router setup function for team service
func createTeamRouterSetup() server.RouterSetupFunction {
	return func(components *server.ServiceComponents) *gin.Engine {
		cfg := config.LoadConfig()

		teamRepo := repositories.NewTeamRepository(components.Database)
		teamService := services.NewTeamService(teamRepo, cfg, components.RedisClient, components.CacheManager, components.AsyncJobQueue, components.WebhookService)
		teamHandler := handlers.NewTeamHandler(teamService)

		// Get logger
		serviceLogger := logger.NewServiceLoggerManager("team").GetLogger()

		return setupRouter(teamHandler, cfg, serviceLogger)
	}
}

func setupRouter(teamHandler *handlers.TeamHandler, cfg *config.Config, serviceLogger logger.ServiceLogger) *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// Disable automatic redirect for trailing slashes
	router.RedirectTrailingSlash = false

	// Use standardized logging middleware
	router.Use(logger.RequestLoggingMiddleware(serviceLogger))
	router.Use(logger.UserContextMiddleware())
	router.Use(logger.ErrorLoggingMiddleware())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// Health check endpoint
	router.GET("/health", teamHandler.HealthCheck)

	// Public endpoints (no authentication required)
	router.GET("/teams/categories", teamHandler.GetTeamCategories)

	// Team API routes
	teams := router.Group("/teams")
	teams.Use(middleware.AuthMiddleware())
	{
		// Team management
		teams.POST("", teamHandler.CreateTeam)
		teams.GET("", teamHandler.GetTeamsByCategory) // GET with category query param
		teams.GET("/me", teamHandler.GetUserTeams)
		teams.GET("/search", teamHandler.SearchTeams)
		teams.GET("/slug/:slug", teamHandler.GetTeamBySlug)
		teams.GET("/:id", teamHandler.GetTeam)
		teams.PUT("/:id", teamHandler.UpdateTeam)
		teams.DELETE("/:id", teamHandler.DeleteTeam)

		// Member management
		teams.POST("/:id/invite", teamHandler.InviteMember)
		teams.GET("/:id/members", teamHandler.GetMembers)
		teams.PUT("/:id/members/:user_id/role", teamHandler.UpdateMemberRole)
		teams.PUT("/:id/members/:user_id/permissions", teamHandler.UpdateMemberPermissions)
		teams.DELETE("/:id/members/:user_id", teamHandler.RemoveMember)

		// Team settings
		teams.GET("/:id/settings", teamHandler.GetSettings)
		teams.PUT("/:id/settings", teamHandler.UpdateSettings)
	}

	// User API routes
	user := router.Group("/user")
	user.Use(middleware.AuthMiddleware())
	{
		user.GET("/context", teamHandler.GetUserContext)
		user.POST("/context", teamHandler.SetUserContext)
		user.GET("/teams", teamHandler.GetUserTeamMemberships)
	}

	// Invitation routes
	invitations := router.Group("/invitations")
	invitations.Use(middleware.AuthMiddleware())
	{
		invitations.POST("/:token/accept", teamHandler.AcceptInvitation)
		invitations.POST("/:token/decline", teamHandler.DeclineInvitation)
	}

	return router
}
