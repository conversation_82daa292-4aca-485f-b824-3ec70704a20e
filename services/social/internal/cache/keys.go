package cache

import "fmt"

// CacheKeyBuilder provides standardized cache key construction for the social service
type CacheKeyBuilder struct{}

// NewCacheKeyBuilder creates a new cache key builder
func NewCacheKeyBuilder() *CacheKeyBuilder {
	return &CacheKeyBuilder{}
}

// Cache key constants for consistency
const (
	// Prefixes
	PostPermissionPrefix = "post_permission"
	AttachmentPrefix     = "attachment"
	UserFeedPrefix       = "user_feed"
	PostPrefix           = "post"
	CommentPrefix        = "comment"
	
	// Separators
	KeySeparator     = ":"
	FieldSeparator   = "_"
)

// Post-related cache keys
func (b *CacheKeyBuilder) PostPermission(postID, userID, action string) string {
	return fmt.Sprintf("%s%s%s%s%s%s%s", PostPermissionPrefix, KeySeparator, postID, KeySeparator, userID, KeySeparator, action)
}

func (b *CacheKeyBuilder) PostData(postID string) string {
	return fmt.Sprintf("%s%s%s", PostPrefix, KeySeparator, postID)
}

func (b *CacheKeyBuilder) PostComments(postID string) string {
	return fmt.Sprintf("%s%s%s%s%s", PostPrefix, KeySeparator, postID, KeySeparator, "comments")
}

// User-related cache keys
func (b *CacheKeyBuilder) UserFeed(userID string) string {
	return fmt.Sprintf("%s%s%s", UserFeedPrefix, KeySeparator, userID)
}

func (b *CacheKeyBuilder) UserPosts(userID string) string {
	return fmt.Sprintf("user%s%s%s%s", KeySeparator, userID, KeySeparator, "posts")
}

// Comment-related cache keys
func (b *CacheKeyBuilder) CommentData(commentID string) string {
	return fmt.Sprintf("%s%s%s", CommentPrefix, KeySeparator, commentID)
}

func (b *CacheKeyBuilder) CommentReplies(commentID string) string {
	return fmt.Sprintf("%s%s%s%s%s", CommentPrefix, KeySeparator, commentID, KeySeparator, "replies")
}

// Attachment-related cache keys
func (b *CacheKeyBuilder) AttachmentData(attachmentID string) string {
	return fmt.Sprintf("%s%s%s", AttachmentPrefix, KeySeparator, attachmentID)
}

func (b *CacheKeyBuilder) AttachmentMetadata(attachmentID string) string {
	return fmt.Sprintf("%s%s%s%s%s", AttachmentPrefix, KeySeparator, attachmentID, KeySeparator, "metadata")
}

// Team-related cache keys
func (b *CacheKeyBuilder) TeamPosts(teamID string) string {
	return fmt.Sprintf("team%s%s%s%s", KeySeparator, teamID, KeySeparator, "posts")
}

func (b *CacheKeyBuilder) TeamMembers(teamID string) string {
	return fmt.Sprintf("team%s%s%s%s", KeySeparator, teamID, KeySeparator, "members")
}

// Engagement-related cache keys
func (b *CacheKeyBuilder) PostLikes(postID string) string {
	return fmt.Sprintf("%s%s%s%s%s", PostPrefix, KeySeparator, postID, KeySeparator, "likes")
}

func (b *CacheKeyBuilder) CommentLikes(commentID string) string {
	return fmt.Sprintf("%s%s%s%s%s", CommentPrefix, KeySeparator, commentID, KeySeparator, "likes")
}

func (b *CacheKeyBuilder) PostShares(postID string) string {
	return fmt.Sprintf("%s%s%s%s%s", PostPrefix, KeySeparator, postID, KeySeparator, "shares")
}

// Statistics cache keys
func (b *CacheKeyBuilder) PostStats(postID string) string {
	return fmt.Sprintf("%s%s%s%s%s", PostPrefix, KeySeparator, postID, KeySeparator, "stats")
}

func (b *CacheKeyBuilder) UserStats(userID string) string {
	return fmt.Sprintf("user%s%s%s%s", KeySeparator, userID, KeySeparator, "stats")
}

// Search-related cache keys
func (b *CacheKeyBuilder) SearchResults(query, userID string) string {
	// Use field separator within the key for multi-part values
	return fmt.Sprintf("search%s%s%s%s", KeySeparator, query, FieldSeparator, userID)
}

// Trending cache keys
func (b *CacheKeyBuilder) TrendingPosts(timeWindow string) string {
	return fmt.Sprintf("trending%s%s", KeySeparator, timeWindow)
}

// Custom cache key for arbitrary patterns
func (b *CacheKeyBuilder) Custom(parts ...string) string {
	result := ""
	for i, part := range parts {
		if i > 0 {
			result += KeySeparator
		}
		result += part
	}
	return result
}