package config

import (
	"time"

	"github.com/swork-team/platform/pkg/config"
)

// Config extends the MongoDB base configuration with Social-specific settings
type Config struct {
	config.MongoBaseServiceConfig
	Feed FeedConfig
}

type FeedConfig struct {
	FeedSize          int
	FeedCacheDuration time.Duration
	TrendingDuration  time.Duration
}

func LoadConfig() *Config {
	// Load base MongoDB configuration
	baseConfig := config.LoadMongoBaseConfig("SOCIAL")

	return &Config{
		MongoBaseServiceConfig: *baseConfig,
		Feed: FeedConfig{
			FeedSize:          config.GetIntEnv("FEED_SIZE", 50),
			FeedCacheDuration: config.GetDurationEnv("FEED_CACHE_DURATION", 15*time.Minute),
			TrendingDuration:  config.GetDurationEnv("TRENDING_DURATION", 24*time.Hour),
		},
	}
}
