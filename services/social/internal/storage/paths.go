// Package storage provides clean object storage path management for the social service.
//
// Architecture:
//   - StorageService: Manages bucket configuration and delegates path construction
//   - ObjectPathBuilder: Handles pure object key construction within buckets
//   - Clean separation between bucket management and object path logic
//
// Path Structure:
//   Bucket: social-attachments
//   Object: userID/filename.ext
//   URL: http://host/social-attachments/userID/filename.ext
package storage

import (
	"fmt"
	"path/filepath"
	"strings"
)

// StorageService provides clean separation between bucket management and object path construction
type StorageService struct {
	bucketName    string
	objectBuilder *ObjectPathBuilder
}

// ObjectPathBuilder handles object key construction within a bucket (no bucket concerns)
type ObjectPathBuilder struct{}

// NewStorageService creates a new storage service with clean separation of concerns
func NewStorageService(bucketName string) *StorageService {
	if bucketName == "" {
		bucketName = "social-attachments" // default bucket name
	}
	
	return &StorageService{
		bucketName:    bucketName,
		objectBuilder: &ObjectPathBuilder{},
	}
}

// StoragePathBuilder is an alias for StorageService to maintain API compatibility
type StoragePathBuilder = StorageService

// NewStoragePathBuilder creates a new storage service (legacy constructor name)
func NewStoragePathBuilder(bucketName string) *StoragePathBuilder {
	return NewStorageService(bucketName)
}

// Path constants for consistency
const (
	// Separators
	PathSeparator = "/"
	
	// Base paths
	AttachmentsBasePath = "social-attachments"
	UploadsBasePath     = "uploads"
	TempBasePath        = "temp"
	ProcessedBasePath   = "processed"
)

// GetBucketName returns the configured bucket name
func (s *StorageService) GetBucketName() string {
	return s.bucketName
}

// ObjectPathBuilder methods - handle only object keys within bucket

// UserAttachmentsPath returns the path for a user's attachments directory
func (o *ObjectPathBuilder) UserAttachmentsPath(userID string) string {
	return userID
}

// StorageService methods - delegate to ObjectPathBuilder

// UserAttachmentsPath returns the object key for a user's attachments directory
func (s *StorageService) UserAttachmentsPath(userID string) string {
	return s.objectBuilder.UserAttachmentsPath(userID)
}

// UserUploadsPath returns the object key for a user's uploads directory
func (o *ObjectPathBuilder) UserUploadsPath(userID string) string {
	return fmt.Sprintf("%s%s%s", userID, PathSeparator, UploadsBasePath)
}

func (s *StorageService) UserUploadsPath(userID string) string {
	return s.objectBuilder.UserUploadsPath(userID)
}

// UserTempPath returns the object key for a user's temp directory
func (o *ObjectPathBuilder) UserTempPath(userID string) string {
	return fmt.Sprintf("%s%s%s", userID, PathSeparator, TempBasePath)
}

func (s *StorageService) UserTempPath(userID string) string {
	return s.objectBuilder.UserTempPath(userID)
}

// AttachmentPath returns the object key for an attachment directory
func (o *ObjectPathBuilder) AttachmentPath(userID, attachmentID string) string {
	return fmt.Sprintf("%s%s%s", userID, PathSeparator, attachmentID)
}

func (s *StorageService) AttachmentPath(userID, attachmentID string) string {
	return s.objectBuilder.AttachmentPath(userID, attachmentID)
}

// AttachmentPathWithExtension returns the object key for an attachment with extension
func (o *ObjectPathBuilder) AttachmentPathWithExtension(userID, attachmentID, extension string) string {
	// Ensure extension starts with a dot
	if extension != "" && !strings.HasPrefix(extension, ".") {
		extension = "." + extension
	}
	return fmt.Sprintf("%s%s%s%s", userID, PathSeparator, attachmentID, extension)
}

func (s *StorageService) AttachmentPathWithExtension(userID, attachmentID, extension string) string {
	return s.objectBuilder.AttachmentPathWithExtension(userID, attachmentID, extension)
}

// AttachmentFileName returns just the filename for an attachment
func (o *ObjectPathBuilder) AttachmentFileName(attachmentID, extension string) string {
	// Ensure extension starts with a dot
	if extension != "" && !strings.HasPrefix(extension, ".") {
		extension = "." + extension
	}
	return fmt.Sprintf("%s%s", attachmentID, extension)
}

func (s *StorageService) AttachmentFileName(attachmentID, extension string) string {
	return s.objectBuilder.AttachmentFileName(attachmentID, extension)
}

// AttachmentFullPath returns the complete object key for an attachment
func (o *ObjectPathBuilder) AttachmentFullPath(userID, attachmentID, extension string) string {
	fileName := o.AttachmentFileName(attachmentID, extension)
	return fmt.Sprintf("%s%s%s", userID, PathSeparator, fileName)
}

func (s *StorageService) AttachmentFullPath(userID, attachmentID, extension string) string {
	return s.objectBuilder.AttachmentFullPath(userID, attachmentID, extension)
}

// UploadPath returns the object key for a specific upload
func (o *ObjectPathBuilder) UploadPath(userID, uploadID string) string {
	return fmt.Sprintf("%s%s%s%s%s", userID, PathSeparator, UploadsBasePath, PathSeparator, uploadID)
}

func (s *StorageService) UploadPath(userID, uploadID string) string {
	return s.objectBuilder.UploadPath(userID, uploadID)
}

// TempUploadPath returns the object key for a temporary upload
func (o *ObjectPathBuilder) TempUploadPath(userID, tempID string) string {
	return fmt.Sprintf("%s%s%s%s%s", userID, PathSeparator, TempBasePath, PathSeparator, tempID)
}

func (s *StorageService) TempUploadPath(userID, tempID string) string {
	return s.objectBuilder.TempUploadPath(userID, tempID)
}

// ProcessedAttachmentPath returns the object key for a processed attachment variant
func (o *ObjectPathBuilder) ProcessedAttachmentPath(userID, attachmentID, variant string) string {
	return fmt.Sprintf("%s%s%s%s%s%s%s", userID, PathSeparator, ProcessedBasePath, PathSeparator, attachmentID, PathSeparator, variant)
}

func (s *StorageService) ProcessedAttachmentPath(userID, attachmentID, variant string) string {
	return s.objectBuilder.ProcessedAttachmentPath(userID, attachmentID, variant)
}

// Thumbnail and preview paths
func (b *StoragePathBuilder) ThumbnailPath(userID, attachmentID string) string {
	return b.ProcessedAttachmentPath(userID, attachmentID, "thumbnail")
}

func (b *StoragePathBuilder) PreviewPath(userID, attachmentID string) string {
	return b.ProcessedAttachmentPath(userID, attachmentID, "preview")
}

// Prefix patterns for listing operations
// UserPrefix returns the object key prefix for all of a user's files
func (o *ObjectPathBuilder) UserPrefix(userID string) string {
	return fmt.Sprintf("%s%s", userID, PathSeparator)
}

func (s *StorageService) UserPrefix(userID string) string {
	return s.objectBuilder.UserPrefix(userID)
}

// AttachmentPrefix returns the object key prefix for a specific attachment
func (o *ObjectPathBuilder) AttachmentPrefix(userID, attachmentID string) string {
	return fmt.Sprintf("%s%s%s", userID, PathSeparator, attachmentID)
}

func (s *StorageService) AttachmentPrefix(userID, attachmentID string) string {
	return s.objectBuilder.AttachmentPrefix(userID, attachmentID)
}

// GlobalPrefix returns the object key prefix for all files (empty for clean structure)
func (o *ObjectPathBuilder) GlobalPrefix() string {
	return "" // No global prefix needed in clean structure
}

func (s *StorageService) GlobalPrefix() string {
	return s.objectBuilder.GlobalPrefix()
}

// Utility methods
func (b *StoragePathBuilder) JoinPath(parts ...string) string {
	cleanParts := make([]string, 0, len(parts))
	for _, part := range parts {
		if part != "" {
			cleanParts = append(cleanParts, strings.Trim(part, PathSeparator))
		}
	}
	return strings.Join(cleanParts, PathSeparator)
}

// ExtractUserID extracts user ID from object key paths like "{userID}/..."
func (o *ObjectPathBuilder) ExtractUserID(objectKey string) string {
	parts := strings.Split(objectKey, PathSeparator)
	if len(parts) >= 1 {
		return parts[0]
	}
	return ""
}

func (s *StorageService) ExtractUserID(objectKey string) string {
	return s.objectBuilder.ExtractUserID(objectKey)
}

// ExtractAttachmentID extracts attachment ID from object key paths like "{userID}/{attachmentID}.ext"
func (o *ObjectPathBuilder) ExtractAttachmentID(objectKey string) string {
	parts := strings.Split(objectKey, PathSeparator)
	if len(parts) >= 2 {
		fileName := parts[1]
		// Remove extension if present
		return strings.TrimSuffix(fileName, filepath.Ext(fileName))
	}
	return ""
}

func (s *StorageService) ExtractAttachmentID(objectKey string) string {
	return s.objectBuilder.ExtractAttachmentID(objectKey)
}

// Validation methods for ObjectPathBuilder
func (o *ObjectPathBuilder) IsUserPath(objectKey, userID string) bool {
	expectedPrefix := o.UserPrefix(userID)
	return strings.HasPrefix(objectKey, expectedPrefix)
}

// StorageService validation methods
func (s *StorageService) IsUserPath(objectKey, userID string) bool {
	return s.objectBuilder.IsUserPath(objectKey, userID)
}

// Legacy validation method for backward compatibility
func (s *StorageService) IsValidPath(path string) bool {
	// In the new clean approach, all paths are valid if they follow userID/filename pattern
	parts := strings.Split(path, PathSeparator)
	return len(parts) >= 2 && parts[0] != "" && parts[1] != ""
}