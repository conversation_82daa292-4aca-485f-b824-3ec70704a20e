package services

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/async"
	"github.com/swork-team/platform/pkg/cache"
	"github.com/swork-team/platform/pkg/clients"
	sharedModels "github.com/swork-team/platform/pkg/models"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/pkg/webhook"
	socialCache "github.com/swork-team/platform/services/social/internal/cache"
	"github.com/swork-team/platform/services/social/internal/config"
	socialErrors "github.com/swork-team/platform/services/social/internal/errors"
	"github.com/swork-team/platform/services/social/internal/models"
	"github.com/swork-team/platform/services/social/internal/repositories"
	"github.com/swork-team/platform/services/social/internal/storage"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SocialService struct {
	postRepo           *repositories.PostRepository
	config             *config.Config
	cacheManager       *cache.DirectCacheManager
	cacheKeyBuilder    *socialCache.CacheKeyBuilder
	storagePathBuilder *storage.StoragePathBuilder
	asyncJobQueue      *async.AsyncJobQueue
	webhookService     webhook.WebhookService
	userClient         clients.UserServiceClient
	teamClient         clients.TeamServiceClient
	batchPopulator     *utils.BatchPopulator
	minioClient        *minio.Client
}

type CreatePostRequest struct {
	Content       string                `json:"content" binding:"required"`
	Type          models.PostType       `json:"type"`
	Visibility    models.PostVisibility `json:"visibility"`
	TeamID        string                `json:"team_id,omitempty"`
	Tags          []string              `json:"tags,omitempty"`
	Mentions      []string              `json:"mentions,omitempty"`
	Location      *models.Location      `json:"location,omitempty"`
	AttachmentIDs []string              `json:"attachment_ids,omitempty"`
}

type UpdatePostRequest struct {
	Content       *string                `json:"content,omitempty"`
	Visibility    *models.PostVisibility `json:"visibility,omitempty"`
	Tags          *[]string              `json:"tags,omitempty"`
	Mentions      *[]string              `json:"mentions,omitempty"`
	Location      *models.Location       `json:"location,omitempty"`
	AttachmentIDs *[]string              `json:"attachment_ids,omitempty"`
}

type CreateCommentRequest struct {
	PostID        string   `json:"post_id" binding:"required"`
	ParentID      string   `json:"parent_id,omitempty"`
	Content       string   `json:"content" binding:"required"`
	AttachmentIDs []string `json:"attachment_ids,omitempty"`
}

type CreateShareRequest struct {
	PostID     string                `json:"post_id" binding:"required"`
	TeamID     string                `json:"team_id,omitempty"`
	Comment    string                `json:"comment,omitempty"`
	Visibility models.PostVisibility `json:"visibility"`
}

type SocialEvent struct {
	Type      string      `json:"type"`
	UserID    string      `json:"user_id"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

func NewSocialService(postRepo *repositories.PostRepository, config *config.Config, redisClient *redis.Client, cacheManager *cache.DirectCacheManager, asyncJobQueue *async.AsyncJobQueue, webhookService webhook.WebhookService) *SocialService {
	// Initialize standardized clients
	userClient := clients.NewUserServiceClient(config.Services.User, redisClient)
	teamClient := clients.NewTeamServiceClient(config.Services.Team, redisClient)

	// Initialize MinIO client for direct attachment storage
	minioClient := initializeMinIOClient()

	// Initialize storage path builder with bucket from config
	bucketName := getEnv("MINIO_BUCKET", "social-attachments")
	storagePathBuilder := storage.NewStoragePathBuilder(bucketName)

	return &SocialService{
		postRepo:           postRepo,
		config:             config,
		cacheManager:       cacheManager,
		cacheKeyBuilder:    socialCache.NewCacheKeyBuilder(),
		storagePathBuilder: storagePathBuilder,
		asyncJobQueue:      asyncJobQueue,
		webhookService:     webhookService,
		userClient:         userClient,
		teamClient:         teamClient,
		batchPopulator:     utils.NewBatchPopulator(userClient, teamClient),
		minioClient:        minioClient,
	}
}

// getEnv gets environment variable with default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// initializeMinIOClient creates and configures a MinIO client for attachment storage
func initializeMinIOClient() *minio.Client {
	// Get MinIO configuration from environment variables
	endpoint := getEnv("MINIO_ENDPOINT", "localhost:9000")
	accessKey := getEnv("MINIO_ACCESS_KEY", "minioadmin")
	secretKey := getEnv("MINIO_SECRET_KEY", "minioadmin")
	useSSL := getEnv("MINIO_USE_SSL", "false") == "true"

	// Create MinIO client
	client, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKey, secretKey, ""),
		Secure: useSSL,
	})
	if err != nil {
		fmt.Printf("Warning: Failed to initialize MinIO client: %v\n", err)
		return nil
	}

	// Ensure bucket exists
	ctx := context.Background()
	bucketName := getEnv("MINIO_BUCKET", "social-attachments")

	exists, err := client.BucketExists(ctx, bucketName)
	if err != nil {
		fmt.Printf("Warning: Failed to check MinIO bucket existence: %v\n", err)
		return client
	}

	if !exists {
		err = client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{})
		if err != nil {
			fmt.Printf("Warning: Failed to create MinIO bucket '%s': %v\n", bucketName, err)
		}
	}

	return client
}

// Post operations
func (s *SocialService) CreatePost(ctx context.Context, userID string, req *CreatePostRequest) (*models.Post, error) {
	// Parse userID to UUID
	userUUID, err := s.parseAndValidateUUID(userID, "user_id")
	if err != nil {
		return nil, err
	}

	// Validate content
	if strings.TrimSpace(req.Content) == "" {
		return nil, socialErrors.ErrEmptyContent
	}

	// Set defaults
	if req.Type == "" {
		req.Type = models.PostTypeText
	}

	// Validate post type
	if err := s.validatePostType(req.Type); err != nil {
		return nil, err
	}
	if req.Visibility == "" {
		req.Visibility = models.PostVisibilityPublic
	}

	// Validate team access if posting to team
	if req.TeamID != "" && req.Visibility == models.PostVisibilityTeam {
		if !s.validateTeamMembership(ctx, userID, req.TeamID) {
			return nil, socialErrors.NewAccessDeniedError("team", "post").WithDetails(map[string]interface{}{
				"team_id": req.TeamID,
				"user_id": userID,
			})
		}
	}

	// Parse TeamID if provided
	var teamUUID *uuid.UUID
	if req.TeamID != "" {
		parsedTeamID, err := uuid.Parse(req.TeamID)
		if err != nil {
			return nil, socialErrors.NewValidationError("team_id", "Invalid team ID format")
		}
		teamUUID = &parsedTeamID
	}

	// Parse and validate attachment IDs
	attachmentIDs, err := s.validateAndParseAttachments(ctx, req.AttachmentIDs, userID)
	if err != nil {
		return nil, err
	}

	// Create post
	post := &models.Post{
		AuthorID:      userUUID,
		TeamID:        teamUUID,
		Content:       req.Content,
		Type:          req.Type,
		Visibility:    req.Visibility,
		Tags:          req.Tags,
		Mentions:      req.Mentions,
		Location:      req.Location,
		AttachmentIDs: attachmentIDs,
	}

	if err := s.postRepo.CreatePost(ctx, post); err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "create_post",
			"error":     err.Error(),
		})
	}

	// Populate author info using batch populator
	posts := []models.Post{*post}
	s.populatePostsBatch(ctx, posts, userID)
	*post = posts[0]

	// Send notifications for mentions
	if len(req.Mentions) > 0 {
		go s.sendMentionNotifications(userID, req.Mentions, post.ID)
	}

	// Direct cache invalidation (immediate)
	s.cacheManager.InvalidateUser(ctx, userID)
	if post.TeamID != nil {
		s.cacheManager.InvalidateTeam(ctx, post.TeamID.String())
	}

	// Async notification (replace Kafka event)
	go s.handlePostCreated(post)

	return post, nil
}

func (s *SocialService) GetPost(ctx context.Context, postID string, userID string) (*models.Post, error) {
	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return nil, socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": postID})
	}

	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post",
			"post_id":   postID,
			"error":     err.Error(),
		})
	}
	if post == nil {
		return nil, socialErrors.NewNotFoundError("post", postID)
	}

	// Check if user can view this post
	// For unauthenticated users (userID == ""), only allow public posts
	if userID == "" {
		if !post.IsPublic() {
			return nil, socialErrors.NewAccessDeniedError("post", "view").WithDetails(map[string]interface{}{
				"post_id":    postID,
				"visibility": string(post.Visibility),
				"reason":     "authentication required for non-public posts",
			})
		}
	} else {
		// For authenticated users, use full permission check
		userTeams := s.getUserTeams(ctx, userID)
		if !post.CanView(userID, userTeams) {
			return nil, socialErrors.NewAccessDeniedError("post", "view").WithDetails(map[string]interface{}{
				"post_id":    postID,
				"user_id":    userID,
				"visibility": string(post.Visibility),
			})
		}
	}

	// Increment view count
	go s.postRepo.IncrementViewCount(context.Background(), objID)

	// Populate related data
	s.populatePostData(ctx, post, userID)

	return post, nil
}

func (s *SocialService) UpdatePost(ctx context.Context, postID string, userID string, req *UpdatePostRequest) (*models.Post, error) {
	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return nil, socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": postID})
	}

	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_update",
			"post_id":   postID,
			"error":     err.Error(),
		})
	}
	if post == nil {
		return nil, socialErrors.NewNotFoundError("post", postID)
	}

	// Check ownership
	if post.AuthorID.String() != userID {
		return nil, socialErrors.NewAccessDeniedError("post", "update").WithDetails(map[string]interface{}{
			"post_id":   postID,
			"user_id":   userID,
			"author_id": post.AuthorID.String(),
		})
	}

	// Update fields
	if req.Content != nil {
		if strings.TrimSpace(*req.Content) == "" {
			return nil, socialErrors.ErrEmptyContent
		}
		post.Content = *req.Content
	}
	if req.Visibility != nil {
		post.Visibility = *req.Visibility
	}
	if req.Tags != nil {
		post.Tags = *req.Tags
	}
	if req.Mentions != nil {
		post.Mentions = *req.Mentions
	}
	if req.Location != nil {
		post.Location = req.Location
	}
	if req.AttachmentIDs != nil {
		// Parse and validate attachment IDs
		attachmentIDs, err := s.validateAndParseAttachments(ctx, *req.AttachmentIDs, userID)
		if err != nil {
			return nil, err
		}
		post.AttachmentIDs = attachmentIDs
	}

	if err := s.postRepo.UpdatePost(ctx, post); err != nil {
		return nil, fmt.Errorf("failed to update post: %w", err)
	}

	// Populate related data
	s.populatePostData(ctx, post, userID)

	// Async notification for post update
	go s.handlePostUpdated(post)

	return post, nil
}

func (s *SocialService) DeletePost(ctx context.Context, postID string, userID string) error {
	objID, err := s.parseAndValidateObjectID(postID, "post_id")
	if err != nil {
		return err
	}

	// Parse user ID to UUID
	userUUID, err := s.parseAndValidateUUID(userID, "user_id")
	if err != nil {
		return err
	}

	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_delete",
			"post_id":   postID,
			"error":     err.Error(),
		})
	}
	if post == nil {
		return socialErrors.NewNotFoundError("post", postID)
	}

	// Check ownership
	if post.AuthorID != userUUID {
		return socialErrors.NewAccessDeniedError("post", "delete").WithDetails(map[string]interface{}{
			"post_id":   postID,
			"user_id":   userID,
			"author_id": post.AuthorID.String(),
		})
	}

	if err := s.postRepo.DeletePost(ctx, objID, userUUID); err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "delete_post",
			"post_id":   postID,
			"error":     err.Error(),
		})
	}

	// Async notification for post deletion
	go s.handlePostDeleted(post.ID.Hex(), post.AuthorID.String())

	return nil
}

// Feed operations
func (s *SocialService) GetUserFeed(ctx context.Context, userID string, request *models.PaginationRequest) ([]models.Post, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	// Parse userID to UUID
	userUUID, err := s.parseAndValidateUUID(userID, "user_id")
	if err != nil {
		return nil, false, err
	}

	// Get user's friends and teams
	friendIDs := s.getUserFriends(ctx, userUUID)
	teamIDs := s.getUserTeamsUUID(ctx, userUUID)

	posts, hasMore, err := s.postRepo.GetPersonalizedFeed(ctx, userUUID, friendIDs, teamIDs, request)
	if err != nil {
		return nil, false, fmt.Errorf("failed to get feed: %w", err)
	}

	// Populate related data for all posts using batch operations
	s.populatePostsBatch(ctx, posts, userID)

	return posts, hasMore, nil
}

func (s *SocialService) GetPublicFeed(ctx context.Context, request *models.PaginationRequest, userID string) ([]models.Post, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	posts, hasMore, err := s.postRepo.GetPublicFeed(ctx, request)
	if err != nil {
		// Check if it's a StandardError (e.g., cursor validation error)
		if _, ok := err.(*socialErrors.StandardError); ok {
			return nil, false, err
		}
		return nil, false, fmt.Errorf("failed to get public feed: %w", err)
	}

	// Populate related data for all posts using batch operations
	// Pass userID to populate user_like field if user is authenticated
	s.populatePostsBatch(ctx, posts, userID)

	return posts, hasMore, nil
}

func (s *SocialService) GetUserPosts(ctx context.Context, targetUserID string, viewerUserID string, request *models.PaginationRequest) ([]models.Post, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	// Parse target user ID to UUID
	targetUUID, err := uuid.Parse(targetUserID)
	if err != nil {
		return nil, false, socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": targetUserID})
	}

	posts, hasMore, err := s.postRepo.GetUserPosts(ctx, targetUUID, request)
	if err != nil {
		// Check if it's a StandardError (e.g., cursor validation error)
		if _, ok := err.(*socialErrors.StandardError); ok {
			return nil, false, err
		}
		return nil, false, fmt.Errorf("failed to get user posts: %w", err)
	}

	// Filter posts based on viewer's access
	var viewerTeams []string
	if viewerUserID != "" {
		if _, err := uuid.Parse(viewerUserID); err != nil {
			return nil, false, socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": viewerUserID})
		}
		viewerTeams = s.getUserTeams(ctx, viewerUserID)
	}
	filteredPosts := make([]models.Post, 0)

	for _, post := range posts {
		if post.CanView(viewerUserID, viewerTeams) {
			filteredPosts = append(filteredPosts, post)
		}
	}

	// Populate filtered posts in batch
	s.populatePostsBatch(ctx, filteredPosts, viewerUserID)

	return filteredPosts, hasMore, nil
}

func (s *SocialService) GetTeamPosts(ctx context.Context, teamID string, userID string, request *models.PaginationRequest) ([]models.Post, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	// Parse team ID to UUID
	teamUUID, err := uuid.Parse(teamID)
	if err != nil {
		return nil, false, socialErrors.NewValidationError("team_id", "Invalid team ID format")
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, false, socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	// Validate team membership
	if !s.validateTeamMembershipUUID(ctx, userUUID, teamUUID) {
		return nil, false, socialErrors.NewAccessDeniedError("team_posts", "view").WithDetails(map[string]interface{}{
			"team_id": teamID,
			"user_id": userID,
		})
	}

	posts, hasMore, err := s.postRepo.GetTeamPosts(ctx, teamUUID, request)
	if err != nil {
		return nil, false, fmt.Errorf("failed to get team posts: %w", err)
	}

	// Populate related data for all posts using batch operations
	s.populatePostsBatch(ctx, posts, userID)

	return posts, hasMore, nil
}

func (s *SocialService) SearchPosts(ctx context.Context, query string, userID string, request *models.PaginationRequest) ([]models.Post, bool, error) {
	if strings.TrimSpace(query) == "" {
		return nil, false, socialErrors.NewValidationError("q", "Search query cannot be empty")
	}

	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, false, socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	// Get user's friends and teams for access control
	friendIDs := s.getUserFriends(ctx, userUUID)
	teamIDs := s.getUserTeamsUUID(ctx, userUUID)

	posts, hasMore, err := s.postRepo.SearchPosts(ctx, query, userUUID, friendIDs, teamIDs, request)
	if err != nil {
		return nil, false, fmt.Errorf("failed to search posts: %w", err)
	}

	// Populate related data for all posts using batch operations
	s.populatePostsBatch(ctx, posts, userID)

	return posts, hasMore, nil
}

func (s *SocialService) GetTrendingPosts(ctx context.Context, request *models.PaginationRequest, userID string) ([]models.Post, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	// Override limit to 50 max for trending posts
	if request.Limit > 50 {
		request.Limit = 50
	}
	if request.Limit <= 0 {
		request.Limit = 10
	}

	since := time.Now().Add(-s.config.Feed.TrendingDuration)
	posts, hasMore, err := s.postRepo.GetTrendingPosts(ctx, since, request)
	if err != nil {
		// Check if it's a StandardError (e.g., cursor validation error)
		if _, ok := err.(*socialErrors.StandardError); ok {
			return nil, false, err
		}
		return nil, false, fmt.Errorf("failed to get trending posts: %w", err)
	}

	// Populate related data for all posts using batch operations
	// Pass userID to populate user_like field if user is authenticated
	s.populatePostsBatch(ctx, posts, userID)

	return posts, hasMore, nil
}

// Helper methods

// populatePostData populates user and team data for a single post (wrapper around batch method)
func (s *SocialService) populatePostData(ctx context.Context, post *models.Post, userID string) {
	if post == nil {
		return
	}

	posts := []models.Post{*post}
	s.populatePostsBatch(ctx, posts, userID)

	// Copy the populated data back to the original post
	if len(posts) > 0 {
		*post = posts[0]
	}
}

// populatePostsBatch efficiently populates user and team data for multiple posts
func (s *SocialService) populatePostsBatch(ctx context.Context, posts []models.Post, userID string) {
	if len(posts) == 0 {
		return
	}

	// Extract all user, team, and attachment IDs
	var authorIDs []string
	var teamIDs []string
	var allAttachmentIDs []uuid.UUID

	for _, post := range posts {
		authorIDs = append(authorIDs, post.AuthorID.String())
		if post.TeamID != nil {
			teamIDs = append(teamIDs, post.TeamID.String())
		}
		allAttachmentIDs = append(allAttachmentIDs, post.AttachmentIDs...)
	}

	// Fetch users and teams in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, authorIDs)
	if err != nil {
		fmt.Printf("Failed to batch populate users: %v\n", err)
		return
	}

	teamMap, err := s.batchPopulator.PopulateTeams(ctx, teamIDs)
	if err != nil {
		fmt.Printf("Failed to batch populate teams: %v\n", err)
		// Continue even if team population fails
	}

	// Fetch attachments in batch from MinIO storage
	var attachmentMap map[string]interface{}
	if len(allAttachmentIDs) > 0 {
		attachments, err := s.GetAttachmentsByIDs(ctx, allAttachmentIDs)
		if err != nil {
			fmt.Printf("Failed to batch populate attachments from MinIO storage: %v\n", err)
			// Initialize empty map to prevent nil pointer issues
			attachmentMap = make(map[string]interface{})
		} else {
			attachmentMap = make(map[string]interface{})
			for _, attachment := range attachments {
				attachmentMap[attachment.ID.String()] = attachment
			}
		}
	}

	// Populate posts with the fetched data
	for i := range posts {
		// Set author
		if author, exists := userMap[posts[i].AuthorID.String()]; exists {
			posts[i].Author = author
		}

		// Set team if applicable
		if posts[i].TeamID != nil {
			if team, exists := teamMap[posts[i].TeamID.String()]; exists {
				posts[i].Team = team
			}
		}

		// Set attachments
		if len(posts[i].AttachmentIDs) > 0 && attachmentMap != nil {
			posts[i].Attachments = make([]sharedModels.AttachmentBasic, 0, len(posts[i].AttachmentIDs))
			for _, attachmentID := range posts[i].AttachmentIDs {
				if attachment, exists := attachmentMap[attachmentID.String()]; exists {
					if attachmentBasic, ok := attachment.(sharedModels.AttachmentBasic); ok {
						posts[i].Attachments = append(posts[i].Attachments, attachmentBasic)
					}
				}
			}
		}

		// Populate user's like if logged in
		if userID != "" {
			if userUUID, err := uuid.Parse(userID); err == nil {
				if like, _ := s.postRepo.GetUserLike(ctx, userUUID, posts[i].ID, primitive.NilObjectID); like != nil {
					posts[i].UserLike = like
				}
			}
		}
	}
}

// populateCommentsBatch efficiently populates user data for multiple comments
func (s *SocialService) populateCommentsBatch(ctx context.Context, comments []models.Comment, userID string) []models.Comment {
	if len(comments) == 0 {
		return comments
	}

	// Extract all author IDs and attachment IDs
	var authorIDs []string
	var allAttachmentIDs []uuid.UUID
	for _, comment := range comments {
		authorIDs = append(authorIDs, comment.AuthorID.String())
		allAttachmentIDs = append(allAttachmentIDs, comment.AttachmentIDs...)
	}

	// Fetch users in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, authorIDs)
	if err != nil {
		fmt.Printf("Failed to batch populate users for comments: %v\n", err)
		return comments
	}

	// Fetch attachments in batch from MinIO storage
	var attachmentMap map[string]interface{}
	if len(allAttachmentIDs) > 0 {
		attachments, err := s.GetAttachmentsByIDs(ctx, allAttachmentIDs)
		if err != nil {
			fmt.Printf("Failed to batch populate attachments for comments from MinIO storage: %v\n", err)
		} else {
			attachmentMap = make(map[string]interface{})
			for _, attachment := range attachments {
				attachmentMap[attachment.ID.String()] = attachment
			}
		}
	}

	// Populate comments with the fetched data
	for i := range comments {
		// Set author
		if author, exists := userMap[comments[i].AuthorID.String()]; exists {
			comments[i].Author = author
		}

		// Set attachments
		if len(comments[i].AttachmentIDs) > 0 && attachmentMap != nil {
			comments[i].Attachments = make([]sharedModels.AttachmentBasic, 0, len(comments[i].AttachmentIDs))
			for _, attachmentID := range comments[i].AttachmentIDs {
				if attachment, exists := attachmentMap[attachmentID.String()]; exists {
					if attachmentBasic, ok := attachment.(sharedModels.AttachmentBasic); ok {
						comments[i].Attachments = append(comments[i].Attachments, attachmentBasic)
					}
				}
			}
		}

		// Populate user's like if logged in
		if userID != "" {
			if userUUID, err := uuid.Parse(userID); err == nil {
				if like, _ := s.postRepo.GetUserLike(ctx, userUUID, primitive.NilObjectID, comments[i].ID); like != nil {
					comments[i].UserLike = like
				}
			}
		}

		// Populate reaction statistics
		if stats, err := s.postRepo.GetCommentReactionStats(ctx, comments[i].ID); err == nil && stats != nil {
			// Populate user information for reaction users
			if len(stats.Users) > 0 {
				stats.Users = s.populateReactionUsersBatch(ctx, stats.Users)
			}
			comments[i].ReactionStats = stats
		}
	}

	return comments
}

// populateCommentsWithNestedReplies recursively populates replies for comments
func (s *SocialService) populateCommentsWithNestedReplies(ctx context.Context, comments []models.Comment, userID string, maxDepth int) []models.Comment {
	if len(comments) == 0 || maxDepth <= 0 {
		return comments
	}

	// For each comment, fetch its replies
	for i := range comments {
		repliesRequest := &models.PaginationRequest{
			Limit:     100,
			Direction: models.PaginationDirectionForward,
		}
		replies, _, err := s.postRepo.GetCommentReplies(ctx, comments[i].ID, repliesRequest) // Get all replies (up to 100)
		if err != nil {
			fmt.Printf("Failed to get replies for comment %s: %v\n", comments[i].ID.Hex(), err)
			continue
		}

		if len(replies) > 0 {
			// Populate the replies with user data and reaction stats
			replies = s.populateCommentsBatch(ctx, replies, userID)

			// Recursively populate nested replies (reduce depth by 1)
			replies = s.populateCommentsWithNestedReplies(ctx, replies, userID, maxDepth-1)

			comments[i].Replies = replies
		}
	}

	return comments
}

// populateLikesBatch efficiently populates user data for multiple likes
func (s *SocialService) populateLikesBatch(ctx context.Context, likes []models.Like) []models.Like {
	if len(likes) == 0 {
		return likes
	}

	// Extract all user IDs
	var userIDs []string
	for _, like := range likes {
		userIDs = append(userIDs, like.UserID.String())
	}

	// Fetch users in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, userIDs)
	if err != nil {
		fmt.Printf("Failed to batch populate users for likes: %v\n", err)
		return likes
	}

	// Populate likes with user data
	for i := range likes {
		if user, exists := userMap[likes[i].UserID.String()]; exists {
			likes[i].User = user.ToBasic()
		}
	}

	return likes
}

// populateReactionUsersBatch efficiently populates user data for reaction stats
func (s *SocialService) populateReactionUsersBatch(ctx context.Context, reactionUsers []models.ReactionUser) []models.ReactionUser {
	if len(reactionUsers) == 0 {
		return reactionUsers
	}

	// Extract all user IDs
	var userIDs []string
	for _, reactionUser := range reactionUsers {
		userIDs = append(userIDs, reactionUser.UserID.String())
	}

	// Fetch users in batch
	userMap, err := s.batchPopulator.PopulateUsers(ctx, userIDs)
	if err != nil {
		fmt.Printf("Failed to batch populate users for reaction stats: %v\n", err)
		return reactionUsers
	}

	// Populate reaction users with user data
	for i := range reactionUsers {
		if user, exists := userMap[reactionUsers[i].UserID.String()]; exists {
			reactionUsers[i].User = user.ToBasic()
		}
	}

	return reactionUsers
}

func (s *SocialService) getUserFriends(ctx context.Context, userID uuid.UUID) []uuid.UUID {
	// This would typically call the user service to get friends
	// For now, return empty slice
	return []uuid.UUID{}
}

// getUserTeams returns team IDs for a user (string version for API compatibility)
func (s *SocialService) getUserTeams(ctx context.Context, userID string) []string {
	if userID == "" {
		fmt.Printf("getUserTeams: empty user ID provided\n")
		return []string{}
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		fmt.Printf("getUserTeams: invalid user ID format %s: %v\n", userID, err)
		return []string{}
	}

	teamUUIDs := s.getUserTeamsUUID(ctx, userUUID)
	teamStrings := make([]string, len(teamUUIDs))
	for i, teamUUID := range teamUUIDs {
		teamStrings[i] = teamUUID.String()
	}
	return teamStrings
}

// getUserTeamsUUID returns team IDs for a user (UUID version for internal use)
func (s *SocialService) getUserTeamsUUID(ctx context.Context, userID uuid.UUID) []uuid.UUID {
	teams, err := s.teamClient.GetUserTeams(ctx, userID.String())
	if err != nil {
		fmt.Printf("Failed to get user teams for %s: %v\n", userID, err)
		return []uuid.UUID{}
	}

	teamIDs := make([]uuid.UUID, len(teams))
	for i, team := range teams {
		teamIDs[i] = team.ID
	}
	return teamIDs
}

// validateTeamMembership checks if user is member of team (string version for API compatibility)
func (s *SocialService) validateTeamMembership(ctx context.Context, userID string, teamID string) bool {
	if userID == "" {
		fmt.Printf("validateTeamMembership: empty user ID provided\n")
		return false
	}
	if teamID == "" {
		fmt.Printf("validateTeamMembership: empty team ID provided\n")
		return false
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		fmt.Printf("validateTeamMembership: invalid user ID format %s: %v\n", userID, err)
		return false
	}

	teamUUID, err := uuid.Parse(teamID)
	if err != nil {
		fmt.Printf("validateTeamMembership: invalid team ID format %s: %v\n", teamID, err)
		return false
	}

	return s.validateTeamMembershipUUID(ctx, userUUID, teamUUID)
}

// validateTeamMembershipUUID checks if user is member of team (UUID version for internal use)
func (s *SocialService) validateTeamMembershipUUID(ctx context.Context, userID uuid.UUID, teamID uuid.UUID) bool {
	members, err := s.teamClient.GetTeamMembers(ctx, teamID.String())
	if err != nil {
		fmt.Printf("Failed to get team members for %s: %v\n", teamID, err)
		return false
	}

	for _, member := range members {
		if member.UserID == userID {
			return true
		}
	}
	return false
}

func (s *SocialService) sendMentionNotifications(userID string, mentions []string, postID primitive.ObjectID) {
	// This would typically call the notification service
	// For now, just log
	fmt.Printf("Sending mention notifications from %s to %v for post %s\n", userID, mentions, postID.Hex())
}

// Social notification methods (replace Kafka events)
func (s *SocialService) handlePostCreated(post *models.Post) {
	// Enqueue async job for notifications
	job := &async.AsyncJob{
		Type:   "post_created",
		UserID: post.AuthorID.String(),
		Data: map[string]interface{}{
			"post_id":    post.ID.Hex(),
			"author_id":  post.AuthorID.String(),
			"content":    post.Content,
			"type":       string(post.Type),
			"visibility": string(post.Visibility),
			"timestamp":  time.Now().Format(time.RFC3339),
		},
	}

	if post.TeamID != nil {
		job.Data["team_id"] = post.TeamID.String()
	}

	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue post creation notification: %v\n", err)
	}

	// Send webhook for external integrations
	webhookReq := &webhook.WebhookRequest{
		Event:  "post_created",
		UserID: post.AuthorID.String(),
		Data: map[string]interface{}{
			"post_id":   post.ID.Hex(),
			"author_id": post.AuthorID.String(),
			"content":   post.Content,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	if err := s.webhookService.SendWebhook(webhookReq); err != nil {
		fmt.Printf("Failed to send post creation webhook: %v\n", err)
	}
}

func (s *SocialService) handlePostUpdated(post *models.Post) {
	// Similar to handlePostCreated but for updates
	job := &async.AsyncJob{
		Type:   "post_updated",
		UserID: post.AuthorID.String(),
		Data: map[string]interface{}{
			"post_id":   post.ID.Hex(),
			"author_id": post.AuthorID.String(),
			"content":   post.Content,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue post update notification: %v\n", err)
	}
}

func (s *SocialService) handlePostDeleted(postID string, authorID string) {
	job := &async.AsyncJob{
		Type:   "post_deleted",
		UserID: authorID,
		Data: map[string]interface{}{
			"post_id":   postID,
			"author_id": authorID,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue post deletion notification: %v\n", err)
	}
}

// GetPostLikes returns a list of users who reacted to a specific post
func (s *SocialService) GetPostLikes(ctx context.Context, postID string, userID string, request *models.PaginationRequest) ([]models.Like, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	// Get users who reacted to this post
	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return nil, false, errors.New("invalid post ID")
	}

	// Check if post exists and user can view it
	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return nil, false, fmt.Errorf("post not found: %w", err)
	}

	// Check permission to view the post (allow public posts for unauthenticated users)
	if userID != "" {
		userTeams := s.getUserTeams(ctx, userID)
		if !post.CanView(userID, userTeams) {
			return nil, false, errors.New("access denied")
		}
	} else {
		// For unauthenticated users, only allow public posts
		if !post.IsPublic() {
			return nil, false, errors.New("access denied")
		}
	}

	likes, hasMore, err := s.postRepo.GetPostLikes(ctx, objID, request)
	if err != nil {
		return nil, false, fmt.Errorf("failed to get post likes: %w", err)
	}

	// Populate user information for likes
	populatedLikes := s.populateLikesBatch(ctx, likes)

	return populatedLikes, hasMore, nil
}

// GetCommentLikes returns a list of users who reacted to a specific comment
func (s *SocialService) GetCommentLikes(ctx context.Context, commentID string, userID string, request *models.PaginationRequest) ([]models.Like, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	objID, err := primitive.ObjectIDFromHex(commentID)
	if err != nil {
		return nil, false, errors.New("invalid comment ID")
	}

	// Check if comment exists
	comment, err := s.postRepo.GetCommentByID(ctx, objID)
	if err != nil {
		return nil, false, fmt.Errorf("comment not found: %w", err)
	}

	// Check if user can view the parent post
	post, err := s.postRepo.GetPostByID(ctx, comment.PostID)
	if err != nil {
		return nil, false, fmt.Errorf("parent post not found: %w", err)
	}

	// Check permission to view the post (allow public posts for unauthenticated users)
	if userID != "" {
		userTeams := s.getUserTeams(ctx, userID)
		if !post.CanView(userID, userTeams) {
			return nil, false, errors.New("access denied")
		}
	} else {
		// For unauthenticated users, only allow public posts
		if !post.IsPublic() {
			return nil, false, errors.New("access denied")
		}
	}

	likes, hasMore, err := s.postRepo.GetCommentLikes(ctx, objID, request)
	if err != nil {
		return nil, false, fmt.Errorf("failed to get comment likes: %w", err)
	}

	// Populate user information for likes
	populatedLikes := s.populateLikesBatch(ctx, likes)

	return populatedLikes, hasMore, nil
}

// GetPostReactionStats returns aggregated reaction statistics for a post
func (s *SocialService) GetPostReactionStats(ctx context.Context, postID string, userID string) (*models.ReactionStats, error) {
	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return nil, errors.New("invalid post ID")
	}

	// Check if post exists and user can view it
	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return nil, fmt.Errorf("post not found: %w", err)
	}

	// Check permission to view the post (allow public posts for unauthenticated users)
	if userID != "" {
		userTeams := s.getUserTeams(ctx, userID)
		if !post.CanView(userID, userTeams) {
			return nil, errors.New("access denied")
		}
	} else {
		// For unauthenticated users, only allow public posts
		if !post.IsPublic() {
			return nil, errors.New("access denied")
		}
	}

	stats, err := s.postRepo.GetPostReactionStats(ctx, objID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post reaction stats: %w", err)
	}

	// Populate user information for reaction users
	if stats != nil && len(stats.Users) > 0 {
		stats.Users = s.populateReactionUsersBatch(ctx, stats.Users)
	}

	return stats, nil
}

// GetCommentReactionStats returns aggregated reaction statistics for a comment
func (s *SocialService) GetCommentReactionStats(ctx context.Context, commentID string, userID string) (*models.ReactionStats, error) {
	objID, err := primitive.ObjectIDFromHex(commentID)
	if err != nil {
		return nil, errors.New("invalid comment ID")
	}

	// Check if comment exists
	comment, err := s.postRepo.GetCommentByID(ctx, objID)
	if err != nil {
		return nil, fmt.Errorf("comment not found: %w", err)
	}

	// Check if user can view the parent post
	post, err := s.postRepo.GetPostByID(ctx, comment.PostID)
	if err != nil {
		return nil, fmt.Errorf("parent post not found: %w", err)
	}

	// Check permission to view the post (allow public posts for unauthenticated users)
	if userID != "" {
		userTeams := s.getUserTeams(ctx, userID)
		if !post.CanView(userID, userTeams) {
			return nil, errors.New("access denied")
		}
	} else {
		// For unauthenticated users, only allow public posts
		if !post.IsPublic() {
			return nil, errors.New("access denied")
		}
	}

	stats, err := s.postRepo.GetCommentReactionStats(ctx, objID)
	if err != nil {
		return nil, fmt.Errorf("failed to get comment reaction stats: %w", err)
	}

	// Populate user information for reaction users
	if stats != nil && len(stats.Users) > 0 {
		stats.Users = s.populateReactionUsersBatch(ctx, stats.Users)
	}

	return stats, nil
}

func (s *SocialService) Close() error {
	// Close webhook service
	if s.webhookService != nil {
		return s.webhookService.Close()
	}
	return nil
}

// Additional async notification handlers for interaction events
func (s *SocialService) handleCommentCreated(comment *models.Comment, userID string) {
	job := &async.AsyncJob{
		Type:   "comment_created",
		UserID: userID,
		Data: map[string]interface{}{
			"comment_id": comment.ID.Hex(),
			"post_id":    comment.PostID.Hex(),
			"author_id":  comment.AuthorID.String(),
			"content":    comment.Content,
			"timestamp":  time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue comment creation notification: %v\n", err)
	}
}

func (s *SocialService) handleCommentUpdated(comment *models.Comment, userID string) {
	job := &async.AsyncJob{
		Type:   "comment_updated",
		UserID: userID,
		Data: map[string]interface{}{
			"comment_id": comment.ID.Hex(),
			"post_id":    comment.PostID.Hex(),
			"timestamp":  time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue comment update notification: %v\n", err)
	}
}

func (s *SocialService) handleCommentDeleted(commentID, postID, userID string) {
	job := &async.AsyncJob{
		Type:   "comment_deleted",
		UserID: userID,
		Data: map[string]interface{}{
			"comment_id": commentID,
			"post_id":    postID,
			"timestamp":  time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue comment deletion notification: %v\n", err)
	}
}

func (s *SocialService) handlePostLiked(postID, userID, authorID, likeType string) {
	job := &async.AsyncJob{
		Type:   "post_liked",
		UserID: userID,
		Data: map[string]interface{}{
			"post_id":   postID,
			"user_id":   userID,
			"author_id": authorID,
			"like_type": likeType,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue post like notification: %v\n", err)
	}
}

func (s *SocialService) handlePostUnliked(postID, userID, authorID string) {
	job := &async.AsyncJob{
		Type:   "post_unliked",
		UserID: userID,
		Data: map[string]interface{}{
			"post_id":   postID,
			"user_id":   userID,
			"author_id": authorID,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue post unlike notification: %v\n", err)
	}
}

func (s *SocialService) handleCommentLiked(commentID, postID, userID, authorID, likeType string) {
	job := &async.AsyncJob{
		Type:   "comment_liked",
		UserID: userID,
		Data: map[string]interface{}{
			"comment_id": commentID,
			"post_id":    postID,
			"user_id":    userID,
			"author_id":  authorID,
			"like_type":  likeType,
			"timestamp":  time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue comment like notification: %v\n", err)
	}
}

func (s *SocialService) handleCommentUnliked(commentID, postID, userID, authorID string) {
	job := &async.AsyncJob{
		Type:   "comment_unliked",
		UserID: userID,
		Data: map[string]interface{}{
			"comment_id": commentID,
			"post_id":    postID,
			"user_id":    userID,
			"author_id":  authorID,
			"timestamp":  time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue comment unlike notification: %v\n", err)
	}
}

func (s *SocialService) handlePostShared(postID, userID, authorID string) {
	job := &async.AsyncJob{
		Type:   "post_shared",
		UserID: userID,
		Data: map[string]interface{}{
			"post_id":   postID,
			"user_id":   userID,
			"author_id": authorID,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue post share notification: %v\n", err)
	}
}

func (s *SocialService) handlePostUnshared(postID, userID, authorID string) {
	job := &async.AsyncJob{
		Type:   "post_unshared",
		UserID: userID,
		Data: map[string]interface{}{
			"post_id":   postID,
			"user_id":   userID,
			"author_id": authorID,
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}
	if err := s.asyncJobQueue.EnqueueJob(context.Background(), job); err != nil {
		fmt.Printf("Failed to enqueue post unshare notification: %v\n", err)
	}
}

// Attachment operations

func (s *SocialService) GetAttachment(ctx context.Context, attachmentID string, userID string) (*sharedModels.AttachmentBasic, error) {
	// Ensure MinIO client is available
	if s.minioClient == nil {
		return nil, errors.New("MinIO client not initialized")
	}

	// Parse attachment ID as UUID
	uuidID, err := uuid.Parse(attachmentID)
	if err != nil {
		return nil, errors.New("invalid attachment ID")
	}

	// Search for the attachment in MinIO
	bucketName := getEnv("MINIO_BUCKET", "social-attachments")

	// Since we store files with the pattern: {userID}/{attachmentID}.ext
	// We need to search for files that start with the attachment ID
	prefix := s.storagePathBuilder.AttachmentPrefix(userID, uuidID.String())

	// List objects with the prefix
	opts := minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	}

	var foundObject *minio.ObjectInfo
	for object := range s.minioClient.ListObjects(ctx, bucketName, opts) {
		if object.Err != nil {
			// Check if this is a "no such key" error or similar - treat as not found
			errStr := object.Err.Error()
			if strings.Contains(errStr, "NoSuchKey") || strings.Contains(errStr, "not found") || strings.Contains(errStr, "NoSuchBucket") {
				return nil, socialErrors.NewNotFoundError("attachment", attachmentID)
			}
			return nil, socialErrors.ErrInternalServer.WithDetails(map[string]interface{}{
				"error":         "Failed to list storage objects",
				"attachment_id": attachmentID,
			})
		}
		foundObject = &object
		break // Take the first match
	}

	if foundObject == nil {
		return nil, socialErrors.NewNotFoundError("attachment", attachmentID)
	}

	// Get object metadata
	objInfo, err := s.minioClient.StatObject(ctx, bucketName, foundObject.Key, minio.StatObjectOptions{})
	if err != nil {
		// Check if this is a "no such key" error - treat as not found
		errStr := err.Error()
		if strings.Contains(errStr, "NoSuchKey") || strings.Contains(errStr, "not found") {
			return nil, socialErrors.NewNotFoundError("attachment", attachmentID)
		}
		return nil, socialErrors.ErrInternalServer.WithDetails(map[string]interface{}{
			"error":         "Failed to get object metadata",
			"attachment_id": attachmentID,
		})
	}

	// Extract filename from metadata or derive from path
	filename := objInfo.UserMetadata["filename"]
	if filename == "" {
		filename = filepath.Base(foundObject.Key)
	}

	// Generate download URL
	downloadURL := s.generatePublicDownloadURL(bucketName, foundObject.Key)

	// Determine attachment type
	contentType := objInfo.ContentType
	if contentType == "" {
		contentType = s.detectContentType(filename)
	}

	// Create attachment response
	attachment := &sharedModels.AttachmentBasic{
		ID:       uuidID,
		Type:     s.determineAttachmentType(contentType, filepath.Ext(filename)),
		Name:     filename,
		URL:      downloadURL,
		Size:     objInfo.Size,
		MimeType: contentType,
	}

	return attachment, nil
}

func (s *SocialService) UploadAttachment(ctx context.Context, userID string, ginCtx interface{}) (*sharedModels.AttachmentBasic, error) {
	// Ensure MinIO client is available
	if s.minioClient == nil {
		return nil, errors.New("MinIO client not initialized")
	}

	// Cast gin context
	c, ok := ginCtx.(*gin.Context)
	if !ok {
		return nil, errors.New("invalid gin context")
	}

	// Parse multipart form
	err := c.Request.ParseMultipartForm(32 << 20) // 32 MB
	if err != nil {
		return nil, fmt.Errorf("failed to parse multipart form: %w", err)
	}

	// Get file from form
	file, fileHeader, err := c.Request.FormFile("file")
	if err != nil {
		return nil, fmt.Errorf("failed to get file from form: %w", err)
	}
	defer file.Close()

	// Get optional description
	description := c.Request.FormValue("description")

	// Validate file
	if err := s.validateUploadedFile(fileHeader); err != nil {
		return nil, err
	}

	// Generate unique file ID and path
	attachmentID := uuid.New()
	fileExt := filepath.Ext(fileHeader.Filename)
	filePath := s.storagePathBuilder.AttachmentFullPath(userID, attachmentID.String(), fileExt)

	// Determine content type
	contentType := fileHeader.Header.Get("Content-Type")
	if contentType == "" {
		contentType = s.detectContentType(fileHeader.Filename)
	}

	// Calculate file hash for integrity checking
	hasher := md5.New()
	fileContent, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}
	hasher.Write(fileContent)
	checksum := fmt.Sprintf("%x", hasher.Sum(nil))

	// Upload to MinIO
	bucketName := getEnv("MINIO_BUCKET", "social-attachments")
	fileReader := strings.NewReader(string(fileContent))

	uploadInfo, err := s.minioClient.PutObject(ctx, bucketName, filePath, fileReader, int64(len(fileContent)), minio.PutObjectOptions{
		ContentType: contentType,
		UserMetadata: map[string]string{
			"user-id":     userID,
			"filename":    fileHeader.Filename,
			"description": description,
			"checksum":    checksum,
			"upload-time": time.Now().UTC().Format(time.RFC3339),
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to upload file to MinIO: %w", err)
	}

	// Generate download URL using external endpoint for browser accessibility
	downloadURL := s.generatePublicDownloadURL(bucketName, filePath)

	// Create attachment response
	attachment := &sharedModels.AttachmentBasic{
		ID:       attachmentID,
		Type:     s.determineAttachmentType(contentType, fileExt),
		Name:     fileHeader.Filename,
		URL:      downloadURL,
		Size:     uploadInfo.Size,
		MimeType: contentType,
	}

	return attachment, nil
}

// generatePublicDownloadURL creates a publicly accessible URL for MinIO objects
func (s *SocialService) generatePublicDownloadURL(bucketName, filePath string) string {
	// Validate inputs
	if bucketName == "" || filePath == "" {
		return ""
	}

	// Clean file path to prevent duplication
	cleanFilePath := strings.TrimPrefix(filePath, bucketName+"/")
	cleanFilePath = strings.TrimPrefix(cleanFilePath, "/")

	// Option 1: Use explicit public URL if configured (recommended for production)
	if publicURL := getEnv("MINIO_PUBLIC_URL", ""); publicURL != "" {
		return fmt.Sprintf("%s/%s/%s", strings.TrimSuffix(publicURL, "/"), bucketName, cleanFilePath)
	}

	// Option 2: Build URL from external endpoint and SSL settings
	externalEndpoint := getEnv("MINIO_EXTERNAL_ENDPOINT", "localhost:9000")
	useSSL := getEnv("MINIO_USE_SSL", "false") == "true"

	// Determine protocol
	protocol := "http"
	if useSSL {
		protocol = "https"
	}

	// Option 3: Direct URL (fastest, but requires public bucket)
	directURL := fmt.Sprintf("%s://%s/%s/%s", protocol, externalEndpoint, bucketName, cleanFilePath)

	// Option 4: Generate presigned URL (secure, works with private buckets)
	// For production, you might want to use presigned URLs for better security
	if getEnv("APP_ENV", "development") == "production" {
		if presignedURL, err := s.generatePresignedURL(bucketName, cleanFilePath); err == nil {
			return presignedURL
		}
		// Fall back to direct URL if presigned fails
	}

	return directURL
}

// generatePresignedURL creates a time-limited presigned URL for secure access
func (s *SocialService) generatePresignedURL(bucketName, filePath string) (string, error) {
	if s.minioClient == nil {
		return "", errors.New("MinIO client not initialized")
	}

	// Generate presigned URL valid for 24 hours
	expiry := time.Hour * 24

	presignedURL, err := s.minioClient.PresignedGetObject(
		context.Background(),
		bucketName,
		filePath,
		expiry,
		nil,
	)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	// Replace internal hostname with external hostname in presigned URL
	externalEndpoint := getEnv("MINIO_EXTERNAL_ENDPOINT", "localhost:9000")
	internalEndpoint := getEnv("MINIO_ENDPOINT", "localhost:9000")

	if internalEndpoint != externalEndpoint {
		urlStr := presignedURL.String()
		urlStr = strings.Replace(urlStr, internalEndpoint, externalEndpoint, 1)
		if newURL, err := url.Parse(urlStr); err == nil {
			return newURL.String(), nil
		}
	}

	return presignedURL.String(), nil
}

// GetAttachments returns a list of attachments based on filters
func (s *SocialService) GetAttachments(ctx context.Context, params map[string]interface{}, userID string) ([]*sharedModels.AttachmentBasic, error) {
	// Ensure MinIO client is available
	if s.minioClient == nil {
		return nil, errors.New("MinIO client not initialized")
	}

	bucketName := getEnv("MINIO_BUCKET", "social-attachments")
	var prefix string

	// Determine prefix based on filters
	if _, exists := params["post_id"]; exists {
		// For post attachments, we need to query the database first to get attachment IDs
		// This is a simplified implementation - in production you'd query the posts collection
		prefix = s.storagePathBuilder.UserPrefix(userID)
	} else if userIDParam, exists := params["user_id"]; exists {
		prefix = s.storagePathBuilder.UserPrefix(userIDParam.(string))
	} else if _, exists := params["team_id"]; exists {
		// For team attachments, we'd need to query across team members
		// This is a simplified implementation
		prefix = s.storagePathBuilder.UserPrefix(userID)
	} else {
		// Default to user's own attachments
		prefix = s.storagePathBuilder.UserPrefix(userID)
	}

	// List objects with the prefix
	opts := minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	}

	var attachments []*sharedModels.AttachmentBasic
	limit := 20 // default limit

	if limitParam, exists := params["limit"]; exists {
		if l, ok := limitParam.(int); ok && l > 0 && l <= 100 {
			limit = l
		}
	}

	for object := range s.minioClient.ListObjects(ctx, bucketName, opts) {
		if object.Err != nil {
			return nil, fmt.Errorf("failed to list objects: %w", object.Err)
		}

		// Break if we've reached the limit
		if len(attachments) >= limit {
			break
		}

		// Get object metadata
		objInfo, err := s.minioClient.StatObject(ctx, bucketName, object.Key, minio.StatObjectOptions{})
		if err != nil {
			continue // Skip objects we can't read
		}

		// Extract attachment ID from filename
		fileName := filepath.Base(object.Key)
		nameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))

		attachmentID, err := uuid.Parse(nameWithoutExt)
		if err != nil {
			continue // Skip invalid attachment IDs
		}

		// Extract filename from metadata or derive from path
		originalFilename := objInfo.UserMetadata["filename"]
		if originalFilename == "" {
			originalFilename = fileName
		}

		// Filter by type if specified
		if typeFilter, exists := params["type"]; exists {
			contentType := objInfo.ContentType
			if contentType == "" {
				contentType = s.detectContentType(originalFilename)
			}
			attachmentType := s.determineAttachmentType(contentType, filepath.Ext(originalFilename))

			if typeStr, ok := typeFilter.(string); ok && string(attachmentType) != typeStr {
				continue
			}
		}

		// Generate download URL
		downloadURL := s.generatePublicDownloadURL(bucketName, object.Key)

		// Determine content type
		contentType := objInfo.ContentType
		if contentType == "" {
			contentType = s.detectContentType(originalFilename)
		}

		// Create attachment object
		attachment := &sharedModels.AttachmentBasic{
			ID:       attachmentID,
			Type:     s.determineAttachmentType(contentType, filepath.Ext(originalFilename)),
			Name:     originalFilename,
			URL:      downloadURL,
			Size:     objInfo.Size,
			MimeType: contentType,
		}

		attachments = append(attachments, attachment)
	}

	return attachments, nil
}

// GetAttachmentsByIDs retrieves specific attachments by their UUID list from MinIO
func (s *SocialService) GetAttachmentsByIDs(ctx context.Context, attachmentIDs []uuid.UUID) ([]sharedModels.AttachmentBasic, error) {
	if len(attachmentIDs) == 0 {
		return []sharedModels.AttachmentBasic{}, nil
	}

	// Ensure MinIO client is available
	if s.minioClient == nil {
		return nil, errors.New("MinIO client not initialized")
	}

	bucketName := getEnv("MINIO_BUCKET", "social-attachments")
	var attachments []sharedModels.AttachmentBasic

	// More efficient approach: search for specific attachment patterns
	// instead of listing all objects in the bucket
	for _, attachmentID := range attachmentIDs {
		attachment, err := s.findAttachmentByID(ctx, bucketName, attachmentID)
		if err != nil {
			fmt.Printf("Error finding attachment %s: %v\n", attachmentID.String(), err)
			continue
		}
		if attachment != nil {
			attachments = append(attachments, *attachment)
		}
	}

	return attachments, nil
}

// findAttachmentByID searches for a specific attachment by ID more efficiently
func (s *SocialService) findAttachmentByID(ctx context.Context, bucketName string, attachmentID uuid.UUID) (*sharedModels.AttachmentBasic, error) {
	// Use a more targeted search approach by looking for the attachment ID pattern
	attachmentIDStr := attachmentID.String()

	// List objects with the attachment ID as prefix to find the file
	opts := minio.ListObjectsOptions{
		Prefix:    "",
		Recursive: true,
	}

	for object := range s.minioClient.ListObjects(ctx, bucketName, opts) {
		if object.Err != nil {
			continue
		}

		// Check if this object matches our attachment ID
		fileName := filepath.Base(object.Key)
		nameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))

		if nameWithoutExt == attachmentIDStr {
			// Found it! Get the object info
			objInfo, err := s.minioClient.StatObject(ctx, bucketName, object.Key, minio.StatObjectOptions{})
			if err != nil {
				return nil, fmt.Errorf("error getting object info for %s: %w", object.Key, err)
			}

			// Extract original filename from metadata
			originalFilename := objInfo.UserMetadata["filename"]
			if originalFilename == "" {
				originalFilename = fileName
			}

			// Generate download URL
			downloadURL := s.generatePublicDownloadURL(bucketName, object.Key)

			// Determine content type
			contentType := objInfo.ContentType
			if contentType == "" {
				contentType = s.detectContentType(originalFilename)
			}

			// Create attachment object
			attachment := sharedModels.AttachmentBasic{
				ID:       attachmentID,
				Type:     s.determineAttachmentType(contentType, filepath.Ext(originalFilename)),
				Name:     originalFilename,
				URL:      downloadURL,
				Size:     objInfo.Size,
				MimeType: contentType,
			}

			return &attachment, nil
		}
	}

	fmt.Printf("Attachment %s not found in MinIO storage\n", attachmentID.String())
	return nil, nil
}

// validateUploadedFile validates the uploaded file
func (s *SocialService) validateUploadedFile(fileHeader *multipart.FileHeader) error {
	// Check file size (100MB limit)
	maxSize := int64(100 * 1024 * 1024)
	if fileHeader.Size > maxSize {
		return fmt.Errorf("file size %d exceeds maximum allowed size %d", fileHeader.Size, maxSize)
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	allowedExtensions := []string{
		// Images & graphics
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".psd", ".ai", ".eps", ".heic", ".heif",
		// Video
		".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm",
		// Audio
		".mp3", ".wav", ".flac", ".aac", ".ogg",
		// Documents & office
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".odt", ".ods", ".odp", ".rtf",
		// Plain text / code
		".txt", ".md", ".csv", ".json", ".xml", ".sql", ".log", ".html", ".css", ".js", ".ts", ".tsx", ".py", ".java", ".c", ".cpp", ".go", ".rb", ".php",
		// Archives
		".zip", ".rar", ".7z", ".tar", ".gz",
	}

	allowed := false
	for _, allowedExt := range allowedExtensions {
		if ext == allowedExt {
			allowed = true
			break
		}
	}

	if !allowed {
		return fmt.Errorf("file type %s is not allowed", ext)
	}

	return nil
}

// detectContentType detects content type from filename
func (s *SocialService) detectContentType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))

	contentTypes := map[string]string{
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".png":  "image/png",
		".gif":  "image/gif",
		".bmp":  "image/bmp",
		".webp": "image/webp",
		".svg":  "image/svg+xml",
		".psd":  "image/vnd.adobe.photoshop",
		".ai":   "application/postscript",
		".eps":  "application/postscript",
		".heic": "image/heic",
		".heif": "image/heif",
		".odt":  "application/vnd.oasis.opendocument.text",
		".ods":  "application/vnd.oasis.opendocument.spreadsheet",
		".odp":  "application/vnd.oasis.opendocument.presentation",
		".rtf":  "application/rtf",
		".sql":  "application/sql",
		".log":  "text/plain",
		".html": "text/html",
		".css":  "text/css",
		".js":   "application/javascript",
		".ts":   "application/typescript",
		".tsx":  "application/typescript",
		".py":   "text/x-python",
		".java": "text/x-java-source",
		".c":    "text/x-c",
		".cpp":  "text/x-c++",
		".go":   "text/x-go",
		".rb":   "application/x-ruby",
		".php":  "application/x-php",
		".zip":  "application/zip",
		".rar":  "application/x-rar-compressed",
		".7z":   "application/x-7z-compressed",
		".tar":  "application/x-tar",
		".gz":   "application/gzip",
	}

	if contentType, exists := contentTypes[ext]; exists {
		return contentType
	}

	return "application/octet-stream"
}

// determineAttachmentType determines the attachment type based on content type
func (s *SocialService) determineAttachmentType(contentType, ext string) sharedModels.AttachmentType {
	if strings.HasPrefix(contentType, "image/") {
		return sharedModels.AttachmentTypeImage
	}
	if strings.HasPrefix(contentType, "video/") {
		return sharedModels.AttachmentTypeVideo
	}
	if strings.HasPrefix(contentType, "audio/") {
		return sharedModels.AttachmentTypeAudio
	}

	ext = strings.ToLower(ext)
	switch ext {
	case ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx":
		return sharedModels.AttachmentTypeDocument
	case ".zip", ".rar", ".7z", ".tar", ".gz":
		return sharedModels.AttachmentTypeArchive
	default:
		return sharedModels.AttachmentTypeFile
	}
}

// DeleteAttachment deletes an attachment from MinIO storage
func (s *SocialService) DeleteAttachment(ctx context.Context, attachmentID string, userID string) error {
	// Ensure MinIO client is available
	if s.minioClient == nil {
		return socialErrors.ErrInternalServer.WithMessage("Storage service not available")
	}

	// Parse attachment ID as UUID
	uuidID, err := uuid.Parse(attachmentID)
	if err != nil {
		return socialErrors.NewValidationError("attachment_id", "Invalid attachment ID format")
	}

	bucketName := getEnv("MINIO_BUCKET", "social-attachments")

	// Search for the attachment in MinIO
	prefix := s.storagePathBuilder.AttachmentPrefix(userID, uuidID.String())

	// List objects with the prefix to find the exact file
	opts := minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	}

	found := false
	var objectName string

	// Find the object with this attachment ID
	for object := range s.minioClient.ListObjects(ctx, bucketName, opts) {
		if object.Err != nil {
			// Check if this is a "no such key" error or similar - treat as not found
			errStr := object.Err.Error()
			if strings.Contains(errStr, "NoSuchKey") || strings.Contains(errStr, "not found") || strings.Contains(errStr, "NoSuchBucket") {
				return socialErrors.NewNotFoundError("attachment", attachmentID)
			}
			return socialErrors.ErrInternalServer.WithDetails(map[string]interface{}{
				"error":         "Failed to list storage objects",
				"attachment_id": attachmentID,
			})
		}

		// Check if the object name contains our attachment ID
		if strings.Contains(object.Key, uuidID.String()) {
			objectName = object.Key
			found = true
			break
		}
	}

	if !found {
		return socialErrors.NewNotFoundError("attachment", attachmentID)
	}

	// Delete the object from MinIO
	err = s.minioClient.RemoveObject(ctx, bucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		// Check if this is a "no such key" error - treat as already deleted (success)
		errStr := err.Error()
		if strings.Contains(errStr, "NoSuchKey") || strings.Contains(errStr, "not found") {
			// Object already deleted, consider this a success
			return nil
		}
		return socialErrors.ErrInternalServer.WithDetails(map[string]interface{}{
			"error":         "Failed to delete attachment from storage",
			"attachment_id": attachmentID,
		})
	}

	// Clear any cached data for this attachment
	cacheKey := s.cacheKeyBuilder.AttachmentData(attachmentID)
	s.cacheManager.Delete(ctx, cacheKey)

	return nil
}

// validateMinIOAttachments validates that attachments exist and belong to the user in MinIO
func (s *SocialService) validateMinIOAttachments(ctx context.Context, attachmentIDs []uuid.UUID, userID string) error {
	if len(attachmentIDs) == 0 {
		return nil
	}

	// Ensure MinIO client is available
	if s.minioClient == nil {
		return errors.New("MinIO client not initialized")
	}

	bucketName := getEnv("MINIO_BUCKET", "social-attachments")

	// Check each attachment exists and belongs to the user
	for _, attachmentID := range attachmentIDs {
		prefix := s.storagePathBuilder.AttachmentPrefix(userID, attachmentID.String())

		// List objects with the prefix
		opts := minio.ListObjectsOptions{
			Prefix:    prefix,
			Recursive: true,
		}

		found := false
		for object := range s.minioClient.ListObjects(ctx, bucketName, opts) {
			if object.Err != nil {
				continue
			}
			found = true
			break
		}

		if !found {
			return fmt.Errorf("attachment %s not found or access denied", attachmentID.String())
		}
	}

	return nil
}

// validateAndParseAttachments provides unified attachment validation for all operations
func (s *SocialService) validateAndParseAttachments(ctx context.Context, attachmentIDStrs []string, userID string) ([]uuid.UUID, error) {
	if len(attachmentIDStrs) == 0 {
		return nil, nil
	}

	attachmentIDs := make([]uuid.UUID, len(attachmentIDStrs))
	for i, idStr := range attachmentIDStrs {
		uuidID, err := uuid.Parse(idStr)
		if err != nil {
			return nil, socialErrors.ErrInvalidAttachmentID.WithDetails(map[string]interface{}{
				"attachment_id": idStr,
				"error":         err.Error(),
			})
		}
		attachmentIDs[i] = uuidID
	}

	// Validate that user has access to these attachments in MinIO
	if err := s.validateMinIOAttachments(ctx, attachmentIDs, userID); err != nil {
		return nil, socialErrors.ErrAccessDenied.WithMessage("Attachment validation failed").WithDetails(map[string]interface{}{
			"error":          err.Error(),
			"attachment_ids": attachmentIDStrs,
		})
	}

	return attachmentIDs, nil
}

// Helper functions for validation and parsing

// parseAndValidateUUID consolidates UUID parsing with consistent error handling
func (s *SocialService) parseAndValidateUUID(idStr, fieldName string) (uuid.UUID, error) {
	if idStr == "" {
		return uuid.Nil, socialErrors.NewValidationError(fieldName, fieldName+" is required")
	}

	parsedUUID, err := uuid.Parse(idStr)
	if err != nil {
		return uuid.Nil, socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{fieldName: idStr})
	}

	return parsedUUID, nil
}

// parseAndValidateObjectID consolidates ObjectID parsing with consistent error handling
func (s *SocialService) parseAndValidateObjectID(idStr, fieldName string) (primitive.ObjectID, error) {
	if idStr == "" {
		return primitive.NilObjectID, socialErrors.NewValidationError(fieldName, fieldName+" is required")
	}

	objID, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		return primitive.NilObjectID, socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{fieldName: idStr})
	}

	return objID, nil
}


// validatePostType checks if the provided post type is valid
func (s *SocialService) validatePostType(postType models.PostType) error {
	validTypes := []models.PostType{
		models.PostTypeText,
		models.PostTypeImage,
		models.PostTypeVideo,
		models.PostTypeLink,
		models.PostTypePoll,
		models.PostTypeEvent,
	}

	for _, validType := range validTypes {
		if postType == validType {
			return nil
		}
	}

	return socialErrors.NewValidationError("type", "Invalid post type. Valid types are: text, image, video, link, poll, event")
}

// Cache invalidation is now handled directly in service methods
// This method is no longer needed as we use DirectCacheManager
