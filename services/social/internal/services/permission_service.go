package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/services/social/internal/cache"
	"github.com/swork-team/platform/services/social/internal/models"
	"github.com/swork-team/platform/services/social/internal/repositories"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// PostPermissionService handles cross-service permission validation
type PostPermissionService struct {
	postRepo      *repositories.PostRepository
	cache         *redis.Client
	cacheKeyBuilder *cache.CacheKeyBuilder
	cacheTTL      time.Duration
}

// PostPermissionRequest represents a permission validation request
type PostPermissionRequest struct {
	PostID    string   `json:"post_id"`
	UserID    string   `json:"user_id"`
	Action    string   `json:"action"` // "view", "comment", "like", "share"
	UserTeams []string `json:"user_teams,omitempty"`
	Context   string   `json:"context,omitempty"` // "direct_view", etc.
}

// PostPermissionResponse represents the validation result
type PostPermissionResponse struct {
	Allowed        bool                  `json:"allowed"`
	PostExists     bool                  `json:"post_exists"`
	PostVisibility models.PostVisibility `json:"post_visibility,omitempty"`
	AuthorID       string                `json:"author_id,omitempty"`
	TeamID         string                `json:"team_id,omitempty"`
	Reason         string                `json:"reason,omitempty"`
	CacheHit       bool                  `json:"cache_hit,omitempty"`
}

// NewPostPermissionService creates a new permission service
func NewPostPermissionService(postRepo *repositories.PostRepository, redisCache *redis.Client) *PostPermissionService {
	return &PostPermissionService{
		postRepo:        postRepo,
		cache:           redisCache,
		cacheKeyBuilder: cache.NewCacheKeyBuilder(),
		cacheTTL:        5 * time.Minute, // Short TTL for security
	}
}

// ValidatePostAccess validates if a user can access a post
func (s *PostPermissionService) ValidatePostAccess(ctx context.Context, req *PostPermissionRequest) (*PostPermissionResponse, error) {
	// Input validation
	if req.PostID == "" || req.UserID == "" {
		return &PostPermissionResponse{
			Allowed: false,
			Reason:  "invalid request: missing post_id or user_id",
		}, nil
	}

	// Try cache first for performance
	if response := s.getCachedPermission(ctx, req); response != nil {
		response.CacheHit = true
		return response, nil
	}

	// Validate post ID format
	postObjID, err := primitive.ObjectIDFromHex(req.PostID)
	if err != nil {
		response := &PostPermissionResponse{
			Allowed:    false,
			PostExists: false,
			Reason:     "invalid post ID format",
		}
		s.cachePermission(ctx, req, response)
		return response, nil
	}

	// Get post from database
	post, err := s.postRepo.GetPostByID(ctx, postObjID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post == nil {
		response := &PostPermissionResponse{
			Allowed:    false,
			PostExists: false,
			Reason:     "post not found",
		}
		s.cachePermission(ctx, req, response)
		return response, nil
	}

	// Check if post is deleted
	if post.IsDeleted {
		response := &PostPermissionResponse{
			Allowed:        false,
			PostExists:     true,
			PostVisibility: post.Visibility,
			AuthorID:       post.AuthorID.String(),
			Reason:         "post is deleted",
		}
		s.cachePermission(ctx, req, response)
		return response, nil
	}

	// Populate team ID if present
	teamID := ""
	if post.TeamID != nil {
		teamID = post.TeamID.String()
	}

	// Apply permission logic based on action and post visibility
	allowed, reason := s.evaluatePermission(post, req)

	response := &PostPermissionResponse{
		Allowed:        allowed,
		PostExists:     true,
		PostVisibility: post.Visibility,
		AuthorID:       post.AuthorID.String(),
		TeamID:         teamID,
		Reason:         reason,
	}

	// Cache the result
	s.cachePermission(ctx, req, response)

	return response, nil
}

// evaluatePermission applies the actual permission logic
func (s *PostPermissionService) evaluatePermission(post *models.Post, req *PostPermissionRequest) (bool, string) {
	// Author always has access
	if post.AuthorID.String() == req.UserID {
		return true, "author access"
	}

	// Apply visibility-based permissions
	switch post.Visibility {
	case models.PostVisibilityPublic:
		return true, "public post"

	case models.PostVisibilityPrivate:
		return false, "private post - author only"

	case models.PostVisibilityFriends:
		// TODO: Implement friendship validation
		// For now, deny access (same as current behavior)
		return false, "friends-only post - friendship validation not implemented"

	case models.PostVisibilityTeam:
		if post.TeamID == nil {
			return false, "team post without team ID"
		}

		// Check if user is member of the post's team
		postTeamID := post.TeamID.String()
		for _, userTeam := range req.UserTeams {
			if userTeam == postTeamID {
				return true, "team member access"
			}
		}
		return false, "not a team member"

	default:
		return false, "unknown visibility setting"
	}
}

// Batch validation for performance
func (s *PostPermissionService) ValidateBatchPostAccess(ctx context.Context, requests []*PostPermissionRequest) ([]*PostPermissionResponse, error) {
	if len(requests) == 0 {
		return []*PostPermissionResponse{}, nil
	}

	responses := make([]*PostPermissionResponse, len(requests))

	// Process each request
	for i, req := range requests {
		response, err := s.ValidatePostAccess(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("failed to validate request %d: %w", i, err)
		}
		responses[i] = response
	}

	return responses, nil
}

// Cache management
func (s *PostPermissionService) getCachedPermission(ctx context.Context, req *PostPermissionRequest) *PostPermissionResponse {
	if s.cache == nil {
		return nil
	}

	key := s.buildCacheKey(req)
	data, err := s.cache.Get(ctx, key).Result()
	if err != nil {
		return nil
	}

	var response PostPermissionResponse
	if err := json.Unmarshal([]byte(data), &response); err != nil {
		return nil
	}

	return &response
}

func (s *PostPermissionService) cachePermission(ctx context.Context, req *PostPermissionRequest, response *PostPermissionResponse) {
	if s.cache == nil {
		return
	}

	key := s.buildCacheKey(req)
	data, err := json.Marshal(response)
	if err == nil {
		s.cache.Set(ctx, key, data, s.cacheTTL)
	}
}

func (s *PostPermissionService) buildCacheKey(req *PostPermissionRequest) string {
	return s.cacheKeyBuilder.PostPermission(req.PostID, req.UserID, req.Action)
}

// InvalidatePostPermissionCache invalidates all cached permissions for a post
func (s *PostPermissionService) InvalidatePostPermissionCache(ctx context.Context, postID string) error {
	if s.cache == nil {
		return nil
	}

	pattern := fmt.Sprintf("%s:%s:*", cache.PostPermissionPrefix, postID)
	keys, err := s.cache.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		return s.cache.Del(ctx, keys...).Err()
	}

	return nil
}

// GetPostVisibilityInfo returns basic post info for permission decisions
func (s *PostPermissionService) GetPostVisibilityInfo(ctx context.Context, postID string) (*PostVisibilityInfo, error) {
	postObjID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return nil, errors.New("invalid post ID format")
	}

	post, err := s.postRepo.GetPostByID(ctx, postObjID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post == nil {
		return nil, errors.New("post not found")
	}

	teamID := ""
	if post.TeamID != nil {
		teamID = post.TeamID.String()
	}

	return &PostVisibilityInfo{
		PostID:     postID,
		AuthorID:   post.AuthorID.String(),
		TeamID:     teamID,
		Visibility: post.Visibility,
		IsDeleted:  post.IsDeleted,
		CreatedAt:  post.CreatedAt,
	}, nil
}

// PostVisibilityInfo contains essential post information for permission checks
type PostVisibilityInfo struct {
	PostID     string                `json:"post_id"`
	AuthorID   string                `json:"author_id"`
	TeamID     string                `json:"team_id,omitempty"`
	Visibility models.PostVisibility `json:"visibility"`
	IsDeleted  bool                  `json:"is_deleted"`
	CreatedAt  time.Time             `json:"created_at"`
}

// Helper method to validate multiple post IDs efficiently
func (s *PostPermissionService) ValidatePostIDsExist(ctx context.Context, postIDs []string) (map[string]bool, error) {
	result := make(map[string]bool)

	for _, postID := range postIDs {
		postObjID, err := primitive.ObjectIDFromHex(postID)
		if err != nil {
			result[postID] = false
			continue
		}

		post, err := s.postRepo.GetPostByID(ctx, postObjID)
		if err != nil {
			return nil, fmt.Errorf("failed to check post %s: %w", postID, err)
		}

		result[postID] = post != nil && !post.IsDeleted
	}

	return result, nil
}
