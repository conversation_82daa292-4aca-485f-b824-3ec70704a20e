package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/google/uuid"
	socialErrors "github.com/swork-team/platform/services/social/internal/errors"
	"github.com/swork-team/platform/services/social/internal/models"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Comment operations
func (s *SocialService) CreateComment(ctx context.Context, userID string, req *CreateCommentRequest) (*models.Comment, error) {
	if strings.TrimSpace(req.Content) == "" {
		return nil, socialErrors.ErrEmptyContent
	}

	// Parse userID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	postObjID, err := primitive.ObjectIDFromHex(req.PostID)
	if err != nil {
		return nil, socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": req.PostID})
	}

	// Verify post exists and user can view it
	post, err := s.postRepo.GetPostByID(ctx, postObjID)
	if err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_comment",
			"post_id":   req.PostID,
			"error":     err.Error(),
		})
	}
	if post == nil {
		return nil, socialErrors.NewNotFoundError("post", req.PostID)
	}

	userTeams := s.getUserTeams(ctx, userID)
	if !post.CanView(userID, userTeams) {
		return nil, socialErrors.NewAccessDeniedError("post", "comment").WithDetails(map[string]interface{}{
			"post_id": req.PostID,
			"user_id": userID,
		})
	}

	// Parse and validate attachment IDs
	attachmentIDs, err := s.validateAndParseAttachments(ctx, req.AttachmentIDs, userID)
	if err != nil {
		return nil, err
	}

	comment := &models.Comment{
		PostID:        postObjID,
		AuthorID:      userUUID,
		Content:       req.Content,
		AttachmentIDs: attachmentIDs,
	}

	// Handle parent comment if this is a reply
	if req.ParentID != "" {
		parentObjID, err := primitive.ObjectIDFromHex(req.ParentID)
		if err != nil {
			return nil, errors.New("invalid parent comment ID")
		}

		// Verify parent comment exists
		parentComment, err := s.postRepo.GetCommentByID(ctx, parentObjID)
		if err != nil {
			return nil, fmt.Errorf("failed to get parent comment: %w", err)
		}
		if parentComment == nil {
			return nil, errors.New("parent comment not found")
		}

		comment.ParentID = parentObjID
	}

	if err := s.postRepo.CreateComment(ctx, comment); err != nil {
		return nil, fmt.Errorf("failed to create comment: %w", err)
	}

	// Populate author info using batch populator
	comments := s.populateCommentsBatch(ctx, []models.Comment{*comment}, userID)
	*comment = comments[0]

	// Send notification to post author (if not commenting on own post)
	if post.AuthorID.String() != userID {
		go s.sendCommentNotification(userID, post.AuthorID.String(), comment.ID)
	}

	// Send notification to parent comment author (if replying)
	if !comment.ParentID.IsZero() && req.ParentID != "" {
		if parentComment, _ := s.postRepo.GetCommentByID(ctx, comment.ParentID); parentComment != nil {
			if parentComment.AuthorID.String() != userID && parentComment.AuthorID != post.AuthorID {
				go s.sendReplyNotification(userID, parentComment.AuthorID.String(), comment.ID)
			}
		}
	}

	// Async notification for comment creation
	go s.handleCommentCreated(comment, userID)

	return comment, nil
}

func (s *SocialService) UpdateComment(ctx context.Context, commentID string, userID string, content string) (*models.Comment, error) {
	if strings.TrimSpace(content) == "" {
		return nil, socialErrors.ErrEmptyContent
	}

	objID, err := primitive.ObjectIDFromHex(commentID)
	if err != nil {
		return nil, socialErrors.ErrInvalidCommentID.WithDetails(map[string]interface{}{"comment_id": commentID})
	}

	comment, err := s.postRepo.GetCommentByID(ctx, objID)
	if err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "get_comment_for_update",
			"comment_id": commentID,
			"error":      err.Error(),
		})
	}
	if comment == nil {
		return nil, socialErrors.NewNotFoundError("comment", commentID)
	}

	// Check ownership
	if comment.AuthorID.String() != userID {
		return nil, socialErrors.NewAccessDeniedError("comment", "update").WithDetails(map[string]interface{}{
			"comment_id": commentID,
			"user_id":    userID,
			"author_id":  comment.AuthorID.String(),
		})
	}

	comment.Content = content
	if err := s.postRepo.UpdateComment(ctx, comment); err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "update_comment",
			"comment_id": commentID,
			"error":      err.Error(),
		})
	}

	// Populate author info using batch populator
	comments := s.populateCommentsBatch(ctx, []models.Comment{*comment}, userID)
	*comment = comments[0]

	// Async notification for comment update
	go s.handleCommentUpdated(comment, userID)

	return comment, nil
}

func (s *SocialService) DeleteComment(ctx context.Context, commentID string, userID string) error {
	objID, err := primitive.ObjectIDFromHex(commentID)
	if err != nil {
		return socialErrors.ErrInvalidCommentID.WithDetails(map[string]interface{}{"comment_id": commentID})
	}

	comment, err := s.postRepo.GetCommentByID(ctx, objID)
	if err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "get_comment_for_delete",
			"comment_id": commentID,
			"error":      err.Error(),
		})
	}
	if comment == nil {
		return socialErrors.NewNotFoundError("comment", commentID)
	}

	// Check ownership
	if comment.AuthorID.String() != userID {
		return socialErrors.NewAccessDeniedError("comment", "delete").WithDetails(map[string]interface{}{
			"comment_id": commentID,
			"user_id":    userID,
			"author_id":  comment.AuthorID.String(),
		})
	}

	// Parse userID to UUID for repository call
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	if err := s.postRepo.DeleteComment(ctx, objID, userUUID); err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "delete_comment",
			"comment_id": commentID,
			"error":      err.Error(),
		})
	}

	// Async notification for comment deletion
	go s.handleCommentDeleted(comment.ID.Hex(), comment.PostID.Hex(), userID)

	return nil
}

func (s *SocialService) GetPostComments(ctx context.Context, postID string, userID string, request *models.PaginationRequest) ([]models.Comment, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return nil, false, socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": postID})
	}

	// Verify post exists and user can view it
	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return nil, false, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_comments",
			"post_id":   postID,
			"error":     err.Error(),
		})
	}
	if post == nil {
		return nil, false, socialErrors.NewNotFoundError("post", postID)
	}

	userTeams := s.getUserTeams(ctx, userID)
	if !post.CanView(userID, userTeams) {
		return nil, false, socialErrors.NewAccessDeniedError("post", "view_comments").WithDetails(map[string]interface{}{
			"post_id": postID,
			"user_id": userID,
		})
	}

	comments, hasMore, err := s.postRepo.GetPostComments(ctx, objID, request)
	if err != nil {
		return nil, false, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_comments",
			"post_id":   postID,
			"error":     err.Error(),
		})
	}

	// Populate author info and user likes for all comments using batch populator
	comments = s.populateCommentsBatch(ctx, comments, userID)

	// Populate nested replies with a maximum depth of 3 levels
	comments = s.populateCommentsWithNestedReplies(ctx, comments, userID, 3)

	return comments, hasMore, nil
}

func (s *SocialService) GetCommentReplies(ctx context.Context, commentID string, userID string, request *models.PaginationRequest) ([]models.Comment, bool, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, false, err
	}

	objID, err := primitive.ObjectIDFromHex(commentID)
	if err != nil {
		return nil, false, socialErrors.ErrInvalidCommentID.WithDetails(map[string]interface{}{"comment_id": commentID})
	}

	// Verify parent comment exists
	parentComment, err := s.postRepo.GetCommentByID(ctx, objID)
	if err != nil {
		return nil, false, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "get_parent_comment",
			"comment_id": commentID,
			"error":      err.Error(),
		})
	}
	if parentComment == nil {
		return nil, false, socialErrors.NewNotFoundError("comment", commentID)
	}

	// Verify user can view the post
	post, err := s.postRepo.GetPostByID(ctx, parentComment.PostID)
	if err != nil {
		return nil, false, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_reply_access",
			"post_id":   parentComment.PostID.Hex(),
			"error":     err.Error(),
		})
	}
	if post == nil {
		return nil, false, socialErrors.NewNotFoundError("post", parentComment.PostID.Hex())
	}

	userTeams := s.getUserTeams(ctx, userID)
	if !post.CanView(userID, userTeams) {
		return nil, false, socialErrors.NewAccessDeniedError("post", "view_replies").WithDetails(map[string]interface{}{
			"post_id": parentComment.PostID.Hex(),
			"user_id": userID,
		})
	}

	replies, hasMore, err := s.postRepo.GetCommentReplies(ctx, objID, request)
	if err != nil {
		return nil, false, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "get_comment_replies",
			"comment_id": commentID,
			"error":      err.Error(),
		})
	}

	// Populate author info and user likes for all replies using batch populator
	replies = s.populateCommentsBatch(ctx, replies, userID)

	return replies, hasMore, nil
}

// Like operations
func (s *SocialService) LikePost(ctx context.Context, postID string, userID string, likeType models.LikeType) error {
	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": postID})
	}

	// Verify post exists and user can view it
	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_like",
			"post_id":   postID,
			"error":     err.Error(),
		})
	}
	if post == nil {
		return socialErrors.NewNotFoundError("post", postID)
	}

	userTeams := s.getUserTeams(ctx, userID)
	if !post.CanView(userID, userTeams) {
		return socialErrors.NewAccessDeniedError("post", "like").WithDetails(map[string]interface{}{
			"post_id": postID,
			"user_id": userID,
		})
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	like := &models.Like{
		UserID: userUUID,
		PostID: objID,
		Type:   likeType,
	}

	if err := s.postRepo.CreateLike(ctx, like); err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "create_post_like",
			"post_id":   postID,
			"user_id":   userID,
			"error":     err.Error(),
		})
	}

	// Send notification to post author (if not liking own post)
	if post.AuthorID.String() != userID {
		go s.sendLikeNotification(userID, post.AuthorID.String(), like.ID, "post")
	}

	// Async notification for post like
	go s.handlePostLiked(postID, userID, post.AuthorID.String(), string(likeType))

	return nil
}

func (s *SocialService) UnlikePost(ctx context.Context, postID string, userID string) error {
	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": postID})
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	if err := s.postRepo.DeleteLike(ctx, userUUID, objID, primitive.NilObjectID); err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "delete_post_like",
			"post_id":   postID,
			"user_id":   userID,
			"error":     err.Error(),
		})
	}

	// Async notification for post unlike
	go func() {
		post, _ := s.postRepo.GetPostByID(ctx, objID)
		if post != nil {
			s.handlePostUnliked(postID, userID, post.AuthorID.String())
		}
	}()

	return nil
}

func (s *SocialService) LikeComment(ctx context.Context, commentID string, userID string, likeType models.LikeType) error {
	objID, err := primitive.ObjectIDFromHex(commentID)
	if err != nil {
		return socialErrors.ErrInvalidCommentID.WithDetails(map[string]interface{}{"comment_id": commentID})
	}

	// Verify comment exists
	comment, err := s.postRepo.GetCommentByID(ctx, objID)
	if err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "get_comment_for_like",
			"comment_id": commentID,
			"error":      err.Error(),
		})
	}
	if comment == nil {
		return socialErrors.NewNotFoundError("comment", commentID)
	}

	// Verify user can view the post
	post, err := s.postRepo.GetPostByID(ctx, comment.PostID)
	if err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_comment_like",
			"post_id":   comment.PostID.Hex(),
			"error":     err.Error(),
		})
	}
	if post == nil {
		return socialErrors.NewNotFoundError("post", comment.PostID.Hex())
	}

	userTeams := s.getUserTeams(ctx, userID)
	if !post.CanView(userID, userTeams) {
		return socialErrors.NewAccessDeniedError("comment", "like").WithDetails(map[string]interface{}{
			"comment_id": commentID,
			"user_id":    userID,
		})
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	like := &models.Like{
		UserID:    userUUID,
		CommentID: objID,
		Type:      likeType,
	}

	if err := s.postRepo.CreateLike(ctx, like); err != nil {
		return fmt.Errorf("failed to like comment: %w", err)
	}

	// Send notification to comment author (if not liking own comment)
	if comment.AuthorID.String() != userID {
		go s.sendLikeNotification(userID, comment.AuthorID.String(), like.ID, "comment")
	}

	// Async notification for comment like
	go s.handleCommentLiked(commentID, comment.PostID.Hex(), userID, comment.AuthorID.String(), string(likeType))

	return nil
}

func (s *SocialService) UnlikeComment(ctx context.Context, commentID string, userID string) error {
	objID, err := primitive.ObjectIDFromHex(commentID)
	if err != nil {
		return socialErrors.ErrInvalidCommentID.WithDetails(map[string]interface{}{"comment_id": commentID})
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	if err := s.postRepo.DeleteLike(ctx, userUUID, primitive.NilObjectID, objID); err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation":  "delete_comment_like",
			"comment_id": commentID,
			"user_id":    userID,
			"error":      err.Error(),
		})
	}

	// Async notification for comment unlike
	go func() {
		comment, _ := s.postRepo.GetCommentByID(ctx, objID)
		if comment != nil {
			s.handleCommentUnliked(commentID, comment.PostID.Hex(), userID, comment.AuthorID.String())
		}
	}()

	return nil
}

// Share operations
func (s *SocialService) SharePost(ctx context.Context, userID string, req *CreateShareRequest) (*models.Share, error) {
	objID, err := primitive.ObjectIDFromHex(req.PostID)
	if err != nil {
		return nil, socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": req.PostID})
	}

	// Verify post exists and user can view it
	post, err := s.postRepo.GetPostByID(ctx, objID)
	if err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "get_post_for_share",
			"post_id":   req.PostID,
			"error":     err.Error(),
		})
	}
	if post == nil {
		return nil, socialErrors.NewNotFoundError("post", req.PostID)
	}

	userTeams := s.getUserTeams(ctx, userID)
	if !post.CanView(userID, userTeams) {
		return nil, socialErrors.NewAccessDeniedError("post", "share").WithDetails(map[string]interface{}{
			"post_id": req.PostID,
			"user_id": userID,
		})
	}

	// Set default visibility
	if req.Visibility == "" {
		req.Visibility = models.PostVisibilityPublic
	}

	// Parse and validate team ID if provided
	var teamUUID *uuid.UUID
	if req.TeamID != "" {
		parsedTeamID, err := uuid.Parse(req.TeamID)
		if err != nil {
			return nil, socialErrors.NewValidationError("team_id", "Invalid team ID format")
		}
		teamUUID = &parsedTeamID

		// Validate team access if sharing to team
		if req.Visibility == models.PostVisibilityTeam {
			if !s.validateTeamMembership(ctx, userID, req.TeamID) {
				return nil, socialErrors.NewAccessDeniedError("team", "share").WithDetails(map[string]interface{}{
					"team_id": req.TeamID,
					"user_id": userID,
				})
			}
		}
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	share := &models.Share{
		UserID:     userUUID,
		PostID:     objID,
		TeamID:     teamUUID,
		Comment:    req.Comment,
		Visibility: req.Visibility,
	}

	if err := s.postRepo.CreateShare(ctx, share); err != nil {
		return nil, socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "create_share",
			"post_id":   req.PostID,
			"user_id":   userID,
			"error":     err.Error(),
		})
	}

	// Populate related data using batch populator
	userMap, err := s.batchPopulator.PopulateUsers(ctx, []string{userID})
	if err == nil {
		if user, exists := userMap[userID]; exists {
			share.User = user
		}
	}
	if req.TeamID != "" {
		teamMap, err := s.batchPopulator.PopulateTeams(ctx, []string{req.TeamID})
		if err == nil {
			if team, exists := teamMap[req.TeamID]; exists {
				share.Team = team
			}
		}
	}
	share.OriginalPost = post

	// Send notification to post author (if not sharing own post)
	if post.AuthorID.String() != userID {
		go s.sendShareNotification(userID, post.AuthorID.String(), share.ID)
	}

	// Async notification for post share
	go s.handlePostShared(req.PostID, userID, post.AuthorID.String())

	return share, nil
}

func (s *SocialService) UnsharePost(ctx context.Context, postID string, userID string) error {
	objID, err := primitive.ObjectIDFromHex(postID)
	if err != nil {
		return socialErrors.ErrInvalidPostID.WithDetails(map[string]interface{}{"post_id": postID})
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return socialErrors.ErrInvalidUserID.WithDetails(map[string]interface{}{"user_id": userID})
	}

	if err := s.postRepo.DeleteShare(ctx, userUUID, objID); err != nil {
		return socialErrors.ErrDatabaseError.WithDetails(map[string]interface{}{
			"operation": "delete_share",
			"post_id":   postID,
			"user_id":   userID,
			"error":     err.Error(),
		})
	}

	// Async notification for post unshare
	go func() {
		post, _ := s.postRepo.GetPostByID(ctx, objID)
		if post != nil {
			s.handlePostUnshared(postID, userID, post.AuthorID.String())
		}
	}()

	return nil
}

// Notification helpers
func (s *SocialService) sendCommentNotification(commenterID, postAuthorID string, commentID primitive.ObjectID) {
	fmt.Printf("Sending comment notification from %s to %s for comment %s\n", commenterID, postAuthorID, commentID.Hex())
}

func (s *SocialService) sendReplyNotification(replierID, commentAuthorID string, replyID primitive.ObjectID) {
	fmt.Printf("Sending reply notification from %s to %s for reply %s\n", replierID, commentAuthorID, replyID.Hex())
}

func (s *SocialService) sendLikeNotification(likerID, contentAuthorID string, likeID primitive.ObjectID, contentType string) {
	fmt.Printf("Sending like notification from %s to %s for %s like %s\n", likerID, contentAuthorID, contentType, likeID.Hex())
}

func (s *SocialService) sendShareNotification(sharerID, postAuthorID string, shareID primitive.ObjectID) {
	fmt.Printf("Sending share notification from %s to %s for share %s\n", sharerID, postAuthorID, shareID.Hex())
}
