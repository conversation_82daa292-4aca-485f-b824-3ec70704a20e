package repositories

import (
	"github.com/swork-team/platform/services/social/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CursorQuery represents a MongoDB query built from cursor-based pagination
type CursorQuery struct {
	Filter bson.M
	Sort   bson.D
	Limit  int64
}

// PaginationQueryBuilder builds MongoDB queries for cursor-based pagination
type PaginationQueryBuilder struct {
	baseFilter bson.M
}

// NewPaginationQueryBuilder creates a new query builder with base filter
func NewPaginationQueryBuilder(baseFilter bson.M) *PaginationQueryBuilder {
	if baseFilter == nil {
		baseFilter = bson.M{}
	}
	return &PaginationQueryBuilder{
		baseFilter: baseFilter,
	}
}

// BuildTimeBasedQuery builds a query for time-based cursor pagination (posts, comments, likes, shares)
func (b *PaginationQueryBuilder) BuildTimeBasedQuery(request *models.PaginationRequest, collectionType string) (*CursorQuery, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, err
	}

	query := &CursorQuery{
		Filter: b.copyBaseFilter(),
		Limit:  int64(request.Limit + 1), // +1 to check if there are more items
	}

	// Handle cursor-based pagination
	if request.Cursor != "" {
		cursor, err := models.DecodeCursor(request.Cursor)
		if err != nil {
			return nil, err
		}

		if err := cursor.ValidateCollectionType(collectionType); err != nil {
			return nil, err
		}

		// Build cursor filter based on direction
		cursorFilter := b.buildTimeBasedCursorFilter(cursor, request.Direction)
		query.Filter = bson.M{"$and": []bson.M{query.Filter, cursorFilter}}
	}

	// Set sort order based on direction
	if request.Direction == models.PaginationDirectionBackward {
		query.Sort = bson.D{{"created_at", 1}, {"_id", 1}} // Ascending for backward pagination
	} else {
		query.Sort = bson.D{{"created_at", -1}, {"_id", -1}} // Descending for forward pagination (default)
	}

	return query, nil
}

// BuildEngagementBasedQuery builds a query for engagement-based cursor pagination (trending posts)
func (b *PaginationQueryBuilder) BuildEngagementBasedQuery(request *models.PaginationRequest) (*CursorQuery, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, err
	}

	query := &CursorQuery{
		Filter: b.copyBaseFilter(),
		Limit:  int64(request.Limit + 1), // +1 to check if there are more items
	}

	// Handle cursor-based pagination for engagement score
	if request.Cursor != "" {
		cursor, err := models.DecodeCursor(request.Cursor)
		if err != nil {
			return nil, err
		}

		if err := cursor.ValidateCollectionType(models.CollectionTypePosts); err != nil {
			return nil, err
		}

		// Build cursor filter for engagement-based sorting
		cursorFilter := b.buildEngagementBasedCursorFilter(cursor, request.Direction)
		query.Filter = bson.M{"$and": []bson.M{query.Filter, cursorFilter}}
	}

	// Note: Sort order for engagement queries should be handled in aggregation pipeline
	// This is just for the filter part
	return query, nil
}

// BuildSearchBasedQuery builds a query for search relevance cursor pagination
func (b *PaginationQueryBuilder) BuildSearchBasedQuery(request *models.PaginationRequest, searchQuery string) (*CursorQuery, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, err
	}

	query := &CursorQuery{
		Filter: b.copyBaseFilter(),
		Limit:  int64(request.Limit + 1), // +1 to check if there are more items
	}

	// Add text search to filter
	query.Filter["$text"] = bson.M{"$search": searchQuery}

	// Handle cursor-based pagination for search results
	if request.Cursor != "" {
		cursor, err := models.DecodeCursor(request.Cursor)
		if err != nil {
			return nil, err
		}

		if err := cursor.ValidateCollectionType(models.CollectionTypePosts); err != nil {
			return nil, err
		}

		// For search results, we typically sort by relevance score then created_at
		cursorFilter := b.buildSearchBasedCursorFilter(cursor, request.Direction)
		query.Filter = bson.M{"$and": []bson.M{query.Filter, cursorFilter}}
	}

	// Sort order for search should prioritize relevance score
	if request.Direction == models.PaginationDirectionBackward {
		query.Sort = bson.D{{"score", bson.M{"$meta": "textScore"}}, {"created_at", 1}, {"_id", 1}}
	} else {
		query.Sort = bson.D{{"score", bson.M{"$meta": "textScore"}}, {"created_at", -1}, {"_id", -1}}
	}

	return query, nil
}

// buildTimeBasedCursorFilter creates a filter for time-based pagination
func (b *PaginationQueryBuilder) buildTimeBasedCursorFilter(cursor *models.CursorData, direction models.PaginationDirection) bson.M {
	if direction == models.PaginationDirectionBackward {
		// For backward pagination: find items created after the cursor
		return bson.M{
			"$or": []bson.M{
				{"created_at": bson.M{"$gt": cursor.Timestamp}},
				{
					"created_at": cursor.Timestamp,
					"_id":        bson.M{"$gt": cursor.ID},
				},
			},
		}
	} else {
		// For forward pagination: find items created before the cursor
		return bson.M{
			"$or": []bson.M{
				{"created_at": bson.M{"$lt": cursor.Timestamp}},
				{
					"created_at": cursor.Timestamp,
					"_id":        bson.M{"$lt": cursor.ID},
				},
			},
		}
	}
}

// buildEngagementBasedCursorFilter creates a filter for engagement-based pagination
func (b *PaginationQueryBuilder) buildEngagementBasedCursorFilter(cursor *models.CursorData, direction models.PaginationDirection) bson.M {
	if cursor.EngagementScore == nil {
		// Fallback to time-based if no engagement score
		return b.buildTimeBasedCursorFilter(cursor, direction)
	}

	if direction == models.PaginationDirectionBackward {
		// For backward pagination: find items with higher engagement score
		return bson.M{
			"$or": []bson.M{
				{"engagement_score": bson.M{"$gt": *cursor.EngagementScore}},
				{
					"engagement_score": *cursor.EngagementScore,
					"created_at":       bson.M{"$gt": cursor.Timestamp},
				},
				{
					"engagement_score": *cursor.EngagementScore,
					"created_at":       cursor.Timestamp,
					"_id":              bson.M{"$gt": cursor.ID},
				},
			},
		}
	} else {
		// For forward pagination: find items with lower engagement score
		return bson.M{
			"$or": []bson.M{
				{"engagement_score": bson.M{"$lt": *cursor.EngagementScore}},
				{
					"engagement_score": *cursor.EngagementScore,
					"created_at":       bson.M{"$lt": cursor.Timestamp},
				},
				{
					"engagement_score": *cursor.EngagementScore,
					"created_at":       cursor.Timestamp,
					"_id":              bson.M{"$lt": cursor.ID},
				},
			},
		}
	}
}

// buildSearchBasedCursorFilter creates a filter for search relevance pagination
func (b *PaginationQueryBuilder) buildSearchBasedCursorFilter(cursor *models.CursorData, direction models.PaginationDirection) bson.M {
	if cursor.RelevanceScore == nil {
		// Fallback to time-based if no relevance score
		return b.buildTimeBasedCursorFilter(cursor, direction)
	}

	if direction == models.PaginationDirectionBackward {
		// For backward pagination: find items with higher relevance score
		return bson.M{
			"$or": []bson.M{
				{"score": bson.M{"$gt": *cursor.RelevanceScore}},
				{
					"score":      *cursor.RelevanceScore,
					"created_at": bson.M{"$gt": cursor.Timestamp},
				},
				{
					"score":      *cursor.RelevanceScore,
					"created_at": cursor.Timestamp,
					"_id":        bson.M{"$gt": cursor.ID},
				},
			},
		}
	} else {
		// For forward pagination: find items with lower relevance score
		return bson.M{
			"$or": []bson.M{
				{"score": bson.M{"$lt": *cursor.RelevanceScore}},
				{
					"score":      *cursor.RelevanceScore,
					"created_at": bson.M{"$lt": cursor.Timestamp},
				},
				{
					"score":      *cursor.RelevanceScore,
					"created_at": cursor.Timestamp,
					"_id":        bson.M{"$lt": cursor.ID},
				},
			},
		}
	}
}

// copyBaseFilter creates a deep copy of the base filter
func (b *PaginationQueryBuilder) copyBaseFilter() bson.M {
	copy := bson.M{}
	for k, v := range b.baseFilter {
		copy[k] = v
	}
	return copy
}

// ToFindOptions converts CursorQuery to MongoDB find options
func (cq *CursorQuery) ToFindOptions() *options.FindOptions {
	opts := options.Find()
	
	if cq.Sort != nil {
		opts.SetSort(cq.Sort)
	}
	
	if cq.Limit > 0 {
		opts.SetLimit(cq.Limit)
	}
	
	return opts
}

// ProcessResultsForPagination processes query results and determines if there are more items
func ProcessResultsForPagination[T any](results []T, requestedLimit int) ([]T, bool) {
	if len(results) > requestedLimit {
		// Remove the extra item and return true for hasMore
		return results[:requestedLimit], true
	}
	return results, false
}

// ReverseSliceIfNeeded reverses the slice if backward pagination was used
func ReverseSliceIfNeeded[T any](slice []T, direction models.PaginationDirection) []T {
	if direction == models.PaginationDirectionBackward {
		// Reverse the slice for backward pagination
		for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
			slice[i], slice[j] = slice[j], slice[i]
		}
	}
	return slice
}

// BuildTrendingPostsAggregationPipeline builds aggregation pipeline for trending posts with cursor support
func BuildTrendingPostsAggregationPipeline(request *models.PaginationRequest, baseFilter bson.M, sinceTime primitive.DateTime) ([]bson.M, error) {
	if err := request.ValidateAndNormalize(); err != nil {
		return nil, err
	}

	pipeline := []bson.M{
		// Match base criteria
		{
			"$match": baseFilter,
		},
		// Calculate engagement score
		{
			"$addFields": bson.M{
				"engagement_score": bson.M{
					"$add": []interface{}{
						"$like_count",
						bson.M{"$multiply": []interface{}{"$comment_count", 2}},
						bson.M{"$multiply": []interface{}{"$share_count", 3}},
						bson.M{"$divide": []interface{}{"$view_count", 10}},
					},
				},
			},
		},
	}

	// Add cursor filter if provided
	if request.Cursor != "" {
		cursor, err := models.DecodeCursor(request.Cursor)
		if err != nil {
			return nil, err
		}

		if err := cursor.ValidateCollectionType(models.CollectionTypePosts); err != nil {
			return nil, err
		}

		builder := NewPaginationQueryBuilder(bson.M{})
		cursorFilter := builder.buildEngagementBasedCursorFilter(cursor, request.Direction)
		
		pipeline = append(pipeline, bson.M{
			"$match": cursorFilter,
		})
	}

	// Add sort stage
	sortDirection := -1
	if request.Direction == models.PaginationDirectionBackward {
		sortDirection = 1
	}

	pipeline = append(pipeline, bson.M{
		"$sort": bson.D{
			{"engagement_score", sortDirection},
			{"created_at", sortDirection},
			{"_id", sortDirection},
		},
	})

	// Add limit stage
	pipeline = append(pipeline, bson.M{
		"$limit": int64(request.Limit + 1), // +1 to check for more items
	})

	return pipeline, nil
}