package repositories

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/database"
	"github.com/swork-team/platform/services/social/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type PostRepository struct {
	client   *mongo.Client
	db       *mongo.Database
	posts    *mongo.Collection
	comments *mongo.Collection
	likes    *mongo.Collection
	shares   *mongo.Collection
	reports  *mongo.Collection
	feeds    *mongo.Collection
}

func NewPostRepository(client *mongo.Client, db *mongo.Database) *PostRepository {
	repo := &PostRepository{
		client:   client,
		db:       db,
		posts:    db.Collection("posts"),
		comments: db.Collection("comments"),
		likes:    db.Collection("likes"),
		shares:   db.Collection("shares"),
		reports:  db.Collection("reports"),
		feeds:    db.Collection("feeds"),
	}

	repo.createIndexes()
	return repo
}

func (r *PostRepository) createIndexes() {
	ctx := context.Background()

	// Post indexes
	r.posts.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.M{"author_id": 1}},
		{Keys: bson.M{"team_id": 1}},
		{Keys: bson.M{"created_at": -1}},
		{Keys: bson.M{"visibility": 1}},
		{Keys: bson.M{"is_deleted": 1}},
		{Keys: bson.M{"tags": 1}},
		{Keys: bson.D{{"author_id", 1}, {"created_at", -1}}},
		{Keys: bson.D{{"team_id", 1}, {"created_at", -1}}},
		{Keys: bson.D{{"visibility", 1}, {"created_at", -1}}},
		// Text search index for content and tags
		{Keys: bson.M{"content": "text", "tags": "text"}},
		// Geospatial index for location-based queries
		{Keys: bson.M{"location.coordinates": "2dsphere"}},
		// Compound index for engagement-based queries (trending)
		{Keys: bson.D{{"like_count", -1}, {"comment_count", -1}, {"share_count", -1}, {"created_at", -1}}},
	})

	// Comment indexes
	r.comments.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.M{"post_id": 1}},
		{Keys: bson.M{"author_id": 1}},
		{Keys: bson.M{"parent_id": 1}},
		{Keys: bson.M{"created_at": -1}},
		{Keys: bson.D{{"post_id", 1}, {"created_at", -1}}},
		{Keys: bson.D{{"post_id", 1}, {"parent_id", 1}}},
	})

	// Like indexes with proper partial filters to prevent duplicate key errors
	r.likes.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.M{"user_id": 1}},
		{Keys: bson.M{"post_id": 1}},
		{Keys: bson.M{"comment_id": 1}},
		{Keys: bson.D{{"user_id", 1}, {"post_id", 1}}, Options: options.Index().SetUnique(true).SetPartialFilterExpression(bson.M{"post_id": bson.M{"$exists": true, "$type": "objectId"}})},
		{Keys: bson.D{{"user_id", 1}, {"comment_id", 1}}, Options: options.Index().SetUnique(true).SetPartialFilterExpression(bson.M{"comment_id": bson.M{"$exists": true, "$type": "objectId"}})},
	})

	// Share indexes
	r.shares.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.M{"user_id": 1}},
		{Keys: bson.M{"post_id": 1}},
		{Keys: bson.M{"created_at": -1}},
	})

	// Feed indexes
	r.feeds.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.M{"user_id": 1}, Options: options.Index().SetUnique(true)},
		{Keys: bson.M{"updated_at": 1}},
	})
}

// Post CRUD operations
func (r *PostRepository) CreatePost(ctx context.Context, post *models.Post) error {
	post.CreatedAt = time.Now()
	post.UpdatedAt = time.Now()

	// Try to use transactions first (for replica sets/sharded clusters)
	// Fall back to non-transactional operations for standalone MongoDB
	err := r.createPostWithTransaction(ctx, post)
	if err != nil {
		// Check if error is related to transactions not being supported
		if database.IsTransactionUnsupportedError(err) {
			// Fall back to non-transactional operation
			return r.createPostWithoutTransaction(ctx, post)
		}
		return err
	}
	return nil
}

// createPostWithTransaction uses MongoDB transactions for atomicity
func (r *PostRepository) createPostWithTransaction(ctx context.Context, post *models.Post) error {
	return database.WithTransaction(ctx, r.client, func(sc mongo.SessionContext) error {
		return r.performCreatePostOperation(sc, post)
	})
}

// createPostWithoutTransaction performs create post operation without transactions
func (r *PostRepository) createPostWithoutTransaction(ctx context.Context, post *models.Post) error {
	return r.performCreatePostOperation(ctx, post)
}

// performCreatePostOperation contains the core logic for creating posts
func (r *PostRepository) performCreatePostOperation(ctx context.Context, post *models.Post) error {
	result, err := r.posts.InsertOne(ctx, post)
	if err != nil {
		return err
	}

	post.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (r *PostRepository) GetPostByID(ctx context.Context, id primitive.ObjectID) (*models.Post, error) {
	var post models.Post
	err := r.posts.FindOne(ctx, bson.M{"_id": id, "is_deleted": false}).Decode(&post)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &post, nil
}

func (r *PostRepository) UpdatePost(ctx context.Context, post *models.Post) error {
	post.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"content":    post.Content,
			"tags":       post.Tags,
			"mentions":   post.Mentions,
			"location":   post.Location,
			"visibility": post.Visibility,
			"updated_at": post.UpdatedAt,
		},
	}

	_, err := r.posts.UpdateOne(ctx, bson.M{"_id": post.ID}, update)
	return err
}

func (r *PostRepository) DeletePost(ctx context.Context, id primitive.ObjectID, userID uuid.UUID) error {
	update := bson.M{
		"$set": bson.M{
			"is_deleted": true,
			"deleted_at": time.Now(),
			"deleted_by": userID,
		},
	}

	_, err := r.posts.UpdateOne(ctx, bson.M{"_id": id}, update)
	return err
}


// GetUserPosts retrieves user posts with cursor-based pagination
func (r *PostRepository) GetUserPosts(ctx context.Context, userID uuid.UUID, request *models.PaginationRequest) ([]models.Post, bool, error) {
	baseFilter := bson.M{
		"author_id":  userID,
		"is_deleted": false,
	}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypePosts)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.posts.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var posts []models.Post
	if err := cursor.All(ctx, &posts); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalPosts, hasMore := ProcessResultsForPagination(posts, request.Limit)
	finalPosts = ReverseSliceIfNeeded(finalPosts, models.PaginationDirection(request.Direction))

	return finalPosts, hasMore, nil
}


// GetTeamPosts retrieves team posts with cursor-based pagination
func (r *PostRepository) GetTeamPosts(ctx context.Context, teamID uuid.UUID, request *models.PaginationRequest) ([]models.Post, bool, error) {
	baseFilter := bson.M{
		"team_id":    teamID,
		"is_deleted": false,
	}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypePosts)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.posts.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var posts []models.Post
	if err := cursor.All(ctx, &posts); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalPosts, hasMore := ProcessResultsForPagination(posts, request.Limit)
	finalPosts = ReverseSliceIfNeeded(finalPosts, models.PaginationDirection(request.Direction))

	return finalPosts, hasMore, nil
}


// GetPublicFeed retrieves public feed with cursor-based pagination
func (r *PostRepository) GetPublicFeed(ctx context.Context, request *models.PaginationRequest) ([]models.Post, bool, error) {
	baseFilter := bson.M{
		"visibility": models.PostVisibilityPublic,
		"is_deleted": false,
	}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypePosts)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.posts.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var posts []models.Post
	if err := cursor.All(ctx, &posts); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalPosts, hasMore := ProcessResultsForPagination(posts, request.Limit)
	finalPosts = ReverseSliceIfNeeded(finalPosts, models.PaginationDirection(request.Direction))

	return finalPosts, hasMore, nil
}


// GetPersonalizedFeed retrieves personalized feed with cursor-based pagination
func (r *PostRepository) GetPersonalizedFeed(ctx context.Context, userID uuid.UUID, friendIDs, teamIDs []uuid.UUID, request *models.PaginationRequest) ([]models.Post, bool, error) {
	// Build filter for personalized feed
	baseFilter := bson.M{
		"is_deleted": false,
		"$or": []bson.M{
			// Public posts
			{"visibility": models.PostVisibilityPublic},
			// User's own posts
			{"author_id": userID},
			// Friends' posts
			{
				"author_id":  bson.M{"$in": friendIDs},
				"visibility": bson.M{"$in": []models.PostVisibility{models.PostVisibilityPublic, models.PostVisibilityFriends}},
			},
			// Team posts
			{
				"team_id":    bson.M{"$in": teamIDs},
				"visibility": models.PostVisibilityTeam,
			},
		},
	}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypePosts)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.posts.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var posts []models.Post
	if err := cursor.All(ctx, &posts); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalPosts, hasMore := ProcessResultsForPagination(posts, request.Limit)
	finalPosts = ReverseSliceIfNeeded(finalPosts, models.PaginationDirection(request.Direction))

	return finalPosts, hasMore, nil
}

// SearchPosts retrieves search posts with cursor-based pagination
func (r *PostRepository) SearchPosts(ctx context.Context, query string, userID uuid.UUID, friendIDs, teamIDs []uuid.UUID, request *models.PaginationRequest) ([]models.Post, bool, error) {
	baseFilter := bson.M{
		"is_deleted": false,
		"$or": []bson.M{
			{"visibility": models.PostVisibilityPublic},
			{"author_id": userID},
			{
				"author_id":  bson.M{"$in": friendIDs},
				"visibility": bson.M{"$in": []models.PostVisibility{models.PostVisibilityPublic, models.PostVisibilityFriends}},
			},
			{
				"team_id":    bson.M{"$in": teamIDs},
				"visibility": models.PostVisibilityTeam,
			},
		},
	}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	queryObj, err := queryBuilder.BuildSearchBasedQuery(request, query)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.posts.Find(ctx, queryObj.Filter, queryObj.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var posts []models.Post
	if err := cursor.All(ctx, &posts); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalPosts, hasMore := ProcessResultsForPagination(posts, request.Limit)
	finalPosts = ReverseSliceIfNeeded(finalPosts, models.PaginationDirection(request.Direction))

	return finalPosts, hasMore, nil
}


// GetTrendingPosts retrieves trending posts with cursor-based pagination
func (r *PostRepository) GetTrendingPosts(ctx context.Context, since time.Time, request *models.PaginationRequest) ([]models.Post, bool, error) {
	baseFilter := bson.M{
		"created_at": bson.M{"$gte": since},
		"visibility": models.PostVisibilityPublic,
		"is_deleted": false,
	}

	pipeline, err := BuildTrendingPostsAggregationPipeline(request, baseFilter, primitive.NewDateTimeFromTime(since))
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.posts.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var posts []models.Post
	if err := cursor.All(ctx, &posts); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalPosts, hasMore := ProcessResultsForPagination(posts, request.Limit)
	finalPosts = ReverseSliceIfNeeded(finalPosts, models.PaginationDirection(request.Direction))

	return finalPosts, hasMore, nil
}

// Engagement operations
func (r *PostRepository) IncrementViewCount(ctx context.Context, postID primitive.ObjectID) error {
	_, err := r.posts.UpdateOne(
		ctx,
		bson.M{"_id": postID},
		bson.M{"$inc": bson.M{"view_count": 1}},
	)
	return err
}

// AtomicIncrementLike atomically increments like count for a post
func (r *PostRepository) AtomicIncrementLike(ctx context.Context, postID primitive.ObjectID) error {
	return database.AtomicIncrement(ctx, r.posts, bson.M{"_id": postID}, "like_count", 1)
}

// AtomicDecrementLike atomically decrements like count for a post
func (r *PostRepository) AtomicDecrementLike(ctx context.Context, postID primitive.ObjectID) error {
	return database.AtomicDecrement(ctx, r.posts, bson.M{"_id": postID}, "like_count", 1)
}

// AtomicIncrementComment atomically increments comment count for a post
func (r *PostRepository) AtomicIncrementComment(ctx context.Context, postID primitive.ObjectID) error {
	return database.AtomicIncrement(ctx, r.posts, bson.M{"_id": postID}, "comment_count", 1)
}

// AtomicDecrementComment atomically decrements comment count for a post
func (r *PostRepository) AtomicDecrementComment(ctx context.Context, postID primitive.ObjectID) error {
	return database.AtomicDecrement(ctx, r.posts, bson.M{"_id": postID}, "comment_count", 1)
}

// AtomicIncrementShare atomically increments share count for a post
func (r *PostRepository) AtomicIncrementShare(ctx context.Context, postID primitive.ObjectID) error {
	return database.AtomicIncrement(ctx, r.posts, bson.M{"_id": postID}, "share_count", 1)
}

// AtomicDecrementShare atomically decrements share count for a post
func (r *PostRepository) AtomicDecrementShare(ctx context.Context, postID primitive.ObjectID) error {
	return database.AtomicDecrement(ctx, r.posts, bson.M{"_id": postID}, "share_count", 1)
}

