package repositories

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/database"
	"github.com/swork-team/platform/services/social/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (r *PostRepository) CreateComment(ctx context.Context, comment *models.Comment) error {
	comment.CreatedAt = time.Now()
	comment.UpdatedAt = time.Now()

	// Try to use transactions first (for replica sets/sharded clusters)
	// Fall back to non-transactional operations for standalone MongoDB
	err := r.createCommentWithTransaction(ctx, comment)
	if err != nil {
		// Check if error is related to transactions not being supported
		if database.IsTransactionUnsupportedError(err) {
			// Fall back to non-transactional operation
			return r.createCommentWithoutTransaction(ctx, comment)
		}
		return err
	}
	return nil
}

// createCommentWithTransaction uses MongoDB transactions for atomicity
func (r *PostRepository) createCommentWithTransaction(ctx context.Context, comment *models.Comment) error {
	return database.WithTransaction(ctx, r.client, func(sc mongo.SessionContext) error {
		return r.performCreateCommentOperation(sc, comment)
	})
}

// createCommentWithoutTransaction performs create comment operation without transactions
func (r *PostRepository) createCommentWithoutTransaction(ctx context.Context, comment *models.Comment) error {
	return r.performCreateCommentOperation(ctx, comment)
}

// performCreateCommentOperation contains the core logic for creating comments
func (r *PostRepository) performCreateCommentOperation(ctx context.Context, comment *models.Comment) error {
	// Insert comment
	result, err := r.comments.InsertOne(ctx, comment)
	if err != nil {
		return err
	}

	comment.ID = result.InsertedID.(primitive.ObjectID)

	// Atomically increment post comment count
	if !comment.PostID.IsZero() {
		return r.AtomicIncrementComment(ctx, comment.PostID)
	}

	return nil
}

func (r *PostRepository) GetCommentByID(ctx context.Context, id primitive.ObjectID) (*models.Comment, error) {
	var comment models.Comment
	err := r.comments.FindOne(ctx, bson.M{"_id": id, "is_deleted": false}).Decode(&comment)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &comment, nil
}

func (r *PostRepository) UpdateComment(ctx context.Context, comment *models.Comment) error {
	comment.UpdatedAt = time.Now()

	update := bson.M{
		"$set": bson.M{
			"content":    comment.Content,
			"updated_at": comment.UpdatedAt,
		},
	}

	_, err := r.comments.UpdateOne(ctx, bson.M{"_id": comment.ID}, update)
	return err
}

func (r *PostRepository) DeleteComment(ctx context.Context, id primitive.ObjectID, userID uuid.UUID) error {
	// Try to use transactions first (for replica sets/sharded clusters)
	// Fall back to non-transactional operations for standalone MongoDB
	err := r.deleteCommentWithTransaction(ctx, id, userID)
	if err != nil {
		// Check if error is related to transactions not being supported
		if database.IsTransactionUnsupportedError(err) {
			// Fall back to non-transactional operation
			return r.deleteCommentWithoutTransaction(ctx, id, userID)
		}
		return err
	}
	return nil
}

// deleteCommentWithTransaction uses MongoDB transactions for atomicity
func (r *PostRepository) deleteCommentWithTransaction(ctx context.Context, id primitive.ObjectID, userID uuid.UUID) error {
	return database.WithTransaction(ctx, r.client, func(sc mongo.SessionContext) error {
		return r.performDeleteCommentOperation(sc, id, userID)
	})
}

// deleteCommentWithoutTransaction performs delete comment operation without transactions
func (r *PostRepository) deleteCommentWithoutTransaction(ctx context.Context, id primitive.ObjectID, userID uuid.UUID) error {
	return r.performDeleteCommentOperation(ctx, id, userID)
}

// performDeleteCommentOperation contains the core logic for deleting comments
func (r *PostRepository) performDeleteCommentOperation(ctx context.Context, id primitive.ObjectID, userID uuid.UUID) error {
	// First get the comment to know which post to update
	var comment models.Comment
	err := r.comments.FindOne(ctx, bson.M{"_id": id, "is_deleted": false}).Decode(&comment)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil // Comment already deleted or doesn't exist
		}
		return err
	}

	// Mark comment as deleted
	update := bson.M{
		"$set": bson.M{
			"is_deleted": true,
			"deleted_at": time.Now(),
			"deleted_by": userID,
		},
	}

	result, err := r.comments.UpdateOne(ctx, bson.M{"_id": id}, update)
	if err != nil {
		return err
	}

	// If comment was actually modified, decrement post comment count
	if result.ModifiedCount > 0 && !comment.PostID.IsZero() {
		return r.AtomicDecrementComment(ctx, comment.PostID)
	}

	return nil
}


// GetPostComments retrieves post comments with cursor-based pagination
func (r *PostRepository) GetPostComments(ctx context.Context, postID primitive.ObjectID, request *models.PaginationRequest) ([]models.Comment, bool, error) {
	baseFilter := bson.M{
		"post_id":    postID,
		"parent_id":  bson.M{"$exists": false}, // Top-level comments only
		"is_deleted": false,
	}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypeComments)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.comments.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var comments []models.Comment
	if err := cursor.All(ctx, &comments); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalComments, hasMore := ProcessResultsForPagination(comments, request.Limit)
	finalComments = ReverseSliceIfNeeded(finalComments, models.PaginationDirection(request.Direction))

	return finalComments, hasMore, nil
}


// GetCommentReplies retrieves comment replies with cursor-based pagination
func (r *PostRepository) GetCommentReplies(ctx context.Context, parentID primitive.ObjectID, request *models.PaginationRequest) ([]models.Comment, bool, error) {
	baseFilter := bson.M{
		"parent_id":  parentID,
		"is_deleted": false,
	}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypeComments)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.comments.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var replies []models.Comment
	if err := cursor.All(ctx, &replies); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalReplies, hasMore := ProcessResultsForPagination(replies, request.Limit)
	finalReplies = ReverseSliceIfNeeded(finalReplies, models.PaginationDirection(request.Direction))

	return finalReplies, hasMore, nil
}


// Like operations
func (r *PostRepository) CreateLike(ctx context.Context, like *models.Like) error {
	like.CreatedAt = time.Now()

	// Try to use transactions first (for replica sets/sharded clusters)
	// Fall back to non-transactional operations for standalone MongoDB
	err := r.createLikeWithTransaction(ctx, like)
	if err != nil {
		// Check if error is related to transactions not being supported
		if database.IsTransactionUnsupportedError(err) {
			// Fall back to non-transactional operation
			return r.createLikeWithoutTransaction(ctx, like)
		}
		return err
	}
	return nil
}

// createLikeWithTransaction uses MongoDB transactions for atomicity
func (r *PostRepository) createLikeWithTransaction(ctx context.Context, like *models.Like) error {
	return database.WithTransaction(ctx, r.client, func(sc mongo.SessionContext) error {
		return r.performLikeOperation(sc, like)
	})
}

// createLikeWithoutTransaction performs like operation without transactions
// Note: This is less safe but works with standalone MongoDB
func (r *PostRepository) createLikeWithoutTransaction(ctx context.Context, like *models.Like) error {
	return r.performLikeOperation(ctx, like)
}

// performLikeOperation contains the core logic for creating/updating likes
func (r *PostRepository) performLikeOperation(ctx context.Context, like *models.Like) error {
	// Check if like already exists
	filter := bson.M{"user_id": like.UserID}
	if !like.PostID.IsZero() {
		filter["post_id"] = like.PostID
	} else if !like.CommentID.IsZero() {
		filter["comment_id"] = like.CommentID
	}

	var existingLike models.Like
	err := r.likes.FindOne(ctx, filter).Decode(&existingLike)
	if err == nil {
		// Update existing like
		update := bson.M{"$set": bson.M{"type": like.Type, "created_at": like.CreatedAt}}
		_, err = r.likes.UpdateOne(ctx, bson.M{"_id": existingLike.ID}, update)
		like.ID = existingLike.ID
		return err
	} else if !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	// Create new like
	result, err := r.likes.InsertOne(ctx, like)
	if err != nil {
		return err
	}

	like.ID = result.InsertedID.(primitive.ObjectID)

	// Atomically update engagement counts
	if !like.PostID.IsZero() {
		return r.AtomicIncrementLike(ctx, like.PostID)
	} else if !like.CommentID.IsZero() {
		return database.AtomicIncrement(ctx, r.comments, bson.M{"_id": like.CommentID}, "like_count", 1)
	}

	return nil
}

func (r *PostRepository) DeleteLike(ctx context.Context, userID uuid.UUID, postID, commentID primitive.ObjectID) error {
	// Try to use transactions first (for replica sets/sharded clusters)
	// Fall back to non-transactional operations for standalone MongoDB
	err := r.deleteLikeWithTransaction(ctx, userID, postID, commentID)
	if err != nil {
		// Check if error is related to transactions not being supported
		if database.IsTransactionUnsupportedError(err) {
			// Fall back to non-transactional operation
			return r.deleteLikeWithoutTransaction(ctx, userID, postID, commentID)
		}
		return err
	}
	return nil
}

// deleteLikeWithTransaction uses MongoDB transactions for atomicity
func (r *PostRepository) deleteLikeWithTransaction(ctx context.Context, userID uuid.UUID, postID, commentID primitive.ObjectID) error {
	return database.WithTransaction(ctx, r.client, func(sc mongo.SessionContext) error {
		return r.performDeleteLikeOperation(sc, userID, postID, commentID)
	})
}

// deleteLikeWithoutTransaction performs delete like operation without transactions
func (r *PostRepository) deleteLikeWithoutTransaction(ctx context.Context, userID uuid.UUID, postID, commentID primitive.ObjectID) error {
	return r.performDeleteLikeOperation(ctx, userID, postID, commentID)
}

// performDeleteLikeOperation contains the core logic for deleting likes
func (r *PostRepository) performDeleteLikeOperation(ctx context.Context, userID uuid.UUID, postID, commentID primitive.ObjectID) error {
	filter := bson.M{"user_id": userID}
	if !postID.IsZero() {
		filter["post_id"] = postID
	} else if !commentID.IsZero() {
		filter["comment_id"] = commentID
	}

	result, err := r.likes.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}

	// If like was actually deleted, decrement counts
	if result.DeletedCount > 0 {
		if !postID.IsZero() {
			return r.AtomicDecrementLike(ctx, postID)
		} else if !commentID.IsZero() {
			return database.AtomicDecrement(ctx, r.comments, bson.M{"_id": commentID}, "like_count", 1)
		}
	}

	return nil
}

func (r *PostRepository) GetUserLike(ctx context.Context, userID uuid.UUID, postID, commentID primitive.ObjectID) (*models.Like, error) {
	filter := bson.M{"user_id": userID}
	if !postID.IsZero() {
		filter["post_id"] = postID
	} else if !commentID.IsZero() {
		filter["comment_id"] = commentID
	}

	var like models.Like
	err := r.likes.FindOne(ctx, filter).Decode(&like)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &like, nil
}


// GetPostLikes retrieves post likes with cursor-based pagination
func (r *PostRepository) GetPostLikes(ctx context.Context, postID primitive.ObjectID, request *models.PaginationRequest) ([]models.Like, bool, error) {
	baseFilter := bson.M{"post_id": postID}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypeLikes)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.likes.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var likes []models.Like
	if err := cursor.All(ctx, &likes); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalLikes, hasMore := ProcessResultsForPagination(likes, request.Limit)
	finalLikes = ReverseSliceIfNeeded(finalLikes, models.PaginationDirection(request.Direction))

	return finalLikes, hasMore, nil
}


// GetCommentLikes retrieves comment likes with cursor-based pagination
func (r *PostRepository) GetCommentLikes(ctx context.Context, commentID primitive.ObjectID, request *models.PaginationRequest) ([]models.Like, bool, error) {
	baseFilter := bson.M{"comment_id": commentID}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypeLikes)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.likes.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var likes []models.Like
	if err := cursor.All(ctx, &likes); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalLikes, hasMore := ProcessResultsForPagination(likes, request.Limit)
	finalLikes = ReverseSliceIfNeeded(finalLikes, models.PaginationDirection(request.Direction))

	return finalLikes, hasMore, nil
}

func (r *PostRepository) GetPostReactionStats(ctx context.Context, postID primitive.ObjectID) (*models.ReactionStats, error) {
	pipeline := []bson.M{
		{"$match": bson.M{"post_id": postID}},
		{
			"$group": bson.M{
				"_id":   "$type",
				"count": bson.M{"$sum": 1},
				"users": bson.M{"$push": bson.M{
					"user_id":    "$user_id",
					"type":       "$type",
					"created_at": "$created_at",
				}},
			},
		},
		{
			"$sort": bson.M{"count": -1},
		},
	}

	cursor, err := r.likes.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []struct {
		Type  string `bson:"_id"`
		Count int    `bson:"count"`
		Users []struct {
			UserID    string    `bson:"user_id"`
			Type      string    `bson:"type"`
			CreatedAt time.Time `bson:"created_at"`
		} `bson:"users"`
	}

	if err := cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	stats := &models.ReactionStats{
		Total:     0,
		Breakdown: make(map[string]int),
		Users:     make([]models.ReactionUser, 0),
	}

	for _, result := range results {
		stats.Total += result.Count
		stats.Breakdown[result.Type] = result.Count

		for _, user := range result.Users {
			userUUID, err := uuid.Parse(user.UserID)
			if err != nil {
				continue // Skip invalid UUIDs
			}
			stats.Users = append(stats.Users, models.ReactionUser{
				UserID:    userUUID,
				Type:      user.Type,
				CreatedAt: user.CreatedAt,
			})
		}
	}

	return stats, nil
}

func (r *PostRepository) GetCommentReactionStats(ctx context.Context, commentID primitive.ObjectID) (*models.ReactionStats, error) {
	pipeline := []bson.M{
		{"$match": bson.M{"comment_id": commentID}},
		{
			"$group": bson.M{
				"_id":   "$type",
				"count": bson.M{"$sum": 1},
				"users": bson.M{"$push": bson.M{
					"user_id":    "$user_id",
					"type":       "$type",
					"created_at": "$created_at",
				}},
			},
		},
		{
			"$sort": bson.M{"count": -1},
		},
	}

	cursor, err := r.likes.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []struct {
		Type  string `bson:"_id"`
		Count int    `bson:"count"`
		Users []struct {
			UserID    string    `bson:"user_id"`
			Type      string    `bson:"type"`
			CreatedAt time.Time `bson:"created_at"`
		} `bson:"users"`
	}

	if err := cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	stats := &models.ReactionStats{
		Total:     0,
		Breakdown: make(map[string]int),
		Users:     make([]models.ReactionUser, 0),
	}

	for _, result := range results {
		stats.Total += result.Count
		stats.Breakdown[result.Type] = result.Count

		for _, user := range result.Users {
			userUUID, err := uuid.Parse(user.UserID)
			if err != nil {
				continue // Skip invalid UUIDs
			}
			stats.Users = append(stats.Users, models.ReactionUser{
				UserID:    userUUID,
				Type:      user.Type,
				CreatedAt: user.CreatedAt,
			})
		}
	}

	return stats, nil
}


// Share operations
func (r *PostRepository) CreateShare(ctx context.Context, share *models.Share) error {
	share.CreatedAt = time.Now()

	// Try to use transactions first (for replica sets/sharded clusters)
	// Fall back to non-transactional operations for standalone MongoDB
	err := r.createShareWithTransaction(ctx, share)
	if err != nil {
		// Check if error is related to transactions not being supported
		if database.IsTransactionUnsupportedError(err) {
			// Fall back to non-transactional operation
			return r.createShareWithoutTransaction(ctx, share)
		}
		return err
	}
	return nil
}

// createShareWithTransaction uses MongoDB transactions for atomicity
func (r *PostRepository) createShareWithTransaction(ctx context.Context, share *models.Share) error {
	return database.WithTransaction(ctx, r.client, func(sc mongo.SessionContext) error {
		return r.performCreateShareOperation(sc, share)
	})
}

// createShareWithoutTransaction performs create share operation without transactions
func (r *PostRepository) createShareWithoutTransaction(ctx context.Context, share *models.Share) error {
	return r.performCreateShareOperation(ctx, share)
}

// performCreateShareOperation contains the core logic for creating shares
func (r *PostRepository) performCreateShareOperation(ctx context.Context, share *models.Share) error {
	result, err := r.shares.InsertOne(ctx, share)
	if err != nil {
		return err
	}

	share.ID = result.InsertedID.(primitive.ObjectID)

	// Atomically increment post share count
	return r.AtomicIncrementShare(ctx, share.PostID)
}

func (r *PostRepository) DeleteShare(ctx context.Context, userID uuid.UUID, postID primitive.ObjectID) error {
	// Try to use transactions first (for replica sets/sharded clusters)
	// Fall back to non-transactional operations for standalone MongoDB
	err := r.deleteShareWithTransaction(ctx, userID, postID)
	if err != nil {
		// Check if error is related to transactions not being supported
		if database.IsTransactionUnsupportedError(err) {
			// Fall back to non-transactional operation
			return r.deleteShareWithoutTransaction(ctx, userID, postID)
		}
		return err
	}
	return nil
}

// deleteShareWithTransaction uses MongoDB transactions for atomicity
func (r *PostRepository) deleteShareWithTransaction(ctx context.Context, userID uuid.UUID, postID primitive.ObjectID) error {
	return database.WithTransaction(ctx, r.client, func(sc mongo.SessionContext) error {
		return r.performDeleteShareOperation(sc, userID, postID)
	})
}

// deleteShareWithoutTransaction performs delete share operation without transactions
func (r *PostRepository) deleteShareWithoutTransaction(ctx context.Context, userID uuid.UUID, postID primitive.ObjectID) error {
	return r.performDeleteShareOperation(ctx, userID, postID)
}

// performDeleteShareOperation contains the core logic for deleting shares
func (r *PostRepository) performDeleteShareOperation(ctx context.Context, userID uuid.UUID, postID primitive.ObjectID) error {
	filter := bson.M{
		"user_id": userID,
		"post_id": postID,
	}

	result, err := r.shares.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}

	// If share was actually deleted, decrement count
	if result.DeletedCount > 0 {
		return r.AtomicDecrementShare(ctx, postID)
	}

	return nil
}

func (r *PostRepository) GetUserShare(ctx context.Context, userID uuid.UUID, postID primitive.ObjectID) (*models.Share, error) {
	filter := bson.M{
		"user_id": userID,
		"post_id": postID,
	}

	var share models.Share
	err := r.shares.FindOne(ctx, filter).Decode(&share)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &share, nil
}

// GetPostShares retrieves post shares with cursor-based pagination
func (r *PostRepository) GetPostShares(ctx context.Context, postID primitive.ObjectID, request *models.PaginationRequest) ([]models.Share, bool, error) {
	baseFilter := bson.M{"post_id": postID}

	queryBuilder := NewPaginationQueryBuilder(baseFilter)
	query, err := queryBuilder.BuildTimeBasedQuery(request, models.CollectionTypeShares)
	if err != nil {
		return nil, false, err
	}

	cursor, err := r.shares.Find(ctx, query.Filter, query.ToFindOptions())
	if err != nil {
		return nil, false, err
	}
	defer cursor.Close(ctx)

	var shares []models.Share
	if err := cursor.All(ctx, &shares); err != nil {
		return nil, false, err
	}

	// Process results for pagination
	finalShares, hasMore := ProcessResultsForPagination(shares, request.Limit)
	finalShares = ReverseSliceIfNeeded(finalShares, models.PaginationDirection(request.Direction))

	return finalShares, hasMore, nil
}
