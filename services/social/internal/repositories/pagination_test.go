package repositories

import (
	"testing"
	"time"

	"github.com/swork-team/platform/services/social/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestCursorEncodingDecoding(t *testing.T) {
	// Test cursor creation and encoding/decoding
	testCases := []struct {
		name        string
		cursorData  *models.CursorData
		expectError bool
	}{
		{
			name: "Valid post cursor",
			cursorData: &models.CursorData{
				Timestamp:      time.Now(),
				ID:             primitive.NewObjectID(),
				CollectionType: models.CollectionTypePosts,
			},
			expectError: false,
		},
		{
			name: "Valid comment cursor",
			cursorData: &models.CursorData{
				Timestamp:      time.Now(),
				ID:             primitive.NewObjectID(),
				CollectionType: models.CollectionTypeComments,
			},
			expectError: false,
		},
		{
			name: "Cursor with engagement score",
			cursorData: &models.CursorData{
				Timestamp:       time.Now(),
				ID:              primitive.NewObjectID(),
				EngagementScore: &[]float64{95.5}[0],
				CollectionType:  models.CollectionTypePosts,
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test encoding
			encoded, err := tc.cursorData.EncodeCursor()
			if tc.expectError && err == nil {
				t.Error("Expected error but got none")
				return
			}
			if !tc.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if tc.expectError {
				return // Skip decoding test for error cases
			}

			// Test decoding
			decoded, err := models.DecodeCursor(encoded)
			if err != nil {
				t.Errorf("Error decoding cursor: %v", err)
				return
			}

			// Verify decoded data matches original
			if decoded.ID != tc.cursorData.ID {
				t.Errorf("ID mismatch: got %v, want %v", decoded.ID, tc.cursorData.ID)
			}
			if decoded.CollectionType != tc.cursorData.CollectionType {
				t.Errorf("CollectionType mismatch: got %v, want %v", decoded.CollectionType, tc.cursorData.CollectionType)
			}
			if !decoded.Timestamp.Equal(tc.cursorData.Timestamp) {
				t.Errorf("Timestamp mismatch: got %v, want %v", decoded.Timestamp, tc.cursorData.Timestamp)
			}
		})
	}
}

func TestPaginationQueryBuilder(t *testing.T) {
	baseFilter := bson.M{
		"is_deleted": false,
		"visibility": "public",
	}

	builder := NewPaginationQueryBuilder(baseFilter)

	testCases := []struct {
		name    string
		request *models.PaginationRequest
		wantErr bool
	}{
		{
			name: "Forward pagination without cursor",
			request: &models.PaginationRequest{
				Limit:     20,
				Direction: "forward",
			},
			wantErr: false,
		},
		{
			name: "Backward pagination without cursor",
			request: &models.PaginationRequest{
				Limit:     10,
				Direction: "backward",
			},
			wantErr: false,
		},
		{
			name: "Invalid direction",
			request: &models.PaginationRequest{
				Limit:     20,
				Direction: "invalid",
			},
			wantErr: true, // Should return error for invalid direction
		},
		{
			name: "Large limit",
			request: &models.PaginationRequest{
				Limit:     1000,
				Direction: "forward",
			},
			wantErr: false, // Should be capped to 100
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			query, err := builder.BuildTimeBasedQuery(tc.request, models.CollectionTypePosts)

			if tc.wantErr && err == nil {
				t.Error("Expected error but got none")
				return
			}
			if !tc.wantErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if tc.wantErr {
				return
			}

			// Verify query structure
			if query == nil {
				t.Error("Query is nil")
				return
			}

			if query.Filter == nil {
				t.Error("Filter is nil")
				return
			}

			if query.Limit <= 0 {
				t.Error("Limit should be positive")
			}

			// Verify base filter is included
			if query.Filter["is_deleted"] != false {
				t.Error("Base filter not properly included")
			}
		})
	}
}

func TestPaginationWithCursor(t *testing.T) {
	// Create a test cursor
	now := time.Now()
	testID := primitive.NewObjectID()
	
	cursor := &models.CursorData{
		Timestamp:      now,
		ID:             testID,
		CollectionType: models.CollectionTypePosts,
	}

	encoded, err := cursor.EncodeCursor()
	if err != nil {
		t.Fatalf("Error encoding cursor: %v", err)
	}

	request := &models.PaginationRequest{
		Cursor:    encoded,
		Limit:     20,
		Direction: "forward",
	}

	baseFilter := bson.M{"is_deleted": false}
	builder := NewPaginationQueryBuilder(baseFilter)

	query, err := builder.BuildTimeBasedQuery(request, models.CollectionTypePosts)
	if err != nil {
		t.Fatalf("Error building query: %v", err)
	}

	// Verify that cursor filter is applied
	if query.Filter == nil {
		t.Fatal("Filter is nil")
	}

	// The filter should be an $and query with base filter and cursor filter
	andFilter, ok := query.Filter["$and"]
	if !ok {
		t.Error("Expected $and filter with cursor")
	}

	andSlice, ok := andFilter.([]bson.M)
	if !ok || len(andSlice) != 2 {
		t.Error("Expected $and filter with 2 conditions")
	}
}

func TestProcessResultsForPagination(t *testing.T) {
	// Create test posts
	posts := []models.Post{
		{ID: primitive.NewObjectID(), CreatedAt: time.Now()},
		{ID: primitive.NewObjectID(), CreatedAt: time.Now()},
		{ID: primitive.NewObjectID(), CreatedAt: time.Now()},
	}

	testCases := []struct {
		name           string
		results        []models.Post
		requestedLimit int
		expectHasMore  bool
		expectLength   int
	}{
		{
			name:           "Fewer results than limit",
			results:        posts[:2],
			requestedLimit: 3,
			expectHasMore:  false,
			expectLength:   2,
		},
		{
			name:           "Equal results to limit",
			results:        posts,
			requestedLimit: 3,
			expectHasMore:  false,
			expectLength:   3,
		},
		{
			name:           "More results than limit (with +1 for hasMore check)",
			results:        append(posts, models.Post{ID: primitive.NewObjectID()}),
			requestedLimit: 3,
			expectHasMore:  true,
			expectLength:   3,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			finalResults, hasMore := ProcessResultsForPagination(tc.results, tc.requestedLimit)

			if hasMore != tc.expectHasMore {
				t.Errorf("HasMore mismatch: got %v, want %v", hasMore, tc.expectHasMore)
			}

			if len(finalResults) != tc.expectLength {
				t.Errorf("Length mismatch: got %v, want %v", len(finalResults), tc.expectLength)
			}
		})
	}
}

func TestReverseSliceIfNeeded(t *testing.T) {
	originalPosts := []models.Post{
		{ID: primitive.NewObjectID(), CreatedAt: time.Now()},
		{ID: primitive.NewObjectID(), CreatedAt: time.Now().Add(time.Hour)},
		{ID: primitive.NewObjectID(), CreatedAt: time.Now().Add(2 * time.Hour)},
	}

	// Test forward direction (no reverse) - make a copy
	forwardPosts := make([]models.Post, len(originalPosts))
	copy(forwardPosts, originalPosts)
	forwardResult := ReverseSliceIfNeeded(forwardPosts, models.PaginationDirectionForward)
	if forwardResult[0].ID != originalPosts[0].ID {
		t.Error("Forward direction should not reverse the slice")
	}

	// Test backward direction (should reverse) - make a copy
	backwardPosts := make([]models.Post, len(originalPosts))
	copy(backwardPosts, originalPosts)
	backwardResult := ReverseSliceIfNeeded(backwardPosts, models.PaginationDirectionBackward)
	if backwardResult[0].ID != originalPosts[2].ID {
		t.Error("Backward direction should reverse the slice")
	}
}

func TestPaginationRequestValidation(t *testing.T) {
	testCases := []struct {
		name    string
		request *models.PaginationRequest
		wantErr bool
	}{
		{
			name: "Valid request",
			request: &models.PaginationRequest{
				Limit:     20,
				Direction: "forward",
			},
			wantErr: false,
		},
		{
			name: "Zero limit",
			request: &models.PaginationRequest{
				Limit:     0,
				Direction: "forward",
			},
			wantErr: false, // Should be normalized to default
		},
		{
			name: "Negative limit",
			request: &models.PaginationRequest{
				Limit:     -5,
				Direction: "forward",
			},
			wantErr: false, // Should be normalized to default
		},
		{
			name: "Excessive limit",
			request: &models.PaginationRequest{
				Limit:     1000,
				Direction: "forward",
			},
			wantErr: false, // Should be capped to 100
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.request.ValidateAndNormalize()

			if tc.wantErr && err == nil {
				t.Error("Expected error but got none")
				return
			}
			if !tc.wantErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Check normalization
			if tc.request.Limit <= 0 || tc.request.Limit > 100 {
				t.Errorf("Limit not properly normalized: %d", tc.request.Limit)
			}

			if tc.request.Direction != "forward" && tc.request.Direction != "backward" {
				t.Errorf("Direction not properly normalized: %s", tc.request.Direction)
			}
		})
	}
}

func BenchmarkCursorEncoding(b *testing.B) {
	cursor := &models.CursorData{
		Timestamp:      time.Now(),
		ID:             primitive.NewObjectID(),
		CollectionType: models.CollectionTypePosts,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := cursor.EncodeCursor()
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkCursorDecoding(b *testing.B) {
	cursor := &models.CursorData{
		Timestamp:      time.Now(),
		ID:             primitive.NewObjectID(),
		CollectionType: models.CollectionTypePosts,
	}

	encoded, err := cursor.EncodeCursor()
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := models.DecodeCursor(encoded)
		if err != nil {
			b.Fatal(err)
		}
	}
}