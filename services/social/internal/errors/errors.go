package errors

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/utils"
)

// StandardError represents a standardized error with consistent structure
type StandardError struct {
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	StatusCode int                    `json:"-"`
	Details    map[string]interface{} `json:"details,omitempty"`
}

func (e *StandardError) Error() string {
	return e.Message
}

// Predefined error codes and messages for consistency
var (
	// Resource Not Found Errors
	ErrPostNotFound = &StandardError{
		Code:       "POST_NOT_FOUND",
		Message:    "Post not found",
		StatusCode: http.StatusNotFound,
	}
	
	ErrCommentNotFound = &StandardError{
		Code:       "COMMENT_NOT_FOUND", 
		Message:    "Comment not found",
		StatusCode: http.StatusNotFound,
	}
	
	ErrAttachmentNotFound = &StandardError{
		Code:       "ATTACHMENT_NOT_FOUND",
		Message:    "Attachment not found",
		StatusCode: http.StatusNotFound,
	}

	ErrUserNotFound = &StandardError{
		Code:       "USER_NOT_FOUND",
		Message:    "User not found",
		StatusCode: http.StatusNotFound,
	}

	// Access Control Errors
	ErrAccessDenied = &StandardError{
		Code:       "ACCESS_DENIED",
		Message:    "Access denied",
		StatusCode: http.StatusForbidden,
	}

	ErrUnauthorized = &StandardError{
		Code:       "UNAUTHORIZED", 
		Message:    "Authentication required",
		StatusCode: http.StatusUnauthorized,
	}

	ErrInsufficientPermissions = &StandardError{
		Code:       "INSUFFICIENT_PERMISSIONS",
		Message:    "Insufficient permissions for this operation",
		StatusCode: http.StatusForbidden,
	}

	// Validation Errors
	ErrInvalidRequest = &StandardError{
		Code:       "INVALID_REQUEST",
		Message:    "Invalid request parameters",
		StatusCode: http.StatusBadRequest,
	}

	ErrInvalidPostID = &StandardError{
		Code:       "INVALID_POST_ID",
		Message:    "Invalid post ID format",
		StatusCode: http.StatusBadRequest,
	}

	ErrInvalidCommentID = &StandardError{
		Code:       "INVALID_COMMENT_ID",
		Message:    "Invalid comment ID format", 
		StatusCode: http.StatusBadRequest,
	}

	ErrInvalidUserID = &StandardError{
		Code:       "INVALID_USER_ID",
		Message:    "Invalid user ID format",
		StatusCode: http.StatusBadRequest,
	}

	ErrInvalidAttachmentID = &StandardError{
		Code:       "INVALID_ATTACHMENT_ID",
		Message:    "Invalid attachment ID format",
		StatusCode: http.StatusBadRequest,
	}

	ErrInvalidCursor = &StandardError{
		Code:       "INVALID_CURSOR",
		Message:    "Invalid pagination cursor format",
		StatusCode: http.StatusBadRequest,
	}

	ErrInvalidCursorType = &StandardError{
		Code:       "INVALID_CURSOR_TYPE",
		Message:    "Cursor type mismatch for this endpoint",
		StatusCode: http.StatusBadRequest,
	}

	ErrEmptyContent = &StandardError{
		Code:       "EMPTY_CONTENT",
		Message:    "Content cannot be empty",
		StatusCode: http.StatusBadRequest,
	}

	ErrInvalidFileType = &StandardError{
		Code:       "INVALID_FILE_TYPE",
		Message:    "Invalid file type",
		StatusCode: http.StatusUnsupportedMediaType,
	}

	ErrFileSizeExceeded = &StandardError{
		Code:       "FILE_SIZE_EXCEEDED",
		Message:    "File size exceeds limit",
		StatusCode: http.StatusRequestEntityTooLarge,
	}

	// Rate Limiting Errors
	ErrRateLimitExceeded = &StandardError{
		Code:       "RATE_LIMIT_EXCEEDED",
		Message:    "Rate limit exceeded",
		StatusCode: http.StatusTooManyRequests,
	}

	// Server Errors
	ErrInternalServer = &StandardError{
		Code:       "INTERNAL_SERVER_ERROR",
		Message:    "Internal server error",
		StatusCode: http.StatusInternalServerError,
	}

	ErrDatabaseError = &StandardError{
		Code:       "DATABASE_ERROR",
		Message:    "Database operation failed",
		StatusCode: http.StatusInternalServerError,
	}

	ErrServiceUnavailable = &StandardError{
		Code:       "SERVICE_UNAVAILABLE",
		Message:    "Service temporarily unavailable",
		StatusCode: http.StatusServiceUnavailable,
	}
)

// WithDetails adds additional context to an error
func (e *StandardError) WithDetails(details map[string]interface{}) *StandardError {
	newErr := *e // Copy the error
	if newErr.Details == nil {
		newErr.Details = make(map[string]interface{})
	}
	for k, v := range details {
		newErr.Details[k] = v
	}
	return &newErr
}

// WithMessage overrides the default message
func (e *StandardError) WithMessage(message string) *StandardError {
	newErr := *e // Copy the error
	newErr.Message = message
	return &newErr
}

// RespondWithError sends a standardized error response
func RespondWithError(c *gin.Context, err *StandardError) {
	utils.ErrorResponse(c, err.StatusCode, err.Message, err)
}

// RespondWithStandardError handles both StandardError and regular errors
func RespondWithStandardError(c *gin.Context, err error) {
	if stdErr, ok := err.(*StandardError); ok {
		RespondWithError(c, stdErr)
		return
	}
	
	// For non-standard errors, use internal server error
	RespondWithError(c, ErrInternalServer.WithDetails(map[string]interface{}{
		"original_error": err.Error(),
	}))
}

// Validation helper functions
func NewValidationError(field, message string) *StandardError {
	return ErrInvalidRequest.WithDetails(map[string]interface{}{
		"field":   field,
		"message": message,
	})
}

func NewNotFoundError(resource, id string) *StandardError {
	switch resource {
	case "post":
		return ErrPostNotFound.WithDetails(map[string]interface{}{"post_id": id})
	case "comment":
		return ErrCommentNotFound.WithDetails(map[string]interface{}{"comment_id": id})
	case "attachment":
		return ErrAttachmentNotFound.WithDetails(map[string]interface{}{"attachment_id": id})
	case "user":
		return ErrUserNotFound.WithDetails(map[string]interface{}{"user_id": id})
	default:
		return &StandardError{
			Code:       "RESOURCE_NOT_FOUND",
			Message:    fmt.Sprintf("%s not found", resource),
			StatusCode: http.StatusNotFound,
			Details:    map[string]interface{}{resource + "_id": id},
		}
	}
}

func NewAccessDeniedError(resource, action string) *StandardError {
	return ErrAccessDenied.WithDetails(map[string]interface{}{
		"resource": resource,
		"action":   action,
	})
}

func NewInvalidCursorError(reason string) *StandardError {
	return ErrInvalidCursor.WithDetails(map[string]interface{}{
		"reason": reason,
	})
}

func NewInvalidCursorTypeError(expectedType, actualType string) *StandardError {
	return ErrInvalidCursorType.WithDetails(map[string]interface{}{
		"expected_type": expectedType,
		"actual_type":   actualType,
	})
}