package models

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"github.com/swork-team/platform/services/social/internal/errors"
)

// PaginationDirection represents the direction of cursor-based pagination
type PaginationDirection string

const (
	PaginationDirectionForward  PaginationDirection = "forward"
	PaginationDirectionBackward PaginationDirection = "backward"
)

// CursorData represents the data encoded in a pagination cursor
type CursorData struct {
	// Primary sort field (usually created_at timestamp)
	Timestamp time.Time `json:"timestamp"`
	
	// Secondary sort field (ObjectID for uniqueness)
	ID primitive.ObjectID `json:"id"`
	
	// Additional fields for complex sorting scenarios
	EngagementScore *float64 `json:"engagement_score,omitempty"` // For trending posts
	RelevanceScore  *float64 `json:"relevance_score,omitempty"`  // For search results
	
	// Collection type for type safety
	CollectionType string `json:"collection_type"`
}

// PaginationRequest represents a cursor-based pagination request
type PaginationRequest struct {
	// Cursor for pagination (base64 encoded CursorData)
	Cursor string `json:"cursor,omitempty" form:"cursor"`
	
	// Number of items to return (1-100)
	Limit int `json:"limit,omitempty" form:"limit"`
	
	// Direction of pagination
	Direction PaginationDirection `json:"direction,omitempty" form:"direction"`
}

// PaginationResponse represents a cursor-based pagination response
type PaginationResponse struct {
	// Current page cursor (for this page)
	CurrentCursor string `json:"current_cursor,omitempty"`
	
	// Next page cursor (null if no more pages)
	NextCursor *string `json:"next_cursor"`
	
	// Previous page cursor (null if first page)
	PrevCursor *string `json:"prev_cursor"`
	
	// Number of items in current page
	Count int `json:"count"`
	
	// Whether there are more items after this page
	HasNext bool `json:"has_next"`
	
	// Whether there are items before this page
	HasPrev bool `json:"has_prev"`
	
	// Request limit for reference
	Limit int `json:"limit"`
}

// PostsPaginationResponse wraps posts with pagination metadata
type PostsPaginationResponse struct {
	Posts      []Post             `json:"posts"`
	Pagination PaginationResponse `json:"pagination"`
}

// CommentsPaginationResponse wraps comments with pagination metadata
type CommentsPaginationResponse struct {
	Comments   []Comment          `json:"comments"`
	Pagination PaginationResponse `json:"pagination"`
}

// LikesPaginationResponse wraps likes with pagination metadata
type LikesPaginationResponse struct {
	Likes      []Like             `json:"likes"`
	Pagination PaginationResponse `json:"pagination"`
}

// SharesPaginationResponse wraps shares with pagination metadata
type SharesPaginationResponse struct {
	Shares     []Share            `json:"shares"`
	Pagination PaginationResponse `json:"pagination"`
}

// Collection type constants for cursor validation
const (
	CollectionTypePosts    = "posts"
	CollectionTypeComments = "comments"
	CollectionTypeLikes    = "likes"
	CollectionTypeShares   = "shares"
)

// EncodeCursor creates a base64-encoded cursor from CursorData
func (cd *CursorData) EncodeCursor() (string, error) {
	if cd == nil {
		return "", nil
	}
	
	jsonData, err := json.Marshal(cd)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor data: %w", err)
	}
	
	return base64.URLEncoding.EncodeToString(jsonData), nil
}

// DecodeCursor decodes a base64-encoded cursor into CursorData
func DecodeCursor(cursor string) (*CursorData, error) {
	if cursor == "" {
		return nil, nil
	}
	
	jsonData, err := base64.URLEncoding.DecodeString(cursor)
	if err != nil {
		return nil, errors.NewInvalidCursorError("invalid base64 encoding")
	}
	
	var cd CursorData
	if err := json.Unmarshal(jsonData, &cd); err != nil {
		return nil, errors.NewInvalidCursorError("invalid cursor data format")
	}
	
	// Validate required fields
	if cd.ID.IsZero() {
		return nil, errors.NewInvalidCursorError("missing or invalid ID field")
	}
	
	if cd.Timestamp.IsZero() {
		return nil, errors.NewInvalidCursorError("missing or invalid timestamp field")
	}
	
	if cd.CollectionType == "" {
		return nil, errors.NewInvalidCursorError("missing collection_type field")
	}
	
	return &cd, nil
}

// ValidateCollectionType ensures cursor is for the expected collection
func (cd *CursorData) ValidateCollectionType(expectedType string) error {
	if cd == nil {
		return nil
	}
	
	if cd.CollectionType != expectedType {
		return errors.NewInvalidCursorTypeError(expectedType, cd.CollectionType)
	}
	
	return nil
}

// CreatePostCursor creates a cursor for post pagination
func CreatePostCursor(post *Post, collectionType string) (*CursorData, error) {
	if post == nil {
		return nil, nil
	}
	
	cursor := &CursorData{
		Timestamp:      post.CreatedAt,
		ID:             post.ID,
		CollectionType: collectionType,
	}
	
	return cursor, nil
}

// CreateTrendingPostCursor creates a cursor for trending post pagination with engagement score
func CreateTrendingPostCursor(post *Post, engagementScore float64) (*CursorData, error) {
	if post == nil {
		return nil, nil
	}
	
	cursor := &CursorData{
		Timestamp:       post.CreatedAt,
		ID:              post.ID,
		EngagementScore: &engagementScore,
		CollectionType:  CollectionTypePosts,
	}
	
	return cursor, nil
}

// CreateCommentCursor creates a cursor for comment pagination
func CreateCommentCursor(comment *Comment) (*CursorData, error) {
	if comment == nil {
		return nil, nil
	}
	
	cursor := &CursorData{
		Timestamp:      comment.CreatedAt,
		ID:             comment.ID,
		CollectionType: CollectionTypeComments,
	}
	
	return cursor, nil
}

// CreateLikeCursor creates a cursor for like pagination
func CreateLikeCursor(like *Like) (*CursorData, error) {
	if like == nil {
		return nil, nil
	}
	
	cursor := &CursorData{
		Timestamp:      like.CreatedAt,
		ID:             like.ID,
		CollectionType: CollectionTypeLikes,
	}
	
	return cursor, nil
}

// CreateShareCursor creates a cursor for share pagination
func CreateShareCursor(share *Share) (*CursorData, error) {
	if share == nil {
		return nil, nil
	}
	
	cursor := &CursorData{
		Timestamp:      share.CreatedAt,
		ID:             share.ID,
		CollectionType: CollectionTypeShares,
	}
	
	return cursor, nil
}

// DefaultPaginationRequest creates a default pagination request
func DefaultPaginationRequest() *PaginationRequest {
	return &PaginationRequest{
		Limit:     20,
		Direction: PaginationDirectionForward,
	}
}

// ValidateAndNormalize validates and normalizes pagination parameters
func (pr *PaginationRequest) ValidateAndNormalize() error {
	// Validate limit
	if pr.Limit <= 0 {
		pr.Limit = 20 // Default
	}
	if pr.Limit > 100 {
		pr.Limit = 100 // Maximum
	}
	
	// Validate direction
	if pr.Direction == "" {
		pr.Direction = PaginationDirectionForward
	}
	if pr.Direction != PaginationDirectionForward && pr.Direction != PaginationDirectionBackward {
		return errors.NewValidationError("direction", fmt.Sprintf("invalid pagination direction: %s. Must be 'forward' or 'backward'", pr.Direction))
	}
	
	return nil
}


// BuildPaginationResponse builds a pagination response with cursors
func BuildPaginationResponse(items interface{}, request *PaginationRequest, hasMore bool) (*PaginationResponse, error) {
	response := &PaginationResponse{
		Count:   getItemCount(items),
		HasNext: hasMore,
		HasPrev: request.Cursor != "",
		Limit:   request.Limit,
	}
	
	// Set current cursor if provided
	if request.Cursor != "" {
		response.CurrentCursor = request.Cursor
	}
	
	// Generate next cursor if there are more items
	if hasMore && response.Count > 0 {
		lastItem := getLastItem(items)
		if cursor, err := createCursorForItem(lastItem); err == nil {
			if encodedCursor, err := cursor.EncodeCursor(); err == nil {
				response.NextCursor = &encodedCursor
			}
		}
	}
	
	// Generate previous cursor if not on first page
	if request.Cursor != "" && response.Count > 0 {
		firstItem := getFirstItem(items)
		if cursor, err := createCursorForItem(firstItem); err == nil {
			// For previous cursor, we reverse the direction
			if encodedCursor, err := cursor.EncodeCursor(); err == nil {
				response.PrevCursor = &encodedCursor
			}
		}
	}
	
	return response, nil
}

// Helper functions for building pagination responses
func getItemCount(items interface{}) int {
	switch v := items.(type) {
	case []Post:
		return len(v)
	case []Comment:
		return len(v)
	case []Like:
		return len(v)
	case []Share:
		return len(v)
	default:
		return 0
	}
}

func getLastItem(items interface{}) interface{} {
	switch v := items.(type) {
	case []Post:
		if len(v) > 0 {
			return &v[len(v)-1]
		}
	case []Comment:
		if len(v) > 0 {
			return &v[len(v)-1]
		}
	case []Like:
		if len(v) > 0 {
			return &v[len(v)-1]
		}
	case []Share:
		if len(v) > 0 {
			return &v[len(v)-1]
		}
	}
	return nil
}

func getFirstItem(items interface{}) interface{} {
	switch v := items.(type) {
	case []Post:
		if len(v) > 0 {
			return &v[0]
		}
	case []Comment:
		if len(v) > 0 {
			return &v[0]
		}
	case []Like:
		if len(v) > 0 {
			return &v[0]
		}
	case []Share:
		if len(v) > 0 {
			return &v[0]
		}
	}
	return nil
}

func createCursorForItem(item interface{}) (*CursorData, error) {
	switch v := item.(type) {
	case *Post:
		return CreatePostCursor(v, CollectionTypePosts)
	case *Comment:
		return CreateCommentCursor(v)
	case *Like:
		return CreateLikeCursor(v)
	case *Share:
		return CreateShareCursor(v)
	default:
		return nil, fmt.Errorf("unsupported item type for cursor creation")
	}
}