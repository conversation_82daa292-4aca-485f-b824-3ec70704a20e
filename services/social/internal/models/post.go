package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/clients"
	sharedModels "github.com/swork-team/platform/pkg/models"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Post struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	AuthorID   uuid.UUID          `json:"author_id" bson:"author_id"`
	TeamID     *uuid.UUID         `json:"team_id,omitempty" bson:"team_id,omitempty"`
	Content    string             `json:"content" bson:"content"`
	Type       PostType           `json:"type" bson:"type"`
	Visibility PostVisibility     `json:"visibility" bson:"visibility"`
	Tags       []string           `json:"tags,omitempty" bson:"tags,omitempty"`
	Mentions   []string           `json:"mentions,omitempty" bson:"mentions,omitempty"`
	Location   *Location          `json:"location,omitempty" bson:"location,omitempty"`

	// Attachments
	AttachmentIDs []uuid.UUID `json:"attachment_ids,omitempty" bson:"attachment_ids,omitempty"`

	// Engagement metrics
	LikeCount    int64 `json:"like_count" bson:"like_count"`
	CommentCount int64 `json:"comment_count" bson:"comment_count"`
	ShareCount   int64 `json:"share_count" bson:"share_count"`
	ViewCount    int64 `json:"view_count" bson:"view_count"`

	// Moderation
	IsDeleted bool       `json:"is_deleted" bson:"is_deleted"`
	IsFlagged bool       `json:"is_flagged" bson:"is_flagged"`
	DeletedAt time.Time  `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
	DeletedBy *uuid.UUID `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`

	CreatedAt time.Time `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time `json:"updated_at" bson:"updated_at"`

	// Virtual fields (populated from other services)
	Author      *clients.User                  `json:"author,omitempty" bson:"-"`
	Team        *clients.Team                  `json:"team,omitempty" bson:"-"`
	UserLike    *Like                          `json:"user_like,omitempty" bson:"-"`
	Comments    []Comment                      `json:"comments,omitempty" bson:"-"`
	Attachments []sharedModels.AttachmentBasic `json:"attachments,omitempty" bson:"-"`
}

type Comment struct {
	ID       primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	PostID   primitive.ObjectID `json:"post_id" bson:"post_id"`
	AuthorID uuid.UUID          `json:"author_id" bson:"author_id"`
	ParentID primitive.ObjectID `json:"parent_id,omitempty" bson:"parent_id,omitempty"`
	Content  string             `json:"content" bson:"content"`

	// Attachments
	AttachmentIDs []uuid.UUID `json:"attachment_ids,omitempty" bson:"attachment_ids,omitempty"`

	// Engagement
	LikeCount int64 `json:"like_count" bson:"like_count"`

	// Moderation
	IsDeleted bool       `json:"is_deleted" bson:"is_deleted"`
	IsFlagged bool       `json:"is_flagged" bson:"is_flagged"`
	DeletedAt time.Time  `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
	DeletedBy *uuid.UUID `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`

	CreatedAt time.Time `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time `json:"updated_at" bson:"updated_at"`

	// Virtual fields
	Author        *clients.User                  `json:"author,omitempty" bson:"-"`
	UserLike      *Like                          `json:"user_like,omitempty" bson:"-"`
	ReactionStats *ReactionStats                 `json:"reaction_stats,omitempty" bson:"-"`
	Replies       []Comment                      `json:"replies,omitempty" bson:"-"`
	Attachments   []sharedModels.AttachmentBasic `json:"attachments,omitempty" bson:"-"`
}

type Like struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID    uuid.UUID          `json:"user_id" bson:"user_id"`
	PostID    primitive.ObjectID `json:"post_id,omitempty" bson:"post_id,omitempty"`
	CommentID primitive.ObjectID `json:"comment_id,omitempty" bson:"comment_id,omitempty"`
	Type      LikeType           `json:"type" bson:"type"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`

	// Virtual field populated from user service
	User *sharedModels.UserBasic `json:"user,omitempty" bson:"-"`
}

type Share struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID     uuid.UUID          `json:"user_id" bson:"user_id"`
	PostID     primitive.ObjectID `json:"post_id" bson:"post_id"`
	TeamID     *uuid.UUID         `json:"team_id,omitempty" bson:"team_id,omitempty"`
	Comment    string             `json:"comment,omitempty" bson:"comment,omitempty"`
	Visibility PostVisibility     `json:"visibility" bson:"visibility"`
	CreatedAt  time.Time          `json:"created_at" bson:"created_at"`

	// Virtual fields
	User         *clients.User `json:"user,omitempty" bson:"-"`
	Team         *clients.Team `json:"team,omitempty" bson:"-"`
	OriginalPost *Post         `json:"original_post,omitempty" bson:"-"`
}

// Removed: Use sharedModels.AttachmentBasic instead

type Location struct {
	Name      string  `json:"name" bson:"name"`
	Latitude  float64 `json:"latitude" bson:"latitude"`
	Longitude float64 `json:"longitude" bson:"longitude"`
	Address   string  `json:"address,omitempty" bson:"address,omitempty"`
	PlaceID   string  `json:"place_id,omitempty" bson:"place_id,omitempty"`
}

type Report struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	PostID     primitive.ObjectID `json:"post_id,omitempty" bson:"post_id,omitempty"`
	CommentID  primitive.ObjectID `json:"comment_id,omitempty" bson:"comment_id,omitempty"`
	ReporterID uuid.UUID          `json:"reporter_id" bson:"reporter_id"`
	Reason     ReportReason       `json:"reason" bson:"reason"`
	Details    string             `json:"details,omitempty" bson:"details,omitempty"`
	Status     ReportStatus       `json:"status" bson:"status"`

	ReviewedBy *uuid.UUID `json:"reviewed_by,omitempty" bson:"reviewed_by,omitempty"`
	ReviewedAt time.Time  `json:"reviewed_at,omitempty" bson:"reviewed_at,omitempty"`
	Action     string     `json:"action,omitempty" bson:"action,omitempty"`

	CreatedAt time.Time `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time `json:"updated_at" bson:"updated_at"`
}

// Feed represents a user's personalized feed
type Feed struct {
	UserID    uuid.UUID            `json:"user_id" bson:"user_id"`
	PostIDs   []primitive.ObjectID `json:"post_ids" bson:"post_ids"`
	UpdatedAt time.Time            `json:"updated_at" bson:"updated_at"`
}

// Note: User and Team models are now imported from pkg/clients to eliminate duplication

// Enums
type PostType string

const (
	PostTypeText  PostType = "text"
	PostTypeImage PostType = "image"
	PostTypeVideo PostType = "video"
	PostTypeLink  PostType = "link"
	PostTypePoll  PostType = "poll"
	PostTypeEvent PostType = "event"
)

type PostVisibility string

const (
	PostVisibilityPublic  PostVisibility = "public"
	PostVisibilityFriends PostVisibility = "friends"
	PostVisibilityTeam    PostVisibility = "team"
	PostVisibilityPrivate PostVisibility = "private"
)

// Removed: Use sharedModels.AttachmentType instead

type LikeType string

const (
	LikeTypeLike  LikeType = "like"
	LikeTypeLove  LikeType = "love"
	LikeTypeHaha  LikeType = "haha"
	LikeTypeWow   LikeType = "wow"
	LikeTypeSad   LikeType = "sad"
	LikeTypeAngry LikeType = "angry"
)

type ReportReason string

const (
	ReportReasonSpam         ReportReason = "spam"
	ReportReasonHarassment   ReportReason = "harassment"
	ReportReasonHateSpeech   ReportReason = "hate_speech"
	ReportReasonViolence     ReportReason = "violence"
	ReportReasonNudity       ReportReason = "nudity"
	ReportReasonFalseInfo    ReportReason = "false_information"
	ReportReasonIntellectual ReportReason = "intellectual_property"
	ReportReasonOther        ReportReason = "other"
)

type ReportStatus string

const (
	ReportStatusPending   ReportStatus = "pending"
	ReportStatusReviewing ReportStatus = "reviewing"
	ReportStatusResolved  ReportStatus = "resolved"
	ReportStatusRejected  ReportStatus = "rejected"
)

// ReactionUser represents a user who reacted to a post/comment
type ReactionUser struct {
	UserID    uuid.UUID `json:"user_id" bson:"user_id"`
	Type      string    `json:"type" bson:"type"`
	CreatedAt time.Time `json:"created_at" bson:"created_at"`

	// Virtual field populated from user service
	User *sharedModels.UserBasic `json:"user,omitempty" bson:"-"`
}

// ReactionStats represents aggregated reaction statistics
type ReactionStats struct {
	Total     int            `json:"total" bson:"total"`
	Breakdown map[string]int `json:"breakdown" bson:"breakdown"`
	Users     []ReactionUser `json:"users" bson:"users"`
}

// Helper methods
func (p *Post) IsPublic() bool {
	return p.Visibility == PostVisibilityPublic
}

func (p *Post) IsTeamPost() bool {
	return p.TeamID != nil
}

func (p *Post) CanView(userID string, userTeams []string) bool {
	if p.IsDeleted {
		return false
	}

	if p.AuthorID.String() == userID {
		return true
	}

	switch p.Visibility {
	case PostVisibilityPublic:
		return true
	case PostVisibilityTeam:
		if p.TeamID == nil {
			return false
		}
		for _, teamID := range userTeams {
			if teamID == p.TeamID.String() {
				return true
			}
		}
		return false
	case PostVisibilityFriends:
		// This would need to check friendship status
		return false
	case PostVisibilityPrivate:
		return false
	default:
		return false
	}
}

func (c *Comment) CanView(userID string) bool {
	return !c.IsDeleted || c.AuthorID.String() == userID
}

func (c *Comment) IsReply() bool {
	return !c.ParentID.IsZero()
}
