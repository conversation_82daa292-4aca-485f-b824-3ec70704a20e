package handlers

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/services/social/internal/services"
)

// PermissionHandler handles post permission validation endpoints
type PermissionHandler struct {
	permissionService *services.PostPermissionService
}

// NewPermissionHandler creates a new permission handler
func NewPermissionHandler(permissionService *services.PostPermissionService) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
	}
}

// ValidatePostAccess validates if a user can access a specific post
// POST /api/v1/posts/validate-access
func (h *PermissionHandler) ValidatePostAccess(c *gin.Context) {
	var req services.PostPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Extract user teams from JWT claims if available
	if userInfo, exists := c.Get("user_info"); exists {
		if userMap, ok := userInfo.(map[string]interface{}); ok {
			if teams, ok := userMap["teams"].([]string); ok {
				req.UserTeams = teams
			}
		}
	}

	// Validate the request
	response, err := h.permissionService.ValidatePostAccess(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "failed to validate post access",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ValidateBatchPostAccess validates access to multiple posts in a single request
// POST /api/v1/posts/validate-batch-access
func (h *PermissionHandler) ValidateBatchPostAccess(c *gin.Context) {
	var requests []*services.PostPermissionRequest
	if err := c.ShouldBindJSON(&requests); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Limit batch size for performance
	if len(requests) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "batch size too large",
			"details": "maximum 100 requests per batch",
		})
		return
	}

	// Extract user teams from JWT claims for all requests
	var userTeams []string
	if userInfo, exists := c.Get("user_info"); exists {
		if userMap, ok := userInfo.(map[string]interface{}); ok {
			if teams, ok := userMap["teams"].([]string); ok {
				userTeams = teams
			}
		}
	}

	// Apply user teams to all requests that don't have them
	for _, req := range requests {
		if len(req.UserTeams) == 0 {
			req.UserTeams = userTeams
		}
	}

	// Validate all requests
	responses, err := h.permissionService.ValidateBatchPostAccess(c.Request.Context(), requests)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "failed to validate batch post access",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"results": responses,
		"count":   len(responses),
	})
}

// GetPostVisibilityInfo returns basic post visibility information
// GET /api/v1/posts/:id/visibility-info
func (h *PermissionHandler) GetPostVisibilityInfo(c *gin.Context) {
	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "post ID is required",
		})
		return
	}

	info, err := h.permissionService.GetPostVisibilityInfo(c.Request.Context(), postID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "post not found",
			})
		} else if strings.Contains(err.Error(), "invalid") {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "invalid post ID",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "failed to get post visibility info",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, info)
}

// InvalidatePostPermissionCache invalidates cached permissions for a post
// DELETE /api/v1/posts/:id/permission-cache
func (h *PermissionHandler) InvalidatePostPermissionCache(c *gin.Context) {
	postID := c.Param("id")
	if postID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "post ID is required",
		})
		return
	}

	err := h.permissionService.InvalidatePostPermissionCache(c.Request.Context(), postID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "failed to invalidate cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "cache invalidated successfully",
		"post_id": postID,
	})
}

// HealthCheck endpoint for permission service
// GET /api/v1/posts/permission-health
func (h *PermissionHandler) HealthCheck(c *gin.Context) {
	// Basic health check - could be extended with database connectivity checks
	c.JSON(http.StatusOK, gin.H{
		"service":   "post-permission-service",
		"status":    "healthy",
		"timestamp": fmt.Sprintf("%d", c.Request.Context().Value("timestamp")),
	})
}

// RegisterRoutes registers all permission-related routes
func (h *PermissionHandler) RegisterRoutes(router *gin.RouterGroup) {
	// Permission validation endpoints
	router.POST("/posts/validate-access", h.ValidatePostAccess)
	router.POST("/posts/validate-batch-access", h.ValidateBatchPostAccess)

	// Post visibility info endpoint
	router.GET("/posts/:id/visibility-info", h.GetPostVisibilityInfo)

	// Cache management endpoint
	router.DELETE("/posts/:id/permission-cache", h.InvalidatePostPermissionCache)

	// Health check endpoint
	router.GET("/posts/permission-health", h.HealthCheck)
}
