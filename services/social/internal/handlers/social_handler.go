package handlers

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/social/internal/errors"
	"github.com/swork-team/platform/services/social/internal/models"
	"github.com/swork-team/platform/services/social/internal/services"
)

// validateUUID is deprecated - use middleware.MustParseUUIDParam instead
// Keeping for backward compatibility during migration
func validateUUID(c *gin.Context, idStr, paramName string) (*uuid.UUID, bool) {
	parsedUUID := middleware.MustParseUUIDParam(c, paramName)
	if parsedUUID == uuid.Nil {
		return nil, false
	}
	return &parsedUUID, true
}

// convertPaginationRequest converts middleware.PaginationRequest to models.PaginationRequest
func convertPaginationRequest(middlewareReq *middleware.PaginationRequest) *models.PaginationRequest {
	return &models.PaginationRequest{
		Cursor:    middlewareReq.Cursor,
		Limit:     middlewareReq.Limit,
		Direction: models.PaginationDirection(middlewareReq.Direction),
	}
}

type SocialHandler struct {
	socialService *services.SocialService
}

func NewSocialHandler(socialService *services.SocialService) *SocialHandler {
	return &SocialHandler{
		socialService: socialService,
	}
}

// @Summary Create a new social media post
// @Description Create a new post with content, tags, mentions, and location data. Supports multiple post types including text, image, video, link, poll, and event. Posts can be public, friends-only, team-specific, or private.
// @Tags posts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.CreatePostRequest true "Post creation data with content, type, visibility, and optional metadata"
// @Success 201 {object} utils.APIResponse{data=models.Post} "Successfully created post with populated author and engagement data"
// @Failure 400 {object} utils.APIResponse "Invalid request data - missing required fields, invalid post type, or content validation errors"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided in Authorization header"
// @Failure 403 {object} utils.APIResponse "Access denied - user lacks permission to post to specified team or visibility level"
// @Failure 422 {object} utils.APIResponse "Validation failed - detailed field-level validation errors"
// @Failure 429 {object} utils.APIResponse "Rate limit exceeded - too many posts created in short time period"
// @Failure 500 {object} utils.APIResponse "Internal server error - database or service unavailable"
// @Router /social/posts [post]
func (h *SocialHandler) CreatePost(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)
	if userToken == "" {
		errors.RespondWithError(c, errors.ErrUnauthorized.WithMessage("Authorization token required"))
		return
	}

	var req services.CreatePostRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	ctx := c.Request.Context()
	ctx = context.WithValue(ctx, "user_token", userToken)

	post, err := h.socialService.CreatePost(ctx, userIDStr, &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, "Failed to create post", err, 
			"create_post", 
			logger.F("user_id", userIDStr),
			logger.F("post_type", req.Type),
			logger.F("visibility", req.Visibility))
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusCreated, "Post created successfully", post,
		"create_post",
		logger.F("user_id", userIDStr),
		logger.F("post_id", post.ID.Hex()),
		logger.F("post_type", post.Type),
		logger.F("visibility", post.Visibility),
		logger.F("content_length", len(req.Content)))
}

// @Summary Retrieve a specific post by ID
// @Description Get detailed information about a post including content, author, engagement metrics, and user's interaction status. Respects privacy settings and team membership for access control.
// @Tags posts
// @Security BearerAuth
// @Param id path string true "Post ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439011")
// @Success 200 {object} utils.APIResponse{data=models.Post} "Successfully retrieved post with full details including author info and engagement metrics"
// @Failure 400 {object} utils.APIResponse "Invalid post ID format - must be a valid 24-character hexadecimal ObjectID"
// @Failure 401 {object} utils.APIResponse "Authentication required for accessing non-public posts"
// @Failure 403 {object} utils.APIResponse "Access denied - insufficient permissions to view this post based on visibility settings"
// @Failure 404 {object} utils.APIResponse "Post not found - post may have been deleted or never existed"
// @Failure 500 {object} utils.APIResponse "Internal server error - database or service unavailable"
// @Router /social/posts/{id} [get]
func (h *SocialHandler) GetPost(c *gin.Context) {
	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	post, err := h.socialService.GetPost(ctx, postID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Post retrieved successfully", post)
}

// @Summary Update an existing post
// @Description Update post content, visibility, tags, mentions, or location. Only the post author can update their posts. All fields are optional - only provided fields will be updated.
// @Tags posts
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Post ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439011")
// @Param request body services.UpdatePostRequest true "Updated post data - all fields optional, only provided fields will be updated"
// @Success 200 {object} utils.APIResponse{data=models.Post} "Successfully updated post with new data and updated timestamp"
// @Failure 400 {object} utils.APIResponse "Invalid request data - malformed post ID or validation errors in update fields"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided"
// @Failure 403 {object} utils.APIResponse "Access denied - only post authors can update their own posts"
// @Failure 404 {object} utils.APIResponse "Post not found - post may have been deleted or never existed"
// @Failure 422 {object} utils.APIResponse "Validation failed - detailed field-level validation errors for update data"
// @Failure 500 {object} utils.APIResponse "Internal server error - database or service unavailable"
// @Router /social/posts/{id} [put]
func (h *SocialHandler) UpdatePost(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)
	if userToken == "" {
		errors.RespondWithError(c, errors.ErrUnauthorized.WithMessage("Authorization token required"))
		return
	}

	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	var req services.UpdatePostRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	ctx := c.Request.Context()
	ctx = context.WithValue(ctx, "user_token", userToken)

	post, err := h.socialService.UpdatePost(ctx, postID, userIDStr, &req)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, "Failed to update post", err,
			"update_post",
			logger.F("user_id", userIDStr),
			logger.F("post_id", postID))
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Post updated successfully", post,
		"update_post",
		logger.F("user_id", userIDStr),
		logger.F("post_id", postID),
		logger.F("post_type", post.Type),
		logger.F("visibility", post.Visibility))
}

// @Summary Delete a post (soft delete)
// @Description Permanently delete a post and all associated comments, likes, and shares. This is a soft delete operation - the post is marked as deleted but data is retained for audit purposes. Only the post author can delete their posts.
// @Tags posts
// @Security BearerAuth
// @Param id path string true "Post ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439011")
// @Success 200 {object} utils.APIResponse "Successfully deleted post - post is now marked as deleted and hidden from feeds"
// @Failure 400 {object} utils.APIResponse "Invalid post ID format - must be a valid 24-character hexadecimal ObjectID"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided"
// @Failure 403 {object} utils.APIResponse "Access denied - only post authors can delete their own posts"
// @Failure 404 {object} utils.APIResponse "Post not found - post may have already been deleted or never existed"
// @Failure 500 {object} utils.APIResponse "Internal server error - database or service unavailable"
// @Router /social/posts/{id} [delete]
func (h *SocialHandler) DeletePost(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	err := h.socialService.DeletePost(c.Request.Context(), postID, userIDStr)
	if err != nil {
		utils.ErrorResponseWithLog(c, http.StatusBadRequest, "Failed to delete post", err,
			"delete_post",
			logger.F("user_id", userIDStr),
			logger.F("post_id", postID))
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponseWithLog(c, http.StatusOK, "Post deleted successfully", nil,
		"delete_post",
		logger.F("user_id", userIDStr),
		logger.F("post_id", postID))
}

// @Summary Get personalized user feed
// @Description Retrieve a personalized feed containing posts from friends, teams, and public content relevant to the authenticated user. Feed is sorted by relevance and recency with smart algorithms.
// @Tags feed
// @Security BearerAuth
// @Param limit query int false "Number of posts per page (1-100)" minimum(1) maximum(100) default(20) example(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Post} "Successfully retrieved personalized feed with posts from friends, teams, and public sources"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to access personalized feed"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - limit exceeds maximum or invalid cursor/direction"
// @Failure 500 {object} utils.APIResponse "Internal server error - feed generation service unavailable"
// @Router /social/feed [get]
func (h *SocialHandler) GetUserFeed(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	posts, hasMore, err := h.socialService.GetUserFeed(ctx, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"posts":    posts,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Feed retrieved successfully", response)
}

// @Summary Get public posts feed
// @Description Retrieve the latest public posts from all users on the platform. Posts are sorted by creation time in descending order. No authentication required.
// @Tags feed
// @Param limit query int false "Number of posts per page (1-100)" minimum(1) maximum(100) default(20) example(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Post} "Successfully retrieved public posts feed sorted by most recent first"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - limit exceeds maximum or negative values provided"
// @Failure 500 {object} utils.APIResponse "Internal server error - database or feed service unavailable"
// @Router /social/feed/public [get]
func (h *SocialHandler) GetPublicFeed(c *gin.Context) {
	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	// Get optional user ID for populating user_like field
	userIDStr := middleware.GetOptionalUser(c)

	posts, hasMore, err := h.socialService.GetPublicFeed(c.Request.Context(), request, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"posts":    posts,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Public feed retrieved successfully", response)
}

// @Summary Get posts by specific user
// @Description Retrieve all posts created by a specific user. Respects privacy settings - only returns posts the requesting user has permission to view based on friendship status and post visibility.
// @Tags posts
// @Param user_id path string true "Target user ID whose posts to retrieve" example("user123")
// @Param limit query int false "Number of posts per page (1-100)" minimum(1) maximum(100) default(20) example(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Post} "Successfully retrieved user posts filtered by visibility permissions"
// @Failure 400 {object} utils.APIResponse "Invalid user ID format or missing required parameters"
// @Failure 404 {object} utils.APIResponse "User not found or has no accessible posts"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - limit exceeds maximum or negative values"
// @Failure 500 {object} utils.APIResponse "Internal server error - user service or database unavailable"
// @Router /social/users/{user_id}/posts [get]
func (h *SocialHandler) GetUserPosts(c *gin.Context) {
	targetUserID := c.Param("user_id")
	targetUUID, valid := validateUUID(c, targetUserID, "user_id")
	if !valid {
		return
	}

	viewerUserIDStr := middleware.GetOptionalUser(c)

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	posts, hasMore, err := h.socialService.GetUserPosts(c.Request.Context(), targetUUID.String(), viewerUserIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"posts":    posts,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "User posts retrieved successfully", response)
}

// @Summary Get posts from a specific team
// @Description Retrieve all posts shared within a specific team. Requires team membership - only team members can access team posts. Posts are sorted by creation time in descending order.
// @Tags posts
// @Security BearerAuth
// @Param team_id path string true "Team ID to retrieve posts from" example("team456")
// @Param limit query int false "Number of posts per page (1-100)" minimum(1) maximum(100) default(20) example(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Post} "Successfully retrieved team posts sorted by most recent first"
// @Failure 400 {object} utils.APIResponse "Invalid team ID format or missing required parameters"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to access team posts"
// @Failure 403 {object} utils.APIResponse "Access denied - user is not a member of the specified team"
// @Failure 404 {object} utils.APIResponse "Team not found or has no posts"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - limit exceeds maximum or negative values"
// @Failure 500 {object} utils.APIResponse "Internal server error - team service or database unavailable"
// @Router /social/teams/{team_id}/posts [get]
func (h *SocialHandler) GetTeamPosts(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	teamID := c.Param("team_id")
	teamUUID, valid := validateUUID(c, teamID, "team_id")
	if !valid {
		return
	}

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	posts, hasMore, err := h.socialService.GetTeamPosts(ctx, teamUUID.String(), userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"posts":    posts,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Team posts retrieved successfully", response)
}

// @Summary Search posts by content
// @Description Search for posts using full-text search across post content, tags, and mentions. Results are ranked by relevance and filtered by user's access permissions. Supports advanced search operators.
// @Tags posts
// @Security BearerAuth
// @Param q query string true "Search query - supports keywords, phrases, and boolean operators" minLength(1) maxLength(100) example("golang programming")
// @Param limit query int false "Number of posts per page (1-100)" minimum(1) maximum(100) default(20) example(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Post} "Successfully found posts matching search criteria, ranked by relevance score"
// @Failure 400 {object} utils.APIResponse "Invalid search query - empty query string or malformed search parameters"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided for search access"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - search query too long or invalid pagination values"
// @Failure 500 {object} utils.APIResponse "Internal server error - search service or database unavailable"
// @Router /social/posts/search [get]
func (h *SocialHandler) SearchPosts(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	query := c.Query("q")
	if query == "" {
		errors.RespondWithError(c, errors.NewValidationError("q", "Search query is required"))
		return
	}

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	posts, hasMore, err := h.socialService.SearchPosts(c.Request.Context(), query, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"posts":    posts,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Search results retrieved successfully", response)
}

// @Summary Get trending posts
// @Description Retrieve posts that are currently trending based on engagement metrics (likes, comments, shares, views) within the last 24 hours. Trending score calculated using weighted engagement algorithms.
// @Tags feed
// @Param limit query int false "Number of trending posts to return (1-50)" minimum(1) maximum(50) default(10) example(10)
// @Param time_window query string false "Time window for trending calculation" enums(1h,6h,24h,7d) default(24h) example("24h")
// @Success 200 {object} utils.APIResponse{data=[]models.Post} "Successfully retrieved trending posts ranked by engagement score within specified time window"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - limit exceeds maximum or invalid time window"
// @Failure 500 {object} utils.APIResponse "Internal server error - trending calculation service unavailable"
// @Router /social/feed/trending [get]
func (h *SocialHandler) GetTrendingPosts(c *gin.Context) {
	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	// Get optional user ID for populating user_like field
	userIDStr := middleware.GetOptionalUser(c)

	posts, hasMore, err := h.socialService.GetTrendingPosts(c.Request.Context(), request, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"posts":    posts,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Trending posts retrieved successfully", response)
}

// Comment handlers

// @Summary Create a comment or reply
// @Description Create a new comment on a post or reply to an existing comment. Supports threaded conversations with unlimited nesting depth. Automatically triggers notifications to post authors and parent comment authors.
// @Tags comments
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.CreateCommentRequest true "Comment creation data including post ID, optional parent comment ID for replies, and comment content"
// @Success 201 {object} utils.APIResponse{data=models.Comment} "Successfully created comment with populated author information and engagement metrics"
// @Failure 400 {object} utils.APIResponse "Invalid request data - missing post ID, invalid parent comment ID, or empty content"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to create comments"
// @Failure 403 {object} utils.APIResponse "Access denied - insufficient permissions to comment on this post based on visibility settings"
// @Failure 404 {object} utils.APIResponse "Post or parent comment not found - target content may have been deleted"
// @Failure 422 {object} utils.APIResponse "Validation failed - comment content too long or contains prohibited content"
// @Failure 429 {object} utils.APIResponse "Rate limit exceeded - too many comments created in short time period"
// @Failure 500 {object} utils.APIResponse "Internal server error - database or notification service unavailable"
// @Router /social/comments [post]
func (h *SocialHandler) CreateComment(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	var req services.CreateCommentRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	comment, err := h.socialService.CreateComment(c.Request.Context(), userIDStr, &req)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Comment created successfully", comment)
}

// @Summary Update an existing comment
// @Description Update the content of an existing comment. Only the comment author can update their own comments. Updated comments are marked with an updated timestamp.
// @Tags comments
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Comment ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439022")
// @Param request body map[string]string true "Updated comment content - only content field can be modified"
// @Success 200 {object} utils.APIResponse{data=models.Comment} "Successfully updated comment with new content and updated timestamp"
// @Failure 400 {object} utils.APIResponse "Invalid comment ID format or malformed request body"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to update comments"
// @Failure 403 {object} utils.APIResponse "Access denied - only comment authors can update their own comments"
// @Failure 404 {object} utils.APIResponse "Comment not found - comment may have been deleted or never existed"
// @Failure 422 {object} utils.APIResponse "Validation failed - updated content is empty or exceeds maximum length"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/comments/{id} [put]
func (h *SocialHandler) UpdateComment(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	var req struct {
		Content string `json:"content" binding:"required"`
	}
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	comment, err := h.socialService.UpdateComment(c.Request.Context(), commentID, userIDStr, req.Content)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Comment updated successfully", comment)
}

// @Summary Delete a comment (soft delete)
// @Description Delete an existing comment and all its replies. This is a soft delete operation - the comment is marked as deleted but retained for audit purposes. Only the comment author can delete their comments.
// @Tags comments
// @Security BearerAuth
// @Param id path string true "Comment ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439022")
// @Success 200 {object} utils.APIResponse "Successfully deleted comment - comment and all replies are now marked as deleted"
// @Failure 400 {object} utils.APIResponse "Invalid comment ID format - must be a valid 24-character hexadecimal ObjectID"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to delete comments"
// @Failure 403 {object} utils.APIResponse "Access denied - only comment authors can delete their own comments"
// @Failure 404 {object} utils.APIResponse "Comment not found - comment may have already been deleted or never existed"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/comments/{id} [delete]
func (h *SocialHandler) DeleteComment(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	err := h.socialService.DeleteComment(c.Request.Context(), commentID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Comment deleted successfully", nil)
}

// @Summary Get comments for a specific post
// @Description Retrieve top-level comments for a post (excluding nested replies). Comments are sorted chronologically and include author information and user's like status. Respects post visibility permissions.
// @Tags comments
// @Param id path string true "Post ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439011")
// @Param limit query int false "Number of comments per page (1-100)" minimum(1) maximum(100) default(20) example(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Comment} "Successfully retrieved post comments with author information and engagement data"
// @Failure 400 {object} utils.APIResponse "Invalid post ID format - must be a valid 24-character hexadecimal ObjectID"
// @Failure 403 {object} utils.APIResponse "Access denied - insufficient permissions to view comments on this post"
// @Failure 404 {object} utils.APIResponse "Post not found - post may have been deleted or never existed"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - limit exceeds maximum or negative values"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/posts/{id}/comments [get]
func (h *SocialHandler) GetPostComments(c *gin.Context) {
	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	comments, hasMore, err := h.socialService.GetPostComments(c.Request.Context(), postID, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"comments": comments,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Comments retrieved successfully", response)
}

// @Summary Get replies to a specific comment
// @Description Retrieve nested replies for a specific comment. Supports unlimited threading depth. Replies are sorted chronologically and include author information and user's like status.
// @Tags comments
// @Param id path string true "Parent comment ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439033")
// @Param limit query int false "Number of replies per page (1-100)" minimum(1) maximum(100) default(20) example(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Comment} "Successfully retrieved comment replies with author information and engagement data"
// @Failure 400 {object} utils.APIResponse "Invalid comment ID format - must be a valid 24-character hexadecimal ObjectID"
// @Failure 403 {object} utils.APIResponse "Access denied - insufficient permissions to view replies on the parent post"
// @Failure 404 {object} utils.APIResponse "Comment not found - parent comment may have been deleted or never existed"
// @Failure 422 {object} utils.APIResponse "Invalid query parameters - limit exceeds maximum or negative values"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/comments/{id}/replies [get]
func (h *SocialHandler) GetCommentReplies(c *gin.Context) {
	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	replies, hasMore, err := h.socialService.GetCommentReplies(c.Request.Context(), commentID, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"replies":  replies,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Replies retrieved successfully", response)
}

// Share handlers

// @Summary Share a post to timeline or team
// @Description Share an existing post to your timeline, a team, or with specific visibility settings. Can include an optional comment with the share. Automatically triggers notifications to the original post author.
// @Tags shares
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.CreateShareRequest true "Share creation data including post ID, optional team ID, visibility settings, and optional comment"
// @Success 201 {object} utils.APIResponse{data=models.Share} "Successfully shared post with populated original post data and share metadata"
// @Failure 400 {object} utils.APIResponse "Invalid request data - missing post ID, invalid team ID, or malformed share parameters"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to share posts"
// @Failure 403 {object} utils.APIResponse "Access denied - insufficient permissions to share this post or post to specified team"
// @Failure 404 {object} utils.APIResponse "Post not found - original post may have been deleted or never existed"
// @Failure 409 {object} utils.APIResponse "Conflict - user has already shared this post"
// @Failure 422 {object} utils.APIResponse "Validation failed - share comment too long or invalid visibility settings"
// @Failure 429 {object} utils.APIResponse "Rate limit exceeded - too many shares in short time period"
// @Failure 500 {object} utils.APIResponse "Internal server error - database or notification service unavailable"
// @Router /social/shares [post]
func (h *SocialHandler) SharePost(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	var req services.CreateShareRequest
	if !middleware.BindJSONRequest(c, &req) {
		return // Error already handled by middleware
	}

	share, err := h.socialService.SharePost(c.Request.Context(), userIDStr, &req)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Post shared successfully", share)
}

// @Summary Remove a shared post (unshare)
// @Description Remove a previously shared post from your timeline or team. This only removes your share - the original post remains unaffected. Updates share counters on the original post.
// @Tags shares
// @Security BearerAuth
// @Param id path string true "Original post ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439011")
// @Success 200 {object} utils.APIResponse "Successfully removed share and updated original post share counters"
// @Failure 400 {object} utils.APIResponse "Invalid post ID format - must be a valid 24-character hexadecimal ObjectID"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to unshare posts"
// @Failure 404 {object} utils.APIResponse "Post not found or user has not shared this post"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/posts/{id}/share [delete]
func (h *SocialHandler) UnsharePost(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	err := h.socialService.UnsharePost(c.Request.Context(), postID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Post unshared successfully", nil)
}

// @Summary Get users who reacted to a post
// @Description Retrieve a paginated list of users who liked or reacted to a specific post. Includes reaction type, user information, and reaction timestamp. Supports filtering by reaction type and pagination.
// @Tags reactions
// @Security BearerAuth
// @Param id path string true "Post ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439011")
// @Param limit query int false "Number of reactions to return (1-100)" minimum(1) maximum(100) default(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Like} "Successfully retrieved list of users who reacted to the post"
// @Failure 400 {object} utils.APIResponse "Invalid post ID format or invalid pagination parameters"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided"
// @Failure 403 {object} utils.APIResponse "Access denied - user cannot view this post's reactions due to privacy settings"
// @Failure 404 {object} utils.APIResponse "Post not found or has been deleted"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/posts/{id}/likes [get]
func (h *SocialHandler) GetPostLikes(c *gin.Context) {
	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	// Parse pagination parameters
	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	likes, hasMore, err := h.socialService.GetPostLikes(ctx, postID, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"likes":    likes,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Post likes retrieved successfully", response)
}

// @Summary Get users who reacted to a comment
// @Description Retrieve a paginated list of users who liked or reacted to a specific comment. Includes reaction type, user information, and reaction timestamp.
// @Tags reactions
// @Security BearerAuth
// @Param id path string true "Comment ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439022")
// @Param limit query int false "Number of reactions to return (1-100)" minimum(1) maximum(100) default(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Like} "Successfully retrieved list of users who reacted to the comment"
// @Failure 400 {object} utils.APIResponse "Invalid comment ID format or invalid pagination parameters"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided"
// @Failure 403 {object} utils.APIResponse "Access denied - user cannot view this comment's reactions due to parent post privacy"
// @Failure 404 {object} utils.APIResponse "Comment not found or parent post has been deleted"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/comments/{id}/likes [get]
func (h *SocialHandler) GetCommentLikes(c *gin.Context) {
	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	// Parse pagination parameters
	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	likes, hasMore, err := h.socialService.GetCommentLikes(ctx, commentID, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"likes":    likes,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Comment likes retrieved successfully", response)
}

// @Summary Get reaction statistics for a post
// @Description Get aggregated reaction statistics including total count, breakdown by reaction type (like, love, haha, wow, sad, angry), and list of users with their reaction types and timestamps.
// @Tags reactions
// @Security BearerAuth
// @Param id path string true "Post ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439011")
// @Success 200 {object} utils.APIResponse{data=models.ReactionStats} "Successfully retrieved reaction statistics with breakdown and user list"
// @Failure 400 {object} utils.APIResponse "Invalid post ID format"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided"
// @Failure 403 {object} utils.APIResponse "Access denied - user cannot view this post's reaction stats due to privacy settings"
// @Failure 404 {object} utils.APIResponse "Post not found or has been deleted"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/posts/{id}/reactions [get]
func (h *SocialHandler) GetPostReactionStats(c *gin.Context) {
	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	stats, err := h.socialService.GetPostReactionStats(ctx, postID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Post reaction stats retrieved successfully", stats)
}

// @Summary Get reaction statistics for a comment
// @Description Get aggregated reaction statistics for a comment including total count, breakdown by reaction type (like, love, haha, wow, sad, angry), and list of users with their reaction types and timestamps.
// @Tags reactions
// @Security BearerAuth
// @Param id path string true "Comment ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439022")
// @Success 200 {object} utils.APIResponse{data=models.ReactionStats} "Successfully retrieved comment reaction statistics with breakdown and user list"
// @Failure 400 {object} utils.APIResponse "Invalid comment ID format"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided"
// @Failure 403 {object} utils.APIResponse "Access denied - user cannot view this comment's reaction stats due to parent post privacy"
// @Failure 404 {object} utils.APIResponse "Comment not found or parent post has been deleted"
// @Failure 500 {object} utils.APIResponse "Internal server error - database service unavailable"
// @Router /social/comments/{id}/reactions [get]
func (h *SocialHandler) GetCommentReactionStats(c *gin.Context) {
	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	stats, err := h.socialService.GetCommentReactionStats(ctx, commentID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Comment reaction stats retrieved successfully", stats)
}

// Attachment handlers

// @Summary Get attachment details
// @Description Retrieve detailed information about a specific attachment. Validates user access and returns attachment metadata including download URL, thumbnail, and processing status.
// @Tags attachments
// @Security BearerAuth
// @Param id path string true "Attachment ID (MongoDB ObjectID)" format("^[0-9a-fA-F]{24}$") example("507f1f77bcf86cd799439044")
// @Success 200 {object} utils.APIResponse{data=sharedModels.AttachmentBasic} "Successfully retrieved attachment details with download URL and metadata"
// @Failure 400 {object} utils.APIResponse "Invalid attachment ID format - must be a valid 24-character hexadecimal ObjectID"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to access attachments"
// @Failure 403 {object} utils.APIResponse "Access denied - user does not have permission to view this attachment"
// @Failure 404 {object} utils.APIResponse "Attachment not found - attachment may have been deleted or never existed"
// @Failure 500 {object} utils.APIResponse "Internal server error - storage service or database unavailable"
// @Router /social/attachments/{id} [get]
func (h *SocialHandler) GetAttachment(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	attachmentID := c.Param("id")
	if attachmentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Attachment ID is required"))
		return
	}

	attachment, err := h.socialService.GetAttachment(c.Request.Context(), attachmentID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Attachment retrieved successfully", attachment)
}

// @Summary Upload attachment for social content
// @Description Upload a file attachment that can be used in posts or comments. Handles file validation, virus scanning, and generates thumbnails for media files. Returns attachment ID for use in posts/comments.
// @Tags attachments
// @Security BearerAuth
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "File to upload - supports images, videos, audio, documents and archives up to configured size limit"
// @Param description formData string false "Optional description or alt text for the attachment"
// @Success 201 {object} utils.APIResponse{data=sharedModels.AttachmentBasic} "Successfully uploaded attachment with generated ID and processing status"
// @Failure 400 {object} utils.APIResponse "Invalid file format, size exceeds limit, or missing file in request"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to upload attachments"
// @Failure 413 {object} utils.APIResponse "File size exceeds maximum allowed limit"
// @Failure 415 {object} utils.APIResponse "Unsupported file type - file format not allowed for security reasons"
// @Failure 422 {object} utils.APIResponse "File validation failed - file appears corrupted or contains malicious content"
// @Failure 507 {object} utils.APIResponse "Insufficient storage space - user storage quota exceeded"
// @Failure 500 {object} utils.APIResponse "Internal server error - file processing or storage service unavailable"
// @Router /social/attachments/upload [post]
func (h *SocialHandler) UploadAttachment(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	// Get user token from Authorization header
	userToken := middleware.ForwardUserToken(c)
	if userToken == "" {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Authorization token required", nil)
		return
	}

	// Add user token to context for user service communication
	ctx := c.Request.Context()
	ctx = context.WithValue(ctx, "user_token", userToken)

	attachment, err := h.socialService.UploadAttachment(ctx, userIDStr, c)
	if err != nil {
		if strings.Contains(err.Error(), "file size") {
			utils.ErrorResponse(c, http.StatusRequestEntityTooLarge, err.Error(), nil)
			return
		}
		if strings.Contains(err.Error(), "file type") {
			utils.ErrorResponse(c, http.StatusUnsupportedMediaType, err.Error(), nil)
			return
		}
		if strings.Contains(err.Error(), "quota") {
			utils.ErrorResponse(c, http.StatusInsufficientStorage, err.Error(), nil)
			return
		}
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	// Debug: Check if attachment is nil
	if attachment == nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Upload succeeded but attachment data is nil", nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Attachment uploaded successfully", attachment)
}

// @Summary List attachments with filtering
// @Description Get a list of attachments based on filters like post_id, user_id, team_id, type, with pagination support
// @Tags attachments
// @Security BearerAuth
// @Produce json
// @Param post_id query string false "Filter attachments by post ID"
// @Param user_id query string false "Filter attachments by user ID"
// @Param team_id query string false "Filter attachments by team ID"
// @Param type query string false "Filter attachments by type (image, video, audio, document, archive, file)"
// @Param limit query int false "Number of attachments to return (max 100)" default(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]sharedModels.AttachmentBasic} "Successfully retrieved attachments list"
// @Failure 400 {object} utils.APIResponse "Invalid query parameters"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided"
// @Failure 500 {object} utils.APIResponse "Internal server error - storage service unavailable"
// @Router /social/attachments [get]
func (h *SocialHandler) GetAttachments(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	// Parse query parameters
	params := make(map[string]interface{})
	
	if postID := c.Query("post_id"); postID != "" {
		params["post_id"] = postID
	}
	
	if userID := c.Query("user_id"); userID != "" {
		params["user_id"] = userID
	}
	
	if teamID := c.Query("team_id"); teamID != "" {
		params["team_id"] = teamID
	}
	
	if attachmentType := c.Query("type"); attachmentType != "" {
		// Validate attachment type
		validTypes := []string{"image", "video", "audio", "document", "archive", "file"}
		isValid := false
		for _, validType := range validTypes {
			if attachmentType == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid attachment type. Valid types: image, video, audio, document, archive, file", nil)
			return
		}
		params["type"] = attachmentType
	}
	
	// Parse pagination parameters
	limit := 20 // default
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}
	params["limit"] = limit

	attachments, err := h.socialService.GetAttachments(c.Request.Context(), params, userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Attachments retrieved successfully", attachments)
}

// @Summary Delete attachment
// @Description Delete an attachment from storage. Only the attachment owner can delete their attachments.
// @Tags attachments
// @Security BearerAuth
// @Param id path string true "Attachment ID (UUID)" format("uuid") example("507f1f77-bcf8-6cd7-9943-9011")
// @Success 200 {object} utils.APIResponse "Successfully deleted attachment from storage"
// @Failure 400 {object} utils.APIResponse "Invalid attachment ID format - must be a valid UUID"
// @Failure 401 {object} utils.APIResponse "Authentication required - valid JWT token must be provided to delete attachments"
// @Failure 403 {object} utils.APIResponse "Access denied - user does not have permission to delete this attachment"
// @Failure 404 {object} utils.APIResponse "Attachment not found - attachment may have been deleted or never existed"
// @Failure 500 {object} utils.APIResponse "Internal server error - storage service unavailable"
// @Router /social/attachments/{id} [delete]
func (h *SocialHandler) DeleteAttachment(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	attachmentID := c.Param("id")
	if attachmentID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Attachment ID is required", nil)
		return
	}

	err := h.socialService.DeleteAttachment(c.Request.Context(), attachmentID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Attachment deleted successfully", nil)
}

// @Summary Health check endpoint
// @Description Check the health and status of the social service including database connectivity and service dependencies
// @Tags health
// @Produce json
// @Success 200 {object} utils.APIResponse "Service is healthy and all dependencies are operational"
// @Failure 503 {object} utils.APIResponse "Service is unhealthy - database or critical dependencies are unavailable"
// @Router /social/health [get]
func (h *SocialHandler) HealthCheck(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Social service is healthy", map[string]string{
		"service": "social-service",
		"status":  "healthy",
	})
}

// @Summary Test attachment upload (no auth required)
// @Description Test endpoint for MinIO attachment upload functionality without authentication requirements
// @Tags attachments
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "File to upload for testing"
// @Param description formData string false "Optional description for the test upload"
// @Success 201 {object} utils.APIResponse{data=sharedModels.AttachmentBasic} "Successfully uploaded test attachment"
// @Failure 400 {object} utils.APIResponse "Invalid file or upload error"
// @Router /social/test/upload [post]
func (h *SocialHandler) TestUploadAttachment(c *gin.Context) {
	// Use a test user ID for the upload
	testUserID := "test-user-123"
	
	attachment, err := h.socialService.UploadAttachment(c.Request.Context(), testUserID, c)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Test attachment uploaded successfully", attachment)
}

// @Summary Test get attachment (no auth required)
// @Description Test endpoint for MinIO attachment retrieval functionality without authentication requirements
// @Tags attachments
// @Param id path string true "Attachment ID to retrieve"
// @Success 200 {object} utils.APIResponse{data=sharedModels.AttachmentBasic} "Successfully retrieved test attachment"
// @Failure 400 {object} utils.APIResponse "Invalid attachment ID or retrieval error"
// @Router /social/test/attachment/{id} [get]
func (h *SocialHandler) TestGetAttachment(c *gin.Context) {
	// Use a test user ID
	testUserID := "test-user-123"
	attachmentID := c.Param("id")
	
	attachment, err := h.socialService.GetAttachment(c.Request.Context(), attachmentID, testUserID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Test attachment retrieved successfully", attachment)
}

// New Consistent Reaction API Handlers

// @Summary Add reaction to a post (NEW CONSISTENT API)
// @Description Add a reaction to a post using the new consistent resource-centric API. Supports multiple reaction types.
// @Tags reactions
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Post ID (MongoDB ObjectID)"
// @Param request body map[string]string false "Reaction type - defaults to 'like' if not specified"
// @Success 200 {object} utils.APIResponse "Successfully added reaction to post"
// @Router /social/posts/{id}/reactions [post]
func (h *SocialHandler) AddPostReaction(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return
	}

	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	var req struct {
		Type models.LikeType `json:"type"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		req.Type = models.LikeTypeLike
	}

	err := h.socialService.LikePost(c.Request.Context(), postID, userIDStr, req.Type)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Reaction added successfully", nil)
}

// @Summary Remove reaction from a post (NEW CONSISTENT API)
// @Description Remove the user's reaction from a post using the new consistent resource-centric API.
// @Tags reactions
// @Security BearerAuth
// @Param id path string true "Post ID (MongoDB ObjectID)"
// @Success 200 {object} utils.APIResponse "Successfully removed reaction from post"
// @Router /social/posts/{id}/reactions [delete]
func (h *SocialHandler) RemovePostReaction(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return
	}

	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	err := h.socialService.UnlikePost(c.Request.Context(), postID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Reaction removed successfully", nil)
}

// @Summary Get users who reacted to a post (NEW CONSISTENT API)
// @Description Get paginated list of users who reacted to a post using the new consistent resource-centric API.
// @Tags reactions
// @Param id path string true "Post ID (MongoDB ObjectID)"
// @Param limit query int false "Number of reactions to return (1-100)" default(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Like} "Successfully retrieved users who reacted"
// @Router /social/posts/{id}/reactions/users [get]
func (h *SocialHandler) GetPostReactionUsers(c *gin.Context) {
	postID := c.Param("id")
	if postID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Post ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	userToken := middleware.ForwardUserToken(c)
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	likes, hasMore, err := h.socialService.GetPostLikes(ctx, postID, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"likes":    likes,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Post reaction users retrieved successfully", response)
}

// @Summary Add reaction to a comment (NEW CONSISTENT API)
// @Description Add a reaction to a comment using the new consistent resource-centric API.
// @Tags reactions
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Comment ID (MongoDB ObjectID)"
// @Param request body map[string]string false "Reaction type - defaults to 'like' if not specified"
// @Success 200 {object} utils.APIResponse "Successfully added reaction to comment"
// @Router /social/comments/{id}/reactions [post]
func (h *SocialHandler) AddCommentReaction(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return
	}

	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	var req struct {
		Type models.LikeType `json:"type"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		req.Type = models.LikeTypeLike
	}

	err := h.socialService.LikeComment(c.Request.Context(), commentID, userIDStr, req.Type)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Reaction added successfully", nil)
}

// @Summary Remove reaction from a comment (NEW CONSISTENT API)
// @Description Remove the user's reaction from a comment using the new consistent resource-centric API.
// @Tags reactions
// @Security BearerAuth
// @Param id path string true "Comment ID (MongoDB ObjectID)"
// @Success 200 {object} utils.APIResponse "Successfully removed reaction from comment"
// @Router /social/comments/{id}/reactions [delete]
func (h *SocialHandler) RemoveCommentReaction(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return
	}

	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	err := h.socialService.UnlikeComment(c.Request.Context(), commentID, userIDStr)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Reaction removed successfully", nil)
}

// @Summary Get users who reacted to a comment (NEW CONSISTENT API)
// @Description Get paginated list of users who reacted to a comment using the new consistent resource-centric API.
// @Tags reactions
// @Param id path string true "Comment ID (MongoDB ObjectID)"
// @Param limit query int false "Number of reactions to return (1-100)" default(20)
// @Param cursor query string false "Pagination cursor for next/previous page" example("eyJ0aW1lc3RhbXAiOiIyMDI0LTAxLTE1VDE...")
// @Param direction query string false "Pagination direction (forward/backward)" enums(forward,backward) default(forward)
// @Success 200 {object} utils.APIResponse{data=[]models.Like} "Successfully retrieved users who reacted"
// @Router /social/comments/{id}/reactions/users [get]
func (h *SocialHandler) GetCommentReactionUsers(c *gin.Context) {
	commentID := c.Param("id")
	if commentID == "" {
		errors.RespondWithError(c, errors.NewValidationError("id", "Comment ID is required"))
		return
	}

	userIDStr := middleware.GetOptionalUser(c)

	userToken := middleware.ForwardUserToken(c)
	ctx := c.Request.Context()
	if userToken != "" {
		ctx = context.WithValue(ctx, "user_token", userToken)
	}

	request := convertPaginationRequest(middleware.ValidateCursorPagination(c))

	likes, hasMore, err := h.socialService.GetCommentLikes(ctx, commentID, userIDStr, request)
	if err != nil {
		errors.RespondWithStandardError(c, err)
		return
	}

	response := map[string]interface{}{
		"likes":    likes,
		"has_more": hasMore,
	}
	utils.SuccessResponse(c, http.StatusOK, "Comment reaction users retrieved successfully", response)
}
