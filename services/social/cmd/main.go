// @title Social Service API
// @version 1.0
// @description Comprehensive social networking and collaboration API for the Swork Team platform. This service provides social media functionality including posts, comments, likes, shares, feeds, and content moderation.
//
// @contact.name Social Service API Support
// @contact.url https://github.com/swork-team/platform
// @contact.email <EMAIL>
//
// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html
//
// @host localhost:8003
// @BasePath /social
//
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Enter your bearer token in the format: Bearer {token}
//
// @tag.name posts
// @tag.description Operations related to social media posts
//
// @tag.name comments
// @tag.description Operations related to post comments and replies
//
// @tag.name likes
// @tag.description Operations related to likes and reactions
//
// @tag.name shares
// @tag.description Operations related to post sharing
//
// @tag.name feed
// @tag.description Operations related to social media feeds
//
// @tag.name moderation
// @tag.description Operations related to content moderation
//
// @tag.name health
// @tag.description Service health and status endpoints
package main

import (
	"github.com/gin-gonic/gin"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	"github.com/swork-team/platform/services/social/internal/config"
	"github.com/swork-team/platform/services/social/internal/handlers"
	"github.com/swork-team/platform/services/social/internal/repositories"
	"github.com/swork-team/platform/services/social/internal/services"
)

func main() {
	cfg := config.LoadConfig()

	// Create config adapter for bootstrap
	configAdapter := server.NewMongoConfigAdapter(cfg.Server, cfg.Database, cfg.Redis)

	// Use the new ServiceBootstrapper
	bootstrapper := server.NewServiceBootstrapper(server.ServiceBootstrapConfig{
		ServiceName:      "social",
		ConfigAdapter:    configAdapter,
		ModelsProvider:   nil, // MongoDB doesn't use GORM models
		RouterSetup:      createSocialRouterSetup(),
		EnableExtensions: false,
		DatabaseType:     server.MongoDB,
		CustomStartupLog: socialCustomStartupLog,
	})

	bootstrapper.Bootstrap()
}

// createSocialRouterSetup creates the router setup function for social service
func createSocialRouterSetup() server.RouterSetupFunction {
	return func(components *server.ServiceComponents) *gin.Engine {
		cfg := config.LoadConfig()

		postRepo := repositories.NewPostRepository(components.MongoClient, components.MongoDB)

		socialService := services.NewSocialService(postRepo, cfg, components.RedisClient, components.CacheManager, components.AsyncJobQueue, components.WebhookService)
		socialHandler := handlers.NewSocialHandler(socialService)

		// Initialize permission service
		permissionService := services.NewPostPermissionService(postRepo, components.RedisClient)
		permissionHandler := handlers.NewPermissionHandler(permissionService)

		// Get logger manager
		loggerManager := logger.NewServiceLoggerManager("social")

		return setupRouter(socialHandler, permissionHandler, components, cfg, loggerManager)
	}
}

// socialCustomStartupLog provides custom startup logging for social service
func socialCustomStartupLog(serviceLogger logger.ServiceLogger) {
	cfg := config.LoadConfig()
	serviceLogger.Info("Social service starting up",
		logger.F("version", cfg.Server.Version),
		logger.F("environment", cfg.Server.Environment),
		logger.F("port", cfg.Server.Port),
		logger.F("database_type", "mongodb"),
	)
}

func setupRouter(socialHandler *handlers.SocialHandler, permissionHandler *handlers.PermissionHandler, components *server.ServiceComponents, cfg *config.Config, loggerManager *logger.ServiceLoggerManager) *gin.Engine {
	router := server.SetupStandardRouter()

	serviceLogger := loggerManager.GetLogger()

	// Add logging middleware
	router.Use(logger.RequestLoggingMiddleware(serviceLogger))
	router.Use(logger.UserContextMiddleware())
	router.Use(logger.ErrorLoggingMiddleware())

	// Health check
	router.GET("/health", socialHandler.HealthCheck)

	// Social routes
	social := router.Group("/social")
	{
		// Public routes (no auth required)
		public := social.Group("")
		public.Use(middleware.OptionalAuthMiddleware())
		{
			public.GET("/feed/public", socialHandler.GetPublicFeed)
			public.GET("/feed/trending", socialHandler.GetTrendingPosts)
			public.GET("/users/:user_id/posts", socialHandler.GetUserPosts)
			public.GET("/posts/:id", socialHandler.GetPost)
			public.GET("/posts/:id/comments", socialHandler.GetPostComments)
			public.GET("/comments/:id/replies", socialHandler.GetCommentReplies)

			// Reaction Stats (public - like social media platforms)
			public.GET("/posts/:id/reactions", socialHandler.GetPostReactionStats)
			public.GET("/comments/:id/reactions", socialHandler.GetCommentReactionStats)

			// CONSISTENT REACTION ENDPOINTS (Public)
			public.GET("/posts/:id/reactions/users", socialHandler.GetPostReactionUsers)
			public.GET("/comments/:id/reactions/users", socialHandler.GetCommentReactionUsers)

			// Test upload endpoint (for testing MinIO functionality)
			public.POST("/test/upload", socialHandler.TestUploadAttachment)
			public.GET("/test/attachment/:id", socialHandler.TestGetAttachment)
		}

		// Authenticated routes
		auth := social.Group("")
		auth.Use(middleware.AuthMiddleware())
		{
			// Posts
			auth.POST("/posts", socialHandler.CreatePost)
			auth.PUT("/posts/:id", socialHandler.UpdatePost)
			auth.DELETE("/posts/:id", socialHandler.DeletePost)
			auth.GET("/posts/search", socialHandler.SearchPosts)

			// Feed
			auth.GET("/feed", socialHandler.GetUserFeed)
			auth.GET("/teams/:team_id/posts", socialHandler.GetTeamPosts)

			// Comments
			auth.POST("/comments", socialHandler.CreateComment)
			auth.PUT("/comments/:id", socialHandler.UpdateComment)
			auth.DELETE("/comments/:id", socialHandler.DeleteComment)

			// CONSISTENT REACTION ENDPOINTS
			auth.POST("/posts/:id/reactions", socialHandler.AddPostReaction)
			auth.DELETE("/posts/:id/reactions", socialHandler.RemovePostReaction)
			auth.POST("/comments/:id/reactions", socialHandler.AddCommentReaction)
			auth.DELETE("/comments/:id/reactions", socialHandler.RemoveCommentReaction)

			// Shares
			auth.POST("/shares", socialHandler.SharePost)
			auth.DELETE("/posts/:id/share", socialHandler.UnsharePost)

			// Attachments
			auth.GET("/attachments", socialHandler.GetAttachments)
			auth.GET("/attachments/:id", socialHandler.GetAttachment)
			auth.POST("/attachments/upload", socialHandler.UploadAttachment)
			auth.DELETE("/attachments/:id", socialHandler.DeleteAttachment)
		}

		// API routes for permission validation (internal service-to-service)
		api := social.Group("/api/v1")
		api.Use(middleware.ServiceAuthMiddleware())
		{
			permissionHandler.RegisterRoutes(api)
		}

	}

	return router
}
