package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/services/notification/internal/models"
	"gorm.io/gorm"
)

type NotificationRepository struct {
	db *gorm.DB
}

func NewNotificationRepository(db *gorm.DB) *NotificationRepository {
	return &NotificationRepository{
		db: db,
	}
}

// Notification operations
func (r *NotificationRepository) CreateNotification(ctx context.Context, notification *models.Notification) error {
	result := r.db.WithContext(ctx).Create(notification)
	if result.Error != nil {
		return fmt.Errorf("failed to create notification: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) GetNotificationByID(ctx context.Context, id uuid.UUID) (*models.Notification, error) {
	var notification models.Notification
	result := r.db.WithContext(ctx).
		Preload("Deliveries").
		Where("id = ?", id).
		First(&notification)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get notification: %w", result.Error)
	}
	
	return &notification, nil
}

func (r *NotificationRepository) GetUserNotifications(ctx context.Context, userID string, limit, offset int, unreadOnly bool) ([]models.Notification, error) {
	var notifications []models.Notification
	query := r.db.WithContext(ctx).
		Preload("Sender").
		Where("user_id = ?", userID)
	
	if unreadOnly {
		query = query.Where("status != ?", models.NotificationStatusRead)
	}
	
	// Filter out expired notifications
	query = query.Where("expires_at IS NULL OR expires_at > ?", time.Now())
	
	result := query.Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user notifications: %w", result.Error)
	}
	
	return notifications, nil
}

func (r *NotificationRepository) GetUnreadCount(ctx context.Context, userID string) (int64, error) {
	var count int64
	result := r.db.WithContext(ctx).Model(&models.Notification{}).
		Where("user_id = ? AND status != ? AND (expires_at IS NULL OR expires_at > ?)", 
			userID, models.NotificationStatusRead, time.Now()).
		Count(&count)
	
	if result.Error != nil {
		return 0, fmt.Errorf("failed to get unread count: %w", result.Error)
	}
	
	return count, nil
}

func (r *NotificationRepository) UpdateNotification(ctx context.Context, notification *models.Notification) error {
	result := r.db.WithContext(ctx).Save(notification)
	if result.Error != nil {
		return fmt.Errorf("failed to update notification: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) MarkAsRead(ctx context.Context, notificationID uuid.UUID, userID string) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.Notification{}).
		Where("id = ? AND user_id = ?", notificationID, userID).
		Updates(map[string]interface{}{
			"status":  models.NotificationStatusRead,
			"read_at": &now,
		})
	
	if result.Error != nil {
		return fmt.Errorf("failed to mark notification as read: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return errors.New("notification not found or not accessible")
	}
	
	return nil
}

func (r *NotificationRepository) MarkAllAsRead(ctx context.Context, userID string) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.Notification{}).
		Where("user_id = ? AND status != ?", userID, models.NotificationStatusRead).
		Updates(map[string]interface{}{
			"status":  models.NotificationStatusRead,
			"read_at": &now,
		})
	
	if result.Error != nil {
		return fmt.Errorf("failed to mark all notifications as read: %w", result.Error)
	}
	
	return nil
}

func (r *NotificationRepository) DeleteNotification(ctx context.Context, notificationID uuid.UUID, userID string) error {
	result := r.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", notificationID, userID).
		Delete(&models.Notification{})
	
	if result.Error != nil {
		return fmt.Errorf("failed to delete notification: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return errors.New("notification not found or not accessible")
	}
	
	return nil
}

func (r *NotificationRepository) GetPendingNotifications(ctx context.Context, limit int) ([]models.Notification, error) {
	var notifications []models.Notification
	result := r.db.WithContext(ctx).
		Preload("Deliveries").
		Where("status = ? AND (expires_at IS NULL OR expires_at > ?)", 
			models.NotificationStatusPending, time.Now()).
		Order("priority DESC, created_at ASC").
		Limit(limit).
		Find(&notifications)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get pending notifications: %w", result.Error)
	}
	
	return notifications, nil
}

func (r *NotificationRepository) CleanupExpiredNotifications(ctx context.Context, retentionDays int) (int64, error) {
	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
	
	result := r.db.WithContext(ctx).
		Where("created_at < ? OR expires_at < ?", cutoffDate, time.Now()).
		Delete(&models.Notification{})
	
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired notifications: %w", result.Error)
	}
	
	return result.RowsAffected, nil
}

// Device operations
func (r *NotificationRepository) CreateDevice(ctx context.Context, device *models.Device) error {
	result := r.db.WithContext(ctx).Create(device)
	if result.Error != nil {
		return fmt.Errorf("failed to create device: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) GetDeviceByToken(ctx context.Context, token string) (*models.Device, error) {
	var device models.Device
	result := r.db.WithContext(ctx).Where("token = ?", token).First(&device)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get device: %w", result.Error)
	}
	
	return &device, nil
}

func (r *NotificationRepository) GetUserDevices(ctx context.Context, userID string, deviceType models.DeviceType) ([]models.Device, error) {
	var devices []models.Device
	query := r.db.WithContext(ctx).Where("user_id = ? AND is_active = ?", userID, true)
	
	if deviceType != "" {
		query = query.Where("type = ?", deviceType)
	}
	
	result := query.Order("last_used_at DESC").Find(&devices)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user devices: %w", result.Error)
	}
	
	return devices, nil
}

func (r *NotificationRepository) UpdateDevice(ctx context.Context, device *models.Device) error {
	result := r.db.WithContext(ctx).Save(device)
	if result.Error != nil {
		return fmt.Errorf("failed to update device: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) DeactivateDevice(ctx context.Context, token string) error {
	result := r.db.WithContext(ctx).Model(&models.Device{}).
		Where("token = ?", token).
		Update("is_active", false)
	
	if result.Error != nil {
		return fmt.Errorf("failed to deactivate device: %w", result.Error)
	}
	
	return nil
}

func (r *NotificationRepository) CleanupInactiveDevices(ctx context.Context, inactiveDays int) (int64, error) {
	cutoffDate := time.Now().AddDate(0, 0, -inactiveDays)
	
	result := r.db.WithContext(ctx).
		Where("last_used_at < ? OR (last_used_at IS NULL AND created_at < ?)", cutoffDate, cutoffDate).
		Delete(&models.Device{})
	
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup inactive devices: %w", result.Error)
	}
	
	return result.RowsAffected, nil
}

// Notification Delivery operations
func (r *NotificationRepository) CreateDelivery(ctx context.Context, delivery *models.NotificationDelivery) error {
	result := r.db.WithContext(ctx).Create(delivery)
	if result.Error != nil {
		return fmt.Errorf("failed to create delivery: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) UpdateDelivery(ctx context.Context, delivery *models.NotificationDelivery) error {
	result := r.db.WithContext(ctx).Save(delivery)
	if result.Error != nil {
		return fmt.Errorf("failed to update delivery: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) GetFailedDeliveries(ctx context.Context, limit int) ([]models.NotificationDelivery, error) {
	var deliveries []models.NotificationDelivery
	result := r.db.WithContext(ctx).
		Preload("Notification").
		Where("status = ? AND attempt_count < ?", models.NotificationStatusFailed, 3).
		Order("last_attempt_at ASC").
		Limit(limit).
		Find(&deliveries)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get failed deliveries: %w", result.Error)
	}
	
	return deliveries, nil
}

// User Notification Preferences operations
func (r *NotificationRepository) CreatePreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	result := r.db.WithContext(ctx).Create(preference)
	if result.Error != nil {
		return fmt.Errorf("failed to create preference: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) GetUserPreferences(ctx context.Context, userID string) ([]models.UserNotificationPreference, error) {
	var preferences []models.UserNotificationPreference
	result := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&preferences)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user preferences: %w", result.Error)
	}
	
	return preferences, nil
}

func (r *NotificationRepository) GetUserPreference(ctx context.Context, userID string, notificationType models.NotificationType) (*models.UserNotificationPreference, error) {
	var preference models.UserNotificationPreference
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND type = ?", userID, notificationType).
		First(&preference)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user preference: %w", result.Error)
	}
	
	return &preference, nil
}

func (r *NotificationRepository) UpdatePreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	result := r.db.WithContext(ctx).Save(preference)
	if result.Error != nil {
		return fmt.Errorf("failed to update preference: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) DeletePreference(ctx context.Context, userID string, notificationType models.NotificationType) error {
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND type = ?", userID, notificationType).
		Delete(&models.UserNotificationPreference{})
	
	if result.Error != nil {
		return fmt.Errorf("failed to delete preference: %w", result.Error)
	}
	
	return nil
}

// Template operations
func (r *NotificationRepository) CreateTemplate(ctx context.Context, template *models.NotificationTemplate) error {
	result := r.db.WithContext(ctx).Create(template)
	if result.Error != nil {
		return fmt.Errorf("failed to create template: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) GetTemplate(ctx context.Context, name string, channel string) (*models.NotificationTemplate, error) {
	var template models.NotificationTemplate
	result := r.db.WithContext(ctx).
		Where("name = ? AND channel = ? AND is_active = ?", name, channel, true).
		First(&template)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get template: %w", result.Error)
	}
	
	return &template, nil
}

func (r *NotificationRepository) GetTemplates(ctx context.Context, notificationType models.NotificationType) ([]models.NotificationTemplate, error) {
	var templates []models.NotificationTemplate
	result := r.db.WithContext(ctx).
		Where("type = ? AND is_active = ?", notificationType, true).
		Order("channel ASC").
		Find(&templates)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get templates: %w", result.Error)
	}
	
	return templates, nil
}

func (r *NotificationRepository) UpdateTemplate(ctx context.Context, template *models.NotificationTemplate) error {
	result := r.db.WithContext(ctx).Save(template)
	if result.Error != nil {
		return fmt.Errorf("failed to update template: %w", result.Error)
	}
	return nil
}

// Statistics operations
func (r *NotificationRepository) CreateStats(ctx context.Context, stats *models.NotificationStats) error {
	result := r.db.WithContext(ctx).Create(stats)
	if result.Error != nil {
		return fmt.Errorf("failed to create stats: %w", result.Error)
	}
	return nil
}

func (r *NotificationRepository) UpdateStats(ctx context.Context, date time.Time, notificationType models.NotificationType, channel string, stats map[string]int64) error {
	// Use ON CONFLICT for PostgreSQL or equivalent for other databases
	existing := &models.NotificationStats{}
	result := r.db.WithContext(ctx).
		Where("date = ? AND type = ? AND channel = ?", date.Truncate(24*time.Hour), notificationType, channel).
		First(existing)
	
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to get existing stats: %w", result.Error)
	}
	
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		// Create new stats
		newStats := &models.NotificationStats{
			Date:           date.Truncate(24 * time.Hour),
			Type:           notificationType,
			Channel:        channel,
			TotalSent:      stats["sent"],
			TotalDelivered: stats["delivered"],
			TotalRead:      stats["read"],
			TotalFailed:    stats["failed"],
		}
		
		// Rates can be calculated on-demand from TotalSent, TotalDelivered, TotalRead
		
		return r.CreateStats(ctx, newStats)
	}
	
	// Update existing stats
	existing.TotalSent += stats["sent"]
	existing.TotalDelivered += stats["delivered"]
	existing.TotalRead += stats["read"]
	existing.TotalFailed += stats["failed"]
	
	// Rates can be calculated on-demand from TotalSent, TotalDelivered, TotalRead
	
	result = r.db.WithContext(ctx).Save(existing)
	if result.Error != nil {
		return fmt.Errorf("failed to update stats: %w", result.Error)
	}
	
	return nil
}

func (r *NotificationRepository) GetStats(ctx context.Context, startDate, endDate time.Time, notificationType models.NotificationType, channel string) ([]models.NotificationStats, error) {
	var stats []models.NotificationStats
	query := r.db.WithContext(ctx).Where("date BETWEEN ? AND ?", startDate, endDate)
	
	if notificationType != "" {
		query = query.Where("type = ?", notificationType)
	}
	
	if channel != "" {
		query = query.Where("channel = ?", channel)
	}
	
	result := query.Order("date ASC").Find(&stats)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get stats: %w", result.Error)
	}
	
	return stats, nil
}

// Bulk operations
func (r *NotificationRepository) BulkCreateNotifications(ctx context.Context, notifications []models.Notification) error {
	if len(notifications) == 0 {
		return nil
	}
	
	result := r.db.WithContext(ctx).CreateInBatches(notifications, 100)
	if result.Error != nil {
		return fmt.Errorf("failed to bulk create notifications: %w", result.Error)
	}
	
	return nil
}

func (r *NotificationRepository) BulkCreateDeliveries(ctx context.Context, deliveries []models.NotificationDelivery) error {
	if len(deliveries) == 0 {
		return nil
	}
	
	result := r.db.WithContext(ctx).CreateInBatches(deliveries, 100)
	if result.Error != nil {
		return fmt.Errorf("failed to bulk create deliveries: %w", result.Error)
	}
	
	return nil
}

// Search operations
func (r *NotificationRepository) SearchNotifications(ctx context.Context, userID string, query string, notificationType models.NotificationType, limit, offset int) ([]models.Notification, error) {
	var notifications []models.Notification
	
	dbQuery := r.db.WithContext(ctx).
		Preload("Sender").
		Where("user_id = ?", userID)
	
	if query != "" {
		searchPattern := "%" + query + "%"
		dbQuery = dbQuery.Where("title ILIKE ? OR message ILIKE ?", searchPattern, searchPattern)
	}
	
	if notificationType != "" {
		dbQuery = dbQuery.Where("type = ?", notificationType)
	}
	
	result := dbQuery.Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications)
	
	if result.Error != nil {
		return nil, fmt.Errorf("failed to search notifications: %w", result.Error)
	}
	
	return notifications, nil
}