package config

import (
	"time"

	"github.com/swork-team/platform/pkg/config"
)

// Config extends the base configuration with Notification-specific settings
type Config struct {
	config.BaseServiceConfig
	Notification  NotificationConfig    `json:"notification"`
	Email         EmailConfig           `json:"email"`
	Push          PushConfig            `json:"push"`
	SMS           SMSConfig             `json:"sms"`
	Webhook       WebhookConfig         `json:"webhook"`
	Queue         QueueConfig           `json:"queue"`
}

type NotificationConfig struct {
	MaxRetries              int           `json:"max_retries"`
	RetryInterval           time.Duration `json:"retry_interval"`
	BatchSize               int           `json:"batch_size"`
	ProcessingInterval      time.Duration `json:"processing_interval"`
	DeliveryTimeout         time.Duration `json:"delivery_timeout"`
	DefaultExpirationHours  int           `json:"default_expiration_hours"`
	MaxNotificationsPerUser int           `json:"max_notifications_per_user"`
	EnableRateLimiting      bool          `json:"enable_rate_limiting"`
	RateLimitPerMinute      int           `json:"rate_limit_per_minute"`
	CleanupInterval         time.Duration `json:"cleanup_interval"`
	RetentionDays           int           `json:"retention_days"`
}

type EmailConfig struct {
	Enabled      bool   `json:"enabled"`
	Provider     string `json:"provider"` // "smtp", "sendgrid", "ses"
	SMTPHost     string `json:"smtp_host"`
	SMTPPort     int    `json:"smtp_port"`
	SMTPUsername string `json:"smtp_username"`
	SMTPPassword string `json:"smtp_password"`
	SMTPUseTLS   bool   `json:"smtp_use_tls"`
	FromEmail    string `json:"from_email"`
	FromName     string `json:"from_name"`
	ReplyTo      string `json:"reply_to"`
	
	// SendGrid config
	SendGridAPIKey string `json:"sendgrid_api_key"`
	
	// AWS SES config
	AWSRegion          string `json:"aws_region"`
	AWSAccessKeyID     string `json:"aws_access_key_id"`
	AWSSecretAccessKey string `json:"aws_secret_access_key"`
	
	// Rate limiting
	MaxEmailsPerMinute int `json:"max_emails_per_minute"`
}

type PushConfig struct {
	Enabled bool `json:"enabled"`
	
	// Firebase Cloud Messaging
	FCMEnabled           bool   `json:"fcm_enabled"`
	FCMProjectID         string `json:"fcm_project_id"`
	FCMCredentialsPath   string `json:"fcm_credentials_path"`
	FCMCredentialsJSON   string `json:"fcm_credentials_json"`
	
	// Apple Push Notification Service
	APNsEnabled          bool   `json:"apns_enabled"`
	APNsKeyID            string `json:"apns_key_id"`
	APNsTeamID           string `json:"apns_team_id"`
	APNsBundleID         string `json:"apns_bundle_id"`
	APNsKeyPath          string `json:"apns_key_path"`
	APNsProduction       bool   `json:"apns_production"`
	
	// Web Push
	WebPushEnabled       bool   `json:"web_push_enabled"`
	WebPushVAPIDPublic   string `json:"web_push_vapid_public"`
	WebPushVAPIDPrivate  string `json:"web_push_vapid_private"`
	WebPushSubject       string `json:"web_push_subject"`
	
	// Rate limiting
	MaxPushesPerMinute   int    `json:"max_pushes_per_minute"`
}

type SMSConfig struct {
	Enabled         bool   `json:"enabled"`
	Provider        string `json:"provider"` // "twilio", "aws_sns"
	
	// Twilio config
	TwilioAccountSID string `json:"twilio_account_sid"`
	TwilioAuthToken  string `json:"twilio_auth_token"`
	TwilioFromNumber string `json:"twilio_from_number"`
	
	// AWS SNS config
	AWSRegion          string `json:"aws_region"`
	AWSAccessKeyID     string `json:"aws_access_key_id"`
	AWSSecretAccessKey string `json:"aws_secret_access_key"`
	
	MaxSMSPerMinute    int    `json:"max_sms_per_minute"`
}

type WebhookConfig struct {
	Enabled        bool          `json:"enabled"`
	Timeout        time.Duration `json:"timeout"`
	MaxRetries     int           `json:"max_retries"`
	RetryInterval  time.Duration `json:"retry_interval"`
	VerifySSL      bool          `json:"verify_ssl"`
}

type QueueConfig struct {
	Provider   string `json:"provider"` // "redis"
	
	// Redis queue config
	RedisPrefix    string `json:"redis_prefix"`
}


func LoadConfig() *Config {
	// Load base configuration
	baseConfig := config.LoadBaseConfig("NOTIFICATION")
	
	return &Config{
		BaseServiceConfig: *baseConfig,
		Notification: NotificationConfig{
			MaxRetries:              config.GetIntEnv("NOTIFICATION_MAX_RETRIES", 3),
			RetryInterval:           config.GetDurationEnv("NOTIFICATION_RETRY_INTERVAL", 5*time.Minute),
			BatchSize:               config.GetIntEnv("NOTIFICATION_BATCH_SIZE", 100),
			ProcessingInterval:      config.GetDurationEnv("NOTIFICATION_PROCESSING_INTERVAL", 30*time.Second),
			DeliveryTimeout:         config.GetDurationEnv("NOTIFICATION_DELIVERY_TIMEOUT", 30*time.Second),
			DefaultExpirationHours:  config.GetIntEnv("NOTIFICATION_DEFAULT_EXPIRATION_HOURS", 72),
			MaxNotificationsPerUser: config.GetIntEnv("NOTIFICATION_MAX_PER_USER", 1000),
			EnableRateLimiting:      config.GetBoolEnv("NOTIFICATION_ENABLE_RATE_LIMITING", true),
			RateLimitPerMinute:      config.GetIntEnv("NOTIFICATION_RATE_LIMIT_PER_MINUTE", 60),
			CleanupInterval:         config.GetDurationEnv("NOTIFICATION_CLEANUP_INTERVAL", 24*time.Hour),
			RetentionDays:           config.GetIntEnv("NOTIFICATION_RETENTION_DAYS", 30),
		},
		Email: EmailConfig{
			Enabled:            config.GetBoolEnv("EMAIL_ENABLED", true),
			Provider:           config.GetEnv("EMAIL_PROVIDER", "smtp"),
			SMTPHost:           config.GetEnv("SMTP_HOST", ""),
			SMTPPort:           config.GetIntEnv("SMTP_PORT", 587),
			SMTPUsername:       config.GetEnv("SMTP_USERNAME", ""),
			SMTPPassword:       config.GetEnv("SMTP_PASSWORD", ""),
			SMTPUseTLS:         config.GetBoolEnv("SMTP_USE_TLS", true),
			FromEmail:          config.GetEnv("EMAIL_FROM", "<EMAIL>"),
			FromName:           config.GetEnv("EMAIL_FROM_NAME", "Swork Team"),
			ReplyTo:            config.GetEnv("EMAIL_REPLY_TO", ""),
			SendGridAPIKey:     config.GetEnv("SENDGRID_API_KEY", ""),
			AWSRegion:          config.GetEnv("AWS_REGION", "us-east-1"),
			AWSAccessKeyID:     config.GetEnv("AWS_ACCESS_KEY_ID", ""),
			AWSSecretAccessKey: config.GetEnv("AWS_SECRET_ACCESS_KEY", ""),
			MaxEmailsPerMinute: config.GetIntEnv("EMAIL_MAX_PER_MINUTE", 100),
		},
		Push: PushConfig{
			Enabled:              config.GetBoolEnv("PUSH_ENABLED", true),
			FCMEnabled:           config.GetBoolEnv("FCM_ENABLED", true),
			FCMProjectID:         config.GetEnv("FCM_PROJECT_ID", ""),
			FCMCredentialsPath:   config.GetEnv("FCM_CREDENTIALS_PATH", ""),
			FCMCredentialsJSON:   config.GetEnv("FCM_CREDENTIALS_JSON", ""),
			APNsEnabled:          config.GetBoolEnv("APNS_ENABLED", false),
			APNsKeyID:            config.GetEnv("APNS_KEY_ID", ""),
			APNsTeamID:           config.GetEnv("APNS_TEAM_ID", ""),
			APNsBundleID:         config.GetEnv("APNS_BUNDLE_ID", ""),
			APNsKeyPath:          config.GetEnv("APNS_KEY_PATH", ""),
			APNsProduction:       config.GetBoolEnv("APNS_PRODUCTION", false),
			WebPushEnabled:       config.GetBoolEnv("WEB_PUSH_ENABLED", true),
			WebPushVAPIDPublic:   config.GetEnv("WEB_PUSH_VAPID_PUBLIC", ""),
			WebPushVAPIDPrivate:  config.GetEnv("WEB_PUSH_VAPID_PRIVATE", ""),
			WebPushSubject:       config.GetEnv("WEB_PUSH_SUBJECT", "mailto:<EMAIL>"),
			MaxPushesPerMinute:   config.GetIntEnv("PUSH_MAX_PER_MINUTE", 1000),
		},
		SMS: SMSConfig{
			Enabled:            config.GetBoolEnv("SMS_ENABLED", false),
			Provider:           config.GetEnv("SMS_PROVIDER", "twilio"),
			TwilioAccountSID:   config.GetEnv("TWILIO_ACCOUNT_SID", ""),
			TwilioAuthToken:    config.GetEnv("TWILIO_AUTH_TOKEN", ""),
			TwilioFromNumber:   config.GetEnv("TWILIO_FROM_NUMBER", ""),
			AWSRegion:          config.GetEnv("AWS_REGION", "us-east-1"),
			AWSAccessKeyID:     config.GetEnv("AWS_ACCESS_KEY_ID", ""),
			AWSSecretAccessKey: config.GetEnv("AWS_SECRET_ACCESS_KEY", ""),
			MaxSMSPerMinute:    config.GetIntEnv("SMS_MAX_PER_MINUTE", 30),
		},
		Webhook: WebhookConfig{
			Enabled:       config.GetBoolEnv("WEBHOOK_ENABLED", true),
			Timeout:       config.GetDurationEnv("WEBHOOK_TIMEOUT", 10*time.Second),
			MaxRetries:    config.GetIntEnv("WEBHOOK_MAX_RETRIES", 3),
			RetryInterval: config.GetDurationEnv("WEBHOOK_RETRY_INTERVAL", 5*time.Minute),
			VerifySSL:     config.GetBoolEnv("WEBHOOK_VERIFY_SSL", true),
		},
		Queue: QueueConfig{
			Provider:     config.GetEnv("QUEUE_PROVIDER", "redis"),
			RedisPrefix:  config.GetEnv("QUEUE_REDIS_PREFIX", "notification_queue"),
		},
	}
}

