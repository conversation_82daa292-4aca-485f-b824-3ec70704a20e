package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/clients"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	NotificationTypeEvent         NotificationType = "event"
	NotificationTypeMessage       NotificationType = "message"
	NotificationTypeSocial        NotificationType = "social"
	NotificationTypeTeam          NotificationType = "team"
	NotificationTypeSystem        NotificationType = "system"
	NotificationTypeReminder      NotificationType = "reminder"
	NotificationTypeCalendar      NotificationType = "calendar"
	NotificationTypeMention       NotificationType = "mention"
	NotificationTypeInvitation    NotificationType = "invitation"
	NotificationTypeTaskAssigned  NotificationType = "task_assigned"
	NotificationTypeTaskCompleted NotificationType = "task_completed"
)

// NotificationStatus represents the delivery status
type NotificationStatus string

const (
	NotificationStatusPending   NotificationStatus = "pending"
	NotificationStatusSent      NotificationStatus = "sent"
	NotificationStatusDelivered NotificationStatus = "delivered"
	NotificationStatusRead      NotificationStatus = "read"
	NotificationStatusFailed    NotificationStatus = "failed"
)

// NotificationPriority represents the priority level
type NotificationPriority string

const (
	NotificationPriorityLow    NotificationPriority = "low"
	NotificationPriorityNormal NotificationPriority = "normal"
	NotificationPriorityHigh   NotificationPriority = "high"
	NotificationPriorityUrgent NotificationPriority = "urgent"
)

// NotificationChannel represents delivery channels
type NotificationChannel string

const (
	ChannelInApp    NotificationChannel = "in_app"
	ChannelEmail    NotificationChannel = "email"
	ChannelPush     NotificationChannel = "push"
	ChannelSMS      NotificationChannel = "sms"
	ChannelWebhook  NotificationChannel = "webhook"
)

// DeviceType represents the type of device
type DeviceType string

const (
	DeviceTypeWeb     DeviceType = "web"
	DeviceTypeMobile  DeviceType = "mobile"
	DeviceTypeDesktop DeviceType = "desktop"
)

// NotificationChannel represents delivery channels as string constants

// NotificationChannels represents a slice of channels that can be stored as JSON
type NotificationChannels []string

// Value implements the driver.Valuer interface for GORM
func (nc NotificationChannels) Value() (driver.Value, error) {
	if nc == nil {
		return nil, nil
	}
	return json.Marshal(nc)
}

// Scan implements the sql.Scanner interface for GORM
func (nc *NotificationChannels) Scan(value interface{}) error {
	if value == nil {
		*nc = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, nc)
	case string:
		return json.Unmarshal([]byte(v), nc)
	default:
		return nil
	}
}

// NotificationData represents arbitrary JSON data
type NotificationData map[string]interface{}

// Value implements the driver.Valuer interface for GORM
func (nd NotificationData) Value() (driver.Value, error) {
	if nd == nil {
		return nil, nil
	}
	return json.Marshal(nd)
}

// Scan implements the sql.Scanner interface for GORM
func (nd *NotificationData) Scan(value interface{}) error {
	if value == nil {
		*nd = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, nd)
	case string:
		return json.Unmarshal([]byte(v), nd)
	default:
		return nil
	}
}

// Core notification model with clean GORM-compatible fields
type Notification struct {
	ID          uuid.UUID            `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	UserID      uuid.UUID            `json:"user_id" gorm:"type:uuid;not null;index"`
	Type        NotificationType     `json:"type" gorm:"type:varchar(50);not null;index"`
	Title       string               `json:"title" gorm:"not null"`
	Message     string               `json:"message" gorm:"not null"`
	Data        NotificationData     `json:"data,omitempty" gorm:"type:jsonb"`
	Status      NotificationStatus   `json:"status" gorm:"type:varchar(20);not null;default:'pending';index"`
	Priority    NotificationPriority `json:"priority" gorm:"type:varchar(20);not null;default:'normal';index"`
	SourceType  string               `json:"source_type,omitempty" gorm:"index"`
	SourceID    string               `json:"source_id,omitempty" gorm:"index"`
	SenderID    *uuid.UUID           `json:"sender_id,omitempty" gorm:"type:uuid;index"`
	ActionURL   string               `json:"action_url,omitempty"`
	ImageURL    string               `json:"image_url,omitempty"`
	ExpiresAt   *time.Time           `json:"expires_at,omitempty"`
	ReadAt      *time.Time           `json:"read_at,omitempty"`
	SentAt      *time.Time           `json:"sent_at,omitempty"`
	DeliveredAt *time.Time           `json:"delivered_at,omitempty"`
	CreatedAt   time.Time            `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time            `json:"updated_at" gorm:"autoUpdateTime"`

	// Relations
	User       *clients.User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Sender     *clients.User          `json:"sender,omitempty" gorm:"foreignKey:SenderID"`
	Deliveries []NotificationDelivery `json:"deliveries,omitempty"`
}

// NotificationDelivery tracks delivery attempts per channel
type NotificationDelivery struct {
	ID             uuid.UUID           `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	NotificationID uuid.UUID           `json:"notification_id" gorm:"type:uuid;not null;index"`
	Channel        NotificationChannel `json:"channel" gorm:"type:varchar(20);not null;index"`
	Status         NotificationStatus  `json:"status" gorm:"type:varchar(20);not null;default:'pending'"`
	AttemptCount   int                 `json:"attempt_count" gorm:"default:0"`
	LastAttemptAt  *time.Time          `json:"last_attempt_at,omitempty"`
	DeliveredAt    *time.Time          `json:"delivered_at,omitempty"`
	FailureReason  string              `json:"failure_reason,omitempty"`
	ExternalID     string              `json:"external_id,omitempty"`
	CreatedAt      time.Time           `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time           `json:"updated_at" gorm:"autoUpdateTime"`

	// Relations
	Notification *Notification `json:"notification,omitempty" gorm:"foreignKey:NotificationID"`
}

// UserNotificationPreference stores user channel preferences
type UserNotificationPreference struct {
	ID          uuid.UUID                `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	UserID      uuid.UUID                `json:"user_id" gorm:"type:uuid;not null;uniqueIndex:idx_user_type"`
	Type        NotificationType         `json:"type" gorm:"type:varchar(50);not null;uniqueIndex:idx_user_type"`
	Channels    NotificationChannels     `json:"channels" gorm:"type:jsonb"`
	Enabled     bool                     `json:"enabled" gorm:"default:true"`
	MinPriority NotificationPriority     `json:"min_priority" gorm:"type:varchar(20);default:'low'"`
	QuietHours  *QuietHours             `json:"quiet_hours,omitempty" gorm:"embedded"`
	CreatedAt   time.Time               `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time               `json:"updated_at" gorm:"autoUpdateTime"`
}

// QuietHours represents quiet period settings
type QuietHours struct {
	Enabled   bool   `json:"enabled"`
	StartTime string `json:"start_time" gorm:"type:varchar(5)"` // HH:MM format
	EndTime   string `json:"end_time" gorm:"type:varchar(5)"`   // HH:MM format
	Timezone  string `json:"timezone" gorm:"type:varchar(50)"`
}

// Device represents user devices for push notifications
type Device struct {
	ID         uuid.UUID  `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	UserID     uuid.UUID  `json:"user_id" gorm:"type:uuid;not null;index"`
	Type       DeviceType `json:"type" gorm:"type:varchar(20);not null"`
	Platform   string     `json:"platform" gorm:"type:varchar(20);not null"` // "android", "ios", "web"
	Token      string     `json:"token" gorm:"not null;uniqueIndex"`
	AppVersion string     `json:"app_version,omitempty" gorm:"type:varchar(20)"`
	OSVersion  string     `json:"os_version,omitempty" gorm:"type:varchar(20)"`
	IsActive   bool       `json:"is_active" gorm:"default:true"`
	LastUsedAt *time.Time `json:"last_used_at,omitempty"`
	CreatedAt  time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt  time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// NotificationTemplate for templated messages
type NotificationTemplate struct {
	ID        uuid.UUID        `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	Name      string           `json:"name" gorm:"not null;uniqueIndex"`
	Type      NotificationType `json:"type" gorm:"type:varchar(50);not null"`
	Channel   string           `json:"channel" gorm:"type:varchar(20);not null"`
	Subject   string           `json:"subject,omitempty"`
	Title     string           `json:"title" gorm:"not null"`
	Body      string           `json:"body" gorm:"not null"`
	Variables []string         `json:"variables" gorm:"type:jsonb"`
	IsActive  bool             `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time        `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time        `json:"updated_at" gorm:"autoUpdateTime"`
}

// NotificationStats for analytics
type NotificationStats struct {
	ID             uuid.UUID        `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	Date           time.Time        `json:"date" gorm:"not null;index"`
	Type           NotificationType `json:"type" gorm:"type:varchar(50);not null"`
	Channel        string           `json:"channel" gorm:"type:varchar(20);not null"`
	TotalSent      int64            `json:"total_sent" gorm:"default:0"`
	TotalDelivered int64            `json:"total_delivered" gorm:"default:0"`
	TotalRead      int64            `json:"total_read" gorm:"default:0"`
	TotalFailed    int64            `json:"total_failed" gorm:"default:0"`
	CreatedAt      time.Time        `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time        `json:"updated_at" gorm:"autoUpdateTime"`
}

// Helper methods for UserNotificationPreference
func (p *UserNotificationPreference) IsChannelEnabled(channel NotificationChannel) bool {
	if !p.Enabled {
		return false
	}
	
	if len(p.Channels) == 0 {
		return true // Default to enabled if no specific channels set
	}
	
	for _, c := range p.Channels {
		if c == string(channel) {
			return true
		}
	}
	return false
}

func (p *UserNotificationPreference) IsInQuietHours(t time.Time) bool {
	if p.QuietHours == nil || !p.QuietHours.Enabled {
		return false
	}
	
	// Parse start and end times
	startTime, err1 := time.Parse("15:04", p.QuietHours.StartTime)
	endTime, err2 := time.Parse("15:04", p.QuietHours.EndTime)
	
	if err1 != nil || err2 != nil {
		return false // Invalid time format
	}
	
	// Convert to same day for comparison
	now := time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	start := time.Date(t.Year(), t.Month(), t.Day(), startTime.Hour(), startTime.Minute(), 0, 0, t.Location())
	end := time.Date(t.Year(), t.Month(), t.Day(), endTime.Hour(), endTime.Minute(), 0, 0, t.Location())
	
	// Handle overnight quiet hours (e.g., 22:00 to 06:00)
	if start.After(end) {
		return now.After(start) || now.Before(end)
	}
	
	return now.After(start) && now.Before(end)
}


// Additional methods for models

// MarkAsSent marks a notification as sent
func (n *Notification) MarkAsSent() {
	now := time.Now()
	n.Status = NotificationStatusSent
	n.SentAt = &now
}

// MarkAsDelivered marks a notification as delivered
func (n *Notification) MarkAsDelivered() {
	now := time.Now()
	n.Status = NotificationStatusDelivered
	n.DeliveredAt = &now
}

// MarkAsRead marks a notification as read
func (n *Notification) MarkAsRead() {
	now := time.Now()
	n.Status = NotificationStatusRead
	n.ReadAt = &now
}

// NotificationDelivery methods

// CanRetry checks if delivery can be retried
func (d *NotificationDelivery) CanRetry() bool {
	return d.AttemptCount < 3 && d.Status == NotificationStatusFailed
}

// MarkAsFailed marks delivery as failed
func (d *NotificationDelivery) MarkAsFailed(reason string) {
	now := time.Now()
	d.Status = NotificationStatusFailed
	d.FailureReason = reason
	d.LastAttemptAt = &now
}

// MarkAsDelivered marks delivery as delivered
func (d *NotificationDelivery) MarkAsDelivered(externalID string) {
	now := time.Now()
	d.Status = NotificationStatusDelivered
	d.DeliveredAt = &now
	d.ExternalID = externalID
}

// IncrementAttempt increments the attempt count
func (d *NotificationDelivery) IncrementAttempt() {
	d.AttemptCount++
	now := time.Now()
	d.LastAttemptAt = &now
}

// Device methods

// IsValidForPush checks if device is valid for push notifications
func (d *Device) IsValidForPush() bool {
	return d.IsActive && d.Token != "" && (d.Type == DeviceTypeMobile || d.Type == DeviceTypeWeb)
}

