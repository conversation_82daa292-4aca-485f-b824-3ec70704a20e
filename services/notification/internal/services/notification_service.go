package services

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/swork-team/platform/pkg/clients"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/notification/internal/config"
	"github.com/swork-team/platform/services/notification/internal/models"
	"github.com/swork-team/platform/services/notification/internal/repositories"
)

type NotificationService struct {
	notificationRepo *repositories.NotificationRepository
	config           *config.Config
	emailService     EmailService
	pushService      PushService
	smsService       SMSService
	webhookService   WebhookService
	userClient       clients.UserServiceClient
	batchPopulator   *utils.BatchPopulator
}

func NewNotificationService(
	notificationRepo *repositories.NotificationRepository,
	config *config.Config,
	emailService EmailService,
	pushService PushService,
	smsService SMSService,
	webhookService WebhookService,
	redisClient *redis.Client,
) *NotificationService {
	userClient := clients.NewUserServiceClient(config.Services.User, redisClient)
	teamClient := clients.NewTeamServiceClient(config.Services.Team, redisClient)
	
	return &NotificationService{
		notificationRepo: notificationRepo,
		config:           config,
		emailService:     emailService,
		pushService:      pushService,
		smsService:       smsService,
		webhookService:   webhookService,
		userClient:       userClient,
		batchPopulator:   utils.NewBatchPopulator(userClient, teamClient),
	}
}

// Core notification operations
func (s *NotificationService) SendNotification(ctx context.Context, req *SendNotificationRequest) (*models.Notification, error) {
	// Validate request
	if err := s.validateNotificationRequest(req); err != nil {
		return nil, err
	}

	// Get user preferences
	preferences, err := s.notificationRepo.GetUserPreferences(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user preferences: %w", err)
	}

	// Check if notification type is enabled for user
	preference := s.findPreference(preferences, req.Type)
	if preference == nil || !preference.Enabled {
		return nil, errors.New("notification type disabled for user")
	}

	// Check quiet hours
	if preference.IsInQuietHours(time.Now()) {
		// Schedule for later or skip based on priority
		if req.Priority != models.NotificationPriorityUrgent {
			return nil, errors.New("notification blocked by quiet hours")
		}
	}

	// Check priority threshold
	if !s.meetsMinimumPriority(req.Priority, preference.MinPriority) {
		return nil, errors.New("notification priority below user threshold")
	}

	// Parse UserID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Create notification
	notification := &models.Notification{
		UserID:     userUUID,
		Type:       req.Type,
		Title:      req.Title,
		Message:    req.Message,
		Data:       req.Data,
		Priority:   req.Priority,
		SourceType: req.SourceType,
		SourceID:   req.SourceID,
		ActionURL:  req.ActionURL,
		ImageURL:   req.ImageURL,
		Status:     models.NotificationStatusPending,
	}

	// Parse SenderID if provided
	if req.SenderID != "" {
		senderUUID, err := uuid.Parse(req.SenderID)
		if err != nil {
			return nil, fmt.Errorf("invalid sender ID: %w", err)
		}
		notification.SenderID = &senderUUID
	}

	// Set expiration
	if req.ExpiresAt != nil {
		notification.ExpiresAt = req.ExpiresAt
	} else {
		defaultExpiration := time.Now().Add(time.Duration(s.config.Notification.DefaultExpirationHours) * time.Hour)
		notification.ExpiresAt = &defaultExpiration
	}

	if err := s.notificationRepo.CreateNotification(ctx, notification); err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	// Schedule delivery for enabled channels
	channels := req.Channels
	if len(channels) == 0 {
		// Convert NotificationChannels ([]string) to []NotificationChannel
		channels = make([]models.NotificationChannel, len(preference.Channels))
		for i, ch := range preference.Channels {
			channels[i] = models.NotificationChannel(ch)
		}
	}

	for _, channel := range channels {
		if preference.IsChannelEnabled(channel) {
			delivery := &models.NotificationDelivery{
				NotificationID: notification.ID,
				Channel:        channel,
				Status:         models.NotificationStatusPending,
			}
			
			if err := s.notificationRepo.CreateDelivery(ctx, delivery); err != nil {
				// Log error but don't fail the entire operation
				fmt.Printf("Failed to create delivery for channel %s: %v\n", channel, err)
			}
		}
	}

	// Process delivery immediately for high priority notifications
	if req.Priority == models.NotificationPriorityUrgent || req.Priority == models.NotificationPriorityHigh {
		go s.processNotificationDelivery(notification)
	}

	return notification, nil
}

func (s *NotificationService) SendBulkNotification(ctx context.Context, req *SendBulkNotificationRequest) error {
	// Validate request
	if len(req.UserIDs) == 0 {
		return errors.New("no users specified")
	}

	if len(req.UserIDs) > 1000 {
		return errors.New("too many users (max 1000)")
	}

	notifications := make([]models.Notification, 0, len(req.UserIDs))
	deliveries := make([]models.NotificationDelivery, 0, len(req.UserIDs)*len(req.Channels))

	for _, userID := range req.UserIDs {
		// Get user preferences
		preferences, err := s.notificationRepo.GetUserPreferences(ctx, userID)
		if err != nil {
			continue // Skip this user but continue with others
		}

		preference := s.findPreference(preferences, req.Type)
		if preference == nil || !preference.Enabled {
			continue
		}

		// Check priority and quiet hours
		if preference.IsInQuietHours(time.Now()) && req.Priority != models.NotificationPriorityUrgent {
			continue
		}

		if !s.meetsMinimumPriority(req.Priority, preference.MinPriority) {
			continue
		}

		// Parse UserID to UUID
		userUUID, err := uuid.Parse(userID)
		if err != nil {
			continue // Skip invalid user ID
		}

		// Create notification
		notification := models.Notification{
			UserID:     userUUID,
			Type:       req.Type,
			Title:      req.Title,
			Message:    req.Message,
			Data:       req.Data,
			Priority:   req.Priority,
			SourceType: req.SourceType,
			SourceID:   req.SourceID,
			ActionURL:  req.ActionURL,
			ImageURL:   req.ImageURL,
			Status:     models.NotificationStatusPending,
		}

		// Parse SenderID if provided
		if req.SenderID != "" {
			senderUUID, err := uuid.Parse(req.SenderID)
			if err == nil {
				notification.SenderID = &senderUUID
			}
		}

		// Set expiration
		if req.ExpiresAt != nil {
			notification.ExpiresAt = req.ExpiresAt
		} else {
			defaultExpiration := time.Now().Add(time.Duration(s.config.Notification.DefaultExpirationHours) * time.Hour)
			notification.ExpiresAt = &defaultExpiration
		}

		notifications = append(notifications, notification)

		// Create deliveries for enabled channels
		channels := req.Channels
		if len(channels) == 0 {
			// Convert NotificationChannels ([]string) to []NotificationChannel
			channels = make([]models.NotificationChannel, len(preference.Channels))
			for i, ch := range preference.Channels {
				channels[i] = models.NotificationChannel(ch)
			}
		}

		for _, channel := range channels {
			if preference.IsChannelEnabled(channel) {
				delivery := models.NotificationDelivery{
					NotificationID: notification.ID,
					Channel:        channel,
					Status:         models.NotificationStatusPending,
				}
				deliveries = append(deliveries, delivery)
			}
		}
	}

	// Bulk create notifications
	if err := s.notificationRepo.BulkCreateNotifications(ctx, notifications); err != nil {
		return fmt.Errorf("failed to create bulk notifications: %w", err)
	}

	// Update delivery notification IDs - match by index since they correspond
	deliveryIndex := 0
	for i := range notifications {
		// Count how many deliveries this notification should have
		channels := req.Channels
		if len(channels) == 0 {
			// Get user preferences to determine channels
			preferences, _ := s.notificationRepo.GetUserPreferences(ctx, notifications[i].UserID.String())
			preference := s.findPreference(preferences, req.Type)
			if preference != nil {
				channels = make([]models.NotificationChannel, len(preference.Channels))
				for k, ch := range preference.Channels {
					channels[k] = models.NotificationChannel(ch)
				}
			}
		}

		// Update deliveries for this notification
		for j := 0; j < len(channels) && deliveryIndex < len(deliveries); j++ {
			deliveries[deliveryIndex].NotificationID = notifications[i].ID
			deliveryIndex++
		}
	}

	if err := s.notificationRepo.BulkCreateDeliveries(ctx, deliveries); err != nil {
		return fmt.Errorf("failed to create bulk deliveries: %w", err)
	}

	return nil
}

func (s *NotificationService) GetUserNotifications(ctx context.Context, userID string, req *GetNotificationsRequest) (*GetNotificationsResponse, error) {
	notifications, err := s.notificationRepo.GetUserNotifications(ctx, userID, req.Limit, req.Offset, req.UnreadOnly)
	if err != nil {
		return nil, fmt.Errorf("failed to get user notifications: %w", err)
	}

	unreadCount, err := s.notificationRepo.GetUnreadCount(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get unread count: %w", err)
	}

	return &GetNotificationsResponse{
		Notifications: notifications,
		UnreadCount:   unreadCount,
		Total:         int64(len(notifications)),
	}, nil
}

func (s *NotificationService) MarkAsRead(ctx context.Context, userID string, notificationID uuid.UUID) error {
	return s.notificationRepo.MarkAsRead(ctx, notificationID, userID)
}

func (s *NotificationService) MarkAllAsRead(ctx context.Context, userID string) error {
	return s.notificationRepo.MarkAllAsRead(ctx, userID)
}

func (s *NotificationService) DeleteNotification(ctx context.Context, userID string, notificationID uuid.UUID) error {
	return s.notificationRepo.DeleteNotification(ctx, notificationID, userID)
}

// Device management
func (s *NotificationService) RegisterDevice(ctx context.Context, req *RegisterDeviceRequest) (*models.Device, error) {
	// Check if device already exists
	existingDevice, err := s.notificationRepo.GetDeviceByToken(ctx, req.Token)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing device: %w", err)
	}

	if existingDevice != nil {
		// Parse UserID to UUID
		userUUID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}

		// Update existing device
		existingDevice.UserID = userUUID
		existingDevice.Type = req.Type
		existingDevice.Platform = req.Platform
		existingDevice.AppVersion = req.AppVersion
		existingDevice.OSVersion = req.OSVersion
		// Device name not needed in current model
		existingDevice.IsActive = true
		now := time.Now()
		existingDevice.LastUsedAt = &now

		if err := s.notificationRepo.UpdateDevice(ctx, existingDevice); err != nil {
			return nil, fmt.Errorf("failed to update device: %w", err)
		}

		return existingDevice, nil
	}

	// Parse UserID to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Create new device
	device := &models.Device{
		UserID:      userUUID,
		Type:        req.Type,
		Token:       req.Token,
		Platform:    req.Platform,
		AppVersion:  req.AppVersion,
		OSVersion:   req.OSVersion,
		// DeviceName not in current model
		IsActive:    true,
	}
	now := time.Now()
	device.LastUsedAt = &now

	if err := s.notificationRepo.CreateDevice(ctx, device); err != nil {
		return nil, fmt.Errorf("failed to create device: %w", err)
	}

	return device, nil
}

func (s *NotificationService) UnregisterDevice(ctx context.Context, token string) error {
	return s.notificationRepo.DeactivateDevice(ctx, token)
}

func (s *NotificationService) GetUserDevices(ctx context.Context, userID string) ([]models.Device, error) {
	return s.notificationRepo.GetUserDevices(ctx, userID, "")
}

// Preferences management
func (s *NotificationService) UpdatePreferences(ctx context.Context, userID string, req *UpdatePreferencesRequest) error {
	for notificationType, settings := range req.Preferences {
		preference, err := s.notificationRepo.GetUserPreference(ctx, userID, notificationType)
		if err != nil {
			return fmt.Errorf("failed to get preference: %w", err)
		}

		if preference == nil {
			// Parse UserID to UUID
			userUUID, err := uuid.Parse(userID)
			if err != nil {
				return fmt.Errorf("invalid user ID: %w", err)
			}

			// Create new preference
			preference = &models.UserNotificationPreference{
				UserID:      userUUID,
				Type:        notificationType,
				Enabled:     settings.Enabled,
				Channels:    convertChannelsToStrings(settings.Channels),
				MinPriority: settings.MinPriority,
			}

			if settings.QuietHours != nil {
				preference.QuietHours = settings.QuietHours
			}

			if err := s.notificationRepo.CreatePreference(ctx, preference); err != nil {
				return fmt.Errorf("failed to create preference: %w", err)
			}
		} else {
			// Update existing preference
			preference.Enabled = settings.Enabled
			preference.Channels = convertChannelsToStrings(settings.Channels)
			preference.MinPriority = settings.MinPriority

			if settings.QuietHours != nil {
				preference.QuietHours = settings.QuietHours
			}

			if err := s.notificationRepo.UpdatePreference(ctx, preference); err != nil {
				return fmt.Errorf("failed to update preference: %w", err)
			}
		}
	}

	return nil
}

func (s *NotificationService) GetUserPreferences(ctx context.Context, userID string) ([]models.UserNotificationPreference, error) {
	return s.notificationRepo.GetUserPreferences(ctx, userID)
}

// Background processing
func (s *NotificationService) ProcessPendingNotifications(ctx context.Context) error {
	notifications, err := s.notificationRepo.GetPendingNotifications(ctx, s.config.Notification.BatchSize)
	if err != nil {
		return fmt.Errorf("failed to get pending notifications: %w", err)
	}

	for _, notification := range notifications {
		go s.processNotificationDelivery(&notification)
	}

	return nil
}

func (s *NotificationService) ProcessFailedDeliveries(ctx context.Context) error {
	deliveries, err := s.notificationRepo.GetFailedDeliveries(ctx, s.config.Notification.BatchSize)
	if err != nil {
		return fmt.Errorf("failed to get failed deliveries: %w", err)
	}

	for _, delivery := range deliveries {
		if delivery.CanRetry() {
			go s.retryDelivery(&delivery)
		}
	}

	return nil
}

func (s *NotificationService) CleanupExpiredNotifications(ctx context.Context) (int64, error) {
	return s.notificationRepo.CleanupExpiredNotifications(ctx, s.config.Notification.RetentionDays)
}

// Template operations
func (s *NotificationService) SendTemplatedNotification(ctx context.Context, req *SendTemplatedNotificationRequest) (*models.Notification, error) {
	// Get template
	template, err := s.notificationRepo.GetTemplate(ctx, req.TemplateName, string(req.Channel))
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	if template == nil {
		return nil, errors.New("template not found")
	}

	// Process template
	title, message, err := s.processTemplate(template, req.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to process template: %w", err)
	}

	// Send notification
	sendReq := &SendNotificationRequest{
		UserID:     req.UserID,
		Type:       template.Type,
		Title:      title,
		Message:    message,
		Data:       req.Data,
		Priority:   req.Priority,
		Channels:   []models.NotificationChannel{req.Channel},
		SourceType: req.SourceType,
		SourceID:   req.SourceID,
		SenderID:   req.SenderID,
		ActionURL:  req.ActionURL,
		ImageURL:   req.ImageURL,
		ExpiresAt:  req.ExpiresAt,
	}

	return s.SendNotification(ctx, sendReq)
}

// Search and statistics
func (s *NotificationService) SearchNotifications(ctx context.Context, userID string, req *SearchNotificationsRequest) ([]models.Notification, error) {
	return s.notificationRepo.SearchNotifications(ctx, userID, req.Query, req.Type, req.Limit, req.Offset)
}

func (s *NotificationService) GetStatistics(ctx context.Context, req *GetStatisticsRequest) ([]models.NotificationStats, error) {
	return s.notificationRepo.GetStats(ctx, req.StartDate, req.EndDate, req.Type, string(req.Channel))
}

// Helper methods
func (s *NotificationService) validateNotificationRequest(req *SendNotificationRequest) error {
	if req.UserID == "" {
		return errors.New("user ID is required")
	}

	if req.Title == "" {
		return errors.New("title is required")
	}

	if req.Message == "" {
		return errors.New("message is required")
	}

	if len(req.Title) > 200 {
		return errors.New("title too long (max 200 characters)")
	}

	if len(req.Message) > 500 {
		return errors.New("message too long (max 500 characters)")
	}

	return nil
}

func (s *NotificationService) findPreference(preferences []models.UserNotificationPreference, notificationType models.NotificationType) *models.UserNotificationPreference {
	for _, pref := range preferences {
		if pref.Type == notificationType {
			return &pref
		}
	}
	return nil
}

func (s *NotificationService) meetsMinimumPriority(priority, minPriority models.NotificationPriority) bool {
	priorityValues := map[models.NotificationPriority]int{
		models.NotificationPriorityLow:    1,
		models.NotificationPriorityNormal: 2,
		models.NotificationPriorityHigh:   3,
		models.NotificationPriorityUrgent: 4,
	}

	return priorityValues[priority] >= priorityValues[minPriority]
}

func (s *NotificationService) processNotificationDelivery(notification *models.Notification) {
	ctx := context.Background()

	// Get all pending deliveries for this notification
	for _, delivery := range notification.Deliveries {
		if delivery.Status != models.NotificationStatusPending {
			continue
		}

		var err error
		switch delivery.Channel {
		case models.ChannelInApp:
			// In-app notifications are handled by storing in database
			err = s.markDeliveryAsDelivered(&delivery, "")
		case models.ChannelEmail:
			err = s.sendEmailNotification(ctx, notification, &delivery)
		case models.ChannelPush:
			err = s.sendPushNotification(ctx, notification, &delivery)
		case models.ChannelSMS:
			err = s.sendSMSNotification(ctx, notification, &delivery)
		case models.ChannelWebhook:
			err = s.sendWebhookNotification(ctx, notification, &delivery)
		default:
			err = fmt.Errorf("unsupported channel: %s", delivery.Channel)
		}

		if err != nil {
			delivery.MarkAsFailed(err.Error())
		}

		// Update delivery status
		s.notificationRepo.UpdateDelivery(ctx, &delivery)
	}

	// Update notification status
	notification.MarkAsSent()
	s.notificationRepo.UpdateNotification(ctx, notification)
}

func (s *NotificationService) sendEmailNotification(ctx context.Context, notification *models.Notification, delivery *models.NotificationDelivery) error {
	if !s.config.Email.Enabled {
		return errors.New("email notifications disabled")
	}

	// Get user info (this would typically come from user service)
	userInfo := s.getUserInfo(ctx, notification.UserID.String())
	if userInfo == nil || userInfo.Email == "" {
		return errors.New("user email not found")
	}

	emailReq := &EmailRequest{
		To:      []string{userInfo.Email},
		Subject: notification.Title,
		Body:    notification.Message,
		IsHTML:  false,
	}

	externalID, err := s.emailService.SendEmail(ctx, emailReq)
	if err != nil {
		return err
	}

	return s.markDeliveryAsDelivered(delivery, externalID)
}

func (s *NotificationService) sendPushNotification(ctx context.Context, notification *models.Notification, delivery *models.NotificationDelivery) error {
	if !s.config.Push.Enabled {
		return errors.New("push notifications disabled")
	}

	// Get user devices
	devices, err := s.notificationRepo.GetUserDevices(ctx, notification.UserID.String(), models.DeviceTypeMobile)
	if err != nil {
		return fmt.Errorf("failed to get user devices: %w", err)
	}

	if len(devices) == 0 {
		return errors.New("no push devices found")
	}

	var lastErr error
	successCount := 0

	for _, device := range devices {
		if !device.IsValidForPush() {
			continue
		}

		pushReq := &PushRequest{
			Token:   device.Token,
			Title:   notification.Title,
			Body:    notification.Message,
			Data:    notification.Data,
			ImageURL: notification.ImageURL,
		}

		externalID, err := s.pushService.SendPush(ctx, pushReq)
		if err != nil {
			lastErr = err
			continue
		}

		successCount++
		if successCount == 1 { // Mark as delivered on first success
			s.markDeliveryAsDelivered(delivery, externalID)
		}
	}

	if successCount == 0 && lastErr != nil {
		return lastErr
	}

	return nil
}

func (s *NotificationService) sendSMSNotification(ctx context.Context, notification *models.Notification, delivery *models.NotificationDelivery) error {
	if !s.config.SMS.Enabled {
		return errors.New("SMS notifications disabled")
	}

	// Get user info
	userInfo := s.getUserInfo(ctx, notification.UserID.String())
	if userInfo == nil || userInfo.Phone == "" {
		return errors.New("user phone number not found")
	}

	smsReq := &SMSRequest{
		To:      userInfo.Phone,
		Message: notification.Title + ": " + notification.Message,
	}

	externalID, err := s.smsService.SendSMS(ctx, smsReq)
	if err != nil {
		return err
	}

	return s.markDeliveryAsDelivered(delivery, externalID)
}

func (s *NotificationService) sendWebhookNotification(ctx context.Context, notification *models.Notification, delivery *models.NotificationDelivery) error {
	if !s.config.Webhook.Enabled {
		return errors.New("webhook notifications disabled")
	}

	// This would typically get webhook URLs from user preferences or system config
	webhookURL := s.getWebhookURL(notification.UserID.String())
	if webhookURL == "" {
		return errors.New("no webhook URL configured")
	}

	webhookReq := &WebhookRequest{
		URL:     webhookURL,
		Payload: notification,
	}

	externalID, err := s.webhookService.SendWebhook(ctx, webhookReq)
	if err != nil {
		return err
	}

	return s.markDeliveryAsDelivered(delivery, externalID)
}

func (s *NotificationService) retryDelivery(delivery *models.NotificationDelivery) {
	// Implement exponential backoff
	delay := time.Duration(delivery.AttemptCount*delivery.AttemptCount) * s.config.Notification.RetryInterval
	time.Sleep(delay)

	delivery.IncrementAttempt()

	// Retry the specific delivery
	ctx := context.Background()

	// Get notification
	notification, err := s.notificationRepo.GetNotificationByID(ctx, delivery.NotificationID)
	if err != nil || notification == nil {
		delivery.MarkAsFailed("notification not found")
		s.notificationRepo.UpdateDelivery(ctx, delivery)
		return
	}

	// Retry based on channel
	switch delivery.Channel {
	case models.ChannelEmail:
		err = s.sendEmailNotification(ctx, notification, delivery)
	case models.ChannelPush:
		err = s.sendPushNotification(ctx, notification, delivery)
	case models.ChannelSMS:
		err = s.sendSMSNotification(ctx, notification, delivery)
	case models.ChannelWebhook:
		err = s.sendWebhookNotification(ctx, notification, delivery)
	default:
		err = fmt.Errorf("unsupported channel: %s", delivery.Channel)
	}

	if err != nil {
		delivery.MarkAsFailed(err.Error())
	}

	s.notificationRepo.UpdateDelivery(ctx, delivery)
}

func (s *NotificationService) markDeliveryAsDelivered(delivery *models.NotificationDelivery, externalID string) error {
	delivery.MarkAsDelivered(externalID)
	return nil
}

func (s *NotificationService) processTemplate(template *models.NotificationTemplate, data map[string]interface{}) (string, string, error) {
	title := template.Title
	body := template.Body

	// Simple template variable replacement
	for key, value := range data {
		placeholder := fmt.Sprintf("{{%s}}", key)
		valueStr := fmt.Sprintf("%v", value)
		title = strings.ReplaceAll(title, placeholder, valueStr)
		body = strings.ReplaceAll(body, placeholder, valueStr)
	}

	return title, body, nil
}

func (s *NotificationService) getUserInfo(ctx context.Context, userID string) *UserInfo {
	user, err := s.userClient.GetUser(ctx, userID)
	if err != nil {
		fmt.Printf("Failed to get user info for %s: %v\n", userID, err)
		// Return basic fallback info
		return &UserInfo{
			ID:    userID,
			Email: "",
			Phone: "",
		}
	}
	
	return &UserInfo{
		ID:    user.ID.String(),
		Email: user.Email,
		Phone: user.PhoneNumber,
	}
}

func (s *NotificationService) getWebhookURL(userID string) string {
	// TODO: Get webhook URL from user preferences or system config
	return ""
}

// Service interfaces (to be implemented by specific services)
type EmailService interface {
	SendEmail(ctx context.Context, req *EmailRequest) (string, error)
}

type PushService interface {
	SendPush(ctx context.Context, req *PushRequest) (string, error)
}

type SMSService interface {
	SendSMS(ctx context.Context, req *SMSRequest) (string, error)
}

type WebhookService interface {
	SendWebhook(ctx context.Context, req *WebhookRequest) (string, error)
}

// Request/Response types
type SendNotificationRequest struct {
	UserID     string                       `json:"user_id" binding:"required"`
	Type       models.NotificationType      `json:"type" binding:"required"`
	Title      string                       `json:"title" binding:"required"`
	Message    string                       `json:"message" binding:"required"`
	Data       map[string]interface{}       `json:"data,omitempty"`
	Priority   models.NotificationPriority  `json:"priority,omitempty"`
	Channels   []models.NotificationChannel `json:"channels,omitempty"`
	SourceType string                       `json:"source_type,omitempty"`
	SourceID   string                       `json:"source_id,omitempty"`
	SenderID   string                       `json:"sender_id,omitempty"`
	ActionURL  string                       `json:"action_url,omitempty"`
	ImageURL   string                       `json:"image_url,omitempty"`
	ExpiresAt  *time.Time                   `json:"expires_at,omitempty"`
}

type SendBulkNotificationRequest struct {
	UserIDs    []string                     `json:"user_ids" binding:"required"`
	Type       models.NotificationType      `json:"type" binding:"required"`
	Title      string                       `json:"title" binding:"required"`
	Message    string                       `json:"message" binding:"required"`
	Data       map[string]interface{}       `json:"data,omitempty"`
	Priority   models.NotificationPriority  `json:"priority,omitempty"`
	Channels   []models.NotificationChannel `json:"channels,omitempty"`
	SourceType string                       `json:"source_type,omitempty"`
	SourceID   string                       `json:"source_id,omitempty"`
	SenderID   string                       `json:"sender_id,omitempty"`
	ActionURL  string                       `json:"action_url,omitempty"`
	ImageURL   string                       `json:"image_url,omitempty"`
	ExpiresAt  *time.Time                   `json:"expires_at,omitempty"`
}

type SendTemplatedNotificationRequest struct {
	UserID       string                      `json:"user_id" binding:"required"`
	TemplateName string                      `json:"template_name" binding:"required"`
	Channel      models.NotificationChannel  `json:"channel" binding:"required"`
	Data         map[string]interface{}      `json:"data" binding:"required"`
	Priority     models.NotificationPriority `json:"priority,omitempty"`
	SourceType   string                      `json:"source_type,omitempty"`
	SourceID     string                      `json:"source_id,omitempty"`
	SenderID     string                      `json:"sender_id,omitempty"`
	ActionURL    string                      `json:"action_url,omitempty"`
	ImageURL     string                      `json:"image_url,omitempty"`
	ExpiresAt    *time.Time                  `json:"expires_at,omitempty"`
}

type GetNotificationsRequest struct {
	Limit      int  `json:"limit"`
	Offset     int  `json:"offset"`
	UnreadOnly bool `json:"unread_only"`
}

type GetNotificationsResponse struct {
	Notifications []models.Notification `json:"notifications"`
	UnreadCount   int64                 `json:"unread_count"`
	Total         int64                 `json:"total"`
}

type RegisterDeviceRequest struct {
	UserID      string             `json:"user_id" binding:"required"`
	Type        models.DeviceType  `json:"type" binding:"required"`
	Token       string             `json:"token" binding:"required"`
	Platform    string             `json:"platform,omitempty"`
	AppVersion  string             `json:"app_version,omitempty"`
	OSVersion   string             `json:"os_version,omitempty"`
	DeviceName  string             `json:"device_name,omitempty"`
}

type UpdatePreferencesRequest struct {
	Preferences map[models.NotificationType]PreferenceSettings `json:"preferences" binding:"required"`
}

type PreferenceSettings struct {
	Enabled     bool                         `json:"enabled"`
	Channels    []models.NotificationChannel `json:"channels"`
	MinPriority models.NotificationPriority  `json:"min_priority"`
	QuietHours  *models.QuietHours           `json:"quiet_hours,omitempty"`
}

type SearchNotificationsRequest struct {
	Query  string                      `json:"query"`
	Type   models.NotificationType     `json:"type,omitempty"`
	Limit  int                         `json:"limit"`
	Offset int                         `json:"offset"`
}

type GetStatisticsRequest struct {
	StartDate time.Time                   `json:"start_date" binding:"required"`
	EndDate   time.Time                   `json:"end_date" binding:"required"`
	Type      models.NotificationType     `json:"type,omitempty"`
	Channel   models.NotificationChannel  `json:"channel,omitempty"`
}

// External service request types
type EmailRequest struct {
	To      []string `json:"to"`
	Subject string   `json:"subject"`
	Body    string   `json:"body"`
	IsHTML  bool     `json:"is_html"`
}

type PushRequest struct {
	Token    string                 `json:"token"`
	Title    string                 `json:"title"`
	Body     string                 `json:"body"`
	Data     map[string]interface{} `json:"data,omitempty"`
	ImageURL string                 `json:"image_url,omitempty"`
}

type SMSRequest struct {
	To      string `json:"to"`
	Message string `json:"message"`
}

type WebhookRequest struct {
	URL     string      `json:"url"`
	Payload interface{} `json:"payload"`
}

type UserInfo struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

// Helper function to convert []NotificationChannel to NotificationChannels ([]string)
func convertChannelsToStrings(channels []models.NotificationChannel) models.NotificationChannels {
	result := make(models.NotificationChannels, len(channels))
	for i, ch := range channels {
		result[i] = string(ch)
	}
	return result
}