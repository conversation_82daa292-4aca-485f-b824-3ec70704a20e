package services

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strings"

	"github.com/swork-team/platform/services/notification/internal/config"
)

type EmailServiceImpl struct {
	config *config.Config
}

func NewEmailService(config *config.Config) EmailService {
	return &EmailServiceImpl{
		config: config,
	}
}

func (s *EmailServiceImpl) SendEmail(ctx context.Context, req *EmailRequest) (string, error) {
	if !s.config.Email.Enabled {
		return "", fmt.Errorf("email service is disabled")
	}

	switch s.config.Email.Provider {
	case "smtp":
		return s.sendSMTPEmail(req)
	case "sendgrid":
		return s.sendSendGridEmail(req)
	case "ses":
		return s.sendSESEmail(req)
	default:
		return "", fmt.Errorf("unsupported email provider: %s", s.config.Email.Provider)
	}
}

func (s *EmailServiceImpl) sendSMTPEmail(req *EmailRequest) (string, error) {
	// Set up authentication information
	auth := smtp.PlainAuth("", s.config.Email.SMTPUsername, s.config.Email.SMTPPassword, s.config.Email.SMTPHost)

	// Build message
	msg := s.buildEmailMessage(req.Subject, req.Body, req.To, req.IsHTML)

	// Connect to server and send
	addr := fmt.Sprintf("%s:%d", s.config.Email.SMTPHost, s.config.Email.SMTPPort)

	var err error
	if s.config.Email.SMTPUseTLS {
		err = s.sendWithTLS(addr, auth, s.config.Email.FromEmail, req.To, []byte(msg))
	} else {
		err = smtp.SendMail(addr, auth, s.config.Email.FromEmail, req.To, []byte(msg))
	}

	if err != nil {
		return "", fmt.Errorf("failed to send email: %w", err)
	}

	// Generate a simple message ID for tracking
	messageID := fmt.Sprintf("smtp-%d", len(msg))
	return messageID, nil
}

func (s *EmailServiceImpl) sendSendGridEmail(req *EmailRequest) (string, error) {
	// TODO: Implement SendGrid integration
	// This would use the SendGrid Go SDK
	return "", fmt.Errorf("SendGrid integration not implemented")
}

func (s *EmailServiceImpl) sendSESEmail(req *EmailRequest) (string, error) {
	// TODO: Implement AWS SES integration
	// This would use the AWS SDK for Go
	return "", fmt.Errorf("AWS SES integration not implemented")
}

func (s *EmailServiceImpl) sendWithTLS(addr string, auth smtp.Auth, from string, to []string, msg []byte) error {
	// Create TLS connection
	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
		ServerName:         s.config.Email.SMTPHost,
	}

	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		return fmt.Errorf("failed to connect with TLS: %w", err)
	}
	defer conn.Close()

	// Create SMTP client
	client, err := smtp.NewClient(conn, s.config.Email.SMTPHost)
	if err != nil {
		return fmt.Errorf("failed to create SMTP client: %w", err)
	}
	defer client.Quit()

	// Authenticate
	if auth != nil {
		if err = client.Auth(auth); err != nil {
			return fmt.Errorf("authentication failed: %w", err)
		}
	}

	// Set sender
	if err = client.Mail(from); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}

	// Set recipients
	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			return fmt.Errorf("failed to set recipient %s: %w", addr, err)
		}
	}

	// Send message
	w, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to get data writer: %w", err)
	}

	_, err = w.Write(msg)
	if err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	err = w.Close()
	if err != nil {
		return fmt.Errorf("failed to close message: %w", err)
	}

	return nil
}

func (s *EmailServiceImpl) buildEmailMessage(subject, body string, to []string, isHTML bool) string {
	var msg strings.Builder

	// Headers
	msg.WriteString(fmt.Sprintf("From: %s <%s>\r\n", s.config.Email.FromName, s.config.Email.FromEmail))
	msg.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(to, ",")))
	msg.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))

	if s.config.Email.ReplyTo != "" {
		msg.WriteString(fmt.Sprintf("Reply-To: %s\r\n", s.config.Email.ReplyTo))
	}

	msg.WriteString("MIME-Version: 1.0\r\n")

	if isHTML {
		msg.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
	} else {
		msg.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
	}

	msg.WriteString("\r\n")

	// Body
	msg.WriteString(body)

	return msg.String()
}