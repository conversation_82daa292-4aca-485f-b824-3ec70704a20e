package services

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/swork-team/platform/services/notification/internal/config"
)

type WebhookServiceImpl struct {
	config     *config.Config
	httpClient *http.Client
}

func NewWebhookService(config *config.Config) WebhookService {
	// Create HTTP client with timeout and SSL configuration
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: !config.Webhook.VerifySSL,
		},
	}

	client := &http.Client{
		Transport: tr,
		Timeout:   config.Webhook.Timeout,
	}

	return &WebhookServiceImpl{
		config:     config,
		httpClient: client,
	}
}

func (s *WebhookServiceImpl) SendWebhook(ctx context.Context, req *WebhookRequest) (string, error) {
	if !s.config.Webhook.Enabled {
		return "", fmt.Errorf("webhook service is disabled")
	}

	// Marshal payload to JSON
	payloadBytes, err := json.Marshal(req.Payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal webhook payload: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", req.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return "", fmt.Errorf("failed to create webhook request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("User-Agent", "Swork-Notification-Service/1.0")
	
	// Add timestamp for security
	httpReq.Header.Set("X-Swork-Timestamp", fmt.Sprintf("%d", time.Now().Unix()))
	
	// TODO: Add webhook signature for security
	// signature := s.generateSignature(payloadBytes, webhookSecret)
	// httpReq.Header.Set("X-Swork-Signature", signature)

	// Send request with retries
	var lastErr error
	for attempt := 0; attempt <= s.config.Webhook.MaxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry
			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(s.config.Webhook.RetryInterval):
			}
		}

		resp, err := s.httpClient.Do(httpReq)
		if err != nil {
			lastErr = fmt.Errorf("webhook request failed (attempt %d): %w", attempt+1, err)
			continue
		}

		// Read response body
		bodyBytes, _ := io.ReadAll(resp.Body)
		resp.Body.Close()

		// Check status code
		if resp.StatusCode >= 200 && resp.StatusCode < 300 {
			// Success
			messageID := fmt.Sprintf("webhook-%d-%s", resp.StatusCode, req.URL[len(req.URL)-8:])
			return messageID, nil
		}

		lastErr = fmt.Errorf("webhook failed with status %d (attempt %d): %s", resp.StatusCode, attempt+1, string(bodyBytes))

		// Don't retry for certain status codes
		if resp.StatusCode == 400 || resp.StatusCode == 401 || resp.StatusCode == 403 || resp.StatusCode == 404 {
			break
		}
	}

	return "", lastErr
}

// generateSignature generates HMAC signature for webhook security
// func (s *WebhookServiceImpl) generateSignature(payload []byte, secret string) string {
// 	mac := hmac.New(sha256.New, []byte(secret))
// 	mac.Write(payload)
// 	return fmt.Sprintf("sha256=%x", mac.Sum(nil))
// }