package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/utils"
	"github.com/swork-team/platform/services/notification/internal/services"
)

type NotificationHandler struct {
	notificationService *services.NotificationService
}

func NewNotificationHandler(notificationService *services.NotificationService) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
	}
}

// Notification management handlers

// @Summary Send a notification
// @Description Send a notification to a user
// @Tags notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.SendNotificationRequest true "Notification data"
// @Success 201 {object} utils.APIResponse{data=models.Notification}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /notifications [post]
func (h *NotificationHandler) SendNotification(c *gin.Context) {
	var req services.SendNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	notification, err := h.notificationService.SendNotification(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Notification sent successfully", notification)
}

// @Summary Send bulk notifications
// @Description Send notifications to multiple users
// @Tags notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.SendBulkNotificationRequest true "Bulk notification data"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /notifications/bulk [post]
func (h *NotificationHandler) SendBulkNotification(c *gin.Context) {
	var req services.SendBulkNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	err := h.notificationService.SendBulkNotification(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Bulk notifications sent successfully", nil)
}

// @Summary Send templated notification
// @Description Send a notification using a predefined template
// @Tags notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.SendTemplatedNotificationRequest true "Templated notification data"
// @Success 201 {object} utils.APIResponse{data=models.Notification}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /notifications/templated [post]
func (h *NotificationHandler) SendTemplatedNotification(c *gin.Context) {
	var req services.SendTemplatedNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	notification, err := h.notificationService.SendTemplatedNotification(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Templated notification sent successfully", notification)
}

// @Summary Get user notifications
// @Description Get notifications for the authenticated user
// @Tags notifications
// @Security BearerAuth
// @Param limit query int false "Number of notifications per page" default(20)
// @Param offset query int false "Number of notifications to skip" default(0)
// @Param unread_only query bool false "Only return unread notifications" default(false)
// @Success 200 {object} utils.APIResponse{data=services.GetNotificationsResponse}
// @Failure 401 {object} utils.APIResponse
// @Router /notifications [get]
func (h *NotificationHandler) GetNotifications(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	limit, offset := middleware.ValidatePagination(c)
	unreadOnly, _ := strconv.ParseBool(c.DefaultQuery("unread_only", "false"))

	req := &services.GetNotificationsRequest{
		Limit:      limit,
		Offset:     offset,
		UnreadOnly: unreadOnly,
	}

	response, err := h.notificationService.GetUserNotifications(c.Request.Context(), userIDStr, req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get notifications", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Notifications retrieved successfully", response)
}

// @Summary Search notifications
// @Description Search user notifications
// @Tags notifications
// @Security BearerAuth
// @Param q query string false "Search query"
// @Param type query string false "Notification type filter"
// @Param limit query int false "Number of notifications per page" default(20)
// @Param offset query int false "Number of notifications to skip" default(0)
// @Success 200 {object} utils.APIResponse{data=[]models.Notification}
// @Failure 401 {object} utils.APIResponse
// @Router /notifications/search [get]
func (h *NotificationHandler) SearchNotifications(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	query := c.Query("q")
	notificationType := c.Query("type")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	// Validate limits
	if limit > 100 {
		limit = 100
	}
	if limit < 1 {
		limit = 20
	}

	req := &services.SearchNotificationsRequest{
		Query:  query,
		Limit:  limit,
		Offset: offset,
	}

	// Convert string to NotificationType if provided
	if notificationType != "" {
		// TODO: Add proper type conversion and validation
	}

	notifications, err := h.notificationService.SearchNotifications(c.Request.Context(), userIDStr, req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to search notifications", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Search completed successfully", notifications)
}

// @Summary Mark notification as read
// @Description Mark a specific notification as read
// @Tags notifications
// @Security BearerAuth
// @Param id path string true "Notification ID"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /notifications/{id}/read [put]
func (h *NotificationHandler) MarkAsRead(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	notificationIDStr := c.Param("id")
	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid notification ID", err)
		return
	}

	err = h.notificationService.MarkAsRead(c.Request.Context(), userIDStr, notificationID)
	if err != nil {
		if err.Error() == "notification not found or not accessible" {
			utils.ErrorResponse(c, http.StatusNotFound, "Notification not found", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to mark notification as read", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Notification marked as read", nil)
}

// @Summary Mark all notifications as read
// @Description Mark all user notifications as read
// @Tags notifications
// @Security BearerAuth
// @Success 200 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /notifications/read-all [put]
func (h *NotificationHandler) MarkAllAsRead(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	err := h.notificationService.MarkAllAsRead(c.Request.Context(), userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to mark all notifications as read", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "All notifications marked as read", nil)
}

// @Summary Delete notification
// @Description Delete a specific notification
// @Tags notifications
// @Security BearerAuth
// @Param id path string true "Notification ID"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Failure 404 {object} utils.APIResponse
// @Router /notifications/{id} [delete]
func (h *NotificationHandler) DeleteNotification(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	notificationIDStr := c.Param("id")
	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid notification ID", err)
		return
	}

	err = h.notificationService.DeleteNotification(c.Request.Context(), userIDStr, notificationID)
	if err != nil {
		if err.Error() == "notification not found or not accessible" {
			utils.ErrorResponse(c, http.StatusNotFound, "Notification not found", nil)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete notification", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Notification deleted successfully", nil)
}

// Device management handlers

// @Summary Register device
// @Description Register a device for push notifications
// @Tags devices
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.RegisterDeviceRequest true "Device registration data"
// @Success 201 {object} utils.APIResponse{data=models.Device}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /devices [post]
func (h *NotificationHandler) RegisterDevice(c *gin.Context) {
	var req services.RegisterDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	device, err := h.notificationService.RegisterDevice(c.Request.Context(), &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Device registered successfully", device)
}

// @Summary Unregister device
// @Description Unregister a device from push notifications
// @Tags devices
// @Security BearerAuth
// @Param token path string true "Device token"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /devices/{token} [delete]
func (h *NotificationHandler) UnregisterDevice(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Device token is required", nil)
		return
	}

	err := h.notificationService.UnregisterDevice(c.Request.Context(), token)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Device unregistered successfully", nil)
}

// @Summary Get user devices
// @Description Get all registered devices for the user
// @Tags devices
// @Security BearerAuth
// @Success 200 {object} utils.APIResponse{data=[]models.Device}
// @Failure 401 {object} utils.APIResponse
// @Router /devices [get]
func (h *NotificationHandler) GetDevices(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	devices, err := h.notificationService.GetUserDevices(c.Request.Context(), userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get devices", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Devices retrieved successfully", devices)
}

// Preferences management handlers

// @Summary Update notification preferences
// @Description Update user notification preferences
// @Tags preferences
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body services.UpdatePreferencesRequest true "Preference data"
// @Success 200 {object} utils.APIResponse
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /preferences [put]
func (h *NotificationHandler) UpdatePreferences(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	var req services.UpdatePreferencesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	err := h.notificationService.UpdatePreferences(c.Request.Context(), userIDStr, &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error(), nil)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Preferences updated successfully", nil)
}

// @Summary Get notification preferences
// @Description Get user notification preferences
// @Tags preferences
// @Security BearerAuth
// @Success 200 {object} utils.APIResponse{data=[]models.UserNotificationPreference}
// @Failure 401 {object} utils.APIResponse
// @Router /preferences [get]
func (h *NotificationHandler) GetPreferences(c *gin.Context) {
	userIDStr := middleware.MustGetAuthenticatedUser(c)
	if userIDStr == "" {
		return // Error already handled by middleware
	}

	preferences, err := h.notificationService.GetUserPreferences(c.Request.Context(), userIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get preferences", err)
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Preferences retrieved successfully", preferences)
}

// Statistics handlers

// @Summary Get notification statistics
// @Description Get notification delivery and read statistics
// @Tags statistics
// @Security BearerAuth
// @Param start_date query string true "Start date (YYYY-MM-DD format)"
// @Param end_date query string true "End date (YYYY-MM-DD format)"
// @Param type query string false "Notification type filter"
// @Param channel query string false "Channel filter"
// @Success 200 {object} utils.APIResponse{data=[]models.NotificationStats}
// @Failure 400 {object} utils.APIResponse
// @Failure 401 {object} utils.APIResponse
// @Router /statistics [get]
func (h *NotificationHandler) GetStatistics(c *gin.Context) {
	// This endpoint might be restricted to admin users
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Start date and end date are required", nil)
		return
	}

	// Parse dates (simplified - in production you'd want better date parsing)
	// startDate, err := time.Parse("2006-01-02", startDateStr)
	// endDate, err := time.Parse("2006-01-02", endDateStr)

	// For now, return a placeholder response
	utils.SuccessResponse(c, http.StatusOK, "Statistics endpoint not fully implemented", map[string]string{
		"message": "Statistics retrieval functionality will be implemented with proper date parsing and admin authorization",
	})
}

// Health check endpoint
func (h *NotificationHandler) HealthCheck(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Notification service is healthy", map[string]string{
		"service": "notification-service",
		"status":  "healthy",
	})
}