package main

import (
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/swork-team/platform/pkg/logger"
	"github.com/swork-team/platform/pkg/middleware"
	"github.com/swork-team/platform/pkg/server"
	"github.com/swork-team/platform/services/notification/internal/config"
	"github.com/swork-team/platform/services/notification/internal/models"
	"github.com/swork-team/platform/services/notification/internal/repositories"
)

func main() {
	cfg := config.LoadConfig()

	// Initialize standardized logger
	loggerManager := logger.NewServiceLoggerManager("notification")
	serviceLogger := loggerManager.GetLogger()

	// Log service startup
	serviceLogger.Info("Notification service starting up",
		logger.F("environment", os.Getenv("APP_ENV")),
		logger.F("port", cfg.Server.Port),
	)

	// Create config adapter for bootstrap
	configAdapter := server.NewConfigAdapter(cfg.Server, cfg.Database, cfg.Redis)

	// Define models to migrate
	models := []interface{}{
		&models.Notification{},
		&models.NotificationDelivery{},
		&models.UserNotificationPreference{},
		&models.Device{},
		&models.NotificationTemplate{},
		&models.NotificationStats{},
	}

	// Setup router function
	routerSetup := func(components *server.ServiceComponents) *gin.Engine {
		return setupRouter(components, cfg, serviceLogger)
	}

	// Cleanup function
	cleanupFunc := func() error {
		serviceLogger.Info("Notification service shutting down")
		return nil
	}

	// Bootstrap the service
	server.BootstrapService(server.ServiceOptions{
		ServiceName:        "notification",
		Config:             configAdapter,
		Models:             models,
		RouterSetup:        routerSetup,
		EnableExtensions:   false,
		ServiceCleanupFunc: cleanupFunc,
		DatabaseType:       server.PostgreSQL,
	})
}

func setupRouter(components *server.ServiceComponents, cfg *config.Config, serviceLogger logger.ServiceLogger) *gin.Engine {
	if os.Getenv("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// Use standardized logging middleware
	router.Use(logger.RequestLoggingMiddleware(serviceLogger))
	router.Use(logger.UserContextMiddleware())
	router.Use(logger.ErrorLoggingMiddleware())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// Health check
	server.AddStandardHealthCheck(router, "notification-service")

	// Initialize repository for basic operations
	notificationRepo := repositories.NewNotificationRepository(components.Database)

	// Basic notification routes
	notifications := router.Group("/notifications")
	notifications.Use(middleware.AuthMiddleware())
	{
		// Get user notifications
		notifications.GET("/", func(c *gin.Context) {
			userIDStr := middleware.MustGetAuthenticatedUser(c)
			if userIDStr == "" {
				c.JSON(http.StatusUnauthorized, gin.H{"success": false, "message": "User not authenticated"})
				return
			}

			limit := 20
			offset := 0
			unreadOnly := false

			notifications, err := notificationRepo.GetUserNotifications(c.Request.Context(), userIDStr, limit, offset, unreadOnly)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Failed to get notifications", "error": err.Error()})
				return
			}

			unreadCount, _ := notificationRepo.GetUnreadCount(c.Request.Context(), userIDStr)

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "Notifications retrieved successfully",
				"data": gin.H{
					"notifications": notifications,
					"unread_count":  unreadCount,
					"total":         len(notifications),
				},
			})
		})

		// Create notification (basic)
		notifications.POST("/", func(c *gin.Context) {
			var req struct {
				UserID   string `json:"user_id" binding:"required"`
				Title    string `json:"title" binding:"required"`
				Message  string `json:"message" binding:"required"`
				Type     string `json:"type"`
				Priority string `json:"priority"`
			}

			if !middleware.BindJSONRequest(c, &req) {
				c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "Invalid request format"})
				return
			}

			notification := &models.Notification{
				UserID:   uuid.MustParse(req.UserID),
				Title:    req.Title,
				Message:  req.Message,
				Type:     models.NotificationType(req.Type),
				Priority: models.NotificationPriority(req.Priority),
				Status:   models.NotificationStatusPending,
			}

			if err := notificationRepo.CreateNotification(c.Request.Context(), notification); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Failed to create notification", "error": err.Error()})
				return
			}

			c.JSON(http.StatusCreated, gin.H{
				"success": true,
				"message": "Notification created successfully",
				"data":    notification,
			})
		})

		// Mark as read
		notifications.PUT("/:id/read", func(c *gin.Context) {
			userIDStr := middleware.MustGetAuthenticatedUser(c)
			if userIDStr == "" {
				c.JSON(http.StatusUnauthorized, gin.H{"success": false, "message": "User not authenticated"})
				return
			}

			notificationIDStr := c.Param("id")
			notificationID, err := uuid.Parse(notificationIDStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "Invalid notification ID"})
				return
			}

			err = notificationRepo.MarkAsRead(c.Request.Context(), notificationID, userIDStr)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Failed to mark as read", "error": err.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "Notification marked as read",
			})
		})
	}

	return router
}
