# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/
build/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# Environment files and secrets
.env
.env.local
.env.development
.env.test
.env.production
.env.bak

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Docker
.dockerignore

# Database
*.sqlite
*.db

# Temporary files
tmp/
temp/

# Node modules (for any frontend)
node_modules/

# Build outputs
dist/
public/build/

# Test files
test-results/

# Swagger docs (auto-generated)
docs/swagger.json
docs/swagger.yaml

# Private keys and certificates
*.pem
*.key
*.crt
*.p12

# Backup files
*.backup
*.bak
open-im-server/.gitattributes
open-im-server/.gitignore
open-im-server/.golangci.yml
open-im-server/bootstrap.bat
open-im-server/bootstrap.sh
open-im-server/CHANGELOG.md
open-im-server/CODE_OF_CONDUCT.md
open-im-server/CONTRIBUTING-zh_CN.md
open-im-server/CONTRIBUTING.md
open-im-server/docker-compose.yml
open-im-server/Dockerfile
open-im-server/go.mod
open-im-server/go.sum
open-im-server/install.sh
open-im-server/LICENSE
open-im-server/magefile_unix.go
open-im-server/magefile_windows.go
open-im-server/magefile.go
open-im-server/README_zh_CN.md
open-im-server/README.md
open-im-server/start-config.yml
open-im-server/.github/.codecov.yml
open-im-server/.github/sync-release.yml
open-im-server/.github/ISSUE_TEMPLATE/bug-report.yml
open-im-server/.github/ISSUE_TEMPLATE/config.yml
open-im-server/.github/ISSUE_TEMPLATE/deployment.yml
open-im-server/.github/ISSUE_TEMPLATE/documentation.md
open-im-server/.github/ISSUE_TEMPLATE/feature-request.yml
open-im-server/.github/ISSUE_TEMPLATE/other.yml
open-im-server/.github/ISSUE_TEMPLATE/rfc.md
open-im-server/.github/workflows/auto-assign-issue.yml
open-im-server/.github/workflows/auto-invite-comment.yml
open-im-server/.github/workflows/changelog.yml
open-im-server/.github/workflows/cla-assistant.yml
open-im-server/.github/workflows/cleanup-after-milestone-prs-merged.yml
open-im-server/.github/workflows/codeql-analysis.yml
open-im-server/.github/workflows/comment-check.yml
open-im-server/.github/workflows/docker-build-and-release-services-images.yml
open-im-server/.github/workflows/go-build-test.yml
open-im-server/.github/workflows/help-comment-issue.yml
open-im-server/.github/workflows/issue-translator.yml
open-im-server/.github/workflows/merge-from-milestone.yml
open-im-server/.github/workflows/publish-docker-image.yml
open-im-server/.github/workflows/remove-unused-labels.yml
open-im-server/.github/workflows/reopen-issue.yml
open-im-server/.github/workflows/update-version-file-on-release.yml
open-im-server/.github/workflows/user-first-interaction.yml
open-im-server/assets/colors.md
open-im-server/assets/intive-slack.png
open-im-server/assets/openim-logo-gradient.pdf
open-im-server/assets/openim-logo-gradient.svg
open-im-server/assets/openim-logo-green.pdf
open-im-server/assets/openim-logo-green.svg
open-im-server/assets/README.md
open-im-server/assets/demo/efficient-meetings.png
open-im-server/assets/demo/group-chat.png
open-im-server/assets/demo/hello-openim.png
open-im-server/assets/demo/multi-terminal-synchronization.png
open-im-server/assets/demo/multiple-message.png
open-im-server/assets/demo/README.md
open-im-server/assets/demo/special-function.png
open-im-server/assets/logo/LICENSE
open-im-server/assets/logo/openim-logo-blue.png
open-im-server/assets/logo/openim-logo-cyan.png
open-im-server/assets/logo/openim-logo-gradient.png
open-im-server/assets/logo/openim-logo-green.png
open-im-server/assets/logo/openim-logo-orange.png
open-im-server/assets/logo/openim-logo-purple.png
open-im-server/assets/logo/openim-logo-red.png
open-im-server/assets/logo/openim-logo-yellow.png
open-im-server/assets/logo/openim-logo.png
open-im-server/assets/logo-gif/LICENSE
open-im-server/assets/logo-gif/openim-logo.gif
open-im-server/cmd/main.go
open-im-server/cmd/openim-api/main.go
open-im-server/cmd/openim-cmdutils/main.go
open-im-server/cmd/openim-crontask/main.go
open-im-server/cmd/openim-msggateway/main.go
open-im-server/cmd/openim-msgtransfer/main.go
open-im-server/cmd/openim-push/main.go
open-im-server/cmd/openim-rpc/openim-rpc-auth/main.go
open-im-server/cmd/openim-rpc/openim-rpc-conversation/main.go
open-im-server/cmd/openim-rpc/openim-rpc-friend/main.go
open-im-server/cmd/openim-rpc/openim-rpc-group/main.go
open-im-server/cmd/openim-rpc/openim-rpc-msg/main.go
open-im-server/cmd/openim-rpc/openim-rpc-third/main.go
open-im-server/cmd/openim-rpc/openim-rpc-user/main.go
open-im-server/config/alertmanager.yml
open-im-server/config/discovery.yml
open-im-server/config/email.tmpl
open-im-server/config/instance-down-rules.yml
open-im-server/config/kafka.yml
open-im-server/config/local-cache.yml
open-im-server/config/log.yml
open-im-server/config/minio.yml
open-im-server/config/mongodb.yml
open-im-server/config/notification.yml
open-im-server/config/openim-api.yml
open-im-server/config/openim-crontask.yml
open-im-server/config/openim-msggateway.yml
open-im-server/config/openim-msgtransfer.yml
open-im-server/config/openim-push.yml
open-im-server/config/openim-rpc-auth.yml
open-im-server/config/openim-rpc-conversation.yml
open-im-server/config/openim-rpc-friend.yml
open-im-server/config/openim-rpc-group.yml
open-im-server/config/openim-rpc-msg.yml
open-im-server/config/openim-rpc-third.yml
open-im-server/config/openim-rpc-user.yml
open-im-server/config/prometheus.yml
open-im-server/config/README_zh_CN.md
open-im-server/config/README.md
open-im-server/config/redis.yml
open-im-server/config/share.yml
open-im-server/config/webhooks.yml
open-im-server/config/grafana-template/Demo.json
open-im-server/deployments/Readme.md
open-im-server/deployments/deploy/clusterRole.yml
open-im-server/deployments/deploy/ingress.yml
open-im-server/deployments/deploy/kafka-secret.yml
open-im-server/deployments/deploy/kafka-service.yml
open-im-server/deployments/deploy/kafka-statefulset.yml
open-im-server/deployments/deploy/minio-secret.yml
open-im-server/deployments/deploy/minio-service.yml
open-im-server/deployments/deploy/minio-statefulset.yml
open-im-server/deployments/deploy/mongo-secret.yml
open-im-server/deployments/deploy/mongo-service.yml
open-im-server/deployments/deploy/mongo-statefulset.yml
open-im-server/deployments/deploy/openim-api-deployment.yml
open-im-server/deployments/deploy/openim-api-service.yml
open-im-server/deployments/deploy/openim-config.yml
open-im-server/deployments/deploy/openim-crontask-deployment.yml
open-im-server/deployments/deploy/openim-msggateway-deployment.yml
open-im-server/deployments/deploy/openim-msggateway-service.yml
open-im-server/deployments/deploy/openim-msgtransfer-deployment.yml
open-im-server/deployments/deploy/openim-msgtransfer-service.yml
open-im-server/deployments/deploy/openim-push-deployment.yml
open-im-server/deployments/deploy/openim-push-service.yml
open-im-server/deployments/deploy/openim-rpc-auth-deployment.yml
open-im-server/deployments/deploy/openim-rpc-auth-service.yml
open-im-server/deployments/deploy/openim-rpc-conversation-deployment.yml
open-im-server/deployments/deploy/openim-rpc-conversation-service.yml
open-im-server/deployments/deploy/openim-rpc-friend-deployment.yml
open-im-server/deployments/deploy/openim-rpc-friend-service.yml
open-im-server/deployments/deploy/openim-rpc-group-deployment.yml
open-im-server/deployments/deploy/openim-rpc-group-service.yml
open-im-server/deployments/deploy/openim-rpc-msg-deployment.yml
open-im-server/deployments/deploy/openim-rpc-msg-service.yml
open-im-server/deployments/deploy/openim-rpc-third-deployment.yml
open-im-server/deployments/deploy/openim-rpc-third-service.yml
open-im-server/deployments/deploy/openim-rpc-user-deployment.yml
open-im-server/deployments/deploy/openim-rpc-user-service.yml
open-im-server/deployments/deploy/redis-secret.yml
open-im-server/deployments/deploy/redis-service.yml
open-im-server/deployments/deploy/redis-statefulset.yml
open-im-server/docs/.generated_docs
open-im-server/docs/CODEOWNERS
open-im-server/docs/README.md
open-im-server/docs/contrib/api.md
open-im-server/docs/contrib/bash-log.md
open-im-server/docs/contrib/cicd-actions.md
open-im-server/docs/contrib/code-conventions.md
open-im-server/docs/contrib/commit.md
open-im-server/docs/contrib/development.md
open-im-server/docs/contrib/directory.md
open-im-server/docs/contrib/environment.md
open-im-server/docs/contrib/error-code.md
open-im-server/docs/contrib/git-workflow.md
open-im-server/docs/contrib/gitcherry-pick.md
open-im-server/docs/contrib/github-workflow.md
open-im-server/docs/contrib/go-code.md
open-im-server/docs/contrib/go-code1.md
open-im-server/docs/contrib/go-doc.md
open-im-server/docs/contrib/images.md
open-im-server/docs/contrib/init-config.md
open-im-server/docs/contrib/install-docker.md
open-im-server/docs/contrib/install-openim-linux-system.md
open-im-server/docs/contrib/kafka.md
open-im-server/docs/contrib/linux-development.md
open-im-server/docs/contrib/local-actions.md
open-im-server/docs/contrib/logging.md
open-im-server/docs/contrib/mac-developer-deployment-guide.md
open-im-server/docs/contrib/offline-deployment.md
open-im-server/docs/contrib/prometheus-grafana.md
open-im-server/docs/contrib/protoc-tools.md
open-im-server/docs/contrib/README.md
open-im-server/docs/contrib/release.md
open-im-server/docs/contrib/test.md
open-im-server/docs/contrib/util-go.md
open-im-server/docs/contrib/util-makefile.md
open-im-server/docs/contrib/util-scripts.md
open-im-server/docs/contrib/version.md
open-im-server/docs/contributing/CONTRIBUTING-JP.md
open-im-server/docs/contributing/CONTRIBUTING-PL.md
open-im-server/docs/images/architecture-layers.png
open-im-server/docs/images/architecture.jpg
open-im-server/docs/images/oepnim-design.png
open-im-server/docs/images/open-im-logo.png
open-im-server/docs/images/open-im-server.png
open-im-server/docs/images/wechat.jpg
open-im-server/docs/readme/README_cs.md
open-im-server/docs/readme/README_da.md
open-im-server/docs/readme/README_el.md
open-im-server/docs/readme/README_es.md
open-im-server/docs/readme/README_fa.md
open-im-server/docs/readme/README_fr.md
open-im-server/docs/readme/README_hu.md
open-im-server/docs/readme/README_ja.md
open-im-server/docs/readme/README_ko.md
open-im-server/docs/readme/README_tr.md
open-im-server/docs/readme/README_uk.md
open-im-server/docs/readme/README_vi.md
open-im-server/internal/api/auth.go
open-im-server/internal/api/config_manager.go
open-im-server/internal/api/conversation.go
open-im-server/internal/api/custom_validator.go
open-im-server/internal/api/friend.go
open-im-server/internal/api/group.go
open-im-server/internal/api/init.go
open-im-server/internal/api/msg.go
open-im-server/internal/api/prometheus_discovery.go
open-im-server/internal/api/router.go
open-im-server/internal/api/third.go
open-im-server/internal/api/user.go
open-im-server/internal/api/jssdk/jssdk.go
open-im-server/internal/api/jssdk/sort.go
open-im-server/internal/api/jssdk/tools.go
open-im-server/internal/msggateway/callback.go
open-im-server/internal/msggateway/client.go
open-im-server/internal/msggateway/compressor_test.go
open-im-server/internal/msggateway/compressor.go
open-im-server/internal/msggateway/constant.go
open-im-server/internal/msggateway/context.go
open-im-server/internal/msggateway/encoder.go
open-im-server/internal/msggateway/http_error.go
open-im-server/internal/msggateway/hub_server.go
open-im-server/internal/msggateway/init.go
open-im-server/internal/msggateway/long_conn.go
open-im-server/internal/msggateway/message_handler.go
open-im-server/internal/msggateway/online.go
open-im-server/internal/msggateway/options.go
open-im-server/internal/msggateway/subscription.go
open-im-server/internal/msggateway/user_map.go
open-im-server/internal/msggateway/ws_server.go
open-im-server/internal/msgtransfer/callback.go
open-im-server/internal/msgtransfer/init.go
open-im-server/internal/msgtransfer/online_history_msg_handler.go
open-im-server/internal/msgtransfer/online_msg_to_mongo_handler.go
open-im-server/internal/push/callback.go
open-im-server/internal/push/offlinepush_handler.go
open-im-server/internal/push/onlinepusher.go
open-im-server/internal/push/push_handler.go
open-im-server/internal/push/push.go
open-im-server/internal/push/offlinepush/offlinepusher.go
open-im-server/internal/push/offlinepush/dummy/push.go
open-im-server/internal/push/offlinepush/fcm/push.go
open-im-server/internal/push/offlinepush/getui/body.go
open-im-server/internal/push/offlinepush/getui/push.go
open-im-server/internal/push/offlinepush/jpush/push.go
open-im-server/internal/push/offlinepush/jpush/body/audience.go
open-im-server/internal/push/offlinepush/jpush/body/message.go
open-im-server/internal/push/offlinepush/jpush/body/notification.go
open-im-server/internal/push/offlinepush/jpush/body/options.go
open-im-server/internal/push/offlinepush/jpush/body/platform.go
open-im-server/internal/push/offlinepush/jpush/body/pushobj.go
open-im-server/internal/push/offlinepush/options/options.go
open-im-server/internal/rpc/auth/auth.go
open-im-server/internal/rpc/conversation/callback.go
open-im-server/internal/rpc/conversation/conversation.go
open-im-server/internal/rpc/conversation/db_map.go
open-im-server/internal/rpc/conversation/notification.go
open-im-server/internal/rpc/conversation/sync.go
open-im-server/internal/rpc/group/cache.go
open-im-server/internal/rpc/group/callback.go
open-im-server/internal/rpc/group/convert.go
open-im-server/internal/rpc/group/db_map.go
open-im-server/internal/rpc/group/fill.go
open-im-server/internal/rpc/group/group.go
open-im-server/internal/rpc/group/notification.go
open-im-server/internal/rpc/group/statistics.go
open-im-server/internal/rpc/group/sync.go
open-im-server/internal/rpc/incrversion/batch_option.go
open-im-server/internal/rpc/incrversion/option.go
open-im-server/internal/rpc/msg/as_read.go
open-im-server/internal/rpc/msg/callback.go
open-im-server/internal/rpc/msg/clear.go
open-im-server/internal/rpc/msg/delete.go
open-im-server/internal/rpc/msg/filter.go
open-im-server/internal/rpc/msg/msg_status.go
open-im-server/internal/rpc/msg/notification.go
open-im-server/internal/rpc/msg/revoke.go
open-im-server/internal/rpc/msg/send.go
open-im-server/internal/rpc/msg/seq.go
open-im-server/internal/rpc/msg/server.go
open-im-server/internal/rpc/msg/statistics.go
open-im-server/internal/rpc/msg/sync_msg.go
open-im-server/internal/rpc/msg/utils.go
open-im-server/internal/rpc/msg/verify.go
open-im-server/internal/rpc/relation/black.go
open-im-server/internal/rpc/relation/callback.go
open-im-server/internal/rpc/relation/friend.go
open-im-server/internal/rpc/relation/notification.go
open-im-server/internal/rpc/relation/sync.go
open-im-server/internal/rpc/third/log.go
open-im-server/internal/rpc/third/s3.go
open-im-server/internal/rpc/third/third.go
open-im-server/internal/rpc/third/tool.go
open-im-server/internal/rpc/user/callback.go
open-im-server/internal/rpc/user/config.go
open-im-server/internal/rpc/user/notification.go
open-im-server/internal/rpc/user/online.go
open-im-server/internal/rpc/user/statistics.go
open-im-server/internal/rpc/user/user.go
open-im-server/internal/tools/cron/cron_task.go
open-im-server/internal/tools/cron/cron_test.go
open-im-server/internal/tools/cron/dist_look.go
open-im-server/internal/tools/cron/msg.go
open-im-server/internal/tools/cron/s3.go
open-im-server/internal/tools/cron/user_msg.go
open-im-server/pkg/apistruct/config_manager.go
open-im-server/pkg/apistruct/doc.go
open-im-server/pkg/apistruct/manage.go
open-im-server/pkg/apistruct/msg.go
open-im-server/pkg/apistruct/public.go
open-im-server/pkg/authverify/doc.go
open-im-server/pkg/authverify/token.go
open-im-server/pkg/callbackstruct/common.go
open-im-server/pkg/callbackstruct/constant.go
open-im-server/pkg/callbackstruct/conversation.go
open-im-server/pkg/callbackstruct/doc.go
open-im-server/pkg/callbackstruct/friend.go
open-im-server/pkg/callbackstruct/group.go
open-im-server/pkg/callbackstruct/message.go
open-im-server/pkg/callbackstruct/msg_gateway.go
open-im-server/pkg/callbackstruct/push.go
open-im-server/pkg/callbackstruct/revoke.go
open-im-server/pkg/callbackstruct/user.go
open-im-server/pkg/common/cmd/api.go
open-im-server/pkg/common/cmd/auth.go
open-im-server/pkg/common/cmd/conversation.go
open-im-server/pkg/common/cmd/cron_task.go
open-im-server/pkg/common/cmd/doc.go
open-im-server/pkg/common/cmd/friend.go
open-im-server/pkg/common/cmd/group.go
open-im-server/pkg/common/cmd/msg_gateway_test.go
open-im-server/pkg/common/cmd/msg_gateway.go
open-im-server/pkg/common/cmd/msg_transfer.go
open-im-server/pkg/common/cmd/msg_utils.go
open-im-server/pkg/common/cmd/msg.go
open-im-server/pkg/common/cmd/push.go
open-im-server/pkg/common/cmd/root.go
open-im-server/pkg/common/cmd/third.go
open-im-server/pkg/common/cmd/user.go
open-im-server/pkg/common/config/config.go
open-im-server/pkg/common/config/constant.go
open-im-server/pkg/common/config/doc.go
open-im-server/pkg/common/config/env.go
open-im-server/pkg/common/config/global.go
open-im-server/pkg/common/config/load_config_test.go
open-im-server/pkg/common/config/load_config.go
open-im-server/pkg/common/config/parse.go
open-im-server/pkg/common/convert/black.go
open-im-server/pkg/common/convert/conversation.go
open-im-server/pkg/common/convert/doc.go
open-im-server/pkg/common/convert/friend.go
open-im-server/pkg/common/convert/group.go
open-im-server/pkg/common/convert/msg.go
open-im-server/pkg/common/convert/user_test.go
open-im-server/pkg/common/convert/user.go
open-im-server/pkg/common/discovery/discoveryregister_test.go
open-im-server/pkg/common/discovery/discoveryregister.go
open-im-server/pkg/common/discovery/doc.go
open-im-server/pkg/common/discovery/direct/direct_resolver.go
open-im-server/pkg/common/discovery/direct/directconn.go
open-im-server/pkg/common/discovery/direct/doc.go
open-im-server/pkg/common/discovery/etcd/config_manager.go
open-im-server/pkg/common/discovery/etcd/const.go
open-im-server/pkg/common/discovery/kubernetes/doc.go
open-im-server/pkg/common/discovery/kubernetes/kubernetes.go
open-im-server/pkg/common/ginprometheus/doc.go
open-im-server/pkg/common/ginprometheus/ginprometheus.go
open-im-server/pkg/common/prommetrics/api.go
open-im-server/pkg/common/prommetrics/grpc_auth.go
open-im-server/pkg/common/prommetrics/grpc_msg.go
open-im-server/pkg/common/prommetrics/grpc_msggateway.go
open-im-server/pkg/common/prommetrics/grpc_push.go
open-im-server/pkg/common/prommetrics/grpc_user.go
open-im-server/pkg/common/prommetrics/prommetrics_test.go
open-im-server/pkg/common/prommetrics/prommetrics.go
open-im-server/pkg/common/prommetrics/rpc.go
open-im-server/pkg/common/prommetrics/transfer.go
open-im-server/pkg/common/servererrs/code.go
open-im-server/pkg/common/servererrs/doc.go
open-im-server/pkg/common/servererrs/predefine.go
open-im-server/pkg/common/servererrs/relation.go
open-im-server/pkg/common/startrpc/mw.go
open-im-server/pkg/common/startrpc/start.go
open-im-server/pkg/common/startrpc/tools.go
open-im-server/pkg/common/storage/cache/batch_handler.go
open-im-server/pkg/common/storage/cache/black.go
open-im-server/pkg/common/storage/cache/client_config.go
open-im-server/pkg/common/storage/cache/conversation.go
open-im-server/pkg/common/storage/cache/doc.go
open-im-server/pkg/common/storage/cache/friend.go
open-im-server/pkg/common/storage/cache/group.go
open-im-server/pkg/common/storage/cache/msg.go
open-im-server/pkg/common/storage/cache/online.go
open-im-server/pkg/common/storage/cache/s3.go
open-im-server/pkg/common/storage/cache/seq_conversation.go
open-im-server/pkg/common/storage/cache/seq_user.go
open-im-server/pkg/common/storage/cache/third.go
open-im-server/pkg/common/storage/cache/token.go
open-im-server/pkg/common/storage/cache/user.go
open-im-server/pkg/common/storage/cache/cachekey/black.go
open-im-server/pkg/common/storage/cache/cachekey/client_config.go
open-im-server/pkg/common/storage/cache/cachekey/conversation.go
open-im-server/pkg/common/storage/cache/cachekey/doc.go
open-im-server/pkg/common/storage/cache/cachekey/friend.go
open-im-server/pkg/common/storage/cache/cachekey/group.go
open-im-server/pkg/common/storage/cache/cachekey/msg.go
open-im-server/pkg/common/storage/cache/cachekey/online.go
open-im-server/pkg/common/storage/cache/cachekey/s3.go
open-im-server/pkg/common/storage/cache/cachekey/seq.go
open-im-server/pkg/common/storage/cache/cachekey/third.go
open-im-server/pkg/common/storage/cache/cachekey/token.go
open-im-server/pkg/common/storage/cache/cachekey/user.go
open-im-server/pkg/common/storage/cache/mcache/minio.go
open-im-server/pkg/common/storage/cache/mcache/msg_cache.go
open-im-server/pkg/common/storage/cache/mcache/online.go
open-im-server/pkg/common/storage/cache/mcache/seq_conversation.go
open-im-server/pkg/common/storage/cache/mcache/third.go
open-im-server/pkg/common/storage/cache/mcache/token.go
open-im-server/pkg/common/storage/cache/mcache/tools.go
open-im-server/pkg/common/storage/cache/redis/batch_handler.go
open-im-server/pkg/common/storage/cache/redis/batch_test.go
open-im-server/pkg/common/storage/cache/redis/batch.go
open-im-server/pkg/common/storage/cache/redis/black.go
open-im-server/pkg/common/storage/cache/redis/client_config.go
open-im-server/pkg/common/storage/cache/redis/conversation.go
open-im-server/pkg/common/storage/cache/redis/friend.go
open-im-server/pkg/common/storage/cache/redis/group.go
open-im-server/pkg/common/storage/cache/redis/lua_script_test.go
open-im-server/pkg/common/storage/cache/redis/lua_script.go
open-im-server/pkg/common/storage/cache/redis/minio.go
open-im-server/pkg/common/storage/cache/redis/msg.go
open-im-server/pkg/common/storage/cache/redis/online_test.go
open-im-server/pkg/common/storage/cache/redis/online.go
open-im-server/pkg/common/storage/cache/redis/redis_shard_manager.go
open-im-server/pkg/common/storage/cache/redis/s3.go
open-im-server/pkg/common/storage/cache/redis/seq_conversation_test.go
open-im-server/pkg/common/storage/cache/redis/seq_conversation.go
open-im-server/pkg/common/storage/cache/redis/seq_user_test.go
open-im-server/pkg/common/storage/cache/redis/seq_user.go
open-im-server/pkg/common/storage/cache/redis/third.go
open-im-server/pkg/common/storage/cache/redis/token.go
open-im-server/pkg/common/storage/cache/redis/user.go
open-im-server/pkg/common/storage/common/types.go
open-im-server/pkg/common/storage/controller/auth.go
open-im-server/pkg/common/storage/controller/black.go
open-im-server/pkg/common/storage/controller/client_config.go
open-im-server/pkg/common/storage/controller/conversation.go
open-im-server/pkg/common/storage/controller/doc.go
open-im-server/pkg/common/storage/controller/friend.go
open-im-server/pkg/common/storage/controller/group.go
open-im-server/pkg/common/storage/controller/msg_transfer.go
open-im-server/pkg/common/storage/controller/msg.go
open-im-server/pkg/common/storage/controller/push.go
open-im-server/pkg/common/storage/controller/s3.go
open-im-server/pkg/common/storage/controller/third.go
open-im-server/pkg/common/storage/controller/user.go
open-im-server/pkg/common/storage/database/black.go
open-im-server/pkg/common/storage/database/cache.go
open-im-server/pkg/common/storage/database/client_config.go
open-im-server/pkg/common/storage/database/conversation.go
open-im-server/pkg/common/storage/database/doc.go
open-im-server/pkg/common/storage/database/friend_request.go
open-im-server/pkg/common/storage/database/friend.go
open-im-server/pkg/common/storage/database/group_member.go
open-im-server/pkg/common/storage/database/group_request.go
open-im-server/pkg/common/storage/database/group.go
open-im-server/pkg/common/storage/database/log.go
open-im-server/pkg/common/storage/database/msg.go
open-im-server/pkg/common/storage/database/name.go
open-im-server/pkg/common/storage/database/object.go
open-im-server/pkg/common/storage/database/seq_user.go
open-im-server/pkg/common/storage/database/seq.go
open-im-server/pkg/common/storage/database/user.go
open-im-server/pkg/common/storage/database/version_log.go
open-im-server/pkg/common/storage/database/mgo/black.go
open-im-server/pkg/common/storage/database/mgo/cache_test.go
open-im-server/pkg/common/storage/database/mgo/cache.go
open-im-server/pkg/common/storage/database/mgo/client_config.go
open-im-server/pkg/common/storage/database/mgo/conversation.go
open-im-server/pkg/common/storage/database/mgo/doc.go
open-im-server/pkg/common/storage/database/mgo/friend_request.go
open-im-server/pkg/common/storage/database/mgo/friend.go
open-im-server/pkg/common/storage/database/mgo/group_member.go
open-im-server/pkg/common/storage/database/mgo/group_request.go
open-im-server/pkg/common/storage/database/mgo/group.go
open-im-server/pkg/common/storage/database/mgo/helpers.go
open-im-server/pkg/common/storage/database/mgo/log.go
open-im-server/pkg/common/storage/database/mgo/msg_test.go
open-im-server/pkg/common/storage/database/mgo/msg.go
open-im-server/pkg/common/storage/database/mgo/object.go
open-im-server/pkg/common/storage/database/mgo/seq_conversation_test.go
open-im-server/pkg/common/storage/database/mgo/seq_conversation.go
open-im-server/pkg/common/storage/database/mgo/seq_user.go
open-im-server/pkg/common/storage/database/mgo/user.go
open-im-server/pkg/common/storage/database/mgo/version_log.go
open-im-server/pkg/common/storage/database/mgo/version_test.go
open-im-server/pkg/common/storage/model/application.go
open-im-server/pkg/common/storage/model/black.go
open-im-server/pkg/common/storage/model/cache.go
open-im-server/pkg/common/storage/model/client_config.go
open-im-server/pkg/common/storage/model/conversation.go
open-im-server/pkg/common/storage/model/doc.go
open-im-server/pkg/common/storage/model/friend_request.go
open-im-server/pkg/common/storage/model/friend.go
open-im-server/pkg/common/storage/model/group_member.go
open-im-server/pkg/common/storage/model/group_request.go
open-im-server/pkg/common/storage/model/group.go
open-im-server/pkg/common/storage/model/log.go
open-im-server/pkg/common/storage/model/msg.go
open-im-server/pkg/common/storage/model/object.go
open-im-server/pkg/common/storage/model/seq_user.go
open-im-server/pkg/common/storage/model/seq.go
open-im-server/pkg/common/storage/model/subscribe.go
open-im-server/pkg/common/storage/model/user.go
open-im-server/pkg/common/storage/model/version_log.go
open-im-server/pkg/common/storage/versionctx/rpc.go
open-im-server/pkg/common/storage/versionctx/version.go
open-im-server/pkg/common/webhook/condition.go
open-im-server/pkg/common/webhook/doc.go
open-im-server/pkg/common/webhook/http_client_test.go
open-im-server/pkg/common/webhook/http_client.go
open-im-server/pkg/dbbuild/builder.go
open-im-server/pkg/dbbuild/microservices.go
open-im-server/pkg/dbbuild/standalone.go
open-im-server/pkg/localcache/cache_test.go
open-im-server/pkg/localcache/cache.go
open-im-server/pkg/localcache/doc.go
open-im-server/pkg/localcache/init.go
open-im-server/pkg/localcache/option.go
open-im-server/pkg/localcache/tool.go
open-im-server/pkg/localcache/link/doc.go
open-im-server/pkg/localcache/link/link_test.go
open-im-server/pkg/localcache/link/link.go
open-im-server/pkg/localcache/lru/doc.go
open-im-server/pkg/localcache/lru/lru_expiration.go
open-im-server/pkg/localcache/lru/lru_lazy_test.go
open-im-server/pkg/localcache/lru/lru_lazy.go
open-im-server/pkg/localcache/lru/lru_slot.go
open-im-server/pkg/localcache/lru/lru.go
open-im-server/pkg/mqbuild/builder.go
open-im-server/pkg/msgprocessor/conversation.go
open-im-server/pkg/msgprocessor/doc.go
open-im-server/pkg/msgprocessor/options.go
open-im-server/pkg/notification/msg.go
open-im-server/pkg/notification/common_user/common.go
open-im-server/pkg/notification/grouphash/grouphash.go
open-im-server/pkg/rpccache/common.go
open-im-server/pkg/rpccache/conversation.go
open-im-server/pkg/rpccache/doc.go
open-im-server/pkg/rpccache/friend.go
open-im-server/pkg/rpccache/group.go
open-im-server/pkg/rpccache/online.go
open-im-server/pkg/rpccache/subscriber.go
open-im-server/pkg/rpccache/user.go
open-im-server/pkg/rpcli/auth.go
open-im-server/pkg/rpcli/conversation.go
open-im-server/pkg/rpcli/group.go
open-im-server/pkg/rpcli/msg.go
open-im-server/pkg/rpcli/msggateway.go
open-im-server/pkg/rpcli/push.go
open-im-server/pkg/rpcli/relation.go
open-im-server/pkg/rpcli/rtc.go
open-im-server/pkg/rpcli/third.go
open-im-server/pkg/rpcli/tool.go
open-im-server/pkg/rpcli/user.go
open-im-server/pkg/statistics/doc.go
open-im-server/pkg/statistics/statistics.go
open-im-server/pkg/tools/batcher/batcher_test.go
open-im-server/pkg/tools/batcher/batcher.go
open-im-server/pkg/util/conversationutil/conversationutil.go
open-im-server/pkg/util/conversationutil/doc.go
open-im-server/pkg/util/hashutil/id.go
open-im-server/pkg/util/useronline/split.go
open-im-server/scripts/template/boilerplate.txt
open-im-server/scripts/template/footer.md.tmpl
open-im-server/scripts/template/head.md.tmpl
open-im-server/scripts/template/LICENSE
open-im-server/scripts/template/LICENSE_TEMPLATES
open-im-server/scripts/template/project_README.md
open-im-server/test/readme
open-im-server/test/e2e/e2e_test.go
open-im-server/test/e2e/e2e.go
open-im-server/test/e2e/README.md
open-im-server/test/e2e/api/token/token.go
open-im-server/test/e2e/api/user/curd.go
open-im-server/test/e2e/api/user/user.go
open-im-server/test/e2e/conformance/.keep
open-im-server/test/e2e/framework/config/config_test.go
open-im-server/test/e2e/framework/config/config.go
open-im-server/test/e2e/framework/ginkgowrapper/.keep
open-im-server/test/e2e/framework/ginkgowrapper/ginkgowrapper_test.go
open-im-server/test/e2e/framework/ginkgowrapper/ginkgowrapper.go
open-im-server/test/e2e/framework/helpers/.keep
open-im-server/test/e2e/framework/helpers/chat/chat.go
open-im-server/test/e2e/page/chat_page.go
open-im-server/test/e2e/page/login_page.go
open-im-server/test/e2e/performance/.keep
open-im-server/test/e2e/rpc/auth/.keep
open-im-server/test/e2e/rpc/conversation/.keep
open-im-server/test/e2e/rpc/friend/.keep
open-im-server/test/e2e/rpc/group/.keep
open-im-server/test/e2e/rpc/message/.keep
open-im-server/test/e2e/scalability/.keep
open-im-server/test/e2e/upgrade/.keep
open-im-server/test/e2e/web/Readme.md
open-im-server/test/jwt/main.go
open-im-server/test/stress-test/main.go
open-im-server/test/stress-test/README.md
open-im-server/test/stress-test-v2/main.go
open-im-server/test/stress-test-v2/README.md
open-im-server/test/testdata/README.md
open-im-server/test/testdata/db/messages.json
open-im-server/test/testdata/db/users.json
open-im-server/test/testdata/requests/login.json
open-im-server/test/testdata/requests/register.json
open-im-server/test/testdata/requests/send-message.json
open-im-server/test/testdata/responses/login.json
open-im-server/test/testdata/responses/register.json
open-im-server/test/testdata/responses/sendMessage.json
open-im-server/test/webhook/msgmodify/main.go
open-im-server/tools/README.md
open-im-server/tools/changelog/changelog.go
open-im-server/tools/check-component/main.go
open-im-server/tools/check-free-memory/main.go
open-im-server/tools/imctl/.gitignore
open-im-server/tools/imctl/main.go
open-im-server/tools/imctl/README.md
open-im-server/tools/infra/main.go
open-im-server/tools/ncpu/main_test.go
open-im-server/tools/ncpu/main.go
open-im-server/tools/ncpu/README.md
open-im-server/tools/s3/main.go
open-im-server/tools/s3/README.md
open-im-server/tools/s3/internal/conversion.go
open-im-server/tools/seq/main.go
open-im-server/tools/seq/internal/seq.go
open-im-server/tools/url2im/main.go
open-im-server/tools/url2im/pkg/api.go
open-im-server/tools/url2im/pkg/buffer.go
open-im-server/tools/url2im/pkg/config.go
open-im-server/tools/url2im/pkg/http.go
open-im-server/tools/url2im/pkg/manage.go
open-im-server/tools/url2im/pkg/md5.go
open-im-server/tools/url2im/pkg/progress.go
open-im-server/tools/versionchecker/main.go
open-im-server/tools/yamlfmt/main_test.go
open-im-server/tools/yamlfmt/main.go
open-im-server/version/version
open-im-server/version/version.go
